(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-functions"],{"0599":function(t,e,n){},"080f":function(t,e,n){"use strict";n.r(e);var i=function(){var t=this,e=t._self._c;return e("container",{class:["custom-diamond-wrapper",t.$gameName]},[t.showHowToCatPage?e("div",{staticClass:"cat-page"},[e("div",{staticClass:"title"},[t._v(t._s(t.$t("login-validation-main-how-cat")))]),e("div",{staticClass:"back-btn",on:{click:function(e){t.showHowToCatPage=!1}}}),e("img",{attrs:{src:t.$imageLoader("loganFindValidationCode"),alt:""}}),e("div",{staticClass:"time-tips",domProps:{innerHTML:t._s(t.$t("login-validation-date-construction").replace("30",`<span>${t.loginValidationExpireCount}</span>`))}})]):e("div",{staticClass:"main-page"},[e("div",{staticClass:"title"},[t._v(t._s(t.$t("login-validation-main-title")))]),e("div",{staticClass:"send-info"},[e("div",{staticClass:"tips",domProps:{innerHTML:t._s(t.$t("login-validation-main-send-over",{0:t.htmlUsername}))}}),t.canResend?e("div",{staticClass:"send-btn",on:{click:t.resend}},[t._v(t._s(t.$t("login-validation-main-resend")))]):t._e(),t.leaveCount>0?e("div",{staticClass:"leave-count"},[t._v("（"+t._s(t.leaveCount)+"s）")]):t._e()]),e("div",{staticClass:"input-wrapper"},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.inputCode,expression:"inputCode"}],class:t.$i18n.locale,attrs:{type:"number",placeholder:t.$t("login-validation-main-placeholder"),autofocus:""},domProps:{value:t.inputCode},on:{input:[function(e){e.target.composing||(t.inputCode=e.target.value)},t.fixInput]}})]),e("div",{staticClass:"safe-tips",domProps:{innerHTML:t._s(t.$t("login-validation-safety-construction"))}}),e("div",{class:["confirm-btn",{"confirm-btn_validated":t.rawValidate}],on:{click:t.checkCode}},[t._v(t._s(t.$t("login-validation-main-confirm")))]),e("div",{staticClass:"cat-code-tips",on:{click:function(e){t.showHowToCatPage=!0}}},[t._v(t._s(t.$t("login-validation-main-how-cat")))]),e("div",{staticClass:"close",on:{click:t.close}})])])},s=[],o=n("d0e9"),r=n("af82"),a=n("fa7d");let c="";Object(a["b"])(t=>{c=t});var l={name:"ChannelKlarnaPopup",components:{Container:o["a"]},props:["option"],computed:{htmlUsername(){return`<span>${this.option.username}</span>`},rawValidate(){const t=this.inputCode;return 6===String(t).length},canResend(){return 0===this.leaveCount&&this.leaveTimes>0}},data(){return{inputCode:"",showHowToCatPage:!1,leaveCount:59,leaveTimes:this.option.remaining_verification_attempts||0,countInterval:"",loginValidationExpireCount:this.$gcbk("ids.loginValidationExpireCount",30)}},methods:{countTime(t){this.leaveCount=t||59,this.countInterval=setInterval(()=>{this.leaveCount--,0===this.leaveCount&&(clearInterval(this.countInterval),this.countInterval=null)},1e3)},resend(){this.$loading.show(),Object(r["t"])({fp_device_id:c,openid:this.option.openid}).then(t=>{const{code:e}=t;switch(e){case 0:this.leaveTimes=t.data.remaining_verification_attempts,this.countTime();break;default:this.$toast.err(this.$t("login-validation-error-code"))}}).finally(()=>this.$loading.hide())},async checkCode(){if(!this.rawValidate)return null;this.$loading.show(),Object(r["e"])({code:+this.inputCode,fp_device_id:c,openid:this.option.openid}).then(t=>{const{code:e}=t;switch(this.inputCode="",e){case 0:case 5011:{const t=this.option.successCb;t&&t(),this.$root.$emit("closePop");break}case 5009:this.$toast.err(this.$t("login-validation-error-expire"));break;case 5010:this.$toast.err(this.$t("login-validation-error-code"));break;default:this.$toast.err(this.$t("login-validation-error-text"))}}).finally(()=>this.$loading.hide())},close(){this.$root.$emit("closePop");const t=this.option.failCb;t&&t()},fixInput(t){const{data:e}=t,n=/^\d$/.test(e);"e"!==e&&"."!==e&&"E"!==e&&n||(t.target.value="");const i=this.inputCode.length;i>6&&(this.inputCode=this.inputCode.slice(0,6))}},created(){const t=this.option&&this.option.send_code_cd||59;this.countTime(t)},beforeDestroy(){this.countInterval&&(this.countInterval=null,clearInterval(this.countInterval))}},d=l,u=(n("30e1"),n("2877")),p=Object(u["a"])(d,i,s,!1,null,"9d763512",null);e["default"]=p.exports},"139e":function(t,e,n){"use strict";n.r(e);var i=function(){var t=this;t._self._c;return t._m(0)},s=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"copyright"},[e("p",[t._v(" ©Puzala Games Limited, All Rights Reserved "),e("a",{staticStyle:{color:"#ddb463"},attrs:{href:"https://www.puzala.com/privacy-policy/",target:"_blank"}},[t._v("Privacy Policy")]),t._v(" , "),e("a",{staticStyle:{color:"#ddb463"},attrs:{href:"https://www.puzala.com/terms-of-service/",target:"_blank"}},[t._v("Terms and Conditions")]),t._v(". ")])])}],o={name:"CommonFooterPuzala"},r=o,a=(n("59cf"),n("2877")),c=Object(a["a"])(r,i,s,!1,null,"12bb518e",null);e["default"]=c.exports},"16db":function(t,e){t.exports="data:image/png;base64,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"},1814:function(t,e,n){},"200c":function(t,e,n){"use strict";n("3c37")},2662:function(t,e,n){t.exports=n.p+"static/1751299200/img/login_reward_0.dc0990f2.png"},"29db":function(t,e,n){t.exports=n.p+"static/1751299200/img/boon-install-award.c5699e31.png"},"2ed8":function(t,e,n){"use strict";n("9abc")},"30e1":function(t,e,n){"use strict";n("c656")},"31ca":function(t,e,n){var i={"./login_reward_0.png":"2662","./login_reward_1.png":"398c","./login_reward_2.png":"7ef4","./login_reward_3.png":"4790"};function s(t){var e=o(t);return n(e)}function o(t){if(!n.o(i,t)){var e=new Error("Cannot find module '"+t+"'");throw e.code="MODULE_NOT_FOUND",e}return i[t]}s.keys=function(){return Object.keys(i)},s.resolve=o,t.exports=s,s.id="31ca"},"34d6":function(t,e,n){},"376d":function(t,e,n){t.exports=n.p+"static/1751299200/img/install_reward_0.f9a9f8bf.png"},"398c":function(t,e,n){t.exports=n.p+"static/1751299200/img/login_reward_1.dea43e02.png"},"3c32":function(t,e,n){"use strict";n("4f6f")},"3c37":function(t,e,n){},"3edd":function(t,e,n){"use strict";n.r(e);var i=function(){var t=this,e=t._self._c;return e("container",{staticClass:"arrears-reminder-wrapper",class:[t.$i18n.locale,t.$gameName],attrs:{title:t.$t("text_tips"),"hide-close":!0},scopedSlots:t._u([{key:"footerBtn",fn:function(){return[e("div",{staticClass:"custom-btn btn-ok",on:{click:t.close}},[t._v(t._s(t.$t("confirm-btn")))])]},proxy:!0}])},[e("div",{staticClass:"desc"},[t._v(t._s(t.$t("settings_terms_of_service_agreement_description")))]),e("div",{staticClass:"private"},[e("span",{on:{click:function(e){return t.go(0)}}},[t._v("《"+t._s(t.$t("agreement"))+"》")]),e("span",{on:{click:function(e){return t.go(1)}}},[t._v("《"+t._s(t.$t("privacy"))+"》")])])])},s=[],o=n("efc1"),r=n("9213"),a=n("af82"),c={name:"PrivacyPolicy",props:{option:Object},components:{Container:o["a"]},methods:{go(t){const e=(this.$store.state.country||"").toLowerCase(),n=["https://funplus.com/terms-conditions","https://funplus.com/privacy-policy/"],i={kr:["https://funplus.com/terms-conditions-en-as/kr/","https://funplus.com/privacy-policy-en-as/kr/"],jp:["https://funplus.com/terms-conditions-en-as/ja/","https://funplus.com/privacy-policy-en-as/ja/ "]};let s=n[t];i[e]&&(s=i[e][t]),this.$store.getters["gameinfo/isPuzalaGame"]&&(s=["https://www.puzala.com/terms-of-service","https://www.puzala.com/privacy-policy"][t]),window.open(s,"_blank")},close(){r["a"].setLocalStorage("confirmPrivacyPolicy",1),this.$store.commit("setPrivacyPolicyStatus",!0);const t={p0:"web",p1:7,p2:"1096",silence:!0};Object(a["h"])(t),this.$root.$emit("closePop")}}},l=c,d=(n("a9ac"),n("2877")),u=Object(d["a"])(l,i,s,!1,null,"633825b6",null);e["default"]=u.exports},4047:function(t,e,n){"use strict";n("c243")},"404e":function(t,e,n){"use strict";n.r(e);var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"checkbox"},[e("label",[e("input",{directives:[{name:"model",rawName:"v-model",value:t.isCheck,expression:"isCheck"}],attrs:{type:"checkbox"},domProps:{checked:Array.isArray(t.isCheck)?t._i(t.isCheck,null)>-1:t.isCheck},on:{change:[function(e){var n=t.isCheck,i=e.target,s=!!i.checked;if(Array.isArray(n)){var o=null,r=t._i(n,o);i.checked?r<0&&(t.isCheck=n.concat([o])):r>-1&&(t.isCheck=n.slice(0,r).concat(n.slice(r+1)))}else t.isCheck=s},function(e){return t.notifyServer("check")}]}}),t._v(" Ich habe "),e("a",{attrs:{href:"https://funplus.com/terms-conditions/#section-13",target:"_blank"}},[t._v("Rückerstattungsrichtlinie")]),t._v(" gelesen und stimme zu. ")])])},s=[],o=n("af82"),r={name:"privatePermission",computed:{calcPop(){return!this.isCheck||!this.isPop}},watch:{calcPop(t){window.__needDEPop=t}},data(){return{isCheck:!1,isPop:!1}},methods:{initState(){const t={p0:"web",p1:9,p2:2531,p3:"api",game:this.$store.state.gameinfo.gameProject.split("_")[0]};Object(o["h"])(t).then(t=>{const{data:e,code:n}=t;0===n&&(window.__needDEPop=!0,this.isCheck=e.check,this.isPop=e.popup)})},notifyServer(t){const e={p0:"web",p1:9,p2:2532,p3:"api",game:this.$store.state.gameinfo.gameProject.split("_")[0]};"check"===t&&(e.set_type=0,e.set_status=Number(this.isCheck)),"pop"===t&&(e.set_type=1,e.set_status=1,this.isCheck=!0),Object(o["h"])(e).then(e=>{const{code:n}=e;0!==n&&("pop"===t&&(this.isPop=!1),"check"===t&&(this.isCheck=!this.isCheck))})}},created(){this.initState(),this.$root.$on("changeDePopPrivacy",()=>this.notifyServer("pop"))}},a=r,c=(n("98e2"),n("2877")),l=Object(c["a"])(a,i,s,!1,null,"6cb8a3e6",null);e["default"]=l.exports},"41a5":function(t,e,n){},"46d5":function(t,e,n){"use strict";n.r(e);var i=function(){var t=this;t._self._c;return t._m(0)},s=[function(){var t=this,e=t._self._c;return e("div",[e("a",{attrs:{href:"https://funplus.com/terms-conditions/#section-13",target:"_blank"}},[t._v("Refund Policy")]),t._v(". ")])}],o={name:"RefundPolicy"},r=o,a=(n("5626"),n("2877")),c=Object(a["a"])(r,i,s,!1,null,"6d6cc831",null);e["default"]=c.exports},4790:function(t,e,n){t.exports=n.p+"static/1751299200/img/login_reward_3.9b45a0c6.png"},4976:function(t,e,n){var i={"./aof/boon/install_reward_0.png":"376d","./aof/boon/install_reward_1.png":"73b0","./aof/boon/install_reward_2.png":"f1d7","./aof/boon/install_reward_3.png":"7d47"};function s(t){var e=o(t);return n(e)}function o(t){if(!n.o(i,t)){var e=new Error("Cannot find module '"+t+"'");throw e.code="MODULE_NOT_FOUND",e}return i[t]}s.keys=function(){return Object.keys(i)},s.resolve=o,t.exports=s,s.id="4976"},"4db7":function(t,e,n){"use strict";n.r(e);var i=function(){var t=this,e=t._self._c;return e("container",{staticClass:"arrears-reminder-wrapper",class:[t.$i18n.locale],attrs:{title:t.$t("boon-page-title")}},[e("div",{staticClass:"divider"}),e("div",{staticClass:"tab-wrapper"},t._l(t.tabList,(function(n,i){return e("div",{key:i,staticClass:"tab",class:["tab-"+i,t.chosenIndex===i?"chosen-active":"","boon-ss-install-title"!==n.title||t.gotInstallReward?"":"dot-active","boon-ss-login-title"!==n.title||t.gotLoginReward?"":"dot-active"],on:{click:function(e){t.chosenIndex=i}}},[e("span",[t._v(t._s(t.$t(n.title)))])])})),0),e("Swiper",{staticClass:"my-swiper-wrapper",attrs:{options:t.swiperOptions}},[e("SwiperSlide",{key:"install"},[e("div",{staticClass:"charge-desc"},[e("div",{staticClass:"row-1"},[t._v(t._s(t.$t("boon-task-2-title")))]),e("div",{staticClass:"row-2"},[t._v(t._s(t.$t("boon-p1-title")))])]),e("div",{staticClass:"gift-image"},[e("img",{attrs:{src:n("b519")(`./${"ss"===t.$gameName?"boon":t.$gameName}/boon-install-award.png`),alt:""}})]),e("div",{staticClass:"login-reward-btn action-btn"},[t.isLogin?t.hadInstall?[t.gotInstallReward?e("span",{staticClass:"forbidden"}):e("span",{staticClass:"todo click-btn",on:{click:function(e){return t.getReward(t.getPwaReward)}}},[t._v(t._s(t.$t("boon-gain")))])]:[t.calcShowInstall?e("span",{staticClass:"click-btn",on:{click:t.install}},[t._v(t._s(t.$t("boon-task-2-add")))]):e("p",{staticClass:"browser-forbidden"},[t._v(t._s(t.$t("boon-browser-forbidden")))])]:[e("span",{staticClass:"click-btn",on:{click:t.focusInput}},[t._v(t._s(t.$t("boon-login")))])]],2)]),e("SwiperSlide",{key:"login"},[e("div",{staticClass:"charge-desc"},[e("div",{staticClass:"row-1"},[t._v(t._s(t.$t("boon-task-1-title")))]),e("div",{staticClass:"row-2"},[t._v(t._s(t.$t("boon-p1-title")))])]),e("div",{staticClass:"gift-image"},[e("img",{attrs:{src:n("f691")(`./${"ss"===t.$gameName?"boon":t.$gameName}/boon-login-award.png`),alt:""}})]),e("div",{staticClass:"login-reward-btn action-btn"},[t.isLogin?t.hadLogin?[t.gotLoginReward?e("span",{staticClass:"forbidden"}):e("span",{staticClass:"todo click-btn",on:{click:function(e){return t.getReward(t.getLoginReward)}}},[t._v(t._s(t.$t("boon-gain")))])]:e("span",{staticClass:"click-btn",on:{click:t.focusInput}},[t._v(t._s(t.$t("boon-go-charge-short")))]):e("span",{staticClass:"click-btn",on:{click:t.focusInput}},[t._v(t._s(t.$t("boon-login")))])],2)])],1)],1)},s=[],o=n("efc1"),r=n("af82"),a=n("7212"),c=(n("f4bd"),n("2b80")),l=n.n(c),d=n("2f62");const{projectId:u,loginAction:p,getLoginReward:h,pwaOpenAction:f,getPwaReward:g}=window.$gcbk("apiParams.boonAme"),m={p0:"web",p1:u};function _(){const t=window.matchMedia("(display-mode: standalone)").matches;return document.referrer.startsWith("android-app://")?"twa":navigator.standalone||t?"standalone":"browser"}var v={name:"BoonPop",components:{Container:o["a"],Swiper:a["Swiper"],SwiperSlide:a["SwiperSlide"]},data(){const t=this;return{hadLogin:!1,gotLoginReward:!1,hadInstall:!1,gotInstallReward:!1,deferredPrompt:window.__deferredPrompt||void 0,showMobileSafariGuide:!1,progressPercent:0,chosenIndex:0,swiperInstance:void 0,swiperOptions:{autoplay:!1,on:{slideChangeTransitionStart:function(){t.chosenIndex=this.activeIndex},init:function(){t.swiperInstance=this}}},getLoginReward:h,getPwaReward:g,tabList:[{title:"boon-ss-install-title"},{title:"boon-ss-login-title"}]}},methods:{onClose(){this.$root.$emit("closePop")},showInstallPart(){window.addEventListener("beforeinstallprompt",t=>{t.preventDefault(),this.deferredPrompt=t})},resetStatus(){const t={p0:"web",p1:u,p2:`${p},${h},${f},${g}`};this.$loading.show(),Object(r["c"])(t).then(t=>{const{data:e,code:n}=t;if(0===n){const t={};for(const n of Object.values(e))t[n.task_id]=n;this.hadLogin=p in t,this.gotLoginReward=h in t,this.hadInstall=f in t,this.gotInstallReward=g in t}}).finally(()=>this.$loading.hide())},getReward(t){const e={p2:t};this.$loading.show(),Object(r["h"])({...e,...m}).then(e=>{const{code:n,data:i=[]}=e;0===n&&i.length&&(t===g&&(this.gotInstallReward=!0),t===h&&(this.gotLoginReward=!0))}).finally(()=>this.$loading.hide())},focusInput(){this.$root.$emit("closePop"),this.$root.$emit("ClickPayButNotLogin")},install(){this.$root.$emit("closePop"),this.deferredPrompt?(this.deferredPrompt.prompt(),this.deferredPrompt.userChoice.then(t=>{if("accepted"===t.outcome){console.log("User accepted the A2HS prompt");const t=setInterval(()=>{"standalone"===_()&&(clearInterval(t),this.$root.$emit("installSuccessful"))},1e3)}else console.log("User dismissed the A2HS prompt");this.deferredPrompt=void 0})):setTimeout(()=>{this.$root.$emit("mobileSafariGuide")},500)}},computed:{...Object(d["c"])("userinfo",["isLogin"]),...Object(d["c"])(["userinfo"]),calcShowInstall(){const t=new l.a(navigator.userAgent),e=t.getResult(),{browser:n}=e,i="Mobile Safari"===n.name||(n.name||"").includes("Chrome")&&this.deferredPrompt;return!this.isLogin||(!!this.hadInstall||i)}},created(){this.$root.$on("loginSuccess",()=>{setTimeout(()=>this.resetStatus(),2e3)}),this.isLogin&&this.resetStatus(),this.$watch("chosenIndex",t=>{this.swiperInstance.slideTo(t,200,!1)}),this.showInstallPart()}},b=v,w=(n("2ed8"),n("2877")),y=Object(w["a"])(b,i,s,!1,null,"7295710c",null);e["default"]=y.exports},"4f6f":function(t,e,n){},"53bb":function(t,e,n){t.exports=n.p+"static/1751299200/img/login_reward_0.dc0990f2.png"},5626:function(t,e,n){"use strict";n("0599")},"59cf":function(t,e,n){"use strict";n("1814")},"5f39":function(t,e,n){t.exports=n.p+"static/1751299200/img/install_reward_3.76a3b0b3.png"},6273:function(t,e,n){"use strict";n.r(e);var i=function(){var t=this,e=t._self._c;return e("container",{staticClass:"arrears-reminder-wrapper",class:[t.$i18n.locale,t.$gameName],attrs:{title:t.$t("text_tips"),"hide-close":!0,id:"risk-policy-wrapper"},scopedSlots:t._u([{key:"footerBtn",fn:function(){return[e("div",{staticClass:"custom-btn btn-ok",on:{click:t.close}},[t._v(t._s(t.$t("confirm-btn")))])]},proxy:!0}])},[e("div",{staticClass:"desc"},["always_banned"===t.key?e("div",{domProps:{innerHTML:t._s(t.$t("risk_policy_forbidden_forever"))}}):t._e(),["banned_adyen","banned_pingpong","banned_uid"].includes(t.key)?e("div",{domProps:{innerHTML:t._s(t.$t("risk_policy_forbidden_some",{0:`<span>${t.leaveDate}</span>`}))}}):t._e(),["access_warn","use_adyen","use_pingpong","use_wxpay","use_alipay","use_paypal","access_warn_black_room"].includes(t.key)?e("div",{domProps:{innerHTML:t._s(t.$t("risk_policy_pay_tip"))}}):t._e()])])},s=[],o=n("efc1"),r={name:"RiskControlPolicy",props:{option:{type:Object,default:()=>({})}},data(){const{key:t,value:e,cb:n}=this.option;return{key:t,value:e,cb:n,interval:"",leaveDate:""}},components:{Container:o["a"]},methods:{close(){this.$root.$emit("closePop"),this.interval&&clearInterval(this.interval),this.cb&&this.cb()},calcLeaveTime(){const t=t=>t<10?"0"+Math.floor(t):Math.floor(t),e=e=>`${Math.floor(e/3600/24)}d ${t(e/3600%24)} : ${t(e/60%60)} : ${t(e%60)}`;this.leaveDate=e(this.value),setInterval(()=>{if(0===this.value)return this.close(),void clearInterval(this.interval);this.leaveDate=e(--this.value)},1e3)}},created(){["banned_uid","banned_adyen","banned_pingpong"].includes(this.key)&&this.calcLeaveTime()},beforeDestroy(){this.interval&&clearInterval(this.interval)}},a=r,c=(n("200c"),n("2877")),l=Object(c["a"])(a,i,s,!1,null,"beffea3e",null);e["default"]=l.exports},6373:function(t,e,n){t.exports=n.p+"static/1751299200/img/icon-police-gongan.228e58cb.png"},"6a8c":function(t,e,n){t.exports=n.p+"static/1751299200/img/install_reward_2.9f56aa98.png"},"6b2b":function(t,e,n){"use strict";n.r(e);var i=function(){var t=this,e=t._self._c;return e("container",{staticClass:"arrears-reminder-wrapper",class:[t.$i18n.locale,t.$gameName],attrs:{title:t.$t("boon-page-title")}},[e("div",{staticClass:"divider"}),e("div",{staticClass:"tab-wrapper",class:{"tab-small":["de","sv"].includes(t.$i18n.locale)}},t._l(t.tabList,(function(n,i){return e("div",{key:n.dataKey,class:["tab",{"dot-active":!(t.$data[n.dataKey]||t.$store.state.formdata[n.dataKey]),"chosen-active":t.chosenIndex===i}],on:{click:function(e){t.chosenIndex=i}}},[e("span",[t._v(t._s(t.$t(n.langKey)))])])})),0),e("Swiper",{staticClass:"my-swiper-wrapper",attrs:{options:t.swiperOptions}},[e("SwiperSlide",{key:"install"},[e("div",{staticClass:"charge-desc"},[e("div",{staticClass:"row-1"},[t._v(t._s(t.$t("boon-task-2-title")))]),e("div",{staticClass:"row-2"},[t._v(t._s(t.$t("boon-p1-title")))])]),e("div",{staticClass:"gift-image"},["koa"===t.$gameName?t._l([0,1,2,3],(function(t){return e("img",{key:t,attrs:{src:n("9df3")(`./install_reward_${t}.png`),alt:""}})})):t._l([0,1,2,3],(function(i){return e("img",{key:i,attrs:{src:n("4976")(`./${"rom"===t.$gameName?"aof":t.$gameName}/boon/install_reward_${i}.png`),alt:""}})}))],2),e("div",{staticClass:"login-reward-btn action-btn"},[t.isLogin?t.hadInstall?[t.gotInstallReward?e("span",{staticClass:"forbidden"}):e("span",{staticClass:"todo click-btn",on:{click:function(e){return t.getReward(t.getPwaReward)}}},[t._v(t._s(t.$t("boon-gain")))])]:[t.calcShowInstall?e("span",{staticClass:"click-btn",on:{click:t.install}},[t._v(t._s(t.$t("boon-task-2-add")))]):e("p",{staticClass:"browser-forbidden"},[t._v(t._s(t.$t("boon-browser-forbidden")))])]:[e("span",{staticClass:"click-btn",on:{click:t.focusInput}},[t._v(t._s(t.$t("boon-login")))])]],2)]),e("SwiperSlide",{key:"login"},[e("div",{staticClass:"charge-desc"},[e("div",{staticClass:"row-1"},[t._v(t._s(t.$t("boon-task-1-title")))]),e("div",{staticClass:"row-2"},[t._v(t._s(t.$t("boon-p1-title")))])]),e("div",{staticClass:"gift-image"},["koa"===t.$gameName?t._l([0,1,2,3],(function(t){return e("img",{key:t,attrs:{src:n("31ca")(`./login_reward_${t}.png`),alt:""}})})):t._l([0,1,2,3],(function(i){return e("img",{key:i,attrs:{src:n("7778")(`./${"rom"===t.$gameName?"aof":t.$gameName}/boon/login_reward_${i}.png`),alt:""}})}))],2),e("div",{staticClass:"login-reward-btn action-btn"},[t.isLogin?t.hadLogin?[t.gotLoginReward?e("span",{staticClass:"forbidden"}):e("span",{staticClass:"todo click-btn",on:{click:function(e){return t.getReward(t.getLoginReward)}}},[t._v(t._s(t.$t("boon-gain")))])]:e("span",{staticClass:"click-btn",on:{click:t.focusInput}},[t._v(t._s(t.$t("boon-go-charge-short")))]):e("span",{staticClass:"click-btn",on:{click:t.focusInput}},[t._v(t._s(t.$t("boon-login")))])],2)]),e("SwiperSlide",{key:"daily-reward"},[e("div",{staticClass:"charge-desc"},[e("div",{staticClass:"row-1"},[t._v(t._s(t.$t("text-login-topup")))]),e("div",{staticClass:"row-2"},[t._v(t._s(t.$t("text-claim-daily-chest")))])]),e("div",{staticClass:"gift-image gift-image__daily-reward"},[e("img",{attrs:{src:n("7f18"),alt:""}})]),e("div",{staticClass:"login-reward-btn action-btn login-reward-btn__daily-reward"},[t.isLogin?[t.gotDailyReward?e("span",{class:["forbidden","forbidden__daily-reward"]}):e("span",{staticClass:"todo click-btn",on:{click:t.getDailyReward}},[t._v(t._s(t.$t("btn-open-now")))])]:e("span",{staticClass:"click-btn",on:{click:t.focusInput}},[t._v(t._s(t.$t("boon-login")))])],2),t.gotDailyReward?t._e():e("div",{staticClass:"tips"},[t._v("*"+t._s(t.$t("subtitle-daily-rewards-boon")))])])],1)],1)},s=[],o=(n("e9f5"),n("910d"),n("efc1")),r=n("af82"),a=n("7212"),c=(n("f4bd"),n("2b80")),l=n.n(c),d=n("2f62"),u=n("fa7d");const{projectId:p,loginAction:h,getLoginReward:f,pwaOpenAction:g,getPwaReward:m}=window.$gcbk("apiParams.boonAme",{}),_={p0:"web",p1:p},v={500:50,2e3:200,5e3:400,1e4:750,2e4:1200,5e4:2e3};function b(){const t=window.matchMedia("(display-mode: standalone)").matches;return document.referrer.startsWith("android-app://")?"twa":navigator.standalone||t?"standalone":"browser"}const w=[{dataKey:"gotInstallReward",langKey:"boon-ss-install-title"},{dataKey:"gotLoginReward",langKey:"boon-ss-login-title"},{dataKey:"gotDailyReward",langKey:"btn-daily-rewards"}];var y={name:"BoonPop",components:{Container:o["a"],Swiper:a["Swiper"],SwiperSlide:a["SwiperSlide"]},data(){const t=this;return{chargeStatus:{},recharge:0,hadLogin:!1,gotLoginReward:!1,hadInstall:!1,gotInstallReward:!1,goTurntable:!1,deferredPrompt:window.__deferredPrompt||void 0,showMobileSafariGuide:!1,progressPercent:0,numsMap:v,chosenIndex:0,swiperInstance:void 0,swiperOptions:{autoplay:!1,on:{slideChangeTransitionStart:function(){t.chosenIndex=this.activeIndex},init:function(){t.swiperInstance=this}}},tabList:w,getLoginReward:f,getPwaReward:m,missionList:[],serverId:0,gotToggleCoupon:!0}},methods:{getImgUrl(t){},numberFormat(t){return Object(u["n"])(t)},onRecive(t){Object(r["h"])({p0:"web",p1:71,p2:1626,id:t}).then(e=>{const{code:n}=e;if(0===n){const e=this.missionList.filter(e=>e.id===t),n=this.missionList.findIndex(e=>e.id===t);e[0].status=2,this.$set(this.missionList,n,e[0])}})},showInstallPart(){window.addEventListener("beforeinstallprompt",t=>{t.preventDefault(),this.deferredPrompt=t})},resetStatus(){const t={p0:"web",p1:p,p2:`${h},${f},${g},${m}`};this.$loading.show(),Object(r["c"])(t).then(t=>{const{data:e,code:n}=t;if(0===n){const t={};for(const n of Object.values(e))t[n.task_id]=n;this.hadLogin=h in t,this.gotLoginReward=f in t,this.hadInstall=g in t,this.gotInstallReward=m in t}}).finally(()=>this.$loading.hide())},resetTurntable(){const t={p0:"web",p1:79,p2:1711};this.$loading.show(),Object(r["a"])(t).then(t=>{const{data:e,code:n}=t;if(0===n){const t=e.score.filter(t=>"10085"==t.score_id)[0];this.goTurntable=!(t.ticket>0||0==t.total)}}).finally(()=>this.$loading.hide())},getReward(t){const e={p2:t};this.$loading.show(),Object(r["h"])({...e,..._}).then(e=>{const{code:n,data:i=[]}=e;0===n&&i.length&&(t===m&&(this.gotInstallReward=!0),t===f&&(this.gotLoginReward=!0))}).finally(()=>this.$loading.hide())},getDailyReward(){this.$loading.show(),Object(r["h"])({p0:"web",p1:11,p2:1422}).then(t=>{const{code:e,data:n=[]}=t;0===e&&n.length?(this.$store.commit("formdata/setDailyRewardStatus",!0),this.$root.$emit("closePop"),setTimeout(()=>{this.$root.$emit("showPop","DailyReward",{reward:n[0]})},0)):this.$toast.err(this.$t("network_err"))}).catch(()=>this.$toast.err(this.$t("network_err"))).finally(()=>this.$loading.hide())},focusInput(){this.$root.$emit("closePop"),this.$root.$emit("ClickPayButNotLogin")},install(){this.$root.$emit("closePop"),this.deferredPrompt?(this.deferredPrompt.prompt(),this.deferredPrompt.userChoice.then(t=>{if("accepted"===t.outcome){console.log("User accepted the A2HS prompt");const t=setInterval(()=>{"standalone"===b()&&(clearInterval(t),this.$root.$emit("installSuccessful"))},1e3)}else console.log("User dismissed the A2HS prompt");this.deferredPrompt=void 0})):setTimeout(()=>{this.$root.$emit("mobileSafariGuide")},500)},goToggleActivityPage(){let t=`${Object({NODE_ENV:"production",VUE_APP_PROD_ENV:"MASTER",VUE_APP_PREFIX_TOKEN_SSCP:"https://ss-coin-master-api.kingsgroupgames.com/api",VUE_APP_PREFIX_TOKEN_STCP:"https://st-store-master.kingsgroupgames.com/api",VUE_APP_PREFIX_TOKEN_MCCP:"https://mc-store-master.kingsgroupgames.com/api",VUE_APP_PREFIX_TOKEN_GOGCP:"https://gog-store-master.kingsgroupgames.com/api",VUE_APP_PREFIX_TOKEN_ROMCP:"https://koa-store-release.kingsgroup.cn/api",VUE_APP_PREFIX_TOKEN_SSV:"https://ssv-store-master.kingsgroupgames.com/api",VUE_APP_PREFIX_TOKEN_SSV2:"https://ssv2-store-master-api.kingsgroupgames.com/api",VUE_APP_PREFIX_TOKEN_DC:"https://dcdl-store-master-api.kingsgroupgames.com/api",VUE_APP_PREFIX_TOKEN_FOUNDATION:"https://foundation-store-release-api.kingsgroup.cn/api",VUE_APP_PREFIX_ACCOUNT_FOUNDATION:"https://store-account-release.nenglianghe.cn/api/account",VUE_APP_PREFIX_TOKEN_SSRP:"https://ss-coin-master-api.kingsgroupgames.com/api",VUE_APP_PREFIX_STORE_SSRP:"https://ss-store-master.kingsgroupgames.com",VUE_APP_PREFIX_TOKEN_ST:"https://st-store-master.kingsgroupgames.com/api",VUE_APP_PREFIX_STORE_STRP:"https://st-store-master.kingsgroupgames.com",VUE_APP_PREFIX_TOKEN_MO:"https://mo-store-master.kingsgroupgames.com/api",VUE_APP_PREFIX_STORE_MORP:"https://mo-store-master.kingsgroupgames.com",VUE_APP_PREFIX_TOKEN_SSD:"https://ts-store-master-api.kingsgroupgames.com/api",VUE_APP_PREFIX_STORE_SSDRP:"https://ts-store-master.kingsgroupgames.com",VUE_APP_PREFIX_AME:"https://ame-test.funplus.com",VUE_APP_PREFIX_ACCOUNT:"https://store-account-stage.funplus.com/api/account",VUE_APP_LOGIN_PAGE_URL:"https://store-funplusid-test.funplus.com/login",VUE_APP_VipIntroducePageUrl_koa:"http://vip-test.funplus.com.cn/koa",VUE_APP_VipIntroducePageUrl_aof:"http://vip-test.funplus.com.cn/vip",VUE_APP_VipIntroducePageUrl_rom:"http://vip-test.funplus.com.cn/rom",VUE_APP_VipIntroducePageUrl_koaCn:"http://vip-test.funplus.com.cn/koa-cn",VUE_APP_ROM_ADDRESS_KOA:"http://**************:10305",BASE_URL:"/res/"}).VUE_APP_URL_KOA_TOGGLE_COUPON}?l=${this.$i18n.locale}`;localStorage.getItem("openid")&&(t+="&openid="+encodeURIComponent(localStorage.getItem("openid"))),window.open(t,"_blank")}},computed:{...Object(d["c"])("userinfo",["isLogin"]),...Object(d["c"])(["userinfo"]),...Object(d["c"])("formdata",["gotDailyReward","koaTopupEnable"]),calcShowInstall(){const t=new l.a(navigator.userAgent),e=t.getResult(),{browser:n}=e,i="Mobile Safari"===n.name||(n.name||"").includes("Chrome")&&this.deferredPrompt;return!this.isLogin||(!!this.hadInstall||i)}},created(){this.$root.$on("loginSuccess",()=>{setTimeout(()=>this.resetStatus(),2e3)}),this.isLogin&&this.resetStatus(),this.$watch("chosenIndex",t=>{this.swiperInstance.slideTo(t,200,!1)}),this.showInstallPart()}},C=y,k=(n("e002"),n("2877")),P=Object(k["a"])(C,i,s,!1,null,"c34714c8",null);e["default"]=P.exports},"73b0":function(t,e,n){t.exports=n.p+"static/1751299200/img/install_reward_1.e49e53f9.png"},7778:function(t,e,n){var i={"./aof/boon/login_reward_0.png":"53bb","./aof/boon/login_reward_1.png":"89c9","./aof/boon/login_reward_2.png":"fc88","./aof/boon/login_reward_3.png":"8ca3"};function s(t){var e=o(t);return n(e)}function o(t){if(!n.o(i,t)){var e=new Error("Cannot find module '"+t+"'");throw e.code="MODULE_NOT_FOUND",e}return i[t]}s.keys=function(){return Object.keys(i)},s.resolve=o,t.exports=s,s.id="7778"},"79b9":function(t,e,n){"use strict";n.r(e);var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"footer-wrapper"},[e("div",{staticClass:"copyright"},[t.isMobile?t._e():e("img",{staticClass:"logo",staticStyle:{"vertical-align":"text-bottom","padding-right":"10px"},attrs:{src:n("16db"),alt:"funplus"}}),t._m(0)])])},s=[function(){var t=this,e=t._self._c;return e("div",[e("p",{staticClass:"links-p"},[e("a",{staticClass:"links",attrs:{href:"https://nenglianghe.cn/compliance/privacyAgreement.html",target:"_blank"}},[t._v("隐私政策")]),e("a",{staticClass:"links",attrs:{href:"https://nenglianghe.cn/compliance/userAgreement.html",target:"_blank"}},[t._v("用户协议")]),e("a",{staticClass:"links",attrs:{href:"https://nenglianghe.cn/compliance/children.html",target:"_blank"}},[t._v("儿童个人信息保护政策")]),e("a",{staticClass:"links",attrs:{href:"http://koa.nenglianghe.cn/hook/",target:"_blank"}},[t._v("防沉迷")])]),e("p",[t._v("网站备案/许可证号: "),e("a",{attrs:{href:"https://beian.miit.gov.cn/",target:"_blank"}},[t._v("京ICP备16053236号-11")])]),e("p",[e("img",{staticClass:"gongan",attrs:{src:n("6373"),alt:""}}),e("a",{attrs:{href:"http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=11010502051888",target:"_blank"}},[t._v("京公网安备 11010502051888号")])])])}],o=n("2f62"),r={name:"CommonFooter",computed:{...Object(o["c"])(["isMobile"])}},a=r,c=(n("4047"),n("2877")),l=Object(c["a"])(a,i,s,!1,null,"5f93b707",null);e["default"]=l.exports},"7bc6":function(t,e,n){t.exports=n.p+"static/1751299200/img/boon-install-award.112be313.png"},"7d47":function(t,e,n){t.exports=n.p+"static/1751299200/img/install_reward_3.76a3b0b3.png"},"7ef4":function(t,e,n){t.exports=n.p+"static/1751299200/img/login_reward_2.a42cc374.png"},"7f18":function(t,e,n){t.exports=n.p+"static/1751299200/img/daily-reward-image.965961d0.png"},"83c1":function(t,e,n){},"89c9":function(t,e,n){t.exports=n.p+"static/1751299200/img/login_reward_1.dea43e02.png"},"8bb0":function(t,e,n){t.exports=n.p+"static/1751299200/img/boon-login-award.b4838254.png"},"8ca3":function(t,e,n){t.exports=n.p+"static/1751299200/img/login_reward_3.9b45a0c6.png"},"91d9":function(t,e,n){"use strict";n("34d6")},9234:function(t,e,n){t.exports=n.p+"static/1751299200/img/boon-login-award.9a9eae60.png"},"98e2":function(t,e,n){"use strict";n("9e37")},"9a1d":function(t,e,n){t.exports=n.p+"static/1751299200/img/install_reward_0.f9a9f8bf.png"},"9abc":function(t,e,n){},"9df3":function(t,e,n){var i={"./install_reward_0.png":"9a1d","./install_reward_1.png":"c23c","./install_reward_2.png":"6a8c","./install_reward_3.png":"5f39"};function s(t){var e=o(t);return n(e)}function o(t){if(!n.o(i,t)){var e=new Error("Cannot find module '"+t+"'");throw e.code="MODULE_NOT_FOUND",e}return i[t]}s.keys=function(){return Object.keys(i)},s.resolve=o,t.exports=s,s.id="9df3"},"9e37":function(t,e,n){},a5f3:function(t,e,n){t.exports=n.p+"static/1751299200/img/boon-install-award.70fbd832.png"},a9ac:function(t,e,n){"use strict";n("d080")},b519:function(t,e,n){var i={"./boon/boon-install-award.png":"a5f3","./ssd/boon-install-award.png":"29db","./ssv2/boon-install-award.png":"7bc6"};function s(t){var e=o(t);return n(e)}function o(t){if(!n.o(i,t)){var e=new Error("Cannot find module '"+t+"'");throw e.code="MODULE_NOT_FOUND",e}return i[t]}s.keys=function(){return Object.keys(i)},s.resolve=o,t.exports=s,s.id="b519"},c23c:function(t,e,n){t.exports=n.p+"static/1751299200/img/install_reward_1.e49e53f9.png"},c243:function(t,e,n){},c656:function(t,e,n){},cb26:function(t,e,n){"use strict";n("41a5")},cb74:function(t,e,n){"use strict";n.r(e);var i=function(){var t=this,e=t._self._c;return e("container-v2",{staticClass:"custom-pop"},[e("div",{staticClass:"header"},[e("span",[t._v("DURCHLESEN！")]),e("i",{on:{click:t.closePop}})]),e("div",{staticClass:"content"},[e("div",{staticClass:"strong"},[t._v("KEIN WIDERRUFSRECHT！")]),t._v(' Wenn du auf "Kaufen" tippst, stimmst du der unmittelbaren Erfüllung des Vertrags zu und akzeptierst, dass du dadurch dein Widerrufsrecht verlierst .(siehe auch '),e("a",{attrs:{href:"https://funplus.com/terms-conditions/#section-13",target:"_blank"}},[t._v("Rückerstattungsbedingungen")]),t._v("). ")]),e("div",{staticClass:"footer"},[e("div",{staticClass:"btn-cancel btn",on:{click:t.closePop}},[t._v("ABBRECHEN")]),e("div",{staticClass:"btn-confirm btn",on:{click:t.confirm}},[t._v("Zustimmen und fortfahren.")])])])},s=[],o=n("d0e9"),r={name:"privateConfirmPop",components:{ContainerV2:o["a"]},props:{option:{type:Object,default:()=>{}}},methods:{closePop(){this.option.no&&this.option.no(),this.$root.$emit("closePop")},confirm(){this.option.ok&&this.option.ok(),this.$root.$emit("changeDePopPrivacy"),this.$root.$emit("closePop")}}},a=r,c=(n("cb26"),n("2877")),l=Object(c["a"])(a,i,s,!1,null,"7591e3ea",null);e["default"]=l.exports},d080:function(t,e,n){},d7d2:function(t,e,n){"use strict";function i(t){return null!==t&&"object"===typeof t&&"constructor"in t&&t.constructor===Object}function s(t,e){void 0===t&&(t={}),void 0===e&&(e={}),Object.keys(e).forEach((function(n){"undefined"===typeof t[n]?t[n]=e[n]:i(e[n])&&i(t[n])&&Object.keys(e[n]).length>0&&s(t[n],e[n])}))}n.d(e,"a",(function(){return o})),n.d(e,"b",(function(){return a}));var o="undefined"!==typeof document?document:{},r={body:{},addEventListener:function(){},removeEventListener:function(){},activeElement:{blur:function(){},nodeName:""},querySelector:function(){return null},querySelectorAll:function(){return[]},getElementById:function(){return null},createEvent:function(){return{initEvent:function(){}}},createElement:function(){return{children:[],childNodes:[],style:{},setAttribute:function(){},getElementsByTagName:function(){return[]}}},createElementNS:function(){return{}},importNode:function(){return null},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};s(o,r);var a="undefined"!==typeof window?window:{},c={document:r,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState:function(){},pushState:function(){},go:function(){},back:function(){}},CustomEvent:function(){return this},addEventListener:function(){},removeEventListener:function(){},getComputedStyle:function(){return{getPropertyValue:function(){return""}}},Image:function(){},Date:function(){},screen:{},setTimeout:function(){},clearTimeout:function(){},matchMedia:function(){return{}}};s(a,c)},e002:function(t,e,n){"use strict";n("83c1")},e065:function(t,e,n){t.exports=n.p+"static/1751299200/img/boon-login-award.c5699e31.png"},f0b4:function(t,e,n){"use strict";n.r(e);var i=function(){var t=this,e=t._self._c;return e("container",{staticClass:"arrears-reminder-wrapper",class:[t.$i18n.locale,this.$store.state.gameinfo.gameCode,t.$gameName],attrs:{"hide-close":!0,option:t.config,title:t.$vt("howToUseDiamond"),id:"what-is-diamond-wrapper"}},[e("div",{staticClass:"description"},[t._v(t._s(t.$vt("whatIsDiamondTitle"))+" ")]),e("poster-swiper")],1)},s=[],o=n("efc1"),r=function(){var t=this,e=t._self._c;return e("Swiper",{staticClass:"my-swiper-wrapper",class:[t.$gameName],attrs:{options:t.swiperOptions}},[t.webCashierBanner.length?[t._l(t.webCashierBanner,(function(n){return e("SwiperSlide",{key:n.imageUrl},[e("img",{attrs:{src:n.imageUrl,alt:""}}),n.jumpUrl?e("a",{attrs:{href:n.jumpUrl,target:"_blank"}}):t._e()])})),e("div",{staticClass:"swiper-pagination",attrs:{slot:"pagination"},slot:"pagination"},t._l(Object.keys(t.webCashierBanner),(function(n){return e("div",{key:n,class:["pagination-dot",{"pagination-dot_active":t.swiperIndex===+n}]})})),0)]:t._e()],2)},a=[],c=n("7212"),l=(n("f4bd"),{name:"PosterSwiper",data(){const t=this;return{swiperOptions:{autoplay:{disableOnInteraction:!1},on:{slideChangeTransitionStart:function(){t.swiperIndex=this.activeIndex}}},swiperIndex:0,webCashierBanner:this.$imageLoader("whatsDiamond",[])}},components:{Swiper:c["Swiper"],SwiperSlide:c["SwiperSlide"]}}),d=l,u=(n("3c32"),n("2877")),p=Object(u["a"])(d,r,a,!1,null,"f793779a",null),h=p.exports,f={name:"WhatIsDiamond",props:{option:Object},data(){return{config:{confirmBtnTxt:this.$t("got-it")}}},components:{PosterSwiper:h,Container:o["a"]}},g=f,m=(n("91d9"),Object(u["a"])(g,i,s,!1,null,"1b49d88b",null));e["default"]=m.exports},f1d7:function(t,e,n){t.exports=n.p+"static/1751299200/img/install_reward_2.9f56aa98.png"},f4fc:function(t,e,n){"use strict";n.d(e,"a",(function(){return o})),n.d(e,"c",(function(){return a})),n.d(e,"F",(function(){return c})),n.d(e,"n",(function(){return l})),n.d(e,"I",(function(){return d})),n.d(e,"e",(function(){return u})),n.d(e,"E",(function(){return p})),n.d(e,"i",(function(){return h})),n.d(e,"J",(function(){return f})),n.d(e,"K",(function(){return g})),n.d(e,"v",(function(){return m})),n.d(e,"t",(function(){return _})),n.d(e,"M",(function(){return v})),n.d(e,"L",(function(){return b})),n.d(e,"x",(function(){return w})),n.d(e,"w",(function(){return y})),n.d(e,"u",(function(){return C})),n.d(e,"G",(function(){return k})),n.d(e,"h",(function(){return P})),n.d(e,"j",(function(){return E})),n.d(e,"l",(function(){return $})),n.d(e,"o",(function(){return S})),n.d(e,"H",(function(){return I})),n.d(e,"q",(function(){return L})),n.d(e,"p",(function(){return O})),n.d(e,"k",(function(){return D})),n.d(e,"d",(function(){return A})),n.d(e,"A",(function(){return R})),n.d(e,"r",(function(){return x})),n.d(e,"s",(function(){return T})),n.d(e,"B",(function(){return N})),n.d(e,"C",(function(){return U})),n.d(e,"y",(function(){return j})),n.d(e,"z",(function(){return M})),n.d(e,"g",(function(){return V})),n.d(e,"m",(function(){return W})),n.d(e,"f",(function(){return K})),n.d(e,"D",(function(){return F})),n.d(e,"b",(function(){return H}));var i=n("d7d2");class s{constructor(t){const e=this;for(let n=0;n<t.length;n+=1)e[n]=t[n];return e.length=t.length,this}}function o(t,e){const n=[];let o=0;if(t&&!e&&t instanceof s)return t;if(t)if("string"===typeof t){let s,r;const a=t.trim();if(a.indexOf("<")>=0&&a.indexOf(">")>=0){let t="div";for(0===a.indexOf("<li")&&(t="ul"),0===a.indexOf("<tr")&&(t="tbody"),0!==a.indexOf("<td")&&0!==a.indexOf("<th")||(t="tr"),0===a.indexOf("<tbody")&&(t="table"),0===a.indexOf("<option")&&(t="select"),r=i["a"].createElement(t),r.innerHTML=a,o=0;o<r.childNodes.length;o+=1)n.push(r.childNodes[o])}else for(s=e||"#"!==t[0]||t.match(/[ .<>:~]/)?(e||i["a"]).querySelectorAll(t.trim()):[i["a"].getElementById(t.trim().split("#")[1])],o=0;o<s.length;o+=1)s[o]&&n.push(s[o])}else if(t.nodeType||t===i["b"]||t===i["a"])n.push(t);else if(t.length>0&&t[0].nodeType)for(o=0;o<t.length;o+=1)n.push(t[o]);return new s(n)}function r(t){const e=[];for(let n=0;n<t.length;n+=1)-1===e.indexOf(t[n])&&e.push(t[n]);return e}function a(t){if("undefined"===typeof t)return this;const e=t.split(" ");for(let n=0;n<e.length;n+=1)for(let t=0;t<this.length;t+=1)"undefined"!==typeof this[t]&&"undefined"!==typeof this[t].classList&&this[t].classList.add(e[n]);return this}function c(t){const e=t.split(" ");for(let n=0;n<e.length;n+=1)for(let t=0;t<this.length;t+=1)"undefined"!==typeof this[t]&&"undefined"!==typeof this[t].classList&&this[t].classList.remove(e[n]);return this}function l(t){return!!this[0]&&this[0].classList.contains(t)}function d(t){const e=t.split(" ");for(let n=0;n<e.length;n+=1)for(let t=0;t<this.length;t+=1)"undefined"!==typeof this[t]&&"undefined"!==typeof this[t].classList&&this[t].classList.toggle(e[n]);return this}function u(t,e){if(1===arguments.length&&"string"===typeof t)return this[0]?this[0].getAttribute(t):void 0;for(let n=0;n<this.length;n+=1)if(2===arguments.length)this[n].setAttribute(t,e);else for(const e in t)this[n][e]=t[e],this[n].setAttribute(e,t[e]);return this}function p(t){for(let e=0;e<this.length;e+=1)this[e].removeAttribute(t);return this}function h(t,e){let n;if("undefined"!==typeof e){for(let i=0;i<this.length;i+=1)n=this[i],n.dom7ElementDataStorage||(n.dom7ElementDataStorage={}),n.dom7ElementDataStorage[t]=e;return this}if(n=this[0],n){if(n.dom7ElementDataStorage&&t in n.dom7ElementDataStorage)return n.dom7ElementDataStorage[t];const e=n.getAttribute("data-"+t);return e||void 0}}function f(t){for(let e=0;e<this.length;e+=1){const n=this[e].style;n.webkitTransform=t,n.transform=t}return this}function g(t){"string"!==typeof t&&(t+="ms");for(let e=0;e<this.length;e+=1){const n=this[e].style;n.webkitTransitionDuration=t,n.transitionDuration=t}return this}function m(...t){let[e,n,i,s]=t;function r(t){const e=t.target;if(!e)return;const s=t.target.dom7EventData||[];if(s.indexOf(t)<0&&s.unshift(t),o(e).is(n))i.apply(e,s);else{const t=o(e).parents();for(let e=0;e<t.length;e+=1)o(t[e]).is(n)&&i.apply(t[e],s)}}function a(t){const e=t&&t.target&&t.target.dom7EventData||[];e.indexOf(t)<0&&e.unshift(t),i.apply(this,e)}"function"===typeof t[1]&&([e,i,s]=t,n=void 0),s||(s=!1);const c=e.split(" ");let l;for(let o=0;o<this.length;o+=1){const t=this[o];if(n)for(l=0;l<c.length;l+=1){const e=c[l];t.dom7LiveListeners||(t.dom7LiveListeners={}),t.dom7LiveListeners[e]||(t.dom7LiveListeners[e]=[]),t.dom7LiveListeners[e].push({listener:i,proxyListener:r}),t.addEventListener(e,r,s)}else for(l=0;l<c.length;l+=1){const e=c[l];t.dom7Listeners||(t.dom7Listeners={}),t.dom7Listeners[e]||(t.dom7Listeners[e]=[]),t.dom7Listeners[e].push({listener:i,proxyListener:a}),t.addEventListener(e,a,s)}}return this}function _(...t){let[e,n,i,s]=t;"function"===typeof t[1]&&([e,i,s]=t,n=void 0),s||(s=!1);const o=e.split(" ");for(let r=0;r<o.length;r+=1){const t=o[r];for(let e=0;e<this.length;e+=1){const o=this[e];let r;if(!n&&o.dom7Listeners?r=o.dom7Listeners[t]:n&&o.dom7LiveListeners&&(r=o.dom7LiveListeners[t]),r&&r.length)for(let e=r.length-1;e>=0;e-=1){const n=r[e];i&&n.listener===i||i&&n.listener&&n.listener.dom7proxy&&n.listener.dom7proxy===i?(o.removeEventListener(t,n.proxyListener,s),r.splice(e,1)):i||(o.removeEventListener(t,n.proxyListener,s),r.splice(e,1))}}}return this}function v(...t){const e=t[0].split(" "),n=t[1];for(let o=0;o<e.length;o+=1){const r=e[o];for(let e=0;e<this.length;e+=1){const o=this[e];let a;try{a=new i["b"].CustomEvent(r,{detail:n,bubbles:!0,cancelable:!0})}catch(s){a=i["a"].createEvent("Event"),a.initEvent(r,!0,!0),a.detail=n}o.dom7EventData=t.filter((t,e)=>e>0),o.dispatchEvent(a),o.dom7EventData=[],delete o.dom7EventData}}return this}function b(t){const e=["webkitTransitionEnd","transitionend"],n=this;let i;function s(o){if(o.target===this)for(t.call(this,o),i=0;i<e.length;i+=1)n.off(e[i],s)}if(t)for(i=0;i<e.length;i+=1)n.on(e[i],s);return this}function w(t){if(this.length>0){if(t){const t=this.styles();return this[0].offsetWidth+parseFloat(t.getPropertyValue("margin-right"))+parseFloat(t.getPropertyValue("margin-left"))}return this[0].offsetWidth}return null}function y(t){if(this.length>0){if(t){const t=this.styles();return this[0].offsetHeight+parseFloat(t.getPropertyValue("margin-top"))+parseFloat(t.getPropertyValue("margin-bottom"))}return this[0].offsetHeight}return null}function C(){if(this.length>0){const t=this[0],e=t.getBoundingClientRect(),n=i["a"].body,s=t.clientTop||n.clientTop||0,o=t.clientLeft||n.clientLeft||0,r=t===i["b"]?i["b"].scrollY:t.scrollTop,a=t===i["b"]?i["b"].scrollX:t.scrollLeft;return{top:e.top+r-s,left:e.left+a-o}}return null}function k(){return this[0]?i["b"].getComputedStyle(this[0],null):{}}function P(t,e){let n;if(1===arguments.length){if("string"!==typeof t){for(n=0;n<this.length;n+=1)for(let e in t)this[n].style[e]=t[e];return this}if(this[0])return i["b"].getComputedStyle(this[0],null).getPropertyValue(t)}if(2===arguments.length&&"string"===typeof t){for(n=0;n<this.length;n+=1)this[n].style[t]=e;return this}return this}function E(t){if(!t)return this;for(let e=0;e<this.length;e+=1)if(!1===t.call(this[e],e,this[e]))return this;return this}function $(t){const e=[],n=this;for(let i=0;i<n.length;i+=1)t.call(n[i],i,n[i])&&e.push(n[i]);return new s(e)}function S(t){if("undefined"===typeof t)return this[0]?this[0].innerHTML:void 0;for(let e=0;e<this.length;e+=1)this[e].innerHTML=t;return this}function I(t){if("undefined"===typeof t)return this[0]?this[0].textContent.trim():null;for(let e=0;e<this.length;e+=1)this[e].textContent=t;return this}function L(t){const e=this[0];let n,r;if(!e||"undefined"===typeof t)return!1;if("string"===typeof t){if(e.matches)return e.matches(t);if(e.webkitMatchesSelector)return e.webkitMatchesSelector(t);if(e.msMatchesSelector)return e.msMatchesSelector(t);for(n=o(t),r=0;r<n.length;r+=1)if(n[r]===e)return!0;return!1}if(t===i["a"])return e===i["a"];if(t===i["b"])return e===i["b"];if(t.nodeType||t instanceof s){for(n=t.nodeType?[t]:t,r=0;r<n.length;r+=1)if(n[r]===e)return!0;return!1}return!1}function O(){let t,e=this[0];if(e){t=0;while(null!==(e=e.previousSibling))1===e.nodeType&&(t+=1);return t}}function D(t){if("undefined"===typeof t)return this;const e=this.length;let n;return t>e-1?new s([]):t<0?(n=e+t,new s(n<0?[]:[this[n]])):new s([this[t]])}function A(...t){let e;for(let n=0;n<t.length;n+=1){e=t[n];for(let t=0;t<this.length;t+=1)if("string"===typeof e){const n=i["a"].createElement("div");n.innerHTML=e;while(n.firstChild)this[t].appendChild(n.firstChild)}else if(e instanceof s)for(let n=0;n<e.length;n+=1)this[t].appendChild(e[n]);else this[t].appendChild(e)}return this}function R(t){let e,n;for(e=0;e<this.length;e+=1)if("string"===typeof t){const s=i["a"].createElement("div");for(s.innerHTML=t,n=s.childNodes.length-1;n>=0;n-=1)this[e].insertBefore(s.childNodes[n],this[e].childNodes[0])}else if(t instanceof s)for(n=0;n<t.length;n+=1)this[e].insertBefore(t[n],this[e].childNodes[0]);else this[e].insertBefore(t,this[e].childNodes[0]);return this}function x(t){return this.length>0?t?this[0].nextElementSibling&&o(this[0].nextElementSibling).is(t)?new s([this[0].nextElementSibling]):new s([]):this[0].nextElementSibling?new s([this[0].nextElementSibling]):new s([]):new s([])}function T(t){const e=[];let n=this[0];if(!n)return new s([]);while(n.nextElementSibling){const i=n.nextElementSibling;t?o(i).is(t)&&e.push(i):e.push(i),n=i}return new s(e)}function N(t){if(this.length>0){const e=this[0];return t?e.previousElementSibling&&o(e.previousElementSibling).is(t)?new s([e.previousElementSibling]):new s([]):e.previousElementSibling?new s([e.previousElementSibling]):new s([])}return new s([])}function U(t){const e=[];let n=this[0];if(!n)return new s([]);while(n.previousElementSibling){const i=n.previousElementSibling;t?o(i).is(t)&&e.push(i):e.push(i),n=i}return new s(e)}function j(t){const e=[];for(let n=0;n<this.length;n+=1)null!==this[n].parentNode&&(t?o(this[n].parentNode).is(t)&&e.push(this[n].parentNode):e.push(this[n].parentNode));return o(r(e))}function M(t){const e=[];for(let n=0;n<this.length;n+=1){let i=this[n].parentNode;while(i)t?o(i).is(t)&&e.push(i):e.push(i),i=i.parentNode}return o(r(e))}function V(t){let e=this;return"undefined"===typeof t?new s([]):(e.is(t)||(e=e.parents(t).eq(0)),e)}function W(t){const e=[];for(let n=0;n<this.length;n+=1){const i=this[n].querySelectorAll(t);for(let t=0;t<i.length;t+=1)e.push(i[t])}return new s(e)}function K(t){const e=[];for(let n=0;n<this.length;n+=1){const i=this[n].childNodes;for(let n=0;n<i.length;n+=1)t?1===i[n].nodeType&&o(i[n]).is(t)&&e.push(i[n]):1===i[n].nodeType&&e.push(i[n])}return new s(r(e))}function F(){for(let t=0;t<this.length;t+=1)this[t].parentNode&&this[t].parentNode.removeChild(this[t]);return this}function H(...t){const e=this;let n,i;for(n=0;n<t.length;n+=1){const s=o(t[n]);for(i=0;i<s.length;i+=1)e[e.length]=s[i],e.length+=1}return e}o.fn=s.prototype,o.Class=s,o.Dom7=s;"resize scroll".split(" ")},f691:function(t,e,n){var i={"./boon/boon-login-award.png":"9234","./ssd/boon-login-award.png":"e065","./ssv2/boon-login-award.png":"8bb0"};function s(t){var e=o(t);return n(e)}function o(t){if(!n.o(i,t)){var e=new Error("Cannot find module '"+t+"'");throw e.code="MODULE_NOT_FOUND",e}return i[t]}s.keys=function(){return Object.keys(i)},s.resolve=o,t.exports=s,s.id="f691"},fc88:function(t,e,n){t.exports=n.p+"static/1751299200/img/login_reward_2.a42cc374.png"}}]);
//# sourceMappingURL=chunk-functions.e3d7a4ee.js.map