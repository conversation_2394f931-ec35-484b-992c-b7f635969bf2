{"version": 3, "sources": ["webpack:///./src/utils/utilsSdk2.js", "webpack:///./src/views/PaySdk2.vue", "webpack:///./src/components/sdk/sdk2Header.vue", "webpack:///src/components/sdk/sdk2Header.vue", "webpack:///./src/components/sdk/sdk2Header.vue?c6d7", "webpack:///./src/components/sdk/sdk2Header.vue?8463", "webpack:///./src/components/sdk/sdk2UserAndGameInfo.vue", "webpack:///src/components/sdk/sdk2UserAndGameInfo.vue", "webpack:///./src/components/sdk/sdk2UserAndGameInfo.vue?0411", "webpack:///./src/components/sdk/sdk2UserAndGameInfo.vue?de7b", "webpack:///./src/components/sdk/sdk2PackageInfo.vue", "webpack:///src/components/sdk/sdk2PackageInfo.vue", "webpack:///./src/components/sdk/sdk2PackageInfo.vue?3202", "webpack:///./src/components/sdk/sdk2PackageInfo.vue?f44f", "webpack:///./src/components/sdk/sdk2Tip.vue", "webpack:///src/components/sdk/sdk2Tip.vue", "webpack:///./src/components/sdk/sdk2Tip.vue?8b16", "webpack:///./src/components/sdk/sdk2Tip.vue?d541", "webpack:///src/views/PaySdk2.vue", "webpack:///./src/views/PaySdk2.vue?3754", "webpack:///./src/views/PaySdk2.vue?060d", "webpack:///./src/components/sdk/sdk2PackageInfo.vue?a06a", "webpack:///./src/components/sdk/sdk2Tip.vue?eb13", "webpack:///./src/views/PaySdk2.vue?3779", "webpack:///./src/components/sdk/sdk2Header.vue?24d0", "webpack:///./src/components/sdk/sdk2UserAndGameInfo.vue?c896"], "names": ["backAppGame", "a", "document", "createElement", "href", "device", "ios", "window", "$gcbk", "style", "display", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "render", "_vm", "this", "_c", "_self", "class", "$gameName", "staticClass", "staticStyle", "directives", "name", "rawName", "value", "hasCoupon", "expression", "showCouponPop", "attrs", "requestLoading", "on", "$event", "judgeRisk", "staticRenderFns", "goStore", "_v", "methods", "jumpToUrl", "process", "toUpperCase", "component", "gameLogo", "_s", "gameName", "$t", "$store", "state", "userinfo", "data", "goodsName", "<PERSON><PERSON><PERSON><PERSON>", "no_tax_price", "currencyUnit", "calState", "isShow", "description", "$root", "$emit", "type", "discountPrice", "_e", "components", "OverSizeScale", "CommonPart", "computed", "mapState", "mapGetters", "temp", "chosen<PERSON><PERSON><PERSON><PERSON>", "defaultCoupon", "defaultDiscountInfo", "feType", "tempCoupon", "discount_amount", "rateWidthOutPercent", "defaultRebate", "defaultRebateInfo", "num", "Math", "floor", "coin", "level_coin", "txt", "$vt", "created", "$on", "defaultPackageName", "_calState", "mixins", "PayMixin", "Sdk2Tip", "Sdk2PackageInfo", "sdk2UserAndGameInfo", "Sdk2Header", "CheckoutFooter", "CouponChoose", "ChannelChoose", "LoginModule", "DirectGiftPackage", "couponList", "includes", "length", "params", "urlParams", "oid", "logForSdk2OpenedSuccess"], "mappings": "sIAAA,kDAEO,SAASA,IAEd,MAAMC,EAAIC,SAASC,cAAc,KACjCF,EAAEG,KAAOC,OAAOC,MACZC,OAAOC,MAAM,+BACbD,OAAOC,MAAM,mCACjBP,EAAEQ,MAAMC,QAAU,OAElBR,SAASS,KAAKC,YAAYX,GAE1BA,EAAEY,QAEFX,SAASS,KAAKG,YAAYb,K,2CCd5B,IAAIc,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,MAAM,CAAC,mBAAoBJ,EAAIK,YAAY,CAACH,EAAG,eAAeA,EAAG,MAAM,CAACI,YAAY,gBAAgB,CAACJ,EAAG,2BAA2BA,EAAG,MAAM,CAACI,YAAY,kBAAkB,CAACJ,EAAG,MAAM,CAACI,YAAY,kBAAkB,CAACJ,EAAG,sBAAsB,CAACK,YAAY,CAAC,QAAU,UAAUL,EAAG,qBAAqBA,EAAG,gBAAgB,CAACM,WAAW,CAAC,CAACC,KAAK,OAAOC,QAAQ,SAASC,MAAOX,EAAIY,UAAWC,WAAW,gBAAgBX,EAAG,kBAAkBA,EAAG,eAAe,CAACM,WAAW,CAAC,CAACC,KAAK,OAAOC,QAAQ,SAASC,OAAO,EAAOE,WAAW,YAAYX,EAAG,aAAa,MAAM,GAAGA,EAAG,kBAAkB,CAACT,MAAO,CAAE,UAAWO,EAAIc,eAAiB,EAAI,GAAKC,MAAM,CAAC,kBAAkBf,EAAIgB,gBAAgBC,GAAG,CAAC,cAAgB,SAASC,GAAQ,OAAOlB,EAAImB,iBAAiB,IAEtyBC,EAAkB,G,wECFlBrB,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,SAAS,CAACA,EAAG,MAAM,CAACI,YAAY,WAAWW,GAAG,CAAC,MAAQjB,EAAIhB,cAAc,CAACkB,EAAG,OAAOA,EAAG,MAAM,CAACI,YAAY,YAAYJ,EAAG,MAAM,CAACI,YAAY,aAAaW,GAAG,CAAC,MAAQjB,EAAIqB,UAAU,CAACnB,EAAG,IAAI,CAACI,YAAY,mBAAmBN,EAAIsB,GAAG,WAAWpB,EAAG,MAAM,CAACM,WAAW,CAAC,CAACC,KAAK,OAAOC,QAAQ,SAASC,MAAOX,EAAIR,MAAM,sBAAuBqB,WAAW,gCAAgCP,YAAY,sBAElcc,EAAkB,G,wBCEP,GACfX,KAAA,aACAc,QAAA,CACAF,UACAG,eAAAC,s9DAAA,6BAAApB,UAAAqB,iBAEA1C,qBCVkW,I,wBCQ9V2C,EAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,MAIa,EAAAA,E,QCnBX5B,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,UAAU,CAACI,YAAY,gBAAgB,CAACJ,EAAG,MAAM,CAACM,WAAW,CAAC,CAACC,KAAK,OAAOC,QAAQ,SAASC,MAAOX,EAAI4B,SAAUf,WAAW,aAAaP,YAAY,SAASJ,EAAG,MAAM,CAACI,YAAY,QAAQ,CAACJ,EAAG,MAAM,CAACI,YAAY,aAAa,CAACN,EAAIsB,GAAGtB,EAAI6B,GAAG7B,EAAI8B,aAAa5B,EAAG,MAAM,CAACI,YAAY,aAAa,CAACN,EAAIsB,GAAGtB,EAAI6B,GAAG7B,EAAI+B,GAAG,YAAY,KAAK/B,EAAI6B,GAAG7B,EAAIgC,OAAOC,MAAMC,SAASzB,MAAQ,aAErbW,EAAkB,GCDP,GACfX,KAAA,sBACA0B,OACA,OACAL,SAAA,KAAAtC,MAAA,qBACAoC,SAAA,KAAApC,MAAA,sBCN2W,ICQvW,G,UAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,OAIa,I,QCnBXO,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,cAAc,CAACE,MAAM,CAAC,eAAgBJ,EAAIK,WAAWU,MAAM,CAAC,aAAaf,EAAI+B,GAAG,qBAAqB,GAAK,iBAAiB,CAAC7B,EAAG,MAAM,CAACI,YAAY,mBAAmB,CAACJ,EAAG,MAAM,CAACI,YAAY,iBAAiBJ,EAAG,MAAM,CAACI,YAAY,gBAAgB,CAACJ,EAAG,MAAM,CAACI,YAAY,QAAQ,CAACN,EAAIsB,GAAGtB,EAAI6B,GAAG7B,EAAIoC,WAAa,QAAQlC,EAAG,MAAM,CAACI,YAAY,SAAS,CAACN,EAAIsB,GAAGtB,EAAI6B,GAAG7B,EAAIqC,cAAcC,cAActC,EAAI6B,GAAG7B,EAAIuC,qBAAsBvC,EAAIwC,SAASC,OAAQvC,EAAG,MAAM,CAACI,YAAY,kBAAkB,CAACJ,EAAG,MAAM,CAACI,YAAY,gBAAgBJ,EAAG,MAAM,CAACI,YAAY,eAAe,CAACJ,EAAG,kBAAkB,CAACF,EAAIsB,GAAGtB,EAAI6B,GAAG7B,EAAIwC,SAASE,iBAAiB,GAAGxC,EAAG,MAAM,CAACI,YAAY,WAAWW,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOlB,EAAI2C,MAAMC,MAAM,UAAW,WAAY,CAAEC,KAAM,sBAAwB7C,EAAIwC,SAASM,cAAe5C,EAAG,MAAM,CAACI,YAAY,YAAY,CAACN,EAAIsB,GAAGtB,EAAI6B,GAAG7B,EAAIwC,SAASM,kBAAkB9C,EAAI+C,OAAO/C,EAAI+C,QAEj8B3B,EAAkB,G,oCCGP,GACfX,KAAA,kBACAuC,WAAA,CAAAC,qBAAAC,mBACAf,OACA,OACAC,UAAA,MAGAe,SAAA,IACAC,eAAA,qBACAC,eAAA,mCACAD,eAAA,uFACAZ,WACA,MAAAc,EAAA,CACAT,KAAA,GACAJ,QAAA,EACAC,YAAA,GACAI,cAAA,IAGAS,EAAA,KAAAA,aACAC,EAAA,KAAAC,oBAGA,wBAAAF,EAAAV,OAAAU,EAAAG,QAAA,0BAAAF,EAAAX,KAAA,CACA,MAAAc,EAAA,qBAAAJ,EAAAV,KAAAU,EAAAC,EACAF,EAAAb,QAAA,EAEAa,EAAAR,cAAA,KAAAa,EAAAC,kBAAA,KAAArB,eACAe,EAAAZ,YAAA,KAAAX,GAAA,cAAA4B,EAAAd,KAAA,GAAAc,EAAAE,oBAAA,UACAP,EAAAT,KAAAc,EAAAd,KAIA,MAAAiB,EAAA,KAAAC,kBACA,+BAAAR,EAAAV,OAAAU,EAAAG,QAAA,wBAAAI,EAAAjB,KAAA,CACA,MAAAc,EAAA,4BAAAJ,EAAAV,KAAAU,EAAAO,EACAR,EAAAb,QAAA,EAEA,MAAAuB,EAAAC,KAAAC,MAAAP,EAAAQ,KAAAR,EAAAS,YACAC,EAAA,GAAAL,KAAA,KAAAM,IAAA,eACAhB,EAAAZ,YAAA,KAAAX,GAAA,cAAA4B,EAAAd,KAAA,GAAAwB,IACAf,EAAAT,KAAAc,EAAAd,KAEA,OAAAS,IAGAiB,UACA,KAAA5B,MAAA6B,IAAA,wBAAA/D,IACAlB,OAAAkF,mBAAA,KAAArC,UAAA3B,IAEAlB,OAAAmF,UAAA,SAAAlC,WCxDuW,ICQnW,G,UAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,OAIa,I,QCnBXzC,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,cAAc,CAACI,YAAY,aAAa,CAACJ,EAAG,MAAM,CAACI,YAAY,QAAQ,CAACN,EAAIsB,GAAG,gFAAgFpB,EAAG,OAAO,CAACe,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOlB,EAAI2C,MAAMC,MAAM,UAAW,WAAY,CAAEC,KAAM,cAAe,CAAC7C,EAAIsB,GAAG,qBAAqBtB,EAAIsB,GAAG,WAElXF,EAAkB,GCCP,GACfX,KAAA,UACAuC,WAAA,CAAAE,oBCL+V,ICQ3V,G,UAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,OAIa,I,oBCgBA,GACfzC,KAAA,MACAkE,OAAA,CAAAC,QACA5B,WAAA,CACA6B,UACAC,kBACAC,sBACAC,aACAC,sBACAC,oBACAC,qBACAC,mBACAC,0BAEAlC,SAAA,IACAC,eAAA,gCACAA,eAAA,gBAEAjB,OACA,OACAvB,WAAA,EACAE,eAAA,IAGAyD,UACA,KAAA5B,MAAA6B,IAAA,uBAAAc,IACA,KAAA/B,aAAAV,MAAA,KAAAU,aAAAV,KAAA0C,SAAA,oBACA,KAAA3E,WAAA,EAGA,KAAAA,UAAA0E,EAAAE,OAAA,IAEA,KAAA7C,MAAA6B,IAAA,gBAAA7D,IACA,KAAAG,cAAAH,IAGA,KAAAgC,MAAA6B,IAAA,WAAAvC,IACA,MAAAwD,EAAA,GACA,KAAAC,UAAAC,MAAAF,EAAAE,IAAA,KAAAD,UAAAC,KACA1D,GAAA2D,qCAAAH,OC1EgV,ICQ5U,G,UAAY,eACd,EACA1F,EACAqB,GACA,EACA,KACA,WACA,OAIa,e,oHCnBf,W,oCCAA,W,kCCAA,W,kCCAA,W,yDCAA", "file": "js/pageSdkV2.808e7cc5.js", "sourcesContent": ["import device from 'current-device'\n\nexport function backAppGame () {\n  // 动态创建一个 <a> 标签\n  const a = document.createElement('a')\n  a.href = device.ios()\n    ? window.$gcbk('gameinfo.appGameDeepLinkIos')\n    : window.$gcbk('gameinfo.appGameDeepLinkAndroid')\n  a.style.display = 'none' // 隐藏标签\n  // 将 <a> 标签添加到 document.body 中\n  document.body.appendChild(a)\n  // 触发点击事件\n  a.click()\n  // 移除动态创建的 <a> 标签\n  document.body.removeChild(a)\n}\n", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{class:['shopping-wrapper', _vm.$gameName]},[_c('sdk2-header'),_c('div',{staticClass:\"body-wrapper\"},[_c('sdk2-user-and-game-info'),_c('div',{staticClass:\"scroll-wrapper\"},[_c('div',{staticClass:\"scroll-content\"},[_c('direct-gift-package',{staticStyle:{\"display\":\"none\"}}),_c('sdk2-package-info'),_c('coupon-choose',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.hasCoupon),expression:\"hasCoupon\"}]}),_c('channel-choose'),_c('login-module',{directives:[{name:\"show\",rawName:\"v-show\",value:(false),expression:\"false\"}]}),_c('sdk2-tip')],1)])],1),_c('checkout-footer',{style:({ 'z-index': _vm.showCouponPop ? -1 : 1 }),attrs:{\"request-loading\":_vm.requestLoading},on:{\"purchaseGoods\":function($event){return _vm.judgeRisk()}}})],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('header',[_c('div',{staticClass:\"btn-back\",on:{\"click\":_vm.backAppGame}},[_c('i')]),_c('div',{staticClass:\"fp-logo\"}),_c('div',{staticClass:\"toggle-btn\",on:{\"click\":_vm.goStore}},[_c('i',{staticClass:\"diamond-toggle\"}),_vm._v(\" Topup \"),_c('img',{directives:[{name:\"lazy\",rawName:\"v-lazy\",value:(_vm.$gcbk('images.iconDiamond')),expression:\"$gcbk('images.iconDiamond')\"}],staticClass:\"diamond-icon\"})])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<script>\nimport { jumpToUrl } from '@/utils/utils'\nimport { backAppGame } from '@/utils/utilsSdk2'\n\nexport default {\n  name: 'sdk2Header',\n  methods: {\n    goStore () {\n      jumpToUrl(process.env[`VUE_APP_PREFIX_STORE_${this.$gameName.toUpperCase()}`])\n    },\n    backAppGame\n  }\n}\n</script>\n\n<template>\n  <header>\n    <div @click=\"backAppGame\" class=\"btn-back\">\n      <i></i>\n    </div>\n    <div class=\"fp-logo\"></div>\n    <div @click=\"goStore\" class=\"toggle-btn\">\n      <i class=\"diamond-toggle\"></i>\n      Topup\n      <img class=\"diamond-icon\" v-lazy=\"$gcbk('images.iconDiamond')\"/>\n    </div>\n  </header>\n</template>\n\n<style scoped lang=\"scss\">\n@use \"~@/utils/utils.scss\" as utils;\nheader {\n  background: black;\n  width: 100%;\n  flex-shrink: 0;\n  height: 112px;\n  @include utils.flexCenter;\n  justify-content: flex-start;\n  padding-left: 10px;\n  padding-right: 30px;\n  padding-bottom: 18px;\n\n  .btn-back{\n    font-weight: 600;\n    font-size: 28px;\n    color: #F4FBFF;\n    line-height: 40px;\n    text-stroke: 1px #000000;\n    text-align: justify;\n    font-style: normal;\n    @include utils.flexCenter;\n    margin-left: 20px;\n\n    i{\n      @include utils.bgCenterForSdk2('header/icon-back.png', 48px, 48px);\n      display: inline-block;\n    }\n  }\n  .fp-logo{\n    display: inline-block;\n    height: 35px;\n    margin-left: 116px;\n\n    @include utils.bgCenterForSdk2('header/sdk2-title-image.png', 308px, 41px);\n  }\n  //.tip-btn{\n  //  display: inline-block;\n  //  @include utils.bgCenterForSdk2('header/diamond-placeholder.png', 20px, 20px);\n  //}\n\n  .toggle-btn{\n    margin-left: auto;\n    width: 183px;\n    flex-shrink: 0;\n    @include utils.flexCenter;\n    font-family: SourceHanSansCN, SourceHanSansCN;\n    font-weight: bold;\n    font-size: 24px;\n    color: white;\n    border-radius: 10px;\n    border: 2px solid #FFFFFF;\n    backdrop-filter: blur(10px);\n    height: 54px;\n\n    .diamond-toggle{\n      @include utils.bgCenterForSdk2('header/icon-toggle.png', 24px, 24px);\n      margin-right: 6px;\n    }\n    .diamond-icon{\n      width: 46px;\n      height: 46px;\n      margin-left: 2px;\n      background-size: cover;\n      display: inline-block;\n    }\n  }\n}\n</style>\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./sdk2Header.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./sdk2Header.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./sdk2Header.vue?vue&type=template&id=70d0324b&scoped=true\"\nimport script from \"./sdk2Header.vue?vue&type=script&lang=js\"\nexport * from \"./sdk2Header.vue?vue&type=script&lang=js\"\nimport style0 from \"./sdk2Header.vue?vue&type=style&index=0&id=70d0324b&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"70d0324b\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('section',{staticClass:\"info-wrapper\"},[_c('img',{directives:[{name:\"lazy\",rawName:\"v-lazy\",value:(_vm.gameLogo),expression:\"gameLogo\"}],staticClass:\"logo\"}),_c('div',{staticClass:\"info\"},[_c('div',{staticClass:\"game-name\"},[_vm._v(_vm._s(_vm.gameName))]),_c('div',{staticClass:\"user-name\"},[_vm._v(_vm._s(_vm.$t('my-role'))+\": \"+_vm._s(_vm.$store.state.userinfo.name || '-'))])])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<script>\nexport default {\n  name: 'sdk2UserAndGameInfo',\n  data () {\n    return {\n      gameName: this.$gcbk('gameinfo.gameName'),\n      gameLogo: this.$gcbk('images.logoPath')\n    }\n  }\n}\n</script>\n\n<template>\n  <section class=\"info-wrapper\">\n    <img v-lazy=\"gameLogo\" class=\"logo\"/>\n    <div class=\"info\">\n      <div class=\"game-name\">{{ gameName }}</div>\n      <div class=\"user-name\">{{ $t('my-role') }}: {{ $store.state.userinfo.name || '-' }}</div>\n    </div>\n  </section>\n</template>\n\n<style scoped lang=\"scss\">\n@use \"~@/utils/utils.scss\" as utils;\n\n.info-wrapper{\n  position: absolute;\n  width: calc(100% - 50px);\n  top: -18px;\n  left: 50%;\n  transform: translateX(-50%);\n  border-radius: 15px; /* 圆角效果 */\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); /* 阴影效果 */\n  background: rgba(255, 255, 255, 0.8); /* 半透明白色背景 */\n  backdrop-filter: blur(15px); /* 毛玻璃效果 */\n  -webkit-backdrop-filter: blur(15px); /* 兼容性处理 */\n  border: 2px solid white;\n  padding: 20px 24px;\n  @include utils.flexCenter;\n  justify-content: flex-start;\n\n  .logo{\n    width: 88px;\n    display: inline-block;\n  }\n\n  .info{\n    margin-left: 12px;\n    .game-name{\n      font-family: SourceHanSansCN, SourceHanSansCN;\n      font-weight: bold;\n      font-size: 32px;\n      color: #2D2D2D;\n      line-height: 46px;\n    }\n    .user-name{\n      font-family: SourceHanSansCN, SourceHanSansCN;\n      font-weight: bold;\n      font-size: 24px;\n      color: #939393;\n      line-height: 35px;\n    }\n  }\n}\n</style>\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./sdk2UserAndGameInfo.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./sdk2UserAndGameInfo.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./sdk2UserAndGameInfo.vue?vue&type=template&id=173e7069&scoped=true\"\nimport script from \"./sdk2UserAndGameInfo.vue?vue&type=script&lang=js\"\nexport * from \"./sdk2UserAndGameInfo.vue?vue&type=script&lang=js\"\nimport style0 from \"./sdk2UserAndGameInfo.vue?vue&type=style&index=0&id=173e7069&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"173e7069\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('common-part',{class:['package-part', _vm.$gameName],attrs:{\"label-font\":_vm.$t('sdk2_product_name'),\"id\":\"package-part\"}},[_c('div',{staticClass:\"package-wrapper\"},[_c('div',{staticClass:\"package-icon\"}),_c('div',{staticClass:\"info-wrapper\"},[_c('div',{staticClass:\"name\"},[_vm._v(_vm._s(_vm.goodsName || '-'))]),_c('div',{staticClass:\"price\"},[_vm._v(_vm._s(_vm.chosenDiamond.no_tax_price)+_vm._s(_vm.currencyUnit))])])]),(_vm.calState.isShow)?_c('div',{staticClass:\"default-coupon\"},[_c('div',{staticClass:\"coupon-icon\"}),_c('div',{staticClass:\"coupon-desc\"},[_c('over-size-scale',[_vm._v(_vm._s(_vm.calState.description))])],1),_c('div',{staticClass:\"tips-btn\",on:{\"click\":function($event){return _vm.$root.$emit('showPop', 'sdk2Tips', { type: 'constructions' })}}}),(_vm.calState.discountPrice)?_c('div',{staticClass:\"discount\"},[_vm._v(_vm._s(_vm.calState.discountPrice))]):_vm._e()]):_vm._e()])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<script>\nimport CommonPart from '@/components/common/CommonPart.vue'\nimport { mapGetters, mapState } from 'vuex'\nimport OverSizeScale from '@/components/OverSizeScale.vue'\n\nexport default {\n  name: 'sdk2PackageInfo',\n  components: { OverSizeScale, CommonPart },\n  data () {\n    return {\n      goodsName: '-'\n    }\n  },\n  computed: {\n    ...mapState(['currencyUnit']),\n    ...mapGetters('formdata', ['FinalPriceState']),\n    ...mapState('formdata', ['chosenCoupon', 'defaultRebateInfo', 'defaultDiscountInfo', 'chosenDiamond']),\n    calState () {\n      const temp = {\n        type: '', // 传给后端用的\n        isShow: false,\n        description: '',\n        discountPrice: '' // 折扣显示降低多少价格\n      }\n\n      const chosenCoupon = this.chosenCoupon\n      const defaultCoupon = this.defaultDiscountInfo\n\n      // 优惠券\n      if (chosenCoupon.type === 'direct_first_pay' || (!chosenCoupon.feType && defaultCoupon.type === 'direct_fixed_discount')) {\n        const tempCoupon = chosenCoupon.type === 'direct_first_pay' ? chosenCoupon : defaultCoupon\n        temp.isShow = true\n\n        temp.discountPrice = `- ${tempCoupon.discount_amount}${this.currencyUnit}`\n        temp.description = this.$t(`sdk2_bonus_${tempCoupon.type}`, { 0: `${tempCoupon.rateWidthOutPercent}% OFF` })\n        temp.type = tempCoupon.type\n      }\n\n      // 返钻券\n      const defaultRebate = this.defaultRebateInfo\n      if (chosenCoupon.type === 'direct_first_pay_rebate' || (!chosenCoupon.feType && defaultRebate.type === 'direct_fixed_rebate')) {\n        const tempCoupon = chosenCoupon.type === 'direct_first_pay_rebate' ? chosenCoupon : defaultRebate\n        temp.isShow = true\n\n        const num = Math.floor(tempCoupon.coin - tempCoupon.level_coin)\n        const txt = `${num} ${this.$vt('tokenName')}`\n        temp.description = this.$t(`sdk2_bonus_${tempCoupon.type}`, { 0: txt })\n        temp.type = tempCoupon.type\n      }\n      return temp\n    }\n  },\n  created () {\n    this.$root.$on('updateSdk2PackageName', (name) => {\n      window.defaultPackageName = this.goodsName = name\n    })\n    window._calState = () => this.calState\n  }\n}\n</script>\n\n<template>\n  <common-part :class=\"['package-part', $gameName]\" :label-font=\"$t('sdk2_product_name')\" id=\"package-part\">\n    <div class=\"package-wrapper\">\n      <div class=\"package-icon\"></div>\n      <div class=\"info-wrapper\">\n        <div class=\"name\">{{ goodsName || '-' }}</div>\n        <div class=\"price\">{{ chosenDiamond.no_tax_price }}{{ currencyUnit }}</div>\n      </div>\n    </div>\n    <div v-if=\"calState.isShow\" class=\"default-coupon\">\n      <div class=\"coupon-icon\"></div>\n      <div class=\"coupon-desc\">\n        <over-size-scale>{{ calState.description }}</over-size-scale>\n      </div>\n      <div class=\"tips-btn\" @click=\"$root.$emit('showPop', 'sdk2Tips', { type: 'constructions' })\"></div>\n      <div class=\"discount\" v-if=\"calState.discountPrice\">{{ calState.discountPrice }}</div>\n    </div>\n  </common-part>\n</template>\n\n<style scoped lang=\"scss\">\n@use \"~@/utils/utils.scss\" as utils;\n  .package-part{\n    margin-top: 0 !important;\n    .package-wrapper {\n      @include utils.flexCenter;\n      justify-content: flex-start;\n      background: #FFFFFF;\n      border-radius: 10px;\n      border: 2px solid #D1D1D1;\n      height: 120px;\n      padding: 20px 24px;\n\n      .package-icon {\n        //width: 80px;\n        //height: 80px;\n        @include utils.bgCenterForSdk2('info/default-package-gift.png', 80px, 80px);\n      }\n\n      .info-wrapper {\n        margin-left: 12px;\n\n        .name {\n          font-family: SourceHanSansCN, SourceHanSansCN;\n          font-weight: bold;\n          font-size: 28px;\n          color: #282828;\n          line-height: 40px;\n        }\n\n        .price {\n          font-family: SourceHanSansCN, SourceHanSansCN;\n          font-weight: bold;\n          font-size: 24px;\n          color: #939393;\n          line-height: 35px;\n        }\n      }\n    }\n\n    .default-coupon{\n      background: #FFF4EA;\n      border-radius: 10px;\n      border: 2px solid #F1DFD2;\n      margin-top: 32px;\n      padding: 17px 14px;\n      @include utils.flexCenter;\n      justify-content: flex-start;\n\n      .coupon-icon{\n        @include utils.bgCenterForSdk2('info/coupon-icon.png', 56px, 56px);\n      }\n\n      .coupon-desc{\n        font-family: SourceHanSansCN, SourceHanSansCN;\n        font-weight: bold;\n        font-size: 24px;\n        color: #414141;\n        //line-height: 28px;\n        margin-left: 12px;\n        max-width: 400px;\n        @include utils.flexCenter;\n      }\n\n      .tips-btn{\n        @include utils.bgCenterForSdk2('info/coupon-tip-icon.png', 24px, 24px);\n        display: inline-block;\n        margin-left: 8px;\n      }\n      .discount{\n        font-family: SourceHanSansCN, SourceHanSansCN;\n        font-weight: bold;\n        font-size: 24px;\n        color: #FF5E0F;\n        line-height: 35px;\n        margin-left: auto;\n      }\n    }\n  }\n</style>\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./sdk2PackageInfo.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./sdk2PackageInfo.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./sdk2PackageInfo.vue?vue&type=template&id=70b41223&scoped=true\"\nimport script from \"./sdk2PackageInfo.vue?vue&type=script&lang=js\"\nexport * from \"./sdk2PackageInfo.vue?vue&type=script&lang=js\"\nimport style0 from \"./sdk2PackageInfo.vue?vue&type=style&index=0&id=70b41223&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"70b41223\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('common-part',{staticClass:\"tips-part\"},[_c('div',{staticClass:\"tips\"},[_vm._v(\" You are purchasing a digital license for this product. For full terms, see \"),_c('span',{on:{\"click\":function($event){return _vm.$root.$emit('showPop', 'sdk2Tips', { type: 'policy' })}}},[_vm._v(\"purchase policy\")]),_vm._v(\". \")])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<script>\nimport CommonPart from '@/components/common/CommonPart.vue'\n\nexport default {\n  name: 'sdk2Tip',\n  components: { CommonPart }\n}\n</script>\n\n<template>\n  <common-part class=\"tips-part\">\n    <div class=\"tips\">\n      You are purchasing a digital license for this product. For full terms, see <span @click=\"$root.$emit('showPop', 'sdk2Tips', { type: 'policy' })\">purchase policy</span>.\n    </div>\n  </common-part>\n</template>\n\n<style scoped lang=\"scss\">\n.tips-part{\n  margin-top: 12px !important;\n\n  .tips{\n    font-family: SourceHanSansCN, SourceHanSansCN;\n    font-weight: 400;\n    font-size: 22px;\n    color: #6A6A6A;\n    line-height: 32px;\n    //margin-top: 7px;\n\n    span{\n      color: #FF5E0F;\n      text-decoration: underline;\n    }\n  }\n\n  ::v-deep{\n    .label{\n      display: none;\n    }\n  }\n}\n</style>\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./sdk2Tip.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./sdk2Tip.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./sdk2Tip.vue?vue&type=template&id=fd054c46&scoped=true\"\nimport script from \"./sdk2Tip.vue?vue&type=script&lang=js\"\nexport * from \"./sdk2Tip.vue?vue&type=script&lang=js\"\nimport style0 from \"./sdk2Tip.vue?vue&type=style&index=0&id=fd054c46&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"fd054c46\",\n  null\n  \n)\n\nexport default component.exports", "<template>\n  <div :class=\"['shopping-wrapper', $gameName]\">\n    <sdk2-header></sdk2-header>\n    <div class=\"body-wrapper\">\n      <sdk2-user-and-game-info></sdk2-user-and-game-info>\n      <div class=\"scroll-wrapper\">\n        <div class=\"scroll-content\">\n          <direct-gift-package style=\"display: none\"></direct-gift-package>\n          <sdk2-package-info></sdk2-package-info>\n          <coupon-choose v-show=\"hasCoupon\"></coupon-choose>\n          <channel-choose></channel-choose>\n          <!-- login 放最下面，等上面组件初始化完成 -->\n          <login-module v-show=\"false\"></login-module>\n          <sdk2-tip></sdk2-tip>\n        </div>\n      </div>\n    </div>\n    <checkout-footer :style=\"{ 'z-index': showCouponPop ? -1 : 1 }\" @purchaseGoods=\"judgeRisk()\" :request-loading=\"requestLoading\"></checkout-footer>\n  </div>\n</template>\n\n<script>\nimport LoginModule from '@/components/LoginModule'\nimport ChannelChoose from '@/components/ChannelChoose'\nimport CouponChoose from '@/components/coupon/CouponChoose'\nimport DirectGiftPackage from '@/components/product/DirectGiftPackage'\nimport CheckoutFooter from '@/components/mobile/CheckoutFooterTax.vue'\nimport PayMixin from './PayMixin'\nimport Sdk2Header from '@/components/sdk/sdk2Header.vue'\nimport sdk2UserAndGameInfo from '@/components/sdk/sdk2UserAndGameInfo.vue'\nimport Sdk2PackageInfo from '@/components/sdk/sdk2PackageInfo.vue'\nimport Sdk2Tip from '@/components/sdk/sdk2Tip.vue'\nimport { mapState } from 'vuex'\nimport { logForSdk2OpenedSuccess } from '@/utils/logHelper'\n\nexport default {\n  name: 'Pay',\n  mixins: [PayMixin],\n  components: {\n    Sdk2Tip,\n    Sdk2PackageInfo,\n    sdk2UserAndGameInfo,\n    Sdk2Header,\n    CheckoutFooter,\n    CouponChoose,\n    ChannelChoose,\n    LoginModule,\n    DirectGiftPackage\n  },\n  computed: {\n    ...mapState('formdata', ['chosenCoupon']),\n    ...mapState(['urlParams'])\n  },\n  data () {\n    return {\n      hasCoupon: false,\n      showCouponPop: false\n    }\n  },\n  created () {\n    this.$root.$on('updateSdk2CouponList', (couponList) => {\n      if (this.chosenCoupon.type && this.chosenCoupon.type.includes('direct_first_pay')) {\n        this.hasCoupon = false\n        return\n      }\n      this.hasCoupon = couponList.length > 0\n    })\n    this.$root.$on('showCouponPop', (value) => {\n      this.showCouponPop = value\n    })\n\n    this.$root.$on('loginEnd', (state) => {\n      const params = {}\n      if (this.urlParams.oid) params.oid = this.urlParams.oid\n      if (state) logForSdk2OpenedSuccess(params)\n    })\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n@use \"~@/utils/utils.scss\" as utils;\n.shopping-wrapper {\n  width: 100%;\n  box-sizing: border-box;\n  margin: 0 auto;\n  text-align: left;\n\n  .body-wrapper{\n    width: 100%;\n    height: 0;\n    flex-grow: 1;\n    background: #FF9744;\n    padding-top: 80px;\n    position: relative;\n\n    .scroll-wrapper{\n      height: 100%;\n      border-top-right-radius: 30px;\n      border-top-left-radius: 30px;\n      padding-top: 74px;\n      overflow: hidden;\n      background: white;\n\n      .scroll-content{\n        height: 100%;\n        overflow-y: scroll;\n      }\n    }\n  }\n  .checkout-footer-wrapper{\n    width: 100%;\n    position: relative;\n  }\n}\n@include utils.setMobileContent{\n  .shopping-wrapper{\n    @include utils.flexCenter;\n    flex-direction: column;\n    height: 100%;\n  }\n}\n</style>\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./PaySdk2.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./PaySdk2.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./PaySdk2.vue?vue&type=template&id=36bbd276&scoped=true\"\nimport script from \"./PaySdk2.vue?vue&type=script&lang=js\"\nexport * from \"./PaySdk2.vue?vue&type=script&lang=js\"\nimport style0 from \"./PaySdk2.vue?vue&type=style&index=0&id=36bbd276&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"36bbd276\",\n  null\n  \n)\n\nexport default component.exports", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./sdk2PackageInfo.vue?vue&type=style&index=0&id=70b41223&prod&scoped=true&lang=scss\"", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./sdk2Tip.vue?vue&type=style&index=0&id=fd054c46&prod&scoped=true&lang=scss\"", "export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./PaySdk2.vue?vue&type=style&index=0&id=36bbd276&prod&scoped=true&lang=scss\"", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./sdk2Header.vue?vue&type=style&index=0&id=70d0324b&prod&scoped=true&lang=scss\"", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./sdk2UserAndGameInfo.vue?vue&type=style&index=0&id=173e7069&prod&scoped=true&lang=scss\""], "sourceRoot": ""}