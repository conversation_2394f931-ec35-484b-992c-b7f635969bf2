(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pageSdkV2"],{"35cc":function(t,s,e){},"4f6d":function(t,s,e){"use strict";e.d(s,"a",(function(){return a}));var o=e("72c2");function a(){const t=document.createElement("a");t.href=o["a"].ios()?window.$gcbk("gameinfo.appGameDeepLinkIos"):window.$gcbk("gameinfo.appGameDeepLinkAndroid"),t.style.display="none",document.body.appendChild(t),t.click(),document.body.removeChild(t)}},"5e29":function(t,s,e){"use strict";e.r(s);var o=function(){var t=this,s=t._self._c;return s("div",{class:["shopping-wrapper",t.$gameName]},[s("sdk2-header"),s("div",{staticClass:"body-wrapper"},[s("sdk2-user-and-game-info"),s("div",{staticClass:"scroll-wrapper"},[s("div",{staticClass:"scroll-content"},[s("direct-gift-package",{staticStyle:{display:"none"}}),s("sdk2-package-info"),s("coupon-choose",{directives:[{name:"show",rawName:"v-show",value:t.hasCoupon,expression:"hasCoupon"}]}),s("channel-choose"),s("login-module",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}]}),s("sdk2-tip")],1)])],1),s("checkout-footer",{style:{"z-index":t.showCouponPop?-1:1},attrs:{"request-loading":t.requestLoading},on:{purchaseGoods:function(s){return t.judgeRisk()}}})],1)},a=[],i=e("72a3"),n=e("1f1f"),c=e("878c"),r=e("8d29"),p=e("e28d"),u=e("0075"),d=function(){var t=this,s=t._self._c;return s("header",[s("div",{staticClass:"btn-back",on:{click:t.backAppGame}},[s("i")]),s("div",{staticClass:"fp-logo"}),s("div",{staticClass:"toggle-btn",on:{click:t.goStore}},[s("i",{staticClass:"diamond-toggle"}),t._v(" Topup "),s("img",{directives:[{name:"lazy",rawName:"v-lazy",value:t.$gcbk("images.iconDiamond"),expression:"$gcbk('images.iconDiamond')"}],staticClass:"diamond-icon"})])])},_=[],m=e("fa7d"),l=e("4f6d"),g={name:"sdk2Header",methods:{goStore(){Object(m["l"])(Object({NODE_ENV:"production",VUE_APP_PROD_ENV:"MASTER",VUE_APP_PREFIX_TOKEN_SSCP:"https://ss-coin-master-api.kingsgroupgames.com/api",VUE_APP_PREFIX_TOKEN_STCP:"https://st-store-master.kingsgroupgames.com/api",VUE_APP_PREFIX_TOKEN_MCCP:"https://mc-store-master.kingsgroupgames.com/api",VUE_APP_PREFIX_TOKEN_GOGCP:"https://gog-store-master.kingsgroupgames.com/api",VUE_APP_PREFIX_TOKEN_ROMCP:"https://koa-store-release.kingsgroup.cn/api",VUE_APP_PREFIX_TOKEN_SSV:"https://ssv-store-master.kingsgroupgames.com/api",VUE_APP_PREFIX_TOKEN_SSV2:"https://ssv2-store-master-api.kingsgroupgames.com/api",VUE_APP_PREFIX_TOKEN_DC:"https://dcdl-store-master-api.kingsgroupgames.com/api",VUE_APP_PREFIX_TOKEN_FOUNDATION:"https://foundation-store-release-api.kingsgroup.cn/api",VUE_APP_PREFIX_ACCOUNT_FOUNDATION:"https://store-account-release.nenglianghe.cn/api/account",VUE_APP_PREFIX_TOKEN_SSRP:"https://ss-coin-master-api.kingsgroupgames.com/api",VUE_APP_PREFIX_STORE_SSRP:"https://ss-store-master.kingsgroupgames.com",VUE_APP_PREFIX_TOKEN_ST:"https://st-store-master.kingsgroupgames.com/api",VUE_APP_PREFIX_STORE_STRP:"https://st-store-master.kingsgroupgames.com",VUE_APP_PREFIX_TOKEN_MO:"https://mo-store-master.kingsgroupgames.com/api",VUE_APP_PREFIX_STORE_MORP:"https://mo-store-master.kingsgroupgames.com",VUE_APP_PREFIX_TOKEN_SSD:"https://ts-store-master-api.kingsgroupgames.com/api",VUE_APP_PREFIX_STORE_SSDRP:"https://ts-store-master.kingsgroupgames.com",VUE_APP_PREFIX_AME:"https://ame-test.funplus.com",VUE_APP_PREFIX_ACCOUNT:"https://store-account-stage.funplus.com/api/account",VUE_APP_LOGIN_PAGE_URL:"https://store-funplusid-test.funplus.com/login",VUE_APP_VipIntroducePageUrl_koa:"http://vip-test.funplus.com.cn/koa",VUE_APP_VipIntroducePageUrl_aof:"http://vip-test.funplus.com.cn/vip",VUE_APP_VipIntroducePageUrl_rom:"http://vip-test.funplus.com.cn/rom",VUE_APP_VipIntroducePageUrl_koaCn:"http://vip-test.funplus.com.cn/koa-cn",VUE_APP_ROM_ADDRESS_KOA:"http://**************:10305",BASE_URL:"/res/"})["VUE_APP_PREFIX_STORE_"+this.$gameName.toUpperCase()])},backAppGame:l["a"]}},P=g,h=(e("ca9b"),e("2877")),f=Object(h["a"])(P,d,_,!1,null,"70d0324b",null),E=f.exports,k=function(){var t=this,s=t._self._c;return s("section",{staticClass:"info-wrapper"},[s("img",{directives:[{name:"lazy",rawName:"v-lazy",value:t.gameLogo,expression:"gameLogo"}],staticClass:"logo"}),s("div",{staticClass:"info"},[s("div",{staticClass:"game-name"},[t._v(t._s(t.gameName))]),s("div",{staticClass:"user-name"},[t._v(t._s(t.$t("my-role"))+": "+t._s(t.$store.state.userinfo.name||"-"))])])])},v=[],C={name:"sdk2UserAndGameInfo",data(){return{gameName:this.$gcbk("gameinfo.gameName"),gameLogo:this.$gcbk("images.logoPath")}}},S=C,O=(e("d460"),Object(h["a"])(S,k,v,!1,null,"173e7069",null)),b=O.exports,U=function(){var t=this,s=t._self._c;return s("common-part",{class:["package-part",t.$gameName],attrs:{"label-font":t.$t("sdk2_product_name"),id:"package-part"}},[s("div",{staticClass:"package-wrapper"},[s("div",{staticClass:"package-icon"}),s("div",{staticClass:"info-wrapper"},[s("div",{staticClass:"name"},[t._v(t._s(t.goodsName||"-"))]),s("div",{staticClass:"price"},[t._v(t._s(t.chosenDiamond.no_tax_price)+t._s(t.currencyUnit))])])]),t.calState.isShow?s("div",{staticClass:"default-coupon"},[s("div",{staticClass:"coupon-icon"}),s("div",{staticClass:"coupon-desc"},[s("over-size-scale",[t._v(t._s(t.calState.description))])],1),s("div",{staticClass:"tips-btn",on:{click:function(s){return t.$root.$emit("showPop","sdk2Tips",{type:"constructions"})}}}),t.calState.discountPrice?s("div",{staticClass:"discount"},[t._v(t._s(t.calState.discountPrice))]):t._e()]):t._e()])},y=[],A=e("3772"),R=e("2f62"),N=e("da93"),w={name:"sdk2PackageInfo",components:{OverSizeScale:N["a"],CommonPart:A["a"]},data(){return{goodsName:"-"}},computed:{...Object(R["c"])(["currencyUnit"]),...Object(R["b"])("formdata",["FinalPriceState"]),...Object(R["c"])("formdata",["chosenCoupon","defaultRebateInfo","defaultDiscountInfo","chosenDiamond"]),calState(){const t={type:"",isShow:!1,description:"",discountPrice:""},s=this.chosenCoupon,e=this.defaultDiscountInfo;if("direct_first_pay"===s.type||!s.feType&&"direct_fixed_discount"===e.type){const o="direct_first_pay"===s.type?s:e;t.isShow=!0,t.discountPrice=`- ${o.discount_amount}${this.currencyUnit}`,t.description=this.$t("sdk2_bonus_"+o.type,{0:o.rateWidthOutPercent+"% OFF"}),t.type=o.type}const o=this.defaultRebateInfo;if("direct_first_pay_rebate"===s.type||!s.feType&&"direct_fixed_rebate"===o.type){const e="direct_first_pay_rebate"===s.type?s:o;t.isShow=!0;const a=Math.floor(e.coin-e.level_coin),i=`${a} ${this.$vt("tokenName")}`;t.description=this.$t("sdk2_bonus_"+e.type,{0:i}),t.type=e.type}return t}},created(){this.$root.$on("updateSdk2PackageName",t=>{window.defaultPackageName=this.goodsName=t}),window._calState=()=>this.calState}},I=w,V=(e("87f8"),Object(h["a"])(I,U,y,!1,null,"70b41223",null)),T=V.exports,$=function(){var t=this,s=t._self._c;return s("common-part",{staticClass:"tips-part"},[s("div",{staticClass:"tips"},[t._v(" You are purchasing a digital license for this product. For full terms, see "),s("span",{on:{click:function(s){return t.$root.$emit("showPop","sdk2Tips",{type:"policy"})}}},[t._v("purchase policy")]),t._v(". ")])])},F=[],X={name:"sdk2Tip",components:{CommonPart:A["a"]}},D=X,j=(e("8db8"),Object(h["a"])(D,$,F,!1,null,"fd054c46",null)),x=j.exports,K=e("5001"),G={name:"Pay",mixins:[u["a"]],components:{Sdk2Tip:x,Sdk2PackageInfo:T,sdk2UserAndGameInfo:b,Sdk2Header:E,CheckoutFooter:p["a"],CouponChoose:c["a"],ChannelChoose:n["a"],LoginModule:i["a"],DirectGiftPackage:r["a"]},computed:{...Object(R["c"])("formdata",["chosenCoupon"]),...Object(R["c"])(["urlParams"])},data(){return{hasCoupon:!1,showCouponPop:!1}},created(){this.$root.$on("updateSdk2CouponList",t=>{this.chosenCoupon.type&&this.chosenCoupon.type.includes("direct_first_pay")?this.hasCoupon=!1:this.hasCoupon=t.length>0}),this.$root.$on("showCouponPop",t=>{this.showCouponPop=t}),this.$root.$on("loginEnd",t=>{const s={};this.urlParams.oid&&(s.oid=this.urlParams.oid),t&&Object(K["logForSdk2OpenedSuccess"])(s)})}},L=G,M=(e("9837"),Object(h["a"])(L,o,a,!1,null,"36bbd276",null));s["default"]=M.exports},7797:function(t,s,e){},"7f35":function(t,s,e){},8706:function(t,s,e){},"87f8":function(t,s,e){"use strict";e("cc11")},"8db8":function(t,s,e){"use strict";e("7797")},9837:function(t,s,e){"use strict";e("7f35")},ca9b:function(t,s,e){"use strict";e("8706")},cc11:function(t,s,e){},d460:function(t,s,e){"use strict";e("35cc")}}]);
//# sourceMappingURL=pageSdkV2.808e7cc5.js.map