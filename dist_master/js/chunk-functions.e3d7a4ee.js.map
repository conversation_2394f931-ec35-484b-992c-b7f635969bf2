{"version": 3, "sources": ["webpack:///./src/components/pop/LoginValidation.vue", "webpack:///src/components/pop/LoginValidation.vue", "webpack:///./src/components/pop/LoginValidation.vue?23ed", "webpack:///./src/components/pop/LoginValidation.vue?7d44", "webpack:///./src/components/common/CommonFooterPuzala.vue", "webpack:///src/components/common/CommonFooterPuzala.vue", "webpack:///./src/components/common/CommonFooterPuzala.vue?930e", "webpack:///./src/components/common/CommonFooterPuzala.vue?2c29", "webpack:///./src/assets/common/icon/fp-logo.png", "webpack:///./src/components/pop/RiskControlPolicy.vue?6b06", "webpack:///./src/assets/koa/boon/login_reward_0.png", "webpack:///./src/assets/ss/ssd/boon-install-award.png", "webpack:///./src/components/game/ssv/BoonPop.vue?20f5", "webpack:///./src/components/pop/LoginValidation.vue?047d", "webpack:///./src/assets/koa/boon sync ^\\.\\/login_reward_.*\\.png$", "webpack:///./src/assets/koa/aof/boon/install_reward_0.png", "webpack:///./src/assets/koa/boon/login_reward_1.png", "webpack:///./src/components/PosterSwiper.vue?5872", "webpack:///./src/components/pop/PrivacyPolicy.vue", "webpack:///src/components/pop/PrivacyPolicy.vue", "webpack:///./src/components/pop/PrivacyPolicy.vue?9757", "webpack:///./src/components/pop/PrivacyPolicy.vue?5635", "webpack:///./src/components/game/koa/CommonFooter.vue?464b", "webpack:///./src/components/privatePermission.vue", "webpack:///src/components/privatePermission.vue", "webpack:///./src/components/privatePermission.vue?b080", "webpack:///./src/components/privatePermission.vue?8c32", "webpack:///./src/components/mobile/RefundPolicy.vue", "webpack:///src/components/mobile/RefundPolicy.vue", "webpack:///./src/components/mobile/RefundPolicy.vue?e92c", "webpack:///./src/components/mobile/RefundPolicy.vue?fd35", "webpack:///./src/assets/koa/boon/login_reward_3.png", "webpack:///./src/assets/koa sync ^\\.\\/.*\\/boon\\/install_reward_.*\\.png$", "webpack:///./src/components/game/ssv/BoonPop.vue", "webpack:///src/components/game/ssv/BoonPop.vue", "webpack:///./src/components/game/ssv/BoonPop.vue?2309", "webpack:///./src/components/game/ssv/BoonPop.vue?a3bb", "webpack:///./src/assets/koa/aof/boon/login_reward_0.png", "webpack:///./src/components/mobile/RefundPolicy.vue?c86e", "webpack:///./src/components/common/CommonFooterPuzala.vue?55d0", "webpack:///./src/assets/koa/boon/install_reward_3.png", "webpack:///./src/components/pop/RiskControlPolicy.vue", "webpack:///src/components/pop/RiskControlPolicy.vue", "webpack:///./src/components/pop/RiskControlPolicy.vue?4682", "webpack:///./src/components/pop/RiskControlPolicy.vue?5e1a", "webpack:///./src/assets/common/icon-police-gongan.png", "webpack:///./src/assets/koa/boon/install_reward_2.png", "webpack:///./src/components/game/koa/BoonPop.vue", "webpack:///src/components/game/koa/BoonPop.vue", "webpack:///./src/components/game/koa/BoonPop.vue?47d6", "webpack:///./src/components/game/koa/BoonPop.vue?a2ce", "webpack:///./src/assets/koa/aof/boon/install_reward_1.png", "webpack:///./src/assets/koa sync ^\\.\\/.*\\/boon\\/login_reward_.*\\.png$", "webpack:///./src/components/game/koa/CommonFooter.vue", "webpack:///src/components/game/koa/CommonFooter.vue", "webpack:///./src/components/game/koa/CommonFooter.vue?0191", "webpack:///./src/components/game/koa/CommonFooter.vue?a325", "webpack:///./src/assets/ss/ssv2/boon-install-award.png", "webpack:///./src/assets/koa/aof/boon/install_reward_3.png", "webpack:///./src/assets/koa/boon/login_reward_2.png", "webpack:///./src/assets/koa/boon/daily-reward-image.png", "webpack:///./src/assets/koa/aof/boon/login_reward_1.png", "webpack:///./src/assets/ss/ssv2/boon-login-award.png", "webpack:///./src/assets/koa/aof/boon/login_reward_3.png", "webpack:///./src/components/pop/WhatIsDiamond.vue?2ae8", "webpack:///./src/assets/ss/boon/boon-login-award.png", "webpack:///./src/components/privatePermission.vue?751f", "webpack:///./src/assets/koa/boon/install_reward_0.png", "webpack:///./src/assets/koa/boon sync ^\\.\\/install_reward_.*\\.png$", "webpack:///./src/assets/ss/boon/boon-install-award.png", "webpack:///./src/components/pop/PrivacyPolicy.vue?036f", "webpack:///./src/assets/ss sync ^\\.\\/.*\\/boon\\-install\\-award\\.png$", "webpack:///./src/assets/koa/boon/install_reward_1.png", "webpack:///./src/components/pop/privateConfirmPop.vue?f6fd", "webpack:///./src/components/pop/privateConfirmPop.vue", "webpack:///src/components/pop/privateConfirmPop.vue", "webpack:///./src/components/pop/privateConfirmPop.vue?4177", "webpack:///./src/components/pop/privateConfirmPop.vue?209d", "webpack:///./node_modules/ssr-window/dist/ssr-window.esm.js", "webpack:///./src/components/game/koa/BoonPop.vue?dca0", "webpack:///./src/assets/ss/ssd/boon-login-award.png", "webpack:///./src/components/pop/WhatIsDiamond.vue", "webpack:///./src/components/PosterSwiper.vue", "webpack:///src/components/PosterSwiper.vue", "webpack:///./src/components/PosterSwiper.vue?63a3", "webpack:///./src/components/PosterSwiper.vue?1700", "webpack:///src/components/pop/WhatIsDiamond.vue", "webpack:///./src/components/pop/WhatIsDiamond.vue?43b4", "webpack:///./src/components/pop/WhatIsDiamond.vue?762d", "webpack:///./src/assets/koa/aof/boon/install_reward_2.png", "webpack:///./node_modules/dom7/dist/dom7.modular.js", "webpack:///./src/assets/ss sync ^\\.\\/.*\\/boon\\-login\\-award\\.png$", "webpack:///./src/assets/koa/aof/boon/login_reward_2.png"], "names": ["render", "_vm", "this", "_c", "_self", "class", "$gameName", "showHowToCatPage", "staticClass", "_v", "_s", "$t", "on", "$event", "attrs", "$imageLoader", "domProps", "replace", "loginValidationExpireCount", "0", "htmlUsername", "canResend", "resend", "_e", "leaveCount", "directives", "name", "rawName", "value", "inputCode", "expression", "$i18n", "locale", "target", "composing", "fixInput", "rawValidate", "checkCode", "close", "staticRenderFns", "deviceId", "dealSmDeviceId", "id", "components", "Container", "props", "computed", "option", "username", "String", "length", "leaveTimes", "data", "remaining_verification_attempts", "countInterval", "$gcbk", "methods", "countTime", "setInterval", "clearInterval", "$loading", "show", "sendCode", "fp_device_id", "openid", "then", "res", "code", "$toast", "err", "finally", "hide", "successCb", "$root", "$emit", "failCb", "e", "re", "test", "slice", "created", "send_code_cd", "<PERSON><PERSON><PERSON><PERSON>", "component", "_m", "staticStyle", "module", "exports", "map", "webpackContext", "req", "webpackContextResolve", "__webpack_require__", "o", "Error", "keys", "Object", "resolve", "scopedSlots", "_u", "key", "fn", "proxy", "go", "seq", "country", "$store", "state", "toLowerCase", "global", "special", "kr", "jp", "finalUrl", "getters", "window", "open", "StorageUtils", "setLocalStorage", "commit", "params", "p0", "p1", "p2", "silence", "getAmeDo", "is<PERSON><PERSON><PERSON>", "Array", "isArray", "_i", "$$a", "$$el", "$$c", "checked", "$$v", "$$i", "concat", "notifyServer", "calcPop", "isPop", "watch", "val", "__needDEPop", "initState", "p3", "game", "gameinfo", "gameProject", "split", "check", "popup", "type", "set_type", "set_status", "Number", "$on", "_l", "tabList", "item", "index", "chosenIndex", "title", "gotInstallReward", "gotLoginReward", "swiperOptions", "require", "is<PERSON>ogin", "hadInstall", "getReward", "getPwaReward", "calcShowInstall", "install", "focusInput", "<PERSON><PERSON><PERSON><PERSON>", "getLoginReward", "projectId", "loginAction", "pwaOpenAction", "ameParams", "displayMode", "isStandalone", "matchMedia", "matches", "document", "referrer", "startsWith", "navigator", "standalone", "Swiper", "SwiperSlide", "instance", "deferred<PERSON>rompt", "__deferredPrompt", "undefined", "showMobileSafariGuide", "progressPercent", "swiperInstance", "autoplay", "slideChangeTransitionStart", "activeIndex", "init", "onClose", "showInstallPart", "addEventListener", "preventDefault", "resetStatus", "ameHoldByGet", "result", "values", "task_id", "taskId", "prompt", "userChoice", "choiceResult", "outcome", "console", "log", "displayModeCheck", "setTimeout", "mapState", "parser", "<PERSON><PERSON><PERSON><PERSON>", "userAgent", "getResult", "browser", "whiteBrowser", "includes", "$watch", "slideTo", "leaveDate", "default", "cb", "interval", "calcLeaveTime", "fixDate", "p", "Math", "floor", "getLeaveDateTxt", "count", "tabItem", "tabIndex", "dataKey", "$data", "formdata", "lang<PERSON><PERSON>", "i", "gotDailyReward", "getDailyReward", "numsMap", "chargeStatus", "recharge", "goTurntable", "missionList", "serverId", "gotToggleCoupon", "getImgUrl", "imageId", "numberFormat", "num", "onRecive", "filter", "list", "findIndex", "status", "$set", "resetTurntable", "ameDoByGet", "ticket", "score", "score_id", "total", "reward", "catch", "goToggleActivityPage", "process", "VUE_APP_URL_KOA_TOGGLE_COUPON", "localStorage", "getItem", "encodeURIComponent", "isMobile", "closePop", "confirm", "ContainerV2", "no", "ok", "isObject", "obj", "constructor", "extend", "src", "for<PERSON>ach", "doc", "ssrDocument", "body", "removeEventListener", "activeElement", "blur", "nodeName", "querySelector", "querySelectorAll", "getElementById", "createEvent", "initEvent", "createElement", "children", "childNodes", "style", "setAttribute", "getElementsByTagName", "createElementNS", "importNode", "location", "hash", "host", "hostname", "href", "origin", "pathname", "protocol", "search", "win", "ssrWindow", "history", "replaceState", "pushState", "back", "CustomEvent", "getComputedStyle", "getPropertyValue", "Image", "Date", "screen", "clearTimeout", "gameCode", "config", "$vt", "webCashierBanner", "banner", "imageUrl", "jumpUrl", "slot", "swiperIndex", "disableOnInteraction", "confirmBtnTxt", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Dom7", "arr", "self", "$", "selector", "context", "els", "tempParent", "html", "trim", "indexOf", "toCreate", "innerHTML", "push", "match", "nodeType", "unique", "uniqueArray", "addClass", "className", "classes", "j", "classList", "add", "removeClass", "remove", "hasClass", "contains", "toggleClass", "toggle", "attr", "arguments", "getAttribute", "attrName", "removeAttr", "removeAttribute", "el", "dom7ElementDataStorage", "transform", "elStyle", "webkitTransform", "transition", "duration", "webkitTransitionDuration", "transitionDuration", "args", "eventType", "targetSelector", "listener", "capture", "handleLiveEvent", "eventData", "dom7EventData", "unshift", "is", "apply", "parents", "k", "handleEvent", "events", "event", "dom7LiveListeners", "proxyListener", "dom7Listeners", "off", "handlers", "handler", "dom7proxy", "splice", "trigger", "evt", "detail", "bubbles", "cancelable", "dataIndex", "dispatchEvent", "transitionEnd", "callback", "dom", "fireCallBack", "call", "outerWidth", "<PERSON><PERSON><PERSON><PERSON>", "styles", "offsetWidth", "parseFloat", "outerHeight", "offsetHeight", "offset", "box", "getBoundingClientRect", "clientTop", "clientLeft", "scrollTop", "scrollY", "scrollLeft", "scrollX", "top", "left", "css", "prop", "each", "matchedItems", "text", "textContent", "compareWith", "webkitMatchesSelector", "msMatchesSelector", "child", "previousSibling", "eq", "returnIndex", "append", "<PERSON><PERSON><PERSON><PERSON>", "tempDiv", "<PERSON><PERSON><PERSON><PERSON>", "append<PERSON><PERSON><PERSON>", "prepend", "insertBefore", "next", "nextElement<PERSON><PERSON>ling", "nextAll", "nextEls", "prev", "previousElementSibling", "prevAll", "prevEls", "parent", "parentNode", "closest", "find", "foundElements", "found", "<PERSON><PERSON><PERSON><PERSON>", "toAdd", "prototype", "Class"], "mappings": "mJAAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,YAAY,CAACE,MAAM,CAAC,yBAA0BJ,EAAIK,YAAY,CAAEL,EAAIM,iBAAkBJ,EAAG,MAAM,CAACK,YAAY,YAAY,CAACL,EAAG,MAAM,CAACK,YAAY,SAAS,CAACP,EAAIQ,GAAGR,EAAIS,GAAGT,EAAIU,GAAG,qCAAqCR,EAAG,MAAM,CAACK,YAAY,WAAWI,GAAG,CAAC,MAAQ,SAASC,GAAQZ,EAAIM,kBAAmB,MAAUJ,EAAG,MAAM,CAACW,MAAM,CAAC,IAAMb,EAAIc,aAAa,2BAA2B,IAAM,MAAMZ,EAAG,MAAM,CAACK,YAAY,YAAYQ,SAAS,CAAC,UAAYf,EAAIS,GAAGT,EAAIU,GAAG,sCAAsCM,QAAQ,KAAM,SAAShB,EAAIiB,2CAA2Cf,EAAG,MAAM,CAACK,YAAY,aAAa,CAACL,EAAG,MAAM,CAACK,YAAY,SAAS,CAACP,EAAIQ,GAAGR,EAAIS,GAAGT,EAAIU,GAAG,mCAAmCR,EAAG,MAAM,CAACK,YAAY,aAAa,CAACL,EAAG,MAAM,CAACK,YAAY,OAAOQ,SAAS,CAAC,UAAYf,EAAIS,GAAGT,EAAIU,GAAG,kCAAmC,CAAEQ,EAAGlB,EAAImB,mBAAqBnB,EAAIoB,UAAWlB,EAAG,MAAM,CAACK,YAAY,WAAWI,GAAG,CAAC,MAAQX,EAAIqB,SAAS,CAACrB,EAAIQ,GAAGR,EAAIS,GAAGT,EAAIU,GAAG,oCAAoCV,EAAIsB,KAAMtB,EAAIuB,WAAa,EAAGrB,EAAG,MAAM,CAACK,YAAY,eAAe,CAACP,EAAIQ,GAAG,IAAIR,EAAIS,GAAGT,EAAIuB,YAAY,QAAQvB,EAAIsB,OAAOpB,EAAG,MAAM,CAACK,YAAY,iBAAiB,CAACL,EAAG,QAAQ,CAACsB,WAAW,CAAC,CAACC,KAAK,QAAQC,QAAQ,UAAUC,MAAO3B,EAAI4B,UAAWC,WAAW,cAAczB,MAAMJ,EAAI8B,MAAMC,OAAOlB,MAAM,CAAC,KAAO,SAAS,YAAcb,EAAIU,GAAG,qCAAqC,UAAY,IAAIK,SAAS,CAAC,MAASf,EAAI4B,WAAYjB,GAAG,CAAC,MAAQ,CAAC,SAASC,GAAWA,EAAOoB,OAAOC,YAAiBjC,EAAI4B,UAAUhB,EAAOoB,OAAOL,QAAO3B,EAAIkC,eAAehC,EAAG,MAAM,CAACK,YAAY,YAAYQ,SAAS,CAAC,UAAYf,EAAIS,GAAGT,EAAIU,GAAG,4CAA4CR,EAAG,MAAM,CAACE,MAAM,CAAC,cAAe,CAAC,wBAAyBJ,EAAImC,cAAcxB,GAAG,CAAC,MAAQX,EAAIoC,YAAY,CAACpC,EAAIQ,GAAGR,EAAIS,GAAGT,EAAIU,GAAG,qCAAqCR,EAAG,MAAM,CAACK,YAAY,gBAAgBI,GAAG,CAAC,MAAQ,SAASC,GAAQZ,EAAIM,kBAAmB,KAAQ,CAACN,EAAIQ,GAAGR,EAAIS,GAAGT,EAAIU,GAAG,qCAAqCR,EAAG,MAAM,CAACK,YAAY,QAAQI,GAAG,CAAC,MAAQX,EAAIqC,cAEnhEC,EAAkB,G,oCC8BtB,IAAAC,EAAA,GACAC,eAAAC,IAAAF,EAAAE,IAEe,OACfhB,KAAA,qBACAiB,WAAA,CAAAC,kBACAC,MAAA,WACAC,SAAA,CACA1B,eACA,oBAAA2B,OAAAC,mBAEAZ,cACA,MAAAP,EAAA,KAAAA,UACA,WAAAoB,OAAApB,GAAAqB,QAEA7B,YACA,gBAAAG,YAAA,KAAA2B,WAAA,IAGAC,OACA,OACAvB,UAAA,GACAtB,kBAAA,EACAiB,WAAA,GACA2B,WAAA,KAAAJ,OAAAM,iCAAA,EACAC,cAAA,GACApC,2BAAA,KAAAqC,MAAA,uCAGAC,QAAA,CACAC,UAAAjC,GACA,KAAAA,cAAA,GACA,KAAA8B,cAAAI,YAAA,KACA,KAAAlC,aAEA,SAAAA,aACAmC,cAAA,KAAAL,eACA,KAAAA,cAAA,OAEA,MAEAhC,SACA,KAAAsC,SAAAC,OACAC,eAAA,CAAAC,aAAAvB,EAAAwB,OAAA,KAAAjB,OAAAiB,SACAC,KAAAC,IACA,WAAAC,GAAAD,EAEA,OAAAC,GACA,OACA,KAAAhB,WAAAe,EAAAd,KAAAC,gCAEA,KAAAI,YACA,MAQA,QACA,KAAAW,OAAAC,IAAA,KAAA1D,GAAA,mCAIA2D,QAAA,SAAAV,SAAAW,SAEA,kBACA,SAAAnC,YAAA,YAEA,KAAAwB,SAAAC,OACAxB,eAAA,CAAA8B,MAAA,KAAAtC,UAAAkC,aAAAvB,EAAAwB,OAAA,KAAAjB,OAAAiB,SACAC,KAAAC,IACA,WAAAC,GAAAD,EAEA,OADA,KAAArC,UAAA,GACAsC,GACA,kBACA,MAAAK,EAAA,KAAAzB,OAAAyB,UACAA,OACA,KAAAC,MAAAC,MAAA,YACA,MAEA,UACA,KAAAN,OAAAC,IAAA,KAAA1D,GAAA,kCACA,MAEA,UACA,KAAAyD,OAAAC,IAAA,KAAA1D,GAAA,gCACA,MAEA,QACA,KAAAyD,OAAAC,IAAA,KAAA1D,GAAA,mCAIA2D,QAAA,SAAAV,SAAAW,SAEAjC,QACA,KAAAmC,MAAAC,MAAA,YACA,MAAAC,EAAA,KAAA5B,OAAA4B,OACAA,QAEAxC,SAAAyC,GACA,WAAAxB,GAAAwB,EACAC,EAAA,OAAAC,KAAA1B,GACA,MAAAA,GAAA,MAAAA,GAAA,MAAAA,GAAAyB,IACAD,EAAA3C,OAAAL,MAAA,IAGA,MAAAsB,EAAA,KAAArB,UAAAqB,OACAA,EAAA,SAAArB,UAAA,KAAAA,UAAAkD,MAAA,QAGAC,UACA,MAAAxD,EAAA,KAAAuB,QAAA,KAAAA,OAAAkC,cAAA,GACA,KAAAxB,UAAAjC,IAEA0D,gBACA,KAAA5B,gBACA,KAAAA,cAAA,KACAK,cAAA,KAAAL,kBCxJuW,I,wBCQnW6B,EAAY,eACd,EACAnF,EACAuC,GACA,EACA,KACA,WACA,MAIa,aAAA4C,E,oDCnBf,IAAInF,EAAS,WAAkB,IAAIC,EAAIC,KAAQD,EAAIG,MAAMD,GAAG,OAAOF,EAAImF,GAAG,IAEtE7C,EAAkB,CAAC,WAAY,IAAItC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACK,YAAY,aAAa,CAACL,EAAG,IAAI,CAACF,EAAIQ,GAAG,gDAAgDN,EAAG,IAAI,CAACkF,YAAY,CAAC,MAAQ,WAAWvE,MAAM,CAAC,KAAO,yCAAyC,OAAS,WAAW,CAACb,EAAIQ,GAAG,oBAAoBR,EAAIQ,GAAG,OAAON,EAAG,IAAI,CAACkF,YAAY,CAAC,MAAQ,WAAWvE,MAAM,CAAC,KAAO,2CAA2C,OAAS,WAAW,CAACb,EAAIQ,GAAG,0BAA0BR,EAAIQ,GAAG,YCUtd,GACfiB,KAAA,sBCb0W,I,wBCQtWyD,EAAY,eACd,EACAnF,EACAuC,GACA,EACA,KACA,WACA,MAIa,aAAA4C,E,8BCnBfG,EAAOC,QAAU,0lF,2DCAjB,W,qBCAAD,EAAOC,QAAU,IAA0B,qD,uBCA3CD,EAAOC,QAAU,IAA0B,yD,oCCA3C,W,oCCAA,W,uBCAA,IAAIC,EAAM,CACT,uBAAwB,OACxB,uBAAwB,OACxB,uBAAwB,OACxB,uBAAwB,QAIzB,SAASC,EAAeC,GACvB,IAAIhD,EAAKiD,EAAsBD,GAC/B,OAAOE,EAAoBlD,GAE5B,SAASiD,EAAsBD,GAC9B,IAAIE,EAAoBC,EAAEL,EAAKE,GAAM,CACpC,IAAId,EAAI,IAAIkB,MAAM,uBAAyBJ,EAAM,KAEjD,MADAd,EAAET,KAAO,mBACHS,EAEP,OAAOY,EAAIE,GAEZD,EAAeM,KAAO,WACrB,OAAOC,OAAOD,KAAKP,IAEpBC,EAAeQ,QAAUN,EACzBL,EAAOC,QAAUE,EACjBA,EAAe/C,GAAK,Q,gDCzBpB4C,EAAOC,QAAU,IAA0B,uD,uBCA3CD,EAAOC,QAAU,IAA0B,qD,oCCA3C,W,oECAA,IAAIvF,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,YAAY,CAACK,YAAY,2BAA2BH,MAAM,CAACJ,EAAI8B,MAAMC,OAAQ/B,EAAIK,WAAWQ,MAAM,CAAC,MAAQb,EAAIU,GAAG,aAAa,cAAa,GAAMuF,YAAYjG,EAAIkG,GAAG,CAAC,CAACC,IAAI,YAAYC,GAAG,WAAW,MAAO,CAAClG,EAAG,MAAM,CAACK,YAAY,oBAAoBI,GAAG,CAAC,MAAQX,EAAIqC,QAAQ,CAACrC,EAAIQ,GAAGR,EAAIS,GAAGT,EAAIU,GAAG,qBAAqB2F,OAAM,MAAS,CAACnG,EAAG,MAAM,CAACK,YAAY,QAAQ,CAACP,EAAIQ,GAAGR,EAAIS,GAAGT,EAAIU,GAAG,uDAAuDR,EAAG,MAAM,CAACK,YAAY,WAAW,CAACL,EAAG,OAAO,CAACS,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOZ,EAAIsG,GAAG,MAAM,CAACtG,EAAIQ,GAAG,IAAIR,EAAIS,GAAGT,EAAIU,GAAG,cAAc,OAAOR,EAAG,OAAO,CAACS,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOZ,EAAIsG,GAAG,MAAM,CAACtG,EAAIQ,GAAG,IAAIR,EAAIS,GAAGT,EAAIU,GAAG,YAAY,YAEtuB4B,EAAkB,G,oCCgBP,GACfb,KAAA,gBACAmB,MAAA,CACAE,OAAAiD,QAEArD,WAAA,CAAAC,kBACAY,QAAA,CACA+C,GAAAC,GACA,MAAAC,GAAA,KAAAC,OAAAC,MAAAF,SAAA,IAAAG,cACAC,EAAA,+EACAC,EAAA,CAEAC,GAAA,kGACAC,GAAA,oGAGA,IAAAC,EAAAJ,EAAAL,GACAM,EAAAL,KAAAQ,EAAAH,EAAAL,GAAAD,IAEA,KAAAE,OAAAQ,QAAA,2BACAD,EAAA,oFAAAT,IAGAW,OAAAC,KAAAH,EAAA,WAEA3E,QACA+E,OAAAC,gBAAA,0BACA,KAAAZ,OAAAa,OAAA,6BACA,MAAAC,EAAA,CACAC,GAAA,MACAC,GAAA,EACAC,GAAA,OACAC,SAAA,GAEAC,eAAAL,GAEA,KAAA/C,MAAAC,MAAA,eCtDqW,I,wBCQjWS,EAAY,eACd,EACAnF,EACAuC,GACA,EACA,KACA,WACA,MAIa,aAAA4C,E,2CCnBf,W,2CCAA,IAAInF,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACK,YAAY,YAAY,CAACL,EAAG,QAAQ,CAACA,EAAG,QAAQ,CAACsB,WAAW,CAAC,CAACC,KAAK,QAAQC,QAAQ,UAAUC,MAAO3B,EAAI6H,QAAShG,WAAW,YAAYhB,MAAM,CAAC,KAAO,YAAYE,SAAS,CAAC,QAAU+G,MAAMC,QAAQ/H,EAAI6H,SAAS7H,EAAIgI,GAAGhI,EAAI6H,QAAQ,OAAO,EAAG7H,EAAI6H,SAAUlH,GAAG,CAAC,OAAS,CAAC,SAASC,GAAQ,IAAIqH,EAAIjI,EAAI6H,QAAQK,EAAKtH,EAAOoB,OAAOmG,IAAID,EAAKE,QAAuB,GAAGN,MAAMC,QAAQE,GAAK,CAAC,IAAII,EAAI,KAAKC,EAAItI,EAAIgI,GAAGC,EAAII,GAAQH,EAAKE,QAASE,EAAI,IAAItI,EAAI6H,QAAQI,EAAIM,OAAO,CAACF,KAAYC,GAAK,IAAItI,EAAI6H,QAAQI,EAAInD,MAAM,EAAEwD,GAAKC,OAAON,EAAInD,MAAMwD,EAAI,UAAWtI,EAAI6H,QAAQM,GAAM,SAASvH,GAAQ,OAAOZ,EAAIwI,aAAa,cAAcxI,EAAIQ,GAAG,cAAcN,EAAG,IAAI,CAACW,MAAM,CAAC,KAAO,mDAAmD,OAAS,WAAW,CAACb,EAAIQ,GAAG,+BAA+BR,EAAIQ,GAAG,iCAE/0B8B,EAAkB,G,YCCP,GACfb,KAAA,oBACAoB,SAAA,CACA4F,UACA,YAAAZ,UAAA,KAAAa,QAGAC,MAAA,CACAF,QAAAG,GACA1B,OAAA2B,YAAAD,IAGAzF,OACA,OACA0E,SAAA,EACAa,OAAA,IAGAnF,QAAA,CACAuF,YACA,MAAAvB,EAAA,CACAC,GAAA,MACAC,GAAA,EACAC,GAAA,KACAqB,GAAA,MACAC,KAAA,KAAAvC,OAAAC,MAAAuC,SAAAC,YAAAC,MAAA,SAEAvB,eAAAL,GACAvD,KAAAC,IACA,WAAAd,EAAA,KAAAe,GAAAD,EACA,IAAAC,IACAgD,OAAA2B,aAAA,EAEA,KAAAhB,QAAA1E,EAAAiG,MACA,KAAAV,MAAAvF,EAAAkG,UAIAb,aAAAc,GACA,MAAA/B,EAAA,CACAC,GAAA,MACAC,GAAA,EACAC,GAAA,KACAqB,GAAA,MACAC,KAAA,KAAAvC,OAAAC,MAAAuC,SAAAC,YAAAC,MAAA,SAEA,UAAAG,IACA/B,EAAAgC,SAAA,EACAhC,EAAAiC,WAAAC,OAAA,KAAA5B,UAEA,QAAAyB,IACA/B,EAAAgC,SAAA,EACAhC,EAAAiC,WAAA,EAGA,KAAA3B,SAAA,GAGAD,eAAAL,GACAvD,KAAAC,IACA,WAAAC,GAAAD,EACA,IAAAC,IACA,QAAAoF,IAAA,KAAAZ,OAAA,GACA,UAAAY,IAAA,KAAAzB,SAAA,KAAAA,cAKA9C,UACA,KAAA+D,YACA,KAAAtE,MAAAkF,IAAA,8BAAAlB,aAAA,UCzE0V,I,wBCQtVtD,EAAY,eACd,EACAnF,EACAuC,GACA,EACA,KACA,WACA,MAIa,aAAA4C,E,6ECnBf,IAAInF,EAAS,WAAkB,IAAIC,EAAIC,KAAQD,EAAIG,MAAMD,GAAG,OAAOF,EAAImF,GAAG,IAEtE7C,EAAkB,CAAC,WAAY,IAAItC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACA,EAAG,IAAI,CAACW,MAAM,CAAC,KAAO,mDAAmD,OAAS,WAAW,CAACb,EAAIQ,GAAG,mBAAmBR,EAAIQ,GAAG,UCKjM,GACfiB,KAAA,gBCRoW,I,wBCQhWyD,EAAY,eACd,EACAnF,EACAuC,GACA,EACA,KACA,WACA,MAIa,aAAA4C,E,8BCnBfG,EAAOC,QAAU,IAA0B,qD,qBCA3C,IAAIC,EAAM,CACT,kCAAmC,OACnC,kCAAmC,OACnC,kCAAmC,OACnC,kCAAmC,QAIpC,SAASC,EAAeC,GACvB,IAAIhD,EAAKiD,EAAsBD,GAC/B,OAAOE,EAAoBlD,GAE5B,SAASiD,EAAsBD,GAC9B,IAAIE,EAAoBC,EAAEL,EAAKE,GAAM,CACpC,IAAId,EAAI,IAAIkB,MAAM,uBAAyBJ,EAAM,KAEjD,MADAd,EAAET,KAAO,mBACHS,EAEP,OAAOY,EAAIE,GAEZD,EAAeM,KAAO,WACrB,OAAOC,OAAOD,KAAKP,IAEpBC,EAAeQ,QAAUN,EACzBL,EAAOC,QAAUE,EACjBA,EAAe/C,GAAK,Q,2CCzBpB,IAAI1C,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,YAAY,CAACK,YAAY,2BAA2BH,MAAM,CAACJ,EAAI8B,MAAMC,QAAQlB,MAAM,CAAC,MAAQb,EAAIU,GAAG,qBAAqB,CAACR,EAAG,MAAM,CAACK,YAAY,YAAYL,EAAG,MAAM,CAACK,YAAY,eAAeP,EAAI2J,GAAI3J,EAAI4J,SAAS,SAASC,EAAKC,GAAO,OAAO5J,EAAG,MAAM,CAACiG,IAAI2D,EAAMvJ,YAAY,MAAMH,MAAM,CAC/V,OAAO0J,EACP9J,EAAI+J,cAAgBD,EAAQ,gBAAkB,GAC/B,0BAAfD,EAAKG,OAAsChK,EAAIiK,iBAAkC,GAAf,aACnD,wBAAfJ,EAAKG,OAAoChK,EAAIkK,eAAgC,GAAf,cAC5DvJ,GAAG,CAAC,MAAQ,SAASC,GAAQZ,EAAI+J,YAAcD,KAAS,CAAC5J,EAAG,OAAO,CAACF,EAAIQ,GAAGR,EAAIS,GAAGT,EAAIU,GAAGmJ,EAAKG,gBAAe,GAAG9J,EAAG,SAAS,CAACK,YAAY,oBAAoBM,MAAM,CAAC,QAAUb,EAAImK,gBAAgB,CAACjK,EAAG,cAAc,CAACiG,IAAI,WAAW,CAACjG,EAAG,MAAM,CAACK,YAAY,eAAe,CAACL,EAAG,MAAM,CAACK,YAAY,SAAS,CAACP,EAAIQ,GAAGR,EAAIS,GAAGT,EAAIU,GAAG,yBAAyBR,EAAG,MAAM,CAACK,YAAY,SAAS,CAACP,EAAIQ,GAAGR,EAAIS,GAAGT,EAAIU,GAAG,uBAAuBR,EAAG,MAAM,CAACK,YAAY,cAAc,CAACL,EAAG,MAAM,CAACW,MAAM,CAAC,IAAMuJ,UAAQ,KAAiC,OAAlBpK,EAAIK,UAAqB,OAASL,EAAIK,oCAAoC,IAAM,QAAQH,EAAG,MAAM,CAACK,YAAY,+BAA+B,CAAGP,EAAIqK,QAAqHrK,EAAIsK,WAA4O,CAAEtK,EAAIiK,iBAAkB/J,EAAG,OAAO,CAACK,YAAY,cAAcL,EAAG,OAAO,CAACK,YAAY,iBAAiBI,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOZ,EAAIuK,UAAUvK,EAAIwK,iBAAiB,CAACxK,EAAIQ,GAAGR,EAAIS,GAAGT,EAAIU,GAAG,kBAAla,CAAGV,EAAIyK,gBAA8GvK,EAAG,OAAO,CAACK,YAAY,YAAYI,GAAG,CAAC,MAAQX,EAAI0K,UAAU,CAAC1K,EAAIQ,GAAGR,EAAIS,GAAGT,EAAIU,GAAG,uBAAhLR,EAAG,IAAI,CAACK,YAAY,qBAAqB,CAACP,EAAIQ,GAAGR,EAAIS,GAAGT,EAAIU,GAAG,+BAAnN,CAACR,EAAG,OAAO,CAACK,YAAY,YAAYI,GAAG,CAAC,MAAQX,EAAI2K,aAAa,CAAC3K,EAAIQ,GAAGR,EAAIS,GAAGT,EAAIU,GAAG,oBAA0d,KAAKR,EAAG,cAAc,CAACiG,IAAI,SAAS,CAACjG,EAAG,MAAM,CAACK,YAAY,eAAe,CAACL,EAAG,MAAM,CAACK,YAAY,SAAS,CAACP,EAAIQ,GAAGR,EAAIS,GAAGT,EAAIU,GAAG,yBAAyBR,EAAG,MAAM,CAACK,YAAY,SAAS,CAACP,EAAIQ,GAAGR,EAAIS,GAAGT,EAAIU,GAAG,uBAAuBR,EAAG,MAAM,CAACK,YAAY,cAAc,CAACL,EAAG,MAAM,CAACW,MAAM,CAAC,IAAMuJ,UAAQ,KAAiC,OAAlBpK,EAAIK,UAAqB,OAASL,EAAIK,kCAAkC,IAAM,QAAQH,EAAG,MAAM,CAACK,YAAY,+BAA+B,CAAGP,EAAIqK,QAAmHrK,EAAI4K,SAA4H,CAAE5K,EAAIkK,eAAgBhK,EAAG,OAAO,CAACK,YAAY,cAAcL,EAAG,OAAO,CAACK,YAAY,iBAAiBI,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOZ,EAAIuK,UAAUvK,EAAI6K,mBAAmB,CAAC7K,EAAIQ,GAAGR,EAAIS,GAAGT,EAAIU,GAAG,kBAApTR,EAAG,OAAO,CAACK,YAAY,YAAYI,GAAG,CAAC,MAAQX,EAAI2K,aAAa,CAAC3K,EAAIQ,GAAGR,EAAIS,GAAGT,EAAIU,GAAG,4BAA9MR,EAAG,OAAO,CAACK,YAAY,YAAYI,GAAG,CAAC,MAAQX,EAAI2K,aAAa,CAAC3K,EAAIQ,GAAGR,EAAIS,GAAGT,EAAIU,GAAG,mBAAyW,MAAM,IAAI,IAEpmE4B,EAAkB,G,iFC+DtB,MAAM,UAANwI,EAAA,YAAAC,EAAA,eAAAF,EAAA,cAAAG,EAAA,aAAAR,GAAAtD,OAAA5D,MAAA,qBAEA2H,EAAA,CAAAzD,GAAA,MAAAC,GAAAqD,GACA,SAAAI,IACA,MAAAC,EAAAjE,OAAAkE,WAAA,8BAAAC,QACA,OAAAC,SAAAC,SAAAC,WAAA,kBACA,MACAC,UAAAC,YAAAP,EACA,aAEA,UAGe,OACf1J,KAAA,UACAiB,WAAA,CAAAC,iBAAAgJ,mBAAAC,8BACAzI,OACA,MAAA0I,EAAA,KACA,OACAjB,UAAA,EACAV,gBAAA,EACAI,YAAA,EACAL,kBAAA,EACA6B,eAAA5E,OAAA6E,uBAAAC,EACAC,uBAAA,EAEAC,gBAAA,EAEAnC,YAAA,EACAoC,oBAAAH,EACA7B,cAAA,CACAiC,UAAA,EACAzL,GAAA,CACA0L,2BAAA,WACAR,EAAA9B,YAAA,KAAAuC,aAEAC,KAAA,WAEAV,EAAAM,eAAA,QAIAtB,iBACAL,eACAZ,QAAA,CACA,CAAAI,MAAA,yBACA,CAAAA,MAAA,0BAIAzG,QAAA,CACAiJ,UACA,KAAAhI,MAAAC,MAAA,aAEAgI,kBACAvF,OAAAwF,iBAAA,sBAAA/H,IAEAA,EAAAgI,iBAEA,KAAAb,eAAAnH,KAGAiI,cACA,MAAArF,EAAA,CACAC,GAAA,MACAC,GAAAqD,EACApD,GAAA,GAAAqD,KAAAF,KAAAG,KAAAR,KAEA,KAAA7G,SAAAC,OACAiJ,eAAAtF,GACAvD,KAAAC,IACA,WAAAd,EAAA,KAAAe,GAAAD,EACA,OAAAC,EAAA,CACA,MAAA4I,EAAA,GACA,UAAAnL,KAAAoE,OAAAgH,OAAA5J,GAAA2J,EAAAnL,EAAAqL,SAAArL,EAEA,KAAAiJ,SAAAG,KAAA+B,EACA,KAAA5C,eAAAW,KAAAiC,EACA,KAAAxC,WAAAU,KAAA8B,EACA,KAAA7C,iBAAAO,KAAAsC,KAGAzI,QAAA,SAAAV,SAAAW,SAGAiG,UAAA0C,GACA,MAAA1F,EAAA,CAAAG,GAAAuF,GACA,KAAAtJ,SAAAC,OACAgE,eAAA,IAAAL,KAAA0D,IACAjH,KAAAC,IACA,WAAAC,EAAA,KAAAf,EAAA,IAAAc,EACA,IAAAC,GAAAf,EAAAF,SACAgK,IAAAzC,IAAA,KAAAP,kBAAA,GACAgD,IAAApC,IAAA,KAAAX,gBAAA,MAGA7F,QAAA,SAAAV,SAAAW,SAEAqG,aACA,KAAAnG,MAAAC,MAAA,YACA,KAAAD,MAAAC,MAAA,wBAMAiG,UACA,KAAAlG,MAAAC,MAAA,YAEA,KAAAqH,gBACA,KAAAA,eAAAoB,SAEA,KAAApB,eAAAqB,WACAnJ,KAAAoJ,IACA,gBAAAA,EAAAC,QAAA,CACAC,QAAAC,IAAA,iCAEA,MAAAC,EAAA/J,YAAA,KACA,eAAAyH,MACAxH,cAAA8J,GACA,KAAAhJ,MAAAC,MAAA,uBAEA,UAEA6I,QAAAC,IAAA,kCAEA,KAAAzB,oBAAAE,KAGAyB,WAAA,KACA,KAAAjJ,MAAAC,MAAA,sBACA,OAIA5B,SAAA,IACA6K,eAAA,2BACAA,eAAA,cACAjD,kBACA,MAAAkD,EAAA,IAAAC,IAAAnC,UAAAoC,WACAf,EAAAa,EAAAG,aACA,QAAAC,GAAAjB,EAEAkB,EAAA,kBAAAD,EAAAtM,OAAAsM,EAAAtM,MAAA,IAAAwM,SAAA,gBAAAnC,eACA,YAAAzB,YACA,KAAAC,YAAA0D,KAIAjJ,UACA,KAAAP,MAAAkF,IAAA,oBACA+D,WAAA,SAAAb,cAAA,OAEA,KAAAvC,SACA,KAAAuC,cAIA,KAAAsB,OAAA,cAAApE,IACA,KAAAqC,eAAAgC,QAAArE,EAAA,UAGA,KAAA2C,oBCxO8W,I,wBCQ1WvH,EAAY,eACd,EACAnF,EACAuC,GACA,EACA,KACA,WACA,MAIa,aAAA4C,E,yDCnBfG,EAAOC,QAAU,IAA0B,qD,kCCA3C,W,oCCAA,W,uBCAAD,EAAOC,QAAU,IAA0B,uD,yCCA3C,IAAIvF,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,YAAY,CAACK,YAAY,2BAA2BH,MAAM,CAACJ,EAAI8B,MAAMC,OAAQ/B,EAAIK,WAAWQ,MAAM,CAAC,MAAQb,EAAIU,GAAG,aAAa,cAAa,EAAK,GAAK,uBAAuBuF,YAAYjG,EAAIkG,GAAG,CAAC,CAACC,IAAI,YAAYC,GAAG,WAAW,MAAO,CAAClG,EAAG,MAAM,CAACK,YAAY,oBAAoBI,GAAG,CAAC,MAAQX,EAAIqC,QAAQ,CAACrC,EAAIQ,GAAGR,EAAIS,GAAGT,EAAIU,GAAG,qBAAqB2F,OAAM,MAAS,CAACnG,EAAG,MAAM,CAACK,YAAY,QAAQ,CAAc,kBAAZP,EAAImG,IAAyBjG,EAAG,MAAM,CAACa,SAAS,CAAC,UAAYf,EAAIS,GAAGT,EAAIU,GAAG,qCAAqCV,EAAIsB,KAAM,CAAC,eAAgB,kBAAmB,cAAc2M,SAASjO,EAAImG,KAAMjG,EAAG,MAAM,CAACa,SAAS,CAAC,UAAYf,EAAIS,GAAGT,EAAIU,GAAG,6BAA8B,CAAEQ,EAAG,SAASlB,EAAIoO,yBAAyBpO,EAAIsB,KAAM,CAAC,cAAe,YAAa,eAAgB,YAAa,aAAc,aAAc,0BAA0B2M,SAASjO,EAAImG,KAAMjG,EAAG,MAAM,CAACa,SAAS,CAAC,UAAYf,EAAIS,GAAGT,EAAIU,GAAG,2BAA2BV,EAAIsB,UAEv9BgB,EAAkB,G,YCcP,GACfb,KAAA,oBACAmB,MAAA,CACAE,OAAA,CACAwG,KAAAvD,OACAsI,aAAA,MAGAlL,OACA,UAAAgD,EAAA,MAAAxE,EAAA,GAAA2M,GAAA,KAAAxL,OACA,OACAqD,MACAxE,QACA2M,KAEAC,SAAA,GACAH,UAAA,KAGA1L,WAAA,CAAAC,kBACAY,QAAA,CACAlB,QACA,KAAAmC,MAAAC,MAAA,YACA,KAAA8J,UAAA7K,cAAA,KAAA6K,UACA,KAAAD,IAAA,KAAAA,MAEAE,gBACA,MAAAC,EAAAC,KAAA,OAAAC,KAAAC,MAAAF,GAAAC,KAAAC,MAAAF,GACAG,EAAAC,GAAA,GAAAH,KAAAC,MAAAE,EAAA,aAAAL,EAAAK,EAAA,cAAAL,EAAAK,EAAA,YAAAL,EAAAK,EAAA,MAEA,KAAAV,UAAAS,EAAA,KAAAlN,OACA8B,YAAA,KACA,YAAA9B,MAGA,OAFA,KAAAU,aACAqB,cAAA,KAAA6K,UAGA,KAAAH,UAAAS,IAAA,KAAAlN,QACA,OAGAoD,UACA,gDAAAkJ,SAAA,KAAA9H,MACA,KAAAqI,iBAGAvJ,gBACA,KAAAsJ,UAAA7K,cAAA,KAAA6K,YC/DyW,I,wBCQrWrJ,EAAY,eACd,EACAnF,EACAuC,GACA,EACA,KACA,WACA,MAIa,aAAA4C,E,8BCnBfG,EAAOC,QAAU,IAA0B,yD,uBCA3CD,EAAOC,QAAU,IAA0B,uD,2CCA3C,IAAIvF,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,YAAY,CAACK,YAAY,2BAA2BH,MAAM,CAACJ,EAAI8B,MAAMC,OAAQ/B,EAAIK,WAAWQ,MAAM,CAAC,MAAQb,EAAIU,GAAG,qBAAqB,CAACR,EAAG,MAAM,CAACK,YAAY,YAAYL,EAAG,MAAM,CAACK,YAAY,cAAcH,MAAM,CAAE,YAAa,CAAC,KAAM,MAAM6N,SAASjO,EAAI8B,MAAMC,UAAW/B,EAAI2J,GAAI3J,EAAI4J,SAAS,SAASmF,EAAQC,GAAU,OAAO9O,EAAG,MAAM,CAACiG,IAAI4I,EAAQE,QAAQ7O,MAAM,CAAC,MAAO,CAAC,eAAgBJ,EAAIkP,MAAMH,EAAQE,UAAYjP,EAAIyG,OAAOC,MAAMyI,SAASJ,EAAQE,UAAW,gBAAiBjP,EAAI+J,cAAgBiF,IAAWrO,GAAG,CAAC,MAAQ,SAASC,GAAQZ,EAAI+J,YAAciF,KAAY,CAAC9O,EAAG,OAAO,CAACF,EAAIQ,GAAGR,EAAIS,GAAGT,EAAIU,GAAGqO,EAAQK,kBAAiB,GAAGlP,EAAG,SAAS,CAACK,YAAY,oBAAoBM,MAAM,CAAC,QAAUb,EAAImK,gBAAgB,CAACjK,EAAG,cAAc,CAACiG,IAAI,WAAW,CAACjG,EAAG,MAAM,CAACK,YAAY,eAAe,CAACL,EAAG,MAAM,CAACK,YAAY,SAAS,CAACP,EAAIQ,GAAGR,EAAIS,GAAGT,EAAIU,GAAG,yBAAyBR,EAAG,MAAM,CAACK,YAAY,SAAS,CAACP,EAAIQ,GAAGR,EAAIS,GAAGT,EAAIU,GAAG,uBAAuBR,EAAG,MAAM,CAACK,YAAY,cAAc,CAAoB,QAAlBP,EAAIK,UAAqBL,EAAI2J,GAAI,CAAC,EAAE,EAAE,EAAE,IAAI,SAAS0F,GAAG,OAAOnP,EAAG,MAAM,CAACiG,IAAIkJ,EAAExO,MAAM,CAAC,IAAMuJ,UAAQ,oBAAoCiF,SAAS,IAAM,SAAQrP,EAAI2J,GAAI,CAAC,EAAE,EAAE,EAAE,IAAI,SAAS0F,GAAG,OAAOnP,EAAG,MAAM,CAACiG,IAAIkJ,EAAExO,MAAM,CAAC,IAAMuJ,UAAQ,KAAkC,QAAlBpK,EAAIK,UAAsB,MAAQL,EAAIK,iCAAiCgP,SAAS,IAAM,UAAS,GAAGnP,EAAG,MAAM,CAACK,YAAY,+BAA+B,CAAGP,EAAIqK,QAAqHrK,EAAIsK,WAA4O,CAAEtK,EAAIiK,iBAAkB/J,EAAG,OAAO,CAACK,YAAY,cAAcL,EAAG,OAAO,CAACK,YAAY,iBAAiBI,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOZ,EAAIuK,UAAUvK,EAAIwK,iBAAiB,CAACxK,EAAIQ,GAAGR,EAAIS,GAAGT,EAAIU,GAAG,kBAAla,CAAGV,EAAIyK,gBAA8GvK,EAAG,OAAO,CAACK,YAAY,YAAYI,GAAG,CAAC,MAAQX,EAAI0K,UAAU,CAAC1K,EAAIQ,GAAGR,EAAIS,GAAGT,EAAIU,GAAG,uBAAhLR,EAAG,IAAI,CAACK,YAAY,qBAAqB,CAACP,EAAIQ,GAAGR,EAAIS,GAAGT,EAAIU,GAAG,+BAAnN,CAACR,EAAG,OAAO,CAACK,YAAY,YAAYI,GAAG,CAAC,MAAQX,EAAI2K,aAAa,CAAC3K,EAAIQ,GAAGR,EAAIS,GAAGT,EAAIU,GAAG,oBAA0d,KAAKR,EAAG,cAAc,CAACiG,IAAI,SAAS,CAACjG,EAAG,MAAM,CAACK,YAAY,eAAe,CAACL,EAAG,MAAM,CAACK,YAAY,SAAS,CAACP,EAAIQ,GAAGR,EAAIS,GAAGT,EAAIU,GAAG,yBAAyBR,EAAG,MAAM,CAACK,YAAY,SAAS,CAACP,EAAIQ,GAAGR,EAAIS,GAAGT,EAAIU,GAAG,uBAAuBR,EAAG,MAAM,CAACK,YAAY,cAAc,CAAoB,QAAlBP,EAAIK,UAAqBL,EAAI2J,GAAI,CAAC,EAAE,EAAE,EAAE,IAAI,SAAS0F,GAAG,OAAOnP,EAAG,MAAM,CAACiG,IAAIkJ,EAAExO,MAAM,CAAC,IAAMuJ,UAAQ,kBAAkCiF,SAAS,IAAM,SAAQrP,EAAI2J,GAAI,CAAC,EAAE,EAAE,EAAE,IAAI,SAAS0F,GAAG,OAAOnP,EAAG,MAAM,CAACiG,IAAIkJ,EAAExO,MAAM,CAAC,IAAMuJ,UAAQ,KAAkC,QAAlBpK,EAAIK,UAAsB,MAAQL,EAAIK,+BAA+BgP,SAAS,IAAM,UAAS,GAAGnP,EAAG,MAAM,CAACK,YAAY,+BAA+B,CAAGP,EAAIqK,QAAmHrK,EAAI4K,SAA4H,CAAE5K,EAAIkK,eAAgBhK,EAAG,OAAO,CAACK,YAAY,cAAcL,EAAG,OAAO,CAACK,YAAY,iBAAiBI,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOZ,EAAIuK,UAAUvK,EAAI6K,mBAAmB,CAAC7K,EAAIQ,GAAGR,EAAIS,GAAGT,EAAIU,GAAG,kBAApTR,EAAG,OAAO,CAACK,YAAY,YAAYI,GAAG,CAAC,MAAQX,EAAI2K,aAAa,CAAC3K,EAAIQ,GAAGR,EAAIS,GAAGT,EAAIU,GAAG,4BAA9MR,EAAG,OAAO,CAACK,YAAY,YAAYI,GAAG,CAAC,MAAQX,EAAI2K,aAAa,CAAC3K,EAAIQ,GAAGR,EAAIS,GAAGT,EAAIU,GAAG,mBAAyW,KAAKR,EAAG,cAAc,CAACiG,IAAI,gBAAgB,CAACjG,EAAG,MAAM,CAACK,YAAY,eAAe,CAACL,EAAG,MAAM,CAACK,YAAY,SAAS,CAACP,EAAIQ,GAAGR,EAAIS,GAAGT,EAAIU,GAAG,wBAAwBR,EAAG,MAAM,CAACK,YAAY,SAAS,CAACP,EAAIQ,GAAGR,EAAIS,GAAGT,EAAIU,GAAG,gCAAgCR,EAAG,MAAM,CAACK,YAAY,uCAAuC,CAACL,EAAG,MAAM,CAACW,MAAM,CAAC,IAAMuJ,EAAQ,QAA4C,IAAM,QAAQlK,EAAG,MAAM,CAACK,YAAY,8DAA8D,CAAGP,EAAIqK,QAAiH,CAAErK,EAAIsP,eAAgBpP,EAAG,OAAO,CAACE,MAAM,CAAC,YAAa,6BAA6BF,EAAG,OAAO,CAACK,YAAY,iBAAiBI,GAAG,CAAC,MAAQX,EAAIuP,iBAAiB,CAACvP,EAAIQ,GAAGR,EAAIS,GAAGT,EAAIU,GAAG,qBAAzRR,EAAG,OAAO,CAACK,YAAY,YAAYI,GAAG,CAAC,MAAQX,EAAI2K,aAAa,CAAC3K,EAAIQ,GAAGR,EAAIS,GAAGT,EAAIU,GAAG,mBAAyN,GAAKV,EAAIsP,eAA2GtP,EAAIsB,KAA/FpB,EAAG,MAAM,CAACK,YAAY,QAAQ,CAACP,EAAIQ,GAAG,IAAIR,EAAIS,GAAGT,EAAIU,GAAG,sCAA+C,IAAI,IAE7+H4B,EAAkB,G,mHC6FtB,MAAM,UAANwI,EAAA,YAAAC,EAAA,eAAAF,EAAA,cAAAG,EAAA,aAAAR,GAAAtD,OAAA5D,MAAA,wBAEA2H,EAAA,CAAAzD,GAAA,MAAAC,GAAAqD,GACA0E,EAAA,CACA,OACA,QACA,QACA,QACA,SACA,SAEA,SAAAtE,IACA,MAAAC,EAAAjE,OAAAkE,WAAA,8BAAAC,QACA,OAAAC,SAAAC,SAAAC,WAAA,kBACA,MACAC,UAAAC,YAAAP,EACA,aAEA,UAGA,MAAAvB,EAAA,CAaA,CACAqF,QAAA,mBACAG,QAAA,yBAEA,CACAH,QAAA,iBACAG,QAAA,uBAEA,CACAH,QAAA,iBACAG,QAAA,sBAIe,OACf3N,KAAA,UACAiB,WAAA,CAAAC,iBAAAgJ,mBAAAC,8BACAzI,OACA,MAAA0I,EAAA,KACA,OACA4D,aAAA,GACAC,SAAA,EAEA9E,UAAA,EACAV,gBAAA,EACAI,YAAA,EACAL,kBAAA,EACA0F,aAAA,EAEA7D,eAAA5E,OAAA6E,uBAAAC,EACAC,uBAAA,EAEAC,gBAAA,EACAsD,UAEAzF,YAAA,EACAoC,oBAAAH,EACA7B,cAAA,CACAiC,UAAA,EACAzL,GAAA,CACA0L,2BAAA,WACAR,EAAA9B,YAAA,KAAAuC,aAEAC,KAAA,WAEAV,EAAAM,eAAA,QAIAvC,UACAiB,iBACAL,eACAoF,YAAA,GACAC,SAAA,EAEAC,iBAAA,IAGAvM,QAAA,CACAwM,UAAAC,KAGAC,aAAAC,GACA,OAAAD,eAAAC,IAEAC,SAAA1N,GACAmF,eAAA,CACAJ,GAAA,MACAC,GAAA,GACAC,GAAA,KACAjF,OACAuB,KAAAC,IACA,WAAAC,GAAAD,EACA,OAAAC,EAAA,CACA,MAAA2F,EAAA,KAAA+F,YAAAQ,OAAAC,KAAA5N,QACAqH,EAAA,KAAA8F,YAAAU,UAAAD,KAAA5N,QACAoH,EAAA,GAAA0G,OAAA,EACA,KAAAC,KAAA,KAAAZ,YAAA9F,EAAAD,EAAA,QAIA4C,kBACAvF,OAAAwF,iBAAA,sBAAA/H,IAEAA,EAAAgI,iBAEA,KAAAb,eAAAnH,KAGAiI,cACA,MAAArF,EAAA,CACAC,GAAA,MACAC,GAAAqD,EACApD,GAAA,GAAAqD,KAAAF,KAAAG,KAAAR,KAEA,KAAA7G,SAAAC,OACAiJ,eAAAtF,GACAvD,KAAAC,IACA,WAAAd,EAAA,KAAAe,GAAAD,EACA,OAAAC,EAAA,CACA,MAAA4I,EAAA,GACA,UAAAnL,KAAAoE,OAAAgH,OAAA5J,GAAA2J,EAAAnL,EAAAqL,SAAArL,EAEA,KAAAiJ,SAAAG,KAAA+B,EACA,KAAA5C,eAAAW,KAAAiC,EACA,KAAAxC,WAAAU,KAAA8B,EACA,KAAA7C,iBAAAO,KAAAsC,KAIAzI,QAAA,SAAAV,SAAAW,SAEAmM,iBACA,MAAAlJ,EAAA,CACAC,GAAA,MACAC,GAAA,GACAC,GAAA,MAEA,KAAA/D,SAAAC,OACA8M,eAAAnJ,GACAvD,KAAAC,IACA,WAAAd,EAAA,KAAAe,GAAAD,EACA,OAAAC,EAAA,CACA,MAAAyM,EAAAxN,EAAAyN,MAAAR,OAAAvG,GAAA,SAAAA,EAAAgH,UAAA,GACA,KAAAlB,cAAAgB,SAAA,MAAAA,EAAAG,UAGAzM,QAAA,SAAAV,SAAAW,SAGAiG,UAAA0C,GACA,MAAA1F,EAAA,CAAAG,GAAAuF,GACA,KAAAtJ,SAAAC,OACAgE,eAAA,IAAAL,KAAA0D,IACAjH,KAAAC,IACA,WAAAC,EAAA,KAAAf,EAAA,IAAAc,EACA,IAAAC,GAAAf,EAAAF,SACAgK,IAAAzC,IAAA,KAAAP,kBAAA,GACAgD,IAAApC,IAAA,KAAAX,gBAAA,MAIA7F,QAAA,SAAAV,SAAAW,SAGAiL,iBACA,KAAA5L,SAAAC,OACAgE,eAAA,CAAAJ,GAAA,MAAAC,GAAA,GAAAC,GAAA,OACA1D,KAAAC,IACA,WAAAC,EAAA,KAAAf,EAAA,IAAAc,EACA,IAAAC,GAAAf,EAAAF,QACA,KAAAwD,OAAAa,OAAA,oCACA,KAAA9C,MAAAC,MAAA,YAEAgJ,WAAA,KACA,KAAAjJ,MAAAC,MAAA,yBAAAsM,OAAA5N,EAAA,MACA,IAEA,KAAAgB,OAAAC,IAAA,KAAA1D,GAAA,kBAGAsQ,MAAA,SAAA7M,OAAAC,IAAA,KAAA1D,GAAA,iBACA2D,QAAA,SAAAV,SAAAW,SAEAqG,aACA,KAAAnG,MAAAC,MAAA,YACA,KAAAD,MAAAC,MAAA,wBAMAiG,UACA,KAAAlG,MAAAC,MAAA,YAEA,KAAAqH,gBACA,KAAAA,eAAAoB,SAEA,KAAApB,eAAAqB,WACAnJ,KAAAoJ,IACA,gBAAAA,EAAAC,QAAA,CACAC,QAAAC,IAAA,iCAEA,MAAAC,EAAA/J,YAAA,KACA,eAAAyH,MACAxH,cAAA8J,GACA,KAAAhJ,MAAAC,MAAA,uBAEA,UAEA6I,QAAAC,IAAA,kCAEA,KAAAzB,oBAAAE,KAGAyB,WAAA,KACA,KAAAjJ,MAAAC,MAAA,sBACA,MAUAwM,uBACA,IAAAjK,EAAA,GAAAkK,s9DAAAC,mCAAA,KAAArP,MAAAC,SACAqP,aAAAC,QAAA,YACArK,GAAA,WAAAsK,mBAAAF,aAAAC,QAAA,YAEAnK,OAAAC,KAAAH,EAAA,YAGAnE,SAAA,IACA6K,eAAA,2BACAA,eAAA,iBACAA,eAAA,gDACAjD,kBACA,MAAAkD,EAAA,IAAAC,IAAAnC,UAAAoC,WACAf,EAAAa,EAAAG,aACA,QAAAC,GAAAjB,EAEAkB,EAAA,kBAAAD,EAAAtM,OAAAsM,EAAAtM,MAAA,IAAAwM,SAAA,gBAAAnC,eACA,YAAAzB,YACA,KAAAC,YAAA0D,KAIAjJ,UACA,KAAAP,MAAAkF,IAAA,oBACA+D,WAAA,SAAAb,cAAA,OAEA,KAAAvC,SAAA,KAAAuC,cACA,KAAAsB,OAAA,cAAApE,IACA,KAAAqC,eAAAgC,QAAArE,EAAA,UAGA,KAAA2C,oBChX8W,I,wBCQ1WvH,EAAY,eACd,EACAnF,EACAuC,GACA,EACA,KACA,WACA,MAIa,aAAA4C,E,gCCnBfG,EAAOC,QAAU,IAA0B,uD,qBCA3C,IAAIC,EAAM,CACT,gCAAiC,OACjC,gCAAiC,OACjC,gCAAiC,OACjC,gCAAiC,QAIlC,SAASC,EAAeC,GACvB,IAAIhD,EAAKiD,EAAsBD,GAC/B,OAAOE,EAAoBlD,GAE5B,SAASiD,EAAsBD,GAC9B,IAAIE,EAAoBC,EAAEL,EAAKE,GAAM,CACpC,IAAId,EAAI,IAAIkB,MAAM,uBAAyBJ,EAAM,KAEjD,MADAd,EAAET,KAAO,mBACHS,EAEP,OAAOY,EAAIE,GAEZD,EAAeM,KAAO,WACrB,OAAOC,OAAOD,KAAKP,IAEpBC,EAAeQ,QAAUN,EACzBL,EAAOC,QAAUE,EACjBA,EAAe/C,GAAK,Q,2CCzBpB,IAAI1C,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACK,YAAY,kBAAkB,CAACL,EAAG,MAAM,CAACK,YAAY,aAAa,CAAGP,EAAIuR,SAA8LvR,EAAIsB,KAAxLpB,EAAG,MAAM,CAACK,YAAY,OAAO6E,YAAY,CAAC,iBAAiB,cAAc,gBAAgB,QAAQvE,MAAM,CAAC,IAAMuJ,EAAQ,QAA2C,IAAM,aAAsBpK,EAAImF,GAAG,QAEhW7C,EAAkB,CAAC,WAAY,IAAItC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACA,EAAG,IAAI,CAACK,YAAY,WAAW,CAACL,EAAG,IAAI,CAACK,YAAY,QAAQM,MAAM,CAAC,KAAO,0DAA0D,OAAS,WAAW,CAACb,EAAIQ,GAAG,UAAUN,EAAG,IAAI,CAACK,YAAY,QAAQM,MAAM,CAAC,KAAO,uDAAuD,OAAS,WAAW,CAACb,EAAIQ,GAAG,UAAUN,EAAG,IAAI,CAACK,YAAY,QAAQM,MAAM,CAAC,KAAO,kDAAkD,OAAS,WAAW,CAACb,EAAIQ,GAAG,gBAAgBN,EAAG,IAAI,CAACK,YAAY,QAAQM,MAAM,CAAC,KAAO,kCAAkC,OAAS,WAAW,CAACb,EAAIQ,GAAG,WAAWN,EAAG,IAAI,CAACF,EAAIQ,GAAG,eAAeN,EAAG,IAAI,CAACW,MAAM,CAAC,KAAO,6BAA6B,OAAS,WAAW,CAACb,EAAIQ,GAAG,yBAAyBN,EAAG,IAAI,CAACA,EAAG,MAAM,CAACK,YAAY,SAASM,MAAM,CAAC,IAAMuJ,EAAQ,QAAiD,IAAM,MAAMlK,EAAG,IAAI,CAACW,MAAM,CAAC,KAAO,8EAA8E,OAAS,WAAW,CAACb,EAAIQ,GAAG,iC,YCqB7+B,GACfiB,KAAA,eACAoB,SAAA,IACA6K,eAAA,gBC1BmX,I,wBCQ/WxI,EAAY,eACd,EACAnF,EACAuC,GACA,EACA,KACA,WACA,MAIa,aAAA4C,E,gCCnBfG,EAAOC,QAAU,IAA0B,yD,uBCA3CD,EAAOC,QAAU,IAA0B,uD,uBCA3CD,EAAOC,QAAU,IAA0B,qD,uBCA3CD,EAAOC,QAAU,IAA0B,yD,gDCA3CD,EAAOC,QAAU,IAA0B,qD,uBCA3CD,EAAOC,QAAU,IAA0B,uD,uBCA3CD,EAAOC,QAAU,IAA0B,qD,oCCA3C,W,qBCAAD,EAAOC,QAAU,IAA0B,uD,oCCA3C,W,uBCAAD,EAAOC,QAAU,IAA0B,uD,gDCA3C,IAAIC,EAAM,CACT,yBAA0B,OAC1B,yBAA0B,OAC1B,yBAA0B,OAC1B,yBAA0B,QAI3B,SAASC,EAAeC,GACvB,IAAIhD,EAAKiD,EAAsBD,GAC/B,OAAOE,EAAoBlD,GAE5B,SAASiD,EAAsBD,GAC9B,IAAIE,EAAoBC,EAAEL,EAAKE,GAAM,CACpC,IAAId,EAAI,IAAIkB,MAAM,uBAAyBJ,EAAM,KAEjD,MADAd,EAAET,KAAO,mBACHS,EAEP,OAAOY,EAAIE,GAEZD,EAAeM,KAAO,WACrB,OAAOC,OAAOD,KAAKP,IAEpBC,EAAeQ,QAAUN,EACzBL,EAAOC,QAAUE,EACjBA,EAAe/C,GAAK,Q,8CCzBpB4C,EAAOC,QAAU,IAA0B,yD,kCCA3C,W,qBCAA,IAAIC,EAAM,CACT,gCAAiC,OACjC,+BAAgC,OAChC,gCAAiC,QAIlC,SAASC,EAAeC,GACvB,IAAIhD,EAAKiD,EAAsBD,GAC/B,OAAOE,EAAoBlD,GAE5B,SAASiD,EAAsBD,GAC9B,IAAIE,EAAoBC,EAAEL,EAAKE,GAAM,CACpC,IAAId,EAAI,IAAIkB,MAAM,uBAAyBJ,EAAM,KAEjD,MADAd,EAAET,KAAO,mBACHS,EAEP,OAAOY,EAAIE,GAEZD,EAAeM,KAAO,WACrB,OAAOC,OAAOD,KAAKP,IAEpBC,EAAeQ,QAAUN,EACzBL,EAAOC,QAAUE,EACjBA,EAAe/C,GAAK,Q,qBCxBpB4C,EAAOC,QAAU,IAA0B,uD,gFCA3C,W,yCCAA,IAAIvF,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,eAAe,CAACK,YAAY,cAAc,CAACL,EAAG,MAAM,CAACK,YAAY,UAAU,CAACL,EAAG,OAAO,CAACF,EAAIQ,GAAG,iBAAiBN,EAAG,IAAI,CAACS,GAAG,CAAC,MAAQX,EAAIwR,cAActR,EAAG,MAAM,CAACK,YAAY,WAAW,CAACL,EAAG,MAAM,CAACK,YAAY,UAAU,CAACP,EAAIQ,GAAG,0BAA0BR,EAAIQ,GAAG,qKAAuKN,EAAG,IAAI,CAACW,MAAM,CAAC,KAAO,mDAAmD,OAAS,WAAW,CAACb,EAAIQ,GAAG,gCAAgCR,EAAIQ,GAAG,SAASN,EAAG,MAAM,CAACK,YAAY,UAAU,CAACL,EAAG,MAAM,CAACK,YAAY,iBAAiBI,GAAG,CAAC,MAAQX,EAAIwR,WAAW,CAACxR,EAAIQ,GAAG,eAAeN,EAAG,MAAM,CAACK,YAAY,kBAAkBI,GAAG,CAAC,MAAQX,EAAIyR,UAAU,CAACzR,EAAIQ,GAAG,oCAEl0B8B,EAAkB,G,YCAP,GACfb,KAAA,oBACAiB,WAAA,CAAAgP,oBACA9O,MAAA,CACAE,OAAA,CACAwG,KAAAvD,OACAsI,iBAGA9K,QAAA,CACAiO,WACA,KAAA1O,OAAA6O,IAAA,KAAA7O,OAAA6O,KACA,KAAAnN,MAAAC,MAAA,aAEAgN,UACA,KAAA3O,OAAA8O,IAAA,KAAA9O,OAAA8O,KAEA,KAAApN,MAAAC,MAAA,sBACA,KAAAD,MAAAC,MAAA,eCpByW,I,wBCQrWS,EAAY,eACd,EACAnF,EACAuC,GACA,EACA,KACA,WACA,MAIa,aAAA4C,E,kECPf,SAAS2M,EAASC,GACd,OAAgB,OAARA,GACW,kBAARA,GACP,gBAAiBA,GACjBA,EAAIC,cAAgBhM,OAE5B,SAASiM,EAAOhQ,EAAQiQ,QACL,IAAXjQ,IAAqBA,EAAS,SACtB,IAARiQ,IAAkBA,EAAM,IAC5BlM,OAAOD,KAAKmM,GAAKC,SAAQ,SAAU/L,GACJ,qBAAhBnE,EAAOmE,GACdnE,EAAOmE,GAAO8L,EAAI9L,GACb0L,EAASI,EAAI9L,KAClB0L,EAAS7P,EAAOmE,KAChBJ,OAAOD,KAAKmM,EAAI9L,IAAMlD,OAAS,GAC/B+O,EAAOhQ,EAAOmE,GAAM8L,EAAI9L,OA3BpC,oEAgCA,IAAIgM,EAA0B,qBAAb7G,SAA2BA,SAAW,GACnD8G,EAAc,CACdC,KAAM,GACN3F,iBAAkB,aAClB4F,oBAAqB,aACrBC,cAAe,CACXC,KAAM,aACNC,SAAU,IAEdC,cAAe,WACX,OAAO,MAEXC,iBAAkB,WACd,MAAO,IAEXC,eAAgB,WACZ,OAAO,MAEXC,YAAa,WACT,MAAO,CACHC,UAAW,eAGnBC,cAAe,WACX,MAAO,CACHC,SAAU,GACVC,WAAY,GACZC,MAAO,GACPC,aAAc,aACdC,qBAAsB,WAClB,MAAO,MAInBC,gBAAiB,WACb,MAAO,IAEXC,WAAY,WACR,OAAO,MAEXC,SAAU,CACNC,KAAM,GACNC,KAAM,GACNC,SAAU,GACVC,KAAM,GACNC,OAAQ,GACRC,SAAU,GACVC,SAAU,GACVC,OAAQ,KAGhB/B,EAAOG,EAAKC,GAEZ,IAAI4B,EAAwB,qBAAX9M,OAAyBA,OAAS,GAC/C+M,EAAY,CACZ3I,SAAU8G,EACV3G,UAAW,CACPoC,UAAW,IAEf0F,SAAU,CACNC,KAAM,GACNC,KAAM,GACNC,SAAU,GACVC,KAAM,GACNC,OAAQ,GACRC,SAAU,GACVC,SAAU,GACVC,OAAQ,IAEZG,QAAS,CACLC,aAAc,aACdC,UAAW,aACX9N,GAAI,aACJ+N,KAAM,cAEVC,YAAa,WACT,OAAOrU,MAEXyM,iBAAkB,aAClB4F,oBAAqB,aACrBiC,iBAAkB,WACd,MAAO,CACHC,iBAAkB,WACd,MAAO,MAInBC,MAAO,aACPC,KAAM,aACNC,OAAQ,GACRlH,WAAY,aACZmH,aAAc,aACdxJ,WAAY,WACR,MAAO,KAGf4G,EAAOgC,EAAKC,I,kCChIZ,W,qBCAA5O,EAAOC,QAAU,IAA0B,uD,yCCA3C,IAAIvF,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,YAAY,CAACK,YAAY,2BAA2BH,MAAM,CAACJ,EAAI8B,MAAMC,OAAQ9B,KAAKwG,OAAOC,MAAMuC,SAAS4L,SAAU7U,EAAIK,WAAWQ,MAAM,CAAC,cAAa,EAAK,OAASb,EAAI8U,OAAO,MAAQ9U,EAAI+U,IAAI,mBAAmB,GAAK,4BAA4B,CAAC7U,EAAG,MAAM,CAACK,YAAY,eAAe,CAACP,EAAIQ,GAAGR,EAAIS,GAAGT,EAAI+U,IAAI,uBAAuB,OAAO7U,EAAG,kBAAkB,IAEpaoC,EAAkB,G,YCFlBvC,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,SAAS,CAACK,YAAY,oBAAoBH,MAAM,CAACJ,EAAIK,WAAWQ,MAAM,CAAC,QAAUb,EAAImK,gBAAgB,CAAEnK,EAAIgV,iBAAiB/R,OAAQ,CAACjD,EAAI2J,GAAI3J,EAAIgV,kBAAkB,SAASC,GAAQ,OAAO/U,EAAG,cAAc,CAACiG,IAAI8O,EAAOC,UAAU,CAAChV,EAAG,MAAM,CAACW,MAAM,CAAC,IAAMoU,EAAOC,SAAS,IAAM,MAAOD,EAAOE,QAASjV,EAAG,IAAI,CAACW,MAAM,CAAC,KAAOoU,EAAOE,QAAQ,OAAS,YAAYnV,EAAIsB,UAASpB,EAAG,MAAM,CAACK,YAAY,oBAAoBM,MAAM,CAAC,KAAO,cAAcuU,KAAK,cAAcpV,EAAI2J,GAAI5D,OAAOD,KAAK9F,EAAIgV,mBAAmB,SAASlL,GAAO,OAAO5J,EAAG,MAAM,CAACiG,IAAI2D,EAAM1J,MAAM,CAAC,iBAAkB,CAAC,wBAAyBJ,EAAIqV,eAAiBvL,SAAY,IAAI9J,EAAIsB,MAAM,IAEjsBgB,EAAkB,G,YCgBP,G,UAAA,CACfb,KAAA,eACA0B,OACA,MAAA0I,EAAA,KACA,OACA1B,cAAA,CACAiC,SAAA,CACAkJ,sBAAA,GAEA3U,GAAA,CACA0L,2BAAA,WACAR,EAAAwJ,YAAA,KAAA/I,eAIA+I,YAAA,EACAL,iBAAA,KAAAlU,aAAA,qBAGA4B,WAAA,CACAiJ,mBACAC,gCCvCqV,I,wBCQjV1G,EAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,MAIa,EAAAA,E,QCTA,GACfzD,KAAA,gBACAmB,MAAA,CACAE,OAAAiD,QAEA5C,OACA,OACA2R,OAAA,CACAS,cAAA,KAAA7U,GAAA,aAIAgC,WAAA,CAAA8S,eAAA7S,mBCtBqW,ICQjW,G,UAAY,eACd,EACA5C,EACAuC,GACA,EACA,KACA,WACA,OAIa,e,8BCnBf+C,EAAOC,QAAU,IAA0B,uD,kCCA3C,8zCAeA,MAAMmQ,EACJ,YAAYC,GACV,MAAMC,EAAO1V,KAEb,IAAK,IAAIoP,EAAI,EAAGA,EAAIqG,EAAIzS,OAAQoM,GAAK,EACnCsG,EAAKtG,GAAKqG,EAAIrG,GAIhB,OAFAsG,EAAK1S,OAASyS,EAAIzS,OAEXhD,MAIX,SAAS2V,EAAEC,EAAUC,GACnB,MAAMJ,EAAM,GACZ,IAAIrG,EAAI,EACR,GAAIwG,IAAaC,GACXD,aAAoBJ,EACtB,OAAOI,EAGX,GAAIA,EAEF,GAAwB,kBAAbA,EAAuB,CAChC,IAAIE,EACAC,EACJ,MAAMC,EAAOJ,EAASK,OACtB,GAAID,EAAKE,QAAQ,MAAQ,GAAKF,EAAKE,QAAQ,MAAQ,EAAG,CACpD,IAAIC,EAAW,MAQf,IAP4B,IAAxBH,EAAKE,QAAQ,SAAcC,EAAW,MACd,IAAxBH,EAAKE,QAAQ,SAAcC,EAAW,SACd,IAAxBH,EAAKE,QAAQ,QAAwC,IAAxBF,EAAKE,QAAQ,SAAcC,EAAW,MACxC,IAA3BH,EAAKE,QAAQ,YAAiBC,EAAW,SACb,IAA5BH,EAAKE,QAAQ,aAAkBC,EAAW,UAC9CJ,EAAa,OAASjD,cAAcqD,GACpCJ,EAAWK,UAAYJ,EAClB5G,EAAI,EAAGA,EAAI2G,EAAW/C,WAAWhQ,OAAQoM,GAAK,EACjDqG,EAAIY,KAAKN,EAAW/C,WAAW5D,SAUjC,IAFE0G,EALGD,GAA2B,MAAhBD,EAAS,IAAeA,EAASU,MAAM,aAK9CT,GAAW,QAAUnD,iBAAiBkD,EAASK,QAHhD,CAAC,OAAStD,eAAeiD,EAASK,OAAO/M,MAAM,KAAK,KAKvDkG,EAAI,EAAGA,EAAI0G,EAAI9S,OAAQoM,GAAK,EAC3B0G,EAAI1G,IAAIqG,EAAIY,KAAKP,EAAI1G,SAGxB,GAAIwG,EAASW,UAAYX,IAAa,QAAUA,IAAa,OAElEH,EAAIY,KAAKT,QACJ,GAAIA,EAAS5S,OAAS,GAAK4S,EAAS,GAAGW,SAE5C,IAAKnH,EAAI,EAAGA,EAAIwG,EAAS5S,OAAQoM,GAAK,EACpCqG,EAAIY,KAAKT,EAASxG,IAIxB,OAAO,IAAIoG,EAAKC,GAOlB,SAASe,EAAOf,GACd,MAAMgB,EAAc,GACpB,IAAK,IAAIrH,EAAI,EAAGA,EAAIqG,EAAIzS,OAAQoM,GAAK,GACE,IAAjCqH,EAAYP,QAAQT,EAAIrG,KAAYqH,EAAYJ,KAAKZ,EAAIrG,IAE/D,OAAOqH,EAkBT,SAASC,EAASC,GAChB,GAAyB,qBAAdA,EACT,OAAO3W,KAET,MAAM4W,EAAUD,EAAUzN,MAAM,KAChC,IAAK,IAAIkG,EAAI,EAAGA,EAAIwH,EAAQ5T,OAAQoM,GAAK,EACvC,IAAK,IAAIyH,EAAI,EAAGA,EAAI7W,KAAKgD,OAAQ6T,GAAK,EACb,qBAAZ7W,KAAK6W,IAAmD,qBAAtB7W,KAAK6W,GAAGC,WAA2B9W,KAAK6W,GAAGC,UAAUC,IAAIH,EAAQxH,IAGlH,OAAOpP,KAET,SAASgX,EAAYL,GACnB,MAAMC,EAAUD,EAAUzN,MAAM,KAChC,IAAK,IAAIkG,EAAI,EAAGA,EAAIwH,EAAQ5T,OAAQoM,GAAK,EACvC,IAAK,IAAIyH,EAAI,EAAGA,EAAI7W,KAAKgD,OAAQ6T,GAAK,EACb,qBAAZ7W,KAAK6W,IAAmD,qBAAtB7W,KAAK6W,GAAGC,WAA2B9W,KAAK6W,GAAGC,UAAUG,OAAOL,EAAQxH,IAGrH,OAAOpP,KAET,SAASkX,EAASP,GAChB,QAAK3W,KAAK,IACHA,KAAK,GAAG8W,UAAUK,SAASR,GAEpC,SAASS,EAAYT,GACnB,MAAMC,EAAUD,EAAUzN,MAAM,KAChC,IAAK,IAAIkG,EAAI,EAAGA,EAAIwH,EAAQ5T,OAAQoM,GAAK,EACvC,IAAK,IAAIyH,EAAI,EAAGA,EAAI7W,KAAKgD,OAAQ6T,GAAK,EACb,qBAAZ7W,KAAK6W,IAAmD,qBAAtB7W,KAAK6W,GAAGC,WAA2B9W,KAAK6W,GAAGC,UAAUO,OAAOT,EAAQxH,IAGrH,OAAOpP,KAET,SAASsX,EAAK1W,EAAOc,GACnB,GAAyB,IAArB6V,UAAUvU,QAAiC,kBAAVpC,EAEnC,OAAIZ,KAAK,GAAWA,KAAK,GAAGwX,aAAa5W,QACzC,EAIF,IAAK,IAAIwO,EAAI,EAAGA,EAAIpP,KAAKgD,OAAQoM,GAAK,EACpC,GAAyB,IAArBmI,UAAUvU,OAEZhD,KAAKoP,GAAG8D,aAAatS,EAAOc,QAI5B,IAAK,MAAM+V,KAAY7W,EACrBZ,KAAKoP,GAAGqI,GAAY7W,EAAM6W,GAC1BzX,KAAKoP,GAAG8D,aAAauE,EAAU7W,EAAM6W,IAI3C,OAAOzX,KAGT,SAAS0X,EAAWJ,GAClB,IAAK,IAAIlI,EAAI,EAAGA,EAAIpP,KAAKgD,OAAQoM,GAAK,EACpCpP,KAAKoP,GAAGuI,gBAAgBL,GAE1B,OAAOtX,KAwBT,SAASkD,EAAKgD,EAAKxE,GACjB,IAAIkW,EACJ,GAAqB,qBAAVlW,EAAX,CAkBA,IAAK,IAAI0N,EAAI,EAAGA,EAAIpP,KAAKgD,OAAQoM,GAAK,EACpCwI,EAAK5X,KAAKoP,GACLwI,EAAGC,yBAAwBD,EAAGC,uBAAyB,IAC5DD,EAAGC,uBAAuB3R,GAAOxE,EAEnC,OAAO1B,KApBL,GAFA4X,EAAK5X,KAAK,GAEN4X,EAAI,CACN,GAAIA,EAAGC,wBAA2B3R,KAAO0R,EAAGC,uBAC1C,OAAOD,EAAGC,uBAAuB3R,GAGnC,MAAM8I,EAAU4I,EAAGJ,aAAa,QAAQtR,GACxC,OAAI8I,QAGJ,GA8EN,SAAS8I,EAAUA,GACjB,IAAK,IAAI1I,EAAI,EAAGA,EAAIpP,KAAKgD,OAAQoM,GAAK,EAAG,CACvC,MAAM2I,EAAU/X,KAAKoP,GAAG6D,MACxB8E,EAAQC,gBAAkBF,EAC1BC,EAAQD,UAAYA,EAEtB,OAAO9X,KAET,SAASiY,EAAWC,GACM,kBAAbA,IACTA,GAAW,MAEb,IAAK,IAAI9I,EAAI,EAAGA,EAAIpP,KAAKgD,OAAQoM,GAAK,EAAG,CACvC,MAAM2I,EAAU/X,KAAKoP,GAAG6D,MACxB8E,EAAQI,yBAA2BD,EACnCH,EAAQK,mBAAqBF,EAE/B,OAAOlY,KAGT,SAASU,KAAM2X,GACb,IAAKC,EAAWC,EAAgBC,EAAUC,GAAWJ,EAOrD,SAASK,EAAgBhU,GACvB,MAAM3C,EAAS2C,EAAE3C,OACjB,IAAKA,EAAQ,OACb,MAAM4W,EAAYjU,EAAE3C,OAAO6W,eAAiB,GAI5C,GAHID,EAAUzC,QAAQxR,GAAK,GACzBiU,EAAUE,QAAQnU,GAEhBiR,EAAE5T,GAAQ+W,GAAGP,GAAiBC,EAASO,MAAMhX,EAAQ4W,OACpD,CACH,MAAMK,EAAUrD,EAAE5T,GAAQiX,UAC1B,IAAK,IAAIC,EAAI,EAAGA,EAAID,EAAQhW,OAAQiW,GAAK,EACnCtD,EAAEqD,EAAQC,IAAIH,GAAGP,IAAiBC,EAASO,MAAMC,EAAQC,GAAIN,IAIvE,SAASO,EAAYxU,GACnB,MAAMiU,EAAYjU,GAAKA,EAAE3C,QAAS2C,EAAE3C,OAAO6W,eAAsB,GAC7DD,EAAUzC,QAAQxR,GAAK,GACzBiU,EAAUE,QAAQnU,GAEpB8T,EAASO,MAAM/Y,KAAM2Y,GA1BA,oBAAZN,EAAK,MACbC,EAAWE,EAAUC,GAAWJ,EACjCE,OAAiBxM,GAEd0M,IAASA,GAAU,GAwBxB,MAAMU,EAASb,EAAUpP,MAAM,KAC/B,IAAI2N,EACJ,IAAK,IAAIzH,EAAI,EAAGA,EAAIpP,KAAKgD,OAAQoM,GAAK,EAAG,CACvC,MAAMwI,EAAK5X,KAAKoP,GAChB,GAAKmJ,EAaH,IAAK1B,EAAI,EAAGA,EAAIsC,EAAOnW,OAAQ6T,GAAK,EAAG,CACrC,MAAMuC,EAAQD,EAAOtC,GAChBe,EAAGyB,oBAAmBzB,EAAGyB,kBAAoB,IAC7CzB,EAAGyB,kBAAkBD,KAAQxB,EAAGyB,kBAAkBD,GAAS,IAChExB,EAAGyB,kBAAkBD,GAAO/C,KAAK,CAC/BmC,WACAc,cAAeZ,IAEjBd,EAAGnL,iBAAiB2M,EAAOV,EAAiBD,QApB9C,IAAK5B,EAAI,EAAGA,EAAIsC,EAAOnW,OAAQ6T,GAAK,EAAG,CACrC,MAAMuC,EAAQD,EAAOtC,GAChBe,EAAG2B,gBAAe3B,EAAG2B,cAAgB,IACrC3B,EAAG2B,cAAcH,KAAQxB,EAAG2B,cAAcH,GAAS,IACxDxB,EAAG2B,cAAcH,GAAO/C,KAAK,CAC3BmC,WACAc,cAAeJ,IAEjBtB,EAAGnL,iBAAiB2M,EAAOF,EAAaT,IAgB9C,OAAOzY,KAET,SAASwZ,KAAOnB,GACd,IAAKC,EAAWC,EAAgBC,EAAUC,GAAWJ,EAC9B,oBAAZA,EAAK,MACbC,EAAWE,EAAUC,GAAWJ,EACjCE,OAAiBxM,GAEd0M,IAASA,GAAU,GAExB,MAAMU,EAASb,EAAUpP,MAAM,KAC/B,IAAK,IAAIkG,EAAI,EAAGA,EAAI+J,EAAOnW,OAAQoM,GAAK,EAAG,CACzC,MAAMgK,EAAQD,EAAO/J,GACrB,IAAK,IAAIyH,EAAI,EAAGA,EAAI7W,KAAKgD,OAAQ6T,GAAK,EAAG,CACvC,MAAMe,EAAK5X,KAAK6W,GAChB,IAAI4C,EAMJ,IALKlB,GAAkBX,EAAG2B,cACxBE,EAAW7B,EAAG2B,cAAcH,GACnBb,GAAkBX,EAAGyB,oBAC9BI,EAAW7B,EAAGyB,kBAAkBD,IAE9BK,GAAYA,EAASzW,OACvB,IAAK,IAAIiW,EAAIQ,EAASzW,OAAS,EAAGiW,GAAK,EAAGA,GAAK,EAAG,CAChD,MAAMS,EAAUD,EAASR,GACrBT,GAAYkB,EAAQlB,WAAaA,GAG1BA,GAAYkB,EAAQlB,UAAYkB,EAAQlB,SAASmB,WAAaD,EAAQlB,SAASmB,YAAcnB,GAFtGZ,EAAGvF,oBAAoB+G,EAAOM,EAAQJ,cAAeb,GACrDgB,EAASG,OAAOX,EAAG,IAITT,IACVZ,EAAGvF,oBAAoB+G,EAAOM,EAAQJ,cAAeb,GACrDgB,EAASG,OAAOX,EAAG,MAM7B,OAAOjZ,KAmBT,SAAS6Z,KAAWxB,GAClB,MAAMc,EAASd,EAAK,GAAGnP,MAAM,KACvByP,EAAYN,EAAK,GACvB,IAAK,IAAIjJ,EAAI,EAAGA,EAAI+J,EAAOnW,OAAQoM,GAAK,EAAG,CACzC,MAAMgK,EAAQD,EAAO/J,GACrB,IAAK,IAAIyH,EAAI,EAAGA,EAAI7W,KAAKgD,OAAQ6T,GAAK,EAAG,CACvC,MAAMe,EAAK5X,KAAK6W,GAChB,IAAIiD,EACJ,IACEA,EAAM,IAAI,OAAOzF,YAAY+E,EAAO,CAClCW,OAAQpB,EACRqB,SAAS,EACTC,YAAY,IAEd,MAAOvV,GACPoV,EAAM,OAASlH,YAAY,SAC3BkH,EAAIjH,UAAUuG,GAAO,GAAM,GAC3BU,EAAIC,OAASpB,EAGff,EAAGgB,cAAgBP,EAAKlI,OAAO,CAACjN,EAAMgX,IAAcA,EAAY,GAChEtC,EAAGuC,cAAcL,GACjBlC,EAAGgB,cAAgB,UACZhB,EAAGgB,eAGd,OAAO5Y,KAET,SAASoa,EAAcC,GACrB,MAAMlB,EAAS,CAAC,sBAAuB,iBACjCmB,EAAMta,KACZ,IAAIoP,EACJ,SAASmL,EAAa7V,GAEpB,GAAIA,EAAE3C,SAAW/B,KAEjB,IADAqa,EAASG,KAAKxa,KAAM0E,GACf0K,EAAI,EAAGA,EAAI+J,EAAOnW,OAAQoM,GAAK,EAClCkL,EAAId,IAAIL,EAAO/J,GAAImL,GAGvB,GAAIF,EACF,IAAKjL,EAAI,EAAGA,EAAI+J,EAAOnW,OAAQoM,GAAK,EAClCkL,EAAI5Z,GAAGyY,EAAO/J,GAAImL,GAGtB,OAAOva,KAgCT,SAASya,EAAWC,GAClB,GAAI1a,KAAKgD,OAAS,EAAG,CACnB,GAAI0X,EAAgB,CAElB,MAAMC,EAAS3a,KAAK2a,SACpB,OAAO3a,KAAK,GAAG4a,YAAcC,WAAWF,EAAOpG,iBAAiB,iBAAmBsG,WAAWF,EAAOpG,iBAAiB,gBAExH,OAAOvU,KAAK,GAAG4a,YAEjB,OAAO,KAaT,SAASE,EAAYJ,GACnB,GAAI1a,KAAKgD,OAAS,EAAG,CACnB,GAAI0X,EAAgB,CAElB,MAAMC,EAAS3a,KAAK2a,SACpB,OAAO3a,KAAK,GAAG+a,aAAeF,WAAWF,EAAOpG,iBAAiB,eAAiBsG,WAAWF,EAAOpG,iBAAiB,kBAEvH,OAAOvU,KAAK,GAAG+a,aAEjB,OAAO,KAET,SAASC,IACP,GAAIhb,KAAKgD,OAAS,EAAG,CACnB,MAAM4U,EAAK5X,KAAK,GACVib,EAAMrD,EAAGsD,wBACT9I,EAAO,OAASA,KAChB+I,EAAYvD,EAAGuD,WAAa/I,EAAK+I,WAAa,EAC9CC,EAAaxD,EAAGwD,YAAchJ,EAAKgJ,YAAc,EACjDC,EAAYzD,IAAO,OAAS,OAAO0D,QAAU1D,EAAGyD,UAChDE,EAAa3D,IAAO,OAAS,OAAO4D,QAAU5D,EAAG2D,WACvD,MAAO,CACLE,IAAMR,EAAIQ,IAAMJ,EAAaF,EAC7BO,KAAOT,EAAIS,KAAOH,EAAcH,GAIpC,OAAO,KAqBT,SAAST,IACP,OAAI3a,KAAK,GAAW,OAAOsU,iBAAiBtU,KAAK,GAAI,MAC9C,GAET,SAAS2b,EAAIhZ,EAAOjB,GAClB,IAAI0N,EACJ,GAAyB,IAArBmI,UAAUvU,OAAc,CAC1B,GAAqB,kBAAVL,EAEJ,CACL,IAAKyM,EAAI,EAAGA,EAAIpP,KAAKgD,OAAQoM,GAAK,EAEhC,IAAK,IAAIwM,KAAQjZ,EACf3C,KAAKoP,GAAG6D,MAAM2I,GAAQjZ,EAAMiZ,GAGhC,OAAO5b,KARP,GAAIA,KAAK,GAAI,OAAO,OAAOsU,iBAAiBtU,KAAK,GAAI,MAAMuU,iBAAiB5R,GAWhF,GAAyB,IAArB4U,UAAUvU,QAAiC,kBAAVL,EAAoB,CACvD,IAAKyM,EAAI,EAAGA,EAAIpP,KAAKgD,OAAQoM,GAAK,EAChCpP,KAAKoP,GAAG6D,MAAMtQ,GAASjB,EAEzB,OAAO1B,KAET,OAAOA,KAYT,SAAS6b,EAAKxB,GAEZ,IAAKA,EAAU,OAAOra,KAEtB,IAAK,IAAIoP,EAAI,EAAGA,EAAIpP,KAAKgD,OAAQoM,GAAK,EAEpC,IAA2C,IAAvCiL,EAASG,KAAKxa,KAAKoP,GAAIA,EAAGpP,KAAKoP,IAEjC,OAAOpP,KAIX,OAAOA,KAgBT,SAASmQ,EAAOkK,GACd,MAAMyB,EAAe,GACfxB,EAAMta,KACZ,IAAK,IAAIoP,EAAI,EAAGA,EAAIkL,EAAItX,OAAQoM,GAAK,EAC/BiL,EAASG,KAAKF,EAAIlL,GAAIA,EAAGkL,EAAIlL,KAAK0M,EAAazF,KAAKiE,EAAIlL,IAE9D,OAAO,IAAIoG,EAAKsG,GAWlB,SAAS9F,EAAKA,GACZ,GAAoB,qBAATA,EACT,OAAOhW,KAAK,GAAKA,KAAK,GAAGoW,eAAYrK,EAGvC,IAAK,IAAIqD,EAAI,EAAGA,EAAIpP,KAAKgD,OAAQoM,GAAK,EACpCpP,KAAKoP,GAAGgH,UAAYJ,EAEtB,OAAOhW,KAGT,SAAS+b,EAAKA,GACZ,GAAoB,qBAATA,EACT,OAAI/b,KAAK,GACAA,KAAK,GAAGgc,YAAY/F,OAEtB,KAGT,IAAK,IAAI7G,EAAI,EAAGA,EAAIpP,KAAKgD,OAAQoM,GAAK,EACpCpP,KAAKoP,GAAG4M,YAAcD,EAExB,OAAO/b,KAET,SAAS8Y,EAAGlD,GACV,MAAMgC,EAAK5X,KAAK,GAChB,IAAIic,EACA7M,EACJ,IAAKwI,GAA0B,qBAAbhC,EAA0B,OAAO,EACnD,GAAwB,kBAAbA,EAAuB,CAChC,GAAIgC,EAAGxM,QAAS,OAAOwM,EAAGxM,QAAQwK,GAC7B,GAAIgC,EAAGsE,sBAAuB,OAAOtE,EAAGsE,sBAAsBtG,GAC9D,GAAIgC,EAAGuE,kBAAmB,OAAOvE,EAAGuE,kBAAkBvG,GAG3D,IADAqG,EAActG,EAAEC,GACXxG,EAAI,EAAGA,EAAI6M,EAAYjZ,OAAQoM,GAAK,EACvC,GAAI6M,EAAY7M,KAAOwI,EAAI,OAAO,EAEpC,OAAO,EACF,GAAIhC,IAAa,OAAU,OAAOgC,IAAO,OAC3C,GAAIhC,IAAa,OAAQ,OAAOgC,IAAO,OAE5C,GAAIhC,EAASW,UAAYX,aAAoBJ,EAAM,CAEjD,IADAyG,EAAcrG,EAASW,SAAW,CAACX,GAAYA,EAC1CxG,EAAI,EAAGA,EAAI6M,EAAYjZ,OAAQoM,GAAK,EACvC,GAAI6M,EAAY7M,KAAOwI,EAAI,OAAO,EAEpC,OAAO,EAET,OAAO,EAQT,SAAS/N,IACP,IACIuF,EADAgN,EAAQpc,KAAK,GAEjB,GAAIoc,EAAO,CACThN,EAAI,EAEJ,MAA2C,QAAnCgN,EAAQA,EAAMC,iBACG,IAAnBD,EAAM7F,WAAgBnH,GAAK,GAEjC,OAAOA,GAKX,SAASkN,EAAGzS,GACV,GAAqB,qBAAVA,EAAuB,OAAO7J,KACzC,MAAMgD,EAAShD,KAAKgD,OACpB,IAAIuZ,EACJ,OAAI1S,EAAQ7G,EAAS,EACZ,IAAIwS,EAAK,IAEd3L,EAAQ,GACV0S,EAAcvZ,EAAS6G,EACK,IAAI2L,EAA5B+G,EAAc,EAAmB,GACrB,CAACvc,KAAKuc,MAEjB,IAAI/G,EAAK,CAACxV,KAAK6J,KAExB,SAAS2S,KAAUnE,GACjB,IAAIoE,EAEJ,IAAK,IAAIxD,EAAI,EAAGA,EAAIZ,EAAKrV,OAAQiW,GAAK,EAAG,CACvCwD,EAAWpE,EAAKY,GAChB,IAAK,IAAI7J,EAAI,EAAGA,EAAIpP,KAAKgD,OAAQoM,GAAK,EACpC,GAAwB,kBAAbqN,EAAuB,CAChC,MAAMC,EAAU,OAAS5J,cAAc,OACvC4J,EAAQtG,UAAYqG,EACpB,MAAOC,EAAQC,WACb3c,KAAKoP,GAAGwN,YAAYF,EAAQC,iBAEzB,GAAIF,aAAoBjH,EAC7B,IAAK,IAAIqB,EAAI,EAAGA,EAAI4F,EAASzZ,OAAQ6T,GAAK,EACxC7W,KAAKoP,GAAGwN,YAAYH,EAAS5F,SAG/B7W,KAAKoP,GAAGwN,YAAYH,GAK1B,OAAOzc,KAOT,SAAS6c,EAAQJ,GACf,IAAIrN,EACAyH,EACJ,IAAKzH,EAAI,EAAGA,EAAIpP,KAAKgD,OAAQoM,GAAK,EAChC,GAAwB,kBAAbqN,EAAuB,CAChC,MAAMC,EAAU,OAAS5J,cAAc,OAEvC,IADA4J,EAAQtG,UAAYqG,EACf5F,EAAI6F,EAAQ1J,WAAWhQ,OAAS,EAAG6T,GAAK,EAAGA,GAAK,EACnD7W,KAAKoP,GAAG0N,aAAaJ,EAAQ1J,WAAW6D,GAAI7W,KAAKoP,GAAG4D,WAAW,SAE5D,GAAIyJ,aAAoBjH,EAC7B,IAAKqB,EAAI,EAAGA,EAAI4F,EAASzZ,OAAQ6T,GAAK,EACpC7W,KAAKoP,GAAG0N,aAAaL,EAAS5F,GAAI7W,KAAKoP,GAAG4D,WAAW,SAGvDhT,KAAKoP,GAAG0N,aAAaL,EAAUzc,KAAKoP,GAAG4D,WAAW,IAGtD,OAAOhT,KA+BT,SAAS+c,EAAKnH,GACZ,OAAI5V,KAAKgD,OAAS,EACZ4S,EACE5V,KAAK,GAAGgd,oBAAsBrH,EAAE3V,KAAK,GAAGgd,oBAAoBlE,GAAGlD,GAC1D,IAAIJ,EAAK,CAACxV,KAAK,GAAGgd,qBAEpB,IAAIxH,EAAK,IAGdxV,KAAK,GAAGgd,mBAA2B,IAAIxH,EAAK,CAACxV,KAAK,GAAGgd,qBAClD,IAAIxH,EAAK,IAEX,IAAIA,EAAK,IAElB,SAASyH,EAAQrH,GACf,MAAMsH,EAAU,GAChB,IAAItF,EAAK5X,KAAK,GACd,IAAK4X,EAAI,OAAO,IAAIpC,EAAK,IACzB,MAAOoC,EAAGoF,mBAAoB,CAC5B,MAAMD,EAAOnF,EAAGoF,mBACZpH,EACED,EAAEoH,GAAMjE,GAAGlD,IAAWsH,EAAQ7G,KAAK0G,GAClCG,EAAQ7G,KAAK0G,GACpBnF,EAAKmF,EAEP,OAAO,IAAIvH,EAAK0H,GAElB,SAASC,EAAKvH,GACZ,GAAI5V,KAAKgD,OAAS,EAAG,CACnB,MAAM4U,EAAK5X,KAAK,GAChB,OAAI4V,EACEgC,EAAGwF,wBAA0BzH,EAAEiC,EAAGwF,wBAAwBtE,GAAGlD,GACxD,IAAIJ,EAAK,CAACoC,EAAGwF,yBAEf,IAAI5H,EAAK,IAGdoC,EAAGwF,uBAA+B,IAAI5H,EAAK,CAACoC,EAAGwF,yBAC5C,IAAI5H,EAAK,IAElB,OAAO,IAAIA,EAAK,IAElB,SAAS6H,EAAQzH,GACf,MAAM0H,EAAU,GAChB,IAAI1F,EAAK5X,KAAK,GACd,IAAK4X,EAAI,OAAO,IAAIpC,EAAK,IACzB,MAAOoC,EAAGwF,uBAAwB,CAChC,MAAMD,EAAOvF,EAAGwF,uBACZxH,EACED,EAAEwH,GAAMrE,GAAGlD,IAAW0H,EAAQjH,KAAK8G,GAClCG,EAAQjH,KAAK8G,GACpBvF,EAAKuF,EAEP,OAAO,IAAI3H,EAAK8H,GAKlB,SAASC,EAAO3H,GACd,MAAMoD,EAAU,GAChB,IAAK,IAAI5J,EAAI,EAAGA,EAAIpP,KAAKgD,OAAQoM,GAAK,EACT,OAAvBpP,KAAKoP,GAAGoO,aACN5H,EACED,EAAE3V,KAAKoP,GAAGoO,YAAY1E,GAAGlD,IAAWoD,EAAQ3C,KAAKrW,KAAKoP,GAAGoO,YAE7DxE,EAAQ3C,KAAKrW,KAAKoP,GAAGoO,aAI3B,OAAO7H,EAAEa,EAAOwC,IAElB,SAASA,EAAQpD,GACf,MAAMoD,EAAU,GAChB,IAAK,IAAI5J,EAAI,EAAGA,EAAIpP,KAAKgD,OAAQoM,GAAK,EAAG,CACvC,IAAImO,EAASvd,KAAKoP,GAAGoO,WACrB,MAAOD,EACD3H,EACED,EAAE4H,GAAQzE,GAAGlD,IAAWoD,EAAQ3C,KAAKkH,GAEzCvE,EAAQ3C,KAAKkH,GAEfA,EAASA,EAAOC,WAGpB,OAAO7H,EAAEa,EAAOwC,IAElB,SAASyE,EAAQ7H,GACf,IAAI6H,EAAUzd,KACd,MAAwB,qBAAb4V,EACF,IAAIJ,EAAK,KAEbiI,EAAQ3E,GAAGlD,KACd6H,EAAUA,EAAQzE,QAAQpD,GAAU0G,GAAG,IAElCmB,GAET,SAASC,EAAK9H,GACZ,MAAM+H,EAAgB,GACtB,IAAK,IAAIvO,EAAI,EAAGA,EAAIpP,KAAKgD,OAAQoM,GAAK,EAAG,CACvC,MAAMwO,EAAQ5d,KAAKoP,GAAGsD,iBAAiBkD,GACvC,IAAK,IAAIiB,EAAI,EAAGA,EAAI+G,EAAM5a,OAAQ6T,GAAK,EACrC8G,EAActH,KAAKuH,EAAM/G,IAG7B,OAAO,IAAIrB,EAAKmI,GAElB,SAAS5K,EAAS6C,GAChB,MAAM7C,EAAW,GACjB,IAAK,IAAI3D,EAAI,EAAGA,EAAIpP,KAAKgD,OAAQoM,GAAK,EAAG,CACvC,MAAM4D,EAAahT,KAAKoP,GAAG4D,WAE3B,IAAK,IAAI6D,EAAI,EAAGA,EAAI7D,EAAWhQ,OAAQ6T,GAAK,EACrCjB,EAEiC,IAA3B5C,EAAW6D,GAAGN,UAAkBZ,EAAE3C,EAAW6D,IAAIiC,GAAGlD,IAC7D7C,EAASsD,KAAKrD,EAAW6D,IAFM,IAA3B7D,EAAW6D,GAAGN,UAAgBxD,EAASsD,KAAKrD,EAAW6D,IAMjE,OAAO,IAAIrB,EAAKgB,EAAOzD,IAEzB,SAASkE,IACP,IAAK,IAAI7H,EAAI,EAAGA,EAAIpP,KAAKgD,OAAQoM,GAAK,EAChCpP,KAAKoP,GAAGoO,YAAYxd,KAAKoP,GAAGoO,WAAWK,YAAY7d,KAAKoP,IAE9D,OAAOpP,KAKT,SAAS+W,KAAOsB,GACd,MAAMiC,EAAMta,KACZ,IAAIoP,EACAyH,EACJ,IAAKzH,EAAI,EAAGA,EAAIiJ,EAAKrV,OAAQoM,GAAK,EAAG,CACnC,MAAM0O,EAAQnI,EAAE0C,EAAKjJ,IACrB,IAAKyH,EAAI,EAAGA,EAAIiH,EAAM9a,OAAQ6T,GAAK,EACjCyD,EAAIA,EAAItX,QAAU8a,EAAMjH,GACxByD,EAAItX,QAAU,EAGlB,OAAOsX,EAz2BT3E,EAAExP,GAAKqP,EAAKuI,UACZpI,EAAEqI,MAAQxI,EACVG,EAAEH,KAAOA,EA+pCS,gBAAkBtM,MAAM,M,qBChvC1C,IAAI5D,EAAM,CACT,8BAA+B,OAC/B,6BAA8B,OAC9B,8BAA+B,QAIhC,SAASC,EAAeC,GACvB,IAAIhD,EAAKiD,EAAsBD,GAC/B,OAAOE,EAAoBlD,GAE5B,SAASiD,EAAsBD,GAC9B,IAAIE,EAAoBC,EAAEL,EAAKE,GAAM,CACpC,IAAId,EAAI,IAAIkB,MAAM,uBAAyBJ,EAAM,KAEjD,MADAd,EAAET,KAAO,mBACHS,EAEP,OAAOY,EAAIE,GAEZD,EAAeM,KAAO,WACrB,OAAOC,OAAOD,KAAKP,IAEpBC,EAAeQ,QAAUN,EACzBL,EAAOC,QAAUE,EACjBA,EAAe/C,GAAK,Q,qBCxBpB4C,EAAOC,QAAU,IAA0B", "file": "js/chunk-functions.e3d7a4ee.js", "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('container',{class:['custom-diamond-wrapper', _vm.$gameName]},[(_vm.showHowToCatPage)?_c('div',{staticClass:\"cat-page\"},[_c('div',{staticClass:\"title\"},[_vm._v(_vm._s(_vm.$t('login-validation-main-how-cat')))]),_c('div',{staticClass:\"back-btn\",on:{\"click\":function($event){_vm.showHowToCatPage = false}}}),_c('img',{attrs:{\"src\":_vm.$imageLoader('loganFindValidationCode'),\"alt\":\"\"}}),_c('div',{staticClass:\"time-tips\",domProps:{\"innerHTML\":_vm._s(_vm.$t('login-validation-date-construction').replace('30', `<span>${_vm.loginValidationExpireCount}</span>`))}})]):_c('div',{staticClass:\"main-page\"},[_c('div',{staticClass:\"title\"},[_vm._v(_vm._s(_vm.$t('login-validation-main-title')))]),_c('div',{staticClass:\"send-info\"},[_c('div',{staticClass:\"tips\",domProps:{\"innerHTML\":_vm._s(_vm.$t('login-validation-main-send-over', { 0: _vm.htmlUsername }))}}),(_vm.canResend)?_c('div',{staticClass:\"send-btn\",on:{\"click\":_vm.resend}},[_vm._v(_vm._s(_vm.$t('login-validation-main-resend')))]):_vm._e(),(_vm.leaveCount > 0)?_c('div',{staticClass:\"leave-count\"},[_vm._v(\"（\"+_vm._s(_vm.leaveCount)+\"s）\")]):_vm._e()]),_c('div',{staticClass:\"input-wrapper\"},[_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.inputCode),expression:\"inputCode\"}],class:_vm.$i18n.locale,attrs:{\"type\":\"number\",\"placeholder\":_vm.$t('login-validation-main-placeholder'),\"autofocus\":\"\"},domProps:{\"value\":(_vm.inputCode)},on:{\"input\":[function($event){if($event.target.composing)return;_vm.inputCode=$event.target.value},_vm.fixInput]}})]),_c('div',{staticClass:\"safe-tips\",domProps:{\"innerHTML\":_vm._s(_vm.$t('login-validation-safety-construction'))}}),_c('div',{class:['confirm-btn', {'confirm-btn_validated': _vm.rawValidate}],on:{\"click\":_vm.checkCode}},[_vm._v(_vm._s(_vm.$t('login-validation-main-confirm')))]),_c('div',{staticClass:\"cat-code-tips\",on:{\"click\":function($event){_vm.showHowToCatPage = true}}},[_vm._v(_vm._s(_vm.$t('login-validation-main-how-cat')))]),_c('div',{staticClass:\"close\",on:{\"click\":_vm.close}})])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <container :class=\"['custom-diamond-wrapper', $gameName]\">\n    <div v-if=\"showHowToCatPage\" class=\"cat-page\">\n      <div class=\"title\">{{ $t('login-validation-main-how-cat') }}</div>\n      <div class=\"back-btn\" @click=\"showHowToCatPage = false\"></div>\n      <img :src=\"$imageLoader('loganFindValidationCode')\" alt=\"\">\n      <div class=\"time-tips\" v-html=\"$t('login-validation-date-construction').replace('30', `<span>${loginValidationExpireCount}</span>`)\">\n      </div>\n    </div>\n    <div v-else class=\"main-page\">\n      <div class=\"title\">{{ $t('login-validation-main-title') }}</div>\n      <div class=\"send-info\">\n        <div class=\"tips\" v-html=\"$t('login-validation-main-send-over', { 0: htmlUsername })\"></div>\n        <div v-if=\"canResend\" class=\"send-btn\" @click=\"resend\">{{ $t('login-validation-main-resend') }}</div>\n        <div v-if=\"leaveCount > 0\" class=\"leave-count\">（{{ leaveCount }}s）</div>\n      </div>\n      <div class=\"input-wrapper\">\n        <input v-model=\"inputCode\" type=\"number\" :placeholder=\"$t('login-validation-main-placeholder')\" autofocus @input=\"fixInput\" :class=\"$i18n.locale\">\n      </div>\n      <div class=\"safe-tips\" v-html=\"$t('login-validation-safety-construction')\"></div>\n\n      <div :class=\"['confirm-btn', {'confirm-btn_validated': rawValidate}]\" @click=\"checkCode\">{{ $t('login-validation-main-confirm') }}</div>\n      <div class=\"cat-code-tips\" @click=\"showHowToCatPage = true\">{{ $t('login-validation-main-how-cat') }}</div>\n      <div class=\"close\" @click=\"close\"></div>\n    </div>\n  </container>\n</template>\n\n<script>\nimport Container from '@/components/pop/containerV2'\nimport { checkCode, sendCode } from '@/server'\nimport { dealSmDeviceId } from '@/utils/utils'\nlet deviceId = ''\ndealSmDeviceId((id) => { deviceId = id })\n\nexport default {\n  name: 'ChannelKlarnaPopup',\n  components: { Container },\n  props: ['option'],\n  computed: {\n    htmlUsername () {\n      return `<span>${this.option.username}</span>`\n    },\n    rawValidate () {\n      const inputCode = this.inputCode\n      return String(inputCode).length === 6\n    },\n    canResend () {\n      return this.leaveCount === 0 && this.leaveTimes > 0\n    }\n  },\n  data () {\n    return {\n      inputCode: '',\n      showHowToCatPage: false,\n      leaveCount: 59,\n      leaveTimes: this.option.remaining_verification_attempts || 0,\n      countInterval: '',\n      loginValidationExpireCount: this.$gcbk('ids.loginValidationExpireCount', 30)\n    }\n  },\n  methods: {\n    countTime (leaveCount) {\n      this.leaveCount = leaveCount || 59\n      this.countInterval = setInterval(() => {\n        this.leaveCount--\n\n        if (this.leaveCount === 0) {\n          clearInterval(this.countInterval)\n          this.countInterval = null\n        }\n      }, 1000)\n    },\n    resend () {\n      this.$loading.show()\n      sendCode({ fp_device_id: deviceId, openid: this.option.openid })\n        .then(res => {\n          const { code } = res\n\n          switch (code) {\n            case 0: {\n              this.leaveTimes = res.data.remaining_verification_attempts\n              // this.$toast.err('验证码已发送')\n              this.countTime()\n              break\n            }\n            // case 5011: {\n            //   const successCb = this.option.successCb\n            //   if (successCb) successCb()\n            //   this.$root.$emit('closePop')\n            //   break\n            // }\n            default: {\n              this.$toast.err(this.$t('login-validation-error-code'))\n            }\n          }\n        })\n        .finally(() => this.$loading.hide())\n    },\n    async checkCode () {\n      if (!this.rawValidate) return null\n\n      this.$loading.show()\n      checkCode({ code: +this.inputCode, fp_device_id: deviceId, openid: this.option.openid })\n        .then(res => {\n          const { code } = res\n          this.inputCode = ''\n          switch (code) {\n            case 0: case 5011: {\n              const successCb = this.option.successCb\n              if (successCb) successCb()\n              this.$root.$emit('closePop')\n              break\n            }\n            case 5009: {\n              this.$toast.err(this.$t('login-validation-error-expire'))\n              break\n            }\n            case 5010: {\n              this.$toast.err(this.$t('login-validation-error-code'))\n              break\n            }\n            default: {\n              this.$toast.err(this.$t('login-validation-error-text'))\n            }\n          }\n        })\n        .finally(() => this.$loading.hide())\n    },\n    close () {\n      this.$root.$emit('closePop')\n      const failCb = this.option.failCb\n      if (failCb) failCb()\n    },\n    fixInput (e) {\n      const { data } = e\n      const re = /^\\d$/.test(data)\n      if (data === 'e' || data === '.' || data === 'E' || !re) {\n        e.target.value = ''\n      }\n\n      const length = this.inputCode.length\n      if (length > 6) this.inputCode = this.inputCode.slice(0, 6)\n    }\n  },\n  created () {\n    const leaveCount = ((this.option && this.option.send_code_cd) || 59)\n    this.countTime(leaveCount)\n  },\n  beforeDestroy () {\n    if (this.countInterval) {\n      this.countInterval = null\n      clearInterval(this.countInterval)\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n@use \"~@/utils/utils.scss\" as utils;\n.custom-diamond-wrapper {\n  background-color: #2C3757;\n  padding-bottom: 28px;\n  padding-top: 20px;\n\n  /* common */\n  .title{\n    font-size: 32px;\n    font-family: SourceHanSansCN, SourceHanSansCN;\n    font-weight: bold;\n    color: #FFFFFF;\n    line-height: 46px;\n    max-width: 80%;\n    margin: 0 auto;\n  }\n\n  .main-page{\n    .send-info{\n      margin-top: 24px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n\n      .tips{\n        font-size: 24px;\n        font-family: SourceHanSansCN, SourceHanSansCN;\n        font-weight: 400;\n        color: #FFFFFF;\n        line-height: 35px;\n\n        ::v-deep{\n          span{\n            margin: 0 8px;\n            color: #FEB522;\n          }\n        }\n      }\n\n      .send-btn{\n        font-size: 24px;\n        font-family: SourceHanSansCN, SourceHanSansCN;\n        font-weight: bold;\n        color: #FEB522;\n        line-height: 35px;\n        margin-left: 8px;\n        text-decoration: underline;\n        cursor: pointer;\n      }\n\n      .leave-count{\n        @extend .tips;\n      }\n    }\n\n    .input-wrapper{\n      margin-top: 30px;\n      input{\n        width: calc(100% - 300px);\n        height: 60px;\n        line-height: 60px;\n        background: transparent;\n        border: 1px solid #FFFFFF;\n        appearance: none;\n        -webkit-appearance:none;\n        color: white;\n        font-size: 24px;\n        text-align: center;\n\n        &:active, &:focus{\n          appearance: none;\n          -webkit-appearance:none;\n          outline: none;\n          border: 1px solid #FFFFFF;\n        }\n\n        &::-webkit-input-placeholder{\n          font-size: 18px;\n          font-family: PingFangSC, PingFang SC;\n          font-weight: 400;\n          color: #999999;\n          text-align: center;\n        }\n      }\n    }\n\n    .safe-tips{\n      font-size: 18px;\n      font-family: SourceHanSansCN, SourceHanSansCN;\n      font-weight: 400;\n      color: #FEB522;\n      line-height: 26px;\n      width: calc(100% - 192px);\n      margin: 20px auto 0;\n    }\n\n    .confirm-btn{\n      font-size: 28px;\n      font-family: SourceHanSansCN, SourceHanSansCN;\n      font-weight: bold;\n      color: #633B00;\n      width: 240px;\n      height: 64px;\n      background: linear-gradient(to bottom, #F6E190, #CBA455);\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin: 40px auto 0;\n      opacity: 0.6;\n      cursor: not-allowed;\n\n      &.confirm-btn_validated{\n        opacity: 1;\n        cursor: pointer;\n      }\n    }\n\n    .cat-code-tips{\n      font-size: 20px;\n      font-family: SourceHanSansCN, SourceHanSansCN;\n      font-weight: 400;\n      color: #FFFFFF;\n      margin: 26px auto 0;\n    }\n\n    .close{\n      @include utils.bgCenter('common/login-validate/login-validate-close.png', 26px, 26px);\n      position: absolute;\n      cursor: pointer;\n      top: 29px;\n      right: 25px;\n    }\n  }\n\n  .cat-page{\n    img{\n      display: inline-block;\n      width: calc(100% - 40px);\n      margin: 30px auto 0;\n    }\n\n    .time-tips{\n      font-size: 24px;\n      font-family: SourceHanSansCN, SourceHanSansCN;\n      font-weight: 400;\n      color: #FFFFFF;\n\n      ::v-deep{\n        span{\n          color: #FEB522;\n          font-weight: bold;\n          margin: 0 8px;\n        }\n      }\n    }\n\n    .back-btn{\n      @include utils.bgCenter('common/login-validate/login-validate-back.png', 32px, 32px);\n      position: absolute;\n      cursor: pointer;\n      top: 22px;\n      left: 30px;\n    }\n  }\n}\n@include utils.setPcContent{\n  .custom-diamond-wrapper {\n    padding-top: 24px;\n\n    .title{\n      font-size: 24px;\n      line-height: 35px;\n    }\n\n    .main-page{\n      .send-info{\n        margin-top: 20px;\n        .tips{\n          font-size: 16px;\n          line-height: 24px;\n\n          ::v-deep{\n            span{\n              margin: 0 6px;\n              color: #FEB522;\n            }\n          }\n        }\n        .send-btn{\n          line-height: 24px;\n          font-size: 16px;\n        }\n\n        .leave-count{\n          @extend .tips;\n        }\n      }\n      .input-wrapper{\n        margin-top: 20px;\n        input{\n          width: calc(100% - 220px);\n          height: 50px;\n          line-height: 50px;\n          font-size: 18px;\n\n          &::-webkit-input-placeholder{\n            font-size: 18px;\n          }\n        }\n      }\n      .safe-tips{\n        font-size: 14px;\n        line-height: 20px;\n        margin-top: 10px;\n        width: calc(100% - 142px);\n      }\n      .confirm-btn{\n        margin-top: 20px;\n        width: 200px;\n        height: 50px;\n        font-size: 20px;\n      }\n      .cat-code-tips{\n        margin-top: 10px;\n        font-size: 14px;\n      }\n      .close{\n        @include utils.bgCenter('common/login-validate/login-validate-close.png', 22px, 22px);\n        position: absolute;\n        cursor: pointer;\n        top: 21px;\n        right: 25px;\n      }\n    }\n\n    .cat-page{\n      img{\n        width: calc(100% - 60px);\n        margin: 30px auto 0;\n      }\n\n      .time-tips{\n        font-size: 16px;\n        line-height: 24px;\n\n        ::v-deep{\n          span{\n            margin: 0 4px;\n          }\n        }\n      }\n    }\n  }\n}\n\n.custom-diamond-wrapper.ssv{\n  background-color: #363535;\n\n  .main-page{\n    .confirm-btn{\n      background: #FF5E0F;\n      color: #FFFFFF;\n      border-radius: 12px;\n    }\n  }\n}\n.custom-diamond-wrapper.dc{\n  background: #C3CBE1;\n  border: 1px solid #979797;\n\n  .title{\n    @extend .dc-stroke\n  }\n\n  .main-page{\n    .send-info{\n      @extend .dc-stroke;\n    }\n    .input-wrapper{\n      input{\n        background: #34353D;\n        border: 1px solid #000000;\n      }\n    }\n    .safe-tips{\n      color: #525280;\n    }\n    .confirm-btn{\n      @extend .dc-btn-decoration;\n      @extend .dc-stroke;\n      color: #F4FBFF;\n    }\n    .cat-code-tips{\n      color: #525280;\n    }\n    .close{\n      @include utils.bgCenterForDC('coupon/pop-close.png', 36px, 36px);\n    }\n  }\n  .cat-page{\n    .time-tips{\n      @extend .dc-stroke\n    }\n  }\n\n  @include utils.setPcContent{\n    .main-page{\n      .close{\n        @include utils.bgCenterForDC('coupon/pop-close.png', 27px, 27px);\n      }\n    }\n  }\n}\n</style>\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./LoginValidation.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./LoginValidation.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./LoginValidation.vue?vue&type=template&id=9d763512&scoped=true\"\nimport script from \"./LoginValidation.vue?vue&type=script&lang=js\"\nexport * from \"./LoginValidation.vue?vue&type=script&lang=js\"\nimport style0 from \"./LoginValidation.vue?vue&type=style&index=0&id=9d763512&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"9d763512\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _vm._m(0)\n}\nvar staticRenderFns = [function (){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"copyright\"},[_c('p',[_vm._v(\" ©Puzala Games Limited, All Rights Reserved \"),_c('a',{staticStyle:{\"color\":\"#ddb463\"},attrs:{\"href\":\"https://www.puzala.com/privacy-policy/\",\"target\":\"_blank\"}},[_vm._v(\"Privacy Policy\")]),_vm._v(\" , \"),_c('a',{staticStyle:{\"color\":\"#ddb463\"},attrs:{\"href\":\"https://www.puzala.com/terms-of-service/\",\"target\":\"_blank\"}},[_vm._v(\"Terms and Conditions\")]),_vm._v(\". \")])])\n}]\n\nexport { render, staticRenderFns }", "<template>\n  <div class=\"copyright\">\n    <p>\n      ©Puzala Games Limited, All Rights Reserved\n      <a href=\"https://www.puzala.com/privacy-policy/\" target=\"_blank\" style=\"color:#ddb463\">Privacy Policy</a>\n      ,\n      <a href=\"https://www.puzala.com/terms-of-service/\" target=\"_blank\" style=\"color:#ddb463\">Terms and Conditions</a>.\n    </p>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'CommonFooterPuzala'\n}\n</script>\n\n<style scoped lang=\"scss\">\n.copyright{\n  height: 40PX;\n  font-size: 13PX;\n  line-height: 40PX;\n  text-align: center;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #c5c5c5;;\n  background-color: black;\n}\n</style>\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./CommonFooterPuzala.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./CommonFooterPuzala.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./CommonFooterPuzala.vue?vue&type=template&id=12bb518e&scoped=true\"\nimport script from \"./CommonFooterPuzala.vue?vue&type=script&lang=js\"\nexport * from \"./CommonFooterPuzala.vue?vue&type=script&lang=js\"\nimport style0 from \"./CommonFooterPuzala.vue?vue&type=style&index=0&id=12bb518e&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"12bb518e\",\n  null\n  \n)\n\nexport default component.exports", "module.exports = \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAUIAAABQCAMAAABbGoyKAAAAmVBMVEUAAAD/WwD/WgD/XAD/WgD/YgD/WgD/WgD/YwD/WgD/WwD/WwD/WgD/XAD/XAD/WgD/WwD/WwD/WwD/YwD/XAD/WgD/WwD/WgD/XQD/WwD/YAD/ZQD/fgD/WwD/WwD/WgD/WgD/WwD/WwD/WgD/WwD/WwD/WwD/XAD/WgD/XQD/YAD/XgD/WgD/WwD/WgD/WgD/WwD/WgD/WgD5tTXEAAAAMnRSTlMAQ9s6kxGFxwhSnLW8aCz35qhGF2yCcNgoPxgUBHha7K5NVsGOYH4y8iELHaLUz+GLsgjPdXUAAAaSSURBVHja7dzpdppAGIDhD8EFNaDGElEQDa64f/d/cbUVZ3Eo68hJDzz/ohXN24EBJkcoZKo1TO9mQy2vbw//0jpQy8XGJw1qeQQNJOp9OZc1Ujeo5dBFyoNaDgtkQK1OmEKd8OepExZ2Q0Z9cp1HAxlbqOW7NqmvTwowLsj5gFpKxtq6cxcmvmhfrbsPxYFanEkbk3jfUF379fV460Vz4Y+liSl0oaJsH3nioW7qYSrVHIcdDWPt4e6I6XgBVE+QdIxzAMDZYUpLqBynj/E8uDtgWhZUzgbrhMXsTcQ0O7KHKY2galaYaAJ3GqZzqdzpdZrRNYa75qUehNG2SJ370Tbwh+1hChu6ZSXKAR4O4c97YDjhgyp5RA0fsYFlh48yL6RsY9oRD1fk7QWT8KkZMJrhg8brduYb7djdrNXIpbiGAfE+h5jkxGy6HTvZjPHhF/cO+PAl3lRTgDF4vVHZRN5Z0x1gtYQbIsJKmg6MET4MuX6tAZ1jjzbdNv2QiWaKfrfWhHPExVi/W04A3pXQT0goGLhNuQk7V+S1VX4DaDqQ2uGELHMJAtkJUUlIKLqoMhNOv8R97uUOfiPHEZRkKCGhnzkhmmN5CT/Fgnh9+UXbkAW3QaOMhKgkJhTp0hIuUKS+fMZF/uUnp5SEfo6EpiEp4QhFHjyQmaErdxFUfkJUsifEnqSE9Pc9XdejudszSbGAHtB+fEI/OaHZ6/X8ATJUKQkDkxxenfAdx4PwBXt6CfKuhH6PpedPiEpiwsbjJ5eedR2lJFRRfH2z8/Icfr8r4QQ4BRL6KRMCTMnrBlIS6vhwBtEy5x9aakgNykqIStqEoNDTBRkJxzEr5mvmrbL4RmpYWkI/dULokb2reEKaaQMiF59mkIVzoicO29ISop0yIW2G4+IJ6c7aj71bCNlsTTqVl5fQT51wjqGNjIQTDK1BMKQHtIzsy2MMuk6JCdFOSCgeaVoSEtJ3NHV41Ucy12QVuH7jrBkAZSb00ybcYMiSkpDurd0AeCcke3lO5SZEOz6huHvNiyUUV5guc+AMmFPQNyW0vhl2zoRHMgzTJZyQX1mVkhBaSLW5CZQ8vALZ2hihnzOhQZLYaRI6PoZ2jpyEnR4ytCb7mUMuAPzghLMrHYbJCRV6yrUACQnFht4SQgbS2TqbaWOlq/vt0vWNUhJ+0mEYk9BrtVqrXgMpXVZCcIbIcoXroGXWhPhUTkKgwzDLza6zzOWnJTe5dZ2X67TtT0/IDMMMCUcyE0Jns8MQmYEtfNr/9ITMMEyfcANSElLNFhPxg5+qOz8+ITMM0ybsgsSE4mq6FzAvxx28LeHaZhj5E9IrBD9dQvMDZCakzw3IlMItgUpNKP/qZAbsMMQ0CYcGvCchHL7Iqie7BPofJIRV6oRmu7UHkJ5QuPZpskugZSf8iEt4jk74aSYl9FzXtdZLlTyRIqEWl/AIkY70PIYugZadUKf3oqgDmXTEhNwwTLxGzpDwV9Ti0TL+utci50zMEmjZCe2o4a9wu4+Y8NOUnpDm2CQ+KN7sX7JLoGUnNOhhT/zg2j8SwuoNCedRh72u8O8j71Rv2SXQshPCLuLlff6CXUz4aUpPSMbRoAlEcHn5dPwvQe4bHZglUKX0hDfxXGCNocO/EsJKfkK4iPPB6vXLT87+FohZ41mdWwItPaFFZrImPOieEEJI+GnKT3jE0HMZyLHo+i5dflqMnMez6wb56NwSaOkJm+RsYGCpTWei9/DJ+ndCWBVL2J1zDvwfS57W2yDYzs/kAZtbifEWV6s1vCAx4pZAS05IY4i8ICbh1MyfULRkD8GiNrvEJPL5JdDyE049jGZBTEJYyU+omhhNYU8fRCq/BFp+QhhhpAXEJpya0hOCi5F+sWffIpdfAi05YcwnPzfjE8JKfkI4xv9ffmGErrgEWn5CmIt70C2AhIRTU2LCmHF2BYLcrBbXTnb5E+5lJIRtHzkDCyApIazekBCWJ+R86dyA6ZrIOdvw4JHZHLKa49MkR0JKWXh0AnQDSJFwakpMSM37ZLtmf+0Ab+bSxmaPvu7CLDJks6cvPUA0Q33oQDxH/XC1bsvSZyDaqg8Ov+UQs42HLfzDQY3UBFZnNN5o2mY8CiDKTLdamvZrrHSi/9z31NUyuJlINKHKLCyuAZV2MLGwK1TbEYvaTaHa9jssaANVp2Mx/cp9E4PsGeVc7ek4NC+wLy+q+OVSEQ5DzKehQy10sPpfGcfi4KzV3/d49xt/6ktmK7nAFAAAAABJRU5ErkJggg==\"", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./RiskControlPolicy.vue?vue&type=style&index=0&id=beffea3e&prod&scoped=true&lang=scss\"", "module.exports = __webpack_public_path__ + \"static/**********/img/login_reward_0.dc0990f2.png\";", "module.exports = __webpack_public_path__ + \"static/**********/img/boon-install-award.c5699e31.png\";", "export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./BoonPop.vue?vue&type=style&index=0&id=7295710c&prod&scoped=true&lang=scss\"", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./LoginValidation.vue?vue&type=style&index=0&id=9d763512&prod&scoped=true&lang=scss\"", "var map = {\n\t\"./login_reward_0.png\": \"2662\",\n\t\"./login_reward_1.png\": \"398c\",\n\t\"./login_reward_2.png\": \"7ef4\",\n\t\"./login_reward_3.png\": \"4790\"\n};\n\n\nfunction webpackContext(req) {\n\tvar id = webpackContextResolve(req);\n\treturn __webpack_require__(id);\n}\nfunction webpackContextResolve(req) {\n\tif(!__webpack_require__.o(map, req)) {\n\t\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\t\te.code = 'MODULE_NOT_FOUND';\n\t\tthrow e;\n\t}\n\treturn map[req];\n}\nwebpackContext.keys = function webpackContextKeys() {\n\treturn Object.keys(map);\n};\nwebpackContext.resolve = webpackContextResolve;\nmodule.exports = webpackContext;\nwebpackContext.id = \"31ca\";", "module.exports = __webpack_public_path__ + \"static/**********/img/install_reward_0.f9a9f8bf.png\";", "module.exports = __webpack_public_path__ + \"static/**********/img/login_reward_1.dea43e02.png\";", "export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./PosterSwiper.vue?vue&type=style&index=0&id=f793779a&prod&scoped=true&lang=scss\"", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('container',{staticClass:\"arrears-reminder-wrapper\",class:[_vm.$i18n.locale, _vm.$gameName],attrs:{\"title\":_vm.$t('text_tips'),\"hide-close\":true},scopedSlots:_vm._u([{key:\"footerBtn\",fn:function(){return [_c('div',{staticClass:\"custom-btn btn-ok\",on:{\"click\":_vm.close}},[_vm._v(_vm._s(_vm.$t('confirm-btn')))])]},proxy:true}])},[_c('div',{staticClass:\"desc\"},[_vm._v(_vm._s(_vm.$t('settings_terms_of_service_agreement_description')))]),_c('div',{staticClass:\"private\"},[_c('span',{on:{\"click\":function($event){return _vm.go(0)}}},[_vm._v(\"《\"+_vm._s(_vm.$t('agreement'))+\"》\")]),_c('span',{on:{\"click\":function($event){return _vm.go(1)}}},[_vm._v(\"《\"+_vm._s(_vm.$t('privacy'))+\"》\")])])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <container :title=\"$t('text_tips')\" :hide-close=\"true\" class=\"arrears-reminder-wrapper\" :class=\"[$i18n.locale, $gameName]\">\n    <div class=\"desc\">{{ $t('settings_terms_of_service_agreement_description') }}</div>\n    <div class=\"private\">\n      <span @click=\"go(0)\">《{{ $t('agreement') }}》</span>\n      <span @click=\"go(1)\">《{{ $t('privacy') }}》</span>\n    </div>\n    <template #footerBtn>\n      <div class=\"custom-btn btn-ok\" @click=\"close\">{{ $t('confirm-btn') }}</div>\n    </template>\n  </container>\n</template>\n\n<script>\nimport Container from '@/components/pop/container'\nimport { StorageUtils } from '@/utils/storageUtils'\nimport { getAmeDo } from '@/server'\n\nexport default {\n  name: 'PrivacyPolicy',\n  props: {\n    option: Object\n  },\n  components: { Container },\n  methods: {\n    go (seq) {\n      const country = (this.$store.state.country || '').toLowerCase()\n      const global = ['https://funplus.com/terms-conditions', 'https://funplus.com/privacy-policy/']\n      const special = {\n        // tw: ['https://privacy.sosgame.tw/terms-conditions.html', 'https://privacy.sosgame.tw/privacy-policy.html'],\n        kr: ['https://funplus.com/terms-conditions-en-as/kr/', 'https://funplus.com/privacy-policy-en-as/kr/'],\n        jp: ['https://funplus.com/terms-conditions-en-as/ja/', 'https://funplus.com/privacy-policy-en-as/ja/ ']\n      }\n\n      let finalUrl = global[seq]\n      if (special[country]) finalUrl = special[country][seq]\n\n      if (this.$store.getters['gameinfo/isPuzalaGame']) {\n        finalUrl = ['https://www.puzala.com/terms-of-service', 'https://www.puzala.com/privacy-policy'][seq]\n      }\n\n      window.open(finalUrl, '_blank')\n    },\n    close () {\n      StorageUtils.setLocalStorage('confirmPrivacyPolicy', 1)\n      this.$store.commit('setPrivacyPolicyStatus', true)\n      const params = {\n        p0: 'web',\n        p1: 7,\n        p2: '1096',\n        silence: true\n      }\n      getAmeDo(params)\n\n      this.$root.$emit('closePop')\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n@use \"~@/utils/utils.scss\" as utils;\n\n.arrears-reminder-wrapper {\n  border-radius: 20px;\n  background-color: #383838;\n  padding-top: 30px!important;\n\n  .desc{\n    text-align: left;\n    font-size: 18px;\n    font-family: PingFangSC-Regular, PingFang SC;\n    font-weight: 400;\n    color: #FFFFFF;\n    line-height: 25px;\n  }\n\n  .private{\n    margin-top: 20px;\n    text-align: left;\n    font-size: 16px;\n    font-family: PingFangSC-Regular, PingFang SC;\n    font-weight: 400;\n    color: #FF5E0F;\n    line-height: 24px;\n    position: relative;\n    left: -8px;\n\n    span{\n      cursor: pointer;\n      position: relative;\n\n      &:after{\n        content: '';\n        height: 2px;\n        width: calc(100% - 30px);\n        position: absolute;\n        bottom: 0;\n        left: 50%;\n        transform: translateX(-50%);\n        background-color: #FF5E0F;\n      }\n    }\n  }\n\n  ::v-deep{\n    .content{\n      margin-top: 20px;\n    }\n\n    .footer-wrapper{\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin-top: 30px;\n\n      .custom-btn{\n        min-width: 200px;\n        border-radius: 10px;\n        font-size: 20px;\n        padding: 11px 0;\n        line-height: 28px;\n        cursor: pointer;\n\n        &.btn-ok{\n          margin-left: 32px;\n          background: #FE6917;\n          color: #FFFFFF;\n        }\n      }\n    }\n  }\n\n  @include utils.setPcContent{\n    padding-bottom: 30PX;\n\n    .desc{\n      font-size: 18PX;\n      line-height: 25PX;\n    }\n    .private{\n      margin-top: 20PX;\n      font-size: 16PX;\n      line-height: 24PX;\n      left: -8PX;\n\n      span{\n        &:after{\n          height: 1PX;\n          width: calc(100% - 20PX);\n        }\n      }\n    }\n\n    ::v-deep{\n      .content{\n        margin-top: 20PX;\n      }\n      .footer-wrapper{\n        margin-top: 30PX;\n\n        .custom-btn{\n          min-width: 200PX;\n          border-radius: 10PX;\n          font-size: 20PX;\n          padding: 11PX 0;\n          line-height: 28PX;\n\n          &.btn-ok{\n            margin-left: 32PX;\n          }\n        }\n      }\n    }\n  }\n\n  &.ar{\n    .desc, .debt{\n      text-align: right;\n      direction: rtl;\n    }\n  }\n}\n\n.arrears-reminder-wrapper.dc{\n  border-radius: 0;\n  .desc{\n    @extend .dc-stroke\n  }\n  ::v-deep{\n    .content{\n      margin-top: 20px;\n    }\n\n    .footer-wrapper{\n      .custom-btn{\n        &.btn-ok{\n          @extend .dc-stroke;\n          @extend .dc-btn-decoration;\n          border-radius: 0;\n        }\n      }\n    }\n  }\n}\n</style>\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./PrivacyPolicy.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./PrivacyPolicy.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./PrivacyPolicy.vue?vue&type=template&id=633825b6&scoped=true\"\nimport script from \"./PrivacyPolicy.vue?vue&type=script&lang=js\"\nexport * from \"./PrivacyPolicy.vue?vue&type=script&lang=js\"\nimport style0 from \"./PrivacyPolicy.vue?vue&type=style&index=0&id=633825b6&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"633825b6\",\n  null\n  \n)\n\nexport default component.exports", "export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./CommonFooter.vue?vue&type=style&index=0&id=5f93b707&prod&scoped=true&lang=scss\"", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"checkbox\"},[_c('label',[_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.isCheck),expression:\"isCheck\"}],attrs:{\"type\":\"checkbox\"},domProps:{\"checked\":Array.isArray(_vm.isCheck)?_vm._i(_vm.isCheck,null)>-1:(_vm.isCheck)},on:{\"change\":[function($event){var $$a=_vm.isCheck,$$el=$event.target,$$c=$$el.checked?(true):(false);if(Array.isArray($$a)){var $$v=null,$$i=_vm._i($$a,$$v);if($$el.checked){$$i<0&&(_vm.isCheck=$$a.concat([$$v]))}else{$$i>-1&&(_vm.isCheck=$$a.slice(0,$$i).concat($$a.slice($$i+1)))}}else{_vm.isCheck=$$c}},function($event){return _vm.notifyServer('check')}]}}),_vm._v(\" Ich habe \"),_c('a',{attrs:{\"href\":\"https://funplus.com/terms-conditions/#section-13\",\"target\":\"_blank\"}},[_vm._v(\"Rückerstattungsrichtlinie\")]),_vm._v(\" gelesen und stimme zu. \")])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<script>\nimport { getAmeDo } from '@/server'\n\nexport default {\n  name: 'privatePermission',\n  computed: {\n    calcPop () {\n      return !this.isCheck || !this.isPop\n    }\n  },\n  watch: {\n    calcPop (val) {\n      window.__needDEPop = val\n    }\n  },\n  data () {\n    return {\n      isCheck: false,\n      isPop: false\n    }\n  },\n  methods: {\n    initState () {\n      const params = {\n        p0: 'web',\n        p1: 9,\n        p2: 2531,\n        p3: 'api',\n        game: this.$store.state.gameinfo.gameProject.split('_')[0]\n      }\n      getAmeDo(params)\n        .then(res => {\n          const { data, code } = res\n          if (code === 0) {\n            window.__needDEPop = true\n\n            this.isCheck = data.check\n            this.isPop = data.popup\n          }\n        })\n    },\n    notifyServer (type) {\n      const params = {\n        p0: 'web',\n        p1: 9,\n        p2: 2532,\n        p3: 'api',\n        game: this.$store.state.gameinfo.gameProject.split('_')[0]\n      }\n      if (type === 'check') {\n        params.set_type = 0\n        params.set_status = Number(this.isCheck)\n      }\n      if (type === 'pop') {\n        params.set_type = 1\n        params.set_status = 1\n\n        // 1选中 0 不选中\n        this.isCheck = true\n      }\n\n      getAmeDo(params)\n        .then(res => {\n          const { code } = res\n          if (code !== 0) {\n            if (type === 'pop') this.isPop = false\n            if (type === 'check') this.isCheck = !this.isCheck\n          }\n        })\n    }\n  },\n  created () {\n    this.initState()\n    this.$root.$on('changeDePopPrivacy', () => this.notifyServer('pop'))\n  }\n}\n</script>\n\n<template>\n  <div class=\"checkbox\">\n    <label>\n      <input @change=\"notifyServer('check')\" v-model=\"isCheck\" type=\"checkbox\">\n      Ich habe\n      <a href=\"https://funplus.com/terms-conditions/#section-13\" target=\"_blank\">Rückerstattungsrichtlinie</a>\n      gelesen und stimme zu.\n    </label>\n  </div>\n</template>\n\n<style scoped lang=\"scss\">\n@use \"~@/utils/utils\" as utils;\n\n.checkbox{\n  margin-bottom: 30px;\n  label{\n    text-align: left;\n    font-family: PingFang-SC, PingFang-SC;\n    font-weight: 500;\n    font-size: 22px;\n    color: #FFFFFF;\n    font-style: normal;\n    display: flex;\n\n    a{\n      display: inline;\n      color: #FEB522;\n      text-decoration: underline;\n      margin: 0 3px;\n    }\n\n    input{\n      appearance: none;\n      -webkit-appearance:none;\n\n      width: 30px;\n      height: 30px;\n      background: white;\n      border-radius: 4px;\n      margin-right: 8px;\n      flex-shrink: 0;\n      border: 1px solid #666666;\n\n      &:checked{\n        @include utils.bgCenterForCommon('privacy/privacyPermissionCheckBg.png', 30px, 30px);\n      }\n    }\n  }\n}\n\n@include utils.setPcContent{\n  .checkbox{\n    cursor: pointer;\n    margin-bottom: 20px;\n    label{\n      font-size: 18px;\n      line-height: 23px;\n      cursor: pointer;\n\n      input{\n        width: 20px;\n        height: 20px;\n        border-radius: 3px;\n        margin-right: 5px;\n\n        &:checked{\n          @include utils.bgCenterForCommon('privacy/privacyPermissionCheckBg.png', 20px, 20px);\n        }\n      }\n    }\n  }\n}\n</style>\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./privatePermission.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./privatePermission.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./privatePermission.vue?vue&type=template&id=6cb8a3e6&scoped=true\"\nimport script from \"./privatePermission.vue?vue&type=script&lang=js\"\nexport * from \"./privatePermission.vue?vue&type=script&lang=js\"\nimport style0 from \"./privatePermission.vue?vue&type=style&index=0&id=6cb8a3e6&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"6cb8a3e6\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _vm._m(0)\n}\nvar staticRenderFns = [function (){var _vm=this,_c=_vm._self._c;return _c('div',[_c('a',{attrs:{\"href\":\"https://funplus.com/terms-conditions/#section-13\",\"target\":\"_blank\"}},[_vm._v(\"Refund Policy\")]),_vm._v(\". \")])\n}]\n\nexport { render, staticRenderFns }", "<template>\n  <div>\n    <a href=\"https://funplus.com/terms-conditions/#section-13\" target=\"_blank\">Refund Policy</a>.\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'RefundPolicy'\n}\n</script>\n\n<style scoped lang=\"scss\">\ndiv{\n  width: calc(100% - 80px);\n  margin: 24px auto 0;\n  display: flex;\n\n  a{\n    height: 32px;\n    line-height: 32px;\n    border-radius: 4px;\n    background: rgba(255,255,255, .1);\n    display: inline-block;\n    white-space: nowrap;\n    padding: 0 10px;\n\n    font-size: 18px;\n    font-family: PingFangSC-Regular, PingFang SC;\n    font-weight: 400;\n    color: rgba(255,255,255, .5);\n  }\n}\n</style>\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./RefundPolicy.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./RefundPolicy.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./RefundPolicy.vue?vue&type=template&id=6d6cc831&scoped=true\"\nimport script from \"./RefundPolicy.vue?vue&type=script&lang=js\"\nexport * from \"./RefundPolicy.vue?vue&type=script&lang=js\"\nimport style0 from \"./RefundPolicy.vue?vue&type=style&index=0&id=6d6cc831&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"6d6cc831\",\n  null\n  \n)\n\nexport default component.exports", "module.exports = __webpack_public_path__ + \"static/**********/img/login_reward_3.9b45a0c6.png\";", "var map = {\n\t\"./aof/boon/install_reward_0.png\": \"376d\",\n\t\"./aof/boon/install_reward_1.png\": \"73b0\",\n\t\"./aof/boon/install_reward_2.png\": \"f1d7\",\n\t\"./aof/boon/install_reward_3.png\": \"7d47\"\n};\n\n\nfunction webpackContext(req) {\n\tvar id = webpackContextResolve(req);\n\treturn __webpack_require__(id);\n}\nfunction webpackContextResolve(req) {\n\tif(!__webpack_require__.o(map, req)) {\n\t\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\t\te.code = 'MODULE_NOT_FOUND';\n\t\tthrow e;\n\t}\n\treturn map[req];\n}\nwebpackContext.keys = function webpackContextKeys() {\n\treturn Object.keys(map);\n};\nwebpackContext.resolve = webpackContextResolve;\nmodule.exports = webpackContext;\nwebpackContext.id = \"4976\";", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('container',{staticClass:\"arrears-reminder-wrapper\",class:[_vm.$i18n.locale],attrs:{\"title\":_vm.$t('boon-page-title')}},[_c('div',{staticClass:\"divider\"}),_c('div',{staticClass:\"tab-wrapper\"},_vm._l((_vm.tabList),function(item,index){return _c('div',{key:index,staticClass:\"tab\",class:[\n    `tab-${index}`,\n    _vm.chosenIndex === index ? 'chosen-active' : '',\n    item.title === 'boon-ss-install-title' && !_vm.gotInstallReward ? 'dot-active' : '',\n    item.title === 'boon-ss-login-title' && !_vm.gotLoginReward ? 'dot-active' : ''\n    ],on:{\"click\":function($event){_vm.chosenIndex = index}}},[_c('span',[_vm._v(_vm._s(_vm.$t(item.title)))])])}),0),_c('Swiper',{staticClass:\"my-swiper-wrapper\",attrs:{\"options\":_vm.swiperOptions}},[_c('SwiperSlide',{key:\"install\"},[_c('div',{staticClass:\"charge-desc\"},[_c('div',{staticClass:\"row-1\"},[_vm._v(_vm._s(_vm.$t('boon-task-2-title')))]),_c('div',{staticClass:\"row-2\"},[_vm._v(_vm._s(_vm.$t('boon-p1-title')))])]),_c('div',{staticClass:\"gift-image\"},[_c('img',{attrs:{\"src\":require(`@/assets/ss/${_vm.$gameName === 'ss' ? 'boon' : _vm.$gameName}/boon-install-award.png`),\"alt\":\"\"}})]),_c('div',{staticClass:\"login-reward-btn action-btn\"},[(!_vm.isLogin)?[_c('span',{staticClass:\"click-btn\",on:{\"click\":_vm.focusInput}},[_vm._v(_vm._s(_vm.$t('boon-login')))])]:(!_vm.hadInstall)?[(!_vm.calcShowInstall)?_c('p',{staticClass:\"browser-forbidden\"},[_vm._v(_vm._s(_vm.$t('boon-browser-forbidden')))]):_c('span',{staticClass:\"click-btn\",on:{\"click\":_vm.install}},[_vm._v(_vm._s(_vm.$t('boon-task-2-add')))])]:[(_vm.gotInstallReward)?_c('span',{staticClass:\"forbidden\"}):_c('span',{staticClass:\"todo click-btn\",on:{\"click\":function($event){return _vm.getReward(_vm.getPwaReward)}}},[_vm._v(_vm._s(_vm.$t('boon-gain')))])]],2)]),_c('SwiperSlide',{key:\"login\"},[_c('div',{staticClass:\"charge-desc\"},[_c('div',{staticClass:\"row-1\"},[_vm._v(_vm._s(_vm.$t('boon-task-1-title')))]),_c('div',{staticClass:\"row-2\"},[_vm._v(_vm._s(_vm.$t('boon-p1-title')))])]),_c('div',{staticClass:\"gift-image\"},[_c('img',{attrs:{\"src\":require(`@/assets/ss/${_vm.$gameName === 'ss' ? 'boon' : _vm.$gameName}/boon-login-award.png`),\"alt\":\"\"}})]),_c('div',{staticClass:\"login-reward-btn action-btn\"},[(!_vm.isLogin)?_c('span',{staticClass:\"click-btn\",on:{\"click\":_vm.focusInput}},[_vm._v(_vm._s(_vm.$t('boon-login')))]):(!_vm.hadLogin)?_c('span',{staticClass:\"click-btn\",on:{\"click\":_vm.focusInput}},[_vm._v(_vm._s(_vm.$t('boon-go-charge-short')))]):[(_vm.gotLoginReward)?_c('span',{staticClass:\"forbidden\"}):_c('span',{staticClass:\"todo click-btn\",on:{\"click\":function($event){return _vm.getReward(_vm.getLoginReward)}}},[_vm._v(_vm._s(_vm.$t('boon-gain')))])]],2)])],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <container :title=\"$t('boon-page-title') \" class=\"arrears-reminder-wrapper\" :class=\"[$i18n.locale]\">\n    <div class=\"divider\"></div>\n    <div class=\"tab-wrapper\">\n      <div :key=\"index\" v-for=\"(item, index) in tabList\" class=\"tab\"\n      :class=\"[\n      `tab-${index}`,\n      chosenIndex === index ? 'chosen-active' : '',\n      item.title === 'boon-ss-install-title' && !gotInstallReward ? 'dot-active' : '',\n      item.title === 'boon-ss-login-title' && !gotLoginReward ? 'dot-active' : ''\n      ]\"\n        @click=\"chosenIndex = index\">\n        <span>{{ $t(item.title) }}</span>\n      </div>\n    </div>\n    <Swiper class=\"my-swiper-wrapper\" :options=\"swiperOptions\">\n      <!--快捷-->\n      <SwiperSlide key=\"install\">\n        <div class=\"charge-desc\">\n          <div class=\"row-1\">{{ $t('boon-task-2-title') }}</div>\n          <div class=\"row-2\">{{ $t('boon-p1-title') }}</div>\n        </div>\n        <div class=\"gift-image\">\n          <img :src=\"require(`@/assets/ss/${$gameName === 'ss' ? 'boon' : $gameName}/boon-install-award.png`)\" alt=\"\">\n        </div>\n        <div class=\"login-reward-btn action-btn\">\n          <template v-if=\"!isLogin\">\n            <span class=\"click-btn\" @click=\"focusInput\">{{ $t('boon-login') }}</span>\n          </template>\n          <template v-else-if=\"!hadInstall\">\n            <p class=\"browser-forbidden\" v-if=\"!calcShowInstall\">{{ $t('boon-browser-forbidden') }}</p>\n            <span v-else class=\"click-btn\" @click=\"install\">{{ $t('boon-task-2-add') }}</span>\n          </template>\n          <template v-else>\n            <span class=\"forbidden\" v-if=\"gotInstallReward\"></span>\n            <span class=\"todo click-btn\" v-else @click=\"getReward(getPwaReward)\">{{ $t('boon-gain') }}</span>\n          </template>\n        </div>\n      </SwiperSlide>\n\n      <!--登录-->\n      <SwiperSlide key=\"login\">\n        <div class=\"charge-desc\">\n          <div class=\"row-1\">{{ $t('boon-task-1-title') }}</div>\n          <div class=\"row-2\">{{ $t('boon-p1-title') }}</div>\n        </div>\n        <div class=\"gift-image\">\n          <img :src=\"require(`@/assets/ss/${$gameName === 'ss' ? 'boon' : $gameName}/boon-login-award.png`)\" alt=\"\">\n        </div>\n        <div class=\"login-reward-btn action-btn\">\n          <span v-if=\"!isLogin\" class=\"click-btn\" @click=\"focusInput\">{{ $t('boon-login') }}</span>\n          <span v-else-if=\"!hadLogin\" class=\"click-btn\" @click=\"focusInput\">{{ $t('boon-go-charge-short') }}</span>\n          <template v-else>\n            <span class=\"forbidden\" v-if=\"gotLoginReward\"></span>\n            <span class=\"todo click-btn\" v-else @click=\"getReward(getLoginReward)\">{{ $t('boon-gain') }}</span>\n          </template>\n        </div>\n      </SwiperSlide>\n    </Swiper>\n  </container>\n</template>\n\n<script>\nimport Container from '@/components/pop/container'\nimport { getAmeDo, ameHoldByGet } from '@/server'\nimport { Swiper, SwiperSlide } from 'vue-awesome-swiper'\nimport 'swiper/css/swiper.min.css'\n\nimport UAParser from 'ua-parser-js'\nimport { mapState } from 'vuex'\nconst { projectId, loginAction, getLoginReward, pwaOpenAction, getPwaReward } = window.$gcbk('apiParams.boonAme')\n\nconst ameParams = { p0: 'web', p1: projectId }\nfunction displayMode () {\n  const isStandalone = window.matchMedia('(display-mode: standalone)').matches\n  if (document.referrer.startsWith('android-app://')) {\n    return 'twa'\n  } else if (navigator.standalone || isStandalone) {\n    return 'standalone'\n  }\n  return 'browser'\n}\n\nexport default {\n  name: 'BoonPop',\n  components: { Container, Swiper, SwiperSlide },\n  data () {\n    const instance = this\n    return {\n      hadLogin: false, // 1097\n      gotLoginReward: false, // 1098\n      hadInstall: false, // 1099\n      gotInstallReward: false, // 1100\n      deferredPrompt: window.__deferredPrompt || undefined,\n      showMobileSafariGuide: false,\n\n      progressPercent: 0,\n\n      chosenIndex: 0,\n      swiperInstance: undefined,\n      swiperOptions: {\n        autoplay: false,\n        on: {\n          slideChangeTransitionStart: function () {\n            instance.chosenIndex = this.activeIndex\n          },\n          init: function () {\n            // Swiper初始化了\n            instance.swiperInstance = this\n          }\n        }\n      },\n      getLoginReward,\n      getPwaReward,\n      tabList: [\n        { title: 'boon-ss-install-title' },\n        { title: 'boon-ss-login-title' }\n      ],\n    }\n  },\n  methods: {\n    onClose () {\n      this.$root.$emit('closePop')\n    },\n    showInstallPart () {\n      window.addEventListener('beforeinstallprompt', (e) => {\n        // 防止 Chrome 67 及更早版本自动显示安装提示\n        e.preventDefault()\n        // 稍后再触发此事件\n        this.deferredPrompt = e\n      })\n    },\n    resetStatus () {\n      const params = {\n        p0: 'web',\n        p1: projectId,\n        p2: `${loginAction},${getLoginReward},${pwaOpenAction},${getPwaReward}`\n      }\n      this.$loading.show()\n      ameHoldByGet(params)\n        .then(res => {\n          const { data, code } = res\n          if (code === 0) {\n            const result = {}\n            for (const value of Object.values(data)) result[value.task_id] = value\n\n            this.hadLogin = loginAction in result\n            this.gotLoginReward = getLoginReward in result\n            this.hadInstall = pwaOpenAction in result\n            this.gotInstallReward = getPwaReward in result\n          }\n        })\n        .finally(() => this.$loading.hide())\n    },\n    // 领取奖励\n    getReward (taskId) {\n      const params = { p2: taskId }\n      this.$loading.show()\n      getAmeDo({ ...params, ...ameParams })\n        .then(res => {\n          const { code, data = [] } = res\n          if (code === 0 && data.length) {\n            if (taskId === getPwaReward) this.gotInstallReward = true\n            if (taskId === getLoginReward) this.gotLoginReward = true\n          }\n        })\n        .finally(() => this.$loading.hide())\n    },\n    focusInput () {\n      this.$root.$emit('closePop')\n      this.$root.$emit('ClickPayButNotLogin')\n      // const input = document.querySelector('#uidInput')\n      // setTimeout(() => {\n      //   input.focus()\n      // }, 100)\n    },\n    install () {\n      this.$root.$emit('closePop')\n      // 如果是谷歌浏览器\n      if (this.deferredPrompt) {\n        this.deferredPrompt.prompt()\n        // 等待用户反馈\n        this.deferredPrompt.userChoice\n          .then((choiceResult) => {\n            if (choiceResult.outcome === 'accepted') {\n              console.log('User accepted the A2HS prompt')\n\n              const displayModeCheck = setInterval(() => {\n                if (displayMode() === 'standalone') {\n                  clearInterval(displayModeCheck)\n                  this.$root.$emit('installSuccessful')\n                }\n              }, 1000)\n            } else {\n              console.log('User dismissed the A2HS prompt')\n            }\n            this.deferredPrompt = undefined\n          })\n      } else {\n        setTimeout(() => {\n          this.$root.$emit('mobileSafariGuide')\n        }, 500)\n      }\n    }\n  },\n  computed: {\n    ...mapState('userinfo', ['isLogin']),\n    ...mapState(['userinfo']),\n    calcShowInstall () {\n      const parser = new UAParser(navigator.userAgent)\n      const result = parser.getResult()\n      const { browser } = result\n\n      const whiteBrowser = browser.name === 'Mobile Safari' || ((browser.name || '').includes('Chrome') && this.deferredPrompt)\n      if (!this.isLogin) return true\n      if (!this.hadInstall) return whiteBrowser\n      return true\n    }\n  },\n  created () {\n    this.$root.$on('loginSuccess', () => {\n      setTimeout(() => this.resetStatus(), 2000)\n    })\n    if (this.isLogin) {\n      this.resetStatus()\n    } else {\n    }\n\n    this.$watch('chosenIndex', (index) => {\n      this.swiperInstance.slideTo(index, 200, false)\n    })\n\n    this.showInstallPart()\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n@use \"~@/utils/utils.scss\" as utils;\n\n.arrears-reminder-wrapper {\n  border-radius: 15px;\n  background-color: #383838;\n  padding-top: 18px!important;\n  background-image: url(\"~@/assets/ss/boon/boon-pop-bg_m.png\");\n  background-position: center center;\n  background-repeat: no-repeat;\n  background-size: cover;\n  border: 1px solid #848484;\n\n  .divider{\n    width: 100%;\n    height: 2px;\n    background: #D8D8D8;\n    opacity: 0.2;\n    margin: 18px auto 0;\n  }\n\n  .tab-wrapper{\n    display: flex;\n    align-items: center;\n    justify-content: center;\n\n    font-size: 24px;\n    font-family: PingFangSC-Regular, PingFang SC;\n    font-weight: 400;\n    color: #AEAEAE;\n    padding-top: 16px;\n    line-height: 1.1;\n\n    .tab{\n      flex-grow: 1;\n      width: 0;\n      min-height: 33px;\n      padding: 3px 20px;\n      align-items: center;\n      justify-content: center;\n      display: inline-block;\n      cursor: pointer;\n\n      word-break: break-all;\n    }\n\n    .dot-active{\n      span{\n        display: inline-block;\n        position: relative;\n\n        &:after{\n          display: inline-block;\n          content: ' ';\n          width: 10px;\n          height: 10px;\n          background: #D20202;\n          border-radius: 50%;\n          position: absolute;\n          top: 0;\n          right: 0;\n          transform: translate(100%, -100%);\n        }\n      }\n    }\n\n    .chosen-active{\n      position: relative;\n      color: white;\n      font-weight: bold;\n\n      &:before{\n        display: inline-block;\n        content: ' ';\n        width: 70px;\n        height: 4px;\n        background: #FF5E0F;\n        border-radius: 2px;\n        position: absolute;\n        bottom: 0;\n        left: 50%;\n        transform: translate(-50%, 100%);\n      }\n    }\n  }\n\n  .my-swiper-wrapper{\n    .charge-desc{\n      font-size: 40px;\n      font-family: PingFangSC-Semibold, PingFang SC;\n      font-weight: 600;\n      color: #FF5E0F;\n      line-height: 56px;\n      padding-top: 48px;\n\n      .row-2{\n        color: white;\n      }\n    }\n\n    .gift-image{\n      margin-top: 106px;\n      padding: 0 30px;\n      img{\n        display: inline-block;\n        width: 100%;\n        height: auto;\n      }\n    }\n  }\n\n  .action-btn{\n    span{\n      margin: 165px auto 0;\n      height: 70px;\n      padding: 0 20px;\n      background-color: #FF5E0F;\n      border-radius: 8px;\n      min-width: 300px;\n\n      font-size: 30px;\n      font-family: PingFangSC-Semibold, PingFang SC;\n      font-weight: 600;\n      color: #FFFFFF;\n      line-height: 70px;\n      display: inline-block;\n\n      &.forbidden{\n        background-color: transparent;\n        @include utils.bgCenterForSSV('boon/boon-award-get.png',349px, 263px);\n        margin-top: 0;\n        position: relative;\n        top: -60px;\n      }\n    }\n\n    .browser-forbidden{\n      font-size: 22px;\n      font-family: PingFangSC-Regular, PingFang SC;\n      font-weight: 400;\n      color: #FF5E0F;\n      line-height: 30px;\n      margin-top: 56px;\n      padding: 0 43px;\n    }\n  }\n\n  ::v-deep{\n    padding-bottom: 14px;\n    .content{\n      margin-top: 0;\n      padding-left: 20px;\n      padding-right: 20px;\n      max-height: none;\n    }\n    .footer-wrapper{\n      display: none;\n    }\n\n    .swiper-slide{\n      max-height: 770px;\n      overflow-y: scroll;\n      min-height: 100%;\n    }\n  }\n}\n\n@include utils.setPcContent{\n  .arrears-reminder-wrapper {\n    border-radius: 15PX;\n    padding-top: 27PX !important;\n    border: 1PX solid #848484;\n\n    .divider {\n      margin-top: 21PX;\n      height: 2PX;\n    }\n\n    .tab-wrapper {\n      font-size: 18PX;\n      line-height: 25PX;\n      padding-top: 14PX;\n\n      .tab{\n        min-height: 25PX;\n        padding: 3PX 20PX;\n      }\n\n      .dot-active{\n        span{\n          &:after{\n            width: 8PX;\n            height: 8PX;\n          }\n        }\n      }\n\n      .chosen-active{\n        color: white;\n        &:before{\n          width: 50PX;\n          height: 4PX;\n          border-radius: 2PX;\n        }\n      }\n    }\n\n    .my-swiper-wrapper{\n      margin-top: 6PX;\n      .charge-desc{\n        font-size: 30PX;\n        line-height: 42PX;\n        padding-top: 50PX;\n      }\n      .gift-image{\n        margin-top: 67PX;\n        padding: 0 120PX;\n      }\n    }\n\n    .action-btn{\n      span{\n        margin-top: 77PX;\n\n        height: 50PX;\n        padding: 0 20PX;\n        background-color: #FF5E0F;\n        border-radius: 8PX;\n        min-width: 200PX;\n        line-height: 50PX;\n        font-size: 20PX;\n        cursor: pointer;\n\n        &.forbidden{\n          @include utils.bgCenterForSSV('boon/boon-award-get.png',243PX, 184PX);\n          top: -50PX;\n        }\n      }\n    }\n\n    .browser-forbidden{\n      font-size: 16PX;\n      font-family: PingFangSC-Regular, PingFang SC;\n      font-weight: 400;\n      color: #FF5E0F;\n      line-height: 22PX;\n      margin-top: 37PX;\n      padding: 0 43PX;\n    }\n\n    ::v-deep{\n      padding-bottom: 14px;\n      .content{\n        margin-top: 0;\n        padding-left: 20PX;\n        padding-right: 20PX;\n      }\n      .footer-wrapper{\n        display: none;\n      }\n\n      .swiper-slide{\n        max-height: 500PX;\n        overflow-y: scroll;\n        height: 100%;\n      }\n\n      .title{\n        font-size: 24PX;\n        line-height: 33PX;\n        i{\n          right: 20PX;\n          height: 26PX;\n          width: 26PX;\n        }\n      }\n    }\n  }\n}\n</style>\n", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./BoonPop.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./BoonPop.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./BoonPop.vue?vue&type=template&id=7295710c&scoped=true\"\nimport script from \"./BoonPop.vue?vue&type=script&lang=js\"\nexport * from \"./BoonPop.vue?vue&type=script&lang=js\"\nimport style0 from \"./BoonPop.vue?vue&type=style&index=0&id=7295710c&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"7295710c\",\n  null\n  \n)\n\nexport default component.exports", "module.exports = __webpack_public_path__ + \"static/**********/img/login_reward_0.dc0990f2.png\";", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./RefundPolicy.vue?vue&type=style&index=0&id=6d6cc831&prod&scoped=true&lang=scss\"", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./CommonFooterPuzala.vue?vue&type=style&index=0&id=12bb518e&prod&scoped=true&lang=scss\"", "module.exports = __webpack_public_path__ + \"static/**********/img/install_reward_3.76a3b0b3.png\";", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('container',{staticClass:\"arrears-reminder-wrapper\",class:[_vm.$i18n.locale, _vm.$gameName],attrs:{\"title\":_vm.$t('text_tips'),\"hide-close\":true,\"id\":\"risk-policy-wrapper\"},scopedSlots:_vm._u([{key:\"footerBtn\",fn:function(){return [_c('div',{staticClass:\"custom-btn btn-ok\",on:{\"click\":_vm.close}},[_vm._v(_vm._s(_vm.$t('confirm-btn')))])]},proxy:true}])},[_c('div',{staticClass:\"desc\"},[(_vm.key === 'always_banned')?_c('div',{domProps:{\"innerHTML\":_vm._s(_vm.$t('risk_policy_forbidden_forever'))}}):_vm._e(),(['banned_adyen', 'banned_pingpong', 'banned_uid'].includes(_vm.key))?_c('div',{domProps:{\"innerHTML\":_vm._s(_vm.$t('risk_policy_forbidden_some', { 0: `<span>${_vm.leaveDate}</span>`}))}}):_vm._e(),(['access_warn', 'use_adyen', 'use_pingpong', 'use_wxpay', 'use_alipay', 'use_paypal', 'access_warn_black_room'].includes(_vm.key))?_c('div',{domProps:{\"innerHTML\":_vm._s(_vm.$t('risk_policy_pay_tip'))}}):_vm._e()])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <container :title=\"$t('text_tips')\" :hide-close=\"true\" class=\"arrears-reminder-wrapper\" id=\"risk-policy-wrapper\" :class=\"[$i18n.locale, $gameName]\">\n    <div class=\"desc\">\n      <div v-if=\"key === 'always_banned'\" v-html=\"$t('risk_policy_forbidden_forever')\"></div>\n      <div v-if=\"['banned_adyen', 'banned_pingpong', 'banned_uid'].includes(key)\" v-html=\"$t('risk_policy_forbidden_some', { 0: `<span>${leaveDate}</span>`})\"></div>\n      <div v-if=\"['access_warn', 'use_adyen', 'use_pingpong', 'use_wxpay', 'use_alipay', 'use_paypal', 'access_warn_black_room'].includes(key)\" v-html=\"$t('risk_policy_pay_tip')\"></div>\n    </div>\n    <template #footerBtn>\n      <div class=\"custom-btn btn-ok\" @click=\"close\">{{ $t('confirm-btn') }}</div>\n    </template>\n  </container>\n</template>\n\n<script>\nimport Container from '@/components/pop/container'\n\nexport default {\n  name: 'RiskControlPolicy',\n  props: {\n    option: {\n      type: Object,\n      default: () => ({})\n    }\n  },\n  data () {\n    const { key, value, cb } = this.option\n    return {\n      key,\n      value,\n      cb,\n\n      interval: '',\n      leaveDate: ''\n    }\n  },\n  components: { Container },\n  methods: {\n    close () {\n      this.$root.$emit('closePop')\n      this.interval && clearInterval(this.interval)\n      this.cb && this.cb()\n    },\n    calcLeaveTime () {\n      const fixDate = p => p < 10 ? `0${Math.floor(p)}` : Math.floor(p)\n      const getLeaveDateTxt = count => `${Math.floor(count / 3600 / 24)}d ${fixDate(count / 3600 % 24)} : ${fixDate((count / 60) % 60)} : ${fixDate(count % 60)}`\n\n      this.leaveDate = getLeaveDateTxt(this.value)\n      setInterval(() => {\n        if (this.value === 0) {\n          this.close()\n          clearInterval(this.interval)\n          return\n        }\n        this.leaveDate = getLeaveDateTxt(--this.value)\n      }, 1000)\n    }\n  },\n  created () {\n    if (['banned_uid', 'banned_adyen', 'banned_pingpong'].includes(this.key)) {\n      this.calcLeaveTime()\n    }\n  },\n  beforeDestroy () {\n    if (this.interval) clearInterval(this.interval)\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n@use \"~@/utils/utils.scss\" as utils;\n\n.arrears-reminder-wrapper {\n  border-radius: 20px;\n  background-color: #383838;\n  padding-top: 30px!important;\n\n  .desc{\n    text-align: left;\n    font-size: 18px;\n    font-family: PingFangSC-Regular, PingFang SC;\n    font-weight: 400;\n    color: #FFFFFF;\n    line-height: 25px;\n\n    ::v-deep{\n      span{\n        color: red;\n        padding-right: 4px;\n        font-weight: bold;\n      }\n    }\n  }\n\n  ::v-deep{\n    .content{\n      margin-top: 20px;\n    }\n\n    .footer-wrapper{\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin-top: 30px;\n\n      .custom-btn{\n        min-width: 200px;\n        border-radius: 10px;\n        font-size: 20px;\n        padding: 11px 0;\n        line-height: 28px;\n        cursor: pointer;\n\n        &.btn-ok{\n          margin-left: 32px;\n          background: #FE6917;\n          color: #FFFFFF;\n        }\n      }\n    }\n  }\n\n  @include utils.setPcContent{\n    padding-bottom: 30PX;\n\n    .desc{\n      font-size: 18PX;\n      line-height: 25PX;\n    }\n\n    ::v-deep{\n      .content{\n        margin-top: 20PX;\n      }\n      .footer-wrapper{\n        margin-top: 30PX;\n\n        .custom-btn{\n          min-width: 200PX;\n          border-radius: 10PX;\n          font-size: 20PX;\n          padding: 11PX 0;\n          line-height: 28PX;\n\n          &.btn-ok{\n            margin-left: 32PX;\n          }\n        }\n      }\n    }\n  }\n\n  &.ar{\n    .desc, .debt{\n      text-align: right;\n      direction: rtl;\n    }\n  }\n}\n\n.arrears-reminder-wrapper.dc{\n  .desc{\n    @extend .dc-stroke\n  }\n  ::v-deep{\n    .content{\n      margin-top: 20px;\n    }\n\n    .footer-wrapper{\n      .custom-btn{\n        &.btn-ok{\n          @extend .dc-stroke;\n          @extend .dc-btn-decoration;\n          border-radius: 0;\n        }\n      }\n    }\n  }\n}\n</style>\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./RiskControlPolicy.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./RiskControlPolicy.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./RiskControlPolicy.vue?vue&type=template&id=beffea3e&scoped=true\"\nimport script from \"./RiskControlPolicy.vue?vue&type=script&lang=js\"\nexport * from \"./RiskControlPolicy.vue?vue&type=script&lang=js\"\nimport style0 from \"./RiskControlPolicy.vue?vue&type=style&index=0&id=beffea3e&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"beffea3e\",\n  null\n  \n)\n\nexport default component.exports", "module.exports = __webpack_public_path__ + \"static/**********/img/icon-police-gongan.228e58cb.png\";", "module.exports = __webpack_public_path__ + \"static/**********/img/install_reward_2.9f56aa98.png\";", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('container',{staticClass:\"arrears-reminder-wrapper\",class:[_vm.$i18n.locale, _vm.$gameName],attrs:{\"title\":_vm.$t('boon-page-title')}},[_c('div',{staticClass:\"divider\"}),_c('div',{staticClass:\"tab-wrapper\",class:{ 'tab-small': ['de', 'sv'].includes(_vm.$i18n.locale) }},_vm._l((_vm.tabList),function(tabItem,tabIndex){return _c('div',{key:tabItem.dataKey,class:['tab', {'dot-active': !(_vm.$data[tabItem.dataKey] || _vm.$store.state.formdata[tabItem.dataKey]), 'chosen-active': _vm.chosenIndex === tabIndex}],on:{\"click\":function($event){_vm.chosenIndex = tabIndex}}},[_c('span',[_vm._v(_vm._s(_vm.$t(tabItem.langKey)))])])}),0),_c('Swiper',{staticClass:\"my-swiper-wrapper\",attrs:{\"options\":_vm.swiperOptions}},[_c('SwiperSlide',{key:\"install\"},[_c('div',{staticClass:\"charge-desc\"},[_c('div',{staticClass:\"row-1\"},[_vm._v(_vm._s(_vm.$t('boon-task-2-title')))]),_c('div',{staticClass:\"row-2\"},[_vm._v(_vm._s(_vm.$t('boon-p1-title')))])]),_c('div',{staticClass:\"gift-image\"},[(_vm.$gameName === 'koa')?_vm._l(([0,1,2,3]),function(i){return _c('img',{key:i,attrs:{\"src\":require(`@/assets/koa/boon/install_reward_${i}.png`),\"alt\":\"\"}})}):_vm._l(([0,1,2,3]),function(i){return _c('img',{key:i,attrs:{\"src\":require(`@/assets/koa/${_vm.$gameName === 'rom' ? 'aof' : _vm.$gameName}/boon/install_reward_${i}.png`),\"alt\":\"\"}})})],2),_c('div',{staticClass:\"login-reward-btn action-btn\"},[(!_vm.isLogin)?[_c('span',{staticClass:\"click-btn\",on:{\"click\":_vm.focusInput}},[_vm._v(_vm._s(_vm.$t('boon-login')))])]:(!_vm.hadInstall)?[(!_vm.calcShowInstall)?_c('p',{staticClass:\"browser-forbidden\"},[_vm._v(_vm._s(_vm.$t('boon-browser-forbidden')))]):_c('span',{staticClass:\"click-btn\",on:{\"click\":_vm.install}},[_vm._v(_vm._s(_vm.$t('boon-task-2-add')))])]:[(_vm.gotInstallReward)?_c('span',{staticClass:\"forbidden\"}):_c('span',{staticClass:\"todo click-btn\",on:{\"click\":function($event){return _vm.getReward(_vm.getPwaReward)}}},[_vm._v(_vm._s(_vm.$t('boon-gain')))])]],2)]),_c('SwiperSlide',{key:\"login\"},[_c('div',{staticClass:\"charge-desc\"},[_c('div',{staticClass:\"row-1\"},[_vm._v(_vm._s(_vm.$t('boon-task-1-title')))]),_c('div',{staticClass:\"row-2\"},[_vm._v(_vm._s(_vm.$t('boon-p1-title')))])]),_c('div',{staticClass:\"gift-image\"},[(_vm.$gameName === 'koa')?_vm._l(([0,1,2,3]),function(i){return _c('img',{key:i,attrs:{\"src\":require(`@/assets/koa/boon/login_reward_${i}.png`),\"alt\":\"\"}})}):_vm._l(([0,1,2,3]),function(i){return _c('img',{key:i,attrs:{\"src\":require(`@/assets/koa/${_vm.$gameName === 'rom' ? 'aof' : _vm.$gameName}/boon/login_reward_${i}.png`),\"alt\":\"\"}})})],2),_c('div',{staticClass:\"login-reward-btn action-btn\"},[(!_vm.isLogin)?_c('span',{staticClass:\"click-btn\",on:{\"click\":_vm.focusInput}},[_vm._v(_vm._s(_vm.$t('boon-login')))]):(!_vm.hadLogin)?_c('span',{staticClass:\"click-btn\",on:{\"click\":_vm.focusInput}},[_vm._v(_vm._s(_vm.$t('boon-go-charge-short')))]):[(_vm.gotLoginReward)?_c('span',{staticClass:\"forbidden\"}):_c('span',{staticClass:\"todo click-btn\",on:{\"click\":function($event){return _vm.getReward(_vm.getLoginReward)}}},[_vm._v(_vm._s(_vm.$t('boon-gain')))])]],2)]),_c('SwiperSlide',{key:\"daily-reward\"},[_c('div',{staticClass:\"charge-desc\"},[_c('div',{staticClass:\"row-1\"},[_vm._v(_vm._s(_vm.$t('text-login-topup')))]),_c('div',{staticClass:\"row-2\"},[_vm._v(_vm._s(_vm.$t('text-claim-daily-chest')))])]),_c('div',{staticClass:\"gift-image gift-image__daily-reward\"},[_c('img',{attrs:{\"src\":require('@/assets/koa/boon/daily-reward-image.png'),\"alt\":\"\"}})]),_c('div',{staticClass:\"login-reward-btn action-btn login-reward-btn__daily-reward\"},[(!_vm.isLogin)?_c('span',{staticClass:\"click-btn\",on:{\"click\":_vm.focusInput}},[_vm._v(_vm._s(_vm.$t('boon-login')))]):[(_vm.gotDailyReward)?_c('span',{class:['forbidden', 'forbidden__daily-reward']}):_c('span',{staticClass:\"todo click-btn\",on:{\"click\":_vm.getDailyReward}},[_vm._v(_vm._s(_vm.$t('btn-open-now')))])]],2),(!_vm.gotDailyReward)?_c('div',{staticClass:\"tips\"},[_vm._v(\"*\"+_vm._s(_vm.$t('subtitle-daily-rewards-boon')))]):_vm._e()])],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <container :title=\"$t('boon-page-title') \" class=\"arrears-reminder-wrapper\" :class=\"[$i18n.locale, $gameName]\">\n    <div class=\"divider\"></div>\n    <div class=\"tab-wrapper\" :class=\"{ 'tab-small': ['de', 'sv'].includes($i18n.locale) }\">\n      <div v-for=\"(tabItem, tabIndex) in tabList\"\n           :key=\"tabItem.dataKey\"\n           :class=\"['tab', {'dot-active': !($data[tabItem.dataKey] || $store.state.formdata[tabItem.dataKey]), 'chosen-active': chosenIndex === tabIndex}]\"\n           @click=\"chosenIndex = tabIndex\">\n        <span>{{ $t(tabItem.langKey) }}</span>\n      </div>\n    </div>\n    <Swiper class=\"my-swiper-wrapper\" :options=\"swiperOptions\">\n      <!--快捷-->\n      <SwiperSlide key=\"install\">\n        <div class=\"charge-desc\">\n          <div class=\"row-1\">{{ $t('boon-task-2-title') }}</div>\n          <div class=\"row-2\">{{ $t('boon-p1-title') }}</div>\n        </div>\n        <div class=\"gift-image\">\n          <template v-if=\"$gameName === 'koa'\">\n            <img v-for=\"i in [0,1,2,3]\" :key=\"i\" :src=\"require(`@/assets/koa/boon/install_reward_${i}.png`)\" alt=\"\">\n          </template>\n          <template v-else>\n            <img v-for=\"i in [0,1,2,3]\" :key=\"i\" :src=\"require(`@/assets/koa/${$gameName === 'rom' ? 'aof' : $gameName}/boon/install_reward_${i}.png`)\" alt=\"\">\n          </template>\n        </div>\n        <div class=\"login-reward-btn action-btn\">\n          <template v-if=\"!isLogin\">\n            <span class=\"click-btn\" @click=\"focusInput\">{{ $t('boon-login') }}</span>\n          </template>\n          <template v-else-if=\"!hadInstall\">\n            <p class=\"browser-forbidden\" v-if=\"!calcShowInstall\">{{ $t('boon-browser-forbidden') }}</p>\n            <span v-else class=\"click-btn\" @click=\"install\">{{ $t('boon-task-2-add') }}</span>\n          </template>\n          <template v-else>\n            <span class=\"forbidden\" v-if=\"gotInstallReward\"></span>\n            <span class=\"todo click-btn\" v-else @click=\"getReward(getPwaReward)\">{{ $t('boon-gain') }}</span>\n          </template>\n        </div>\n      </SwiperSlide>\n      <!--登录-->\n      <SwiperSlide key=\"login\">\n        <div class=\"charge-desc\">\n          <div class=\"row-1\">{{ $t('boon-task-1-title') }}</div>\n          <div class=\"row-2\">{{ $t('boon-p1-title') }}</div>\n        </div>\n        <div class=\"gift-image\">\n          <template v-if=\"$gameName === 'koa'\">\n            <img v-for=\"i in [0,1,2,3]\" :key=\"i\" :src=\"require(`@/assets/koa/boon/login_reward_${i}.png`)\" alt=\"\">\n          </template>\n          <template v-else>\n            <img v-for=\"i in [0,1,2,3]\" :key=\"i\" :src=\"require(`@/assets/koa/${$gameName === 'rom' ? 'aof' : $gameName}/boon/login_reward_${i}.png`)\" alt=\"\">\n          </template>\n        </div>\n        <div class=\"login-reward-btn action-btn\">\n          <span v-if=\"!isLogin\" class=\"click-btn\" @click=\"focusInput\">{{ $t('boon-login') }}</span>\n          <span v-else-if=\"!hadLogin\" class=\"click-btn\" @click=\"focusInput\">{{ $t('boon-go-charge-short') }}</span>\n          <template v-else>\n            <span class=\"forbidden\" v-if=\"gotLoginReward\"></span>\n            <span class=\"todo click-btn\" v-else @click=\"getReward(getLoginReward)\">{{ $t('boon-gain') }}</span>\n          </template>\n        </div>\n      </SwiperSlide>\n      <!--每日登录-->\n      <SwiperSlide key=\"daily-reward\">\n        <div class=\"charge-desc\">\n          <div class=\"row-1\">{{ $t('text-login-topup') }}</div>\n          <div class=\"row-2\">{{ $t('text-claim-daily-chest') }}</div>\n        </div>\n        <div class=\"gift-image gift-image__daily-reward\">\n          <img :src=\"require('@/assets/koa/boon/daily-reward-image.png')\" alt=\"\">\n        </div>\n        <div class=\"login-reward-btn action-btn login-reward-btn__daily-reward\">\n          <span v-if=\"!isLogin\" class=\"click-btn\" @click=\"focusInput\">{{ $t('boon-login') }}</span>\n          <template v-else>\n            <span :class=\"['forbidden', 'forbidden__daily-reward']\" v-if=\"gotDailyReward\"></span>\n            <span class=\"todo click-btn\" v-else @click=\"getDailyReward\">{{ $t('btn-open-now') }}</span>\n          </template>\n        </div>\n\n        <div v-if=\"!gotDailyReward\" class=\"tips\">*{{ $t('subtitle-daily-rewards-boon') }}</div>\n      </SwiperSlide>\n    </Swiper>\n  </container>\n</template>\n\n<script>\nimport Container from '@/components/pop/container.vue'\nimport { getAmeDo, ameHoldByGet, ameDoByGet } from '@/server'\nimport { Swiper, SwiperSlide } from 'vue-awesome-swiper'\nimport 'swiper/css/swiper.min.css'\n\nimport UAParser from 'ua-parser-js'\nimport { mapState } from 'vuex'\nimport { numberFormat } from '@/utils/utils'\nconst { projectId, loginAction, getLoginReward, pwaOpenAction, getPwaReward } = window.$gcbk('apiParams.boonAme', {})\n\nconst ameParams = { p0: 'web', p1: projectId }\nconst numsMap = {\n  500: 50,\n  2000: 200,\n  5000: 400,\n  10000: 750,\n  20000: 1200,\n  50000: 2000\n}\nfunction displayMode () {\n  const isStandalone = window.matchMedia('(display-mode: standalone)').matches\n  if (document.referrer.startsWith('android-app://')) {\n    return 'twa'\n  } else if (navigator.standalone || isStandalone) {\n    return 'standalone'\n  }\n  return 'browser'\n}\n\nconst tabList = [\n  // {\n  //   dataKey: 'gotSeasonBenefitsReward',\n  //   langKey: 'season-benefits-tab-txt'\n  // },\n  // {\n  //   dataKey: 'gotTopupReward',\n  //   langKey: 'koa-topup-short-title'\n  // },\n  // {\n  //   dataKey: 'goTurntable',\n  //   langKey: 'boon-koa-turntable'\n  // },\n  {\n    dataKey: 'gotInstallReward',\n    langKey: 'boon-ss-install-title'\n  },\n  {\n    dataKey: 'gotLoginReward',\n    langKey: 'boon-ss-login-title'\n  },\n  {\n    dataKey: 'gotDailyReward',\n    langKey: 'btn-daily-rewards'\n  }\n]\n\nexport default {\n  name: 'BoonPop',\n  components: { Container, Swiper, SwiperSlide },\n  data () {\n    const instance = this\n    return {\n      chargeStatus: {},\n      recharge: 0,\n\n      hadLogin: false, // 1097\n      gotLoginReward: false, // 1098\n      hadInstall: false, // 1099\n      gotInstallReward: false, // 1100\n      goTurntable: false,\n      // gotSeasonBenefitsReward: false, // 1153,\n      deferredPrompt: window.__deferredPrompt || undefined,\n      showMobileSafariGuide: false,\n\n      progressPercent: 0,\n      numsMap,\n\n      chosenIndex: 0,\n      swiperInstance: undefined,\n      swiperOptions: {\n        autoplay: false,\n        on: {\n          slideChangeTransitionStart: function () {\n            instance.chosenIndex = this.activeIndex\n          },\n          init: function () {\n            // Swiper初始化了\n            instance.swiperInstance = this\n          }\n        }\n      },\n      tabList,\n      getLoginReward,\n      getPwaReward,\n      missionList: [],\n      serverId: 0,\n\n      gotToggleCoupon: true\n    }\n  },\n  methods: {\n    getImgUrl (imageId) {\n      // return require(`@/assets/koa/topup/${imageId}.png`)\n    },\n    numberFormat (num) {\n      return numberFormat(num)\n    },\n    onRecive (id) {\n      getAmeDo({\n        p0: 'web',\n        p1: 71,\n        p2: 1626,\n        id: id\n      }).then(res => {\n        const { code } = res\n        if (code === 0) {\n          const item = this.missionList.filter(list => list.id === id)\n          const index = this.missionList.findIndex(list => list.id === id)\n          item[0].status = 2\n          this.$set(this.missionList, index, item[0])\n        }\n      })\n    },\n    showInstallPart () {\n      window.addEventListener('beforeinstallprompt', (e) => {\n        // 防止 Chrome 67 及更早版本自动显示安装提示\n        e.preventDefault()\n        // 稍后再触发此事件\n        this.deferredPrompt = e\n      })\n    },\n    resetStatus () {\n      const params = {\n        p0: 'web',\n        p1: projectId,\n        p2: `${loginAction},${getLoginReward},${pwaOpenAction},${getPwaReward}`\n      }\n      this.$loading.show()\n      ameHoldByGet(params)\n        .then(res => {\n          const { data, code } = res\n          if (code === 0) {\n            const result = {}\n            for (const value of Object.values(data)) result[value.task_id] = value\n\n            this.hadLogin = loginAction in result\n            this.gotLoginReward = getLoginReward in result\n            this.hadInstall = pwaOpenAction in result\n            this.gotInstallReward = getPwaReward in result\n            // this.gotSeasonBenefitsReward = '1153' in result\n          }\n        })\n        .finally(() => this.$loading.hide())\n    },\n    resetTurntable () {\n      const params = {\n        p0: 'web',\n        p1: 79,\n        p2: 1711\n      }\n      this.$loading.show()\n      ameDoByGet(params)\n        .then(res => {\n          const { data, code } = res\n          if (code === 0) {\n            const ticket = data.score.filter((item) => item.score_id == '10085')[0]\n            this.goTurntable = !(ticket.ticket > 0 || ticket.total == 0)\n          }\n        })\n        .finally(() => this.$loading.hide())\n    },\n    // 领取奖励\n    getReward (taskId) {\n      const params = { p2: taskId }\n      this.$loading.show()\n      getAmeDo({ ...params, ...ameParams })\n        .then(res => {\n          const { code, data = [] } = res\n          if (code === 0 && data.length) {\n            if (taskId === getPwaReward) this.gotInstallReward = true\n            if (taskId === getLoginReward) this.gotLoginReward = true\n            // if (taskId === 1153) this.gotSeasonBenefitsReward = true\n          }\n        })\n        .finally(() => this.$loading.hide())\n    },\n    // 领取奖励\n    getDailyReward () {\n      this.$loading.show()\n      getAmeDo({ p0: 'web', p1: 11, p2: 1422 })\n        .then(res => {\n          const { code, data = [] } = res\n          if (code === 0 && data.length) {\n            this.$store.commit('formdata/setDailyRewardStatus', true)\n            this.$root.$emit('closePop')\n\n            setTimeout(() => {\n              this.$root.$emit('showPop', 'DailyReward', { reward: data[0] })\n            }, 0)\n          } else {\n            this.$toast.err(this.$t('network_err'))\n          }\n        })\n        .catch(() => this.$toast.err(this.$t('network_err')))\n        .finally(() => this.$loading.hide())\n    },\n    focusInput () {\n      this.$root.$emit('closePop')\n      this.$root.$emit('ClickPayButNotLogin')\n      // const input = document.querySelector('#uidInput')\n      // setTimeout(() => {\n      //   input.focus()\n      // }, 100)\n    },\n    install () {\n      this.$root.$emit('closePop')\n      // 如果是谷歌浏览器\n      if (this.deferredPrompt) {\n        this.deferredPrompt.prompt()\n        // 等待用户反馈\n        this.deferredPrompt.userChoice\n          .then((choiceResult) => {\n            if (choiceResult.outcome === 'accepted') {\n              console.log('User accepted the A2HS prompt')\n\n              const displayModeCheck = setInterval(() => {\n                if (displayMode() === 'standalone') {\n                  clearInterval(displayModeCheck)\n                  this.$root.$emit('installSuccessful')\n                }\n              }, 1000)\n            } else {\n              console.log('User dismissed the A2HS prompt')\n            }\n            this.deferredPrompt = undefined\n          })\n      } else {\n        setTimeout(() => {\n          this.$root.$emit('mobileSafariGuide')\n        }, 500)\n      }\n    },\n    // goTurntablePage() {\n    //   let finalUrl = `${process.env.VUE_APP_URL_KOA_TURNTABLE}?l=${this.$i18n.locale}`\n    //   if(localStorage.getItem('openid')) {\n    //     finalUrl += `&openid=${encodeURIComponent(localStorage.getItem('openid'))}`\n    //   }\n    //   window.open(finalUrl, '_blank')\n    // }\n    goToggleActivityPage () {\n      let finalUrl = `${process.env.VUE_APP_URL_KOA_TOGGLE_COUPON}?l=${this.$i18n.locale}`\n      if (localStorage.getItem('openid')) {\n        finalUrl += `&openid=${encodeURIComponent(localStorage.getItem('openid'))}`\n      }\n      window.open(finalUrl, '_blank')\n    }\n  },\n  computed: {\n    ...mapState('userinfo', ['isLogin']),\n    ...mapState(['userinfo']),\n    ...mapState('formdata', ['gotDailyReward', 'koaTopupEnable']),\n    calcShowInstall () {\n      const parser = new UAParser(navigator.userAgent)\n      const result = parser.getResult()\n      const { browser } = result\n\n      const whiteBrowser = browser.name === 'Mobile Safari' || ((browser.name || '').includes('Chrome') && this.deferredPrompt)\n      if (!this.isLogin) return true\n      if (!this.hadInstall) return whiteBrowser\n      return true\n    }\n  },\n  created () {\n    this.$root.$on('loginSuccess', () => {\n      setTimeout(() => this.resetStatus(), 2000)\n    })\n    if (this.isLogin) this.resetStatus()\n    this.$watch('chosenIndex', (index) => {\n      this.swiperInstance.slideTo(index, 200, false)\n    })\n\n    this.showInstallPart()\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n@use \"~@/utils/utils\" as utils;\n.arrears-reminder-wrapper {\n  background-color: #383838;\n  padding-top: 18px!important;\n  background-image: url(\"~@/assets/koa/boon/boon-pop-bg_m.png\");\n  background-position: bottom center;\n  background-repeat: no-repeat;\n  background-size: 100% 100%;\n\n  .divider{\n    width: 100%;\n    height: 2px;\n    background: #D8D8D8;\n    opacity: 0.2;\n    margin: 18px auto 0;\n  }\n\n  .tab-wrapper{\n    display: flex;\n    align-items: center;\n    justify-content: space-around;\n\n    font-size: 24px;\n    font-family: PingFangSC-Regular, PingFang SC;\n    font-weight: 400;\n    color: #AEAEAE;\n    padding-top: 16px;\n    line-height: 1.1;\n\n    &.tab-small {\n      font-size: 18px;\n    }\n\n    .tab{\n      min-height: 33px;\n      padding: 3px 10px;\n      align-items: center;\n      justify-content: center;\n      display: inline-block;\n      cursor: pointer;\n    }\n\n    .dot-active{\n      span{\n        display: inline-block;\n        position: relative;\n\n        &:after{\n          display: inline-block;\n          content: ' ';\n          width: 10px;\n          height: 10px;\n          background: #D20202;\n          border-radius: 50%;\n          position: absolute;\n          top: 0;\n          right: 0;\n          transform: translate(100%, -100%);\n        }\n      }\n    }\n\n    .chosen-active{\n      position: relative;\n      span {\n        color: #ffffff;\n      }\n\n      &:before{\n        display: inline-block;\n        content: ' ';\n        width: 70px;\n        height: 4px;\n        background: rgba(235, 170, 36, 1);\n        border-radius: 2px;\n        position: absolute;\n        bottom: 0;\n        left: 50%;\n        transform: translate(-50%, 100%);\n      }\n    }\n  }\n\n  .my-swiper-wrapper{\n    //.turntable {\n    //  position: relative;\n    //  display: flex;\n    //  height: 390PX;\n    //  align-items: center;\n    //  justify-content: center;\n    //  flex-direction: column;\n    //  .turntable-banner {\n    //    position: absolute;\n    //    top: -20px;\n    //    @include utils.bgCenter('koa/turntable/banner.png',330PX, 390PX);\n    //  }\n    //  .turntable-button {\n    //    position: absolute;\n    //    bottom: 40px;\n    //    display: flex;\n    //    align-items: center;\n    //    justify-content: center;\n    //    font-weight: bold;\n    //    // font-size: 20PX;\n    //    padding-bottom: 2px;\n    //    transition: all 0.2s;\n    //    color: #410B00;\n    //    cursor: pointer;\n    //    @include utils.bgCenter('koa/turntable/btn.png',200PX, 38PX);\n    //\n    //    &:hover {\n    //      transform: scale(0.95);\n    //    }\n    //  }\n    //}\n    .charge-desc{\n      font-size: 40px;\n      font-family: PingFangSC-Semibold, PingFang SC;\n      font-weight: 600;\n      color: #D4B880;\n      line-height: 56px;\n      padding-top: 48px;\n\n      .row-1{\n        color: rgba(235, 170, 36, 1);\n      }\n      .row-2{\n        color: white;\n      }\n    }\n\n    .gift-image{\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      flex-wrap: wrap;\n      width: 380px;\n      margin: 50px auto 0;\n      img{\n        display: inline-block;\n        width: 188px;\n        height: auto;\n      }\n    }\n\n    .gift-image__daily-reward {\n      margin-top: 110px;\n      img{\n        width: 570px;\n      }\n\n      & + .login-reward-btn{\n        .click-btn{\n          margin-top: 120px;\n        }\n      }\n    }\n    .topup-title {\n      color:#fff;\n      padding: 20px\n    }\n    //.mission-item{\n    //  margin: 0 auto;\n    //  width: 632px;\n    //  height: 124px;\n    //  margin-bottom: 16px;\n    //  background-image: url(\"~@/assets/koa/mission-item-bg.png\");\n    //  background-position: center center;\n    //  background-repeat: no-repeat;\n    //  background-size: 100% 100%;\n    //  .mission-title{\n    //    color: #fff;\n    //    width: 100%;\n    //    height: 42px;\n    //    padding: 10px 0px 0px 20px;\n    //    line-height: 42px;\n    //    font-size: 20px;\n    //    line-height: 22px;\n    //    text-align: left;\n    //    display: flex;\n    //    align-items: center;\n    //    ::v-deep {\n    //      em {\n    //        margin-left: 5px;\n    //        display: inline-block;\n    //        width: 21px;\n    //        height: 18px;\n    //        background-image: url(\"~@/assets/koa/boon-diamond.png\");\n    //        background-position: center center;\n    //        background-repeat: no-repeat;\n    //        background-size: 100% 100%;\n    //      }\n    //    }\n    //  }\n    //  .mission-content{\n    //    width: 100%;\n    //    color: #fff;\n    //    padding: 10px 20px;\n    //    display: flex;\n    //    align-items: center;\n    //    justify-content: space-between;\n    //  }\n    //  .awards {\n    //    display: flex;\n    //    align-items: center;\n    //    justify-content: flex-start;\n    //  }\n    //  .award{\n    //    position: relative;\n    //    // @include flex-center($justify-content: flex-start);\n    //    .award-view{\n    //      width: 60px;\n    //      height: 60px;\n    //      margin-right: 10px;\n    //      img {\n    //        width: 60px;\n    //        height: 60px;\n    //      }\n    //      // @include bg('icon-counter.png');\n    //    }\n    //    .award-quantity{\n    //      font-size: 10px;\n    //      text-align: right;\n    //      position: absolute;\n    //      height: 16px;\n    //      line-height: 14px;\n    //      padding-right: 2px;\n    //      left: 2px;\n    //      bottom: 0px;\n    //      width: 56px;\n    //      background: rgba(0,0,0,0.4);\n    //    }\n    //  }\n    //  .btn-group{\n    //    .btn{\n    //      // width: 198px;\n    //      // height: 58px;\n    //      width: 140px;\n    //      height: 40px;\n    //      padding: 0 4px;\n    //      font-size: 16px;\n    //      font-weight: 500;\n    //      color: #FFFFFF;\n    //      display: flex;\n    //      align-items: center;\n    //      justify-content: center;\n    //      line-height: 16px;\n    //      // @include flex-center();\n    //      &.disable{\n    //        pointer-events: none;\n    //        filter: grayscale(1);\n    //      }\n    //    }\n    //    .btn-receive{\n    //      color: #582D05;\n    //      background-image: url(\"~@/assets/koa/btn-topup-receive.png\");\n    //      background-position: center center;\n    //      background-repeat: no-repeat;\n    //      background-size: 100% 100%;\n    //    }\n    //    .btn-received{\n    //      background-image: url(\"~@/assets/koa/btn-topup-received.png\");\n    //      background-position: center center;\n    //      background-repeat: no-repeat;\n    //      background-size: 100% 100%;\n    //    }\n    //    .btn-unfinished{\n    //      cursor: pointer;\n    //      background-image: url(\"~@/assets/koa/btn-topup-unfinished.png\");\n    //      background-position: center center;\n    //      background-repeat: no-repeat;\n    //      background-size: 100% 100%;\n    //    }\n    //\n    //  }\n    //\n    //}\n\n    //.toggle-poster{\n    //  @include utils.bgCenter('koa/boon-pop-toggle-coupon-m.png', 603px, 358px);\n    //  margin: 100px auto 50px;\n    //}\n  }\n\n  .action-btn{\n    position: relative;\n    min-height: 110px;\n    span{\n      margin: 45px auto 0;\n      padding: 0 20px;\n      background-color: #C58833;\n      @include utils.bgCenter('koa/boon/boon_btn.png',312px, 70px);\n      min-width: 300px;\n\n      font-size: 30px;\n      font-family: PingFangSC-Semibold, PingFang SC;\n      font-weight: 600;\n      color: #633B00;\n      line-height: 70px;\n      display: inline-block;\n\n      &.forbidden{\n        background-color: transparent;\n        @include utils.bgCenter('koa/boon/boon-award-get.png',349px, 263px);\n        margin-top: 0;\n        position: absolute;\n        left: 50%;\n        transform: translateX(-50%);\n        top: -160px;\n      }\n    }\n\n    .browser-forbidden{\n      font-size: 22px;\n      font-family: PingFangSC-Regular, PingFang SC;\n      font-weight: 400;\n      color: #EED16D;\n      line-height: 30px;\n      margin-top: 56px;\n      padding: 0 43px;\n    }\n  }\n\n  .login-reward-btn__daily-reward{\n    height: 220px;\n\n    .forbidden__daily-reward{\n      top: -60px!important;\n    }\n  }\n\n  .tips{\n    font-size: 20px;\n    font-family: PingFang-SC-Bold, PingFang-SC;\n    font-weight: bold;\n    color: #AEAEAE;\n    line-height: 28px;\n    margin-top: 20px;\n    margin-bottom: 30px;\n  }\n\n  ::v-deep{\n    padding-bottom: 14px;\n    .content{\n      margin-top: 0;\n      padding-left: 20px;\n      padding-right: 20px;\n      max-height: none;\n    }\n    .footer-wrapper{\n      display: none;\n    }\n\n    .swiper-slide{\n      max-height: 870px;\n      overflow-y: scroll;\n    }\n  }\n}\n@include utils.setPcContent{\n  .arrears-reminder-wrapper {\n    padding-top: 27PX !important;\n    background-image: url(\"~@/assets/koa/boon/boon-pop-bg_pc.png\");\n    background-position: bottom center;\n\n    .divider {\n      margin-top: 21PX;\n      height: 2PX;\n    }\n\n    .tab-wrapper {\n      font-size: 18PX;\n      line-height: 25PX;\n      padding-top: 14PX;\n\n      .tab{\n        min-height: 25PX;\n        padding: 3PX 10PX;\n        margin: 0 0;\n      }\n\n      .dot-active{\n        span{\n          &:after{\n            width: 8PX;\n            height: 8PX;\n          }\n        }\n      }\n\n      .chosen-active{\n        color: white;\n        &:before{\n          width: 50PX;\n          height: 4PX;\n          border-radius: 2PX;\n        }\n      }\n    }\n\n    .my-swiper-wrapper{\n      margin-top: 6PX;\n      //.turntable {\n      //  position: relative;\n      //  display: flex;\n      //  height: 446PX;\n      //  align-items: center;\n      //  justify-content: center;\n      //  flex-direction: column;\n      //  .turntable-banner {\n      //    position: absolute;\n      //    top: 0px;\n      //    @include utils.bgCenter('koa/turntable/banner.png',400PX, 446PX);\n      //  }\n      //  .turntable-button {\n      //    position: absolute;\n      //    bottom: 10px;\n      //    display: flex;\n      //    align-items: center;\n      //    justify-content: center;\n      //    font-weight: bold;\n      //    // font-size: 20PX;\n      //    padding-bottom: 2px;\n      //    transition: all 0.2s;\n      //    color: #410B00;\n      //    cursor: pointer;\n      //    @include utils.bgCenter('koa/turntable/btn.png',250PX, 47PX);\n      //\n      //    &:hover {\n      //      transform: scale(0.95);\n      //    }\n      //  }\n      //}\n      .charge-desc{\n        font-size: 26PX;\n        line-height: 32PX;\n        padding-top: 36PX;\n      }\n      .gift-image{\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        margin: 60PX auto 0;\n        flex-wrap: nowrap;\n        img {\n          width: 126PX;\n        }\n      }\n\n      .gift-image__daily-reward {\n        margin-top: 50px;\n        img{\n          width: 480px;\n        }\n\n        & + .login-reward-btn{\n          .click-btn{\n            margin-top: 60px;\n          }\n        }\n      }\n      .mission-item {\n        width: 610px;\n      }\n\n      //.toggle-poster{\n      //  @include utils.bgCenter('koa/boon-pop-toggle-coupon-m.png', 545px, 327px);\n      //  margin: 20px auto 0;\n      //\n      //  & + .action-btn .click-btn{\n      //    margin-top: 30px;\n      //  }\n      //}\n    }\n\n    .action-btn{\n      span{\n        margin-top: 60PX;\n        padding: 0 20PX;\n        min-width: 200PX;\n        line-height: 50PX;\n        font-size: 20PX;\n        @include utils.bgCenter('koa/boon/boon_btn.png',220PX, 50PX);\n        cursor: pointer;\n\n        &.forbidden{\n          @include utils.bgCenter('koa/boon/boon-award-get.png',243PX, 184PX);\n          top: -120PX;\n        }\n      }\n    }\n\n    .login-reward-btn__daily-reward{\n      height: 110px;\n\n      .forbidden__daily-reward{\n        top: -80px!important;\n      }\n    }\n\n    .browser-forbidden{\n      font-size: 16PX;\n      font-family: PingFangSC-Regular, PingFang SC;\n      font-weight: 400;\n      color: #FF5E0F;\n      line-height: 22PX;\n      margin-top: 37PX;\n      padding: 0 43PX;\n    }\n\n    ::v-deep{\n      padding-bottom: 14px;\n      .content{\n        margin-top: 0;\n        padding-left: 20PX;\n        padding-right: 20PX;\n      }\n      .footer-wrapper{\n        display: none;\n      }\n\n      .swiper-slide{\n        max-height: 500PX;\n        overflow-y: scroll;\n        height: 100%;\n      }\n\n      .title{\n        font-size: 24PX;\n        line-height: 33PX;\n        i{\n          top:29PX;\n          right: 16PX;\n          height: 26PX;\n          width: 26PX;\n        }\n      }\n    }\n  }\n}\n\n.arrears-reminder-wrapper.aof{\n  background-image: url(\"~@/assets/koa/boon/boon-pop-bg_m.png\");\n  .action-btn{\n    span{\n      @include utils.bgCenter('koa/boon/boon_btn.png',312px, 70px);\n\n      &.forbidden{\n        @include utils.bgCenter('koa/boon/boon-award-get.png',349px, 263px);\n      }\n    }\n  }\n}\n@include utils.setPcContent{\n  .arrears-reminder-wrapper.aof{\n    background-image: url(\"~@/assets/koa/boon/boon-pop-bg_pc.png\");\n\n    .action-btn{\n      span{\n        @include utils.bgCenter('koa/boon/boon_btn.png',220PX, 50PX);\n\n        &.forbidden{\n          @include utils.bgCenter('koa/boon/boon-award-get.png',243PX, 184PX);\n        }\n      }\n    }\n  }\n}\n\n.arrears-reminder-wrapper.rom{\n  @extend .aof;\n}\n</style>\n", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./BoonPop.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./BoonPop.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./BoonPop.vue?vue&type=template&id=c34714c8&scoped=true\"\nimport script from \"./BoonPop.vue?vue&type=script&lang=js\"\nexport * from \"./BoonPop.vue?vue&type=script&lang=js\"\nimport style0 from \"./BoonPop.vue?vue&type=style&index=0&id=c34714c8&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"c34714c8\",\n  null\n  \n)\n\nexport default component.exports", "module.exports = __webpack_public_path__ + \"static/**********/img/install_reward_1.e49e53f9.png\";", "var map = {\n\t\"./aof/boon/login_reward_0.png\": \"53bb\",\n\t\"./aof/boon/login_reward_1.png\": \"89c9\",\n\t\"./aof/boon/login_reward_2.png\": \"fc88\",\n\t\"./aof/boon/login_reward_3.png\": \"8ca3\"\n};\n\n\nfunction webpackContext(req) {\n\tvar id = webpackContextResolve(req);\n\treturn __webpack_require__(id);\n}\nfunction webpackContextResolve(req) {\n\tif(!__webpack_require__.o(map, req)) {\n\t\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\t\te.code = 'MODULE_NOT_FOUND';\n\t\tthrow e;\n\t}\n\treturn map[req];\n}\nwebpackContext.keys = function webpackContextKeys() {\n\treturn Object.keys(map);\n};\nwebpackContext.resolve = webpackContextResolve;\nmodule.exports = webpackContext;\nwebpackContext.id = \"7778\";", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"footer-wrapper\"},[_c('div',{staticClass:\"copyright\"},[(!_vm.isMobile)?_c('img',{staticClass:\"logo\",staticStyle:{\"vertical-align\":\"text-bottom\",\"padding-right\":\"10px\"},attrs:{\"src\":require(\"../../../assets/common/icon/fp-logo.png\"),\"alt\":\"funplus\"}}):_vm._e(),_vm._m(0)])])\n}\nvar staticRenderFns = [function (){var _vm=this,_c=_vm._self._c;return _c('div',[_c('p',{staticClass:\"links-p\"},[_c('a',{staticClass:\"links\",attrs:{\"href\":\"https://nenglianghe.cn/compliance/privacyAgreement.html\",\"target\":\"_blank\"}},[_vm._v(\"隐私政策\")]),_c('a',{staticClass:\"links\",attrs:{\"href\":\"https://nenglianghe.cn/compliance/userAgreement.html\",\"target\":\"_blank\"}},[_vm._v(\"用户协议\")]),_c('a',{staticClass:\"links\",attrs:{\"href\":\"https://nenglianghe.cn/compliance/children.html\",\"target\":\"_blank\"}},[_vm._v(\"儿童个人信息保护政策\")]),_c('a',{staticClass:\"links\",attrs:{\"href\":\"http://koa.nenglianghe.cn/hook/\",\"target\":\"_blank\"}},[_vm._v(\"防沉迷\")])]),_c('p',[_vm._v(\"网站备案/许可证号: \"),_c('a',{attrs:{\"href\":\"https://beian.miit.gov.cn/\",\"target\":\"_blank\"}},[_vm._v(\"京ICP备16053236号-11\")])]),_c('p',[_c('img',{staticClass:\"gongan\",attrs:{\"src\":require(\"../../../assets/common/icon-police-gongan.png\"),\"alt\":\"\"}}),_c('a',{attrs:{\"href\":\"http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=11010502051888\",\"target\":\"_blank\"}},[_vm._v(\"京公网安备 11010502051888号\")])])])\n}]\n\nexport { render, staticRenderFns }", "<template>\n  <div class=\"footer-wrapper\">\n    <div class=\"copyright\">\n      <img class=\"logo\" src=\"../../../assets/common/icon/fp-logo.png\" alt=\"funplus\" style=\"vertical-align: text-bottom; padding-right: 10px;\" v-if=\"!isMobile\">\n      <div>\n        <p class=\"links-p\">\n          <a class=\"links\" href=\"https://nenglianghe.cn/compliance/privacyAgreement.html\" target=\"_blank\">隐私政策</a>\n          <a class=\"links\" href=\"https://nenglianghe.cn/compliance/userAgreement.html\" target=\"_blank\">用户协议</a>\n          <a class=\"links\" href=\"https://nenglianghe.cn/compliance/children.html\" target=\"_blank\">儿童个人信息保护政策</a>\n          <a class=\"links\" href=\"http://koa.nenglianghe.cn/hook/\" target=\"_blank\">防沉迷</a>\n        </p>\n        <p>网站备案/许可证号: <a href=\"https://beian.miit.gov.cn/\" target=\"_blank\">京ICP备16053236号-11</a></p>\n        <p>\n          <img class=\"gongan\" src=\"../../../assets/common/icon-police-gongan.png\" alt=\"\">\n          <a href=\"http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=11010502051888\" target=\"_blank\">京公网安备 11010502051888号</a>\n        </p>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { mapState } from 'vuex'\nexport default {\n  name: 'CommonFooter',\n  computed: {\n    ...mapState(['isMobile'])\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n@use \"~@/utils/utils.scss\" as utils;\n.copyright {\n  height: 100PX;\n  font-size: 13PX;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #CACACA;\n  background-color: black;\n  font-size: 14PX;\n\n  .logo {\n    margin-right: 59PX;\n    height: 36PX;\n  }\n\n  p {\n    margin: 10PX 0;\n    display: flex;\n    align-items: center;\n  }\n  .gongan {\n    width: 16PX;\n    margin-right: 5PX;\n  }\n  a, a:active, a:visited {\n    color: #CACACA;\n  }\n  a:hover {\n    color: #ffffff;\n  }\n  a.links {\n    color: #ffffff;\n    font-size: 16PX;\n    & + a.links {\n      margin-left: 25PX;\n    }\n    &:hover {\n      color: #d5d5d5;\n    }\n  }\n}\n@include utils.setMobileContent{\n  .footer-wrapper {\n    flex: 1;\n    display: flex;\n    flex-direction: column;\n    justify-content: flex-end;\n  }\n  .copyright {\n    padding: 10px 0;\n    height: auto;\n    margin-top: 40px;\n    background-color: rgba(0, 0, 0, 0.2);\n    font-size: 16px;\n    text-align: center;\n\n    p {\n      margin: 10px 0;\n      justify-content: center;\n    }\n    a.links {\n      font-size: 20px;\n      & + a.links {\n        margin-left: 30px;\n      }\n    }\n  }\n}\n</style>\n", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./CommonFooter.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./CommonFooter.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./CommonFooter.vue?vue&type=template&id=5f93b707&scoped=true\"\nimport script from \"./CommonFooter.vue?vue&type=script&lang=js\"\nexport * from \"./CommonFooter.vue?vue&type=script&lang=js\"\nimport style0 from \"./CommonFooter.vue?vue&type=style&index=0&id=5f93b707&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"5f93b707\",\n  null\n  \n)\n\nexport default component.exports", "module.exports = __webpack_public_path__ + \"static/**********/img/boon-install-award.112be313.png\";", "module.exports = __webpack_public_path__ + \"static/**********/img/install_reward_3.76a3b0b3.png\";", "module.exports = __webpack_public_path__ + \"static/**********/img/login_reward_2.a42cc374.png\";", "module.exports = __webpack_public_path__ + \"static/**********/img/daily-reward-image.965961d0.png\";", "module.exports = __webpack_public_path__ + \"static/**********/img/login_reward_1.dea43e02.png\";", "module.exports = __webpack_public_path__ + \"static/**********/img/boon-login-award.b4838254.png\";", "module.exports = __webpack_public_path__ + \"static/**********/img/login_reward_3.9b45a0c6.png\";", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./WhatIsDiamond.vue?vue&type=style&index=0&id=1b49d88b&prod&scoped=true&lang=scss\"", "module.exports = __webpack_public_path__ + \"static/**********/img/boon-login-award.9a9eae60.png\";", "export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./privatePermission.vue?vue&type=style&index=0&id=6cb8a3e6&prod&scoped=true&lang=scss\"", "module.exports = __webpack_public_path__ + \"static/**********/img/install_reward_0.f9a9f8bf.png\";", "var map = {\n\t\"./install_reward_0.png\": \"9a1d\",\n\t\"./install_reward_1.png\": \"c23c\",\n\t\"./install_reward_2.png\": \"6a8c\",\n\t\"./install_reward_3.png\": \"5f39\"\n};\n\n\nfunction webpackContext(req) {\n\tvar id = webpackContextResolve(req);\n\treturn __webpack_require__(id);\n}\nfunction webpackContextResolve(req) {\n\tif(!__webpack_require__.o(map, req)) {\n\t\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\t\te.code = 'MODULE_NOT_FOUND';\n\t\tthrow e;\n\t}\n\treturn map[req];\n}\nwebpackContext.keys = function webpackContextKeys() {\n\treturn Object.keys(map);\n};\nwebpackContext.resolve = webpackContextResolve;\nmodule.exports = webpackContext;\nwebpackContext.id = \"9df3\";", "module.exports = __webpack_public_path__ + \"static/**********/img/boon-install-award.70fbd832.png\";", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./PrivacyPolicy.vue?vue&type=style&index=0&id=633825b6&prod&scoped=true&lang=scss\"", "var map = {\n\t\"./boon/boon-install-award.png\": \"a5f3\",\n\t\"./ssd/boon-install-award.png\": \"29db\",\n\t\"./ssv2/boon-install-award.png\": \"7bc6\"\n};\n\n\nfunction webpackContext(req) {\n\tvar id = webpackContextResolve(req);\n\treturn __webpack_require__(id);\n}\nfunction webpackContextResolve(req) {\n\tif(!__webpack_require__.o(map, req)) {\n\t\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\t\te.code = 'MODULE_NOT_FOUND';\n\t\tthrow e;\n\t}\n\treturn map[req];\n}\nwebpackContext.keys = function webpackContextKeys() {\n\treturn Object.keys(map);\n};\nwebpackContext.resolve = webpackContextResolve;\nmodule.exports = webpackContext;\nwebpackContext.id = \"b519\";", "module.exports = __webpack_public_path__ + \"static/**********/img/install_reward_1.e49e53f9.png\";", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./privateConfirmPop.vue?vue&type=style&index=0&id=7591e3ea&prod&scoped=true&lang=scss\"", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('container-v2',{staticClass:\"custom-pop\"},[_c('div',{staticClass:\"header\"},[_c('span',[_vm._v(\"DURCHLESEN！\")]),_c('i',{on:{\"click\":_vm.closePop}})]),_c('div',{staticClass:\"content\"},[_c('div',{staticClass:\"strong\"},[_vm._v(\"KEIN WIDERRUFSRECHT！\")]),_vm._v(\" Wenn du auf \\\"Kaufen\\\" tippst, stimmst du der unmittelbaren Erfüllung des Vertrags zu und akzeptierst, dass du dadurch dein Widerrufsrecht verlierst .(siehe auch \"),_c('a',{attrs:{\"href\":\"https://funplus.com/terms-conditions/#section-13\",\"target\":\"_blank\"}},[_vm._v(\"Rückerstattungsbedingungen\")]),_vm._v(\"). \")]),_c('div',{staticClass:\"footer\"},[_c('div',{staticClass:\"btn-cancel btn\",on:{\"click\":_vm.closePop}},[_vm._v(\"ABBRECHEN\")]),_c('div',{staticClass:\"btn-confirm btn\",on:{\"click\":_vm.confirm}},[_vm._v(\"Zustimmen und fortfahren.\")])])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<script>\nimport ContainerV2 from '@/components/pop/containerV2.vue'\nexport default {\n  name: 'privateConfirmPop',\n  components: { ContainerV2 },\n  props: {\n    option: {\n      type: Object,\n      default: () => {}\n    }\n  },\n  methods: {\n    closePop () {\n      if (this.option.no) this.option.no()\n      this.$root.$emit('closePop')\n    },\n    confirm () {\n      if (this.option.ok) this.option.ok()\n\n      this.$root.$emit('changeDePopPrivacy')\n      this.$root.$emit('closePop')\n    }\n  }\n}\n</script>\n\n<template>\n  <container-v2 class=\"custom-pop\">\n    <div class=\"header\">\n      <span>DURCHLESEN！</span>\n      <i @click=\"closePop\"></i>\n    </div>\n    <div class=\"content\">\n      <div class=\"strong\">KEIN WIDERRUFSRECHT！</div>\n      Wenn du auf \"Kaufen\" tippst, stimmst du der unmittelbaren Erfüllung des Vertrags zu und akzeptierst, dass du dadurch dein Widerrufsrecht verlierst .(siehe auch <a href=\"https://funplus.com/terms-conditions/#section-13\" target=\"_blank\">Rückerstattungsbedingungen</a>).\n    </div>\n    <div class=\"footer\">\n      <div @click=\"closePop\" class=\"btn-cancel btn\">ABBRECHEN</div>\n      <div class=\"btn-confirm btn\" @click=\"confirm\">Zustimmen und fortfahren.</div>\n    </div>\n  </container-v2>\n</template>\n\n<style scoped lang=\"scss\">\n@use \"~@/utils/utils.scss\" as utils;\n.pop-container{\n  height: 450px;\n  background: rgb(50, 30, 9);\n  border-radius: 10px;\n  padding-top: 10px;\n  display: flex;\n  flex-direction: column;\n  padding-bottom: 40px;\n  border: 1px solid #827350;\n\n  .header{\n    height: 80px;\n    position: relative;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n\n    span{\n      font-family: SourceHanSansCN, SourceHanSansCN;\n      font-weight: bold;\n      font-size: 32px;\n      color: white;\n      max-width: 80%;\n      line-height: 1;\n    }\n\n    i{\n      display: inline-block;\n      @include utils.bgCenterForCommon('pop/close-popup.png',29px,30px);\n      position: absolute;\n      right: 30px;\n    }\n  }\n  .content{\n    flex: 1;\n    margin-bottom: 10px;\n    overflow-y: scroll;\n    padding: 0 30px;\n\n    font-family: SourceHanSansCN, SourceHanSansCN;\n    font-weight: 500;\n    font-size: 24px;\n    color: white;\n    line-height: 35px;\n    letter-spacing: 1px;\n    text-align: left;\n\n    .strong{\n      font-family: SourceHanSansCN, SourceHanSansCN;\n      font-weight: bold;\n      font-size: 24px;\n      color: #FEB522;\n      margin-bottom: 8px;\n    }\n\n    a{\n      color: white;\n      text-decoration: underline;\n    }\n  }\n  .footer{\n    display: flex;\n    align-items: center;\n    justify-content: center;\n\n    .btn{\n      border-radius: 2px;\n      font-family: PingFangSC, PingFang SC;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-size: 24px;\n      font-weight: 600;\n      line-height: 1;\n      text-shadow: 0px 4px 4px rgba(0,0,0,0.7);\n    }\n\n    .btn-cancel{\n      color: white;\n      @include utils.bgCenterForCommon('privacy/pop-privacy-cancel-bg-m.png', 260px, 60px)\n    }\n\n    .btn-confirm{\n      color: #FFFFFF;\n      margin-left: 40px;\n      @include utils.bgCenterForCommon('privacy/pop-privacy-confirm-bg-m.png', 260px, 60px);\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    }\n  }\n  .checkbox{\n    margin-top: 10px;\n    margin-bottom: 30px;\n  }\n}\n\n@include utils.setPcContent{\n  .pop-container{\n    height: 340px;\n    border-radius: 5px;\n    padding-bottom: 30px;\n\n    .header{\n      height: 60px;\n\n      span{\n        font-size: 26px;\n      }\n\n      i{\n        @include utils.bgCenterForCommon('pop/close-popup.png',20px,20px);\n        cursor: pointer;\n      }\n    }\n\n    .content{\n      font-size: 18px;\n      line-height: 24px;\n    }\n\n    .footer{\n      .btn{\n        border-radius: 1px;\n        width: 200px;\n        height: 40px;\n        font-size: 16px;\n        cursor: pointer;\n      }\n\n      .btn-cancel{\n        @include utils.bgCenterForCommon('privacy/pop-privacy-cancel-bg-pc.png', 180px, 40px)\n      }\n\n      .btn-confirm{\n        margin-left: 30px;\n        @include utils.bgCenterForCommon('privacy/pop-privacy-confirm-bg-pc.png', 180px, 40px)\n      }\n    }\n  }\n}\n</style>\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./privateConfirmPop.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./privateConfirmPop.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./privateConfirmPop.vue?vue&type=template&id=7591e3ea&scoped=true\"\nimport script from \"./privateConfirmPop.vue?vue&type=script&lang=js\"\nexport * from \"./privateConfirmPop.vue?vue&type=script&lang=js\"\nimport style0 from \"./privateConfirmPop.vue?vue&type=style&index=0&id=7591e3ea&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"7591e3ea\",\n  null\n  \n)\n\nexport default component.exports", "/**\n * SSR Window 2.0.0\n * Better handling for window object in SSR environment\n * https://github.com/nolimits4web/ssr-window\n *\n * Copyright 2020, <PERSON>\n *\n * Licensed under MIT\n *\n * Released on: May 12, 2020\n */\n/* eslint-disable no-param-reassign */\nfunction isObject(obj) {\n    return (obj !== null &&\n        typeof obj === 'object' &&\n        'constructor' in obj &&\n        obj.constructor === Object);\n}\nfunction extend(target, src) {\n    if (target === void 0) { target = {}; }\n    if (src === void 0) { src = {}; }\n    Object.keys(src).forEach(function (key) {\n        if (typeof target[key] === 'undefined')\n            target[key] = src[key];\n        else if (isObject(src[key]) &&\n            isObject(target[key]) &&\n            Object.keys(src[key]).length > 0) {\n            extend(target[key], src[key]);\n        }\n    });\n}\n\nvar doc = typeof document !== 'undefined' ? document : {};\nvar ssrDocument = {\n    body: {},\n    addEventListener: function () { },\n    removeEventListener: function () { },\n    activeElement: {\n        blur: function () { },\n        nodeName: '',\n    },\n    querySelector: function () {\n        return null;\n    },\n    querySelectorAll: function () {\n        return [];\n    },\n    getElementById: function () {\n        return null;\n    },\n    createEvent: function () {\n        return {\n            initEvent: function () { },\n        };\n    },\n    createElement: function () {\n        return {\n            children: [],\n            childNodes: [],\n            style: {},\n            setAttribute: function () { },\n            getElementsByTagName: function () {\n                return [];\n            },\n        };\n    },\n    createElementNS: function () {\n        return {};\n    },\n    importNode: function () {\n        return null;\n    },\n    location: {\n        hash: '',\n        host: '',\n        hostname: '',\n        href: '',\n        origin: '',\n        pathname: '',\n        protocol: '',\n        search: '',\n    },\n};\nextend(doc, ssrDocument);\n\nvar win = typeof window !== 'undefined' ? window : {};\nvar ssrWindow = {\n    document: ssrDocument,\n    navigator: {\n        userAgent: '',\n    },\n    location: {\n        hash: '',\n        host: '',\n        hostname: '',\n        href: '',\n        origin: '',\n        pathname: '',\n        protocol: '',\n        search: '',\n    },\n    history: {\n        replaceState: function () { },\n        pushState: function () { },\n        go: function () { },\n        back: function () { },\n    },\n    CustomEvent: function CustomEvent() {\n        return this;\n    },\n    addEventListener: function () { },\n    removeEventListener: function () { },\n    getComputedStyle: function () {\n        return {\n            getPropertyValue: function () {\n                return '';\n            },\n        };\n    },\n    Image: function () { },\n    Date: function () { },\n    screen: {},\n    setTimeout: function () { },\n    clearTimeout: function () { },\n    matchMedia: function () {\n        return {};\n    },\n};\nextend(win, ssrWindow);\n\nexport { doc as document, extend, win as window };\n", "export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./BoonPop.vue?vue&type=style&index=0&id=c34714c8&prod&scoped=true&lang=scss\"", "module.exports = __webpack_public_path__ + \"static/**********/img/boon-login-award.c5699e31.png\";", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('container',{staticClass:\"arrears-reminder-wrapper\",class:[_vm.$i18n.locale, this.$store.state.gameinfo.gameCode, _vm.$gameName],attrs:{\"hide-close\":true,\"option\":_vm.config,\"title\":_vm.$vt('howToUseDiamond'),\"id\":\"what-is-diamond-wrapper\"}},[_c('div',{staticClass:\"description\"},[_vm._v(_vm._s(_vm.$vt('whatIsDiamondTitle'))+\" \")]),_c('poster-swiper')],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('Swiper',{staticClass:\"my-swiper-wrapper\",class:[_vm.$gameName],attrs:{\"options\":_vm.swiperOptions}},[(_vm.webCashierBanner.length)?[_vm._l((_vm.webCashierBanner),function(banner){return _c('SwiperSlide',{key:banner.imageUrl},[_c('img',{attrs:{\"src\":banner.imageUrl,\"alt\":\"\"}}),(banner.jumpUrl)?_c('a',{attrs:{\"href\":banner.jumpUrl,\"target\":\"_blank\"}}):_vm._e()])}),_c('div',{staticClass:\"swiper-pagination\",attrs:{\"slot\":\"pagination\"},slot:\"pagination\"},_vm._l((Object.keys(_vm.webCashierBanner)),function(index){return _c('div',{key:index,class:['pagination-dot', {'pagination-dot_active': _vm.swiperIndex === +index}]})}),0)]:_vm._e()],2)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <Swiper class=\"my-swiper-wrapper\" :class=\"[$gameName]\" :options=\"swiperOptions\">\n    <template v-if=\"webCashierBanner.length\">\n      <SwiperSlide v-for=\"banner in webCashierBanner\" :key=\"banner.imageUrl\">\n        <img :src=\"banner.imageUrl\" alt=\"\">\n        <a v-if=\"banner.jumpUrl\" :href=\"banner.jumpUrl\" target=\"_blank\"></a>\n      </SwiperSlide>\n\n      <div class=\"swiper-pagination\" slot=\"pagination\">\n        <div :class=\"['pagination-dot', {'pagination-dot_active': swiperIndex === +index}]\" :key=\"index\" v-for=\"index in Object.keys(webCashierBanner)\"></div>\n      </div>\n    </template>\n  </Swiper>\n</template>\n\n<script>\nimport { Swiper, SwiperSlide } from 'vue-awesome-swiper'\nimport 'swiper/css/swiper.min.css'\nexport default {\n  name: 'PosterSwiper',\n  data () {\n    const instance = this\n    return {\n      swiperOptions: {\n        autoplay: {\n          disableOnInteraction: false\n        },\n        on: {\n          slideChangeTransitionStart: function () {\n            instance.swiperIndex = this.activeIndex\n          }\n        }\n      },\n      swiperIndex: 0,\n      webCashierBanner: this.$imageLoader('whatsDiamond', [])\n    }\n  },\n  components: {\n    Swiper,\n    SwiperSlide\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n@use \"~@/utils/utils.scss\" as utils;\n\n.my-swiper-wrapper {\n  border-radius: 4px;\n  overflow: hidden;\n  position: relative;\n  padding-bottom: 18px;\n\n  ::v-deep {\n    .swiper-slide {\n      overflow: hidden;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      img {\n        width: 100%;\n        display: inline-block;\n      }\n\n      a {\n        position: absolute;\n        left: 0;\n        top: 0;\n        height: 100%;\n        width: 100%;\n        z-index: 10;\n        display: inline-block;\n      }\n    }\n  }\n\n  .swiper-pagination {\n    position: absolute;\n    z-index: 10;\n    display: flex;\n    align-items: center;\n    bottom: 0;\n    left: 50%;\n    transform: translate(-50%, 0);\n\n    .pagination-dot {\n      width: 8px;\n      height: 8px;\n      background: #6F6F6F;\n      border-radius: 50%;\n      margin: 0 3px;\n    }\n\n    .pagination-dot_active {\n      background: #C0C0C0;;\n    }\n  }\n\n  @include utils.setPcContent{\n    padding-bottom: 17PX;\n    border-radius: 4PX;\n\n    .swiper-pagination{\n\n      .pagination-dot{\n        height: 7PX;\n        width: 7PX;\n      }\n    }\n  }\n}\n\n.my-swiper-wrapper.dc{\n  border-radius: 0;\n\n  .swiper-pagination {\n    .pagination-dot_active {\n      background: #525280;;\n    }\n  }\n}\n</style>\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./PosterSwiper.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./PosterSwiper.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./PosterSwiper.vue?vue&type=template&id=f793779a&scoped=true\"\nimport script from \"./PosterSwiper.vue?vue&type=script&lang=js\"\nexport * from \"./PosterSwiper.vue?vue&type=script&lang=js\"\nimport style0 from \"./PosterSwiper.vue?vue&type=style&index=0&id=f793779a&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"f793779a\",\n  null\n  \n)\n\nexport default component.exports", "<template>\n  <container :hide-close=\"true\" :option=\"config\" :title=\"$vt('howToUseDiamond')\" class=\"arrears-reminder-wrapper\" :class=\"[$i18n.locale, this.$store.state.gameinfo.gameCode, $gameName]\" id=\"what-is-diamond-wrapper\">\n    <div class=\"description\">{{ $vt('whatIsDiamondTitle') }} </div>\n    <poster-swiper></poster-swiper>\n  </container>\n</template>\n\n<script>\nimport Container from '@/components/pop/container'\nimport PosterSwiper from '@/components/PosterSwiper'\nexport default {\n  name: 'WhatIsDiamond',\n  props: {\n    option: Object\n  },\n  data () {\n    return {\n      config: {\n        confirmBtnTxt: this.$t('got-it')\n      }\n    }\n  },\n  components: { PosterSwiper, Container }\n}\n</script>\n\n<style scoped lang=\"scss\">\n@use \"~@/utils/utils.scss\" as utils;\n\n.arrears-reminder-wrapper {\n  box-shadow: 0px 2px 12px 0px rgba(0,0,0,0.25);\n  border-radius: 20px;\n  background:rgba(56, 56, 56, 1);\n  padding-top: 14px;\n  padding-bottom: 22px;\n\n  .description {\n    font-size: 20px;\n    font-family: PingFangSC-Regular, PingFang SC;\n    font-weight: 400;\n    line-height: 28px;\n    margin-bottom: 12px;\n    color: lighten(#999999, 30);\n  }\n\n  ::v-deep{\n    .content{\n      margin-top: 9px;\n      padding: 0 44px;\n    }\n    .footer-wrapper{\n      .btn-confirm{\n        margin-top: 26px;\n      }\n    }\n  }\n\n  @include utils.setPcContent{\n    padding-top: 21PX;\n\n    .description{\n      font-size: 16PX;\n      line-height: 22PX;\n      margin-bottom: 10PX;\n      padding-bottom: 21PX;\n    }\n\n    ::v-deep{\n      .title{\n        font-size: 22PX;\n        line-height: 30PX;\n      }\n      .content{\n        margin-top: 10PX;\n        padding: 0 56PX;\n      }\n      //.footer-wrapper{\n      //  .btn-confirm{\n      //    margin-top: 26px;\n      //  }\n      //}\n    }\n  }\n}\n\n.KOA{\n  background: rgb(44, 55, 87);\n  border-radius: 0;\n\n  @include utils.setPcContent{\n    border: 1PX solid rgb(72, 96, 131);\n  }\n}\n\n.arrears-reminder-wrapper.dc{\n  border: 1px solid #979797;\n  border-radius: 0;\n  box-shadow: none;\n\n  .description{\n    color: #525280;\n  }\n}\n</style>\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./WhatIsDiamond.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./WhatIsDiamond.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./WhatIsDiamond.vue?vue&type=template&id=1b49d88b&scoped=true\"\nimport script from \"./WhatIsDiamond.vue?vue&type=script&lang=js\"\nexport * from \"./WhatIsDiamond.vue?vue&type=script&lang=js\"\nimport style0 from \"./WhatIsDiamond.vue?vue&type=style&index=0&id=1b49d88b&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1b49d88b\",\n  null\n  \n)\n\nexport default component.exports", "module.exports = __webpack_public_path__ + \"static/**********/img/install_reward_2.9f56aa98.png\";", "/**\n * Dom7 2.1.5\n * Minimalistic JavaScript library for DOM manipulation, with a jQuery-compatible API\n * http://framework7.io/docs/dom.html\n *\n * Copyright 2020, <PERSON>\n * The iDangero.us\n * http://www.idangero.us/\n *\n * Licensed under MIT\n *\n * Released on: May 15, 2020\n */\nimport { document, window } from 'ssr-window';\n\nclass Dom7 {\n  constructor(arr) {\n    const self = this;\n    // Create array-like object\n    for (let i = 0; i < arr.length; i += 1) {\n      self[i] = arr[i];\n    }\n    self.length = arr.length;\n    // Return collection with methods\n    return this;\n  }\n}\n\nfunction $(selector, context) {\n  const arr = [];\n  let i = 0;\n  if (selector && !context) {\n    if (selector instanceof Dom7) {\n      return selector;\n    }\n  }\n  if (selector) {\n      // String\n    if (typeof selector === 'string') {\n      let els;\n      let tempParent;\n      const html = selector.trim();\n      if (html.indexOf('<') >= 0 && html.indexOf('>') >= 0) {\n        let toCreate = 'div';\n        if (html.indexOf('<li') === 0) toCreate = 'ul';\n        if (html.indexOf('<tr') === 0) toCreate = 'tbody';\n        if (html.indexOf('<td') === 0 || html.indexOf('<th') === 0) toCreate = 'tr';\n        if (html.indexOf('<tbody') === 0) toCreate = 'table';\n        if (html.indexOf('<option') === 0) toCreate = 'select';\n        tempParent = document.createElement(toCreate);\n        tempParent.innerHTML = html;\n        for (i = 0; i < tempParent.childNodes.length; i += 1) {\n          arr.push(tempParent.childNodes[i]);\n        }\n      } else {\n        if (!context && selector[0] === '#' && !selector.match(/[ .<>:~]/)) {\n          // Pure ID selector\n          els = [document.getElementById(selector.trim().split('#')[1])];\n        } else {\n          // Other selectors\n          els = (context || document).querySelectorAll(selector.trim());\n        }\n        for (i = 0; i < els.length; i += 1) {\n          if (els[i]) arr.push(els[i]);\n        }\n      }\n    } else if (selector.nodeType || selector === window || selector === document) {\n      // Node/element\n      arr.push(selector);\n    } else if (selector.length > 0 && selector[0].nodeType) {\n      // Array of elements or instance of Dom\n      for (i = 0; i < selector.length; i += 1) {\n        arr.push(selector[i]);\n      }\n    }\n  }\n  return new Dom7(arr);\n}\n\n$.fn = Dom7.prototype;\n$.Class = Dom7;\n$.Dom7 = Dom7;\n\nfunction unique(arr) {\n  const uniqueArray = [];\n  for (let i = 0; i < arr.length; i += 1) {\n    if (uniqueArray.indexOf(arr[i]) === -1) uniqueArray.push(arr[i]);\n  }\n  return uniqueArray;\n}\nfunction toCamelCase(string) {\n  return string.toLowerCase().replace(/-(.)/g, (match, group1) => group1.toUpperCase());\n}\n\nfunction requestAnimationFrame(callback) {\n  if (window.requestAnimationFrame) return window.requestAnimationFrame(callback);\n  else if (window.webkitRequestAnimationFrame) return window.webkitRequestAnimationFrame(callback);\n  return window.setTimeout(callback, 1000 / 60);\n}\nfunction cancelAnimationFrame(id) {\n  if (window.cancelAnimationFrame) return window.cancelAnimationFrame(id);\n  else if (window.webkitCancelAnimationFrame) return window.webkitCancelAnimationFrame(id);\n  return window.clearTimeout(id);\n}\n\n// Classes and attributes\nfunction addClass(className) {\n  if (typeof className === 'undefined') {\n    return this;\n  }\n  const classes = className.split(' ');\n  for (let i = 0; i < classes.length; i += 1) {\n    for (let j = 0; j < this.length; j += 1) {\n      if (typeof this[j] !== 'undefined' && typeof this[j].classList !== 'undefined') this[j].classList.add(classes[i]);\n    }\n  }\n  return this;\n}\nfunction removeClass(className) {\n  const classes = className.split(' ');\n  for (let i = 0; i < classes.length; i += 1) {\n    for (let j = 0; j < this.length; j += 1) {\n      if (typeof this[j] !== 'undefined' && typeof this[j].classList !== 'undefined') this[j].classList.remove(classes[i]);\n    }\n  }\n  return this;\n}\nfunction hasClass(className) {\n  if (!this[0]) return false;\n  return this[0].classList.contains(className);\n}\nfunction toggleClass(className) {\n  const classes = className.split(' ');\n  for (let i = 0; i < classes.length; i += 1) {\n    for (let j = 0; j < this.length; j += 1) {\n      if (typeof this[j] !== 'undefined' && typeof this[j].classList !== 'undefined') this[j].classList.toggle(classes[i]);\n    }\n  }\n  return this;\n}\nfunction attr(attrs, value) {\n  if (arguments.length === 1 && typeof attrs === 'string') {\n    // Get attr\n    if (this[0]) return this[0].getAttribute(attrs);\n    return undefined;\n  }\n\n  // Set attrs\n  for (let i = 0; i < this.length; i += 1) {\n    if (arguments.length === 2) {\n      // String\n      this[i].setAttribute(attrs, value);\n    } else {\n      // Object\n      // eslint-disable-next-line\n      for (const attrName in attrs) {\n        this[i][attrName] = attrs[attrName];\n        this[i].setAttribute(attrName, attrs[attrName]);\n      }\n    }\n  }\n  return this;\n}\n// eslint-disable-next-line\nfunction removeAttr(attr) {\n  for (let i = 0; i < this.length; i += 1) {\n    this[i].removeAttribute(attr);\n  }\n  return this;\n}\n// eslint-disable-next-line\nfunction prop(props, value) {\n  if (arguments.length === 1 && typeof props === 'string') {\n    // Get prop\n    if (this[0]) return this[0][props];\n  } else {\n    // Set props\n    for (let i = 0; i < this.length; i += 1) {\n      if (arguments.length === 2) {\n        // String\n        this[i][props] = value;\n      } else {\n        // Object\n        // eslint-disable-next-line\n        for (const propName in props) {\n          this[i][propName] = props[propName];\n        }\n      }\n    }\n    return this;\n  }\n}\nfunction data(key, value) {\n  let el;\n  if (typeof value === 'undefined') {\n    el = this[0];\n    // Get value\n    if (el) {\n      if (el.dom7ElementDataStorage && (key in el.dom7ElementDataStorage)) {\n        return el.dom7ElementDataStorage[key];\n      }\n\n      const dataKey = el.getAttribute(`data-${key}`);\n      if (dataKey) {\n        return dataKey;\n      }\n      return undefined;\n    }\n    return undefined;\n  }\n\n  // Set value\n  for (let i = 0; i < this.length; i += 1) {\n    el = this[i];\n    if (!el.dom7ElementDataStorage) el.dom7ElementDataStorage = {};\n    el.dom7ElementDataStorage[key] = value;\n  }\n  return this;\n}\nfunction removeData(key) {\n  for (let i = 0; i < this.length; i += 1) {\n    const el = this[i];\n    if (el.dom7ElementDataStorage && el.dom7ElementDataStorage[key]) {\n      el.dom7ElementDataStorage[key] = null;\n      delete el.dom7ElementDataStorage[key];\n    }\n  }\n}\nfunction dataset() {\n  const el = this[0];\n  if (!el) return undefined;\n  const dataset = {}; // eslint-disable-line\n  if (el.dataset) {\n    // eslint-disable-next-line\n    for (const dataKey in el.dataset) {\n      dataset[dataKey] = el.dataset[dataKey];\n    }\n  } else {\n    for (let i = 0; i < el.attributes.length; i += 1) {\n      // eslint-disable-next-line\n      const attr = el.attributes[i];\n      if (attr.name.indexOf('data-') >= 0) {\n        dataset[toCamelCase(attr.name.split('data-')[1])] = attr.value;\n      }\n    }\n  }\n  // eslint-disable-next-line\n  for (const key in dataset) {\n    if (dataset[key] === 'false') dataset[key] = false;\n    else if (dataset[key] === 'true') dataset[key] = true;\n    else if (parseFloat(dataset[key]) === dataset[key] * 1) dataset[key] *= 1;\n  }\n  return dataset;\n}\nfunction val(value) {\n  const dom = this;\n  if (typeof value === 'undefined') {\n    if (dom[0]) {\n      if (dom[0].multiple && dom[0].nodeName.toLowerCase() === 'select') {\n        const values = [];\n        for (let i = 0; i < dom[0].selectedOptions.length; i += 1) {\n          values.push(dom[0].selectedOptions[i].value);\n        }\n        return values;\n      }\n      return dom[0].value;\n    }\n    return undefined;\n  }\n\n  for (let i = 0; i < dom.length; i += 1) {\n    const el = dom[i];\n    if (Array.isArray(value) && el.multiple && el.nodeName.toLowerCase() === 'select') {\n      for (let j = 0; j < el.options.length; j += 1) {\n        el.options[j].selected = value.indexOf(el.options[j].value) >= 0;\n      }\n    } else {\n      el.value = value;\n    }\n  }\n  return dom;\n}\n// Transforms\n// eslint-disable-next-line\nfunction transform(transform) {\n  for (let i = 0; i < this.length; i += 1) {\n    const elStyle = this[i].style;\n    elStyle.webkitTransform = transform;\n    elStyle.transform = transform;\n  }\n  return this;\n}\nfunction transition(duration) {\n  if (typeof duration !== 'string') {\n    duration = `${duration}ms`; // eslint-disable-line\n  }\n  for (let i = 0; i < this.length; i += 1) {\n    const elStyle = this[i].style;\n    elStyle.webkitTransitionDuration = duration;\n    elStyle.transitionDuration = duration;\n  }\n  return this;\n}\n// Events\nfunction on(...args) {\n  let [eventType, targetSelector, listener, capture] = args;\n  if (typeof args[1] === 'function') {\n    [eventType, listener, capture] = args;\n    targetSelector = undefined;\n  }\n  if (!capture) capture = false;\n\n  function handleLiveEvent(e) {\n    const target = e.target;\n    if (!target) return;\n    const eventData = e.target.dom7EventData || [];\n    if (eventData.indexOf(e) < 0) {\n      eventData.unshift(e);\n    }\n    if ($(target).is(targetSelector)) listener.apply(target, eventData);\n    else {\n      const parents = $(target).parents(); // eslint-disable-line\n      for (let k = 0; k < parents.length; k += 1) {\n        if ($(parents[k]).is(targetSelector)) listener.apply(parents[k], eventData);\n      }\n    }\n  }\n  function handleEvent(e) {\n    const eventData = e && e.target ? e.target.dom7EventData || [] : [];\n    if (eventData.indexOf(e) < 0) {\n      eventData.unshift(e);\n    }\n    listener.apply(this, eventData);\n  }\n  const events = eventType.split(' ');\n  let j;\n  for (let i = 0; i < this.length; i += 1) {\n    const el = this[i];\n    if (!targetSelector) {\n      for (j = 0; j < events.length; j += 1) {\n        const event = events[j];\n        if (!el.dom7Listeners) el.dom7Listeners = {};\n        if (!el.dom7Listeners[event]) el.dom7Listeners[event] = [];\n        el.dom7Listeners[event].push({\n          listener,\n          proxyListener: handleEvent,\n        });\n        el.addEventListener(event, handleEvent, capture);\n      }\n    } else {\n      // Live events\n      for (j = 0; j < events.length; j += 1) {\n        const event = events[j];\n        if (!el.dom7LiveListeners) el.dom7LiveListeners = {};\n        if (!el.dom7LiveListeners[event]) el.dom7LiveListeners[event] = [];\n        el.dom7LiveListeners[event].push({\n          listener,\n          proxyListener: handleLiveEvent,\n        });\n        el.addEventListener(event, handleLiveEvent, capture);\n      }\n    }\n  }\n  return this;\n}\nfunction off(...args) {\n  let [eventType, targetSelector, listener, capture] = args;\n  if (typeof args[1] === 'function') {\n    [eventType, listener, capture] = args;\n    targetSelector = undefined;\n  }\n  if (!capture) capture = false;\n\n  const events = eventType.split(' ');\n  for (let i = 0; i < events.length; i += 1) {\n    const event = events[i];\n    for (let j = 0; j < this.length; j += 1) {\n      const el = this[j];\n      let handlers;\n      if (!targetSelector && el.dom7Listeners) {\n        handlers = el.dom7Listeners[event];\n      } else if (targetSelector && el.dom7LiveListeners) {\n        handlers = el.dom7LiveListeners[event];\n      }\n      if (handlers && handlers.length) {\n        for (let k = handlers.length - 1; k >= 0; k -= 1) {\n          const handler = handlers[k];\n          if (listener && handler.listener === listener) {\n            el.removeEventListener(event, handler.proxyListener, capture);\n            handlers.splice(k, 1);\n          } else if (listener && handler.listener && handler.listener.dom7proxy && handler.listener.dom7proxy === listener) {\n            el.removeEventListener(event, handler.proxyListener, capture);\n            handlers.splice(k, 1);\n          } else if (!listener) {\n            el.removeEventListener(event, handler.proxyListener, capture);\n            handlers.splice(k, 1);\n          }\n        }\n      }\n    }\n  }\n  return this;\n}\nfunction once(...args) {\n  const dom = this;\n  let [eventName, targetSelector, listener, capture] = args;\n  if (typeof args[1] === 'function') {\n    [eventName, listener, capture] = args;\n    targetSelector = undefined;\n  }\n  function onceHandler(...eventArgs) {\n    listener.apply(this, eventArgs);\n    dom.off(eventName, targetSelector, onceHandler, capture);\n    if (onceHandler.dom7proxy) {\n      delete onceHandler.dom7proxy;\n    }\n  }\n  onceHandler.dom7proxy = listener;\n  return dom.on(eventName, targetSelector, onceHandler, capture);\n}\nfunction trigger(...args) {\n  const events = args[0].split(' ');\n  const eventData = args[1];\n  for (let i = 0; i < events.length; i += 1) {\n    const event = events[i];\n    for (let j = 0; j < this.length; j += 1) {\n      const el = this[j];\n      let evt;\n      try {\n        evt = new window.CustomEvent(event, {\n          detail: eventData,\n          bubbles: true,\n          cancelable: true,\n        });\n      } catch (e) {\n        evt = document.createEvent('Event');\n        evt.initEvent(event, true, true);\n        evt.detail = eventData;\n      }\n      // eslint-disable-next-line\n      el.dom7EventData = args.filter((data, dataIndex) => dataIndex > 0);\n      el.dispatchEvent(evt);\n      el.dom7EventData = [];\n      delete el.dom7EventData;\n    }\n  }\n  return this;\n}\nfunction transitionEnd(callback) {\n  const events = ['webkitTransitionEnd', 'transitionend'];\n  const dom = this;\n  let i;\n  function fireCallBack(e) {\n    /* jshint validthis:true */\n    if (e.target !== this) return;\n    callback.call(this, e);\n    for (i = 0; i < events.length; i += 1) {\n      dom.off(events[i], fireCallBack);\n    }\n  }\n  if (callback) {\n    for (i = 0; i < events.length; i += 1) {\n      dom.on(events[i], fireCallBack);\n    }\n  }\n  return this;\n}\nfunction animationEnd(callback) {\n  const events = ['webkitAnimationEnd', 'animationend'];\n  const dom = this;\n  let i;\n  function fireCallBack(e) {\n    if (e.target !== this) return;\n    callback.call(this, e);\n    for (i = 0; i < events.length; i += 1) {\n      dom.off(events[i], fireCallBack);\n    }\n  }\n  if (callback) {\n    for (i = 0; i < events.length; i += 1) {\n      dom.on(events[i], fireCallBack);\n    }\n  }\n  return this;\n}\n// Sizing/Styles\nfunction width() {\n  if (this[0] === window) {\n    return window.innerWidth;\n  }\n\n  if (this.length > 0) {\n    return parseFloat(this.css('width'));\n  }\n\n  return null;\n}\nfunction outerWidth(includeMargins) {\n  if (this.length > 0) {\n    if (includeMargins) {\n      // eslint-disable-next-line\n      const styles = this.styles();\n      return this[0].offsetWidth + parseFloat(styles.getPropertyValue('margin-right')) + parseFloat(styles.getPropertyValue('margin-left'));\n    }\n    return this[0].offsetWidth;\n  }\n  return null;\n}\nfunction height() {\n  if (this[0] === window) {\n    return window.innerHeight;\n  }\n\n  if (this.length > 0) {\n    return parseFloat(this.css('height'));\n  }\n\n  return null;\n}\nfunction outerHeight(includeMargins) {\n  if (this.length > 0) {\n    if (includeMargins) {\n      // eslint-disable-next-line\n      const styles = this.styles();\n      return this[0].offsetHeight + parseFloat(styles.getPropertyValue('margin-top')) + parseFloat(styles.getPropertyValue('margin-bottom'));\n    }\n    return this[0].offsetHeight;\n  }\n  return null;\n}\nfunction offset() {\n  if (this.length > 0) {\n    const el = this[0];\n    const box = el.getBoundingClientRect();\n    const body = document.body;\n    const clientTop = el.clientTop || body.clientTop || 0;\n    const clientLeft = el.clientLeft || body.clientLeft || 0;\n    const scrollTop = el === window ? window.scrollY : el.scrollTop;\n    const scrollLeft = el === window ? window.scrollX : el.scrollLeft;\n    return {\n      top: (box.top + scrollTop) - clientTop,\n      left: (box.left + scrollLeft) - clientLeft,\n    };\n  }\n\n  return null;\n}\nfunction hide() {\n  for (let i = 0; i < this.length; i += 1) {\n    this[i].style.display = 'none';\n  }\n  return this;\n}\nfunction show() {\n  for (let i = 0; i < this.length; i += 1) {\n    const el = this[i];\n    if (el.style.display === 'none') {\n      el.style.display = '';\n    }\n    if (window.getComputedStyle(el, null).getPropertyValue('display') === 'none') {\n      // Still not visible\n      el.style.display = 'block';\n    }\n  }\n  return this;\n}\nfunction styles() {\n  if (this[0]) return window.getComputedStyle(this[0], null);\n  return {};\n}\nfunction css(props, value) {\n  let i;\n  if (arguments.length === 1) {\n    if (typeof props === 'string') {\n      if (this[0]) return window.getComputedStyle(this[0], null).getPropertyValue(props);\n    } else {\n      for (i = 0; i < this.length; i += 1) {\n        // eslint-disable-next-line\n        for (let prop in props) {\n          this[i].style[prop] = props[prop];\n        }\n      }\n      return this;\n    }\n  }\n  if (arguments.length === 2 && typeof props === 'string') {\n    for (i = 0; i < this.length; i += 1) {\n      this[i].style[props] = value;\n    }\n    return this;\n  }\n  return this;\n}\n\n// Dom manipulation\nfunction toArray() {\n  const arr = [];\n  for (let i = 0; i < this.length; i += 1) {\n    arr.push(this[i]);\n  }\n  return arr;\n}\n// Iterate over the collection passing elements to `callback`\nfunction each(callback) {\n  // Don't bother continuing without a callback\n  if (!callback) return this;\n  // Iterate over the current collection\n  for (let i = 0; i < this.length; i += 1) {\n    // If the callback returns false\n    if (callback.call(this[i], i, this[i]) === false) {\n      // End the loop early\n      return this;\n    }\n  }\n  // Return `this` to allow chained DOM operations\n  return this;\n}\nfunction forEach(callback) {\n  // Don't bother continuing without a callback\n  if (!callback) return this;\n  // Iterate over the current collection\n  for (let i = 0; i < this.length; i += 1) {\n    // If the callback returns false\n    if (callback.call(this[i], this[i], i) === false) {\n      // End the loop early\n      return this;\n    }\n  }\n  // Return `this` to allow chained DOM operations\n  return this;\n}\nfunction filter(callback) {\n  const matchedItems = [];\n  const dom = this;\n  for (let i = 0; i < dom.length; i += 1) {\n    if (callback.call(dom[i], i, dom[i])) matchedItems.push(dom[i]);\n  }\n  return new Dom7(matchedItems);\n}\nfunction map(callback) {\n  const modifiedItems = [];\n  const dom = this;\n  for (let i = 0; i < dom.length; i += 1) {\n    modifiedItems.push(callback.call(dom[i], i, dom[i]));\n  }\n  return new Dom7(modifiedItems);\n}\n// eslint-disable-next-line\nfunction html(html) {\n  if (typeof html === 'undefined') {\n    return this[0] ? this[0].innerHTML : undefined;\n  }\n\n  for (let i = 0; i < this.length; i += 1) {\n    this[i].innerHTML = html;\n  }\n  return this;\n}\n// eslint-disable-next-line\nfunction text(text) {\n  if (typeof text === 'undefined') {\n    if (this[0]) {\n      return this[0].textContent.trim();\n    }\n    return null;\n  }\n\n  for (let i = 0; i < this.length; i += 1) {\n    this[i].textContent = text;\n  }\n  return this;\n}\nfunction is(selector) {\n  const el = this[0];\n  let compareWith;\n  let i;\n  if (!el || typeof selector === 'undefined') return false;\n  if (typeof selector === 'string') {\n    if (el.matches) return el.matches(selector);\n    else if (el.webkitMatchesSelector) return el.webkitMatchesSelector(selector);\n    else if (el.msMatchesSelector) return el.msMatchesSelector(selector);\n\n    compareWith = $(selector);\n    for (i = 0; i < compareWith.length; i += 1) {\n      if (compareWith[i] === el) return true;\n    }\n    return false;\n  } else if (selector === document) return el === document;\n  else if (selector === window) return el === window;\n\n  if (selector.nodeType || selector instanceof Dom7) {\n    compareWith = selector.nodeType ? [selector] : selector;\n    for (i = 0; i < compareWith.length; i += 1) {\n      if (compareWith[i] === el) return true;\n    }\n    return false;\n  }\n  return false;\n}\nfunction indexOf(el) {\n  for (let i = 0; i < this.length; i += 1) {\n    if (this[i] === el) return i;\n  }\n  return -1;\n}\nfunction index() {\n  let child = this[0];\n  let i;\n  if (child) {\n    i = 0;\n    // eslint-disable-next-line\n    while ((child = child.previousSibling) !== null) {\n      if (child.nodeType === 1) i += 1;\n    }\n    return i;\n  }\n  return undefined;\n}\n// eslint-disable-next-line\nfunction eq(index) {\n  if (typeof index === 'undefined') return this;\n  const length = this.length;\n  let returnIndex;\n  if (index > length - 1) {\n    return new Dom7([]);\n  }\n  if (index < 0) {\n    returnIndex = length + index;\n    if (returnIndex < 0) return new Dom7([]);\n    return new Dom7([this[returnIndex]]);\n  }\n  return new Dom7([this[index]]);\n}\nfunction append(...args) {\n  let newChild;\n\n  for (let k = 0; k < args.length; k += 1) {\n    newChild = args[k];\n    for (let i = 0; i < this.length; i += 1) {\n      if (typeof newChild === 'string') {\n        const tempDiv = document.createElement('div');\n        tempDiv.innerHTML = newChild;\n        while (tempDiv.firstChild) {\n          this[i].appendChild(tempDiv.firstChild);\n        }\n      } else if (newChild instanceof Dom7) {\n        for (let j = 0; j < newChild.length; j += 1) {\n          this[i].appendChild(newChild[j]);\n        }\n      } else {\n        this[i].appendChild(newChild);\n      }\n    }\n  }\n\n  return this;\n}\n// eslint-disable-next-line\nfunction appendTo(parent) {\n  $(parent).append(this);\n  return this;\n}\nfunction prepend(newChild) {\n  let i;\n  let j;\n  for (i = 0; i < this.length; i += 1) {\n    if (typeof newChild === 'string') {\n      const tempDiv = document.createElement('div');\n      tempDiv.innerHTML = newChild;\n      for (j = tempDiv.childNodes.length - 1; j >= 0; j -= 1) {\n        this[i].insertBefore(tempDiv.childNodes[j], this[i].childNodes[0]);\n      }\n    } else if (newChild instanceof Dom7) {\n      for (j = 0; j < newChild.length; j += 1) {\n        this[i].insertBefore(newChild[j], this[i].childNodes[0]);\n      }\n    } else {\n      this[i].insertBefore(newChild, this[i].childNodes[0]);\n    }\n  }\n  return this;\n}\n// eslint-disable-next-line\nfunction prependTo(parent) {\n  $(parent).prepend(this);\n  return this;\n}\nfunction insertBefore(selector) {\n  const before = $(selector);\n  for (let i = 0; i < this.length; i += 1) {\n    if (before.length === 1) {\n      before[0].parentNode.insertBefore(this[i], before[0]);\n    } else if (before.length > 1) {\n      for (let j = 0; j < before.length; j += 1) {\n        before[j].parentNode.insertBefore(this[i].cloneNode(true), before[j]);\n      }\n    }\n  }\n}\nfunction insertAfter(selector) {\n  const after = $(selector);\n  for (let i = 0; i < this.length; i += 1) {\n    if (after.length === 1) {\n      after[0].parentNode.insertBefore(this[i], after[0].nextSibling);\n    } else if (after.length > 1) {\n      for (let j = 0; j < after.length; j += 1) {\n        after[j].parentNode.insertBefore(this[i].cloneNode(true), after[j].nextSibling);\n      }\n    }\n  }\n}\nfunction next(selector) {\n  if (this.length > 0) {\n    if (selector) {\n      if (this[0].nextElementSibling && $(this[0].nextElementSibling).is(selector)) {\n        return new Dom7([this[0].nextElementSibling]);\n      }\n      return new Dom7([]);\n    }\n\n    if (this[0].nextElementSibling) return new Dom7([this[0].nextElementSibling]);\n    return new Dom7([]);\n  }\n  return new Dom7([]);\n}\nfunction nextAll(selector) {\n  const nextEls = [];\n  let el = this[0];\n  if (!el) return new Dom7([]);\n  while (el.nextElementSibling) {\n    const next = el.nextElementSibling; // eslint-disable-line\n    if (selector) {\n      if ($(next).is(selector)) nextEls.push(next);\n    } else nextEls.push(next);\n    el = next;\n  }\n  return new Dom7(nextEls);\n}\nfunction prev(selector) {\n  if (this.length > 0) {\n    const el = this[0];\n    if (selector) {\n      if (el.previousElementSibling && $(el.previousElementSibling).is(selector)) {\n        return new Dom7([el.previousElementSibling]);\n      }\n      return new Dom7([]);\n    }\n\n    if (el.previousElementSibling) return new Dom7([el.previousElementSibling]);\n    return new Dom7([]);\n  }\n  return new Dom7([]);\n}\nfunction prevAll(selector) {\n  const prevEls = [];\n  let el = this[0];\n  if (!el) return new Dom7([]);\n  while (el.previousElementSibling) {\n    const prev = el.previousElementSibling; // eslint-disable-line\n    if (selector) {\n      if ($(prev).is(selector)) prevEls.push(prev);\n    } else prevEls.push(prev);\n    el = prev;\n  }\n  return new Dom7(prevEls);\n}\nfunction siblings(selector) {\n  return this.nextAll(selector).add(this.prevAll(selector));\n}\nfunction parent(selector) {\n  const parents = []; // eslint-disable-line\n  for (let i = 0; i < this.length; i += 1) {\n    if (this[i].parentNode !== null) {\n      if (selector) {\n        if ($(this[i].parentNode).is(selector)) parents.push(this[i].parentNode);\n      } else {\n        parents.push(this[i].parentNode);\n      }\n    }\n  }\n  return $(unique(parents));\n}\nfunction parents(selector) {\n  const parents = []; // eslint-disable-line\n  for (let i = 0; i < this.length; i += 1) {\n    let parent = this[i].parentNode; // eslint-disable-line\n    while (parent) {\n      if (selector) {\n        if ($(parent).is(selector)) parents.push(parent);\n      } else {\n        parents.push(parent);\n      }\n      parent = parent.parentNode;\n    }\n  }\n  return $(unique(parents));\n}\nfunction closest(selector) {\n  let closest = this; // eslint-disable-line\n  if (typeof selector === 'undefined') {\n    return new Dom7([]);\n  }\n  if (!closest.is(selector)) {\n    closest = closest.parents(selector).eq(0);\n  }\n  return closest;\n}\nfunction find(selector) {\n  const foundElements = [];\n  for (let i = 0; i < this.length; i += 1) {\n    const found = this[i].querySelectorAll(selector);\n    for (let j = 0; j < found.length; j += 1) {\n      foundElements.push(found[j]);\n    }\n  }\n  return new Dom7(foundElements);\n}\nfunction children(selector) {\n  const children = []; // eslint-disable-line\n  for (let i = 0; i < this.length; i += 1) {\n    const childNodes = this[i].childNodes;\n\n    for (let j = 0; j < childNodes.length; j += 1) {\n      if (!selector) {\n        if (childNodes[j].nodeType === 1) children.push(childNodes[j]);\n      } else if (childNodes[j].nodeType === 1 && $(childNodes[j]).is(selector)) {\n        children.push(childNodes[j]);\n      }\n    }\n  }\n  return new Dom7(unique(children));\n}\nfunction remove() {\n  for (let i = 0; i < this.length; i += 1) {\n    if (this[i].parentNode) this[i].parentNode.removeChild(this[i]);\n  }\n  return this;\n}\nfunction detach() {\n  return this.remove();\n}\nfunction add(...args) {\n  const dom = this;\n  let i;\n  let j;\n  for (i = 0; i < args.length; i += 1) {\n    const toAdd = $(args[i]);\n    for (j = 0; j < toAdd.length; j += 1) {\n      dom[dom.length] = toAdd[j];\n      dom.length += 1;\n    }\n  }\n  return dom;\n}\nfunction empty() {\n  for (let i = 0; i < this.length; i += 1) {\n    const el = this[i];\n    if (el.nodeType === 1) {\n      for (let j = 0; j < el.childNodes.length; j += 1) {\n        if (el.childNodes[j].parentNode) {\n          el.childNodes[j].parentNode.removeChild(el.childNodes[j]);\n        }\n      }\n      el.textContent = '';\n    }\n  }\n  return this;\n}\n\nfunction scrollTo(...args) {\n  let [left, top, duration, easing, callback] = args;\n  if (args.length === 4 && typeof easing === 'function') {\n    callback = easing;\n    [left, top, duration, callback, easing] = args;\n  }\n  if (typeof easing === 'undefined') easing = 'swing';\n\n  return this.each(function animate() {\n    const el = this;\n    let currentTop;\n    let currentLeft;\n    let maxTop;\n    let maxLeft;\n    let newTop;\n    let newLeft;\n    let scrollTop; // eslint-disable-line\n    let scrollLeft; // eslint-disable-line\n    let animateTop = top > 0 || top === 0;\n    let animateLeft = left > 0 || left === 0;\n    if (typeof easing === 'undefined') {\n      easing = 'swing';\n    }\n    if (animateTop) {\n      currentTop = el.scrollTop;\n      if (!duration) {\n        el.scrollTop = top;\n      }\n    }\n    if (animateLeft) {\n      currentLeft = el.scrollLeft;\n      if (!duration) {\n        el.scrollLeft = left;\n      }\n    }\n    if (!duration) return;\n    if (animateTop) {\n      maxTop = el.scrollHeight - el.offsetHeight;\n      newTop = Math.max(Math.min(top, maxTop), 0);\n    }\n    if (animateLeft) {\n      maxLeft = el.scrollWidth - el.offsetWidth;\n      newLeft = Math.max(Math.min(left, maxLeft), 0);\n    }\n    let startTime = null;\n    if (animateTop && newTop === currentTop) animateTop = false;\n    if (animateLeft && newLeft === currentLeft) animateLeft = false;\n    function render(time = new Date().getTime()) {\n      if (startTime === null) {\n        startTime = time;\n      }\n      const progress = Math.max(Math.min((time - startTime) / duration, 1), 0);\n      const easeProgress = easing === 'linear' ? progress : (0.5 - (Math.cos(progress * Math.PI) / 2));\n      let done;\n      if (animateTop) scrollTop = currentTop + (easeProgress * (newTop - currentTop));\n      if (animateLeft) scrollLeft = currentLeft + (easeProgress * (newLeft - currentLeft));\n      if (animateTop && newTop > currentTop && scrollTop >= newTop) {\n        el.scrollTop = newTop;\n        done = true;\n      }\n      if (animateTop && newTop < currentTop && scrollTop <= newTop) {\n        el.scrollTop = newTop;\n        done = true;\n      }\n      if (animateLeft && newLeft > currentLeft && scrollLeft >= newLeft) {\n        el.scrollLeft = newLeft;\n        done = true;\n      }\n      if (animateLeft && newLeft < currentLeft && scrollLeft <= newLeft) {\n        el.scrollLeft = newLeft;\n        done = true;\n      }\n\n      if (done) {\n        if (callback) callback();\n        return;\n      }\n      if (animateTop) el.scrollTop = scrollTop;\n      if (animateLeft) el.scrollLeft = scrollLeft;\n      requestAnimationFrame(render);\n    }\n    requestAnimationFrame(render);\n  });\n}\n// scrollTop(top, duration, easing, callback) {\nfunction scrollTop(...args) {\n  let [top, duration, easing, callback] = args;\n  if (args.length === 3 && typeof easing === 'function') {\n    [top, duration, callback, easing] = args;\n  }\n  const dom = this;\n  if (typeof top === 'undefined') {\n    if (dom.length > 0) return dom[0].scrollTop;\n    return null;\n  }\n  return dom.scrollTo(undefined, top, duration, easing, callback);\n}\nfunction scrollLeft(...args) {\n  let [left, duration, easing, callback] = args;\n  if (args.length === 3 && typeof easing === 'function') {\n    [left, duration, callback, easing] = args;\n  }\n  const dom = this;\n  if (typeof left === 'undefined') {\n    if (dom.length > 0) return dom[0].scrollLeft;\n    return null;\n  }\n  return dom.scrollTo(left, undefined, duration, easing, callback);\n}\n\nfunction animate(initialProps, initialParams) {\n  const els = this;\n  const a = {\n    props: Object.assign({}, initialProps),\n    params: Object.assign({\n      duration: 300,\n      easing: 'swing', // or 'linear'\n      /* Callbacks\n      begin(elements)\n      complete(elements)\n      progress(elements, complete, remaining, start, tweenValue)\n      */\n    }, initialParams),\n\n    elements: els,\n    animating: false,\n    que: [],\n\n    easingProgress(easing, progress) {\n      if (easing === 'swing') {\n        return 0.5 - (Math.cos(progress * Math.PI) / 2);\n      }\n      if (typeof easing === 'function') {\n        return easing(progress);\n      }\n      return progress;\n    },\n    stop() {\n      if (a.frameId) {\n        cancelAnimationFrame(a.frameId);\n      }\n      a.animating = false;\n      a.elements.each((index, el) => {\n        const element = el;\n        delete element.dom7AnimateInstance;\n      });\n      a.que = [];\n    },\n    done(complete) {\n      a.animating = false;\n      a.elements.each((index, el) => {\n        const element = el;\n        delete element.dom7AnimateInstance;\n      });\n      if (complete) complete(els);\n      if (a.que.length > 0) {\n        const que = a.que.shift();\n        a.animate(que[0], que[1]);\n      }\n    },\n    animate(props, params) {\n      if (a.animating) {\n        a.que.push([props, params]);\n        return a;\n      }\n      const elements = [];\n\n      // Define & Cache Initials & Units\n      a.elements.each((index, el) => {\n        let initialFullValue;\n        let initialValue;\n        let unit;\n        let finalValue;\n        let finalFullValue;\n\n        if (!el.dom7AnimateInstance) a.elements[index].dom7AnimateInstance = a;\n\n        elements[index] = {\n          container: el,\n        };\n        Object.keys(props).forEach((prop) => {\n          initialFullValue = window.getComputedStyle(el, null).getPropertyValue(prop).replace(',', '.');\n          initialValue = parseFloat(initialFullValue);\n          unit = initialFullValue.replace(initialValue, '');\n          finalValue = parseFloat(props[prop]);\n          finalFullValue = props[prop] + unit;\n          elements[index][prop] = {\n            initialFullValue,\n            initialValue,\n            unit,\n            finalValue,\n            finalFullValue,\n            currentValue: initialValue,\n          };\n        });\n      });\n\n      let startTime = null;\n      let time;\n      let elementsDone = 0;\n      let propsDone = 0;\n      let done;\n      let began = false;\n\n      a.animating = true;\n\n      function render() {\n        time = new Date().getTime();\n        let progress;\n        let easeProgress;\n        // let el;\n        if (!began) {\n          began = true;\n          if (params.begin) params.begin(els);\n        }\n        if (startTime === null) {\n          startTime = time;\n        }\n        if (params.progress) {\n          // eslint-disable-next-line\n          params.progress(els, Math.max(Math.min((time - startTime) / params.duration, 1), 0), ((startTime + params.duration) - time < 0 ? 0 : (startTime + params.duration) - time), startTime);\n        }\n\n        elements.forEach((element) => {\n          const el = element;\n          if (done || el.done) return;\n          Object.keys(props).forEach((prop) => {\n            if (done || el.done) return;\n            progress = Math.max(Math.min((time - startTime) / params.duration, 1), 0);\n            easeProgress = a.easingProgress(params.easing, progress);\n            const { initialValue, finalValue, unit } = el[prop];\n            el[prop].currentValue = initialValue + (easeProgress * (finalValue - initialValue));\n            const currentValue = el[prop].currentValue;\n\n            if (\n              (finalValue > initialValue && currentValue >= finalValue) ||\n              (finalValue < initialValue && currentValue <= finalValue)) {\n              el.container.style[prop] = finalValue + unit;\n              propsDone += 1;\n              if (propsDone === Object.keys(props).length) {\n                el.done = true;\n                elementsDone += 1;\n              }\n              if (elementsDone === elements.length) {\n                done = true;\n              }\n            }\n            if (done) {\n              a.done(params.complete);\n              return;\n            }\n            el.container.style[prop] = currentValue + unit;\n          });\n        });\n        if (done) return;\n        // Then call\n        a.frameId = requestAnimationFrame(render);\n      }\n      a.frameId = requestAnimationFrame(render);\n      return a;\n    },\n  };\n\n  if (a.elements.length === 0) {\n    return els;\n  }\n\n  let animateInstance;\n  for (let i = 0; i < a.elements.length; i += 1) {\n    if (a.elements[i].dom7AnimateInstance) {\n      animateInstance = a.elements[i].dom7AnimateInstance;\n    } else a.elements[i].dom7AnimateInstance = a;\n  }\n  if (!animateInstance) {\n    animateInstance = a;\n  }\n\n  if (initialProps === 'stop') {\n    animateInstance.stop();\n  } else {\n    animateInstance.animate(a.props, a.params);\n  }\n\n  return els;\n}\n\nfunction stop() {\n  const els = this;\n  for (let i = 0; i < els.length; i += 1) {\n    if (els[i].dom7AnimateInstance) {\n      els[i].dom7AnimateInstance.stop();\n    }\n  }\n}\n\nconst noTrigger = ('resize scroll').split(' ');\nfunction eventShortcut(name, ...args) {\n  if (typeof args[0] === 'undefined') {\n    for (let i = 0; i < this.length; i += 1) {\n      if (noTrigger.indexOf(name) < 0) {\n        if (name in this[i]) this[i][name]();\n        else {\n          $(this[i]).trigger(name);\n        }\n      }\n    }\n    return this;\n  }\n  return this.on(name, ...args);\n}\n\nfunction click(...args) {\n  return eventShortcut.bind(this)('click', ...args);\n}\nfunction blur(...args) {\n  return eventShortcut.bind(this)('blur', ...args);\n}\nfunction focus(...args) {\n  return eventShortcut.bind(this)('focus', ...args);\n}\nfunction focusin(...args) {\n  return eventShortcut.bind(this)('focusin', ...args);\n}\nfunction focusout(...args) {\n  return eventShortcut.bind(this)('focusout', ...args);\n}\nfunction keyup(...args) {\n  return eventShortcut.bind(this)('keyup', ...args);\n}\nfunction keydown(...args) {\n  return eventShortcut.bind(this)('keydown', ...args);\n}\nfunction keypress(...args) {\n  return eventShortcut.bind(this)('keypress', ...args);\n}\nfunction submit(...args) {\n  return eventShortcut.bind(this)('submit', ...args);\n}\nfunction change(...args) {\n  return eventShortcut.bind(this)('change', ...args);\n}\nfunction mousedown(...args) {\n  return eventShortcut.bind(this)('mousedown', ...args);\n}\nfunction mousemove(...args) {\n  return eventShortcut.bind(this)('mousemove', ...args);\n}\nfunction mouseup(...args) {\n  return eventShortcut.bind(this)('mouseup', ...args);\n}\nfunction mouseenter(...args) {\n  return eventShortcut.bind(this)('mouseenter', ...args);\n}\nfunction mouseleave(...args) {\n  return eventShortcut.bind(this)('mouseleave', ...args);\n}\nfunction mouseout(...args) {\n  return eventShortcut.bind(this)('mouseout', ...args);\n}\nfunction mouseover(...args) {\n  return eventShortcut.bind(this)('mouseover', ...args);\n}\nfunction touchstart(...args) {\n  return eventShortcut.bind(this)('touchstart', ...args);\n}\nfunction touchend(...args) {\n  return eventShortcut.bind(this)('touchend', ...args);\n}\nfunction touchmove(...args) {\n  return eventShortcut.bind(this)('touchmove', ...args);\n}\nfunction resize(...args) {\n  return eventShortcut.bind(this)('resize', ...args);\n}\nfunction scroll(...args) {\n  return eventShortcut.bind(this)('scroll', ...args);\n}\n\nexport { $, addClass, removeClass, hasClass, toggleClass, attr, removeAttr, prop, data, removeData, dataset, val, transform, transition, on, off, once, trigger, transitionEnd, animationEnd, width, outerWidth, height, outerHeight, offset, hide, show, styles, css, toArray, each, forEach, filter, map, html, text, is, indexOf, index, eq, append, appendTo, prepend, prependTo, insertBefore, insertAfter, next, nextAll, prev, prevAll, siblings, parent, parents, closest, find, children, remove, detach, add, empty, scrollTo, scrollTop, scrollLeft, animate, stop, click, blur, focus, focusin, focusout, keyup, keydown, keypress, submit, change, mousedown, mousemove, mouseup, mouseenter, mouseleave, mouseout, mouseover, touchstart, touchend, touchmove, resize, scroll };\n", "var map = {\n\t\"./boon/boon-login-award.png\": \"9234\",\n\t\"./ssd/boon-login-award.png\": \"e065\",\n\t\"./ssv2/boon-login-award.png\": \"8bb0\"\n};\n\n\nfunction webpackContext(req) {\n\tvar id = webpackContextResolve(req);\n\treturn __webpack_require__(id);\n}\nfunction webpackContextResolve(req) {\n\tif(!__webpack_require__.o(map, req)) {\n\t\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\t\te.code = 'MODULE_NOT_FOUND';\n\t\tthrow e;\n\t}\n\treturn map[req];\n}\nwebpackContext.keys = function webpackContextKeys() {\n\treturn Object.keys(map);\n};\nwebpackContext.resolve = webpackContextResolve;\nmodule.exports = webpackContext;\nwebpackContext.id = \"f691\";", "module.exports = __webpack_public_path__ + \"static/**********/img/login_reward_2.a42cc374.png\";"], "sourceRoot": ""}