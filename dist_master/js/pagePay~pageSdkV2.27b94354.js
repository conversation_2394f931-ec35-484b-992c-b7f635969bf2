(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pagePay~pageSdkV2"],{"0075":function(t,e,i){"use strict";i("d9e2"),i("14d9"),i("88a7"),i("271a"),i("5494");var s=i("fa7d"),o=i("2b80"),a=i.n(o),n=i("af82"),c=i("5001"),r=i("2f62");e["a"]={methods:{customGetSmDeviceId(t){if("function"!==typeof t)return console.error("customGetSmDeviceId cb 必须为函数！");new Promise((t,e)=>{this.$loading.show();const i=setTimeout(()=>t(""),2500);Object(s["b"])((function(e){i&&clearTimeout(i),t(e)}))}).then(e=>{e||console.error("dealSmDeviceId 失败！"),t(e)}).finally(()=>this.$loading.hide())},purchaseGoodsWithDeviceId(t,e){const{channel_id:i,channel_name:o,sub_channel_id:r}=this.chosenChannel,d=this.chosenDiamond,u={name:this.$vt("tokenName"),product_id:d.product_id,price:+(d.nowPriceWithTaxAndExtra||d.price),channel:o,channel_id:i,sub_channel_id:r,return_url:location.origin,fp_device_id:e};if("/"!==this.$router.options.base&&(u.return_url+="/"+this.$router.options.base.replace(/\//g,""),this.IS_CHECKOUT_SDK)){let t=location.origin+window.__ROUTERPATH;t.endsWith("/")&&(t=t.slice(0,-1)),u.return_url=t}"aof"!==this.$gameName&&"rom"!==this.$gameName||(u.ext_info=JSON.stringify({project_code:"koa_aof_web"}));const{type:h,chosenNum:l}=this.chosenDiamond;2===h&&(u.custom_multiple=l),t&&(u.backup=1);let p=this.$store.state.urlParams.utm_campaign;if(p){if(p.includes("?")){const t=p.indexOf("?");p=p.slice(0,t)}u.tracking_id=p}"standalone"===s["g"]&&(u.tracking_id="pwa_"+s["h"]),u.browser_id=localStorage.getItem("browserMark")||"";const _=new a.a;u.browser_info={terminalType:window.innerWidth>1200?"pc":"mobile",osType:_.getOS().name,...Object(s["f"])()};const m=this.$store.getters["formdata/FinalPriceState"],f=this.chosenCoupon;switch(m.feType){case"first_pay":case"direct_first_pay":u.act_type="first_pay",u.discount=f.discount,u.discount_price=f.discount_price;break;case"first_pay_rebate":case"direct_first_pay_rebate":u.act_type="first_pay_rebate",u.discount=f.discount,u.discount_price=f.discount_price,f.act_type&&(u.act_type=f.act_type);break;case"discount_coupon":u.act_type="coupon",u.discount=f.discount,u.discount_price=f.discount_price,u.coupon_id=f.coupon_id;break;case"cash_coupon":u.act_type="deduct",u.discount=f.deduct_price,u.discount_price=f.price,u.coupon_id=f.coupon_id;break;case"rebate_coupon":u.act_type="rebate",u.discount=f.discount,u.discount_price=f.discount_price,u.coupon_id=f.coupon_id;break;case"fixed_discount_coupon":case"direct_fixed_discount":{const t=this.$store.state.formdata.defaultDiscountInfo;u.act_type="fixed_discount",u.discount=t.discount,u.discount_price=t.discount_price;break}case"fixed_rebate":case"direct_fixed_rebate":u.act_type="fixed_rebate";break;case"fixed_dynamic_rebate":u.act_type="product_fixed_rebate";break;default:{const t=this.$store.state.functionSwitch.fixedDiscountType;t&&!u.act_type&&(u.act_type=t);const e=this.$store.state.functionSwitch.smallDiamondDoubleDiscount&&d.product_id===this.$gcbk("ids.minimumDiamondId"),i=e||this.$store.getters["formdata/TWMyCard"];i&&delete u.act_type}}const g=this.chosenChannel,v=`${g.channel_id}_${g.channel_name}_${g.sub_channel_id}_dropin`;if(this.$store.state.vb.builtInCashier&&"payermax_A34_A34_dropin"===v&&(u.order_type="drop_in"),sessionStorage.removeItem("goodsName"),this.IS_CHECKOUT_SDK){const t=this.$store.state.urlParams,e=JSON.parse(t.tc||"{}");m.sdkType&&(u.act_type=m.sdkType),u.package_id=e.package_id,u.name=u.package_name=e.product_name,u.game_order_id=t.oid,sessionStorage.setItem("goodsName",e.product_name)}if(this.IS_CHECKOUT_SDK_V2){const t=window._calState();t.type&&(u.act_type=t.type),u.name||(u.name=window.defaultPackageName,sessionStorage.setItem("goodsName",window.defaultPackageName))}const C=this.$store.state,y={method:`${o}|${i}|${r}`,amount:"",country:C.country,currency:C.currency,product_info:u.product_id,event:"pay_completed",revenue:u.price};this.$loading.show(),this.requestLoading=!0,new Promise(t=>{if(this.$store.getters["formdata/TWMyCard"])return t(Object(n["r"])(u));t(Object(n["s"])(u))}).then(t=>{const{data:e,code:i,message:s}=t;switch(i){case 0:new Promise((t,i)=>{"coin_debt"in e?(this.$root.$emit("showPop","ArrearsReminder",{debt:e.coin_debt||0}),this.$root.$once("arrearsReminderResult",e=>{e?t(1):i(Error("cancel pop!"))})):t(2)}).then(t=>{if(Object(c["logForPayResult"])("success",e.order_id,"-",y),"string"===typeof e.pay_url){const i=new URL(e.pay_url),s=new URLSearchParams(i.search);if(sessionStorage.setItem("3zRtY8vXwN",JSON.stringify(e)),sessionStorage.removeItem("7x9FkL2pQm"),e.pay_url.includes("pingpong")&&"jump_url"!==s.get("window_type")){const i={ppToken:s.get("token"),coinNums:e.coin_recv,currency:e.currency,currency_symbol:e.currency_symbol,amount:e.price};1===t&&(i.inDebt=!0),sessionStorage.setItem("ppParams",JSON.stringify(i)),this.$router.push("/pp")}else{if("jump_url"===s.get("window_type"))return location.href=e.pay_url.replace("&window_type=jump_url","").replace("?window_type=jump_url",""),null;if(e.open_with_new_window)return window.open(e.pay_url,"_blank");location.href=e.pay_url}}else e.pay_url.coinNums=e.coin_recv,e.pay_url.currency_symbol=e.currency_symbol,e.pay_url.host=e.payment_host,e.pay_url.order_id=e.payment_order_id,e.pay_url.out_trade_no=e.order_id,sessionStorage.setItem("id_sign",e.payment_order_id_sign),sessionStorage.setItem("url",e.payment_host),1===t&&(e.pay_url.inDebt=!0),"payermax"===e.pay_url.channel&&(e.pay_url.payment_order_id=e.payment_order_id,e.pay_url.name=e.name),sessionStorage.setItem("params",JSON.stringify(e.pay_url)),"payermax"===e.pay_url.channel?this.$router.push("/pm"):e.pay_url.client_secret?this.$router.push("/aw"):e.pay_url.store_card_url?this.$router.push("/cko"):e.pay_url.stripe_client_secret?this.$router.push("/sp"):this.$router.push("/ad")}).catch(t=>{console.log(t.message)});break;case 1003:{const t={1:this.$t("illegalGift"),2:this.$t("expiredPackage"),3:this.$t("InventoryShortage")};this.$toast.err(t[e.check_status]),Object(c["logForPayResult"])("failed","-",t[e.check_status],y);break}default:throw this.$toast.err(this.$t("shop_fail")),Error(s)}}).catch(t=>{this.requestLoading=!1,Object(c["logForPayResult"])("failed","-",t,y),console.error("coinPlaceOrder下单失败："+t.message)}).finally(()=>{this.requestLoading=!1,this.$loading.hide()})},async purchaseGoods(t){if(this.requestLoading||this.$store.getters["riskPolicy/forbiddenAccess"])return null;if(!this.isLogin)return this.$root.$emit("ClickPayButNotLogin");if(!this.$store.state.agreePrivacyPolicy)return this.$root.$emit("showPop","PrivacyPolicy");if(window.__needDEPop){const t=await new Promise((t,e)=>{const i={ok:()=>t(1),no:()=>t(0)};this.$root.$emit("showPop","privateConfirmPop",i)});if(!t)return null}if("{}"===JSON.stringify(this.chosenChannel))return this.$toast.err(this.$t("ModalTIpsShopBeforeChooseChannel"));const e=this;this.customGetSmDeviceId((function(i){e.purchaseGoodsWithDeviceId(t,i)}))},judgeRisk(){const{channel_id:t}=this.chosenChannel,e=this.$store.getters["riskPolicy/showTipsWhenSomeChannel"].indexOf(t);if(-1!==e){const t="use_"+this.$store.getters["riskPolicy/showTipsWhenSomeChannel"][e];return this.$root.$emit("showPop","RiskControlPolicy",{key:t,cb:this.purchaseGoods})}this.purchaseGoods()}},computed:{...Object(r["c"])("formdata",["chosenChannel","chosenDiamond","chosenCoupon","vip"]),...Object(r["c"])(["isPc","isMobile","IS_CHECKOUT_SDK","IS_CHECKOUT_SDK_V2"]),...Object(r["c"])("userinfo",["isLogin"]),...Object(r["c"])("gameinfo",["gameCode","isKOA","isSS"]),...Object(r["c"])("functionSwitch",["showMobilePolicy","boon"])},data(){return{requestLoading:!1}},created(){this.$root.$on("adyenInitError",()=>this.purchaseGoods(1)),location.search&&history.pushState({},"",location.pathname)}}},"1f1f":function(t,e,i){"use strict";var s=function(){var t=this,e=t._self._c;return e("common-part",{class:[t.$store.state.gameinfo.gameCode,"channel-part-wrapper",t.$gameName,{sdk:t.$store.state.IS_CHECKOUT_SDK}],attrs:{"label-font":t.$t("channelChosen"),id:"channel-part-wrapper"}},[e("div",{staticClass:"channel-list"},[t.calChannelList.length?t._l(t.calChannelList,(function(i,s){return e("div",{key:i.FE_CHANNEL_ID,staticClass:"channel-btn",class:[{"channel-chosen__active":i.FE_CHANNEL_ID===t.chosenChannel.FE_CHANNEL_ID}],on:{click:function(e){return t.toggleStatus(s)}}},[e("div",{directives:[{name:"lazy",rawName:"v-lazy:backgroundImage",value:i.icon_url,expression:"channel.icon_url",arg:"backgroundImage"}],staticClass:"image common-fade-in"}),i.subscript||t.whetherShowVipBonus(i)?e("div",{class:["recommendation","recommendation-REC"]},[e("span",{staticClass:"blank"}),t.whetherShowVipBonus(i)?e("span",{staticClass:"bonus-description"},[t._v(" VIP +"+t._s(t.vip.channelBonus*(2===t.chosenDiamond.type?t.chosenDiamond.totalDiamond:t.chosenDiamond.coin))+" "),e("i")]):e("span",{staticClass:"txt"},[t._v(t._s(t.$t("recommend-txt")))])]):t._e()])})):e("div",{staticClass:"empty"},[t._v(t._s(t.$t("nothingHere")))])],2)])},o=[],a=(i("e9f5"),i("910d"),i("ab43"),i("3772")),n=i("af82"),c=i("2f62"),r=i("fa7d"),d={name:"ChannelChoose",components:{CommonPart:a["a"]},props:{activity:{type:Object,default:()=>{}}},data(){return{channelList:[],unwatch:void 0,isUserChosen:!1}},computed:{...Object(c["c"])(["urlParams","isArZone","currencyUnit"]),...Object(c["c"])("formdata",["chosenChannel","chosenDiamond","chosenCoupon","vip","isInit","isFirstPayUsed"]),whetherShowVipBonus(){return t=>this.isInit&&this.vip.discountSubChannelId.includes(t.sub_channel_id)&&this.vip.channelBonus&&this.isFirstPayUsed&&!this.chosenCoupon.FE_INDEX},calChannelList(){const t=this.$store.getters["riskPolicy/hideSomeChannel"]||[];return t.length?(t.includes(this.chosenChannel.channel_id)&&this.$store.commit("formdata/resetChannel"),this.channelList.filter(e=>!t.includes(e.channel_id))):this.channelList}},methods:{loadChannelList(){const t={currency:this.urlParams.cr,price:+this.chosenDiamond.price,product_id:this.chosenDiamond.product_id},e=this.$store.state.formdata.chosenCoupon;if(e.FE_INDEX&&("discount_coupon"===e.feType&&(t.price=e.discount_price),"cash_coupon"===e.feType&&(t.price=e.price),"first_pay"===e.feType&&(t.price=e.discount_price)),this.$store.getters["formdata/takeEffectDefaultDiscount"]){const e=["BDT","CLP","COP","CRC","DZD","HUF","IDR","INR","IQD","JPY","KES","KRW","KZT","LBP","LKR","MMK","NGN","PHP","PKR","PYG","RSD","RUB","THB","TWD","TZS","VND"];let i=.95*t.price;i=e.includes(this.chosenDiamond.currency)?Math.ceil(i):i.toFixed(2),t.price=+i}const{type:i,nowPrice:s}=this.chosenDiamond;2===i&&(t.price=+s),this.$loading.show(),this.$store.commit("formdata/resetChannel"),Object(n["m"])(t).then(({data:t,code:e,message:i})=>{0===e?(this.channelList=this.adapterChannel(t),setTimeout(()=>{const{lastChannel:t}=this.$store.state.userinfo;if(t){const e=this.calChannelList.filter(e=>e.channel_id===t.channel_id&&t.sub_channel_id===e.sub_channel_id);e&&e.length&&this.$store.commit("formdata/setChosenChannel",e[0]);const i=this.calChannelList.filter(e=>e.channel_name===t.channel_name);if(i&&i.length)return this.$store.commit("formdata/setChosenChannel",i[0])}},0),this.$gcbk("switch.enableAnimation",!1)&&!window.channelFlag&&this.$store.state.isPc&&(window.channelFlag=1,this.$nextTick(()=>{gsap&&gsap.from(".channel-list",{height:0,duration:.4,clearProps:"height"})}))):this.$toast.err(this.$t("fetchChannelError"))}).finally(()=>this.$loading.hide())},adapterChannel(t){if(this.$store.state.gameinfo.isCn){let e;e=r["k"]?["WxpayJSAPI","Alipaywap"]:window.isMobile?["WxpayMWEB","Alipaywap"]:["WxpayPcNATIVE","Alipaypc"],t=t.filter(t=>"wxpay"!==t.channel_id&&"alipay"!==t.channel_id||e.indexOf(t.channel_name+t.sub_channel_id)>-1)}return t.map(t=>(t.FE_CHANNEL_ID=`${t.channel_id}__${t.channel_name}`,t))},toggleStatus(t){this.isUserChosen=!0;const e=this.calChannelList[t],i=this.chosenChannel;if(e.FE_CHANNEL_ID===i.FE_CHANNEL_ID)return null;this.$store.commit("formdata/setChosenChannel",this.calChannelList[t])},initLastChannel(){Object(n["k"])().then(t=>{const{code:e,data:i}=t;if(0===e){if(this.$store.commit("userinfo/saveLastChannelInfo",i),this.isUserChosen)return null;const{lastChannel:t}=this.$store.state.userinfo;if(t){const e=this.calChannelList.filter(e=>e.channel_id===t.channel_id&&t.sub_channel_id===e.sub_channel_id);if(e&&e.length)return this.$store.commit("formdata/setChosenChannel",e[0])}}})}},created(){this.$root.$on("couponChoose",()=>this.loadChannelList()),this.$root.$on("activityInitEnd",()=>this.loadChannelList()),this.$root.$on("loginEnd",t=>{1===t&&this.initLastChannel()})},beforeDestroy(){this.unwatch&&this.unwatch()}},u=d,h=(i("4979"),i("2877")),l=Object(h["a"])(u,s,o,!1,null,"97029186",null);e["a"]=l.exports},"271a":function(t,e,i){"use strict";var s=i("cb2d"),o=i("e330"),a=i("577e"),n=i("d6d6"),c=URLSearchParams,r=c.prototype,d=o(r.getAll),u=o(r.has),h=new c("a=1");!h.has("a",2)&&h.has("a",void 0)||s(r,"has",(function(t){var e=arguments.length,i=e<2?void 0:arguments[1];if(e&&void 0===i)return u(this,t);var s=d(this,t);n(e,1);var o=a(i),c=0;while(c<s.length)if(s[c++]===o)return!0;return!1}),{enumerable:!0,unsafe:!0})},"2fdd":function(t,e,i){},3772:function(t,e,i){"use strict";var s=function(){var t=this,e=t._self._c;return e("div",{staticClass:"common-part-wrapper",class:[t.$gameName,{sdk:t.$store.state.IS_CHECKOUT_SDK}]},[e("div",{staticClass:"label"},[t._t("label",(function(){return[t._v(t._s(t.labelFont))]}))],2),e("div",{staticClass:"body"},[t._t("default")],2)])},o=[],a={name:"CommonPart",props:{labelFont:{type:String,default:""}}},n=a,c=(i("c3e4"),i("2877")),r=Object(c["a"])(n,s,o,!1,null,"c17bc992",null);e["a"]=r.exports},"402f":function(t,e,i){},"43ae":function(t,e,i){"use strict";i("480e")},"480e":function(t,e,i){},4979:function(t,e,i){"use strict";i("fefc")},5494:function(t,e,i){"use strict";var s=i("83ab"),o=i("e330"),a=i("edd0"),n=URLSearchParams.prototype,c=o(n.forEach);s&&!("size"in n)&&a(n,"size",{get:function(){var t=0;return c(this,(function(){t++})),t},configurable:!0,enumerable:!0})},"5f48":function(t,e,i){"use strict";i("fef2")},6201:function(t,e,i){},"72a3":function(t,e,i){"use strict";var s=function(){var t=this,e=t._self._c;return e("common-part",{class:["wrapper",t.$gameName],attrs:{id:"login-part-wrapper","label-font":t.userinfo.isLogin?"":t.$vt("loginPlaceHolder")}},[t.userinfo.isLogin?e("div",{staticClass:"login-status__login"},[e("div",{directives:[{name:"lazy",rawName:"v-lazy:backgroundImage",value:t.userinfo.icon,expression:"userinfo.icon",arg:"backgroundImage"}],staticClass:"avatar lazy"}),e("div",{staticClass:"user-info"},[e("div",{staticClass:"row-1"},[e("div",{staticClass:"name"},[t._v(t._s(t.userinfo.name))]),t.$store.state.isPCSDK?t._e():e("div",{staticClass:"toggle click-btn",on:{click:t.logout}},["ssv"!==t.$gameName?[t._v(t._s(t.$t("switch_account")))]:t._e()],2)]),t.isKOA&&t.vip.isInit?e("div",{staticClass:"row-koa"},[e("div",{staticClass:"grade"},[t._v("VIP "+t._s(t.vip.level))]),e("div",{staticClass:"btn-jump"},[e("a",{attrs:{href:t.vipIntroducePageUrl,target:"_blank"}},[t._v(t._s(t.$t("title_s_vip")))])])]):t._e(),e("div",{staticClass:"row-2"},[e("span",{staticClass:"leave"},[t._v(t._s(t.$t("user_level",{0:t.userinfo.level})))]),e("span",{staticClass:"server"},[t._v(t._s(t.$t("user_server",{0:t.userinfo.server})))])])])]):[e("div",{staticClass:"login-status__not-login",class:[{emphasize:t.focusEmphasize}]},[e("div",{staticClass:"login-input-wrapper"},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.uid,expression:"uid"}],attrs:{type:"number",id:"uidInput",placeholder:t.focusEmphasize?"":t.$vt("loginPlaceHolder")},domProps:{value:t.uid},on:{input:function(e){e.target.composing||(t.uid=e.target.value)}}}),e("i",{on:{click:function(e){e.stopPropagation(),t.isShowTipsImg=!0},mouseenter:function(e){e.stopPropagation(),t.isShowTipsImg=!0}}}),e("img",{staticClass:"tips-img",class:{active:t.isShowTipsImg},attrs:{src:t.$imageLoader("uidTips"),alt:""},on:{click:function(t){t.stopPropagation()}}})]),e("div",{class:["btn-login","click-btn","btn",{btn_disable:!t.uid},{disable:!t.uid}],on:{click:function(e){return t.query()}}},[t._v(t._s(t.$t("login")))]),t.isKOA?e("div",{staticClass:"vip-jump-wrapper"},[e("a",{attrs:{href:t.vipIntroducePageUrl,target:"_blank"}},[t._v(t._s(t.$t("title_s_vip")))])]):t._e()]),t.focusEmphasize?e("div",{staticClass:"tip tip-please-input"},[t._v(t._s(t.$vt("loginPlaceHolder")))]):t._e(),t.focusEmphasize?t._e():e("div",{staticClass:"tip tip-uid-get"},[t._v(t._s(t.$t("where-uid-is")))])]],2)},o=[],a=i("3772"),n=i("af82"),c=i("2f62"),r=i("5001"),d=i("9213"),u=i("fa7d"),h=i("3452"),l=i.n(h);const p=l.a.enc.Utf8.parse("84uyzdgah9m#m9x4qzu&ye53"),_=p.clone();function m(t){return encodeURIComponent(l.a.AES.encrypt(l.a.enc.Utf8.parse(t),p,{iv:_,mode:l.a.mode.CBC,padding:l.a.pad.Pkcs7}).toString())}_.sigBytes=16,_.words.splice(4);var f={name:"LoginModule",components:{CommonPart:a["a"]},data(){return{uid:"",isShowTipsImg:!1,focusEmphasize:!1}},computed:{...Object(c["c"])(["userinfo"]),...Object(c["c"])("gameinfo",["whiteChannel","blackChannel","greyChannel","gameCode","isKOA","isROMCP"]),...Object(c["c"])("functionSwitch",["loginValidation"]),...Object(c["c"])(["isPc"]),...Object(c["c"])("formdata",["vip"]),vipIntroducePageUrl(){return Object({NODE_ENV:"production",VUE_APP_PROD_ENV:"MASTER",VUE_APP_PREFIX_TOKEN_SSCP:"https://ss-coin-master-api.kingsgroupgames.com/api",VUE_APP_PREFIX_TOKEN_STCP:"https://st-store-master.kingsgroupgames.com/api",VUE_APP_PREFIX_TOKEN_MCCP:"https://mc-store-master.kingsgroupgames.com/api",VUE_APP_PREFIX_TOKEN_GOGCP:"https://gog-store-master.kingsgroupgames.com/api",VUE_APP_PREFIX_TOKEN_ROMCP:"https://koa-store-release.kingsgroup.cn/api",VUE_APP_PREFIX_TOKEN_SSV:"https://ssv-store-master.kingsgroupgames.com/api",VUE_APP_PREFIX_TOKEN_SSV2:"https://ssv2-store-master-api.kingsgroupgames.com/api",VUE_APP_PREFIX_TOKEN_DC:"https://dcdl-store-master-api.kingsgroupgames.com/api",VUE_APP_PREFIX_TOKEN_FOUNDATION:"https://foundation-store-release-api.kingsgroup.cn/api",VUE_APP_PREFIX_ACCOUNT_FOUNDATION:"https://store-account-release.nenglianghe.cn/api/account",VUE_APP_PREFIX_TOKEN_SSRP:"https://ss-coin-master-api.kingsgroupgames.com/api",VUE_APP_PREFIX_STORE_SSRP:"https://ss-store-master.kingsgroupgames.com",VUE_APP_PREFIX_TOKEN_ST:"https://st-store-master.kingsgroupgames.com/api",VUE_APP_PREFIX_STORE_STRP:"https://st-store-master.kingsgroupgames.com",VUE_APP_PREFIX_TOKEN_MO:"https://mo-store-master.kingsgroupgames.com/api",VUE_APP_PREFIX_STORE_MORP:"https://mo-store-master.kingsgroupgames.com",VUE_APP_PREFIX_TOKEN_SSD:"https://ts-store-master-api.kingsgroupgames.com/api",VUE_APP_PREFIX_STORE_SSDRP:"https://ts-store-master.kingsgroupgames.com",VUE_APP_PREFIX_AME:"https://ame-test.funplus.com",VUE_APP_PREFIX_ACCOUNT:"https://store-account-stage.funplus.com/api/account",VUE_APP_LOGIN_PAGE_URL:"https://store-funplusid-test.funplus.com/login",VUE_APP_VipIntroducePageUrl_koa:"http://vip-test.funplus.com.cn/koa",VUE_APP_VipIntroducePageUrl_aof:"http://vip-test.funplus.com.cn/vip",VUE_APP_VipIntroducePageUrl_rom:"http://vip-test.funplus.com.cn/rom",VUE_APP_VipIntroducePageUrl_koaCn:"http://vip-test.funplus.com.cn/koa-cn",VUE_APP_ROM_ADDRESS_KOA:"http://**************:10305",BASE_URL:"/res/"})["VUE_APP_VipIntroducePageUrl_"+this.$gameName]+"?l="+this.$i18n.locale}},methods:{async query(t){if(!this.uid&&!t)return;const e=this.$store.state,i={hideErrToast:!0,game_project:e.gameProject};t&&"string"===typeof t&&(i.openid=t),this.uid&&(localStorage.removeItem("openid"),i.uid=+this.uid,"KOA"===this.$store.state.gameinfo.gameCode&&(i.ticket=m(`${this.uid}|${Math.floor(Date.now()/1e3)}`),i.fopenid=m(`${Math.ceil(Date.now()/1e3)}|${this.uid}`))),this.uid&&Object(r["logForClickLogin"])(this.uid);try{this.$loading.show();let{data:t={},code:e}=await Object(n["p"])(i);switch(this.$loading.hide(),e){case 0:{try{const e=this.$gcbk("ids.secretKey");"string"===typeof t&&(t=JSON.parse(Object(u["c"])(t,e)))}catch(s){console.error("解密失败！"+this.uid)}const e=this.whiteChannel||[];if(e.length&&!e.includes(t.pkg_channel))return this.uid="",this.$toast.err(this.$t("pkg_not_allow"));const i=this.greyChannel||[],{pkg_channel:o,openid:a}=t;if(i.length)for(const t of i){const{channel:e,to:i}=t;if(e.includes(o))return localStorage.removeItem("openid"),void(window.location.href=`${i}?openid=${encodeURIComponent(a)}`)}const n=this.blackChannel||[];if(n.length&&n.includes(o))return this.$toast.err(this.$t("pkg_not_allow"));const c=this.$store.state.IS_CHECKOUT_SDK||this.$store.state.isPCSDK;if(this.loginValidation&&!c){const e=await this.goValidation(t);if(!e)return null}this.$store.commit("userinfo/setUserInfo",t),(this.isKOA||this.isROMCP)&&await this.loadVipStatus(),this.judgePop(),this.initRiskPolicy(),Object(r["logForLoginSuccess"])(),this.$root.$emit("loginSuccess"),this.$root.$emit("loginEnd",1),this.uid="",this.$gcbk("switch.enableAnimation",!1)&&setTimeout(()=>gsap&&gsap.from(".login-status__login",{opacity:0,xPercent:-5,duration:.5}),0);break}case 2:this.$tips.show(this.$t("maintenance_tips"));break;case 1e3:case 1002:case 4001:case 1007:{const t={1e3:"toast_invalid_param",1002:"text_no_role",4001:"toast_black_user",1007:"toast_black_country"};this.$toast.err(this.$t(t[e])),1002!==e||i.uid||this.logout();break}case 2001:this.$toast.err(this.$t("account_login_fail")),setTimeout(()=>this.$store.commit("userinfo/logout"),1e3);break;default:this.$toast.err(this.$t("account_login_fail"))}}catch(o){o.message.includes("timeout of")&&this.$loading.hide()}},logout(){this.$store.commit("userinfo/logout")},judgePop(){if(window.location.pathname.includes("/common/"))return;const t=window.localStorage.getItem("isWhatDiamondPop");t||(window.localStorage.setItem("isWhatDiamondPop","true"),this.$root.$emit("showWhatIsDiamondPop"))},initRiskPolicy(){const t=this.$root.$emit.bind(this.$root);this.$loading.show(),Object(n["i"])().then(e=>{const{code:i,data:s}=e;0===i&&(this.$store.commit("riskPolicy/init",{list:s.user_risk_list,emit:t}),this.$store.commit("vb/savePrefixChannel",s.reserve_card_channel_gray_ratio),this.$store.commit("vb/resetBuiltInCashierStatus",s.switch_global_config),this.$store.commit("formdata/switchToggle",s.change_coupon_enable),this.$store.commit("functionSwitch/updateFunctionInfo",s),this.$root.$emit("updateSpecialDiamond",s.point_card_product))}).finally(()=>{this.$loading.hide()})},async goValidation(t){let e="";return Object(u["b"])(t=>{e=t}),new Promise((i,s)=>{this.$loading.show(),Object(n["t"])({fp_device_id:e,openid:t.openid}).then(e=>{let{code:s,data:o}=e;switch(o||(o={}),o.username=t.name,o.openid=t.openid,s){case 0:case 5004:o.successCb=()=>i(!0),o.failCb=()=>i(!1),this.$root.$emit("saveValidation",o);break;case 5011:case 5001:case 5002:i(!0);break;default:i(!1),this.$toast.err(this.$t("login-validation-error-text"))}}).finally(()=>this.$loading.hide())})},judgeAvatarPop(){if(this.$store.state.IS_CHECKOUT_SDK)return;const t=d["a"].getLocalStorage("isAvatarBonusPop"),e=`${(new Date).getMonth()+1}/${(new Date).getDate()}`;e!==t&&(this.$root.$emit("showPop","AvatarBonusPop"),d["a"].setLocalStorage("isAvatarBonusPop",e))},async loadVipStatus(){if(this.$store.state.IS_CHECKOUT_SDK_V2)return;const t={p0:"web",p1:11,p2:1075};this.$loading.show();try{const{code:e,data:i}=await Object(n["h"])(t);0===e&&this.$store.commit("formdata/initVipInfo",i)}catch(i){}this.$loading.hide(),this.vip.isNewUser&&this.judgeAvatarPop();let e=!1;this.$root.$on("availableTicketChosen",()=>{!e&&this.$toast.success(this.$t("bonus_coupon_mutually_exclusive")),e=!0}),this.$root.$on("TicketPopClose",()=>e=!1)}},created(){const t=localStorage.getItem("openid");t?this.query(t):(this.$root.$emit("loginEnd",0),this.isKOA&&this.judgeAvatarPop(),this.judgePop()),this.$root.$on("BodyClick",()=>{this.isShowTipsImg=!1}),this.$root.$on("ClickPayButNotLogin",()=>{this.focusEmphasize=!0;const t=document.querySelector("#uidInput");setTimeout(()=>{t.focus()},300),t.addEventListener("input",()=>{this.focusEmphasize=!1})})}},g=f,v=(i("7965"),i("2877")),C=Object(v["a"])(g,s,o,!1,null,"12983a50",null);e["a"]=C.exports},"733f":function(t,e,i){"use strict";i("77e3")},7673:function(t,e,i){"use strict";i("6201")},"774a":function(t,e,i){t.exports=i.p+"static/1751299200/img/coupon-item-chosen.fe63410a.png"},"77e3":function(t,e,i){},7965:function(t,e,i){"use strict";i("402f")},"878c":function(t,e,i){"use strict";var s=function(){var t=this,e=t._self._c;return e("common-part",{class:["coupon-bar-wrapper",t.$gameName],attrs:{"label-font":t.$t("coupon"),id:"coupon-bar-wrapper"}},[t.$store.state.userinfo.isLogin?e("div",{staticStyle:{display:"inline-block"},on:{click:function(e){t.activity.showPop=!0}}},[t.activity.isFirstPayUsed?[0===t.availableCouponNum?e("div",{staticClass:"coupons-wrapper coupons-wrapper__no_coupon"},[t._v(" "+t._s(t.$t("coupon_desc_unavailable"))+" ")]):t._e(),t.availableCouponNum>0&&!t.chosenCoupon.FE_INDEX?e("div",{staticClass:"coupons-wrapper coupons-wrapper__available"},[e("span",[t._v(t._s(t.$t("coupon_nums",{0:t.availableCouponNum})))]),t._v(" "),e("i")]):t._e(),t.availableCouponNum>0&&t.chosenCoupon.FE_INDEX?e("div",{staticClass:"coupons-wrapper coupons-wrapper__chosen"},[e("div",{staticClass:"left"},[e("span",[t._v(t._s(t.$t("coupon_desc_chosen_erver")))])]),e("div",{staticClass:"right"},[e("over-size-scale",{key:t.chosenCoupon.FE_INDEX},[e("span",["discount_coupon"===t.chosenCoupon.feType?[t._v(t._s(t.$t("coupon_discount",{0:t.chosenCoupon.rateWidthOutPercent})))]:t._e(),"cash_coupon"===t.chosenCoupon.feType?[e("span",{class:{"is-ar-zone":t.isArZone}},[t._v(t._s(t.chosenCoupon.deduct_price)+" "+t._s(t.currencyUnit))]),t._v(" OFF")]:t._e(),"rebate_coupon"===t.chosenCoupon.feType?[e("span",[t._v(t._s(t.$t("bonus_tips"))+" ")]),t._v(t._s(t.chosenCoupon.rate)),e("i",{staticClass:"diamond-icon"})]:t._e()],2)]),e("i")],1)]):t._e()]:e("div",{staticClass:"coupons-wrapper coupons-wrapper__unavailable"},[e("div",{staticClass:"left"},[0===t.availableCouponNum?e("span",[t._v(t._s(t.$t("coupon_desc_unavailable")))]):e("span",[t._v(t._s(t.$t("coupon_nums",{0:t.availableCouponNum})))])])])],2):e("div",{staticClass:"coupons-wrapper coupons-wrapper__not-login",on:{click:function(e){return t.$root.$emit("ClickPayButNotLogin")}}},[t._v(t._s(t.$t("view_coupons_after_login")))]),t.activity.showPop?e("coupon-choose-pop",{attrs:{"had-obtained-list":t.activity.hadObtainedList,"not-obtained-list":t.activity.notObtainedList,"last-index":t.activity.chosenIndex,"is-first-charge-used":t.activity.isFirstPayUsed},on:{close:t.closeCouponPop}}):t._e()],1)},o=[],a=(i("d9e2"),i("14d9"),i("e9f5"),i("910d"),i("7d54"),i("ab43"),i("3772")),n=i("af82"),c=i("2f62"),r=function(){var t=this,e=t._self._c;return e("div",{class:["ticket-chosen-wrapper",t.$gameName],attrs:{id:"ticket-chosen-wrapper"}},[e("div",{staticClass:"pop-main"},[e("div",{staticClass:"pop-close",on:{click:function(e){return t.$emit("close")}}}),e("div",{staticClass:"pop-title"},[e("h3",[t._v(t._s(t.$t("coupon")))]),e("span",[t._v(t._s(t.$t("discount_offer_tips")))])]),e("div",{staticClass:"divider"}),e("div",{staticClass:"nav-btn-wrapper"},[e("div",{class:["nav",{"nav-active":0===t.navIndex}],on:{click:function(e){t.navIndex=0}}},[t._v(t._s(t.$t("nav_my_coupon")))]),e("div",{class:["nav",{"nav-active":1===t.navIndex}],on:{click:function(e){t.navIndex=1}}},[t._v(t._s(t.$t("nav_other_coupon")))])]),e("div",{staticClass:"main-container"},[0===t.navIndex?[e("coupon-choose-list",{key:0,attrs:{"coupon-list":t.hadObtainedList,"is-first-charge-used":t.isFirstChargeUsed,reach:!0,"temp-chosen-coupon":t.tempChosenCoupon},on:{"update:tempChosenCoupon":function(e){t.tempChosenCoupon=e},"update:temp-chosen-coupon":function(e){t.tempChosenCoupon=e}}}),e("div",{class:["btn-confirm","click-btn",{"btn-confirm__unavailable":!t.isFirstChargeUsed}],on:{click:t.chooseCoupon}},[t._v(t._s(t.$t("modalBtnOk")))])]:e("coupon-choose-list",{key:1,attrs:{"coupon-list":t.notObtainedList,"is-first-charge-used":t.isFirstChargeUsed,reach:!1}})],2),e("p",{staticClass:"coupon-repeat-tips"},[t._v(" *"+t._s(t.$t("construction_faq_q5a1"))+" "),"RU"===t.$store.state.country?[t._v("Купоны не поддерживают Huawei Pay.")]:t._e()],2)])])},d=[],u=function(){var t=this,e=t._self._c;return e("div",{class:["ticket-wrapper",t.$gameName],attrs:{id:"ticket-wrapper"}},[e("div",{staticClass:"ticket-list"},[t._l(t.couponList,(function(i,s){return["first_pay"!==i.feType&&"first_pay_rebate"!==i.feType&&(!("leaveCount"in i)||i.leaveCount>0)?e("div",{key:i.coupon_id+i.type+s,class:["item",{item__active:t.reach&&i.FE_INDEX===t.tempChosenCoupon.FE_INDEX},{item__unavailable:!t.isFirstChargeUsed||!t.reach||0===i.is_invalid}],on:{click:function(e){return t.choose(s,i)}}},[e("div",{staticClass:"left"},[t.$store.state.formdata.switchToggleState&&i.change_enable?e("coupon-toggle",{attrs:{"temp-chosen":t.reach&&i.FE_INDEX===t.tempChosenCoupon.FE_INDEX,"coupon-item":i}}):t._e(),e("div",{staticClass:"title"},["discount_coupon"===i.feType?[t._v(t._s(i.rate)+" "),e("span",[t._v("OFF")])]:"cash_coupon"===i.feType?[e("span",{class:{"is-ar-zone":t.isArZone}},[t._v(t._s(i.deduct_price)+" "+t._s(t.currencyUnit))]),t._v(" OFF ")]:"rebate_coupon"===i.feType?[e("span",[t._v(t._s(t.$t("bonus_tips"))+" ")]),t._v(t._s(i.rate)),e("i",{staticClass:"diamond-icon"})]:t._e()],2)],1),e("div",{staticClass:"right"},[e("div",{staticClass:"desc"},[t._v(t._s(t.$t(i.langKey,{0:i.num}))+" "+t._s(i.langValue))]),i.showLeaveDate?e("div",{staticClass:"time"},[t._v(t._s(i.showLeaveDate))]):t._e()])]):t._e()]}))],2),t.showEmpty?e("span",{staticClass:"no-data"},[t._v(t._s(t.$t("nothingHere")))]):t._e()])},h=[],l=function(){var t=this,e=t._self._c;return e("div",{staticClass:"coupon-type-toggle-wrapper",on:{click:function(e){return e.stopPropagation(),t.beginToggleCoupon.apply(null,arguments)},mouseover:function(e){t.tips="discount_coupon"===t.couponItem.feType?t.$t("toggle-to-rebate"):t.$t("toggle-to-common")},mouseleave:function(e){t.tips=""}}},[t.tips?e("div",{staticClass:"tips"},[t._v(t._s(t.tips))]):t._e()])},p=[],_={name:"couponToggle",props:["couponItem","tempChosen"],data(){return{tips:""}},methods:{beginToggleCoupon(){this.tips="";const t=this.couponItem,e={change_type:"discount_coupon"===t.feType?"rebate":"coupon",coupon_id:this.couponItem.coupon_id};this.$loading.show(),Object(n["u"])(e).then(t=>{const{code:e}=t;0===e?(sessionStorage.setItem("reopenCoupon","1"),this.tempChosen&&sessionStorage.setItem("reChooseCoupon",this.couponItem.coupon_id),this.$root.$emit("couponToggleSuccess")):this.$toast.err(this.$t("toggle-fail-tips"))}).catch(t=>{console.error(t.message)}).finally(()=>this.$loading.hide())}},mounted(){this.couponItem.FE_INDEX===sessionStorage.getItem("popIndex")&&(sessionStorage.removeItem("popIndex"),this.tips="discount_coupon"===this.couponItem.feType?this.$t("toggle-to-rebate"):this.$t("toggle-to-common"),this.$el.scrollIntoView({behavior:"smooth"}))}},m=_,f=(i("929d"),i("2877")),g=Object(f["a"])(m,l,p,!1,null,"153b91ac",null),v=g.exports,C=i("fa7d"),y={name:"CouponChooseList",components:{CouponToggle:v},props:["couponList","isFirstChargeUsed","reach","tempChosenCoupon"],methods:{choose(t,e){if(!this.isFirstChargeUsed||!this.reach||0===e.is_invalid)return null;this.tempChosenCoupon.FE_INDEX===e.FE_INDEX?this.$emit("update:tempChosenCoupon",{}):(this.$emit("update:tempChosenCoupon",e),this.$root.$emit("availableTicketChosen"))}},computed:{...Object(c["c"])(["isArZone","currencyUnit"]),showEmpty(){return!this.couponList.filter(t=>t.feType.includes("_coupon")).length}},created(){if(this.$store.state.formdata.switchToggleState&&this.reach&&Object(C["o"])("toggleCoupon")){const t=this.couponList.findIndex(t=>t.change_enable);this.couponList[t]&&sessionStorage.setItem("popIndex",this.couponList[t].FE_INDEX)}}},$=y,b=(i("43ae"),Object(f["a"])($,u,h,!1,null,"3d2a8ef0",null)),P=b.exports,w={name:"CouponChoosePop",components:{CouponChooseList:P},props:["hadObtainedList","notObtainedList","isFirstChargeUsed","lastIndex"],data(){return{navIndex:0,tempChosenCoupon:this.$store.state.formdata.chosenCoupon}},computed:{...Object(c["c"])("formdata",["switchToggleState"])},methods:{chooseCoupon(){if(!this.isFirstChargeUsed)return null;this.tempChosenCoupon&&this.$store.commit("formdata/setChosenCoupon",this.tempChosenCoupon),this.$emit("close"),this.$root.$emit("couponChoose"),this.tempChosenCoupon.FE_INDEX&&this.$root.$emit("couponChosen")}},mounted(){this.$gcbk("switch.enableAnimation",!1)&&(gsap&&gsap.from(".ticket-chosen-wrapper .pop-main",{top:"45%",duration:.4,ease:"back",clearProps:!0}),gsap&&gsap.from(".ticket-chosen-wrapper",{backgroundColor:"rgba(0, 0, 0, 0)",duration:.4,clearProps:!0}))}},E=w,I=(i("5f48"),Object(f["a"])(E,r,d,!1,null,"26605e4f",null)),k=I.exports,S=i("da93"),O=i("72c2");const D=t=>(100*(1-t)).toFixed(0),x=t=>100*(t-1)&&(100*(t-1)).toFixed(0),T=t=>t.coin-t.level_coin;var A={name:"CouponChoose",components:{OverSizeScale:S["a"],CommonPart:a["a"],CouponChoosePop:k},data(){return{activity:{notObtainedList:[],hadObtainedList:[],chosenIndex:-1,showPop:!1,isFirstPayUsed:!0,timeInterval:void 0,isLoadingCoupon:!1}}},computed:{...Object(c["c"])(["urlParams","isArZone","currencyUnit"]),...Object(c["c"])("formdata",["chosenChannel","chosenCoupon","chosenDiamond","isFixedRebateWork"]),...Object(c["b"])("formdata",["FinalPriceState"]),availableCouponNum(){const t=this.activity.hadObtainedList;return t&&t.length?t.filter(t=>t.feType.includes("_coupon")&&("leaveCount"in t&&t.leaveCount>0||!("leaveCount"in t))).length:0}},methods:{initActInfo(t){const{timeInterval:e}=this.activity;e&&clearInterval(e),this.activity={notObtainedList:[],hadObtainedList:[],chosenIndex:-1,showPop:!1,isFirstPayUsed:!0,timeInterval:void 0},this.$store.commit("formdata/resetCouponInfo");const i=this.chosenDiamond.price;this.$root.$emit("setDefaultDiscountInfo",{price:i,discount_price:(.95*i).toFixed(2),feType:"fixed_discount_coupon",FE_INDEX:"fixed_discount_coupon_1",rateWidthOutPercent:5,type:"fixed_discount"}),(sessionStorage.getItem("reopenCoupon")||t)&&(sessionStorage.removeItem("reopenCoupon"),this.$nextTick(()=>{this.activity.showPop=!0}))},couponSort(t){const e=t;return e.sort((t,e)=>t.coupon_expire_time>e.coupon_expire_time?1:t.coupon_expire_time===e.coupon_expire_time?parseFloat(t.rateWidthOutPercent)<parseFloat(e.rateWidthOutPercent)?1:parseFloat(t.rateWidthOutPercent)===parseFloat(e.rateWidthOutPercent)?0:-1:-1),e},loadActivity(t=!1){const e={};e.price=this.chosenDiamond.price,e.product_id=this.chosenDiamond.product_id;const{type:i,chosenNum:s}=this.chosenDiamond;if(2===i&&(e.custom_multiple=s),!e.product_id)return null;this.chosenChannel&&(e.channel_id=this.chosenChannel.channel_id,e.sub_channel_id=this.chosenChannel.sub_channel_id),this.$store.state.IS_CHECKOUT_SDK&&(e.package_type=this.chosenDiamond.package_type),this.$store.state.IS_CHECKOUT_SDK&&!this.$store.state.country&&(e.country="US",e.currency="USD"),this.$loading.show(),this.couponLoading=!0,Object(n["g"])(e).then(i=>{this.initActInfo();const{code:s,data:o,message:a}=i;if(t&&(this.isFixedRebateWork?o.fixed_discount=[]:o.fixed_rebate=[],this.$store.commit("formdata/toggleCoupon")),0!==s)throw Error(a);{this.$store.getters["formdata/TWMyCard"]&&(o.first_pay=o.coupon=o.deduct=o.fixed_discount=[]),this.$store.commit("formdata/setIsInit",!0),this.$store.state.gameinfo.isKOA&&this.$store.commit("formdata/setFirstPayProducts",o.range_first_pay||[]),"foundation"===this.$gameName&&this.fixFoundationCoupon(e,o),this.adapterCouponType(o);let t=o.first_pay||[];t=t.map((t,i)=>({...t,feType:"first_pay",rateWidthOutPercent:D(t.discount),rate:D(t.discount)+"%",FE_INDEX:"first_pay_"+i,productId:e.product_id})),o.first_pay&&o.first_pay.length&&!o.first_pay[0].discount&&(t=[]),(o.first_pay_rebate||[]).length&&(t=(o.first_pay_rebate||[]).map((t,i)=>({...t,feType:"first_pay_rebate",rate:""+T(t),FE_INDEX:"first_pay_rebate_"+i,productId:e.product_id})));let i=o.coupon||[];i=i.map((t,i)=>({...t,feType:"discount_coupon",rateWidthOutPercent:D(t.discount),rate:D(t.discount)+"%",FE_INDEX:"discount_coupon_"+i,productId:e.product_id}));const s=i.filter(t=>t.is_received&&t.is_invalid),a=i.filter(t=>t.is_received&&!t.is_invalid);let n=o.deduct||[];n=n.map((t,i)=>({...t,feType:"cash_coupon",FE_INDEX:"cash_coupon_"+i,productId:e.product_id}));const c=n.filter(t=>t.is_received&&t.is_invalid),r=n.filter(t=>t.is_received&&!t.is_invalid);let d=o.rebate||[];d=d.map((t,i)=>({...t,feType:"rebate_coupon",FE_INDEX:"rebate_coupon_"+i,rate:x(t.discount)+"%",rateWidthOutPercent:x(t.discount),productId:e.product_id})),d=this.couponSort(d);const u=d.filter(t=>t.is_received&&t.is_invalid),h=d.filter(t=>t.is_received&&!t.is_invalid);let l=o.fixed_discount||[];l=l.map((t,e)=>({...t,feType:"fixed_discount_coupon",FE_INDEX:"fixed_discount_coupon_"+e,rateWidthOutPercent:D(t.discount)})),l.length&&this.$store.commit("formdata/setFixedCoupon",l[0]);let p=o.fixed_rebate||[];p=p.map((t,i)=>({...t,feType:"fixed_rebate",rateWidthOutPercent:x(t.discount),rate:x(t.discount)+"%",FE_INDEX:"fixed_rebate_"+i,productId:e.product_id})),this.$store.commit("formdata/setFixedRebate",p.length?p[0]:{});let _=o.product_fixed_rebate||[];_=_.map((t,i)=>({...t,feType:"fixed_dynamic_rebate",rateWidthOutPercent:x(t.discount),rate:x(t.discount)+"%",FE_INDEX:"fixed_dynamic_rebate_"+i,productId:e.product_id})),this.$store.commit("formdata/setFixedDynamicRebate",{chosen:_[0]||{},all:o.range_product_fixed_rebate||[]}),this.calcLeaveTime([...i,...n,...d].filter(t=>t.is_received));const m=[...t,...u,...c,...s,...h,...r,...a];this.activity.isFirstPayUsed=0===t.length,this.$store.commit("formdata/setFirstPayStatus",this.activity.isFirstPayUsed),this.activity.hadObtainedList=m;const f=[...u,...c,...s];if(t.length)this.$store.commit("formdata/setChosenCoupon",m[0]);else{const t=this.$store.state.IS_CHECKOUT_SDK_V2&&[...l,...p].length>0;if(f.length&&!t){let t=0;const e=sessionStorage.getItem("reChooseCoupon");e&&(t=m.findIndex(t=>t.coupon_id===+e),t=Math.max(0,t)),this.$store.commit("formdata/setChosenCoupon",m[t])}}this.parsingSdk2Coupon(f),this.activity.hadObtainedList=this.activity.hadObtainedList.map(t=>{if(t.discount_range){const e=this.$store.state.IS_CHECKOUT_SDK,i=e&&t.feType.includes("_coupon")?t.discount_price_range.split("-"):t.discount_range.split("-"),s=this.$store.state.currency;switch(this.$store.state.gameinfo.gameCode){case"KOA":case"MO":"cash_coupon"===t.feType&&(t.langValue=this.$t("min_cash_available_num",{0:i[0],1:this.$vt("tokenName")})),"discount_coupon"===t.feType&&(t.langValue=this.$t("max_cash_available_num",{0:i[1],1:this.$vt("tokenName")})),"rebate_coupon"===t.feType&&(t.langValue=this.$t("max_cash_available_num",{0:i[1],1:this.$vt("tokenName")}));break;default:i.length>1?"0"===i[0]?t.langValue=this.$t("max_cash_available_num",{0:i[1],1:this.$vt("tokenName")}):"0"===i[1]?t.langValue=this.$t("min_cash_available_num",{0:i[0],1:this.$vt("tokenName")}):t.langValue=this.$t("btw_cash_available_num2",{1:i[0],2:i[1],0:this.$vt("tokenName")}):t.langValue=this.$t("cash-num-eq-to",{0:i[0],1:this.$vt("tokenName")})}this.$store.state.IS_CHECKOUT_SDK&&(i.length>1?"0"===i[0]?t.langValue=this.$t("max_cash_available_num",{0:s,1:i[1]}):"0"===i[1]?t.langValue=this.$t("min_cash_available_num",{0:s,1:i[0]}):t.langValue=this.$t("btw_cash_available_num",{0:s,1:i[0],2:i[1]}):t.langValue=this.$t("cash-num-eq-to",{0:s,1:i[0]}))}return t});const g=[...d,...n,...i].filter(t=>!t.is_received),v={"login_0.9":[],"comm_third_0.8":[],"comm_third_0.9":[]},C={"login_0.9":"login_gain_coupon","comm_third_0.9":"invite_gain_coupon","comm_third_0.8":"invite_gain_coupon"};g.forEach(t=>{t.type.includes("login_")&&.9===t.discount&&v["login_0.9"].push(t),t.type.includes("comm_third")&&.8===t.discount&&v["comm_third_0.8"].push(t),t.type.includes("comm_third")&&.9===t.discount&&v["comm_third_0.9"].push(t)});for(const[e,o]of Object.entries(v))o.length&&this.activity.notObtainedList.push({...o[0],num:o.length,langKey:C[e]});this.activity.notObtainedList=this.activity.notObtainedList.sort((t,e)=>t.discount-e.discount),this.$root.$emit("activityInitEnd")}}).catch(t=>{this.initActInfo(),this.$toast.err(this.$t("network_err")),console.error("优惠券初始化失败："+t.message)}).finally(()=>{this.couponLoading=!1,this.$loading.hide(),sessionStorage.removeItem("reChooseCoupon")})},fixActivityInfo(){if(this.couponLoading)return null;const t=this.chosenCoupon.FE_INDEX,e={};e.price=this.chosenDiamond.price,e.product_id=this.chosenDiamond.product_id;const{type:i,chosenNum:s}=this.chosenDiamond;if(2===i&&(e.custom_multiple=s),!e.product_id)return null;this.chosenChannel&&(e.channel_id=this.chosenChannel.channel_id,e.sub_channel_id=this.chosenChannel.sub_channel_id),this.$store.state.IS_CHECKOUT_SDK&&(e.package_type=this.chosenDiamond.package_type),this.$store.state.IS_CHECKOUT_SDK&&!this.$store.state.country&&(e.country="US",e.currency="USD"),Object(n["g"])(e).then(i=>{this.initActInfo(this.activity.showPop);const{code:s,data:o,message:a}=i;if(0!==s)throw Error(a);{this.$store.getters["formdata/TWMyCard"]&&(o.first_pay=o.coupon=o.deduct=o.fixed_discount=[]),this.$store.commit("formdata/setIsInit",!0),"foundation"===this.$gameName&&this.fixFoundationCoupon(e,o),this.adapterCouponType(o);let i=o.first_pay||[];i=i.map((t,i)=>({...t,feType:"first_pay",rateWidthOutPercent:D(t.discount),rate:D(t.discount)+"%",FE_INDEX:"first_pay_"+i,productId:e.product_id})),o.first_pay&&o.first_pay.length&&!o.first_pay[0].discount&&(i=[]),(o.first_pay_rebate||[]).length&&(i=(o.first_pay_rebate||[]).map((t,i)=>({...t,feType:"first_pay_rebate",rate:""+T(t),FE_INDEX:"first_pay_rebate_"+i,productId:e.product_id})));let s=o.coupon||[];s=s.map((t,i)=>({...t,feType:"discount_coupon",rateWidthOutPercent:D(t.discount),rate:D(t.discount)+"%",FE_INDEX:"discount_coupon_"+i,productId:e.product_id}));const a=s.filter(t=>t.is_received&&t.is_invalid),n=s.filter(t=>t.is_received&&!t.is_invalid);let c=o.deduct||[];c=c.map((t,i)=>({...t,feType:"cash_coupon",FE_INDEX:"cash_coupon_"+i,productId:e.product_id}));const r=c.filter(t=>t.is_received&&t.is_invalid),d=c.filter(t=>t.is_received&&!t.is_invalid);let u=o.rebate||[];u=u.map((t,i)=>({...t,feType:"rebate_coupon",FE_INDEX:"rebate_coupon_"+i,rate:x(t.discount)+"%",rateWidthOutPercent:x(t.discount),productId:e.product_id})),u=this.couponSort(u);const h=u.filter(t=>t.is_received&&t.is_invalid),l=u.filter(t=>t.is_received&&!t.is_invalid);let p=o.fixed_discount||[];p=p.map((t,e)=>({...t,feType:"fixed_discount_coupon",FE_INDEX:"fixed_discount_coupon_"+e,rateWidthOutPercent:D(t.discount)})),p.length&&this.$store.commit("formdata/setFixedCoupon",p[0]);let _=o.fixed_rebate||[];_=_.map((t,i)=>({...t,feType:"fixed_rebate",rateWidthOutPercent:x(t.discount),rate:x(t.discount)+"%",FE_INDEX:"fixed_rebate_"+i,productId:e.product_id})),this.$store.commit("formdata/setFixedRebate",_.length?_[0]:{});let m=o.product_fixed_rebate||[];m=m.map((t,i)=>({...t,feType:"fixed_dynamic_rebate",rateWidthOutPercent:x(t.discount),rate:x(t.discount)+"%",FE_INDEX:"fixed_dynamic_rebate_"+i,productId:e.product_id})),this.$store.commit("formdata/setFixedDynamicRebate",{chosen:m[0]||{},all:o.range_product_fixed_rebate||[]}),this.calcLeaveTime([...s,...c,...u].filter(t=>t.is_received));const f=[...i,...h,...r,...a,...l,...d,...n];this.activity.isFirstPayUsed=0===i.length,this.$store.commit("formdata/setFirstPayStatus",this.activity.isFirstPayUsed),this.activity.hadObtainedList=f;const g=[...h,...r,...a];if(t)if(i.length)t===f[0].FE_INDEX&&this.$store.commit("formdata/setChosenCoupon",f[0]);else if(g.length){const e=f.findIndex(e=>e.FE_INDEX===t)||0;this.$store.commit("formdata/setChosenCoupon",f[e])}this.parsingSdk2Coupon(g),this.activity.hadObtainedList=this.activity.hadObtainedList.map(t=>{if(t.discount_range){const e=this.$store.state.IS_CHECKOUT_SDK,i=e&&t.feType.includes("_coupon")?t.discount_price_range.split("-"):t.discount_range.split("-"),s=this.$store.state.currency;switch(this.$store.state.gameinfo.gameCode){case"KOA":case"MO":"cash_coupon"===t.feType&&(t.langValue=this.$t("min_cash_available_num",{0:i[0],1:this.$vt("tokenName")})),"discount_coupon"===t.feType&&(t.langValue=this.$t("max_cash_available_num",{0:i[1],1:this.$vt("tokenName")})),"rebate_coupon"===t.feType&&(t.langValue=this.$t("max_cash_available_num",{0:i[1],1:this.$vt("tokenName")}));break;default:i.length>1?"0"===i[0]?t.langValue=this.$t("max_cash_available_num",{0:i[1],1:this.$vt("tokenName")}):"0"===i[1]?t.langValue=this.$t("min_cash_available_num",{0:i[0],1:this.$vt("tokenName")}):t.langValue=this.$t("btw_cash_available_num2",{1:i[0],2:i[1],0:this.$vt("tokenName")}):t.langValue=this.$t("cash-num-eq-to",{0:i[0],1:this.$vt("tokenName")})}this.$store.state.IS_CHECKOUT_SDK&&(i.length>1?"0"===i[0]?t.langValue=this.$t("max_cash_available_num",{0:s,1:i[1]}):"0"===i[1]?t.langValue=this.$t("min_cash_available_num",{0:s,1:i[0]}):t.langValue=this.$t("btw_cash_available_num",{0:s,1:i[0],2:i[1]}):t.langValue=this.$t("cash-num-eq-to",{0:s,1:i[0]}))}return t});const v=[...u,...c,...s].filter(t=>!t.is_received),C={"login_0.9":[],"comm_third_0.8":[],"comm_third_0.9":[]},y={"login_0.9":"login_gain_coupon","comm_third_0.9":"invite_gain_coupon","comm_third_0.8":"invite_gain_coupon"};v.forEach(t=>{t.type.includes("login_")&&.9===t.discount&&C["login_0.9"].push(t),t.type.includes("comm_third")&&.8===t.discount&&C["comm_third_0.8"].push(t),t.type.includes("comm_third")&&.9===t.discount&&C["comm_third_0.9"].push(t)});for(const[t,e]of Object.entries(C))e.length&&this.activity.notObtainedList.push({...e[0],num:e.length,langKey:y[t]});this.activity.notObtainedList=this.activity.notObtainedList.sort((t,e)=>t.discount-e.discount)}}).catch(t=>{this.initActInfo(),this.$toast.err(this.$t("network_err")),console.error("优惠券初始化失败："+t.message)})},calcLeaveTime(t){const e=t=>t<10?"0"+Math.floor(t):Math.floor(t),i=t=>`${Math.floor(t/3600/24)}d ${e(t/3600%24)} : ${e(t/60%60)} : ${e(t%60)}`,s=t.filter(t=>t.coupon_expire_time&&t.coupon_expire_time>0);for(const o of Object.values(s)){const t=o.leaveCount=o.coupon_expire_time;o.showLeaveDate=i(t)}this.activity.timeInterval=setInterval(()=>{for(const t of Object.values(s)){const e=t.leaveCount-1;e>=0&&(t.leaveCount--,t.showLeaveDate=i(e)),0===e&&this.chosenCoupon.FE_INDEX===t.FE_INDEX&&(this.$store.commit("formdata/setChosenCoupon",{}),this.$root.$emit("couponChoose"))}},1e3)},closeCouponPop(t){this.activity.showPop=!1,this.$root.$emit("TicketPopClose")},parsingSdk2Coupon(t){this.$store.state.IS_CHECKOUT_SDK_V2&&this.$root.$emit("updateSdk2CouponList",t)},fixFoundationCoupon(t,e){const i=e.coin_level_first_pay;if(!i||!i.length)return null;const s=i.map(t=>(t.rate=""+T(t),t.feType="first_pay_rebate",t));s.length&&this.$store.commit("formdata/setFirstPayProducts",[{product_discount_range:s}]);const o=s.filter(e=>e.product_id===t.product_id)||[];o.length&&(o[0].act_type="coin_level_first_pay",e.first_pay_rebate=o)},adapterCouponType(t){this.$store.state.IS_CHECKOUT_SDK_V2&&(t.first_pay=t.direct_first_pay,t.first_pay_rebate=t.direct_first_pay_rebate,t.fixed_discount=t.direct_fixed_discount,t.fixed_rebate=t.direct_fixed_rebate,Reflect.deleteProperty(t,"direct_first_pay"),Reflect.deleteProperty(t,"direct_first_pay_rebate"),Reflect.deleteProperty(t,"direct_fixed_discount"),Reflect.deleteProperty(t,"direct_fixed_rebate"))}},created(){const t="$store.state.formdata.chosenDiamond.product_id";this.unwatch=this.$watch(t,t=>t&&this.loadActivity(),{immediate:!0});const e="$store.state.formdata.chosenDiamond.totalDiamond";this.unwatch=this.$watch(e,(t,e)=>t&&e&&this.loadActivity()),this.$root.$once("loginSuccess",()=>this.loadActivity());const i="$store.state.formdata.chosenChannel.FE_CHANNEL_ID";this.unwatch=this.$watch(i,t=>t&&this.fixActivityInfo()),this.$root.$on("couponToggleSuccess",()=>this.loadActivity()),this.$root.$on("reloadActivity",()=>this.loadActivity()),this.$root.$on("toggleFixedCoupon",t=>this.loadActivity(t))},mounted(){O["a"].ios()&&this.$watch("activity.showPop",t=>this.$root.$emit("showCouponPop",t))},beforeDestroy(){this.unwatch&&this.unwatch()}},N=A,F=(i("ec3d"),Object(f["a"])(N,s,o,!1,null,"066665a8",null));e["a"]=F.exports},"88a7":function(t,e,i){"use strict";var s=i("cb2d"),o=i("e330"),a=i("577e"),n=i("d6d6"),c=URLSearchParams,r=c.prototype,d=o(r.append),u=o(r["delete"]),h=o(r.forEach),l=o([].push),p=new c("a=1&a=2&b=3");p["delete"]("a",1),p["delete"]("b",void 0),p+""!=="a=2"&&s(r,"delete",(function(t){var e=arguments.length,i=e<2?void 0:arguments[1];if(e&&void 0===i)return u(this,t);var s=[];h(this,(function(t,e){l(s,{key:e,value:t})})),n(e,1);var o,c=a(t),r=a(i),p=0,_=0,m=!1,f=s.length;while(p<f)o=s[p++],m||o.key===c?(m=!0,u(this,o.key)):_++;while(_<f)o=s[_++],o.key===c&&o.value===r||d(this,o.key,o.value)}),{enumerable:!0,unsafe:!0})},"8d29":function(t,e,i){"use strict";var s=function(){var t=this,e=t._self._c;return e("common-part",{scopedSlots:t._u([{key:"label",fn:function(){return[e("div",{staticClass:"package-label"},[e("div",[t._v(t._s(t.$t("adyen-order-info")))]),t.$gcbk("switch.switchRebate",!1)&&t.$store.state.formdata.isFixedEventOpen?e("div",[e("fixed-coupon-switch")],1):t._e()])]},proxy:!0}])},[e("div",{staticClass:"goods-name",class:[t.$gameName],attrs:{id:"direct-package-name"}},[t._v(" "+t._s(t.goodsName)+" ")])])},o=[],a=(i("bbd2"),i("3772")),n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"coupon-switch-toggle",on:{click:function(e){return t.$root.$emit("toggleFixedCoupon",!0)}}},[e("i",{staticClass:"toggle-icon",class:{active:t.isFixedRebateWork}}),e("span",{staticClass:"toggle-lang"},[t._v(t._s(t.$t("gog-fixed-coupon-switch").replace("5 %","5%")))])])},c=[],r=i("2f62"),d={name:"fixed-coupon-switch",computed:{...Object(r["c"])("formdata",["isFixedRebateWork"])}},u=d,h=(i("b599"),i("2877")),l=Object(h["a"])(u,n,c,!1,null,"2b400ddb",null),p=l.exports,_=i("af82"),m={components:{CommonPart:a["a"],FixedCouponSwitch:p},data(){return{goodsName:"-"}},methods:{loadDiamondList(){const t={};if(this.$store.state.urlParams.tc){const e=JSON.parse(this.$store.state.urlParams.tc);t.product_id=e.product_id,this.goodsName=e.product_name}this.$loading.show(),Object(_["l"])(t).then(t=>{const{data:e,code:i}=t;if(0===i){if(e.length&&1!==e.length)return this.$toast.err(this.$t("cb_page_title_err"));this.$store.commit("formdata/setChosenDiamond",e[0])}}).finally(()=>this.$loading.hide())},loadOtherDiamondList(){const t={game_order_id:this.$store.state.urlParams.oid||""};if(this.$store.state.urlParams.tc){const e=JSON.parse(this.$store.state.urlParams.tc);t.product_id=e.product_id,e.product_name&&(this.goodsName=e.product_name,this.$root.$emit("updateSdk2PackageName",e.product_name))}if(!t.game_order_id)return this.$toast.err(this.$t("sdk2_error_order"));this.$loading.show(),Object(_["l"])(t).then(t=>{const{data:e,code:i}=t;switch(i){case 0:this.$store.commit("formdata/setChosenDiamond",e),e.sku_name&&"-"===this.goodsName&&(this.goodsName=e.sku_name,this.$store.commit("updateUrlParams",{product_name:e.sku_name}),this.$root.$emit("updateSdk2PackageName",this.goodsName)),"-"===this.goodsName&&this.setDefaultName(e);break;case 106041:this.$toast.err(this.$t("cb_view_err_tips"));break;default:this.$toast.err(this.$t("RU_refused"))}}).finally(()=>this.$loading.hide())},resetSdkStatus(){localStorage.setItem("isWhatDiamondPop","true")},loginFail(){this.$nextTick(()=>{document.querySelector(".checkout-footer-wrapper")&&(document.querySelector(".checkout-footer-wrapper").style.display="none"),document.querySelector(".checkout-counter-sdk-b")&&(document.querySelector(".checkout-counter-sdk-b").style.display="none")})},setDefaultName(t){const e=this.$t("sdk2_default_package_name",{1:t.no_tax_price,0:t.currency_symbol});this.goodsName=e,this.$store.commit("updateUrlParams",{product_name:e}),this.$root.$emit("updateSdk2PackageName",e)}},created(){this.resetSdkStatus(),this.$root.$on("loginEnd",t=>{1===t&&("dc"===this.$gameName?this.loadDiamondList():this.loadOtherDiamondList()),0===t&&this.loginFail()}),console.log(this.$gcbk("switch.switchRebate",!1))}},f=m,g=(i("733f"),Object(h["a"])(f,s,o,!1,null,"cf7c3d58",null));e["a"]=g.exports},"929d":function(t,e,i){"use strict";i("9f15")},"9f15":function(t,e,i){},b534:function(t,e,i){"use strict";i("bf28")},b599:function(t,e,i){"use strict";i("f7c8")},bbd2:function(t,e,i){const s=[i("fb5d"),i("774a")],o=t=>t?new Promise((e,i)=>{const s=document.createElement("link");s.as="image",s.rel="preload",s.href=t,document.head.appendChild(s),s.onload=e,s.onerror=i,setTimeout(i,5e3)}):Promise.resolve(),a=async()=>{while(s.length)try{await o(s.shift())}catch(t){console.error(t)}};Promise.all(Array.from({length:1},a))},bf28:function(t,e,i){},c3e4:function(t,e,i){"use strict";i("e8db")},d6d6:function(t,e,i){"use strict";var s=TypeError;t.exports=function(t,e){if(t<e)throw new s("Not enough arguments");return t}},da93:function(t,e,i){"use strict";var s=function(){var t=this,e=t._self._c;return e("span",{ref:"outer",staticClass:"outer"},[e("span",{ref:"inner",staticClass:"inner",style:`transform: scale(${t.scale})`},[t._t("default")],2)])},o=[],a={name:"OverSizeScale",data(){return{scale:1}},methods:{calc(){const t=this.$refs.outer.offsetWidth,e=this.$refs.inner.offsetWidth;e>t&&(this.scale=t/e)}},mounted(){this.calc()}},n=a,c=(i("b534"),i("2877")),r=Object(c["a"])(n,s,o,!1,null,"62189d20",null);e["a"]=r.exports},e28d:function(t,e,i){"use strict";var s=function(){var t=this,e=t._self._c;return e("div",{class:["checkout-footer-wrapper",t.$gameName,{sdk:t.IS_CHECKOUT_SDK}],attrs:{id:"checkout-footer-wrapper"},on:{click:function(t){t.stopPropagation()}}},[e("transition",{attrs:{name:"fade"}},[t.expandMode?e("div",{staticClass:"expand-part"},[e("div",{staticClass:"pop-title"},[t._v(t._s(t.$t("tax-details")))]),e("div",{staticClass:"divider"}),e("div",{staticClass:"value-wrapper"},[e("div",{staticClass:"origin-price"},[e("span",[t._v(t._s(t.$t("tax-price"))),t.IS_CHECKOUT_SDK?[t._v("：")]:t._e()],2),e("span",[t._v(t._s(t.chosenDiamond.nowPrice||t.chosenDiamond.level_currency_price)+t._s(t.currencyUnit))])]),t.FinalPriceState.feType&&t.hideDiscountRow?e("div",{staticClass:"discount"},[e("span",[t._v(t._s(t.$t("tax-discount"))+" "),t.IS_CHECKOUT_SDK?[t._v("：")]:t._e()],2),e("span",[[["first_pay","discount_coupon"].includes(t.chosenCoupon.feType)?[t._v(" - "+t._s(t.chosenCoupon.discount_amount)+" "+t._s(t.currencyUnit)+" ("+t._s(t.chosenCoupon.rateWidthOutPercent)+"% OFF) ")]:t._e(),"cash_coupon"===t.chosenCoupon.feType?[t._v(" - "+t._s(t.chosenCoupon.discount_amount)+" "+t._s(t.currencyUnit)+" ")]:t._e(),"fixed_discount_coupon"===t.FinalPriceState.feType?[t._v(" - "+t._s(t.FinalPriceState.offCountAmount)+" "+t._s(t.currencyUnit)+" ")]:t._e()]],2)]):t._e(),t.taxCost?e("div",{staticClass:"tax"},[e("span",[t._v(t._s(t.$t("tax-txt"))),t.IS_CHECKOUT_SDK?[t._v("：")]:t._e()],2),e("span",[t._v(t._s(t.taxCost)+t._s(t.currencyUnit))])]):t._e(),t.extraCost?e("div",{staticClass:"tax"},[e("span",[t._v(t._s(t.$t("extra-txt"))),t.IS_CHECKOUT_SDK?[t._v("：")]:t._e()],2),e("span",[t._v(t._s(t.extraCost)+t._s(t.currencyUnit))])]):t._e()]),e("div",{staticClass:"divider"})]):t._e()]),e("div",{staticClass:"common-part"},[e("div",{staticClass:"total-price"},[e("div",{staticClass:"row-1"},[e("span",{staticClass:"now-price",class:{"is-ar-zone":t.isArZone}},[t._v(t._s(t.FinalPriceState.finalNowPrice))]),t.showTaxBtn?e("span",{staticClass:"rate",class:{active:t.expandMode},on:{click:function(e){t.expandMode=!t.expandMode}}},[t._v("+ "+t._s(t.$t("tax-txt"))),e("i")]):t._e()]),e("div",{staticClass:"row-2"},[t.FinalPriceState.finalOriginPrice?e("span",{class:["origin-price",{"is-ar-zone":t.isArZone}]},[t._v(t._s(t.FinalPriceState.finalOriginPrice))]):t._e(),t.FinalPriceState.offCountTips?e("span",{staticClass:"off-count-tips",class:{"off-count-left":!t.hideDiscountRow},domProps:{innerHTML:t._s(t.FinalPriceState.offCountTips)}}):t._e()])]),e("div",{staticClass:"btn click-btn",class:[{disable:t.requestLoading||t.$store.getters["riskPolicy/forbiddenAccess"]},t.$i18n.locale],on:{click:function(e){return t.$emit("purchaseGoods")}}},[e("span",[t._v(t._s(t.$t("shop_now")))]),t.vip.isNewUser?e("i"):t._e()])])],1)},o=[],a=i("2f62"),n={name:"CheckoutFooterTax",props:["requestLoading"],data(){return{expandMode:!1}},computed:{...Object(a["c"])(["urlParams","isArZone","currencyUnit","IS_CHECKOUT_SDK"]),...Object(a["c"])("formdata",["chosenChannel","chosenDiamond","chosenCoupon","vip"]),...Object(a["c"])("gameinfo",["defaultDiscount","gameCode"]),...Object(a["c"])("userinfo",["isLogin"]),...Object(a["b"])("formdata",["FinalPriceState","getRebateCoin","getSDKRebateCoin"]),taxCost(){return this.chosenCoupon.taxation||this.FinalPriceState.taxation||this.chosenDiamond.taxation},extraCost(){return this.chosenCoupon.extra_fee_amount||this.FinalPriceState.extra_fee_amount||this.chosenDiamond.extra_fee_amount},showTaxBtn(){return this.taxCost||this.extraCost},hideDiscountRow(){return!!this.FinalPriceState.feType&&!this.FinalPriceState.feType.includes("rebate")}},watch:{showTaxBtn(t){t||(this.expandMode=!1)}},mounted(){this.$root.$on("BodyClick",()=>{this.expandMode=!1})}},c=n,r=(i("7673"),i("2877")),d=Object(r["a"])(c,s,o,!1,null,"4ceefa9e",null);e["a"]=d.exports},e8db:function(t,e,i){},ec3d:function(t,e,i){"use strict";i("2fdd")},f7c8:function(t,e,i){},fb5d:function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAABDwAAACGBAMAAADDWofQAAAAD1BMVEUAAAD/66//67L/6bH/6rHH7gOuAAAABHRSTlMAQL+ATezaDQAAAq1JREFUeNrs3FFu01AURVEXGICDGICFOoBUMIAmefMfEx8tGJA4QpXqd2+01gTys+34WTlZ4D+cjvTHB1PPX218HYdafjMo6HZefvk8Drbun/0wKOlpefVhvKucx8dBTefX6/dxHG2TRwMvF/GXcbjnPY9Pg6Ius778b3se3wdVrfuTx7EcXDrYZl2+2/5YTFnXWZfvzXdLB+usk8P2840LhW3TTg5np9r6nufd3b+dTu4dxV18+ZOeTR8H/MPVewfkwdvIA3kgD+RBIA8CeRDIg0AeBPIgkAeBPAjkQSAPAnkQ3FEet/XBL9Wqm5bHdV0WP1Wrbloel5d5L6VNy+NsI9fAtDxW//3SwLQ8DLA7mHf3kEcD8/IwomxgWh6bPBqYd7D19w0NLGOWs5tHfRNfqj+dvDStztkBeSAP5EEgDwJ5EMiDQB4E8iCQB4E8CORBIA8CeRDIg0AeBPIgkAfBHeVhod+AhT4V87DQ78BCn4p5WOh3YKFPxTws9Duw0KdiHhb6HVjoUzEPC/0OLPQpmQcNyAN5IA/kQSAPAnkQyINAHgTyIJAHgTwI5EEgDwJ5EMiDQB4E8iC4ozws9Buw0KdiHhb6HVjoUzEPC/0OLPSpmIeFfgcW+lTMw0K/Awt9KuZhod+BhT4l86ABeSAP5IE8CORBIA8CeRDI40c7d3CDMAwEUXQkKCB0AKnAEg2EdfqvCWQf4MIecsjO4b8WPLIsa3eQIB5IEA8kiAcSxAMJ4oEE8cCZWFTDf0E8QDxwSC9bc42bdCGb3l667iVi7rkwqm5tq5oWbxKLLvaatFfo+mDTxV3VATWJ68Ner+rY0ERBkLVWdEBdAwVB1kJFFT2bBgqCrN2rnoeNePjrZc/DRQP9UcZi0VDweyni4S6avp7rqfRjhaHHvDveAy+ZKkukVxYAAAAASUVORK5CYII="},fef2:function(t,e,i){},fefc:function(t,e,i){}}]);
//# sourceMappingURL=pagePay~pageSdkV2.27b94354.js.map