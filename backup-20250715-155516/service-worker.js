// 优化后的Service Worker
// 版本号，用于缓存更新
const CACHE_VERSION = 'v2.0.0'
const CACHE_NAME = `pay-hub-${CACHE_VERSION}`

// 缓存策略配置
const CACHE_STRATEGIES = {
  // 静态资源 - 缓存优先
  STATIC: 'cache-first',
  // API请求 - 网络优先
  API: 'network-first',
  // 图片资源 - 缓存优先，网络降级
  IMAGES: 'cache-first',
  // HTML文档 - 网络优先，缓存降级
  DOCUMENTS: 'network-first'
}

// 需要预缓存的关键资源
const PRECACHE_URLS = [
  '/',
  '/static/css/app.css',
  '/static/js/app.js',
  '/static/js/chunk-vendors.js',
  '/static/fonts/main.woff2',
  '/config/koa.manifest.webmanifest',
  '/config/dc.manifest.webmanifest'
]

// API缓存配置
const API_CACHE_CONFIG = {
  // 用户信息缓存30分钟
  '/account/store/u': { ttl: 30 * 60 * 1000, strategy: 'network-first' },
  // 商品列表缓存10分钟
  '/api/sdk/coin_products': { ttl: 10 * 60 * 1000, strategy: 'network-first' },
  // 支付渠道缓存1小时
  '/token/channel/list': { ttl: 60 * 60 * 1000, strategy: 'cache-first' },
  // 汇率信息缓存24小时
  '/token/getIpCurrency': { ttl: 24 * 60 * 60 * 1000, strategy: 'cache-first' }
}

// 安装事件 - 预缓存关键资源
self.addEventListener('install', event => {
  console.log('Service Worker installing...')
  
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => {
        console.log('Precaching static resources...')
        return cache.addAll(PRECACHE_URLS)
      })
      .then(() => {
        console.log('Service Worker installed successfully')
        // 强制激活新的Service Worker
        return self.skipWaiting()
      })
      .catch(error => {
        console.error('Service Worker installation failed:', error)
      })
  )
})

// 激活事件 - 清理旧缓存
self.addEventListener('activate', event => {
  console.log('Service Worker activating...')
  
  event.waitUntil(
    caches.keys()
      .then(cacheNames => {
        return Promise.all(
          cacheNames.map(cacheName => {
            if (cacheName !== CACHE_NAME && cacheName.startsWith('pay-hub-')) {
              console.log('Deleting old cache:', cacheName)
              return caches.delete(cacheName)
            }
          })
        )
      })
      .then(() => {
        console.log('Service Worker activated successfully')
        // 立即控制所有客户端
        return self.clients.claim()
      })
  )
})

// 获取事件 - 处理网络请求
self.addEventListener('fetch', event => {
  const { request } = event
  const url = new URL(request.url)
  
  // 只处理同源请求和特定的第三方请求
  if (!shouldHandleRequest(request)) {
    return
  }
  
  // 根据请求类型选择缓存策略
  if (isStaticResource(request)) {
    event.respondWith(handleStaticResource(request))
  } else if (isAPIRequest(request)) {
    event.respondWith(handleAPIRequest(request))
  } else if (isImageRequest(request)) {
    event.respondWith(handleImageRequest(request))
  } else if (isDocumentRequest(request)) {
    event.respondWith(handleDocumentRequest(request))
  }
})

// 判断是否应该处理该请求
function shouldHandleRequest(request) {
  const url = new URL(request.url)
  
  // 只处理GET请求
  if (request.method !== 'GET') {
    return false
  }
  
  // 跳过chrome-extension等特殊协议
  if (!url.protocol.startsWith('http')) {
    return false
  }
  
  // 跳过某些第三方域名
  const skipDomains = ['google-analytics.com', 'googletagmanager.com']
  if (skipDomains.some(domain => url.hostname.includes(domain))) {
    return false
  }
  
  return true
}

// 判断是否为静态资源
function isStaticResource(request) {
  const url = new URL(request.url)
  return url.pathname.match(/\.(js|css|woff2?|ttf|eot)$/)
}

// 判断是否为API请求
function isAPIRequest(request) {
  const url = new URL(request.url)
  return url.pathname.startsWith('/api/') || 
         url.pathname.startsWith('/token/') || 
         url.pathname.startsWith('/account/')
}

// 判断是否为图片请求
function isImageRequest(request) {
  const url = new URL(request.url)
  return url.pathname.match(/\.(png|jpg|jpeg|gif|svg|webp|ico)$/)
}

// 判断是否为文档请求
function isDocumentRequest(request) {
  return request.destination === 'document'
}

// 处理静态资源 - 缓存优先策略
async function handleStaticResource(request) {
  try {
    const cache = await caches.open(CACHE_NAME)
    const cachedResponse = await cache.match(request)
    
    if (cachedResponse) {
      return cachedResponse
    }
    
    const networkResponse = await fetch(request)
    
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone())
    }
    
    return networkResponse
  } catch (error) {
    console.error('Static resource fetch failed:', error)
    return new Response('Resource not available', { status: 503 })
  }
}

// 处理API请求 - 网络优先策略
async function handleAPIRequest(request) {
  const url = new URL(request.url)
  const pathname = url.pathname
  const config = API_CACHE_CONFIG[pathname] || { ttl: 5 * 60 * 1000, strategy: 'network-first' }
  
  try {
    if (config.strategy === 'network-first') {
      return await networkFirstStrategy(request, config)
    } else {
      return await cacheFirstStrategy(request, config)
    }
  } catch (error) {
    console.error('API request failed:', error)
    return new Response(JSON.stringify({ error: 'Service unavailable' }), {
      status: 503,
      headers: { 'Content-Type': 'application/json' }
    })
  }
}

// 网络优先策略
async function networkFirstStrategy(request, config) {
  try {
    const networkResponse = await fetch(request)
    
    if (networkResponse.ok) {
      const cache = await caches.open(CACHE_NAME)
      const responseToCache = networkResponse.clone()
      
      // 添加时间戳用于TTL检查
      const headers = new Headers(responseToCache.headers)
      headers.set('sw-cache-timestamp', Date.now().toString())
      
      const modifiedResponse = new Response(responseToCache.body, {
        status: responseToCache.status,
        statusText: responseToCache.statusText,
        headers
      })
      
      cache.put(request, modifiedResponse)
    }
    
    return networkResponse
  } catch (error) {
    // 网络失败，尝试从缓存获取
    const cache = await caches.open(CACHE_NAME)
    const cachedResponse = await cache.match(request)
    
    if (cachedResponse && !isCacheExpired(cachedResponse, config.ttl)) {
      return cachedResponse
    }
    
    throw error
  }
}

// 缓存优先策略
async function cacheFirstStrategy(request, config) {
  const cache = await caches.open(CACHE_NAME)
  const cachedResponse = await cache.match(request)
  
  if (cachedResponse && !isCacheExpired(cachedResponse, config.ttl)) {
    return cachedResponse
  }
  
  try {
    const networkResponse = await fetch(request)
    
    if (networkResponse.ok) {
      const headers = new Headers(networkResponse.headers)
      headers.set('sw-cache-timestamp', Date.now().toString())
      
      const modifiedResponse = new Response(networkResponse.body, {
        status: networkResponse.status,
        statusText: networkResponse.statusText,
        headers
      })
      
      cache.put(request, modifiedResponse.clone())
      return modifiedResponse
    }
    
    return networkResponse
  } catch (error) {
    if (cachedResponse) {
      return cachedResponse
    }
    throw error
  }
}

// 检查缓存是否过期
function isCacheExpired(response, ttl) {
  const timestamp = response.headers.get('sw-cache-timestamp')
  if (!timestamp) return true
  
  const cacheTime = parseInt(timestamp)
  const now = Date.now()
  
  return (now - cacheTime) > ttl
}

// 处理图片请求 - 缓存优先策略
async function handleImageRequest(request) {
  try {
    const cache = await caches.open(CACHE_NAME)
    const cachedResponse = await cache.match(request)
    
    if (cachedResponse) {
      return cachedResponse
    }
    
    const networkResponse = await fetch(request)
    
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone())
    }
    
    return networkResponse
  } catch (error) {
    console.error('Image fetch failed:', error)
    // 返回占位图片或默认响应
    return new Response('', { status: 404 })
  }
}

// 处理文档请求 - 网络优先策略
async function handleDocumentRequest(request) {
  try {
    const networkResponse = await fetch(request)
    return networkResponse
  } catch (error) {
    // 网络失败时返回缓存的首页
    const cache = await caches.open(CACHE_NAME)
    const cachedResponse = await cache.match('/')
    
    if (cachedResponse) {
      return cachedResponse
    }
    
    return new Response('Page not available offline', { status: 503 })
  }
}

// 消息处理 - 与主线程通信
self.addEventListener('message', event => {
  const { type, payload } = event.data
  
  switch (type) {
    case 'SKIP_WAITING':
      self.skipWaiting()
      break
      
    case 'CLEAR_CACHE':
      clearAllCaches()
      break
      
    case 'GET_CACHE_SIZE':
      getCacheSize().then(size => {
        event.ports[0].postMessage({ type: 'CACHE_SIZE', payload: size })
      })
      break
      
    default:
      console.log('Unknown message type:', type)
  }
})

// 清理所有缓存
async function clearAllCaches() {
  const cacheNames = await caches.keys()
  await Promise.all(
    cacheNames.map(cacheName => caches.delete(cacheName))
  )
  console.log('All caches cleared')
}

// 获取缓存大小
async function getCacheSize() {
  const cache = await caches.open(CACHE_NAME)
  const requests = await cache.keys()
  
  let totalSize = 0
  for (const request of requests) {
    const response = await cache.match(request)
    if (response) {
      const blob = await response.blob()
      totalSize += blob.size
    }
  }
  
  return {
    entries: requests.length,
    size: totalSize,
    sizeFormatted: formatBytes(totalSize)
  }
}

// 格式化字节大小
function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
