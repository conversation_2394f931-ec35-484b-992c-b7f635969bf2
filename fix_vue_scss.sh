#!/bin/bash

# 查找所有包含@use规则的Vue文件
find src -name "*.vue" -exec grep -l "@use.*utils" {} \; | while read file; do
    echo "Processing $file"

    # 使用sed替换@use为@import并移除as utils
    sed -i '' 's/@use "~@\/utils\/utils.scss" as utils;/@import "~@\/utils\/utils.scss";/g' "$file"
    sed -i '' 's/@use "~@\/utils\/utils" as utils;/@import "~@\/utils\/utils.scss";/g' "$file"

    # 移除所有utils.前缀
    sed -i '' 's/utils\.//g' "$file"
done

echo "Done!"
