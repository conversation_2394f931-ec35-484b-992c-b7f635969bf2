const CopyWebpackPlugin = require('copy-webpack-plugin')
const CompressionPlugin = require('compression-webpack-plugin')
const path = require('path')
const AllPageConfig = require('./config/metaConfig.js')
const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin

const GAME = process.env.VUE_APP_GAME
const isDev = process.env.NODE_ENV === 'development'
const isProduction = process.env.NODE_ENV === 'production'
const timeMark = getDateMark()

const config = {
  publicPath: '/', // 基本路径
  lintOnSave: false, // 生产环境关闭lint检查以提升构建速度
  
  configureWebpack: {
    devtool: isDev ? 'eval-cheap-module-source-map' : false, // 优化source map
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src')
      }
    },
    plugins: [
      new CopyWebpackPlugin([
        { from: './robots.txt' },
        { from: './config/pwa', to: 'config' },
        { from: './config/service-worker.js' }
      ]),
      // 生产环境启用Gzip压缩
      ...(isProduction ? [
        new CompressionPlugin({
          test: /\.(js|css|html|svg)$/,
          threshold: 8192, // 8KB以上的文件才压缩
          deleteOriginalAssets: false,
          algorithm: 'gzip'
        })
      ] : []),
      // 开发环境可选择性启用bundle分析
      // new BundleAnalyzerPlugin({ analyzerMode: 'server', openAnalyzer: false })
    ],
    optimization: {
      // 代码分割优化
      splitChunks: {
        chunks: 'all',
        minSize: 20000,
        maxSize: 244000, // 244KB chunks
        cacheGroups: {
          // 第三方支付SDK分离
          paymentSDK: {
            name: 'chunk-payment-sdk',
            test: /[\\/]node_modules[\\/](@adyen|@airwallex|@checkout)/,
            priority: 20,
            chunks: 'all',
            enforce: true
          },
          // 支付相关组件分离
          payment: {
            name: 'chunk-payment',
            test: /[\\/]src[\\/](views[\\/]paymethod|components[\\/](ChannelChoose|coupon))/,
            priority: 15,
            chunks: 'all'
          },
          // 游戏特定代码分离
          koa: {
            name: 'chunk-koa',
            test: /[\\/]src[\\/].*koa/i,
            priority: 12,
            chunks: 'all'
          },
          dc: {
            name: 'chunk-dc',
            test: /[\\/]src[\\/].*dc/i,
            priority: 12,
            chunks: 'all'
          },
          foundation: {
            name: 'chunk-foundation',
            test: /[\\/]src[\\/].*foundation/i,
            priority: 12,
            chunks: 'all'
          },
          // 工具库分离
          tools: {
            name: 'chunk-tools',
            test: /[\\/]node_modules[\\/](swiper|crypto-js|ua-parser-js)/,
            priority: 10,
            chunks: 'all'
          },
          // Vue生态系统
          vue: {
            name: 'chunk-vue',
            test: /[\\/]node_modules[\\/](vue|vue-router|vuex|vue-i18n)/,
            priority: 8,
            chunks: 'all'
          },
          // 其他第三方库
          vendor: {
            name: 'chunk-vendors',
            test: /[\\/]node_modules[\\/]/,
            priority: 5,
            chunks: 'all',
            minChunks: 2
          },
          // 公共代码
          common: {
            name: 'chunk-common',
            minChunks: 2,
            chunks: 'all',
            priority: 1,
            enforce: true
          }
        }
      },
      // 生产环境优化
      ...(isProduction && {
        minimize: true,
        usedExports: true, // 启用Tree Shaking
        sideEffects: false
      })
    },
    // 性能提示
    performance: {
      hints: isProduction ? 'warning' : false,
      maxEntrypointSize: 512000, // 512KB
      maxAssetSize: 512000
    }
  },
  
  pages: {},
  
  chainWebpack: config => {
    // 图片优化
    config.module
      .rule('images')
      .test(/\.(png|jpe?g|gif|svg)(\?.*)?$/)
      .use('url-loader')
      .loader('url-loader')
      .options({
        limit: 1024 * 4, // 4KB以下内联
        name: `static/${timeMark}/img/[name].[hash:8].[ext]`,
        // 图片压缩
        ...(isProduction && {
          mozjpeg: { progressive: true, quality: 80 },
          optipng: { enabled: true },
          pngquant: { quality: [0.65, 0.8] },
          webp: { quality: 80 }
        })
      })

    // 字体优化
    config.module
      .rule('fonts')
      .test(/\.(woff2?|eot|ttf|otf)(\?.*)?$/i)
      .use('url-loader')
      .loader('url-loader')
      .options({
        limit: 1024 * 2, // 2KB以下内联
        name: `static/${timeMark}/fonts/[name].[hash:8].[ext]`
      })

    // 预加载优化
    config.plugin('preload').tap(options => {
      options[0] = {
        rel: 'preload',
        include: 'initial',
        fileBlacklist: [/\.map$/, /hot-update\.js$/, /runtime\..*\.js$/]
      }
      return options
    })

    // 预取优化
    config.plugin('prefetch').tap(options => {
      options[0].fileBlacklist = options[0].fileBlacklist || []
      options[0].fileBlacklist.push(/\.map$/, /hot-update\.js$/)
      return options
    })

    // 生产环境CSS优化
    if (isProduction) {
      config.plugin('extract-css').tap(options => {
        options[0].filename = `static/${timeMark}/css/[name].[contenthash:8].css`
        options[0].chunkFilename = `static/${timeMark}/css/[name].[contenthash:8].css`
        return options
      })
    }

    // 开发环境热更新优化
    if (isDev) {
      config.devServer.hot(true)
      config.devServer.compress(true)
    }
  },

  // CSS优化
  css: {
    extract: isProduction ? {
      filename: `static/${timeMark}/css/[name].[contenthash:8].css`,
      chunkFilename: `static/${timeMark}/css/[name].[contenthash:8].css`
    } : false,
    sourceMap: isDev,
    loaderOptions: {
      sass: {
        // 全局样式变量
        additionalData: `@import "@/styles/variables.scss";`
      },
      postcss: {
        plugins: [
          require('autoprefixer'),
          ...(isProduction ? [
            require('cssnano')({
              preset: ['default', {
                discardComments: { removeAll: true },
                normalizeWhitespace: false
              }]
            })
          ] : [])
        ]
      }
    }
  },

  // 开发服务器配置
  devServer: {
    port: 8080,
    hot: true,
    compress: true,
    overlay: {
      warnings: false,
      errors: true
    }
  }
}

updatePageConfig(AllPageConfig)

try {
  const scriptParams = process.env.npm_lifecycle_event
  const [action, env] = scriptParams.split(':') || []

  if (action === 'build') {
    config.outputDir = 'dist_' + env
    if (env === 'online') {
      Object.assign(config, {
        publicPath: process.env.onlinePublicGlobalPath,
        outputDir: 'dist_online',
        assetsDir: `static/${timeMark}`,
        indexPath: 'index.html',
        productionSourceMap: false, // 生产环境关闭source map
        crossorigin: 'anonymous'
      })
    }
  }
} catch (e) {
  console.log(e)
}

// 页面配置函数保持不变
function risePage (curGame, PageInfo) {
  const isOnline = process.env.VUE_APP_PROD_ENV === 'ONLINE'
  const isBuild = process.env.NODE_ENV === 'production'
  let resBathPath = '/'
  
  if (isBuild && PageInfo.linkMainDomain) resBathPath = `/${PageInfo.linkPathName}/`
  if (isOnline) {
    resBathPath = curGame.includes('cn') ? process.env.onlinePublicCnPath : process.env.onlinePublicGlobalPath
  }

  if (curGame === 'pc') {
    if (isBuild && !isOnline) resBathPath = '/res/'
    return {
      template: 'public/pc.html',
      entry: '/src/main.js',
      filename: isDev ? 'index.html' : `index_${curGame}.html`,
      title: PageInfo.title,
      themeColor: PageInfo.themeColor || 'white',
      resBathPath,
      nameKey: JSON.stringify(PageInfo.nameKey)
    }
  }

  if (curGame === 'sdk') {
    if (isBuild && !isOnline) resBathPath = '/res/'
    return {
      template: 'public/sdk.html',
      entry: '/src/main.js',
      filename: isDev ? 'index.html' : `index_${curGame}.html`,
      title: PageInfo.title,
      themeColor: PageInfo.themeColor || 'white',
      resBathPath,
      nameKey: JSON.stringify(PageInfo.nameKey)
    }
  }
  
  if (curGame === 'sdk2') {
    if (isBuild && !isOnline) resBathPath = '/res/'
    return {
      template: 'public/sdk2.html',
      entry: '/src/main.js',
      filename: isDev ? 'index.html' : `index_${curGame}.html`,
      title: PageInfo.title,
      themeColor: PageInfo.themeColor || 'white',
      nameKey: JSON.stringify(PageInfo.nameKey)
    }
  }

  return {
    template: 'public/index.html',
    entry: '/src/main.js',
    filename: isDev ? 'index.html' : `index_${curGame}.html`,
    game: curGame,
    title: PageInfo.title,
    description: PageInfo.description,
    icon: PageInfo.icon,
    themeColor: PageInfo.themeColor || 'white',
    linkMainDomain: PageInfo.linkMainDomain || false,
    linkPathName: PageInfo.linkPathName,
    manifestPath: (isOnline && PageInfo.linkMainDomain ? `https://store.funplus.com/subservice/${curGame}/` : 'config/') + `${curGame}.manifest.webmanifest`,
    resBathPath
  }
}

function updatePageConfig (allConfig) {
  if (isDev) {
    const devGame = GAME
    if (!devGame) {
      console.log('pageConfig[\'devGame\'] 不能为空！')
      process.exit(0)
    }
    config.pages.index = risePage(devGame, allConfig[devGame])
  } else {
    config.publicPath = '/res/'
    for (const [curGame, curConfig] of Object.entries(allConfig)) {
      config.pages[curGame] = risePage(curGame, curConfig)
    }
  }
  console.log(config.pages)
}

function getDateMark () {
  const now = new Date()
  const year = now.getFullYear()
  const month = now.getMonth() + 1

  let targetMonth
  if (month <= 3) {
    targetMonth = 1
  } else if (month <= 6) {
    targetMonth = 4
  } else if (month <= 9) {
    targetMonth = 7
  } else {
    targetMonth = 10
  }

  const targetDate = new Date(year, targetMonth - 1, 1)
  return Math.floor(targetDate.getTime() / 1000)
}

module.exports = config
