var fs = require('fs')
var xlsx = require('node-xlsx')
var path = require('path')

const getParams = (argv) => {
  var t = new RegExp(/^--/g)
  const params = {}
  argv.filter(item => {
    return item.match(t)
  }).forEach(item => {
    const r = item.replace('--', '').split('=')
    params[r[0]] = r[1]
  })
  return params
}

var params = getParams(process.argv)
var data = xlsx.parse('./PC微端多语言文档.xlsx')
var table = data.filter(item => item.name === 'KOA-VIP&SS-充值站')[0].data
// 遍历lang key
for (let j = 2; j < table[0].length; j++) {
  const langCode = table[0][j]
  console.log(langCode)
  let obj = ''
  // 遍历每一行内容
  for (let i = 1; i < table.length; i++) {
    // 判断key是否为空
    if (table[i][0] && table[i][0] !== '') {
      if (table[i][j]) {
        obj += ',"' + table[i][0].replace('"', '') + '":"' + table[i][j].replace(/["]+/gm, '').replace(/\r|\n/g, '<br/>') + '"'
      }
    }
  }
  const result = '{' + obj.substr(1) + '}'
  fs.writeFile(path.resolve('../langJson' + (params.game ? `/${params.game}` : '') + '/' + langCode + '.json'), result, function (err) {
    if (err) {
      console.log('Error! ' + err)
      return
    }
    console.log(langCode + '.js写入完成')
  })
}
