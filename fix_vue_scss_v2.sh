#!/bin/bash

# 修复所有Vue文件中的导入路径
find src -name "*.vue" -exec grep -l "@import.*utils" {} \; | while read file; do
    echo "Processing $file"

    # 修复各种可能的导入路径问题
    sed -i '' 's/@import "~@\/utils\/utils\.scss";/@import "~@\/utils\/utils.scss";/g' "$file"
    sed -i '' 's/@import "~@\/utils\/utils";/@import "~@\/utils\/utils.scss";/g' "$file"
    sed -i '' 's/@import "~@\/utils\/scss";/@import "~@\/utils\/utils.scss";/g' "$file"
    sed -i '' 's/@import "~@\/utils\/utils\/scss";/@import "~@\/utils\/utils.scss";/g' "$file"
done

echo "Done!"
