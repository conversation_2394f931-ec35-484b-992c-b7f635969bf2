server {
  listen 80;
  server_name st-store-hub.funplus.com;
  expires -1;
  add_header Cache-Control "no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0";
  add_header Pragma "no-cache";
  root /data/nginx/dist_online;

  location ^~ /stormshot/sdk {
    index index_sdk.html index_sdk.html;
    try_files $uri $uri/ /index_sdk.html;
  }
  
  location ^~ /stormshot/checkout {
    index index_sdk2.html index_sdk2.html;
    try_files $uri $uri/ /index_sdk2.html;
  }
}
