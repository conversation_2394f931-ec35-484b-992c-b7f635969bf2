server {
  listen 80;
  server_name tilessurvive-store-internal.funplus.com tilessurvive-store.funplus.com;
  expires -1;
  add_header Cache-Control "no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0";
  add_header Pragma "no-cache";
  root /data/nginx/dist_online;

    location ^~ /tilessurvive {
       index index_ssd.html index_ssd.html;
       try_files $uri $uri/ /index_ssd.html;
  }
}

server {
  listen 80;
  server_name tilessurvive-store-sdk-internal.funplus.com tilessurvive-store-sdk.funplus.com;
  expires -1;
  add_header Cache-Control "no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0";
  add_header Pragma "no-cache";
  root /data/nginx/dist_online;

  location ^~ /tilessurvive/checkout {
      index index_sdk2.html index_sdk2.html;
      try_files $uri $uri/ /index_sdk2.html;
    }
}
