server {
  listen 80;
  server_name dcdl-store.funplus.com dcdl-store-internal.funplus.com;
  expires -1;
  add_header Cache-Control "no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0";
  add_header Pragma "no-cache";
  root /data/nginx/dist_online;

  location ^~ /dcdarklegion/sdk {
    index index_sdk.html index_sdk.html;
    try_files $uri $uri/ /index_sdk.html;
  }

  location ^~ /dcdarklegion {
    index index_dc.html index_dc.html;
    try_files $uri $uri/ /index_dc.html;
  }
  location ^~ /dcdarklegion-pc {
    index index_dc.html index_dc.html;
    try_files $uri $uri/ /index_dc.html;
  }

  location ^~ /dcdarklegion/checkout {
    index index_sdk2.html index_sdk2.html;
    try_files $uri $uri/ /index_sdk2.html;
  }
}

server {
    listen       88;
    server_name  paykoa.com test-koa-store.paykoa.com;
    gzip_types   text/plain application/x-javascript application/javascript text/javascript text/css application/xml image/jpeg image/gif image/png application/json;

    location / {
        expires -1;
        add_header Cache-Control "no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0";
        add_header Pragma "no-cache";
        root  /data/nginx/dist_online;
        index  index.php index_koa.html index.htm;
        try_files $uri $uri/ /index_koa.html;
    }

    location ^~ /kingofavalon/checkout {
          index index_sdk2.html index_sdk2.html;
          try_files $uri $uri/ /index_sdk2.html;
        }

}

server {
    listen       88;
    server_name payromgame.com koa-store.payromgame.com;
    gzip_types   text/plain application/x-javascript application/javascript text/javascript text/css application/xml image/jpeg image/gif image/png application/json;

    location / {
        expires -1;
        add_header Cache-Control "no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0";
        add_header Pragma "no-cache";
        root  /data/nginx/dist_online;
        index  index.php index_rom.html index.htm;
        try_files $uri $uri/ /index_rom.html;
    }

    location ~ /vip {
        resolver ******* valid=60s;
        resolver_timeout 3s;
        set $webapi_url "vip.funplus.com";
        proxy_pass https://$webapi_url;
    }

    location ^~ /realmofmystery/sdk {
      index index_sdk.html index_sdk.html;
      try_files $uri $uri/ /index_sdk.html;
    }
}

server {
    listen       88;
    server_name payaof.com test-koa-store.payaof.com;
    gzip_types   text/plain application/x-javascript application/javascript text/javascript text/css application/xml image/jpeg image/gif image/png application/json;

    location / {
        expires -1;
        add_header Cache-Control "no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0";
        add_header Pragma "no-cache";
        root  /data/nginx/dist_online;
        index  index.php index_aof.html index.htm;
        try_files $uri $uri/ /index_aof.html;
    }

    location ~ /vip {
        resolver ******* valid=60s;
        resolver_timeout 3s;
        set $webapi_url "vip.funplus.com";
        proxy_pass https://$webapi_url;
    }
}

