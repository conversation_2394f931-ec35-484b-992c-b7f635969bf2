server {
  listen 80;
  server_name foundation-store-internal.funplus.com foundation-store.funplus.com;
  expires -1;
  add_header Cache-Control "no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0";
  add_header Pragma "no-cache";
  root /data/nginx/dist_online;

  location ^~ /foundation/sdk {
      index index_sdk.html index_sdk.html;
      try_files $uri $uri/ /index_sdk.html;
    }

    location ^~ /foundation {
       index index_foundation.html index_foundation.html;
       try_files $uri $uri/ /index_foundation.html;
  }
}
