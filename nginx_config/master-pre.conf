server {
    listen       88;
    server_name  store-pre.paykoa.com;
    gzip_types   text/plain application/x-javascript application/javascript text/javascript text/css application/xml image/jpeg image/gif image/png application/json;

    location / {
        expires -1;
        add_header Cache-Control "no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0";
        add_header Pragma "no-cache";
        root  /data/nginx/dist_online;
        index  index.php index_koa.html index.htm;
        try_files $uri $uri/ /index.html;
    }

}
server {
    listen       88;
    server_name  store-pre.payromgame.com;
    gzip_types   text/plain application/x-javascript application/javascript text/javascript text/css application/xml image/jpeg image/gif image/png application/json;

    location / {
        expires -1;
        add_header Cache-Control "no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0";
        add_header Pragma "no-cache";
        root  /data/nginx/dist_online;
        index  index.php index_rom.html index.htm;
        try_files $uri $uri/ /index_rom.html;
    }

    location ~ /vip {
        resolver ******* valid=60s;
        resolver_timeout 3s;
        set $webapi_url "vip.funplus.com";
        proxy_pass https://$webapi_url;
    }

}
server {
    listen       88;
    server_name  store-pre.payaof.com;
    gzip_types   text/plain application/x-javascript application/javascript text/javascript text/css application/xml image/jpeg image/gif image/png application/json;

    location / {
        expires -1;
        add_header Cache-Control "no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0";
        add_header Pragma "no-cache";
        root  /data/nginx/dist_online;
        index  index.php index_aof.html index.htm;
        try_files $uri $uri/ /index_aof.html;
    }

    location ~ /vip {
        resolver ******* valid=60s;
        resolver_timeout 3s;
        set $webapi_url "vip.funplus.com";
        proxy_pass https://$webapi_url;
    }
}
