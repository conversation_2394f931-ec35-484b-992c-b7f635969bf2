<!DOCTYPE html>
<html lang="">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link rel="icon" href="https://kg-web-cdn.akamaized.net/prod/web-pay-unique_koa/dist_online/favicon.ico">
    <title><%= htmlWebpackPlugin.options.title %></title>
    <meta name="theme-color" content="<%=htmlWebpackPlugin.options.themeColor%>">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
    <meta content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=no" id="viewport" name="viewport">
<!--    <base href="<%=htmlWebpackPlugin.options.resBathPath%>">-->

    <script src="https://kg-web-cdn.akamaized.net/prod/versionAdmin/source/js/fake.min.20240805.js"></script>
    <script>
        var pathname = location.pathname.split('/')[1]
        window.__GAMENAME = pathname
        window.fixStorage && window.fixStorage(window)

        var nameKey = JSON.parse('<%= htmlWebpackPlugin.options.nameKey %>')
        var fullName = pathname.replace('-pc', '')
        var sortName = nameKey[fullName]

        window.__GAMENAME = sortName
        sessionStorage.setItem('__GAMENAME', sortName)
        window.__ROUTERPATH= '/' + pathname + '/'
        window.__isPCSDK = true
    </script>
  </head>
  <body>
    <noscript>
      <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>
    <div id="app"></div>
    <!-- built files will be auto injected -->
    <script>
      (function () {
          window._smReadyFuncs = [];
          window.SMSdk = {
            onBoxDataReady: function (boxData) {},
            ready: function (fn) {
              fn && _smReadyFuncs.push(fn);
            }
          };
          window._smConf = {
            organization: 'xNxMh079HgFDeRkJE1qN',
            appId: 'web_sdk',
            publicKey: 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtbekoieD6v30htpLAEPVp3w2nr9DRC8ElQu+qQfT+MPIU3K3Qc1FiF3gL0GDIKoTOXGuCXz/pVe7snZqcBh/8fsxpsQQMv/pSpzd6rLOthwwbvxLbWW06CU6SGXHBUDCYY+V/Y4GtsgwzhAf3Z0VZ/t0DXX8Yh/SaPXJuKJFn0wIDAQAB',
            apiHost: 'devproxy-web.kingsgroupgames.com'
            // apiHost: 'fp-devproxy-dev.nenglianghe.cn'
          };
      })();
    </script>
    <script src="https://static.portal101.cn/dist/web/v3.0.0/fp.min.js" type="text/javascript"></script>
  </body>
</html>
