<!DOCTYPE html>
<html lang="">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link rel="icon" href="https://kg-web-cdn.akamaized.net/prod/web-pay-unique_koa/dist_online/favicon.ico">
    <title><%= htmlWebpackPlugin.options.title %></title>
    <meta name="theme-color" content="<%=htmlWebpackPlugin.options.themeColor%>">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
    <meta content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=no" id="viewport" name="viewport">
    <!--    <base href="<%=htmlWebpackPlugin.options.resBathPath%>">-->
    <script src="https://cdn.jsdelivr.net/npm/gsap@3.12.5/dist/gsap.min.js" defer></script>

    <script src="https://kg-web-cdn.akamaized.net/prod/versionAdmin/source/js/fake.min.20240805.js"></script>
    <script>
      var pathname = location.pathname.split('/')[1]
      window.__GAMENAME = pathname + 'Checkout'
      window.fixStorage && window.fixStorage(window)

      var nameKey = JSON.parse('<%= htmlWebpackPlugin.options.nameKey %>')
      var sortName = nameKey[pathname]

      window.__GAMENAME = sortName
      sessionStorage.setItem('__GAMENAME', sortName)
      window.__ROUTERPATH= '/' + pathname + '/checkout/'
      window.__IS_CHECKOUT_SDK = window.__IS_CHECKOUT_SDK_V2 = true
      closeClientLoading()

      function closeClientLoading () {
        function getMobileOperatingSystem () {
          var userAgent = navigator.userAgent || navigator.vendor || window.opera
          // Windows Phone must come first because its UA also contains "Android"
          if (/windows phone/i.test(userAgent)) {
            return 'Windows Phone'
          }
          if (/android/i.test(userAgent)) {
            return 'android'
          }
          // iOS detection from: http://stackoverflow.com/a/9039885/177710
          if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) {
            return 'ios'
          }
          return 'unknown'
        }
        const ms = getMobileOperatingSystem()
        if (ms === 'android') {
          if (window.JsHandler && window.JsHandler.callSdk) {
            window.JsHandler.callSdk('close_loading')
          }
        }
        if (ms === 'ios') {
          if (window.webkit && window.webkit.messageHandlers) {
            // eslint-disable-next-line no-unused-expressions
            window.webkit.messageHandlers.callSdk?.postMessage('close_loading')
          }
        }
      }
    </script>
</head>
<body>
<noscript>
    <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
</noscript>
<div id="app"></div>
<!-- built files will be auto injected -->
<script>
  (function () {
    window._smReadyFuncs = [];
    window.SMSdk = {
      onBoxDataReady: function (boxData) {},
      ready: function (fn) {
        fn && _smReadyFuncs.push(fn);
      }
    };
    window._smConf = {
      organization: 'xNxMh079HgFDeRkJE1qN',
      appId: 'web_sdk',
      publicKey: 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtbekoieD6v30htpLAEPVp3w2nr9DRC8ElQu+qQfT+MPIU3K3Qc1FiF3gL0GDIKoTOXGuCXz/pVe7snZqcBh/8fsxpsQQMv/pSpzd6rLOthwwbvxLbWW06CU6SGXHBUDCYY+V/Y4GtsgwzhAf3Z0VZ/t0DXX8Yh/SaPXJuKJFn0wIDAQAB',
      apiHost: 'devproxy-web.kingsgroupgames.com'
      // apiHost: 'fp-devproxy-dev.nenglianghe.cn'
    };
  })();
</script>
<script src="https://static.portal101.cn/dist/web/v3.0.0/fp.min.js" type="text/javascript"></script>
</body>
</html>
