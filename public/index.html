<!DOCTYPE html>
<html lang="">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link rel="icon" href="https://kg-web-cdn.akamaized.net/prod/web-pay-unique_koa/dist_online/favicon.ico">
    <title><%= htmlWebpackPlugin.options.title %></title>
    <meta name="description" content="<%=htmlWebpackPlugin.options.description%>">
    <meta name="theme-color" content="<%=htmlWebpackPlugin.options.themeColor%>">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
    <meta content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=no" id="viewport" name="viewport">
    <script src="https://cdn.jsdelivr.net/npm/gsap@3.12.5/dist/gsap.min.js" defer></script>

    <!-- CDN加载核心库以减小包大小 -->
    <% if (process.env.NODE_ENV === 'production') { %>
    <script src="https://cdn.jsdelivr.net/npm/vue@2.7.14/dist/vue.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/vue-router@3.6.5/dist/vue-router.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/vuex@3.6.2/dist/vuex.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios@1.6.2/dist/axios.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/lodash@4.17.21/lodash.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/moment@2.29.4/min/moment.min.js"></script>

    <!-- 支付SDK按需加载 -->
    <% if (htmlWebpackPlugin.options.game === 'koa' || htmlWebpackPlugin.options.game === 'aof' || htmlWebpackPlugin.options.game === 'rom') { %>
    <script src="https://checkoutshopper-live.adyen.com/checkoutshopper/sdk/5.53.2/adyen.js" integrity="sha384-e3Vc1Fv6JOHEiOLTLs0gvB8cKJ65cXJQSYK9FQiG/5sMRiVJLULzqYp7gg/jZJ0Y" crossorigin="anonymous"></script>
    <link rel="stylesheet" href="https://checkoutshopper-live.adyen.com/checkoutshopper/sdk/5.53.2/adyen.css" integrity="sha384-9EdBqZRrjveMTq9UpAP6CO5br5yMGCxqWKC9zKGKr3pWyNFHOI0L8/+sI7ZGUx5k" crossorigin="anonymous">
    <% } %>

    <% if (htmlWebpackPlugin.options.game === 'dc' || htmlWebpackPlugin.options.game === 'foundation') { %>
    <script src="https://js.stripe.com/v3/"></script>
    <% } %>
    <% } %>

      <!--    <base href="<%=htmlWebpackPlugin.options.resBathPath%>">-->

    <% if (htmlWebpackPlugin.options.linkMainDomain) { %>
      <script src="https://kg-web-cdn.akamaized.net/prod/versionAdmin/source/js/fake.min.20240805.js"></script>
    <% } %>
    <% if (htmlWebpackPlugin.options.linkPathName) { %>
      <script>
        window.__ROUTERPATH = '/<%= htmlWebpackPlugin.options.linkPathName %>/'
      </script>
    <% } %>

    <% if (htmlWebpackPlugin.options.game === 'koaCn') { %>
        <script>
        var open_id = window.localStorage.getItem('fp_wx_openid')
        if (navigator.userAgent.toLowerCase().indexOf('micromessenger') !== -1 && !open_id && window.location.href.indexOf('fp_wx_get_code') === -1) {
            var url = 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx9d99d0a7d7b719b8&redirect_uri=' + encodeURIComponent(window.location.href) + '&response_type=code&scope=snsapi_base&state=fp_wx_get_code#wechat_redirect'
            window.location.href = url
        }
    </script>
    <% } %>
    <script>
        window.__GAMENAME = '<%= htmlWebpackPlugin.options.game %>'
        window.fixStorage && window.fixStorage(window)
        sessionStorage.setItem('__GAMENAME', '<%= htmlWebpackPlugin.options.game %>')

        window.addEventListener('beforeinstallprompt', (e) => {
          // 防止 Chrome 67 及更早版本自动显示安装提示
          e.preventDefault()
          // 稍后再触发此事件
          window.__deferredPrompt = e
        })
        // if ('serviceWorker' in navigator) {
        //   navigator.serviceWorker.register('/service-worker.js', {
        //     scope: '/'
        //   }).then(function (registration) {
        //     console.log('ServiceWorker registration successful with scope: ', registration.scope);
        //   });
        // }
    </script>
    <link rel="manifest" href="<%=htmlWebpackPlugin.options.manifestPath%>" crossorigin="use-credentials">
  </head>
  <body>
    <noscript>
      <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>
    <div id="app"></div>
    <!-- built files will be auto injected -->
    <script>
      (function () {
          window._smReadyFuncs = [];
          window.SMSdk = {
            onBoxDataReady: function (boxData) {},
            ready: function (fn) {
              fn && _smReadyFuncs.push(fn);
            }
          };
          window._smConf = {
            organization: 'xNxMh079HgFDeRkJE1qN',
            appId: 'web_sdk',
            publicKey: 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtbekoieD6v30htpLAEPVp3w2nr9DRC8ElQu+qQfT+MPIU3K3Qc1FiF3gL0GDIKoTOXGuCXz/pVe7snZqcBh/8fsxpsQQMv/pSpzd6rLOthwwbvxLbWW06CU6SGXHBUDCYY+V/Y4GtsgwzhAf3Z0VZ/t0DXX8Yh/SaPXJuKJFn0wIDAQAB',
            apiHost: 'devproxy-web.kingsgroupgames.com'
            // apiHost: 'fp-devproxy-dev.nenglianghe.cn'
          };
      })();
    </script>
    <script src="https://static.portal101.cn/dist/web/v3.0.0/fp.min.js" type="text/javascript"></script>
  </body>
</html>
