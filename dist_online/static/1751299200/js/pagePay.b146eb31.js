(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pagePay"],{"0ef5":function(t,e,s){},"16db":function(t,e){t.exports="data:image/png;base64,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"},"2b95":function(t,e,s){},"30ec":function(t,e,s){},"40ef":function(t,e,s){},"440f":function(t,e,s){"use strict";s("57cb")},"44d8":function(t,e,s){"use strict";s("8e6d")},"57cb":function(t,e,s){},"617a":function(t,e,s){"use strict";s("fb5e")},"76a1":function(t,e,s){"use strict";s.r(e);var i=function(){var t=this,e=t._self._c;return e("div",{class:["shopping-wrapper",t.$gameName,{sdk:t.IS_CHECKOUT_SDK}],attrs:{id:"page-pay-wrapper"}},[t.isPc&&!t.IS_CHECKOUT_SDK?[e("section",{staticClass:"pc-content-wrapper"},[e("add-screen-btn"),e("add-ios-safari-btn"),e("div",{staticClass:"page-title"},[t._v(t._s(t.$vt("pageTitle"))+" "),e("span",[t._v(t._s(t.$t("mobile_available"))+" "),e("i")])]),e("div",{staticClass:"content-center content-center__main"},[e("div",{staticClass:"left-part"},[e("div",{staticClass:"logo"}),e("div",{staticClass:"name"}),e("p",{staticClass:"description"},[e("span",[t._v(t._s(t.$vt("whatIsDiamondTitle")))]),t.$store.state.functionSwitch.showPcDiscountTips?e("span",[t._v(t._s(t.$vt("discount95Tips")))]):t._e()]),e("div",{staticClass:"charge-construction",on:{click:function(e){return t.$root.$emit("showPop","ChargeConstruction")}}},[e("i"),t._v(t._s(t.$t("construction_title"))+" ")])]),e("div",{staticClass:"right-part"},[e("login-module"),t.boon?e("entrance-of-boon"):t._e(),e("coupon-choose"),e("diamond-choose-k-o-a"),e("channel-choose"),t.$store.state.gameinfo.isCn?e("checkout-counter-cn"):e("checkout-counter"),e("div",{staticClass:"shop-btn"},[e("span",{staticClass:"click-btn",class:[{disable:t.requestLoading||t.$store.getters["riskPolicy/forbiddenAccess"]}],on:{click:function(e){return t.judgeRisk()}}},[t._v(t._s(t.$t("shop_now"))+" "),t.vip.isNewUser||!t.isLogin?e("i"):t._e()])]),"DE"===t.$store.state.country&&t.isLogin?e("common-part",[e("private-permission")],1):t._e()],1)])],1),t.$store.state.gameinfo.isCn?e("common-footer-cn"):t._e(),t.$store.state.gameinfo.mainBody?e("CommonFooterPuzala"):e("common-footer")]:t._e(),t.isMobile&&!t.IS_CHECKOUT_SDK?[e("div",{staticClass:"mobile-body-wrapper"},[e("add-screen-btn"),e("add-ios-safari-btn"),e("login-module"),t.boon?e("entrance-of-boon"):t._e(),e("coupon-choose"),e("diamond-choose-k-o-a"),e("channel-choose"),"DE"===t.$store.state.country&&t.isLogin?e("common-part",[e("private-permission")],1):t.showMobilePolicy?e("refund-policy"):t._e(),t.$store.state.gameinfo.isCn?e("common-footer-cn"):t._e()],1),e("checkout-footer",{attrs:{"request-loading":t.requestLoading},on:{purchaseGoods:function(e){return t.judgeRisk()}}})]:t._e(),t.IS_CHECKOUT_SDK?[e("div",{staticClass:"sdk-body-wrapper"},[e("direct-gift-package"),e("coupon-choose"),e("channel-choose"),"DE"===t.$store.state.country&&t.isLogin?e("common-part",[e("private-permission")],1):t._e(),e("checkout-counter-s-d-k",{attrs:{"request-loading":t.requestLoading},on:{purchaseGoods:function(e){return t.judgeRisk()}}}),e("login-module",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}]})],1),e("checkout-footer",{attrs:{"request-loading":t.requestLoading},on:{purchaseGoods:function(e){return t.judgeRisk()}}}),e("common-footer")]:t._e()],2)},o=[],a=function(){var t=this,e=t._self._c;return t.showFooter?e("div",{staticClass:"copyright"},[e("img",{staticStyle:{"vertical-align":"text-bottom","padding-right":"10px"},attrs:{src:s("16db"),alt:"funplus"}}),t._m(0)]):t._e()},n=[function(){var t=this,e=t._self._c;return e("p",[t._v(" © FUNPLUS INTERNATIONAL AG(Bahnhofstrasse 2, 6300 Zug) - ALL RIGHTS RESERVED "),e("a",{staticStyle:{color:"#ddb463"},attrs:{href:"https://funplus.com/privacy-policy/",target:"_blank"}},[t._v("Privacy Policy")]),t._v(" , "),e("a",{staticStyle:{color:"#ddb463"},attrs:{href:"https://funplus.com/terms-conditions/",target:"_blank"}},[t._v("Terms and Conditions")]),t._v(" and "),e("a",{staticStyle:{color:"#ddb463"},attrs:{href:"https://funplus.com/terms-conditions/#section-13",target:"_blank"}},[t._v("Refund Policy")]),t._v(". ")])}],c={name:"CommonFooter",computed:{showFooter(){return!["aof","rom"].includes(this.$gameName)}}},r=c,d=(s("b5b8"),s("2877")),l=Object(d["a"])(r,a,n,!1,null,"ae65575c",null),u=l.exports,p=s("72a3"),h=s("1f1f"),m=s("878c"),_=function(){var t=this,e=t._self._c;return t.expandMode?e("common-part",{class:["expand",t.$gameName],attrs:{id:"checkout-counter-expand"},scopedSlots:t._u([{key:"label",fn:function(){return[e("div",{staticClass:"sub-total"},[t._v(t._s(t.$t("tax-sub-total")))]),t.taxCost?e("div",{staticClass:"tax"},[t._v(t._s(t.$t("tax-txt")))]):t._e(),t.extraCost?e("div",{staticClass:"tax"},[t._v(t._s(t.$t("extra-txt")))]):t._e(),e("div",{staticClass:"total"},[t._v(" "+t._s(t.$t("totalPrice")))]),e("span",{staticClass:"rate",class:{active:t.expandMode},on:{click:function(e){t.expandMode=!t.expandMode}}},[t._v("+ "+t._s(t.$t("tax-txt"))),e("i")])]},proxy:!0}],null,!1,2468513191)},[e("div",{staticClass:"price-wrapper"},[e("span",{staticClass:"now-price",class:{"is-ar-zone":t.isArZone}},[t._v(t._s(t.FinalPriceState.rawNowPrice))]),t.FinalPriceState.rawOriginPrice?e("span",{class:["origin-price",{"is-ar-zone":t.isArZone}]},[t._v(t._s(t.FinalPriceState.rawOriginPrice))]):t._e(),t.FinalPriceState.offCountTips?e("div",{staticClass:"off-count-tips",domProps:{innerHTML:t._s(t.FinalPriceState.offCountTips)}}):t._e()]),t.taxCost?e("div",{staticClass:"tax-wrapper"},[t._v(t._s(t.taxCost)+" "+t._s(t.currencyUnit))]):t._e(),t.extraCost?e("div",{staticClass:"tax-wrapper"},[t._v(t._s(t.extraCost)+" "+t._s(t.currencyUnit))]):t._e(),e("div",{staticClass:"final-price"},[t._v(t._s(t.FinalPriceState.finalNowPrice))])]):e("common-part",{class:["normal",t.$gameName],attrs:{id:"checkout-counter-normal"},scopedSlots:t._u([{key:"label",fn:function(){return[e("div",{staticClass:"total"},[t._v(t._s(t.$t("totalPrice")))]),t.showTaxBtn?e("span",{staticClass:"rate",class:{active:t.expandMode},on:{click:function(e){t.expandMode=!t.expandMode}}},[t._v("+ "+t._s(t.$t("tax-txt"))),e("i")]):t._e()]},proxy:!0}])},[e("div",{staticClass:"total-price",attrs:{id:"total-price"}},[e("span",{staticClass:"now-price",class:{"is-ar-zone":t.isArZone}},[t._v(t._s(t.FinalPriceState.finalNowPrice))]),t.FinalPriceState.finalOriginPrice?e("span",{staticClass:"origin-price",class:{"is-ar-zone":t.isArZone}},[t._v(t._s(t.FinalPriceState.finalOriginPrice))]):t._e(),t.FinalPriceState.offCountTips?e("div",{staticClass:"off-count-tips",domProps:{innerHTML:t._s(t.FinalPriceState.offCountTips)}}):t._e()])])},f=[],v=s("3772"),g=s("2f62"),C={name:"CheckoutCounterTax",components:{CommonPart:v["a"]},data(){return{expandMode:!1}},computed:{...Object(g["c"])(["urlParams","isArZone","currencyUnit","IS_CHECKOUT_SDK"]),...Object(g["c"])("formdata",["chosenChannel","chosenDiamond","chosenCoupon","vip"]),...Object(g["c"])("gameinfo",["defaultDiscount"]),...Object(g["c"])("userinfo",["isLogin"]),...Object(g["b"])("formdata",["FinalPriceState","getRebateCoin","getSDKRebateCoin"]),taxCost(){return this.chosenCoupon.taxation||this.FinalPriceState.taxation||this.chosenDiamond.taxation},extraCost(){return this.chosenCoupon.extra_fee_amount||this.FinalPriceState.extra_fee_amount||this.chosenDiamond.extra_fee_amount},showTaxBtn(){return this.taxCost||this.extraCost}},watch:{showTaxBtn(t){t||(this.expandMode=!1)}}},b={name:"CheckoutCounterTax",mixins:[C]},w=b,D=(s("ffaf"),Object(d["a"])(w,_,f,!1,null,"d3472866",null)),P=D.exports,y=function(){var t=this,e=t._self._c;return e("common-part",{class:t.$store.state.gameinfo.gameCode,staticStyle:{color:"#000"},attrs:{"label-font":"合计"}},[e("div",{staticClass:"total-price",attrs:{id:"total-price"}},[t.chosenCoupon.FE_INDEX?[e("span",{staticClass:"now-price",class:{"is-ar-zone":t.isArZone}},["first_pay"===t.chosenCoupon.feType?[t._v(t._s(t.currencyUnit)+t._s(t._f("formatPrice")(t.chosenCoupon.discount_price)))]:t._e(),"discount_coupon"===t.chosenCoupon.feType?[t._v(t._s(t.currencyUnit)+t._s(t._f("formatPrice")(t.chosenCoupon.discount_price)))]:t._e(),"cash_coupon"===t.chosenCoupon.feType?[t._v(t._s(t.currencyUnit)+t._s(t._f("formatPrice")(t.chosenCoupon.price)))]:t._e(),"rebate_coupon"===t.chosenCoupon.feType?[t._v(t._s(t.currencyUnit)+t._s(t._f("formatPrice")(t.chosenCoupon.price)))]:t._e()],2),"rebate_coupon"!==t.chosenCoupon.feType?e("span",{class:["origin-price",{"is-ar-zone":t.isArZone}]},[t._v(t._s(t.currencyUnit)+t._s(t._f("formatPrice")(t.chosenDiamond.nowPrice||t.chosenDiamond.price)))]):t._e(),e("div",{staticClass:"off-count-tips"},["first_pay"===t.chosenCoupon.feType?[t._v(t._s(`首充 ${t.chosenCoupon.rateWidthOutPercent} 折`))]:t._e(),"discount_coupon"===t.chosenCoupon.feType?[t._v(t._s(t.chosenCoupon.rateWidthOutPercent)+"折 优惠券")]:t._e(),"cash_coupon"===t.chosenCoupon.feType?[e("span",{class:{"is-ar-zone":t.isArZone}},[t._v("减 "+t._s(t.currencyUnit)+t._s(t.chosenCoupon.deduct_price))])]:t._e(),"rebate_coupon"===t.chosenCoupon.feType?[e("span",{class:{"is-ar-zone":t.isArZone}},[t._v("送 "+t._s(t.chosenCoupon.rate)+" "),e("i",{staticClass:"diamond-icon"})])]:t._e()],2)]:e("span",{class:{"is-ar-zone":t.isArZone}},[t._v(t._s(t.currencyUnit)+t._s(t._f("formatPrice")(t.chosenDiamond.nowPriceWidthTax||t.chosenDiamond.price)))])],2)])},S=[],x={name:"CheckoutCounter",components:{CommonPart:v["a"]},data(){return{defaultDiscountInfo:{}}},computed:{...Object(g["c"])(["urlParams","isArZone","currencyUnit"]),...Object(g["c"])("formdata",["chosenChannel","chosenDiamond","chosenCoupon"]),...Object(g["c"])("gameinfo",["defaultDiscount"])},created(){this.$root.$on("setDefaultDiscountInfo",t=>{this.defaultDiscountInfo=t})}},$=x,k=(s("440f"),Object(d["a"])($,y,S,!1,null,"5a809613",null)),O=k.exports,A=s("af82"),T=s("e28d"),I=function(){var t=this,e=t._self._c;return e("div",{class:["add-screen-pc-wrapper",[t.$gameName]]},[e("transition",{attrs:{name:"addScreen"}},[t.deferredPrompt&&t.$store.state.isMobile?e("div",{staticClass:"add-to-main-screen__mobile",on:{click:t.goInstall}},[t._v(t._s(t.$t("add-to-screen")))]):t._e()]),t.deferredPrompt&&t.$store.state.isPc?e("div",{staticClass:"add-to-main-screen__pc"},[t._v(" "+t._s(t.$t("add_screen_des1"))+" "+t._s(t.$t("add_screen_des2"))+" "),e("span",{staticClass:"click-btn",on:{click:t.goInstall}},[t._v(t._s(t.$t("add-to-screen")))])]):t._e()],1)},E=[];function L(){const t=window.matchMedia("(display-mode: standalone)").matches;return document.referrer.startsWith("android-app://")?"twa":navigator.standalone||t?"standalone":"browser"}var N={name:"AddScreenBtn",data(){return{deferredPrompt:window.__deferredPrompt||void 0}},methods:{showBtn(){window.addEventListener("beforeinstallprompt",t=>{t.preventDefault(),this.deferredPrompt=t})},goInstall(){this.$gtag.event("click_chrome_install",{event_label:this.$store.state.isMobile?"mobile":"pc"}),this.deferredPrompt.prompt(),this.deferredPrompt.userChoice.then(t=>{if("accepted"===t.outcome){console.log("User accepted the A2HS prompt");const t=setInterval(()=>{"standalone"===L()&&(clearInterval(t),this.$root.$emit("installSuccessful"))},1e3)}else console.log("User dismissed the A2HS prompt");this.deferredPrompt=void 0})}},created(){const t=(navigator.userAgent||"").toLowerCase();if(t.includes("chrome")&&this.showBtn(),this.$gcbk("switch.enableAnimation",!1)&&this.$store.state.isPc){const t=this.$watch("deferredPrompt",e=>{e&&(this.$nextTick(()=>gsap&&gsap.from(".add-to-main-screen__pc",{height:0,duration:.4,clearProps:"height"})),t())})}}},j=N,F=(s("44d8"),Object(d["a"])(j,I,E,!1,null,"d8c45582",null)),U=F.exports,M=function(){var t=this,e=t._self._c;return e("div",{class:["add-ios-wrapper",t.$gameName],attrs:{id:"add-ios-wrapper"}},[e("transition",{attrs:{name:"addScreen"}},[t.isShowBtn&&t.showTimes<t.defaultShowTimes||!this.hadInstall?e("div",{staticClass:"add-to-main-screen__mobile heart",on:{click:t.showGuide}},[t._v(t._s(t.$t("add-to-screen")))]):t._e()]),e("transition",{attrs:{name:"iosGuide"}},[e("div",{directives:[{name:"show",rawName:"v-show",value:t.isShowGuide,expression:"isShowGuide"}],staticClass:"ios-guide",class:{active:t.isShowGuide},on:{click:function(e){t.isShowGuide=!1}}},[e("div",{staticClass:"guide-wrap",on:{click:function(t){t.stopPropagation()}}},[e("div",{staticClass:"close",on:{click:function(e){t.isShowGuide=!1}}}),e("div",{staticClass:"title"},[t._v(t._s(t.$t("ios-guide-0")))]),e("div",{staticClass:"subtitle"},[t._v(t._s(t.$t("ios-guide-2"))+" "+t._s(t.$t("ios-guide-3")))]),e("div",{staticClass:"logo"}),e("div",{staticClass:"game"},[t._v(t._s(t.gameName)+" TopupCenter")]),e("div",{staticClass:"phone"}),e("i18n",{staticClass:"txt",attrs:{path:"ios-guide-4",tag:"div"}},[e("div",{staticClass:"up",attrs:{slot:0},slot:0})]),e("i18n",{staticClass:"txt",attrs:{path:"ios-guide-5",tag:"div"}},[e("div",{staticClass:"add",attrs:{slot:0},slot:0}),e("template",{slot:1},[t._v(t._s(t.$t("ios-guide-1")))])],2),e("div",{staticClass:"mune"},[e("div",[t._v(t._s(t.$t("ios-guide-1")))])]),e("div",{staticClass:"arr"})],1)])])],1)},W=[],Z=s("fa7d"),z={name:"AddIosSafariBtn",data(){return{isShowBtn:!1,isShowGuide:!1,showTimes:Number(window.localStorage.getItem("_i_g_times")||0),defaultShowTimes:3,hadInstall:!0}},created(){const t=window.navigator.userAgent.toLowerCase(),e=window.navigator.standalone;var s=t.indexOf("applewebkit")>-1&&t.indexOf("mobile")>-1&&t.indexOf("safari")>-1&&-1===t.indexOf("linux")&&-1===t.indexOf("android")&&-1===t.indexOf("chrome")&&-1===t.indexOf("ios")&&-1===t.indexOf("browser");s&&this.$store.state.isMobile&&!e&&(this.showTimes<this.defaultShowTimes&&(this.isShowBtn=!0),window.localStorage.setItem("_i_g_times",this.showTimes+1)),s&&this.$store.state.isMobile&&!e&&this.$root.$on("loginSuccess",()=>this.rejudge()),this.$root.$on("mobileSafariGuide",()=>{this.isShowGuide=!0})},methods:{showGuide(){this.$gtag.event("click_guide",{event_label:"safari"}),this.showTimes=this.defaultShowTimes,window.localStorage.setItem("_i_g_times",this.showTimes),this.isShowGuide=!0},rejudge(){const{projectId:t,pwaOpenAction:e}=this.$gcbk("apiParams.boonAme",{});if(!e)return;const s={p0:"web",p1:t,p2:e};this.$loading.show(),Object(A["c"])(s).then(t=>{if("standalone"===Object(Z["a"])())return!1;const{data:e=[],code:s}=t;if(0===s&&(this.hadInstall=1===e.length,!this.hadInstall)){const t=setInterval(()=>{"standalone"===Object(Z["a"])()&&(clearInterval(t),this.hadInstall=!0)},2e3)}}).finally(()=>this.$loading.hide())}},computed:{gameName(){return this.$gcbk("gameinfo.sortName")||(this.$store.state.gameinfo.game||"").toUpperCase()}}},K=z,R=(s("ee5b"),Object(d["a"])(K,M,W,!1,null,"1df0d6bd",null)),B=R.exports,H=function(){var t=this,e=t._self._c;return e("common-part",{class:["diamond-part-wrapper",t.$gameName],attrs:{id:"diamond-part-wrapper"},scopedSlots:t._u([{key:"label",fn:function(){return[e("div",{staticClass:"label-wrap"},[e("span",{staticClass:"label",on:{click:function(e){return t.$root.$emit("showWhatIsDiamondPop")}}},[t._v(" "+t._s(t.$t("charge_gear"))+" "),e("i",{staticClass:"diamond-icon"})]),t.isMobile?e("div",{staticClass:"charge-construction",on:{click:function(e){return t.$root.$emit("showPop","ChargeConstruction")}}},[e("i"),t._v(t._s(t.$t("construction_title"))+" ")]):t._e()])]},proxy:!0}])},[e("div",{staticClass:"diamond-list-wrapper"},[t._l(t.diamondList,(function(s,i){return e("div",{key:s.product_id,class:["diamond-item",{"diamond-item__active":s.product_id===t.chosenDiamond.product_id},{"sold-out":s.soldOut}],on:{click:function(e){return t.toggleStatus(i)}}},[2===s.type?[e("div",{staticClass:"top"},[e("div",{staticClass:"coin-num"},[t._v(t._s(t.lastDiamondCalc.totalDiamond)+" "),e("i",{staticClass:"diamond-icon"})]),e("transition",{attrs:{name:"bonus"}},[t.showBonus?e("div",{staticClass:"bonus"},[e("div",{staticClass:"bonus-description"},[t._v("+"+t._s(t.vip.diamondBonus*t.lastDiamondCalc.totalDiamond)+" "),e("br"),t._v(" "),e("i")])]):t._e()]),e("div",{staticClass:"diamond-input-wrapper"},[e("span",{staticClass:"basic-num"},[t._v(t._s(t.lastDiamondCalc.coin))]),e("i"),t._v(" x "),e("input",{directives:[{name:"model",rawName:"v-model",value:t.chosenNum,expression:"chosenNum"}],attrs:{type:"number",disabled:""},domProps:{value:t.chosenNum},on:{input:function(e){e.target.composing||(t.chosenNum=e.target.value)}}}),e("div",{staticClass:"tips",on:{click:function(e){e.stopPropagation(),t.showPopTips=!0}}},[t.showPopTips?e("div",{staticClass:"custom-num-range-tips"},[t._v(t._s(t.$vt("customDiamondLimitTips")))]):t._e()])])],1),e("div",{staticClass:"bottom"},[e("div",{class:["now",{"is-ar-zone":t.isArZone}]},[t._v(t._s(t.diamondState(s).priceState.nowPrice))]),t.diamondState(s).priceState.originPrice?e("div",{class:["origin",{"is-ar-zone":t.isArZone}]},[e("del",[t._v(t._s(t.diamondState(s).priceState.originPrice))])]):t._e()])]:[e("div",{staticClass:"top"},[e("div",{staticClass:"coin-num"},[t._v(" "+t._s(s.coin)+" "),e("i",{staticClass:"diamond-icon"})]),e("transition",{attrs:{name:"bonus"}},[t.showBonus?e("div",{staticClass:"bonus"},[e("div",{staticClass:"bonus-description"},[t._v("+"+t._s(t.vip.diamondBonus*s.coin)+" "),e("br"),t._v(" "),e("i")])]):t._e()]),e("div",{class:["image","image_"+i]}),s.soldOut?e("div",{staticClass:"sold-out-mask"}):t._e()],1),e("div",{staticClass:"bottom"},[e("div",{class:["now",{"is-ar-zone":t.isArZone}]},[t._v(t._s(t.diamondState(s).priceState.nowPrice))]),t.diamondState(s).priceState.originPrice?e("div",{class:["origin",{"is-ar-zone":t.isArZone}]},[e("del",[t._v(t._s(t.diamondState(s).priceState.originPrice))])]):t._e()])],t.isKOA&&!t.isLogin&&t.firstPayProducts[s.product_id]?e("div",{class:["bonus","orange"]},[e("p",{staticClass:"discount"},[e("over-size-scale",[t._v("20%")])],1),e("p",{staticClass:"off"},[t._v("OFF")])]):t._e(),t.diamondState(s).bonusState.type?e("div",{staticClass:"common-bonus"},["rebate"===t.diamondState(s).bonusState.type?[e("div",{staticClass:"send"},[e("over-size-scale",[t._v(t._s(t.$t("bonus_tips")))])],1),e("div",{staticClass:"num"},[t._v(t._s(t.diamondState(s).bonusState.coin)),e("i",{staticClass:"diamond-icon"})])]:t._e(),"coupon"===t.diamondState(s).bonusState.type?[e("div",{staticClass:"discount"},[t._v(t._s(t.diamondState(s).bonusState.rate))]),e("div",{staticClass:"off"},[t._v("OFF")])]:t._e()],2):t._e(),s.total_purchase_times||s.purchased_times?e("div",{staticClass:"forbidden-by-num"},[t._v(" "+t._s(s.purchased_times)+"/"+t._s(s.total_purchase_times)+" ")]):t._e()],2)})),t.diamondList.length?t._e():e("div",{staticClass:"empty"},[t._v(t._s(t.$t("nothingHere")))])],2),"TW"===t.$store.state.country&&"ssv"===t.$gameName?e("extra-diamond"):t._e()],1)},Q=[],G=(s("14d9"),s("a573"),s("da93")),J=function(){var t=this,e=t._self._c;return e("div",{staticClass:"extra-diamond-wrapper",on:{click:function(t){t.stopPropagation()}}},[e("div",{staticClass:"select-wrapper"},[e("div",{class:["label-wrapper",{"label-wrapper-active":t.chosenSpecialDiamond.product_id}],on:{click:function(e){t.showList=!t.showList}}},[t._m(0),e("div",{staticClass:"right-part"},[t.chosenSpecialDiamond.product_id?e("div",{staticClass:"chosen-diamond"},[e("span",{staticClass:"products"},[t._v(t._s(t.chosenSpecialDiamond.coin)+" "),e("i")]),e("span",[t._v(t._s(t.chosenSpecialDiamond.price)+" "+t._s(t.chosenSpecialDiamond.currency_symbol))])]):e("div",{staticClass:"more"},[t._v(t._s(t.$t("load_more")))]),e("i",{class:[{"toggle-active":t.showList}]})])]),e("transition",{attrs:{name:"option"}},[t.showList?e("div",{staticClass:"option-list"},t._l(t.extraList,(function(s,i){return e("div",{key:i,class:["option-item",{"option-item-active":s.product_id===t.chosenSpecialDiamond.product_id}],on:{click:function(e){return t.toggleStatus(i)}}},[e("span",{staticClass:"products"},[t._v(t._s(s.coin)+" "),e("i")]),e("span",[t._v(t._s(s.price)+" "+t._s(s.currency_symbol))])])})),0):t._e()])],1)])},X=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"my-card-logo"},[e("img",{attrs:{src:s("a59f"),alt:""}})])}],q={name:"extraDiamond",data(){return{showList:!1,extraList:[]}},computed:{...Object(g["c"])("formdata",["chosenDiamond"]),chosenSpecialDiamond(){return this.$store.getters["formdata/TWMyCard"]?this.chosenDiamond:{}}},methods:{toggleStatus(t){this.$store.commit("formdata/setChosenDiamond",this.extraList[t]),this.showList=!1}},created(){this.$root.$on("updateSpecialDiamond",t=>{this.extraList=t}),this.$root.$on("BodyClick",()=>{this.showList=!1})}},Y=q,V=(s("cf21"),Object(d["a"])(Y,J,X,!1,null,"5c91cc60",null)),tt=V.exports;const et=window.$gcbk("ids.minCustomDiamondNum",11);var st={name:"DiamondChooseKOA",components:{ExtraDiamond:tt,CommonPart:v["a"],OverSizeScale:G["a"]},data(){return{diamondList:[],canICustom:!1,lastDiamondItem:{},chosenNum:et,showPopTips:!1}},computed:{...Object(g["c"])("formdata",["chosenDiamond","chosenCoupon","vip","isInit","isFirstPayUsed","chosenCoupon","chosenCouponOther","firstPayProducts"]),...Object(g["c"])("gameinfo",["defaultDiscount","isKOA"]),...Object(g["c"])("userinfo",["isLogin"]),...Object(g["c"])(["isPc","isMobile"]),...Object(g["c"])(["isArZone"]),...Object(g["b"])("formdata",["takeEffectDefaultDiscount","takeEffectDefaultRebate","FinalPriceState","isDiamondOwn95Off","getRebateCoin","isDiamondOwnRebate"]),...Object(g["c"])("functionSwitch",["smallDiamondDoubleDiscount"]),...Object(g["c"])("vb",["isDiscountUsed"]),showBonus(){return this.isInit&&this.isFirstPayUsed&&!this.chosenCoupon.FE_INDEX&&this.vip.isInit},lastDiamondCalc(){const{level_currency_price:t,currency:e,coin:s,tax_rate:i}=this.lastDiamondItem,o=Object.assign({},this.lastDiamondItem,{totalDiamond:s*this.chosenNum,chosenNum:+this.chosenNum,defaultPrice:Object(Z["p"])(et*t,e),nowPrice:Object(Z["p"])(this.chosenNum*t,e),nowPriceWidthTax:Object(Z["p"])(t*this.chosenNum*(i||1),e)});return o.taxation=Object(Z["p"])(o.nowPriceWidthTax-o.nowPrice,e),o},diamondState(){const t=this.chosenDiamond,e=this.chosenCoupon,s=(s,i,o)=>{["first_pay","discount_coupon","fixed_discount_coupon"].includes(e.feType)?(s.type="coupon",s.rate=e.rate,i.originPrice=o.level_currency_price,i.nowPrice=o.no_tax_price):"cash_coupon"===e.feType?(s.type="coupon",s.rate=`${e.deduct_price} ${t.currency_symbol}`,i.originPrice=o.level_currency_price,i.nowPrice=o.no_tax_price):["rebate_coupon","fixed_rebate","fixed_dynamic_rebate","first_pay_rebate"].includes(e.feType||o.feType)&&(s.type="rebate",s.coin=o.rate||e.rate,i.nowPrice=o.level_currency_price)};return i=>{const o={type:"",rate:"",coin:0},a={nowPrice:0,originPrice:0};if(this.firstPayProducts[i.product_id]){const t=this.firstPayProducts[i.product_id];s(o,a,t)}else if(i.product_id===t.product_id&&e.productId===i.product_id)s(o,a,e);else if(this.chosenCouponOther[i.product_id]){const t=this.chosenCouponOther[i.product_id];s(o,a,t)}else if(this.isDiamondOwn95Off(i))o.type="coupon",o.rate=this.$store.state.formdata.defaultDiscountInfo.rateWidthOutPercent+"%",a.originPrice=this.isDiamondOwn95Off(i).level_currency_price,a.nowPrice=this.isDiamondOwn95Off(i).no_tax_price;else if(this.isDiamondOwnRebate(i)){if(o.type="rebate",o.coin=this.getRebateCoin(i),"dc"===this.$gameName){const t=this.$store.state.formdata.defaultRebateDynamicInfoAll[i.product_id].level_coin,e=this.getRebateCoin(i)/t;o.coin=Math.floor(100*e)+"%"}a.nowPrice=this.isDiamondOwnRebate(i).no_tax_price}else a.nowPrice=i.level_currency_price,2===i.type&&(a.nowPrice=this.lastDiamondCalc.nowPrice);const n=this.$gcbk("ids.minimumDiamondId","");if(this.smallDiamondDoubleDiscount&&i.product_id===n){const t=()=>"first_pay"!==this.chosenCoupon.feType&&(this.chosenDiamond.product_id===n&&!this.chosenCoupon.FE_INDEX||this.chosenDiamond.product_id!==n);!this.isDiscountUsed&&t()&&(o.coin=i.coin,o.type="rebate")}return a.nowPrice&&(a.nowPrice+=" "+i.currency_symbol),a.originPrice&&(a.originPrice+=" "+i.currency_symbol),this.isKOA&&(o.type=void 0),{bonusState:o,priceState:a}}}},methods:{loadDiamondList(){this.$loading.show(),Object(A["n"])({store_from:"storeFromWeb"}).then(t=>{const{data:e,code:s}=t;if(0===s){this.diamondList=e.sort((t,e)=>t.coin-e.coin),this.diamondList=this.diamondList.map((t,e)=>{const{level_currency_price:s,currency:i,tax_rate:o}=t;return t.nowPriceWidthTax=Object(Z["p"])(s*(o||1),i),t.index=e,t.soldOut=(t.total_purchase_times||t.purchased_times)&&t.total_purchase_times===t.purchased_times,t}),this.initCustomParams(this.diamondList);for(const[t,s]of Object.entries(e))if(s.product_id===this.$route.query.pd)return this.$store.commit("formdata/setChosenDiamond",e[t]);if(e&&e.length){const t=this.diamondList.findIndex(t=>!t.soldOut);this.$store.commit("formdata/setChosenDiamond",e[t||0])}"ssv2"===this.$gameName&&this.initFixCouponByDiamond4(this.diamondList[4])}else this.$toast.err(this.$t("fetchChannelError"))}).finally(()=>this.$loading.hide())},toggleStatus(t){if(this.canICustom&&t===this.diamondList.length-1){if(this.lastDiamondCalc.totalDiamond===this.chosenDiamond.totalDiamond)return null;this.$root.$emit("showPop","CustomDiamond",{diamond:this.lastDiamondCalc,cb:this.toggleCustom.bind(this)})}else{const e=this.diamondList[t];if(e.soldOut)return this.$toast.err(this.$t("product-sold-out"));if(this.diamondList[t].product_id===this.chosenDiamond.product_id)return null;this.$store.commit("formdata/setChosenDiamond",this.diamondList[t])}this.$store.commit("formdata/resetChannel")},initCustomParams(t=[]){let e=-1;for(const[s,i]of Object.entries(t))if(2===i.type){e=s;break}if(-1!==e){const s=t.splice(e,1)[0];t.push(s),this.lastDiamondItem=s,this.canICustom=!0}},toggleCustom(t){t&&(this.chosenNum=t),this.$store.commit("formdata/setChosenDiamond",this.lastDiamondCalc)},initSmallDiamond(){const{p1:t,p2:e}=this.$gcbk("apiParams.smallDiamondDiscount",{}),s={p0:"web",p1:t,p2:e};Object(A["h"])(s).then(t=>{const{code:e,data:s}=t;0===e&&this.$store.commit("vb/setIsDiscountUsed",s.is_received)}),this.judgeFirstDoubleDiscountStatus()},judgeFirstDoubleDiscountStatus(){const t={p0:"web",p1:10,p2:1144};t.p2=1144,Object(A["h"])(t).then(t=>{const{code:e}=t;0===e&&setTimeout(()=>{this.$root.$emit("reloadActivity")},400)})},smallDiamondEvent(){this.$root.$on("couponChosen",()=>{if(this.isDiscountUsed)return null;100===this.chosenDiamond.coin&&this.$toast.err(this.$t("bonus_coupon_mutually_exclusive"))}),this.$root.$on("activityInitEnd",()=>{if(this.isDiscountUsed)return null;const t=this.chosenDiamond,e=this.chosenCoupon;100===t.coin&&["discount_coupon","cash_coupon"].includes(e.feType)&&(console.log("reset coupon!"),this.$store.commit("formdata/resetCouponInfo"))})},initFixCouponByDiamond4(t){const e=t=>(100*(1-t)).toFixed(0),s={};s.price=t.price,s.product_id=t.product_id,this.$loading.show(),Object(A["g"])(s).then(t=>{const{code:i,data:o}=t;if(0===i){let t=o.fixed_discount||[];t=t.map((t,s)=>({...t,feType:"fixed_discount_coupon",FE_INDEX:"fixed_discount_coupon_"+s,rateWidthOutPercent:e(t.discount)})),t.length&&this.$store.commit("formdata/setFixedCouponByProduct4",t[0]);let i=o.fixed_rebate||[];i=i.map((t,i)=>({...t,feType:"fixed_rebate",rateWidthOutPercent:e(t.discount),rate:e(t.discount)+"%",FE_INDEX:"fixed_rebate_"+i,productId:s.product_id})),i.length&&this.$store.commit("formdata/setFixedRebateByProduct4",i[0])}}).finally(()=>this.$loading.hide())}},created(){this.loadDiamondList(),this.smallDiamondDoubleDiscount&&this.smallDiamondEvent(),this.$root.$on("loginEnd",t=>{1===t&&(this.loadDiamondList(),this.smallDiamondDoubleDiscount&&this.initSmallDiamond())}),this.$root.$on("BodyClick",()=>{this.showPopTips=!1})}},it=st,ot=(s("a2bf"),Object(d["a"])(it,H,Q,!1,null,"83f0c5b0",null)),at=ot.exports,nt=function(){var t=this,e=t._self._c;return e("common-part",{staticClass:"entrance-part"},[e("div",{staticClass:"entrance-wrapper",on:{click:t.go}},[t.$store.state.formdata.showDoubleExperience?e("div",{staticClass:"toggle-coupon-act-level-up"}):e("div",{staticClass:"award",class:{rotate:t.rotate}}),t._v(" "+t._s(t.$t("boon-page-title"))+" "),e("i")])])},ct=[];const{projectId:rt,pwaOpenAction:dt,loginAction:lt,getLoginReward:ut,getPwaReward:pt}=window.$gcbk("apiParams.boonAme",{});var ht={name:"EntranceOfBoon",components:{CommonPart:v["a"]},data(){return{clicked:!1,gain:!1}},computed:{...Object(g["c"])("userinfo",["isLogin"]),rotate(){return!this.isLogin||!this.clicked&&!this.gain}},methods:{getStatus(){const t={p0:"web",p1:rt,p2:`${ut},${pt}`};this.$loading.show(),Object(A["c"])(t).then(t=>{const{data:e=[],code:s}=t;0===s&&(2===e.length&&(this.gain=!0),"KOA"===this.$store.state.gameinfo.gameCode&&(this.gain=this.gain&&this.$store.state.formdata.gotDailyReward))}).finally(()=>this.$loading.hide())},go(){this.clicked=!0,this.$root.$emit("showPop","BoonPop")},informServer(t){const e={p0:"web",p1:rt};Object(A["a"])({p2:t,...e}).then(e=>{const{code:s}=e;0===s&&this.getReward(t)})},getReward(t){const e={p2:+t+1},s={p0:"web",p1:rt};this.$loading.show(),Object(A["h"])({...e,...s}).finally(()=>this.$loading.hide())},checkAddBtn(){this.setInterval=setInterval(()=>{const t=document.querySelector(".add-to-main-screen__mobile"),e=document.querySelector(".mobile-body-wrapper");t?(t.style.zoom=.75,e.style.paddingTop="1.3rem"):e.style.paddingTop="0.53333rem"},1e3)}},created(){this.$root.$on("loginEnd",t=>{"standalone"===Z["g"]?this.informServer(dt):(window.$event=this.$root,this.$root.$on("installSuccessful",()=>this.informServer(dt))),this.informServer(lt),this.getStatus()}),this.$store.state.isMobile&&this.checkAddBtn()},beforeDestroy(){this.setInterval&&clearInterval(this.setInterval)}},mt=ht,_t=(s("617a"),Object(d["a"])(mt,nt,ct,!1,null,null,null)),ft=_t.exports,vt=s("8d29"),gt=function(){var t=this,e=t._self._c;return e("common-part",{staticClass:"checkout-counter-sdk-b",class:[t.$gameName],attrs:{labelFont:t.$t("totalPrice"),id:"checkout-counter-sdk-b"}},[t.expandMode?e("div",{staticClass:"expand",class:[t.$gameName]},[e("div",{staticClass:"price-wrapper"},[e("span",{staticClass:"sub-total"},[t._v(t._s(t.$t("tax-sub-total"))+":  ")]),e("span",{staticClass:"now-price",class:{"is-ar-zone":t.isArZone}},[t._v(t._s(t.FinalPriceState.rawNowPrice))]),t.FinalPriceState.rawOriginPrice?e("span",{class:["origin-price",{"is-ar-zone":t.isArZone}]},[t._v(t._s(t.FinalPriceState.rawOriginPrice))]):t._e(),t.FinalPriceState.offCountTips?e("div",{staticClass:"off-count-tips",domProps:{innerHTML:t._s(t.FinalPriceState.offCountTips)}}):t._e()]),t.taxCost?e("div",{staticClass:"tax-wrapper"},[t._v(t._s(t.$t("tax-txt"))+":  "+t._s(t.taxCost)+" "+t._s(t.currencyUnit))]):t._e(),t.extraCost?e("div",{staticClass:"tax-wrapper"},[t._v(t._s(t.$t("extra-txt"))+":  "+t._s(t.extraCost)+" "+t._s(t.currencyUnit))]):t._e(),e("div",{staticClass:"final-price"},[t._v(t._s(t.FinalPriceState.finalNowPrice))]),e("span",{staticClass:"rate",class:{active:t.expandMode},on:{click:function(e){t.expandMode=!t.expandMode}}},[t._v("+ "+t._s(t.$t("tax-txt"))),e("i")])]):e("div",{staticClass:"normal",class:[t.$gameName]},[e("div",{staticClass:"price-wrapper"},[e("span",{staticClass:"now-price",class:{"is-ar-zone":t.isArZone}},[t._v(t._s(t.FinalPriceState.finalNowPrice||"-"))]),t.FinalPriceState.finalOriginPrice?e("span",{staticClass:"origin-price",class:{"is-ar-zone":t.isArZone}},[t._v(t._s(t.FinalPriceState.finalOriginPrice))]):t._e(),t.FinalPriceState.offCountTips?e("div",{staticClass:"off-count-tips",domProps:{innerHTML:t._s(t.FinalPriceState.offCountTips)}}):t._e()]),t.showTaxBtn?e("span",{staticClass:"rate",class:{active:t.expandMode},on:{click:function(e){t.expandMode=!t.expandMode}}},[t._v("+ "+t._s(t.$t("tax-txt"))),e("i")]):t._e()]),e("div",{staticClass:"click-btn",class:[{disable:t.requestLoading||t.$store.getters["riskPolicy/forbiddenAccess"]}],on:{click:function(e){return t.$emit("purchaseGoods")}}},[t._v(" "+t._s(t.$t("shop_now"))+" "),t.vip.isNewUser?e("i"):t._e()])])},Ct=[],bt={name:"CheckoutCounterSDK",props:["requestLoading"],mixins:[C]},wt=bt,Dt=(s("c510"),Object(d["a"])(wt,gt,Ct,!1,null,"40652ba8",null)),Pt=Dt.exports,yt=s("0075"),St={name:"Pay",components:{CheckoutCounterSDK:Pt,CommonPart:v["a"],RefundPolicy:()=>Promise.all([s.e("chunk-tool"),s.e("chunk-functions~pagePay~pageSdkV2"),s.e("chunk-functions")]).then(s.bind(null,"46d5")),EntranceOfBoon:ft,DiamondChooseKOA:at,AddScreenBtn:U,AddIosSafariBtn:B,CheckoutFooter:T["a"],CheckoutCounter:P,CouponChoose:m["a"],ChannelChoose:h["a"],LoginModule:p["a"],CommonFooter:u,CheckoutCounterCn:O,CommonFooterCn:()=>Promise.all([s.e("chunk-tool"),s.e("chunk-functions~pagePay~pageSdkV2"),s.e("chunk-functions")]).then(s.bind(null,"79b9")),CommonFooterPuzala:()=>Promise.all([s.e("chunk-tool"),s.e("chunk-functions~pagePay~pageSdkV2"),s.e("chunk-functions")]).then(s.bind(null,"139e")),PrivatePermission:()=>Promise.all([s.e("chunk-tool"),s.e("chunk-functions~pagePay~pageSdkV2"),s.e("chunk-functions")]).then(s.bind(null,"404e")),DirectGiftPackage:vt["a"]},mixins:[yt["a"]],methods:{showDiamondPop(){this.$root.$on("showWhatIsDiamondPop",()=>{const t=this.$imageLoader("whatsDiamond",[]),e=new Image;e.src=t[0].imageUrl,this.$loading.show(),e.onload=()=>{this.$root.$emit("showPop","WhatIsDiamond"),this.$loading.hide()},e.onerror=()=>{this.$loading.hide()}})},initShowDoubleExperience(){if(this.IS_CHECKOUT_SDK)return null;Object(A["a"])({p0:"web",p1:11,p2:2195,p3:"api"}).then(t=>{const{code:e,data:s}=t;0===e&&this.$store.commit("formdata/switchDoubleExperience",s.double_flage||!1)})}},created(){this.isKOA&&this.initShowDoubleExperience(),this.showDiamondPop()}},xt=St,$t=(s("fe5e"),Object(d["a"])(xt,i,o,!1,null,"093b7ec1",null));e["default"]=$t.exports},"8e6d":function(t,e,s){},a2bf:function(t,e,s){"use strict";s("40ef")},a59f:function(t,e,s){t.exports=s.p+"static/1751299200/img/mycard-logo.18a331bb.png"},b5b8:function(t,e,s){"use strict";s("30ec")},bd30:function(t,e,s){},c510:function(t,e,s){"use strict";s("bd30")},cf21:function(t,e,s){"use strict";s("e98e")},e98e:function(t,e,s){},ee5b:function(t,e,s){"use strict";s("fa0c")},fa0c:function(t,e,s){},fb5e:function(t,e,s){},fe5e:function(t,e,s){"use strict";s("0ef5")},ffaf:function(t,e,s){"use strict";s("2b95")}}]);