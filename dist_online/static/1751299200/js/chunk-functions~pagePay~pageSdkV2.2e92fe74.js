(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-functions~pagePay~pageSdkV2"],{"2b80":function(i,e,o){var a;(function(r,n){"use strict";var t="1.0.40",s="",b="?",w="function",l="undefined",d="object",c="string",u="major",p="model",m="name",h="type",f="vendor",g="version",v="architecture",k="console",x="mobile",y="tablet",_="smarttv",S="wearable",T="embedded",q=500,z="Amazon",N="Apple",A="ASUS",O="BlackBerry",C="Browser",E="Chrome",P="Edge",U="Firefox",j="Google",B="Huawei",M="LG",R="Microsoft",V="Motorola",D="Opera",I="Samsung",G="Sharp",L="Sony",H="Xiaomi",F="Zebra",Z="Facebook",$="Chromium OS",W="Mac OS",X=" Browser",J=function(i,e){var o={};for(var a in i)e[a]&&e[a].length%2===0?o[a]=e[a].concat(i[a]):o[a]=i[a];return o},K=function(i){for(var e={},o=0;o<i.length;o++)e[i[o].toUpperCase()]=i[o];return e},Q=function(i,e){return typeof i===c&&-1!==Y(e).indexOf(Y(i))},Y=function(i){return i.toLowerCase()},ii=function(i){return typeof i===c?i.replace(/[^\d\.]/g,s).split(".")[0]:n},ei=function(i,e){if(typeof i===c)return i=i.replace(/^\s\s*/,s),typeof e===l?i:i.substring(0,q)},oi=function(i,e){var o,a,r,t,s,b,l=0;while(l<e.length&&!s){var c=e[l],u=e[l+1];o=a=0;while(o<c.length&&!s){if(!c[o])break;if(s=c[o++].exec(i),s)for(r=0;r<u.length;r++)b=s[++a],t=u[r],typeof t===d&&t.length>0?2===t.length?typeof t[1]==w?this[t[0]]=t[1].call(this,b):this[t[0]]=t[1]:3===t.length?typeof t[1]!==w||t[1].exec&&t[1].test?this[t[0]]=b?b.replace(t[1],t[2]):n:this[t[0]]=b?t[1].call(this,b,t[2]):n:4===t.length&&(this[t[0]]=b?t[3].call(this,b.replace(t[1],t[2])):n):this[t]=b||n}l+=2}},ai=function(i,e){for(var o in e)if(typeof e[o]===d&&e[o].length>0){for(var a=0;a<e[o].length;a++)if(Q(e[o][a],i))return o===b?n:o}else if(Q(e[o],i))return o===b?n:o;return e.hasOwnProperty("*")?e["*"]:i},ri={"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"},ni={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},ti={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[g,[m,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[g,[m,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[m,g],[/opios[\/ ]+([\w\.]+)/i],[g,[m,D+" Mini"]],[/\bop(?:rg)?x\/([\w\.]+)/i],[g,[m,D+" GX"]],[/\bopr\/([\w\.]+)/i],[g,[m,D]],[/\bb[ai]*d(?:uhd|[ub]*[aekoprswx]{5,6})[\/ ]?([\w\.]+)/i],[g,[m,"Baidu"]],[/\b(?:mxbrowser|mxios|myie2)\/?([-\w\.]*)\b/i],[g,[m,"Maxthon"]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer|sleipnir)[\/ ]?([\w\.]*)/i,/(avant|iemobile|slim(?:browser|boat|jet))[\/ ]?([\d\.]*)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|ovibrowser|bolt|iron|vivaldi|iridium|phantomjs|bowser|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|duckduckgo|klar|helio|(?=comodo_)?dragon)\/([-\w\.]+)/i,/(heytap|ovi|115)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[m,g],[/quark(?:pc)?\/([-\w\.]+)/i],[g,[m,"Quark"]],[/\bddg\/([\w\.]+)/i],[g,[m,"DuckDuckGo"]],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[g,[m,"UC"+C]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i,/micromessenger\/([\w\.]+)/i],[g,[m,"WeChat"]],[/konqueror\/([\w\.]+)/i],[g,[m,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[g,[m,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[g,[m,"Yandex"]],[/slbrowser\/([\w\.]+)/i],[g,[m,"Smart Lenovo "+C]],[/(avast|avg)\/([\w\.]+)/i],[[m,/(.+)/,"$1 Secure "+C],g],[/\bfocus\/([\w\.]+)/i],[g,[m,U+" Focus"]],[/\bopt\/([\w\.]+)/i],[g,[m,D+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[g,[m,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[g,[m,"Dolphin"]],[/coast\/([\w\.]+)/i],[g,[m,D+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[g,[m,"MIUI"+X]],[/fxios\/([\w\.-]+)/i],[g,[m,U]],[/\bqihoobrowser\/?([\w\.]*)/i],[g,[m,"360"]],[/\b(qq)\/([\w\.]+)/i],[[m,/(.+)/,"$1Browser"],g],[/(oculus|sailfish|huawei|vivo|pico)browser\/([\w\.]+)/i],[[m,/(.+)/,"$1"+X],g],[/samsungbrowser\/([\w\.]+)/i],[g,[m,I+" Internet"]],[/metasr[\/ ]?([\d\.]+)/i],[g,[m,"Sogou Explorer"]],[/(sogou)mo\w+\/([\d\.]+)/i],[[m,"Sogou Mobile"],g],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|2345(?=browser|chrome|explorer))\w*[\/ ]?v?([\w\.]+)/i],[m,g],[/(lbbrowser|rekonq)/i,/\[(linkedin)app\]/i],[m],[/ome\/([\w\.]+) \w* ?(iron) saf/i,/ome\/([\w\.]+).+qihu (360)[es]e/i],[g,m],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[m,Z],g],[/(Klarna)\/([\w\.]+)/i,/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(alipay)client\/([\w\.]+)/i,/(twitter)(?:and| f.+e\/([\w\.]+))/i,/(chromium|instagram|snapchat)[\/ ]([-\w\.]+)/i],[m,g],[/\bgsa\/([\w\.]+) .*safari\//i],[g,[m,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[g,[m,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[g,[m,E+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[m,E+" WebView"],g],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[g,[m,"Android "+C]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[m,g],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[g,[m,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[g,m],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[m,[g,ai,ri]],[/(webkit|khtml)\/([\w\.]+)/i],[m,g],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[m,"Netscape"],g],[/(wolvic|librewolf)\/([\w\.]+)/i],[m,g],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[g,[m,U+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i],[m,[g,/_/g,"."]],[/(cobalt)\/([\w\.]+)/i],[m,[g,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[v,"amd64"]],[/(ia32(?=;))/i],[[v,Y]],[/((?:i[346]|x)86)[;\)]/i],[[v,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[v,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[v,"armhf"]],[/windows (ce|mobile); ppc;/i],[[v,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[v,/ower/,s,Y]],[/(sun4\w)[;\)]/i],[[v,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[v,Y]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[p,[f,I],[h,y]],[/\b((?:s[cgp]h|gt|sm)-(?![lr])\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]((?!sm-[lr])[-\w]+)/i,/sec-(sgh\w+)/i],[p,[f,I],[h,x]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[p,[f,N],[h,x]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[p,[f,N],[h,y]],[/(macintosh);/i],[p,[f,N]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[p,[f,G],[h,x]],[/(?:honor)([-\w ]+)[;\)]/i],[p,[f,"Honor"],[h,x]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[p,[f,B],[h,y]],[/(?:huawei)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[p,[f,B],[h,x]],[/\b(poco[\w ]+|m2\d{3}j\d\d[a-z]{2})(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/oid[^\)]+; (m?[12][0-389][01]\w{3,6}[c-y])( bui|; wv|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite|pro)?)(?: bui|\))/i],[[p,/_/g," "],[f,H],[h,x]],[/oid[^\)]+; (2\d{4}(283|rpbf)[cgl])( bui|\))/i,/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[p,/_/g," "],[f,H],[h,y]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[p,[f,"OPPO"],[h,x]],[/\b(opd2\d{3}a?) bui/i],[p,[f,"OPPO"],[h,y]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[p,[f,"Vivo"],[h,x]],[/\b(rmx[1-3]\d{3})(?: bui|;|\))/i],[p,[f,"Realme"],[h,x]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[p,[f,V],[h,x]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[p,[f,V],[h,y]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[p,[f,M],[h,y]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[p,[f,M],[h,x]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[p,[f,"Lenovo"],[h,y]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[p,/_/g," "],[f,"Nokia"],[h,x]],[/(pixel c)\b/i],[p,[f,j],[h,y]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[p,[f,j],[h,x]],[/droid.+; (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[p,[f,L],[h,x]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[p,"Xperia Tablet"],[f,L],[h,y]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[p,[f,"OnePlus"],[h,x]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo(?!bc)\w\w)( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[p,[f,z],[h,y]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[p,/(.+)/g,"Fire Phone $1"],[f,z],[h,x]],[/(playbook);[-\w\),; ]+(rim)/i],[p,f,[h,y]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[p,[f,O],[h,x]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[p,[f,A],[h,y]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[p,[f,A],[h,x]],[/(nexus 9)/i],[p,[f,"HTC"],[h,y]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[f,[p,/_/g," "],[h,x]],[/droid [\w\.]+; ((?:8[14]9[16]|9(?:0(?:48|60|8[01])|1(?:3[27]|66)|2(?:6[69]|9[56])|466))[gqswx])\w*(\)| bui)/i],[p,[f,"TCL"],[h,y]],[/(itel) ((\w+))/i],[[f,Y],p,[h,ai,{tablet:["p10001l","w7001"],"*":"mobile"}]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[p,[f,"Acer"],[h,y]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[p,[f,"Meizu"],[h,x]],[/; ((?:power )?armor(?:[\w ]{0,8}))(?: bui|\))/i],[p,[f,"Ulefone"],[h,x]],[/; (energy ?\w+)(?: bui|\))/i,/; energizer ([\w ]+)(?: bui|\))/i],[p,[f,"Energizer"],[h,x]],[/; cat (b35);/i,/; (b15q?|s22 flip|s48c|s62 pro)(?: bui|\))/i],[p,[f,"Cat"],[h,x]],[/((?:new )?andromax[\w- ]+)(?: bui|\))/i],[p,[f,"Smartfren"],[h,x]],[/droid.+; (a(?:015|06[35]|142p?))/i],[p,[f,"Nothing"],[h,x]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron|infinix|tecno|micromax|advan)[-_ ]?([-\w]*)/i,/; (imo) ((?!tab)[\w ]+?)(?: bui|\))/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[f,p,[h,x]],[/(imo) (tab \w+)/i,/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[f,p,[h,y]],[/(surface duo)/i],[p,[f,R],[h,y]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[p,[f,"Fairphone"],[h,x]],[/(u304aa)/i],[p,[f,"AT&T"],[h,x]],[/\bsie-(\w*)/i],[p,[f,"Siemens"],[h,x]],[/\b(rct\w+) b/i],[p,[f,"RCA"],[h,y]],[/\b(venue[\d ]{2,7}) b/i],[p,[f,"Dell"],[h,y]],[/\b(q(?:mv|ta)\w+) b/i],[p,[f,"Verizon"],[h,y]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[p,[f,"Barnes & Noble"],[h,y]],[/\b(tm\d{3}\w+) b/i],[p,[f,"NuVision"],[h,y]],[/\b(k88) b/i],[p,[f,"ZTE"],[h,y]],[/\b(nx\d{3}j) b/i],[p,[f,"ZTE"],[h,x]],[/\b(gen\d{3}) b.+49h/i],[p,[f,"Swiss"],[h,x]],[/\b(zur\d{3}) b/i],[p,[f,"Swiss"],[h,y]],[/\b((zeki)?tb.*\b) b/i],[p,[f,"Zeki"],[h,y]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[f,"Dragon Touch"],p,[h,y]],[/\b(ns-?\w{0,9}) b/i],[p,[f,"Insignia"],[h,y]],[/\b((nxa|next)-?\w{0,9}) b/i],[p,[f,"NextBook"],[h,y]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[f,"Voice"],p,[h,x]],[/\b(lvtel\-)?(v1[12]) b/i],[[f,"LvTel"],p,[h,x]],[/\b(ph-1) /i],[p,[f,"Essential"],[h,x]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[p,[f,"Envizen"],[h,y]],[/\b(trio[-\w\. ]+) b/i],[p,[f,"MachSpeed"],[h,y]],[/\btu_(1491) b/i],[p,[f,"Rotor"],[h,y]],[/(shield[\w ]+) b/i],[p,[f,"Nvidia"],[h,y]],[/(sprint) (\w+)/i],[f,p,[h,x]],[/(kin\.[onetw]{3})/i],[[p,/\./g," "],[f,R],[h,x]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[p,[f,F],[h,y]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[p,[f,F],[h,x]],[/smart-tv.+(samsung)/i],[f,[h,_]],[/hbbtv.+maple;(\d+)/i],[[p,/^/,"SmartTV"],[f,I],[h,_]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[f,M],[h,_]],[/(apple) ?tv/i],[f,[p,N+" TV"],[h,_]],[/crkey/i],[[p,E+"cast"],[f,j],[h,_]],[/droid.+aft(\w+)( bui|\))/i],[p,[f,z],[h,_]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[p,[f,G],[h,_]],[/(bravia[\w ]+)( bui|\))/i],[p,[f,L],[h,_]],[/(mitv-\w{5}) bui/i],[p,[f,H],[h,_]],[/Hbbtv.*(technisat) (.*);/i],[f,p,[h,_]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[f,ei],[p,ei],[h,_]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[h,_]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[f,p,[h,k]],[/droid.+; (shield) bui/i],[p,[f,"Nvidia"],[h,k]],[/(playstation [345portablevi]+)/i],[p,[f,L],[h,k]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[p,[f,R],[h,k]],[/\b(sm-[lr]\d\d[05][fnuw]?s?)\b/i],[p,[f,I],[h,S]],[/((pebble))app/i],[f,p,[h,S]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[p,[f,N],[h,S]],[/droid.+; (glass) \d/i],[p,[f,j],[h,S]],[/droid.+; (wt63?0{2,3})\)/i],[p,[f,F],[h,S]],[/droid.+; (glass) \d/i],[p,[f,j],[h,S]],[/(pico) (4|neo3(?: link|pro)?)/i],[f,p,[h,S]],[/; (quest( \d| pro)?)/i],[p,[f,Z],[h,S]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[f,[h,T]],[/(aeobc)\b/i],[p,[f,z],[h,T]],[/droid .+?; ([^;]+?)(?: bui|; wv\)|\) applew).+? mobile safari/i],[p,[h,x]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[p,[h,y]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[h,y]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[h,x]],[/(android[-\w\. ]{0,9});.+buil/i],[p,[f,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[g,[m,P+"HTML"]],[/(arkweb)\/([\w\.]+)/i],[m,g],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[g,[m,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna|servo)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[m,g],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[g,m]],os:[[/microsoft (windows) (vista|xp)/i],[m,g],[/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i],[m,[g,ai,ni]],[/windows nt 6\.2; (arm)/i,/windows[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i,/(?:win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[g,ai,ni],[m,"Windows"]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/(?:ios;fbsv\/|iphone.+ios[\/ ])([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[g,/_/g,"."],[m,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[m,W],[g,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[g,m],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish|openharmony)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[m,g],[/\(bb(10);/i],[g,[m,O]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[g,[m,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[g,[m,U+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[g,[m,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[g,[m,"watchOS"]],[/crkey\/([\d\.]+)/i],[g,[m,E+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[m,$],g],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[m,g],[/(sunos) ?([\w\.\d]*)/i],[[m,"Solaris"],g],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[m,g]]},si=function(i,e){if(typeof i===d&&(e=i,i=n),!(this instanceof si))return new si(i,e).getResult();var o=typeof r!==l&&r.navigator?r.navigator:n,a=i||(o&&o.userAgent?o.userAgent:s),t=o&&o.userAgentData?o.userAgentData:n,b=e?J(ti,e):ti,k=o&&o.userAgent==a;return this.getBrowser=function(){var i={};return i[m]=n,i[g]=n,oi.call(i,a,b.browser),i[u]=ii(i[g]),k&&o&&o.brave&&typeof o.brave.isBrave==w&&(i[m]="Brave"),i},this.getCPU=function(){var i={};return i[v]=n,oi.call(i,a,b.cpu),i},this.getDevice=function(){var i={};return i[f]=n,i[p]=n,i[h]=n,oi.call(i,a,b.device),k&&!i[h]&&t&&t.mobile&&(i[h]=x),k&&"Macintosh"==i[p]&&o&&typeof o.standalone!==l&&o.maxTouchPoints&&o.maxTouchPoints>2&&(i[p]="iPad",i[h]=y),i},this.getEngine=function(){var i={};return i[m]=n,i[g]=n,oi.call(i,a,b.engine),i},this.getOS=function(){var i={};return i[m]=n,i[g]=n,oi.call(i,a,b.os),k&&!i[m]&&t&&t.platform&&"Unknown"!=t.platform&&(i[m]=t.platform.replace(/chrome os/i,$).replace(/macos/i,W)),i},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return a},this.setUA=function(i){return a=typeof i===c&&i.length>q?ei(i,q):i,this},this.setUA(a),this};si.VERSION=t,si.BROWSER=K([m,g,u]),si.CPU=K([v]),si.DEVICE=K([p,f,h,k,x,_,y,S,T]),si.ENGINE=si.OS=K([m,g]),typeof e!==l?(typeof i!==l&&i.exports&&(e=i.exports=si),e.UAParser=si):"function"===w&&o("3c35")?(a=function(){return si}.call(e,o,e,i),a===n||(i.exports=a)):typeof r!==l&&(r.UAParser=si);var bi=typeof r!==l&&(r.jQuery||r.Zepto);if(bi&&!bi.ua){var wi=new si;bi.ua=wi.getResult(),bi.ua.get=function(){return wi.getUA()},bi.ua.set=function(i){wi.setUA(i);var e=wi.getResult();for(var o in e)bi.ua[o]=e[o]}}})("object"===typeof window?window:this)},"3c35":function(i,e){(function(e){i.exports=e}).call(this,{})}}]);