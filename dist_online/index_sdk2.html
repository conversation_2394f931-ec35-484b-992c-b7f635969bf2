<!doctype html><html lang=""><head><meta charset="utf-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><link rel="icon" href="https://kg-web-cdn.akamaized.net/prod/web-pay-unique_koa/dist_online/favicon.ico"><title>Topup center</title><meta name="theme-color" content="white"><meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate"/><meta content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no" id="viewport" name="viewport"><script src="https://cdn.jsdelivr.net/npm/gsap@3.12.5/dist/gsap.min.js" defer="defer"></script><script src="https://kg-web-cdn.akamaized.net/prod/versionAdmin/source/js/fake.min.20240805.js"></script><script>var pathname = location.pathname.split('/')[1]
      window.__GAMENAME = pathname + 'Checkout'
      window.fixStorage && window.fixStorage(window)

      var nameKey = JSON.parse('{"stateofsurvival":"ssRP","tilessurvive":"ssdRP","stormshot":"stRP","seaofconquest":"moRP","kingofavalon":"koaRP","dcdarklegion":"dcRP"}')
      var sortName = nameKey[pathname]

      window.__GAMENAME = sortName
      sessionStorage.setItem('__GAMENAME', sortName)
      window.__ROUTERPATH= '/' + pathname + '/checkout/'
      window.__IS_CHECKOUT_SDK = window.__IS_CHECKOUT_SDK_V2 = true
      closeClientLoading()

      function closeClientLoading () {
        function getMobileOperatingSystem () {
          var userAgent = navigator.userAgent || navigator.vendor || window.opera
          // Windows Phone must come first because its UA also contains "Android"
          if (/windows phone/i.test(userAgent)) {
            return 'Windows Phone'
          }
          if (/android/i.test(userAgent)) {
            return 'android'
          }
          // iOS detection from: http://stackoverflow.com/a/9039885/177710
          if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) {
            return 'ios'
          }
          return 'unknown'
        }
        const ms = getMobileOperatingSystem()
        if (ms === 'android') {
          if (window.JsHandler && window.JsHandler.callSdk) {
            window.JsHandler.callSdk('close_loading')
          }
        }
        if (ms === 'ios') {
          if (window.webkit && window.webkit.messageHandlers) {
            // eslint-disable-next-line no-unused-expressions
            window.webkit.messageHandlers.callSdk?.postMessage('close_loading')
          }
        }
      }</script><script defer="defer" src="https://kg-web-cdn.akamaized.net/online/user-platform-web/web-pay-hub/dist_online/static/1751299200/js/chunk-koa-4c120c8f.131a5b89.js" crossorigin="anonymous"></script><script defer="defer" src="https://kg-web-cdn.akamaized.net/online/user-platform-web/web-pay-hub/dist_online/static/1751299200/js/chunk-tools-ef39e8ad.f56f8a66.js" crossorigin="anonymous"></script><script defer="defer" src="https://kg-web-cdn.akamaized.net/online/user-platform-web/web-pay-hub/dist_online/static/1751299200/js/chunk-tools-2d3f5a9f.c497367d.js" crossorigin="anonymous"></script><script defer="defer" src="https://kg-web-cdn.akamaized.net/online/user-platform-web/web-pay-hub/dist_online/static/1751299200/js/chunk-tools-20043ecd.eac4af05.js" crossorigin="anonymous"></script><script defer="defer" src="https://kg-web-cdn.akamaized.net/online/user-platform-web/web-pay-hub/dist_online/static/1751299200/js/chunk-tools-a9a8c684.f193385d.js" crossorigin="anonymous"></script><script defer="defer" src="https://kg-web-cdn.akamaized.net/online/user-platform-web/web-pay-hub/dist_online/static/1751299200/js/chunk-tools-ecbca18b.e682bf8d.js" crossorigin="anonymous"></script><script defer="defer" src="https://kg-web-cdn.akamaized.net/online/user-platform-web/web-pay-hub/dist_online/static/1751299200/js/chunk-vue.f3dc7a69.js" crossorigin="anonymous"></script><script defer="defer" src="https://kg-web-cdn.akamaized.net/online/user-platform-web/web-pay-hub/dist_online/static/1751299200/js/chunk-vendors.0843d29d.js" crossorigin="anonymous"></script><script defer="defer" src="https://kg-web-cdn.akamaized.net/online/user-platform-web/web-pay-hub/dist_online/static/1751299200/js/chunk-common-c3373795.f68b6ec7.js" crossorigin="anonymous"></script><script defer="defer" src="https://kg-web-cdn.akamaized.net/online/user-platform-web/web-pay-hub/dist_online/static/1751299200/js/chunk-common-42f9d7e6.f3c2ff11.js" crossorigin="anonymous"></script><script defer="defer" src="https://kg-web-cdn.akamaized.net/online/user-platform-web/web-pay-hub/dist_online/static/1751299200/js/chunk-common-b35596cd.c4c2f7d8.js" crossorigin="anonymous"></script><script defer="defer" src="https://kg-web-cdn.akamaized.net/online/user-platform-web/web-pay-hub/dist_online/static/1751299200/js/chunk-common-42298f48.dc4e13d9.js" crossorigin="anonymous"></script><script defer="defer" src="https://kg-web-cdn.akamaized.net/online/user-platform-web/web-pay-hub/dist_online/static/1751299200/js/chunk-common-5c551db8.93e13cc1.js" crossorigin="anonymous"></script><script defer="defer" src="https://kg-web-cdn.akamaized.net/online/user-platform-web/web-pay-hub/dist_online/static/1751299200/js/sdk2.bb32ab7e.js" crossorigin="anonymous"></script><link href="https://kg-web-cdn.akamaized.net/online/user-platform-web/web-pay-hub/dist_online/static/1751299200/css/chunk-koa-4c120c8f.4a501655.css" rel="stylesheet" crossorigin="anonymous"><link href="https://kg-web-cdn.akamaized.net/online/user-platform-web/web-pay-hub/dist_online/static/1751299200/css/chunk-koa-cdbd86b1.00951a08.css" rel="stylesheet" crossorigin="anonymous"><link href="https://kg-web-cdn.akamaized.net/online/user-platform-web/web-pay-hub/dist_online/static/1751299200/css/chunk-common-42f9d7e6.94416417.css" rel="stylesheet" crossorigin="anonymous"><link href="https://kg-web-cdn.akamaized.net/online/user-platform-web/web-pay-hub/dist_online/static/1751299200/css/chunk-common-b35596cd.4e9578b5.css" rel="stylesheet" crossorigin="anonymous"></head><body><noscript><strong>We're sorry but Topup center doesn't work properly without JavaScript enabled. Please enable it to continue.</strong></noscript><div id="app"></div><script>(function () {
    window._smReadyFuncs = [];
    window.SMSdk = {
      onBoxDataReady: function (boxData) {},
      ready: function (fn) {
        fn && _smReadyFuncs.push(fn);
      }
    };
    window._smConf = {
      organization: 'xNxMh079HgFDeRkJE1qN',
      appId: 'web_sdk',
      publicKey: 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtbekoieD6v30htpLAEPVp3w2nr9DRC8ElQu+qQfT+MPIU3K3Qc1FiF3gL0GDIKoTOXGuCXz/pVe7snZqcBh/8fsxpsQQMv/pSpzd6rLOthwwbvxLbWW06CU6SGXHBUDCYY+V/Y4GtsgwzhAf3Z0VZ/t0DXX8Yh/SaPXJuKJFn0wIDAQAB',
      apiHost: 'devproxy-web.kingsgroupgames.com'
      // apiHost: 'fp-devproxy-dev.nenglianghe.cn'
    };
  })();</script><script src="https://static.portal101.cn/dist/web/v3.0.0/fp.min.js"></script></body></html>