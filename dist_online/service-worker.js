self.addEventListener('install', function (event) {
  // caches.open('v1').then(function (cache) {
  //   return cache.addAll([
  //     // '/index.html',
  //     // '/manifest.webmanifest'
  //   ])
  // })
})

self.addEventListener('fetch', function (event) {
  event.respondWith(
    caches.match(event.request).then(function (response) {
      if (response) {
        return response
      }
      return fetch(event.request)
    })
  )
})
