const CACHE_VERSION="v2.0.0",CACHE_NAME=`pay-hub-${CACHE_VERSION}`,CACHE_STRATEGIES={STATIC:"cache-first",API:"network-first",IMAGES:"cache-first",DOCUMENTS:"network-first"},PRECACHE_URLS=["/","/static/css/app.css","/static/js/app.js","/static/js/chunk-vendors.js","/static/fonts/main.woff2","/config/koa.manifest.webmanifest","/config/dc.manifest.webmanifest"],API_CACHE_CONFIG={"/account/store/u":{ttl:18e5,strategy:"network-first"},"/api/sdk/coin_products":{ttl:6e5,strategy:"network-first"},"/token/channel/list":{ttl:36e5,strategy:"cache-first"},"/token/getIpCurrency":{ttl:864e5,strategy:"cache-first"}};function shouldHandleRequest(t){const e=new URL(t.url);if("GET"!==t.method)return!1;if(!e.protocol.startsWith("http"))return!1;const s=["google-analytics.com","googletagmanager.com"];return!s.some(t=>e.hostname.includes(t))}function isStaticResource(t){const e=new URL(t.url);return e.pathname.match(/\.(js|css|woff2?|ttf|eot)$/)}function isAPIRequest(t){const e=new URL(t.url);return e.pathname.startsWith("/api/")||e.pathname.startsWith("/token/")||e.pathname.startsWith("/account/")}function isImageRequest(t){const e=new URL(t.url);return e.pathname.match(/\.(png|jpg|jpeg|gif|svg|webp|ico)$/)}function isDocumentRequest(t){return"document"===t.destination}async function handleStaticResource(t){try{const e=await caches.open(CACHE_NAME),s=await e.match(t);if(s)return s;const a=await fetch(t);return a.ok&&e.put(t,a.clone()),a}catch(e){return console.error("Static resource fetch failed:",e),new Response("Resource not available",{status:503})}}async function handleAPIRequest(t){const e=new URL(t.url),s=e.pathname,a=API_CACHE_CONFIG[s]||{ttl:3e5,strategy:"network-first"};try{return"network-first"===a.strategy?await networkFirstStrategy(t,a):await cacheFirstStrategy(t,a)}catch(n){return console.error("API request failed:",n),new Response(JSON.stringify({error:"Service unavailable"}),{status:503,headers:{"Content-Type":"application/json"}})}}async function networkFirstStrategy(t,e){try{const e=await fetch(t);if(e.ok){const s=await caches.open(CACHE_NAME),a=e.clone(),n=new Headers(a.headers);n.set("sw-cache-timestamp",Date.now().toString());const c=new Response(a.body,{status:a.status,statusText:a.statusText,headers:n});s.put(t,c)}return e}catch(s){const a=await caches.open(CACHE_NAME),n=await a.match(t);if(n&&!isCacheExpired(n,e.ttl))return n;throw s}}async function cacheFirstStrategy(t,e){const s=await caches.open(CACHE_NAME),a=await s.match(t);if(a&&!isCacheExpired(a,e.ttl))return a;try{const e=await fetch(t);if(e.ok){const a=new Headers(e.headers);a.set("sw-cache-timestamp",Date.now().toString());const n=new Response(e.body,{status:e.status,statusText:e.statusText,headers:a});return s.put(t,n.clone()),n}return e}catch(n){if(a)return a;throw n}}function isCacheExpired(t,e){const s=t.headers.get("sw-cache-timestamp");if(!s)return!0;const a=parseInt(s),n=Date.now();return n-a>e}async function handleImageRequest(t){try{const e=await caches.open(CACHE_NAME),s=await e.match(t);if(s)return s;const a=await fetch(t);return a.ok&&e.put(t,a.clone()),a}catch(e){return console.error("Image fetch failed:",e),new Response("",{status:404})}}async function handleDocumentRequest(t){try{const e=await fetch(t);return e}catch(e){const t=await caches.open(CACHE_NAME),s=await t.match("/");return s||new Response("Page not available offline",{status:503})}}async function clearAllCaches(){const t=await caches.keys();await Promise.all(t.map(t=>caches.delete(t))),console.log("All caches cleared")}async function getCacheSize(){const t=await caches.open(CACHE_NAME),e=await t.keys();let s=0;for(const a of e){const e=await t.match(a);if(e){const t=await e.blob();s+=t.size}}return{entries:e.length,size:s,sizeFormatted:formatBytes(s)}}function formatBytes(t){if(0===t)return"0 Bytes";const e=1024,s=["Bytes","KB","MB","GB"],a=Math.floor(Math.log(t)/Math.log(e));return parseFloat((t/Math.pow(e,a)).toFixed(2))+" "+s[a]}self.addEventListener("install",t=>{console.log("Service Worker installing..."),t.waitUntil(caches.open(CACHE_NAME).then(t=>(console.log("Precaching static resources..."),t.addAll(PRECACHE_URLS))).then(()=>(console.log("Service Worker installed successfully"),self.skipWaiting())).catch(t=>{console.error("Service Worker installation failed:",t)}))}),self.addEventListener("activate",t=>{console.log("Service Worker activating..."),t.waitUntil(caches.keys().then(t=>Promise.all(t.map(t=>{if(t!==CACHE_NAME&&t.startsWith("pay-hub-"))return console.log("Deleting old cache:",t),caches.delete(t)}))).then(()=>(console.log("Service Worker activated successfully"),self.clients.claim())))}),self.addEventListener("fetch",t=>{const{request:e}=t;new URL(e.url);shouldHandleRequest(e)&&(isStaticResource(e)?t.respondWith(handleStaticResource(e)):isAPIRequest(e)?t.respondWith(handleAPIRequest(e)):isImageRequest(e)?t.respondWith(handleImageRequest(e)):isDocumentRequest(e)&&t.respondWith(handleDocumentRequest(e)))}),self.addEventListener("message",t=>{const{type:e,payload:s}=t.data;switch(e){case"SKIP_WAITING":self.skipWaiting();break;case"CLEAR_CACHE":clearAllCaches();break;case"GET_CACHE_SIZE":getCacheSize().then(e=>{t.ports[0].postMessage({type:"CACHE_SIZE",payload:e})});break;default:console.log("Unknown message type:",e)}});