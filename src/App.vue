<template>
  <div id="app" @click="$root.$emit('BodyClick')" :class="[$gameName]">
    <keep-alive include="Pay">
      <router-view v-if="showPage && isExtraInfoInit" :key="$route.fullPath"></router-view>
    </keep-alive>
    <PopHome></PopHome>
  </div>
</template>

<script>
import PopHome from '@/components/pop'
import { getCurrencyByIp, changeFpidToOpenid, getCommonInfo, getWxOpenid } from '@/server'
import { getPWADisplayMode, getPlatform, isWx, urlHelper } from '@/utils/utils'
import { logForPageLoad } from '@/utils/logHelper'
import i18n from '@/utils/i18n'

export default {
  components: { PopHome },
  data () {
    return {
      showPage: false,
      isExtraInfoInit: false
    }
  },
  methods: {
    async loadTheme () {
      // 使用明确的主题加载器，避免动态字符串构建
      const { loadTheme, loadSDK2Theme } = await import('@/utils/themeLoader')

      // 只在需要时加载主题文件
      if (this.$gcbk('switch.useThemeFile', false)) {
        await loadTheme(this.$gameName)
      }

      // 加载SDK2主题
      if (this.$store.state.IS_CHECKOUT_SDK_V2) {
        await loadSDK2Theme(this.$gameName)
      }
    },
    init () {
      // 减少收银台等待时间（领导希望更快展示）（优化2）。
      if (this.$store.state.IS_CHECKOUT_SDK) this.showPage = this.isExtraInfoInit = true
      getCurrencyByIp()
        .then(res => {
          const { code, data } = res
          if (code === 0) {
            this.showPage = true
            this.$store.commit('setCurrencyUnitByIp', data)
            this.$root.$emit('IPInitEnd')
            if (['/pay', '/'].includes(this.$route?.path)) this.$root.$emit('backendPopup', data)
          }
        })

      this.$loading.show()
      getCommonInfo()
        .then(res => {
          const { code, data } = res
          if (code === 0) {
            this.isExtraInfoInit = true
            this.$store.commit('formdata/setExtraCostInfo', data.channel_extra_fee_list)
            this.$store.commit('formdata/switchToggle', data.change_coupon_enable)
            this.$store.commit('functionSwitch/updateFunctionInfo', data)
            this.$root.$emit('updateSpecialDiamond', data.point_card_product)
            window.__showEmailForm = data.billing_address_enable // 控制在adyen等渠道页，是否显示email搜集表单。
            this.$store.commit('formdata/setFixedToggleEvent', data.change_coupon_enable) // gog、mc 是否开启切换返钻
          }
          if (code === 1000) this.$store.commit('userinfo/logout') // 错误openid
        })
        .finally(() => this.$loading.hide())
    },
    getOpenidByFpid () {
      this.$loading.show()
      changeFpidToOpenid({ fpid: this.$store.state.urlParams.t })
        .then(res => {
          const { code, data } = res
          if (code === 0 && data.openid_list && data.openid_list.length) {
            localStorage.setItem('openid', data.openid_list[0].openid)
          } else {
            console.error(`openid兑换失败：${this.$store.state.urlParams.t}`)
          }
          window.location.href = window.location.origin
        })
        .finally(() => this.$loading.hide())
    },
    checkWxOpenid () {
      if (!isWx) return
      const urlParams = urlHelper()
      if (window.localStorage.getItem('fp_wx_openid')) return undefined
      else if (urlParams.code && urlParams.state === 'fp_wx_get_code') {
        window.localStorage.setItem(`code_${new Date().getTime()}`, urlParams.code)
        getWxOpenid(urlParams.code).then(res => {
          const { code, data } = res
          code === 0 && window.localStorage.setItem('fp_wx_openid', data.openid)
        })
        return undefined
      } else {
        window.location.href = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx9d99d0a7d7b719b8&redirect_uri=${encodeURIComponent(window.location.origin)}&response_type=code&scope=snsapi_base&state=fp_wx_get_code#wechat_redirect`
      }
    },
    initLang () {
      const local = this.$i18n.locale || ''
      import(/* webpackChunkName: 'asyncTxt\/' */ `../langHelper/langJson/${local}.json`)
        .then(res => {
          res && i18n.setLocaleMessage(local, res)

          var title = document.querySelector('title')
          title.innerText = this.$vt('pageTitle')
        })
    }
  },
  created () {
    const href = location.href
    const tFlag = href.includes('&t=') || href.includes('?t=')
    if (tFlag) return this.getOpenidByFpid()

    logForPageLoad()
    const displayMode = getPWADisplayMode
    this.$gtag.event('opened_by', {
      event_label: displayMode === 'standalone'
        ? `pwa_${getPlatform}`
        : displayMode
    })

    if (this.$store.state.gameinfo.isCn) this.checkWxOpenid()
    this.init()
    this.initLang()

    this.loadTheme()
  }
}
</script>

<style lang="scss">
@import "~@/utils/reset.scss";
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  overflow-y: scroll;
  height: 100%;
  background-size: cover;
  background-position: center center;
}

#app.koa{
  color: #2c3e50;
  background-image: url(https://kg-web-cdn.akamaized.net/prod/versionAdmin/source/web-pay-hub/dev/page-bg-koa.png);
  background-color: rgb(28, 38, 64);
}

#app.aof, #app.rom, #app.koaCn{
  @extend .koa;
}

#app.dc{
  background-image: url(https://kg-web-cdn.akamaized.net/prod/versionAdmin/source/web-pay-hub/dev/page-bg-dc.png);
  background-color: rgb(23, 20, 25);
}
#app.ssv{
  background-image: url(https://kg-web-cdn.akamaized.net/prod/versionAdmin/source/web-pay-hub/page-bg-ssv.png);
  background-color: rgb(28, 28, 28);
}

#app.ssv2{
  background-image: url(https://kg-web-cdn.akamaized.net/prod/versionAdmin/source/web-pay-hub/page-bg-ssv.png);
  background-color: rgb(37, 23, 13);
}
</style>
