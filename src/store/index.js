import Vue from 'vue'
import Vuex from 'vuex'
import formdata from '@/store/module/formdata'
import gameinfo from '@/store/module/gameinfo'
import userinfo from '@/store/module/userinfo'
import orderPage from '@/store/module/orderPage'
import riskPolicy from '@/store/module/riskPolicy'
import vb from '@/store/module/vb'
import functionSwitch from '@/store/module/function'
import { getUrlParams, isArZone } from '@/utils/utils'

Vue.use(Vuex)

export default new Vuex.Store({
  state: {
    urlParams: getUrlParams(),

    currencyUnit: '', // 货币符号
    country: '',
    city: '',
    state: '',
    currency: '',
    zipCode: '',
    isArZone: false, // 是否需要货币 金额和符号
    isPc: window.innerWidth > 940,
    isMobile: window.innerWidth < 940,

    agreePrivacyPolicy: true,
    isPCSDK: !!window.__isPCSDK,
    IS_CHECKOUT_SDK: !!window.__IS_CHECKOUT_SDK,
    IS_CHECKOUT_SDK_V2: !!window.__IS_CHECKOUT_SDK_V2
  },
  mutations: {
    setCurrencyUnitByIp (state, payload) {
      state.currencyUnit = payload.currency_symbol
      state.country = payload.country
      state.city = payload.city
      state.state = payload.state
      state.currency = payload.currency
      state.isArZone = isArZone(payload.currency)
      state.zipCode = payload.zipcode
    },
    resetIsXXX (state) {
      state.isPc = window.innerWidth > 940
      state.isMobile = window.innerWidth < 940
    },
    setPrivacyPolicyStatus (state, payload) {
      state.agreePrivacyPolicy = payload
    },

    updateUrlParams (state, payload) {
      const tc = JSON.parse(state.urlParams.tc || '{}')
      Object.assign(tc, payload)
      state.urlParams.tc = JSON.stringify(tc)
    }
  },
  actions: {
  },
  modules: { formdata, gameinfo, userinfo, orderPage, riskPolicy, vb, functionSwitch }
})
