import Vue from 'vue'

export default {
  namespaced: true,
  state: {
    gameProject: '',
    gameCode: '',
    gameName: '',
    appId: '',
    gameId: '',

    defaultDiscount: true,
    whiteChannel: [],
    blackChannel: [],
    greyChannel: [],
    mainBody: 'funplus', // 游戏主体

    isSS: false,
    isKOA: false,
    isROMCP: false,
    game: window.__GAMENAME,
    isCn: false,

    // API主机地址
    tokenHost: '',
    apiHost: '',
    accountHost: '',
    ameHost: ''
  },
  mutations: {
    setGameInfo: (state) => {
      const payload = window.$gcbk('gameinfo', {})
      state.gameProject = payload.gameProject
      state.gameCode = payload.gameCode
      state.gameName = payload.gameName
      state.appId = payload.appId
      state.gameId = payload.gameId

      state.defaultDiscount = payload.defaultDiscount
      state.whiteChannel = payload.whiteChannel
      state.blackChannel = payload.blackChannel
      state.greyChannel = payload.greyChannel

      state.mainBody = payload.mainBody
      state.isCn = payload.isCn
      Vue.set(state, `is${state.gameCode}`, true)

      // 设置API主机地址
      const isCn = payload.isCn
      const gameCode = isCn ? `${payload.gameCode}CN` : payload.gameCode

      state.tokenHost = process.env[`VUE_APP_PREFIX_TOKEN_${gameCode}`] || ''
      state.apiHost = process.env[`VUE_APP_PREFIX_API_${gameCode}`] || ''
      state.accountHost = process.env[`VUE_APP_PREFIX_ACCOUNT_${gameCode}`] || process.env.VUE_APP_PREFIX_ACCOUNT || ''
      state.ameHost = process.env[`VUE_APP_PREFIX_AME_${gameCode}`] || process.env.VUE_APP_PREFIX_AME || ''
    }
  },
  getters: {
    isPuzalaGame: state => state.mainBody === 'puzala',
  }
}
