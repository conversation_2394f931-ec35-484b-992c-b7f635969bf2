import Vue from 'vue'

export default {
  namespaced: true,
  state: {
    gameProject: '',
    gameCode: '',
    gameName: '',
    appId: '',
    gameId: '',

    defaultDiscount: true,
    whiteChannel: [],
    blackChannel: [],
    greyChannel: [],
    mainBody: 'funplus', // 游戏主体

    isSS: false,
    isKOA: false,
    isROMCP: false,
    game: window.__GAMENAME,
    isCn: false
  },
  mutations: {
    setGameInfo: (state) => {
      const payload = window.$gcbk('gameinfo', {})
      state.gameProject = payload.gameProject
      state.gameCode = payload.gameCode
      state.gameName = payload.gameName
      state.appId = payload.appId
      state.gameId = payload.gameId

      state.defaultDiscount = payload.defaultDiscount
      state.whiteChannel = payload.whiteChannel
      state.blackChannel = payload.blackChannel
      state.greyChannel = payload.greyChannel

      state.mainBody = payload.mainBody
      state.isCn = payload.isCn
      Vue.set(state, `is${state.gameCode}`, true)
    }
  },
  getters: {
    isPuzalaGame: state => state.mainBody === 'puzala',
  }
}
