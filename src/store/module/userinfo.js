import { StorageUtils } from '@/utils/storageUtils'

export default {
  namespaced: true,
  state: {
    isLogin: false,
    country: '',
    currency: '',
    fpid: '',
    icon: '',
    lang: '',
    level: 0,
    name: '',
    openid: '',
    pkg_channel: '',
    server: 0,
    lastChannel: {} // 上次选中的渠道
  },
  mutations: {
    setUserInfo (state, payload) {
      state.level = payload.level
      state.server = payload.server
      state.uid = payload.uid
      state.openid = payload.openid
      state.icon = payload.icon
      state.name = payload.name
      state.fpid = payload.fpid
      localStorage.setItem('openid', state.openid)

      state.isLogin = true

      const logParams = {}
      if (payload.uid) logParams.uid = payload.uid
      if (payload.openid) logParams.openid = payload.openid
      window.__ReportExtraData__ && window.__ReportExtraData__(logParams)
      StorageUtils.init(payload.openid)
    },
    saveLastChannelInfo (state, payload) {
      state.lastChannel = payload.last_channel
    },
    logout () {
      localStorage.removeItem('openid')

      const { origin, pathname } = location
      window.location.href = `${origin}${pathname}`
    }
  }
}
