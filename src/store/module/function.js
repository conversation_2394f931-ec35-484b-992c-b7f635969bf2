export default {
  namespaced: true,
  state: {
    loginValidation: false,
    showMobilePolicy: false, // 移动端在下面的退款协议
    showPopPolicy: false, // 弹窗的协议
    boon: false,
    fixedDiscountType: '',
    smallDiamondDoubleDiscount: false, // 100钻小档位双倍
    ckoCheckedByDefault: true, // 默认勾选cko记住卡号
    showPcDiscountTips: false // pc端左侧faq上面显示折扣信息
  },
  mutations: {
    setFunctionInfo: (state) => {
      const payload = window.$gcbk('switch', {})
      state.loginValidation = payload.loginValidation
      state.showMobilePolicy = payload.showMobilePolicy
      state.showPopPolicy = payload.showPopPolicy
      state.boon = payload.boon
      state.fixedDiscountType = payload.fixedDiscountType
      state.ckoCheckedByDefault = payload.ckoCheckedByDefault
      state.smallDiamondDoubleDiscount = payload.smallDiamondDoubleDiscount
      state.showPcDiscountTips = payload.showPcDiscountTips
    },
    updateFunctionInfo(state, payload){
      if ('send_code_enable' in payload) state.loginValidation = payload.send_code_enable

      // 切换功能
      if ('switches' in payload) {
        if ('bind_card' in payload['switches'])  state.ckoCheckedByDefault = payload['switches'].bind_card
      }
    }
  }
}
