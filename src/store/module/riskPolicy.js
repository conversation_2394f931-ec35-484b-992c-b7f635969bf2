export default {
  namespaced: true,
  state: {
    currentKey: [],
    // currentValue: 0,
    isInit: false,
    emit: () => {}
  },
  getters: {
    forbiddenAccess (state) {
      if (!state.isInit) return false
      return state.currentKey._hasUnion(['always_banned', 'banned_uid'])
    },
    showAdyenTips (state) {
      if (!state.isInit) return false
      return state.currentKey._hasUnion(['use_adyen'])
    },
    showTipsWhenSomeChannel (state, getters) {
      const finalNames = []
      if (!state.isInit) return finalNames
      if (getters.showAdyenTips) finalNames.push('adyen')
      if (state.currentKey.includes('use_wxpay')) finalNames.push('wxpay')
      if (state.currentKey.includes('use_alipay')) finalNames.push('alipay')
      if (state.currentKey.includes('use_paypal')) finalNames.push('paypal')
      if (state.currentKey.includes('use_pingpong')) finalNames.push('pingpong')

      return finalNames
    },
    hideAdyenChannel (state) {
      if (!state.isInit) return false
      return state.currentKey._hasUnion(['banned_adyen', 'banned_adyen_card'])
    },
    hideSomeChannel (state, getters) {
      const finalNames = []
      if (!state.isInit) return finalNames
      if (getters.hideAdyenChannel) finalNames.push('adyen')
      if (state.currentKey.includes('banned_wxpay')) finalNames.push('wxpay')
      if (state.currentKey.includes('banned_alipay')) finalNames.push('alipay')
      if (state.currentKey.includes('banned_paypal')) finalNames.push('paypal')
      if (state.currentKey.includes('banned_pingpong')) finalNames.push('pingpong')

      return finalNames
    }
  },
  mutations: {
    init (state, payload = {}) {
      // payload.list = [{
      //   banned_status: 'always_banned',
      //   expire_time: 0
      // }]
      const { list = [], emit } = payload
      state.isInit = true
      state.emit = emit

      list.forEach(item => {
        const { banned_status: key, expire_time: value } = item
        state.currentKey.push(key)

        if (['always_banned', 'banned_uid', 'access_warn', 'access_warn_black_room'].includes(key)) {
          emit('showPop', 'RiskControlPolicy', { key, value })
        }
      })
    }
  }
}
