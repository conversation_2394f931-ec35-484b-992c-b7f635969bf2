import { OrderPageOpenidKey } from '@/config/OrderPageConf'

export default {
  namespaced: true,
  state: {
    userinfo: {
      isLogin: false,
      country: '',
      currency: '',
      fpid: '',
      icon: '',
      lang: '',
      level: 0,
      name: '',
      openid: '',
      pkg_channel: '',
      server: 0
    }
  },
  mutations: {
    setUserInfo (state, payload) {
      state.userinfo.level = payload.level
      state.userinfo.server = payload.server
      state.userinfo.uid = payload.uid
      state.userinfo.openid = payload.openid
      state.userinfo.icon = payload.icon
      state.userinfo.name = payload.name
      localStorage.setItem(OrderPageOpenidKey, state.userinfo.openid)

      state.userinfo.isLogin = true

      const logParams = {}
      if (payload.uid) logParams.uid = payload.uid
      if (payload.openid) logParams.openid = payload.openid
      window.__ReportExtraData__ && window.__ReportExtraData__(logParams)
    },
    logout () {
      localStorage.removeItem(OrderPageOpenidKey)

      const { origin, pathname } = location
      window.location.href = `${origin}${pathname}/order`
    }
  }
}
