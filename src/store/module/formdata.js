import Vue from 'vue'
import { priceHelper } from '@/utils/utils'
import i18n from '@/utils/i18n'

export default {
  namespaced: true,
  state: {
    /* 渠道 */
    chosenChannel: {},

    /* ss钻石 */
    chosenDiamond: {},

    /* 优惠券 */
    chosenCoupon: {},
    isFirstPayUsed: true,
    isInit: false,
    chosenCouponOther: {},

    vip: {
      discountSubChannelId: [],
      diamondBonus: 0,
      channelBonus: 0,
      level: 0,
      isInit: false,
      isNewUser: false
    },

    /* 首充折扣档位 */
    firstPayProducts: {},

    // 每日奖励状态
    gotDailyReward: false,
    extraCostInfo: {},
    koaTopupEnable: false,

    switchToggleState: false, // 优惠券转化是否开启
    showDoubleExperience: false,

    defaultRebateDynamicInfo: {}, // 固定动态返钻，每个档位价格不一样
    defaultRebateDynamicInfoAll: {},

    defaultRebateInfo: {}, // 固定返钻
    defaultRebateInfoByProduct4: {}, // ss等不是每个档位都有固定折扣，但是第四个有，通过第四个档位来判断该用户是否有固定折扣
    defaultDiscountInfo: {}, // 固定折扣
    defaultDiscountInfoByProduct4: {},

    isFixedEventOpen: false, // mc、gog 切换活动是否开启
    isFixedRebateWork: false // mc、gog通过开关切成固定返钻
  },
  getters: {
    // 开启长期XX折
    takeEffectDefaultDiscount (state, getters, rootState) {
      if (!rootState.gameinfo.defaultDiscount) return false
      // case 1： 登录情况下，没有首冲、没有选中优惠券
      return (rootState.userinfo.isLogin && !!(state.isFirstPayUsed && !state.chosenCoupon.FE_INDEX)) ||
        // case 2: 未登录
        (!rootState.userinfo.isLogin)
    },
    // 固定返钻
    takeEffectDefaultRebate (state, getters, rootState) {
      if (!state.defaultRebateInfo.FE_INDEX) return false
      return (rootState.userinfo.isLogin && !!(state.isFirstPayUsed && !state.chosenCoupon.FE_INDEX)) ||
        (!rootState.userinfo.isLogin)
    },
    TWMyCard (state) {
      const chosenDiamond = state.chosenDiamond
      return chosenDiamond.product_id && chosenDiamond.product_id.includes('com.kingsgroup.ss.tw.mc')
    },
    FinalPriceState (state, getters, rootState) {
      // const chosenDiamond = state.chosenDiamond
      const chosenCoupon = state.chosenCoupon
      const chosenDiamond = state.chosenDiamond
      const temp = {
        /* 优惠券 */
        rawOriginPrice: 0,
        rawNowPrice: 0, // 展示原价
        taxation: 0,
        extra_fee_amount: 0,
        offCountTips: '', // 比如多少折
        offCountAmount: '', // 比如实际少了2块
        /* 上面价格 + (加税+额外费用) */
        finalNowPrice: 0,
        finalOriginPrice: 0,

        rate: '', // 返钻比例
        coin: 0,  // 实际发放钻石数量
        level_coin: 0,  // 档位钻石数量
        // 其他辅助
        feType: '', // 优惠券类型,
        isDefault: false, // 95或者返钻
        isFixedCoupon: false, // 固定折扣
        isFixedRebate: false, // 固定返钻
        sdkType: '' // 首充优惠与固定优惠 act_type
      }
      const couponKey = chosenCoupon.feType || state.defaultRebateInfo.feType || state.defaultDiscountInfo.feType || state.defaultRebateDynamicInfo.feType


      // 首冲、优惠券
      switch (couponKey) {
        case 'first_pay': {
          temp.feType = 'first_pay'
          temp.finalOriginPrice = chosenCoupon.price
          temp.finalNowPrice = chosenCoupon.discount_price
          temp.rawNowPrice = chosenCoupon.no_tax_price
          temp.rawOriginPrice = chosenCoupon.level_currency_price
          temp.offCountTips = `1st Pay ${chosenCoupon.rate} OFF`
          temp.sdkType = chosenCoupon.type
          break
        }
        case 'first_pay_rebate': {
          temp.feType = 'first_pay_rebate'

          temp.finalNowPrice = chosenCoupon.price
          temp.rawNowPrice = chosenDiamond.no_tax_price
          temp.taxation = chosenCoupon.taxation
          temp.extra_fee_amount = chosenCoupon.extra_fee_amount
          temp.rate = chosenCoupon.rate
          temp.coin = chosenCoupon.coin
          temp.level_coin = chosenCoupon.level_coin
          temp.sdkType = chosenCoupon.type

          temp.offCountTips = `1st Pay ${i18n.t('bonus_tips')} ${chosenCoupon.rate} <i class="diamond-icon"></i>`
          break
        }
        case 'discount_coupon': {
          temp.feType = 'discount_coupon'
          temp.finalOriginPrice = chosenCoupon.price
          temp.finalNowPrice = chosenCoupon.discount_price
          temp.rawNowPrice = chosenCoupon.no_tax_price
          temp.rawOriginPrice = chosenCoupon.level_currency_price

          temp.offCountTips = i18n.t('coupon_discount', { 0: chosenCoupon.rateWidthOutPercent })
          break
        }
        case 'cash_coupon': {
          temp.feType = 'cash_coupon'
          temp.finalOriginPrice = chosenDiamond.price
          temp.finalNowPrice = chosenCoupon.price
          temp.rawNowPrice = chosenCoupon.no_tax_price
          temp.rawOriginPrice = chosenCoupon.level_currency_price

          temp.offCountTips = `${chosenCoupon.deduct_price} ${chosenDiamond.currency_symbol} OFF`
          break
        }
        case 'rebate_coupon': {
          temp.feType = 'rebate_coupon'
          temp.finalNowPrice = chosenCoupon.price
          temp.rawNowPrice = chosenCoupon.no_tax_price

          temp.offCountTips = `${i18n.t('bonus_tips')} ${chosenCoupon.rate} <i class="diamond-icon"></i>`
          break
        }
        case 'fixed_discount_coupon': {
          const defaultDiscountInfo = state.defaultDiscountInfo
          // 有些没有95折
          temp.feType = 'fixed_discount_coupon'
          temp.isDefault = true

          temp.finalNowPrice = defaultDiscountInfo.discount_price
          temp.finalOriginPrice = defaultDiscountInfo.price
          temp.rawNowPrice = defaultDiscountInfo.no_tax_price
          temp.rawOriginPrice = defaultDiscountInfo.level_currency_price

          temp.offCountTips = `${defaultDiscountInfo.rateWidthOutPercent}% OFF`
          temp.offCountAmount = defaultDiscountInfo.discount_amount
          temp.taxation = defaultDiscountInfo.taxation
          temp.extra_fee_amount = defaultDiscountInfo.extra_fee_amount

          temp.isFixedCoupon = true

          temp.sdkType = defaultDiscountInfo.type
          break
        }
        case 'fixed_rebate': {
          const defaultRebateInfo = state.defaultRebateInfo
          temp.feType = 'fixed_rebate'
          temp.isDefault = true

          temp.finalNowPrice = defaultRebateInfo.price
          temp.rawNowPrice = defaultRebateInfo.no_tax_price
          temp.taxation = defaultRebateInfo.taxation
          temp.extra_fee_amount = defaultRebateInfo.extra_fee_amount

          temp.offCountTips = `${i18n.t('bonus_tips')} ${defaultRebateInfo.rate} <i class="diamond-icon"></i>`
          temp.isFixedRebate = true
          temp.sdkType = defaultRebateInfo.type
          break
        }
        case 'fixed_dynamic_rebate': {
          const defaultRebateDynamicInfo = state.defaultRebateDynamicInfo
          temp.feType = 'fixed_dynamic_rebate'
          temp.isDefault = true

          temp.finalNowPrice = defaultRebateDynamicInfo.price
          temp.rawNowPrice = defaultRebateDynamicInfo.no_tax_price
          temp.taxation = defaultRebateDynamicInfo.taxation
          temp.extra_fee_amount = defaultRebateDynamicInfo.extra_fee_amount

          temp.offCountTips = `${i18n.t('bonus_tips')} ${defaultRebateDynamicInfo.rate} <i class="diamond-icon"></i>`
          temp.isFixedRebate = true
          break
        }
        default: {
          temp.rawNowPrice = chosenDiamond.no_tax_price
          temp.finalNowPrice = chosenDiamond.price

          const chosenChannel = state.chosenChannel
          const unionKey = chosenChannel.channel_id + (chosenChannel.sub_channel_id ? `_${chosenChannel.sub_channel_id}` : '')
          const { level_currency_price: price, currency, tax_rate: taxRate, chosenNum = 1 } = state.chosenDiamond
          const extraRate = state.extraCostInfo[unionKey] || state.extraCostInfo[chosenChannel.channel_id] || 1

          if (extraRate && price) {
            temp.extra_fee_amount = priceHelper(chosenNum * price * (extraRate - 1), currency)
            temp.finalNowPrice = priceHelper(chosenNum * price * (extraRate - 1 + (taxRate || 1)), currency)
            state.chosenDiamond.extra_fee_amount = temp.extra_fee_amount // 不改之前的逻辑，因为之前是否显示额外费用用的此参数
          }
        }
      }

      return fixPrice(temp, state.chosenDiamond)
    },
    isDiamondOwn95Off: (state) => diamond => state.defaultDiscountInfo[diamond.product_id] || state.defaultDiscountInfoByProduct4[diamond.product_id] || false,
    isDiamondOwnRebate: (state) => diamond => state.defaultRebateInfo[diamond.product_id] || state.defaultRebateInfoByProduct4[diamond.product_id] || state.defaultRebateDynamicInfoAll[diamond.product_id] || false,
    getRebateCoin (state) {
      return (diamond) => {
        const t = state.defaultRebateInfo[diamond.product_id] || state.defaultRebateInfoByProduct4[diamond.product_id] || state.defaultRebateDynamicInfoAll[diamond.product_id]
        return (t.coin - t.level_coin) || 0
      }
    },
    getSDKRebateCoin (state) {
      return (state.defaultRebateInfo.coin - state.defaultRebateInfo.level_coin) || 0
    }
  },
  mutations: {
    /* 渠道 */
    setChosenChannel (state, payload) {
      state.chosenChannel = payload

      resetPrice(state)
      require('@/utils/logHelper').logForClickChannel(JSON.stringify({
        channel: payload.channel_name,
        channel_id: payload.channel_id,
        sub_channel_id: payload.sub_channel_id
      }))
    },
    resetChannel (state) {
      state.chosenChannel = {}

      resetPrice(state)
    },
    /* ss钻石 */
    setChosenDiamond (state, payload) {
      state.chosenDiamond = payload

      resetPrice(state)
      require('@/utils/logHelper').logForClickDiamond(JSON.stringify(payload))
    },
    /* 优惠券 */
    setChosenCoupon (state, payload) {
      if (!payload) return
      state.chosenCoupon = payload

      state.chosenCouponOther = {}
      for (const item of Object.values(payload.product_discount_range || [])) {
        Vue.set(state.chosenCouponOther, item.product_id, item)
      }

      require('@/utils/logHelper').logForClickCoupon(payload)
    },
    setFirstPayStatus (state, payload) {
      state.isFirstPayUsed = payload
    },
    setIsInit (state, payload) {
      state.isInit = payload
    },
    resetCouponInfo (state) {
      state.chosenCoupon = {}
      state.chosenCouponOther = {}
      state.isFirstPayUsed = true
      state.isInit = false
      state.firstPayProducts = {};
    },
    initVipInfo (state, payload) {
      state.vip.discountSubChannelId = ['scheme', '1380']
      state.vip.diamondBonus = payload.vip_bonus
      state.vip.channelBonus = payload.pay_bonus
      state.vip.level = payload.level
      state.vip.isInit = payload.is_white
      state.vip.isNewUser = (payload.exp === 0 && payload.level === 1)

      state.gotDailyReward = payload.daily_reward_receive
    },
    setFirstPayProducts (state, payLoad) {
      if (!payLoad.length) return
      (payLoad[0].product_discount_range || []).forEach(item => {
        state.firstPayProducts[item.product_id] = { ...item }
      })
    },
    setFixedCoupon (state, payload) {
      state.defaultDiscountInfo = payload
      for (const item of Object.values(payload.product_discount_range || [])) {
        Vue.set(state.defaultDiscountInfo, item.product_id, item)
      }
    },
    // 固定返钻
    setFixedRebate (state, payload) {
      state.defaultRebateInfo = payload
      for (const item of Object.values(payload.product_discount_range || [])) {
        Vue.set(state.defaultRebateInfo, item.product_id, item)
      }
    },
    setFixedCouponByProduct4 (state, payload) {
      for (const item of Object.values(payload.product_discount_range || [])) {
        Vue.set(state.defaultDiscountInfoByProduct4, item.product_id, item)
      }
    },
    setFixedRebateByProduct4 (state, payload) {
      for (const item of Object.values(payload.product_discount_range || [])) {
        Vue.set(state.defaultRebateInfoByProduct4, item.product_id, item)
      }
    },

    setDailyRewardStatus (state, payload) {
      state.gotDailyReward = payload
    },
    setExtraCostInfo (state, payLoad = []) {
      payLoad.forEach(item => {
        const unionKey = item.channel_id + (item.sub_channel_id ? `_${item.sub_channel_id}` : '')
        state.extraCostInfo[unionKey] = item.extra_fee_rate
      })
    },
    setKoaTopupEnable (state, payload) {
      state.koaTopupEnable = payload
    },
    switchToggle (state, payload) {
      state.switchToggleState = payload || false
    },
    switchDoubleExperience (state, payload) {
      state.showDoubleExperience = payload || false
    },
    setFixedDynamicRebate (state, { chosen, all } = {}) {
      state.defaultRebateDynamicInfo = chosen
      for (const item of (all || [])) {
        Vue.set(state.defaultRebateDynamicInfoAll, item.product_id, item)
      }
    },
    toggleCoupon (state) {
      state.isFixedRebateWork = !state.isFixedRebateWork
      localStorage.setItem('fixedRebateWorkStatus', Number(state.isFixedRebateWork))
    },
    setFixedToggleEvent (state, payload) {
      state.isFixedEventOpen = payload
      if (payload) {
        state.isFixedRebateWork = localStorage.getItem('fixedRebateWorkStatus') === '1'
      }
    }
  }
}

let timeout
function resetPrice (state) {
  if (timeout) {
    clearTimeout(timeout)
    timeout = undefined
  }

  timeout = setTimeout(() => {
    const chosenChannel = state.chosenChannel
    const unionKey = chosenChannel.channel_id + (chosenChannel.sub_channel_id ? `_${chosenChannel.sub_channel_id}` : '')
    const extraRate = state.extraCostInfo[unionKey] || state.extraCostInfo[chosenChannel.channel_id] || 1
    const { level_currency_price: price, currency, tax_rate: taxRate, chosenNum = 1 } = state.chosenDiamond

    // Vue.set(state.chosenDiamond, 'extra_fee_amount', priceHelper(chosenNum * price * (extraRate - 1), currency))
    // Vue.set(state.chosenDiamond, 'nowPriceWithTaxAndExtra', priceHelper(chosenNum * price * (extraRate - 1 + (taxRate || 1)), currency))

    clearTimeout(timeout)
    timeout = null
  }, 500)
}

function fixPrice (temp, chosenDiamond) {
  const currencySymbol = chosenDiamond.currency_symbol
  const toAddSymbolList = ['rawOriginPrice', 'rawNowPrice', 'finalNowPrice', 'finalOriginPrice']
  toAddSymbolList.forEach(key => {
    if (temp[key]) temp[key] += ` ${currencySymbol}`
  })

  return temp
}
