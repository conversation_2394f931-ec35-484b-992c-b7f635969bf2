export default {
  namespaced: true,
  state: {
    builtInCashier: false, // 内嵌收银台，前置卡组件忽略
    isDiscountUsed: false // 小档位是否使用
  },
  mutations: {
    setIsDiscountUsed (state, payload) {
      state.isDiscountUsed = payload
    },
    savePrefixChannel (state, payload) {
      if (payload && payload.length) {
        state.builtInCashier = payload[0].type === 'drop_in' // 在此填写其他打开灰度的逻辑
      }
    },
    resetBuiltInCashierStatus (state, payload) {
      if (!payload || !payload.length) return null

      const kv = {}
      payload.forEach(item => {
        kv[`${item.channel_id}_${item.channel_name}_${item.sub_channel_id}_${item.type}`] = item
      })

      const payermaxDropInObj = kv.payermax_A34_A34_dropin
      if (payermaxDropInObj) {
        switch (payermaxDropInObj.status) {
          case 'close': {
            state.builtInCashier = false
            break
          }
          case 'open_all': {
            state.builtInCashier = true
            break
          }
          default: {
            // 默认open_ab，不管
          }
        }
      }
    }
  }
}
