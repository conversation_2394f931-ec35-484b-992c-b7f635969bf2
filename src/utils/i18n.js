import Vue from 'vue'
import VueI18n from 'vue-i18n'
import langConf from '../config/LangConf'
import store from '@/store'

Vue.use(VueI18n)

const lang = store.state.urlParams.l || navigator.language || navigator.userLanguage
let defaultLang

if (lang.toLowerCase() === 'zh-tw' || lang.toLowerCase() === 'zh_tw') defaultLang = 'zh_tw'
else if (lang.startsWith('zh')) defaultLang = 'zh_cn'
else defaultLang = lang.split('-')[0]

const allowLang = ['zh_cn', 'en', 'zh_tw', 'ar', 'de', 'es', 'fr', 'id', 'it', 'ja', 'ko', 'nl', 'pl', 'pt', 'ru', 'sv', 'th', 'tr', 'vi', 'my']
if (!allowLang.includes(defaultLang)) defaultLang = 'en'

const i18n = new VueI18n({
  locale: defaultLang, // 语言标识
  fallbackLocale: 'en',
  messages: langConf.config
})

export default i18n

export const langObj = {
  en: 'English',
  // zh_cn: '中文',
  // zh_tw: '繁體中文',
  ko: '한국어',
  // sv: 'xxx',
  // pl:'xxx',
  // nl:'xxx',
  // id: 'Bahasa Indonesia',
  // es: 'Español',
  ja: '日本語',
  // tr:'xxx',
  // vi:'xxx',
  fr: 'Français',
  de: 'Deutsch',
  // it: 'Italiano',
  ru: 'Русский'
  // pt: 'Português',
  // th: 'xxx',
  // ar: 'xxx',
  // my: 'xxx',
}
