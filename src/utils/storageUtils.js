export const StorageUtils = (function () {
  let nameSpaceKey
  const StorageUtils = {}

  const finalKey = key => nameSpaceKey ? `${nameSpaceKey}_${key}` : key

  StorageUtils.init = function (key) {
    nameSpaceKey = key
  }

  StorageUtils.setLocalStorage = function (key, value) {
    console.log(finalKey(key), value)
    localStorage.setItem(finalKey(key), value)
  }

  StorageUtils.setSessionStorage = function (key, value) {
    sessionStorage.setItem(finalKey(key), value)
  }

  StorageUtils.getLocalStorage = function (key) {
    return localStorage.getItem(finalKey(key))
  }

  StorageUtils.getSessionStorage = function (key) {
    return sessionStorage.getItem(finalKey(key))
  }

  return StorageUtils
})()
