/**
 * 组件懒加载工具
 * 提供组件按需加载和预加载功能
 */

// 懒加载组件缓存
const componentCache = new Map()

/**
 * 创建懒加载组件
 * @param {Function} importFn - 动态导入函数
 * @param {Object} options - 配置选项
 * @returns {Function} Vue异步组件
 */
export function createLazyComponent(importFn, options = {}) {
  const {
    loading = null,
    error = null,
    delay = 200,
    timeout = 10000,
    retry = 3,
    chunkName = 'lazy-component'
  } = options

  return () => ({
    component: importFn(),
    loading,
    error,
    delay,
    timeout
  })
}

/**
 * 预加载组件
 * @param {Array} components - 组件导入函数数组
 */
export async function preloadComponents(components) {
  const promises = components.map(async (importFn) => {
    try {
      const component = await importFn()
      return component
    } catch (error) {
      console.warn('Failed to preload component:', error)
      return null
    }
  })

  return Promise.allSettled(promises)
}

/**
 * 支付相关组件懒加载配置
 */
export const PaymentComponents = {
  // 支付方式组件
  Adyen: () => import(/* webpackChunkName: "payment-adyen" */ '@/views/paymethod/Adyen.vue'),
  Airwallex: () => import(/* webpackChunkName: "payment-airwallex" */ '@/views/paymethod/airwallex.vue'),
  Checkout: () => import(/* webpackChunkName: "payment-checkout" */ '@/views/paymethod/checkout.vue'),
  Payermax: () => import(/* webpackChunkName: "payment-payermax" */ '@/views/paymethod/payermax.vue'),
  Pingpong: () => import(/* webpackChunkName: "payment-pingpong" */ '@/views/paymethod/pingpong.vue'),
  Stripe: () => import(/* webpackChunkName: "payment-stripe" */ '@/views/paymethod/stripe.vue'),
  
  // 支付组件
  ChannelOrder: () => import(/* webpackChunkName: "payment-channel" */ '@/views/paymethod/channelOrder.vue'),
  ChannelLogo: () => import(/* webpackChunkName: "payment-channel" */ '@/views/paymethod/channelLogo.vue'),
  ChannelWrapper: () => import(/* webpackChunkName: "payment-channel" */ '@/views/paymethod/channelWrapper.vue'),
  
  // 通用组件
  ChannelChoose: () => import(/* webpackChunkName: "payment-common" */ '@/components/ChannelChoose.vue'),
  CheckoutCounter: () => import(/* webpackChunkName: "payment-common" */ '@/components/CheckoutCounterTax.vue'),
  DiamondChoose: () => import(/* webpackChunkName: "payment-common" */ '@/components/DiamondChooseKOA.vue')
}

/**
 * 根据支付方式预加载相关组件
 * @param {string} paymentMethod - 支付方式
 */
export async function preloadPaymentComponents(paymentMethod) {
  const componentsToLoad = []
  
  // 根据支付方式加载对应组件
  switch (paymentMethod) {
    case 'adyen':
      componentsToLoad.push(PaymentComponents.Adyen)
      break
    case 'airwallex':
      componentsToLoad.push(PaymentComponents.Airwallex)
      break
    case 'checkout':
      componentsToLoad.push(PaymentComponents.Checkout)
      break
    case 'payermax':
      componentsToLoad.push(PaymentComponents.Payermax)
      break
    case 'pingpong':
      componentsToLoad.push(PaymentComponents.Pingpong)
      break
    case 'stripe':
      componentsToLoad.push(PaymentComponents.Stripe)
      break
    default:
      // 加载通用组件
      componentsToLoad.push(
        PaymentComponents.ChannelChoose,
        PaymentComponents.CheckoutCounter,
        PaymentComponents.DiamondChoose
      )
  }
  
  return preloadComponents(componentsToLoad)
}

/**
 * 智能预加载
 * 根据用户行为预测需要的组件
 */
export class SmartPreloader {
  constructor() {
    this.loadedComponents = new Set()
    this.preloadQueue = []
    this.isPreloading = false
  }

  /**
   * 添加到预加载队列
   * @param {Function} importFn - 组件导入函数
   * @param {number} priority - 优先级 (数字越小优先级越高)
   */
  addToQueue(importFn, priority = 5) {
    this.preloadQueue.push({ importFn, priority })
    this.preloadQueue.sort((a, b) => a.priority - b.priority)
    
    if (!this.isPreloading) {
      this.processQueue()
    }
  }

  /**
   * 处理预加载队列
   */
  async processQueue() {
    if (this.preloadQueue.length === 0) {
      this.isPreloading = false
      return
    }

    this.isPreloading = true
    
    // 在空闲时间预加载
    if ('requestIdleCallback' in window) {
      requestIdleCallback(() => this.loadNext())
    } else {
      setTimeout(() => this.loadNext(), 100)
    }
  }

  /**
   * 加载下一个组件
   */
  async loadNext() {
    if (this.preloadQueue.length === 0) {
      this.isPreloading = false
      return
    }

    const { importFn } = this.preloadQueue.shift()
    
    try {
      await importFn()
      this.loadedComponents.add(importFn)
    } catch (error) {
      console.warn('Failed to preload component:', error)
    }

    // 继续处理队列
    this.processQueue()
  }

  /**
   * 预加载支付相关组件
   */
  preloadPaymentComponents() {
    // 高优先级：通用支付组件
    this.addToQueue(PaymentComponents.ChannelChoose, 1)
    this.addToQueue(PaymentComponents.CheckoutCounter, 1)
    
    // 中优先级：常用支付方式
    this.addToQueue(PaymentComponents.Adyen, 3)
    this.addToQueue(PaymentComponents.Stripe, 3)
    
    // 低优先级：其他支付方式
    this.addToQueue(PaymentComponents.Airwallex, 5)
    this.addToQueue(PaymentComponents.Checkout, 5)
    this.addToQueue(PaymentComponents.Payermax, 5)
    this.addToQueue(PaymentComponents.Pingpong, 5)
  }
}

// 创建全局预加载器实例
export const smartPreloader = new SmartPreloader()

/**
 * 初始化懒加载
 */
export function initLazyLoad() {
  // 页面加载完成后开始预加载
  if (document.readyState === 'complete') {
    smartPreloader.preloadPaymentComponents()
  } else {
    window.addEventListener('load', () => {
      smartPreloader.preloadPaymentComponents()
    })
  }
}
