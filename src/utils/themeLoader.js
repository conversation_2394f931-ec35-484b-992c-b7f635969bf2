// 主题文件映射
// 只导入实际存在的主题文件，避免webpack尝试解析所有可能的路径
const themeMap = {
  // 如果需要为特定游戏添加主题，在这里明确导入
  // 例如：
  // 'ss': () => import('@/theme/ss.scss'),
  // 'dc': () => import('@/theme/dc.scss'),
  // 注意：koa不需要主题文件，所以不在这里添加
}

export async function loadTheme(gameName) {
  const themeLoader = themeMap[gameName]
  if (themeLoader) {
    try {
      await themeLoader()
      console.log(`Theme loaded for ${gameName}`)
    } catch (error) {
      console.warn(`Failed to load theme for ${gameName}:`, error)
    }
  } else {
    console.log(`No theme file for ${gameName}`)
  }
}

export async function loadSDK2Theme(gameName) {
  // 对于koa游戏，跳过SDK2主题文件加载以避免资源错误
  if (gameName === 'koa') {
    console.log('Skipping SDK2 theme for koa game')
    return
  }

  // 检查是否为支持的游戏
  const supportedGames = ['ss', 'dc', 'foundation']
  if (!supportedGames.includes(gameName)) {
    console.log(`SDK2 theme not supported for ${gameName}`)
    return
  }

  try {
    await import('@/theme/sdk2.scss')
    console.log('SDK2 theme loaded')
  } catch (error) {
    console.warn('Failed to load SDK2 theme:', error)
  }
}
