/**
 * 性能监控工具
 * 监控Core Web Vitals和自定义性能指标
 */

class PerformanceMonitor {
  constructor() {
    this.metrics = new Map()
    this.observers = []
    this.isEnabled = process.env.NODE_ENV === 'production'
    
    if (this.isEnabled) {
      this.init()
    }
  }

  init() {
    // 监控Core Web Vitals
    this.observeWebVitals()
    
    // 监控资源加载
    this.observeResourceTiming()
    
    // 监控长任务
    this.observeLongTasks()
    
    // 监控页面可见性变化
    this.observeVisibilityChange()
    
    // 页面卸载时发送数据
    this.setupBeforeUnload()
  }

  /**
   * 监控Core Web Vitals指标
   */
  observeWebVitals() {
    // LCP (Largest Contentful Paint)
    if ('PerformanceObserver' in window) {
      const lcpObserver = new PerformanceObserver((entryList) => {
        const entries = entryList.getEntries()
        const lastEntry = entries[entries.length - 1]
        this.recordMetric('LCP', lastEntry.startTime, {
          element: lastEntry.element?.tagName || 'unknown',
          url: lastEntry.url || location.href
        })
      })
      
      try {
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })
        this.observers.push(lcpObserver)
      } catch (e) {
        console.warn('LCP observer not supported')
      }
    }

    // FID (First Input Delay)
    if ('PerformanceObserver' in window) {
      const fidObserver = new PerformanceObserver((entryList) => {
        const entries = entryList.getEntries()
        entries.forEach(entry => {
          this.recordMetric('FID', entry.processingStart - entry.startTime, {
            eventType: entry.name,
            target: entry.target?.tagName || 'unknown'
          })
        })
      })
      
      try {
        fidObserver.observe({ entryTypes: ['first-input'] })
        this.observers.push(fidObserver)
      } catch (e) {
        console.warn('FID observer not supported')
      }
    }

    // CLS (Cumulative Layout Shift)
    if ('PerformanceObserver' in window) {
      let clsValue = 0
      const clsObserver = new PerformanceObserver((entryList) => {
        const entries = entryList.getEntries()
        entries.forEach(entry => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value
          }
        })
        this.recordMetric('CLS', clsValue)
      })
      
      try {
        clsObserver.observe({ entryTypes: ['layout-shift'] })
        this.observers.push(clsObserver)
      } catch (e) {
        console.warn('CLS observer not supported')
      }
    }

    // FCP (First Contentful Paint)
    if ('PerformanceObserver' in window) {
      const fcpObserver = new PerformanceObserver((entryList) => {
        const entries = entryList.getEntries()
        entries.forEach(entry => {
          if (entry.name === 'first-contentful-paint') {
            this.recordMetric('FCP', entry.startTime)
          }
        })
      })
      
      try {
        fcpObserver.observe({ entryTypes: ['paint'] })
        this.observers.push(fcpObserver)
      } catch (e) {
        console.warn('FCP observer not supported')
      }
    }
  }

  /**
   * 监控资源加载性能
   */
  observeResourceTiming() {
    if ('PerformanceObserver' in window) {
      const resourceObserver = new PerformanceObserver((entryList) => {
        const entries = entryList.getEntries()
        entries.forEach(entry => {
          // 只监控关键资源
          if (this.isKeyResource(entry.name)) {
            this.recordMetric('resource_load_time', entry.duration, {
              name: entry.name,
              type: entry.initiatorType,
              size: entry.transferSize || 0
            })
          }
        })
      })
      
      try {
        resourceObserver.observe({ entryTypes: ['resource'] })
        this.observers.push(resourceObserver)
      } catch (e) {
        console.warn('Resource observer not supported')
      }
    }
  }

  /**
   * 监控长任务
   */
  observeLongTasks() {
    if ('PerformanceObserver' in window) {
      const longTaskObserver = new PerformanceObserver((entryList) => {
        const entries = entryList.getEntries()
        entries.forEach(entry => {
          this.recordMetric('long_task', entry.duration, {
            startTime: entry.startTime,
            attribution: entry.attribution?.[0]?.name || 'unknown'
          })
        })
      })
      
      try {
        longTaskObserver.observe({ entryTypes: ['longtask'] })
        this.observers.push(longTaskObserver)
      } catch (e) {
        console.warn('Long task observer not supported')
      }
    }
  }

  /**
   * 监控页面可见性变化
   */
  observeVisibilityChange() {
    let visibilityStartTime = Date.now()
    
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        const visibleTime = Date.now() - visibilityStartTime
        this.recordMetric('page_visible_time', visibleTime)
      } else {
        visibilityStartTime = Date.now()
      }
    })
  }

  /**
   * 判断是否为关键资源
   */
  isKeyResource(url) {
    const keyPatterns = [
      /\.js$/,
      /\.css$/,
      /chunk-.*\.js$/,
      /vendor.*\.js$/,
      /app.*\.js$/
    ]
    
    return keyPatterns.some(pattern => pattern.test(url))
  }

  /**
   * 记录性能指标
   */
  recordMetric(name, value, metadata = {}) {
    const metric = {
      name,
      value,
      timestamp: Date.now(),
      url: location.href,
      userAgent: navigator.userAgent,
      ...metadata
    }
    
    this.metrics.set(`${name}_${Date.now()}`, metric)
    
    // 实时发送关键指标
    if (this.isCriticalMetric(name)) {
      this.sendMetric(metric)
    }
  }

  /**
   * 判断是否为关键指标
   */
  isCriticalMetric(name) {
    return ['LCP', 'FID', 'CLS'].includes(name)
  }

  /**
   * 发送指标数据
   */
  sendMetric(metric) {
    // 使用beacon API发送数据，确保页面卸载时也能发送
    if ('sendBeacon' in navigator) {
      const data = JSON.stringify(metric)
      navigator.sendBeacon('/api/metrics', data)
    } else {
      // 降级到fetch
      fetch('/api/metrics', {
        method: 'POST',
        body: JSON.stringify(metric),
        headers: {
          'Content-Type': 'application/json'
        },
        keepalive: true
      }).catch(err => {
        console.warn('Failed to send metric:', err)
      })
    }
  }

  /**
   * 批量发送所有指标
   */
  sendAllMetrics() {
    const allMetrics = Array.from(this.metrics.values())
    if (allMetrics.length === 0) return
    
    const data = JSON.stringify({
      metrics: allMetrics,
      session: this.getSessionId(),
      page: location.href
    })
    
    if ('sendBeacon' in navigator) {
      navigator.sendBeacon('/api/metrics/batch', data)
    }
  }

  /**
   * 获取会话ID
   */
  getSessionId() {
    let sessionId = sessionStorage.getItem('performance_session_id')
    if (!sessionId) {
      sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
      sessionStorage.setItem('performance_session_id', sessionId)
    }
    return sessionId
  }

  /**
   * 设置页面卸载监听
   */
  setupBeforeUnload() {
    window.addEventListener('beforeunload', () => {
      this.sendAllMetrics()
    })
    
    // 页面隐藏时也发送数据
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.sendAllMetrics()
      }
    })
  }

  /**
   * 手动记录支付相关性能指标
   */
  recordPaymentMetric(action, duration, metadata = {}) {
    this.recordMetric(`payment_${action}`, duration, {
      ...metadata,
      category: 'payment'
    })
  }

  /**
   * 记录API调用性能
   */
  recordAPIMetric(url, duration, status, metadata = {}) {
    this.recordMetric('api_call', duration, {
      url,
      status,
      ...metadata,
      category: 'api'
    })
  }

  /**
   * 记录用户交互性能
   */
  recordInteractionMetric(action, duration, metadata = {}) {
    this.recordMetric(`interaction_${action}`, duration, {
      ...metadata,
      category: 'interaction'
    })
  }

  /**
   * 清理资源
   */
  destroy() {
    this.observers.forEach(observer => {
      observer.disconnect()
    })
    this.observers = []
    this.metrics.clear()
  }
}

// 创建全局实例
const performanceMonitor = new PerformanceMonitor()

// 导出实例和类
export default performanceMonitor
export { PerformanceMonitor }
