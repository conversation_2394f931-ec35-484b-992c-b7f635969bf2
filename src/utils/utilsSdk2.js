import device from 'current-device'

export function backAppGame () {
  // 动态创建一个 <a> 标签
  const a = document.createElement('a')
  a.href = device.ios()
    ? window.$gcbk('gameinfo.appGameDeepLinkIos')
    : window.$gcbk('gameinfo.appGameDeepLinkAndroid')
  a.style.display = 'none' // 隐藏标签
  // 将 <a> 标签添加到 document.body 中
  document.body.appendChild(a)
  // 触发点击事件
  a.click()
  // 移除动态创建的 <a> 标签
  document.body.removeChild(a)
}
