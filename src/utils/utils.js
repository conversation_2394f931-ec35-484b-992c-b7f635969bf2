import CryptoJS from 'crypto-js'
export const urlHelper = (search) => {
  search = search || window.location.search.slice(1)
  const urlPrams = {}
  const searchArr = search.split('&')
  for (const value of searchArr.values()) {
    const index = value.indexOf('=')
    const k = value.slice(0, index)
    urlPrams[k] = decodeURIComponent(value.slice(index + 1))
  }
  return urlPrams
}

export const getUrlParams = (function () {
  let urlPrams = {}
  return function () {
    // order页逻辑和主站隔离
    if (location.href.includes('/order')) return {}
    if (JSON.stringify(urlPrams) === '{}') {
      const tempUrl = urlHelper()
      const localParams = JSON.parse(localStorage.getItem('urlParams') || '{}')
      const sessionParams = JSON.parse(sessionStorage.getItem('urlParams') || '{}')

      /* ruapp优先级最高 */
      if (localParams.utm_campaign === 'ruapp' && (window.__GAMENAME && window.__GAMENAME.startsWith('ss'))) tempUrl.utm_campaign = 'ruapp' // ss 特有

      if (location.pathname === '/' || (window.__ROUTERPATH && window.__ROUTERPATH.startsWith(location.pathname))) {
        /* 打开/sdk/pay 视为第一次进入页面 */
        localStorage.setItem('urlParams', JSON.stringify(Object.assign({}, localParams, tempUrl)))
      } else {
        sessionStorage.setItem('urlParams', JSON.stringify(Object.assign({}, sessionParams, tempUrl)))
      }

      urlPrams = Object.assign({}, localParams, sessionParams, tempUrl)
      if ('openid' in tempUrl) localStorage.setItem('openid', tempUrl.openid)
      if (tempUrl.event === 'boon') watchDisplayMode()
    }
    return urlPrams
  }
}())

export const isArZone = key => ['VND'].includes(key)

export const calcDisplayMode = () => {
  const isStandalone = window.matchMedia('(display-mode: standalone)').matches
  if (document.referrer.startsWith('android-app://')) {
    return 'twa'
  } else if (navigator.standalone || isStandalone) {
    return 'standalone'
  }
  return 'browser'
}
export const getPWADisplayMode = calcDisplayMode()
var watchDisplayMode = function () {
  const interval = setInterval(() => {
    if (calcDisplayMode() === 'standalone') {
      console.log('install successful!')
      clearInterval(interval)
      // window.location.reload()
      if (window.$event && window.$event.$emit) window.$event.$emit('installSuccessful')
    }
  }, 3000)
}

export const getPlatform = (() => {
  const ua = navigator.userAgent
  const isIos = ua.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)
  const isAndroid = ua.indexOf('Android') > -1 || ua.indexOf('Adr') > -1
  if (isIos) return 'ios'
  if (isAndroid) return 'android'
  return 'pc'
})()

export function randomString (len) {
  len = len || 18
  const chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz123456789'
  const maxPos = chars.length
  let pwd = ''
  for (let i = 0; i < len; i++) {
    pwd = pwd + chars.charAt(Math.floor(Math.random() * maxPos))
  }
  return pwd + new Date().getTime()
}

export const navToLogin = (l = '', gameId = '') => {
  const lang = (l || '').replace('_', '-')
  const returnUrl = location.origin + location.pathname

  window.location.href = `${process.env.VUE_APP_LOGIN_PAGE_URL}?refer=${encodeURIComponent(returnUrl)}&source_id=1&ng=token&lang=${lang}&gid=${gameId}`
}

export const priceHelper = function (price, currency) {
  price = Number(price.toFixed(4))
  const arr = ['BDT', 'CLP', 'COP', 'CRC', 'DZD', 'HUF', 'IDR', 'INR', 'IQD', 'JPY', 'KES', 'KRW', 'KZT', 'LBP', 'LKR', 'MMK', 'NGN', 'PHP', 'PKR', 'PYG', 'RSD', 'RUB', 'THB', 'TWD', 'TZS', 'VND']
  if (arr.includes(currency)) return Math.ceil(price)
  else return +price.toFixed(2)
}

export const fixToFixed = function () {
  const origin = Number.prototype.toFixed
  // eslint-disable-next-line no-extend-native
  Number.prototype.toFixed = function (n) {
    const number = this
    if (n > 20 || n < 0) throw new RangeError('toFixed() digits argument must be between 0 and 20')
    if (Number.isNaN(number)) throw new TypeError(number + '.toFixed() is not a function')
    // 如果忽略该参数，则默认为 0，进行四舍五入，不包括小数部分
    if (n === undefined || n === 0) return Math.round(number).toString()

    return origin.call(
      Math.round((this + Number.EPSILON) * Math.pow(10, n)) / Math.pow(10, n),
      n
    )
  }
}

export const onceADay = function (storageKey, range = [], useLocalDate = false) {
  const date = new Date(Date.now() + (useLocalDate ? 0 : new Date().getTimezoneOffset() * 60 * 1000))
  const tempDate = `${date.getFullYear()}/${date.getMonth() + 1}/${date.getDate()}`
  const popHistory = JSON.parse(localStorage.getItem(storageKey) || '[]')
  const showPopup = !popHistory.includes(tempDate) && (!range.length || range.includes(tempDate))
  if (showPopup) {
    popHistory.push(tempDate)
    localStorage.setItem(storageKey, JSON.stringify(popHistory))
  }
  return showPopup
}

export const numberFormat = (num) => {
  const digits = 2
  const lookup = [
    { value: 1, symbol: '' },
    { value: 1e3, symbol: 'K' },
    { value: 1e6, symbol: 'M' },
    { value: 1e9, symbol: 'G' },
    { value: 1e12, symbol: 'T' },
    { value: 1e15, symbol: 'P' },
    { value: 1e18, symbol: 'E' }
  ]
  const rx = /\.0+$|(\.[0-9]*[1-9])0+$/
  var item = lookup.slice().reverse().find(function (item) {
    return num >= item.value
  })
  return item ? (num / item.value).toFixed(digits).replace(rx, '$1') + item.symbol : '0'
}

export const isActivityTime = (value) => {
  const { start, end } = value
  const now = new Date().getTime()
  if (start && end) {
    const utcDate1 = new Date(Date.UTC(start.year, start.month - 1, start.day, 0, 0, 0)).getTime()
    const utcDate2 = new Date(Date.UTC(end.year, end.month - 1, end.day, 0, 0, 0)).getTime()
    if (now > utcDate1 && now < utcDate2) {
      return true
    }
    return false
  } else if (start && !end) {
    const utcDate1 = new Date(Date.UTC(start.year, start.month - 1, start.day, 0, 0, 0)).getTime()
    if (now > utcDate1) return true
    return false
  } else {
    const utcDate2 = new Date(Date.UTC(end.year, end.month - 1, end.day, 0, 0, 0)).getTime()
    if (now < utcDate2) {
      return true
    }
    return false
  }
}

export function dealSmDeviceId (cb) {
  let smDeviceId = ''
  let smDeviceIdReady = false
  if (window.SMSdk && window.SMSdk.ready) {
    window.SMSdk.ready(function () {
      smDeviceId = window.SMSdk.getDeviceId ? window.SMSdk.getDeviceId() : smDeviceId

      if (!smDeviceIdReady) {
        smDeviceIdReady = true
        cb && cb(smDeviceId)
      }
    })
  } else {
    console.error('数美 sdk is not ready')
    cb && cb(smDeviceId)
  }
}

export function getBrowserInfo () {
  return {
    windowSize: '05',
    acceptHeader: 'text/html, application/xhtml+xml, application/xml;q=0.9, image/webp, image/apng, *;q=0.8',
    colorDepth: screen.colorDepth,
    screenHeight: document.documentElement.clientHeight || document.body.clientHeight,
    jetLag: new Date().getTimezoneOffset(),
    userAgent: navigator.userAgent,
    screenWidth: document.documentElement.clientWidth || document.body.clientWidth,
    javaEnabled: true,
    javaScriptEnabled: true
  }
}

export function decryptAES (ciphertextStr, secretKey) {
  const url = secretKey
  function deobfuscate (obfuscatedStr) {
    return obfuscatedStr.split(' ').map(code => String.fromCharCode(code - 1)).join('')
  }
  var decrypted = CryptoJS.AES.decrypt(
    ciphertextStr,
    CryptoJS.enc.Utf8.parse(deobfuscate(url)),
    {
      iv: CryptoJS.enc.Utf8.parse(deobfuscate(url).substr(0, 16)),
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    }
  )
  var decryptedStr = decrypted.toString(CryptoJS.enc.Utf8)

  return decryptedStr
}
export function encryptAES (ciphertextStr) {
  const url = '110 106 111 106 38 37 112 49 112 79 79 112 80 91 111 108 36 112 51 114 99 114 68 102 82 67 50 106 89 102 74 83'
  function deobfuscate (obfuscatedStr) {
    return obfuscatedStr.split(' ').map(code => String.fromCharCode(code - 1)).join('')
  }

  const encryptd = CryptoJS.AES.encrypt(
    ciphertextStr,
    CryptoJS.enc.Utf8.parse(deobfuscate(url)),
    {
      iv: CryptoJS.enc.Utf8.parse(deobfuscate(url).substr(0, 16)),
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    }
  )

  return encryptd.toString()
}

export const isWx = (() => {
  var ua = navigator.userAgent.toLowerCase()
  var isWeixin = ua.indexOf('micromessenger') !== -1
  return isWeixin
})()

export async function jumpToUrl (jumpUrl = ''){
  // const urlParams = window._getInitUrlParams()
  const store = (await import('@/store')).default

  if (store.state.userinfo.openid) {
    const contact = jumpUrl.indexOf('?') >= 0 ? '&' : '?'
    jumpUrl += `${contact}openid=${encodeURIComponent(store.state.userinfo.openid)}`
  }
  window.open(jumpUrl, '_blank')
}
