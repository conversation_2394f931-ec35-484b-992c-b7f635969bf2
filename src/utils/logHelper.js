import qs from 'qs'
import { makeUpCommonParams } from '@/server/http'
import store from '@/store'
import { getPlatform, getPWADisplayMode, randomString } from '@/utils/utils'
const gameInfo = store.state.gameinfo
const userinfo = store.state.userinfo
const urlParams = store.state.urlParams

let browserMark = localStorage.getItem('browserMark') || ''
if (!browserMark) {
  browserMark = randomString()
  localStorage.setItem('browserMark', browserMark)
}

function send (logoInfo) {
  const info = {
    app_id: `${store.state.gameinfo.gameProject.split('_')[0]}.global.prod`,
    data_version: '3.0',
    event: 'third_pay',
    event_ts: 1670812301475,
    fpid: userinfo.fpid,
    // ip: '*************',
    ts_pretty: '2022/12/12 02:31:41'
  }
  info.detail = {
    gamecode: window.$gcbk('gameinfo.gameLogCode', '') || gameInfo.gameCode,
    gameid: +gameInfo.gameId,
    pay_channel: 'web',
    tracking_id: urlParams.utm_campaign || '',
    browser_id: browserMark,
    opened_by: getPWADisplayMode === 'standalone' ? `pwa_${getPlatform}` : '',
    ...logoInfo,
    action: logoInfo.event
  }
  if (window.__isPCSDK || window.__IS_CHECKOUT_SDK) info.detail.pay_channel = urlParams.s
  if (info.detail.gamecode === 'DC') {
    info.detail.gamecode = 'dc'
    info.app_id = 'dc.global.prod'
  }
  if (info.detail.gamecode === 'SSV2') info.app_id = 'ss.global.prod'
  const { uid, openid } = userinfo
  if (uid && !logoInfo.uid) info.detail.uid = uid
  if (openid) info.detail.openid = openid

  Object.assign(info, makeUpCommonParams())

  // detail 补充 openid
  if (info.openid && !info.detail.openid) info.detail.openid = info.openid

  const biPath =  window.$gcbk('apiParams.biPath', '/bilog')
  const target = `${process.env['VUE_APP_PREFIX_TOKEN_' + gameInfo.gameCode]}${biPath}`
  if (navigator.sendBeacon) {
    navigator.sendBeacon(target, JSON.stringify(info))
  } else {
    const img = new Image()
    img.src = `${target}?${qs.stringify(info, { allowDots: true })}`
  }
}

// export function logForPageLoad () {
//   const params = {
//     position: 'choose_pay',
//     action: 'load_done'
//   }
//   send(params)
// }
//
// export function logForBtnClick (method) {
//   const params = {
//     position: 'choose_pay',
//     action: 'click_channel',
//     method
//   }
//   send(params)
// }
//
// export function logForConfirmPurchase (method) {
//   const params = {
//     position: 'choose_pay',
//     action: 'click_method',
//     method: method // '选择支付方式时加入该参数（采用选取的支付方式英文代称）'
//   }
//   send(params)
// }
//
// export function logForPayResult (status, orderId, reason, method) {
//   const params = {
//     position: 'pay_end',
//     action: 'judgment',
//     status, // 'success / failed'
//     order_id: orderId, // '订单id，订单唯一识别码'
//     reason, // '余额不足等代指支付失败原因的英文代称 / ',
//     method // '支付方式（采用选取的支付方式英文代称）'
//   }
//   send(params)
// }

// 收银台页面加载
export function logForPageLoad () {
  const param = {
    event: 'load_done'
  }
  send(param)
}

// 点击登录
export function logForClickLogin (uid) {
  const params = {
    event: 'click_login',
    uid
  }
  send(params)
}

// 登录成功
export function logForLoginSuccess () {
  const params = {
    event: 'login_successful',
    level: userinfo.level,
    gameserver_id: userinfo.server,
    name: userinfo.name
  }
  send(params)
}

// 渠道点击
export function logForClickChannel (method) {
  const params = {
    event: 'click_method',
    method
  }
  send(params)
}

// 钻石点击
export function logForClickDiamond (method) {
  const params = {
    event: 'click_product',
    method
  }
  send(params)
}

// 选择优惠券
export function logForClickCoupon (coupon) {
  const params = {
    event: 'select_coupon',
    coupon_id: coupon.coupon_id,
    coupon_description: coupon.type
  }
  send(params)
}

// 请求结果
export function logForPayResult (status, orderId, reason, orderInfo) {
  const params = {
    status, // 'success / failed'
    order_id: orderId, // '订单id，订单唯一识别码'
    reason, // '余额不足等代指支付失败原因的英文代称 / ',
    ...orderInfo
  }
  send(params)
}

export function logForSdk2OpenedSuccess (o = {}) {
  const params = {
    event: 'open_fpstore_checkout',
    ...o
  }
  send(params)
}
