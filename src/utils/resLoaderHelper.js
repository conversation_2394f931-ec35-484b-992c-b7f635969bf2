import Vue from 'vue'
import i18n from '@/utils/i18n'
import getContentByKey from '@/config/resConfig'

window.$gcbk = getContentByKey
Vue.prototype.$gcbk = getContentByKey
Vue.prototype.$vt = (key) => i18n.t(getContentByKey(`langKey.${key}`)) // 变量 $vt
Vue.prototype.$imageLoader = (key, defaultValue) => getContentByKey(`images.${key}`, defaultValue)
Vue.prototype.$idLoader = window.$idLoader = (key, defaultValue) => getContentByKey(`ids.${key}`, defaultValue)
Vue.prototype.$gameName = window.__GAMENAME
