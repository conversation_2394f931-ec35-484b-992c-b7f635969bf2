import CryptoJS from 'crypto-js'

const userKey = CryptoJS.enc.Utf8.parse('84uyzdgah9m#m9x4qzu&ye53')

const ivHex = userKey.clone()
ivHex.sigBytes = 16
ivHex.words.splice(4)

export function GetAesResult(uidplus) {
    return encodeURIComponent(CryptoJS.AES.encrypt(CryptoJS.enc.Utf8.parse(uidplus), userKey, {
        iv: ivHex,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7,
    }).toString())
}