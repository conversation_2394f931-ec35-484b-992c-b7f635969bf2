import Vue from 'vue'

// 尝试导入reporter，如果失败则忽略
let ReportCustom = null
try {
  ReportCustom = require('@ufe/reporter')
} catch (e) {
  console.warn('webReporter: @ufe/reporter not found, reporting disabled')
}

if (process.env.NODE_ENV !== 'development' && ReportCustom) {
  // eslint-disable-next-line no-new
  new ReportCustom({
    domain: 'https://web-monitor.funplus.com',
    appId: window.$idLoader && window.$idLoader('appId'),
    autoErr: true,
    vueError: Vue,
    useBeaconFirst: true,
    extra: {
      appVersion: '0.0.2'
    }
  })
}
