/**
 * API缓存管理器
 * 提供多层缓存策略，优化API调用性能
 */

class APICache {
  constructor() {
    this.memoryCache = new Map()
    this.memoryTTL = new Map()
    this.maxMemorySize = 50 // 最大缓存50个条目
    this.defaultTTL = 5 * 60 * 1000 // 默认5分钟TTL
  }

  /**
   * 生成缓存键
   */
  generateKey(url, params = {}) {
    const sortedParams = Object.keys(params)
      .sort()
      .reduce((result, key) => {
        result[key] = params[key]
        return result
      }, {})
    
    return `${url}:${JSON.stringify(sortedParams)}`
  }

  /**
   * 设置内存缓存
   */
  setMemoryCache(key, data, ttl = this.defaultTTL) {
    // 如果缓存已满，删除最旧的条目
    if (this.memoryCache.size >= this.maxMemorySize) {
      const oldestKey = this.memoryCache.keys().next().value
      this.memoryCache.delete(oldestKey)
      this.memoryTTL.delete(oldestKey)
    }

    this.memoryCache.set(key, data)
    this.memoryTTL.set(key, Date.now() + ttl)
  }

  /**
   * 获取内存缓存
   */
  getMemoryCache(key) {
    const ttl = this.memoryTTL.get(key)
    
    if (!ttl || Date.now() > ttl) {
      this.memoryCache.delete(key)
      this.memoryTTL.delete(key)
      return null
    }
    
    return this.memoryCache.get(key)
  }

  /**
   * 设置本地存储缓存
   */
  setLocalStorageCache(key, data, ttl = this.defaultTTL) {
    try {
      const cacheData = {
        data,
        timestamp: Date.now(),
        ttl
      }
      localStorage.setItem(`api_cache_${key}`, JSON.stringify(cacheData))
    } catch (error) {
      console.warn('Failed to set localStorage cache:', error)
    }
  }

  /**
   * 获取本地存储缓存
   */
  getLocalStorageCache(key) {
    try {
      const cached = localStorage.getItem(`api_cache_${key}`)
      if (!cached) return null

      const cacheData = JSON.parse(cached)
      const { data, timestamp, ttl } = cacheData

      if (Date.now() - timestamp > ttl) {
        localStorage.removeItem(`api_cache_${key}`)
        return null
      }

      return data
    } catch (error) {
      console.warn('Failed to get localStorage cache:', error)
      return null
    }
  }

  /**
   * 设置会话存储缓存
   */
  setSessionCache(key, data) {
    try {
      sessionStorage.setItem(`api_cache_${key}`, JSON.stringify({
        data,
        timestamp: Date.now()
      }))
    } catch (error) {
      console.warn('Failed to set sessionStorage cache:', error)
    }
  }

  /**
   * 获取会话存储缓存
   */
  getSessionCache(key) {
    try {
      const cached = sessionStorage.getItem(`api_cache_${key}`)
      if (!cached) return null

      const { data } = JSON.parse(cached)
      return data
    } catch (error) {
      console.warn('Failed to get sessionStorage cache:', error)
      return null
    }
  }

  /**
   * 获取缓存数据（多层级查找）
   */
  get(url, params = {}, options = {}) {
    const key = this.generateKey(url, params)
    
    // 1. 优先从内存缓存获取
    let data = this.getMemoryCache(key)
    if (data) {
      return Promise.resolve(data)
    }

    // 2. 从会话缓存获取（用于页面刷新后的快速恢复）
    if (options.useSessionCache) {
      data = this.getSessionCache(key)
      if (data) {
        // 同时设置到内存缓存
        this.setMemoryCache(key, data, options.ttl)
        return Promise.resolve(data)
      }
    }

    // 3. 从本地存储获取（用于跨会话缓存）
    if (options.useLocalStorage) {
      data = this.getLocalStorageCache(key)
      if (data) {
        // 同时设置到内存缓存和会话缓存
        this.setMemoryCache(key, data, options.ttl)
        if (options.useSessionCache) {
          this.setSessionCache(key, data)
        }
        return Promise.resolve(data)
      }
    }

    return null
  }

  /**
   * 设置缓存数据
   */
  set(url, params = {}, data, options = {}) {
    const key = this.generateKey(url, params)
    const ttl = options.ttl || this.defaultTTL

    // 设置内存缓存
    this.setMemoryCache(key, data, ttl)

    // 设置会话缓存
    if (options.useSessionCache) {
      this.setSessionCache(key, data)
    }

    // 设置本地存储缓存
    if (options.useLocalStorage) {
      this.setLocalStorageCache(key, data, ttl)
    }
  }

  /**
   * 删除缓存
   */
  delete(url, params = {}) {
    const key = this.generateKey(url, params)
    
    this.memoryCache.delete(key)
    this.memoryTTL.delete(key)
    
    try {
      localStorage.removeItem(`api_cache_${key}`)
      sessionStorage.removeItem(`api_cache_${key}`)
    } catch (error) {
      console.warn('Failed to delete cache:', error)
    }
  }

  /**
   * 清空所有缓存
   */
  clear() {
    this.memoryCache.clear()
    this.memoryTTL.clear()
    
    try {
      // 清空localStorage中的API缓存
      Object.keys(localStorage).forEach(key => {
        if (key.startsWith('api_cache_')) {
          localStorage.removeItem(key)
        }
      })
      
      // 清空sessionStorage中的API缓存
      Object.keys(sessionStorage).forEach(key => {
        if (key.startsWith('api_cache_')) {
          sessionStorage.removeItem(key)
        }
      })
    } catch (error) {
      console.warn('Failed to clear cache:', error)
    }
  }

  /**
   * 获取缓存统计信息
   */
  getStats() {
    return {
      memorySize: this.memoryCache.size,
      maxMemorySize: this.maxMemorySize,
      memoryKeys: Array.from(this.memoryCache.keys())
    }
  }

  /**
   * 预热缓存（预加载常用数据）
   */
  async warmup(requests = []) {
    const promises = requests.map(async ({ url, params, options, fetcher }) => {
      try {
        const cached = this.get(url, params, options)
        if (!cached && fetcher) {
          const data = await fetcher()
          this.set(url, params, data, options)
          return data
        }
        return cached
      } catch (error) {
        console.warn(`Failed to warmup cache for ${url}:`, error)
        return null
      }
    })

    return Promise.allSettled(promises)
  }
}

// 创建全局缓存实例
const apiCache = new APICache()

/**
 * 缓存装饰器函数
 */
export function withCache(options = {}) {
  return function(target, propertyKey, descriptor) {
    const originalMethod = descriptor.value

    descriptor.value = async function(...args) {
      const [url, params] = args
      const cacheKey = apiCache.generateKey(url, params)
      
      // 尝试从缓存获取
      const cached = apiCache.get(url, params, options)
      if (cached) {
        return cached
      }

      // 执行原始方法
      try {
        const result = await originalMethod.apply(this, args)
        
        // 缓存结果
        apiCache.set(url, params, result, options)
        
        return result
      } catch (error) {
        throw error
      }
    }

    return descriptor
  }
}

/**
 * 缓存配置预设
 */
export const CachePresets = {
  // 用户信息缓存（长期缓存）
  USER_INFO: {
    ttl: 30 * 60 * 1000, // 30分钟
    useLocalStorage: true,
    useSessionCache: true
  },
  
  // 商品列表缓存（中期缓存）
  PRODUCT_LIST: {
    ttl: 10 * 60 * 1000, // 10分钟
    useSessionCache: true
  },
  
  // 支付渠道缓存（长期缓存）
  PAYMENT_CHANNELS: {
    ttl: 60 * 60 * 1000, // 1小时
    useLocalStorage: true,
    useSessionCache: true
  },
  
  // 优惠券列表缓存（短期缓存）
  COUPON_LIST: {
    ttl: 5 * 60 * 1000, // 5分钟
    useSessionCache: true
  },
  
  // 汇率信息缓存（长期缓存）
  CURRENCY_RATE: {
    ttl: 24 * 60 * 60 * 1000, // 24小时
    useLocalStorage: true,
    useSessionCache: true
  }
}

export default apiCache
