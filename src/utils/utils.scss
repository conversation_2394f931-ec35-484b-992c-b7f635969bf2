@mixin bgCenter($name, $w,$h) {
  height: $h;
  width: $w;
  background-image: url('~@/assets/'+ $name);
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
}

@mixin bgCenterForKoa($w,$h,$name) {
  @include bgCenter($w, $h, 'koa/#{$name}')
}

@mixin bgCenterForSS($name, $w, $h) {
  @include bgCenter('ss/#{$name}', $w, $h)
}

@mixin bgCenterForKoaIcon($name,$w,$h) {
  height: $h;
  width: $w;
  background-image: url('~@/assets/'+ $name);
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
}


$sizeMap: (
        m: 'mobile',
        p: 'pc'
);

@mixin setContentByBp($size) {
  $transformSize: map-get($sizeMap, $size);
  @if not $transformSize {
    @error 'there is no key named `#{$size}`！'
  }

  @if ($transformSize == 'mobile') {
    @media screen and (max-width: 940px) {
      @content
    }
  }
  @if ($transformSize == 'pc') {
    @media screen and (min-width: 940.9px) {
      @content
    }
  }
}

// 传递变量
@mixin setPropByBp($m: (), $p: ()) {
  @if length($m) > 0 {
    @include setMobileContent() {
      @each $prop, $value in $m {
        @if (type-of($value) == string) {
          #{$prop} : $value
        } @else {
          #{$prop} : $value
        }
      }
    }
  }

  @if length($p) > 0 {
    @include setPcContent() {
      @each $prop, $value in $p {
        @if (type-of($value) == string) {
          #{$prop} : $value
        } @else {
          @if ($prop == 'p') {
            #{$prop} : to-upper-case(#{$value})
          } @else {
            #{$prop} : $value
          }
        }
      }
    }
  }
}

@mixin setMobileContent(){
  @media screen and (max-width: 940px) {
    @content
  }
}
@mixin setPcContent(){
  @media screen and (min-width: 940.9px) {
    @content
  }
}

.click-btn{
  transition: all .1s;
  transform-origin: center center;
  cursor: pointer;

  &:not(.disable):hover{
    transform: scale(.96);
  }
}

@mixin bgCenterForCommon($name, $w,$h) {
  @include bgCenter('common/#{$name}', $w, $h)
}

/* dc */
@mixin bgCenterForDC($name, $w,$h) {
  @include bgCenter('dc/#{$name}', $w, $h)
}
.dc-stroke{
  text-shadow: 1px 1px 1px #000000,
  -1px -1px 1px #000000,
  -1px 1px 1px #000000,
  1px -1px 1px #000000;
}
.dc-btn-decoration{
  border-left: 2px solid black;
  border-right: 2px solid black;
  border-bottom: 2.5px solid black;
  border-top: 2.5px solid black;
  position: relative;
  overflow: hidden;
  background: rgb(53, 121, 209);
  box-shadow: 3px 3px 3px rgba(0,0,0,.6);

  &:after{
    content: '';
    height: 150%;
    width: 20px;
    background: rgba(0,0,0,.3);
    position: absolute;
    right: 0;
    top: 50%;
    transform: translate(15px, -50%) rotate(20deg) ;
  }
}

/* ssv */
@mixin bgCenterForSSV($name, $w,$h) {
  @include bgCenter('ss/#{$name}', $w, $h)
}

/* ss */
@mixin bgCenterForSS($name, $w,$h) {
  @include bgCenter('ss/#{$name}', $w, $h)
}

/* ss */
@mixin bgCenterForSSV2($name, $w,$h) {
  @include bgCenter('ss/ssv2/#{$name}', $w, $h)
}

/* foundation */
@mixin bgCenterForFoundation($name, $w,$h) {
  @include bgCenter('foundation/#{$name}', $w, $h)
}
/* ss */
@mixin bgCenterForSdk2($name, $w,$h) {
  @include bgCenter('sdk/#{$name}', $w, $h)
}

@mixin flexCenter{
  display: flex;
  align-items: center;
  justify-content: center;
}

.common-fade-in[lazy=loading]{
  opacity: 0;
}
.common-fade-in[lazy=loaded]{
  opacity: 1;
  transition: all .4s;
}
img[lazy=loading]{
  opacity: 0;
}
img[lazy=loaded]{
  transition: all .5s;
}
