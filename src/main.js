import Vue from 'vue'
import '@/utils/resLoaderHelper'
import './utils/webReporter'
import './utils/flexible_custom'
import './utils/prepareBasicInfo'
// 性能监控系统
import performanceMonitor from '@/utils/performance-monitor'
// API缓存系统
import apiCache from '@/utils/api-cache'
// 懒加载工具
import { initLazyLoad } from '@/utils/lazy-load'
import App from './App.vue'
import router from './router'
import store from './store'
import i18n from '@/utils/i18n'
import VueLazyload from 'vue-lazyload'
import loading from '@/components/common/loading/loading'
import toast from '@/components/common/toast/toast'
import dialog from '@/components/common/dialog/dialog'
import VueGtag from 'vue-gtag'
import { fixToFixed } from '@/utils/utils'
import device from 'current-device'

// 设备检测
window.isMobile = device.mobile()
fixToFixed()
Vue.config.productionTip = false

// 初始化性能监控
performanceMonitor.recordMetric('app_init_start', 0)

// 初始化Google Analytics
Vue.use(VueGtag, {
  config: { id: window.$idLoader('gid') }
})

// 初始化UI组件
Vue.use(VueLazyload, {
  preLoad: 1.3,
  attempt: 1
})
Vue.use(loading)
Vue.use(toast)
Vue.use(dialog)

// 初始化懒加载系统
initLazyLoad()

// 记录版本号
console.log('Version', '0.2.0', 202507151040)

new Vue({
  router,
  store,
  i18n,
  render: (h) => h(App)
}).$mount('#app')
