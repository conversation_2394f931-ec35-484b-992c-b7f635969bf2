<template>
  <div :class="['shopping-wrapper', $gameName]">
    <sdk2-header></sdk2-header>
    <div class="body-wrapper">
      <sdk2-user-and-game-info></sdk2-user-and-game-info>
      <div class="scroll-wrapper">
        <div class="scroll-content">
          <direct-gift-package style="display: none"></direct-gift-package>
          <sdk2-package-info></sdk2-package-info>
          <coupon-choose v-show="hasCoupon"></coupon-choose>
          <channel-choose></channel-choose>
          <!-- login 放最下面，等上面组件初始化完成 -->
          <login-module v-show="false"></login-module>
          <sdk2-tip></sdk2-tip>
        </div>
      </div>
    </div>
    <checkout-footer :style="{ 'z-index': showCouponPop ? -1 : 1 }" @purchaseGoods="judgeRisk()" :request-loading="requestLoading"></checkout-footer>
  </div>
</template>

<script>
import LoginModule from '@/components/LoginModule'
import ChannelChoose from '@/components/ChannelChoose'
import CouponChoose from '@/components/coupon/CouponChoose'
import DirectGiftPackage from '@/components/product/DirectGiftPackage'
import CheckoutFooter from '@/components/mobile/CheckoutFooterTax.vue'
import PayMixin from './PayMixin'
import Sdk2Header from '@/components/sdk/sdk2Header.vue'
import sdk2UserAndGameInfo from '@/components/sdk/sdk2UserAndGameInfo.vue'
import Sdk2PackageInfo from '@/components/sdk/sdk2PackageInfo.vue'
import Sdk2Tip from '@/components/sdk/sdk2Tip.vue'
import { mapState } from 'vuex'
import { logForSdk2OpenedSuccess } from '@/utils/logHelper'

export default {
  name: 'Pay',
  mixins: [PayMixin],
  components: {
    Sdk2Tip,
    Sdk2PackageInfo,
    sdk2UserAndGameInfo,
    Sdk2Header,
    CheckoutFooter,
    CouponChoose,
    ChannelChoose,
    LoginModule,
    DirectGiftPackage
  },
  computed: {
    ...mapState('formdata', ['chosenCoupon']),
    ...mapState(['urlParams'])
  },
  data () {
    return {
      hasCoupon: false,
      showCouponPop: false
    }
  },
  created () {
    this.$root.$on('updateSdk2CouponList', (couponList) => {
      if (this.chosenCoupon.type && this.chosenCoupon.type.includes('direct_first_pay')) {
        this.hasCoupon = false
        return
      }
      this.hasCoupon = couponList.length > 0
    })
    this.$root.$on('showCouponPop', (value) => {
      this.showCouponPop = value
    })

    this.$root.$on('loginEnd', (state) => {
      const params = {}
      if (this.urlParams.oid) params.oid = this.urlParams.oid
      if (state) logForSdk2OpenedSuccess(params)
    })
  }
}
</script>

<style scoped lang="scss">
@import "~@/utils/utils.scss";
.shopping-wrapper {
  width: 100%;
  box-sizing: border-box;
  margin: 0 auto;
  text-align: left;

  .body-wrapper{
    width: 100%;
    height: 0;
    flex-grow: 1;
    background: #FF9744;
    padding-top: 80px;
    position: relative;

    .scroll-wrapper{
      height: 100%;
      border-top-right-radius: 30px;
      border-top-left-radius: 30px;
      padding-top: 74px;
      overflow: hidden;
      background: white;

      .scroll-content{
        height: 100%;
        overflow-y: scroll;
      }
    }
  }
  .checkout-footer-wrapper{
    width: 100%;
    position: relative;
  }
}
@include setMobileContent{
  .shopping-wrapper{
    @include flexCenter;
    flex-direction: column;
    height: 100%;
  }
}
</style>
