<template>
  <div class="payment-callback-page">
    <template v-if="status === 'completed'">
      <div class="info-wrapper">
        <div :class="['image','image-status__'+status]"></div>
        <section>
          <div class="title-desc">{{ $t(title[status]) }}</div>
          <div class="command">{{ $t(tips[status]) }}</div>
        </section>
      </div>
    </template>
    <template v-else-if="status === 'fail'">
      <div class="info-wrapper">
        <div :class="['image','image-status__'+status]"></div>
        <section>
          <div class="title-desc">{{ $t(title[status]) }}</div>
          <div class="command">{{ $t(tips[status]) }}</div>
        </section>
      </div>
    </template>
    <template v-else-if="status === 'pending'">
      <div class="info-wrapper" style="flex-direction: column">
        <div :class="['image','image-status__'+status]"></div>
        <section>
          <div class="title-desc">{{ $t('cb_page_pending_desc') }}</div>
          <div class="command">{{ $t('cb_page_pending_tips') }}</div>
        </section>
      </div>
    </template>
    <template v-else>Configuration Error!</template>
  </div>
</template>

<script>
import { getTokenOrderDetails } from '@/server'

export default {
  name: 'PaymentCallback',
  data () {
    return {
      status: this.$route.path.replace('/common/', ''),
      title: { completed: 'cb_pay_succeed', fail: 'cb_page_title_err' },
      tips: { completed: 'cb_view_tips', fail: 'cb_view_err_tips' },

      // pending
      interval: '',
      timeStop: false
    }
  },
  methods: {
    clearInterval () {
      if (this.interval) {
        clearInterval(this.interval)
        this.interval = ''
      }
    },
    getPaymentStatus () {
      const query = this.$route.query
      const transactionId = query.foreignInvoice || query.orderId || query.OrderId
      const params = { transaction_id: transactionId, hideErrToast: true }

      getTokenOrderDetails(params)
        .then(res => {
          if (res.code === 0) {
            this.$router.replace('/completed')
            this.clearInterval()
          }
        })
    },
    adapterStatus () {
      const map = { fail: 2, completed: 1, pending: 0 }
      return map[this.status]
    },
    backGame () {
      const flag = location.href.includes('adyen') || location.href.includes('airwallext')
      this.$router.replace('/')
      if (flag) setTimeout(() => window.location.reload(), 300)
      // if (this.isCommonMode) exeCommand('shop_back_game', { state: this.adapterStatus.bind(this)() })
      // else {
      //   try {
      //     const game = this.$store.state.urlParams.g.split('_')
      //     window.location.href = `${location.origin}/${game[0]}`
      //   } catch (e) {
      //     window.location.href = location.origin
      //   }
      // }
    },
    navTo (aim) {
      // logOnBtnClick(aim)
      this.$router.replace(aim)
        .then(res => {
          if (res.fullPath.includes('/gallery')) window.location.reload()
        })
    },
    judgeStatus () {
      if (this.interval) {
        clearInterval(this.interval)
        this.interval = ''
      }
      this.$root.$emit('showPop', 'CallbackPendingTips')
    }
  },
  created () {
    window.fetchOrderStatus = this.adapterStatus.bind(this)
    if (this.status === 'pending') {
      this.getPaymentStatus()
      this.interval = setInterval(() => {
        this.getPaymentStatus()
      }, 2000)

      setTimeout(() => {
        this.clearInterval()
        this.timeStop = true
      }, 60000)
    }
  },
  beforeDestroy () {
    this.clearInterval()
  }
}
</script>

<style scoped lang="scss">
@import "~@/utils/utils.scss";

.payment-callback-page {
  box-sizing: border-box;
  font-weight: 500;
  color: #111111;
  min-height: 100%;

  .info-wrapper{
    display: flex;
    align-items: center;
    justify-content: center;
    @media screen and (max-width: 940px) {
      margin-top: 262px;
    }
    @media screen and (min-width: 940.9px) {
      margin-top: 106px;
    }

    .image{
      background-position: center center;
      background-size: 96% 96%;
      background-repeat: no-repeat;
      @media screen and (max-width: 940px) {
        height: 75px;
        width: 75px;
      }
      @media screen and (min-width: 940.9px) {
        height: 50px;
        width: 50px;
      }
      &.image-status__pending {
        background-image: url('~@/assets/koa/cb/cb-pending-image-status.png');
      }
      &.image-status__fail {
        background-image: url('~@/assets/koa/cb/cb-fail-image-status.png');
      }
      &.image-status__completed {
        background-image: url('~@/assets/koa/cb/cb-completed-image-status.png');
      }
    }

    section{
      text-align: left;
      @media screen and (max-width: 940px) {
        margin-left: 25px;
      }
      @media screen and (min-width: 940.9px) {
        margin-left: 18px;
      }

      .title-desc {
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #FFFFFF;

        @media screen and (max-width: 940px) {
          line-height: 56px;
          font-size: 40px;
        }
        @media screen and (min-width: 940.9px) {
          line-height: 33px;
          font-size: 24px;
        }
      }

      .command {
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #8C8C8C;

        @media screen and (max-width: 940px) {
          font-size: 24px;
        }
        @media screen and (min-width: 940.9px) {
          font-size: 14px;
        }
      }
    }

    .btn-wrapper {
      display: flex;
      align-items: center;
      justify-content: center;

      @media screen and (max-width: 940px) {
        margin-top: 77px;
      }
      @media screen and (min-width: 940.9px) {
        margin-top: 107px;
      }

      .btn-status {
        font-weight: 400;
        display: flex;
        align-items: center;
        justify-content: center;

        box-sizing: border-box;
        white-space: nowrap;

        @media screen and (max-width: 940px) {
          font-size: 30px;
          padding: 0 60px;
          border-radius: 8px;
          height: 70px;
          max-width: 37vw;
        }
        @media screen and (min-width: 940.9px) {
          font-size: 22px;
          padding: 0 54px;
          border-radius: 8px;
          height: 52px;
        }

        &.btn-status-not-pay {
          color: #F05805;
          border: 1PX solid #F05805;
        }

        &.btn-status-has-pay {
          color: white;
          background-color: #F05805;
          @media screen and (max-width: 940px) {
            margin-left: 65px;
          }
          @media screen and (min-width: 940.9px) {
            margin-left: 67px;
          }
        }
      }
    }
  }

  .btn-back{
    display: inline-block;
    cursor: pointer;
    background: #FF5E0F;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #FFFFFF;

    @media screen and (max-width: 940px) {
      padding: 0 60px;
      height: 70px;
      font-size: 30px;
      line-height: 70px;
      margin-top: 80px;
      border-radius: 8px;
    }
    @media screen and (min-width: 940.9px) {
      padding: 0 60px;
      height: 52px;
      font-size: 22px;
      line-height: 52px;
      margin-top: 107px;
      border-radius: 8px;
    }
  }
}
</style>
