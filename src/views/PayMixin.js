import { dealSmDeviceId, getBrowserInfo, getPlatform, getPWADisplayMode } from '@/utils/utils'
import UAParser from 'ua-parser-js'
import { placeOrderCard, placeOrderToken } from '@/server'
import { logForPayResult } from '@/utils/logHelper'
import { mapState } from 'vuex'

export default {
  methods: {
    customGetSmDeviceId (cb) {
      if (typeof cb !== 'function') return console.error('customGetSmDeviceId cb 必须为函数！')

      new Promise((resolve, reject) => {
        this.$loading.show()

        const timer = setTimeout(() => resolve(''), 2500)
        dealSmDeviceId(function (deviceId) {
          timer && clearTimeout(timer)
          resolve(deviceId)
        })
      })
        .then(deviceId => {
          if (!deviceId) console.error('dealSmDeviceId 失败！')
          cb(deviceId)
        })
        .finally(() => this.$loading.hide())
    },
    purchaseGoodsWithDeviceId (backup, deviceId) {
      const {
        channel_id: channelId,
        channel_name: channelName,
        sub_channel_id: subChannelId
      } = this.chosenChannel

      const chosenDiamond = this.chosenDiamond
      const params = {
        // group_id: this.urlParams.gid,
        // sku_id: this.urlParams.sk,
        name: this.$vt('tokenName'),
        product_id: chosenDiamond.product_id,
        price: +(chosenDiamond.nowPriceWithTaxAndExtra || chosenDiamond.price),
        // sku_source: ['game', 'mall_center'][+this.urlParams.cms],
        channel: channelName,
        channel_id: channelId,
        sub_channel_id: subChannelId,
        // through_cargo: this.urlParams.tc,
        return_url: location.origin, // payment 根据此参数来跳转
        fp_device_id: deviceId
      }
      if (this.$router.options.base !== '/') {
        params.return_url += `/${this.$router.options.base.replace(/\//g, '')}`
        if (this.IS_CHECKOUT_SDK) {
          let returnUrl = location.origin + window.__ROUTERPATH
          if (returnUrl.endsWith('/')) returnUrl = returnUrl.slice(0, -1)
          params.return_url = returnUrl
        }
      }

      if (this.$gameName === 'aof' || this.$gameName === 'rom') {
        params.ext_info = JSON.stringify({
          project_code: 'koa_aof_web'
        })
      }

      const { type, chosenNum } = this.chosenDiamond
      if (type === 2) params.custom_multiple = chosenNum

      if (backup) params.backup = 1

      let utmCampaign = this.$store.state.urlParams.utm_campaign
      if (utmCampaign) {
        if (utmCampaign.includes('?')) {
          const index = utmCampaign.indexOf('?')
          utmCampaign = utmCampaign.slice(0, index)
        }
        params.tracking_id = utmCampaign
      }

      if (getPWADisplayMode === 'standalone') params.tracking_id = `pwa_${getPlatform}`
      params.browser_id = localStorage.getItem('browserMark') || ''

      // 其他额外参数
      const ref = new UAParser()
      params.browser_info = {
        terminalType: window.innerWidth > 1200 ? 'pc' : 'mobile',
        osType: ref.getOS().name,
        ...getBrowserInfo()
      }

      const FinalPriceState = this.$store.getters['formdata/FinalPriceState']
      const chosenCoupon = this.chosenCoupon
      switch (FinalPriceState.feType) {
        case 'first_pay': case 'direct_first_pay': {
          params.act_type = 'first_pay'
          params.discount = chosenCoupon.discount
          params.discount_price = chosenCoupon.discount_price
          break
        }
        case 'first_pay_rebate': case 'direct_first_pay_rebate': {
          params.act_type = 'first_pay_rebate'
          params.discount = chosenCoupon.discount
          params.discount_price = chosenCoupon.discount_price

          if (chosenCoupon.act_type) params.act_type = chosenCoupon.act_type
          break
        }
        case 'discount_coupon': {
          params.act_type = 'coupon'
          params.discount = chosenCoupon.discount
          params.discount_price = chosenCoupon.discount_price
          params.coupon_id = chosenCoupon.coupon_id
          break
        }
        case 'cash_coupon': {
          params.act_type = 'deduct'
          params.discount = chosenCoupon.deduct_price
          params.discount_price = chosenCoupon.price
          params.coupon_id = chosenCoupon.coupon_id
          break
        }
        case 'rebate_coupon': {
          params.act_type = 'rebate'
          params.discount = chosenCoupon.discount
          params.discount_price = chosenCoupon.discount_price
          params.coupon_id = chosenCoupon.coupon_id
          break
        }
        /* 固定折扣 */
        case 'fixed_discount_coupon': case 'direct_fixed_discount': {
          const defaultDiscountInfo = this.$store.state.formdata.defaultDiscountInfo
          params.act_type = 'fixed_discount'
          params.discount = defaultDiscountInfo.discount
          params.discount_price = defaultDiscountInfo.discount_price
          break
        }
        case 'fixed_rebate': case 'direct_fixed_rebate': {
          params.act_type = 'fixed_rebate'
          break
        }
        case 'fixed_dynamic_rebate': {
          params.act_type = 'product_fixed_rebate'
          break
        }
        default: {
          // 兜底折扣
          const type = this.$store.state.functionSwitch.fixedDiscountType
          if (type && !params.act_type) {
            params.act_type = type
          }

          // 兼容ssv 第一个档位没有返钻、mycard 没有档位
          const isMinimumDiamondId = this.$store.state.functionSwitch.smallDiamondDoubleDiscount && (chosenDiamond.product_id === this.$gcbk('ids.minimumDiamondId'))
          const isNotRebate = isMinimumDiamondId || this.$store.getters['formdata/TWMyCard']
          if (isNotRebate) delete params.act_type
        }
      }

      /* Xsolla Partner network 活动 */
      // const tck = this.urlParams.tck || ''
      // if (tck.startsWith('x@')) {
      //   params.tracking_id = tck.replace('x@', '')
      //   params.source = 'pc_xsolla'
      // }

      // payermax 灰度
      const chosenChannel = this.chosenChannel
      const payermaxKey = `${chosenChannel.channel_id}_${chosenChannel.channel_name}_${chosenChannel.sub_channel_id}_dropin`
      if (this.$store.state.vb.builtInCashier && payermaxKey === 'payermax_A34_A34_dropin') {
        params.order_type = 'drop_in'
      }

      /* 支持直购礼包 */
      sessionStorage.removeItem('goodsName')
      if (this.IS_CHECKOUT_SDK) {
        const urlParams = this.$store.state.urlParams
        const tc = JSON.parse(urlParams.tc || '{}')
        if (FinalPriceState.sdkType) params.act_type = FinalPriceState.sdkType
        params.package_id = tc.package_id
        params.name = params.package_name = tc.product_name
        params.game_order_id = urlParams.oid
        sessionStorage.setItem('goodsName', tc.product_name)
      }
      if (this.IS_CHECKOUT_SDK_V2) {
        const calcSdk2Info = window._calState()
        if (calcSdk2Info.type) params.act_type = calcSdk2Info.type
        if (!params.name) {
          params.name = window.defaultPackageName
          sessionStorage.setItem('goodsName', window.defaultPackageName)
        }
      }

      const state = this.$store.state
      const orderInfo = {
        method: `${channelName}|${channelId}|${subChannelId}`,
        amount: '', // 定价金额
        country: state.country, // 国家
        currency: state.currency, // 当地货币
        product_info: params.product_id, // 购买的商品id信息集
        event: 'pay_completed', // 支付完成
        revenue: params.price // 支付的当地货币金额
      }

      this.$loading.show()
      this.requestLoading = true

      new Promise(resolve => {
        if (this.$store.getters['formdata/TWMyCard']) return resolve(placeOrderCard(params))
        resolve(placeOrderToken(params))
      })
        .then(res => {
          const { data, code, message } = res
          switch (code) {
            case 0: {
              new Promise((resolve, reject) => {
                if ('coin_debt' in data) {
                  this.$root.$emit('showPop', 'ArrearsReminder', { debt: data.coin_debt || 0 })
                  this.$root.$once('arrearsReminderResult', result => {
                    if (result) resolve(1)
                    else reject(Error('cancel pop!'))
                  })
                } else {
                  resolve(2)
                }
              })
                .then(res => {
                  logForPayResult('success', data.order_id, '-', orderInfo)
                  if (typeof data.pay_url === 'string') {
                    const url = new URL(data.pay_url)
                    const urlParams = new URLSearchParams(url.search)

                    sessionStorage.setItem('3zRtY8vXwN', JSON.stringify(data))
                    sessionStorage.removeItem('7x9FkL2pQm')
                    if (data.pay_url.includes('pingpong') && urlParams.get('window_type') !== 'jump_url') {
                      const ppParams = {
                        ppToken: urlParams.get('token'),
                        coinNums: data.coin_recv,
                        currency: data.currency,
                        currency_symbol: data.currency_symbol,
                        amount: data.price
                      }

                      if (res === 1) ppParams.inDebt = true
                      sessionStorage.setItem('ppParams', JSON.stringify(ppParams))
                      this.$router.push('/pp')
                    } else {
                      if (urlParams.get('window_type') === 'jump_url') {
                        location.href = data.pay_url.replace('&window_type=jump_url', '').replace('?window_type=jump_url', '')
                        return null
                      }
                      if (data.open_with_new_window) return window.open(data.pay_url, '_blank')
                      location.href = data.pay_url
                    }
                  } else {
                    data.pay_url.coinNums = data.coin_recv
                    data.pay_url.currency_symbol = data.currency_symbol
                    data.pay_url.host = data.payment_host
                    data.pay_url.order_id = data.payment_order_id
                    data.pay_url.out_trade_no = data.order_id

                    sessionStorage.setItem('id_sign', data.payment_order_id_sign) // 临时数据
                    sessionStorage.setItem('url', data.payment_host) // 临时数据

                    if (res === 1) data.pay_url.inDebt = true

                    if (data.pay_url.channel === 'payermax') {
                      data.pay_url.payment_order_id = data.payment_order_id
                      data.pay_url.name = data.name
                    }
                    sessionStorage.setItem('params', JSON.stringify(data.pay_url))

                    if (data.pay_url.channel === 'payermax') {
                      this.$router.push('/pm')
                    } else if (data.pay_url.client_secret) {
                      this.$router.push('/aw')
                    } else if (data.pay_url.store_card_url) {
                      this.$router.push('/cko')
                    } else if (data.pay_url.stripe_client_secret) {
                      this.$router.push('/sp')
                    } else {
                      this.$router.push('/ad')
                    }
                  }
                })
                .catch(err => {
                  console.log(err.message)
                })
              break
            }
            case 1003: {
              const errTipsList = {
                1: this.$t('illegalGift'),
                2: this.$t('expiredPackage'),
                3: this.$t('InventoryShortage')
              }
              this.$toast.err(errTipsList[data.check_status])

              logForPayResult('failed', '-', errTipsList[data.check_status], orderInfo)
              break
            }
            default: {
              this.$toast.err(this.$t('shop_fail'))
              throw Error(message)
            }
          }
        })
        .catch(err => {
          this.requestLoading = false
          logForPayResult('failed', '-', err, orderInfo)
          console.error(`coinPlaceOrder下单失败：${err.message}`)
        })
        .finally(() => {
          this.requestLoading = false
          this.$loading.hide()
        })
    },
    async purchaseGoods (backup) {
      if (this.requestLoading || this.$store.getters['riskPolicy/forbiddenAccess']) return null
      if (!this.isLogin) return this.$root.$emit('ClickPayButNotLogin')
      // 未同意隐私协议
      if (!this.$store.state.agreePrivacyPolicy) return this.$root.$emit('showPop', 'PrivacyPolicy')
      // 德国区 需要同意隐私协议
      if (window.__needDEPop) {
        const result = await new Promise((resolve, reject) => {
          const payload = {
            ok: () => resolve(1),
            no: () => resolve(0)
          }
          this.$root.$emit('showPop', 'privateConfirmPop', payload)
        })
        if (!result) return null
      }
      if (JSON.stringify(this.chosenChannel) === '{}') return this.$toast.err(this.$t('ModalTIpsShopBeforeChooseChannel'))
      const me = this
      this.customGetSmDeviceId(function (deviceId) {
        me.purchaseGoodsWithDeviceId(backup, deviceId)
      })
    },
    judgeRisk () {
      const { channel_id: channelId } = this.chosenChannel
      const index = this.$store.getters['riskPolicy/showTipsWhenSomeChannel'].indexOf(channelId)
      if (index !== -1) {
        const key = `use_${this.$store.getters['riskPolicy/showTipsWhenSomeChannel'][index]}`
        return this.$root.$emit('showPop', 'RiskControlPolicy', { key, cb: this.purchaseGoods })
      }

      this.purchaseGoods()
    }
  },
  computed: {
    ...mapState('formdata', ['chosenChannel', 'chosenDiamond', 'chosenCoupon', 'vip']),
    ...mapState(['isPc', 'isMobile', 'IS_CHECKOUT_SDK', 'IS_CHECKOUT_SDK_V2']),
    ...mapState('userinfo', ['isLogin']),
    ...mapState('gameinfo', ['gameCode', 'isKOA', 'isSS']),
    ...mapState('functionSwitch', ['showMobilePolicy', 'boon'])
  },
  data () {
    return {
      requestLoading: false
    }
  },
  created () {
    this.$root.$on('adyenInitError', () => this.purchaseGoods(1))

    location.search && history.pushState({}, '', location.pathname)
  }
}
