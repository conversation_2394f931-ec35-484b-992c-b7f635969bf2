<script>
import ChannelLogo from '@/views/paymethod/channelLogo.vue'
import ChannelOrder from '@/views/paymethod/channelOrder.vue'
import ChannelWrapper from '@/views/paymethod/channelWrapper.vue'
import { mapState } from 'vuex'

import { service } from '../../server/http'

const stripeCdnPath = 'https://js.stripe.com/v3/'

export default {
  name: 'stripe',
  components: { ChannelWrapper, ChannelOrder, ChannelLogo },
  computed: {
    ...mapState(['isPc', 'isMobile'])
  },
  data() {
    return {
      stripe: '',
      elements: '',
      initParams: {},
      paramIntent: ''
    }
  },
  methods: {
    loadScript() {
      const params = new URLSearchParams(window.location.search)
      // 是否为重定向回来
      this.paramIntent = params.get('payment_intent_client_secret')
      
      const script = document.createElement('script')
      script.src = stripeCdnPath
      script.onload = this.paramIntent ? this.redirect : this.initForm
      document.body.appendChild(script)
    },
    async initForm() {
      const stripe = window.Stripe(this.initParams.pub_secret_key)
      const options = {
        clientSecret: this.initParams.stripe_client_secret,
        customerSessionClientSecret: this.initParams.custom_client_secret,
        appearance: {
          // theme: 'flat',
          variables: {
            colorPrimary: 'black',
            borderRadius: '10px'
          },
          rules: {
            '.Input': {
              border: '1px solid #b9c4c9'
            },
            '.Input:hover': {
              border: '1px solid #99a3ad'
            },
            '.Input:focus': {
              border: '1px solid #0066ff',
              boxShadow: '0 0 0 2px #99c2ff'
            },
            '.CheckboxInput:hover': {
              border: '1px solid #99a3ad'
            },
            '.CheckboxInput:focus': {
              border: '1px solid #0066ff',
              boxShadow: '0 0 0 2px #99c2ff'
            }
          }
        }
      }

      const elements = stripe.elements(options)
      const paymentElementOptions = {
        layout: {
          type: 'accordion',
          defaultCollapsed: false
        },
        fields: {
          billingDetails: {
            address: 'never'
          }
        }
      }
      const paymentElement = elements.create('payment', paymentElementOptions)
      paymentElement.on('loaderror', this.onError)
      paymentElement.mount('#payment-element')

      this.stripe = stripe
      this.elements = elements
    },
    onError(error) {
      this.$toast.err(this.$t('cb_page_pending_desc'))
      this.$router.go(-1)
      console.error(`stripe init error: ${JSON.stringify(error)}`)
      setTimeout(() => this.$root.$emit('adyenInitError'), 200)
    },
    async prefetchValidation(channel, ctoken) {
      const { code } = await service.post(`${this.initParams.host}/api/payment/pay_risk_before`, {
        order_id: this.initParams.order_id,
        out_trade_no: this.initParams.out_trade_no,
        channel,
        ctoken
      })
      return code
    },
    async onSubmit() {
      const { error: submitError } = await this.elements.submit()
      if (submitError) return

      const { error, confirmationToken } = await this.stripe.createConfirmationToken({
        elements: this.elements,
        params: {
          payment_method_data: {
            billing_details: {
              address: {
                line1: this.$store.state.country,
                line2: this.$store.state.country,
                country: this.$store.state.country,
                state: this.$store.state.country,
                city: this.$store.state.country,
                postal_code: this.$store.state.zipCode
              }
            }
          }
        }
      })
      if (error) {
        console.error(`stripe create token error: ${JSON.stringify(error)}`)
        return this.$toast.err(this.$t('cb_page_title_err'))
      }
      const code = await this.prefetchValidation('stripe', confirmationToken.id)
      if (code === 120012) {
        this.$toast.err(this.$t('prefetch-safety-error').replace(/<br\/>/g, ''))
        sessionStorage.removeItem('params')
        setTimeout(() => this.$router.replace('/'), 500)
        return 
      } else if (code) {
        return this.$toast.err(this.$t('cb_page_title_err'))
      }

      this.stripe.confirmPayment({
        elements: this.elements,
        confirmParams: {
          return_url: location.href,
          payment_method_data: {
            billing_details: {
              address: {
                line1: this.$store.state.country,
                line2: this.$store.state.country,
                country: this.$store.state.country,
                state: this.$store.state.country,
                city: this.$store.state.country,
                postal_code: this.$store.state.zipCode
              }
            }
          }
        }
      }).then((result) => {
        /**
         * generic_decline
         * insufficient_funds
         * incorrect_zip
         * incorrect_cvc
         * invalid_cvc
         * invalid_expiry_month
         * invalid_expiry_year
         * expired_card
         * fraudulent
         * lost_card
         * stolen_card
         * card_velocity_exceeded
         */
        if (result.error) {
          console.error(`stripe payment error: ${JSON.stringify(result.error)}`)
          this.$toast.err(result.error.message)
        }
      })
    },
    /* 根据支付结果 重定向 */
    async redirect() {
      const localParams = JSON.parse(sessionStorage.getItem('params') || '{}')
      const stripe = window.Stripe(localParams.pub_secret_key)
      
      const urlParams = new URLSearchParams(window.location.search)
      const clientSecret = urlParams.get('payment_intent_client_secret')

      stripe.retrievePaymentIntent(clientSecret).then(({ paymentIntent }) => {
        switch (paymentIntent.status) {
          case 'succeeded':
            this.$router.replace('/completed?rf=1')
            break
          case 'processing':
            this.$router.replace('/pending?rf=1') // todo 什么时候会 pending
            break
          case 'requires_payment_method':
            this.$router.replace('/fail')
            break
          default:
            this.$router.replace('/fail')
            break
        }
      }).catch((error) => {
        console.error(`stripe retrieve error: ${JSON.stringify(error)}`)
        const order_id = this.initParams.order_id
        location.href = `${sessionStorage.getItem('url')}/api/payment/result?channel=stripe&app_id=8519&OrderId=${order_id}`
      })
    }
  },
  created() {
    this.initParams = JSON.parse(sessionStorage.getItem('params') || '{}')
    this.loadScript()
  },
  beforeDestroy() {
    /* 销毁 */
    const srcToRemove = stripeCdnPath
    const scripts = document.getElementsByTagName('script')
    const scriptsArray = Array.prototype.slice.call(scripts)
    scriptsArray.forEach(function (script) {
      if (script.src === srcToRemove) {
        script.parentNode.removeChild(script)
      }
    })
  }
}
</script>

<template>
  <div class="stripe-page-wrapper" v-show="!paramIntent">
    <channel-logo v-if="isPc"></channel-logo>
    <div class="content-wrapper">
      <channel-order :coin="initParams.coinNums" :currency="initParams.currency_symbol" :amount="initParams.amount"></channel-order>
      <channel-wrapper>
        <section style="font-size: 15px;text-align: left" class="stripe-wrapper">
          <div class="inner-wrapper">
            <form id="payment-form">
              <div id="payment-element"></div>
              <button id="submit" class="stripe-submit" @click.prevent="onSubmit">
                <img src="https://kg-web-cdn.akamaized.net/prod/web-pay-unique/dist_online/static/img/lock.b2cb557c.svg" aria-hidden="true">
                Pay {{ initParams.currency_symbol }}{{ initParams.amount }}
              </button>
            </form>
          </div>
        </section>
      </channel-wrapper>
    </div>
    <channel-logo v-if="isMobile"></channel-logo>
  </div>
</template>

<style scoped lang="scss">
.stripe-page-wrapper {
  background-color: rgb(240, 242, 245);
  overflow-y: scroll;
  height: 100%;
  width: 100%;

  .content-wrapper {
    .stripe-wrapper {
      height: 100%;
      width: 100%;
      box-sizing: border-box;
      overflow-y: auto;
      background-color: rgb(240, 242, 245);

      .inner-wrapper {
        max-width: 1200PX;
        margin: 0 auto;

        .stripe-submit {
          background: #00112c;
          border: 0;
          border-radius: 8PX;
          color: #fff;
          cursor: pointer;
          font-size: 1em;
          font-weight: 500;
          height: 48PX;
          margin: 20px 0 0 0;
          padding: 15px;
          text-decoration: none;
          transition: background .3s ease-out, box-shadow .3s ease-out;
          width: 100%;

          &:hover {
            background: #203248;
          }
        }
      }
    }
  }
}

@media screen and (min-width: 1200PX) {
  .stripe-page-wrapper {
    .content-wrapper {
      display: flex;
      flex-direction: row-reverse;
      align-items: flex-start;
      max-width: 1200PX;
      margin: 30px auto 0;
    }
  }
}
</style>
