<template>
  <div class="pingpong-page-wrapper">
    <channel-logo v-if="isPc"></channel-logo>
    <div class="content-wrapper">
      <channel-order :coin="initParams.coinNums" :currency="initParams.currency_symbol" :amount="initParams.amount" :in-debt="initParams.inDebt"></channel-order>
      <channel-wrapper>
        <section class="pingpong-wrapper">
          <div class="inner-wrapper">
            <pp-funplus-checkout :savepay="savePay" :accessToken="initParams.ppToken"></pp-funplus-checkout>
          </div>
        </section>
      </channel-wrapper>
    </div>
    <channel-logo v-if="isMobile"></channel-logo>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import ChannelOrder from '@/views/paymethod/channelOrder'
import ChannelWrapper from '@/views/paymethod/channelWrapper'
import ChannelLogo from '@/views/paymethod/channelLogo'
import alertError from './alertError'

export default {
  name: 'pingpong',
  components: { ChannelLogo, ChannelWrapper, ChannelOrder },
  mixins: [alertError],
  computed: {
    ...mapState(['isPc', 'isMobile']),
    savePay () {
      return this.$store.state.functionSwitch.ckoCheckedByDefault ? 'Y' : 'N'
    }
  },
  data () {
    return {
      initParams: {}
    }
  },
  methods: {
    loadPingpongScript () {
      const scriptUrl = process.env.VUE_APP_PROD_ENV === 'ONLINE'
        ? 'https://payssr-cdn.pingpongx.com/production-fra/acquirer-checkout-funplus/pp-funplus-checkout.js'
        : 'https://pay-cdn.pingpongx.com/production-fra/static/pp-funplus-checkout/sandbox/pp-funplus-checkout.js'
      const script = document.createElement('script')
      script.src = scriptUrl
      script.type = 'module'
      script.onload = this.onScriptLoad
      document.body.appendChild(script)
    },
    onScriptLoad () {
      const PingPong = window.PingPong
      PingPong.Checkout.initializedHook = (mountedDomNode) => {
        // HTMLElement
        if (mountedDomNode) this.onReady(mountedDomNode)
        else this.onCmpError()
      }
      PingPong.Checkout.beforeCheckoutHook = async () => this.prefetchValidation('pingpong')
      this.initParams = JSON.parse(sessionStorage.getItem('ppParams') || '{}')
    },
    onReady () {
      console.log('cmp ready!')
    },
    onCmpError () {
      this.$router.go(-1)
      setTimeout(() => this.$root.$emit('adyenInitError'), 200)
    }
  },
  created () {
    this.loadPingpongScript()
  }
}
</script>

<style lang="scss">
.pingpong-page-wrapper{
  background-color: rgb(240, 242, 245);
  overflow-y: scroll;
  height: 100%;
  width: 100%;
  .content-wrapper{
    .pingpong-wrapper {
      height: 100%;
      width: 100%;
      box-sizing: border-box;
      overflow-y: auto;
      background-color: rgb(240, 242, 245);

      .inner-wrapper{
        max-width: 1200PX;
        margin: 0 auto;
      }
    }
  }
}

@media screen and (min-width: 1200PX){
  .pingpong-page-wrapper{
    .content-wrapper{
      display: flex;
      flex-direction: row-reverse;
      align-items: flex-start;
      max-width: 1200PX;
      margin: 30px auto 0;
    }
  }
}
</style>
