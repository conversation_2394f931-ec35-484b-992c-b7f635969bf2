<template>
  <div class="pingpong-page-wrapper">
    <channel-logo v-if="isPc"></channel-logo>
    <div class="content-wrapper">
      <channel-order :coin="initParams.coinNums" :currency="initParams.currency_symbol" :amount="initParams.amount" :in-debt="initParams.inDebt"></channel-order>
      <channel-wrapper>
        <section class="pingpong-wrapper">
          <div class="inner-wrapper">
            <div class="cmp-wrapper">
              <div class="frame-card"></div>
              <button id="submit" @click="submitForm">
                <img src="https://kg-web-cdn.akamaized.net/prod/web-pay-unique/dist_online/static/img/lock.b2cb557c.svg" alt="" aria-hidden="true">
                Pay {{initParams.currency_symbol}}{{ initParams.amount }}
              </button>
            </div>
          </div>
        </section>
      </channel-wrapper>
    </div>
    <channel-logo v-if="isMobile"></channel-logo>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import ChannelOrder from '@/views/paymethod/channelOrder'
import ChannelWrapper from '@/views/paymethod/channelWrapper'
import ChannelLogo from '@/views/paymethod/channelLogo'
import { service } from '@/server/http'

export default {
  name: 'payermax',
  components: { ChannelLogo, ChannelWrapper, ChannelOrder },
  computed: {
    ...mapState(['isPc', 'isMobile'])
  },
  data () {
    return {
      initParams: {},
      cardInstance: {},

      isFormValid: false,
      cardChosen: false
    }
  },
  methods: {
    loadPingpongScript () {
      const scriptUrl = 'https://cdn.payermax.com/dropin/js/pmdropin.min.js'
      const script = document.createElement('script')
      script.src = scriptUrl
      script.onload = this.onScriptLoad
      document.body.appendChild(script)
    },
    onScriptLoad () {
      const PMdropin = window.PMdropin
      this.initParams = JSON.parse(sessionStorage.getItem('params') || '{}')

      // 初始化卡组件
      const card = PMdropin.create('card', {
        clientKey: this.initParams.clientKey,
        sessionKey: this.initParams.sessionKey,
        language: this.$i18n.locale,
        sandbox: this.initParams.sand_box
      })
      // 挂载实例
      card.mount('.frame-card') // 将挂载至匹配到的第一个 dom 元素上

      // 事件
      card.on('form-check', res => {
        this.isFormValid = res.isFormValid
        this.cardChosen = res.isFormValid || Boolean(res.from)
      })
      card.on('ready', () => this.onReady())

      this.cardInstance = card
    },
    onReady () {
      // 状态不可信
      console.log('cmp ready!')
    },
    onCmpError () {
      this.$router.go(-1)
      setTimeout(() => this.$root.$emit('adyenInitError'), 200)
    },

    submitForm () {
      const cardInstance = this.cardInstance
      if (!this.cardChosen) return this.$toast.err('Please select a payment method!')
      if (!this.isFormValid) {
        cardInstance.emit('canMakePayment')
        return null
      }

      cardInstance.emit('setDisabled', true)
      cardInstance.emit('canMakePayment')
        .then(res => {
          const { code } = res
          switch (code) {
            case 'APPLY_SUCCESS': {
              const paymentToken = res?.data?.paymentToken
              this.requestPay(paymentToken)
              break
            }
            default: {

            }
          }
          cardInstance.emit('setDisabled', false)
        })
        .catch(err => {
          cardInstance.emit('setDisabled', false)
          console.log(err)
        })
    },
    requestPay (token) {
      const initParams = this.initParams
      const params = {
        reference: initParams.payment_order_id,
        sek: initParams.sessionKey,
        pt: token,
        subject: initParams.name
      }

      this.$loading.show()
      service.post(initParams.pay_url, params)
        .then(res => {
          const { code, data } = res
          switch (code) {
            case 0: {
              const { status } = data
              if (status === 'SUCCESS') {
                this.$router.replace('/completed?rf=1')
              }
              break
            }
            // case 120005: {
            //   break
            // }
            default: {
              this.$toast.err(this.$t('cb_page_title_err'))
            }
          }
        })
        .finally(() => this.$loading.hide())
    }
  },
  created () {
    this.loadPingpongScript()
  },
  beforeDestroy () {
    sessionStorage.removeItem('params')
  }
}
</script>

<style lang="scss">
button{
  display: inline-flex;
  -webkit-box-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  align-items: center;
  width: 100%;
  white-space: nowrap;
  outline: none;
  appearance: none;
  border-radius: 6px;
  border-style: none;
  font-weight: bold;
  font-family: inherit;
  box-shadow: none;
  background: #00112c;
  color: rgb(255, 255, 255);
  cursor: pointer;
  transition: all .3s;

  &:hover{
    background-color: rgb(28, 47, 69);
  }
  &:active{
    background: rgb(58, 74, 92);
  }
}

.pingpong-page-wrapper{
  background-color: rgb(240, 242, 245);
  overflow-y: scroll;
  height: 100%;
  width: 100%;
  .content-wrapper{
    .pingpong-wrapper {
      height: 100%;
      width: 100%;
      box-sizing: border-box;
      overflow-y: auto;
      background-color: rgb(240, 242, 245);

      .inner-wrapper{
        max-width: 1200PX;
        margin: 0 auto;

        .cmp-wrapper{
          border-radius: 24px;
          background-color: white;
          overflow: hidden;

          button{
            width: calc(100% - 64px);
            margin: 0 auto 30px;
            padding: 30px;
            line-height: 36px;
            font-size: 30px;
            font-weight: 500;
            border-radius: 12px;
            display: flex;
            align-items: center;

            img{
              display: inline-block;
              width: 32px;
              height: 32px;
              margin-right: 24px;
            }
          }
        }
      }
    }
  }
}

@media screen and (min-width: 1200PX){
  .pingpong-page-wrapper{
    .content-wrapper{
      display: flex;
      flex-direction: row-reverse;
      align-items: flex-start;
      max-width: 1200PX;
      margin: 30px auto 0;

      .pingpong-wrapper {
        .inner-wrapper{
          .cmp-wrapper{
            border-radius: 12px;

            button{
              margin: 12px auto 15px;
              padding: 15px;
              line-height: 18px;
              font-size: 15px;
              border-radius: 6px;
              width: calc(100% - 32px);

              img{
                width: 16px;
                height: 16px;
                margin-right: 12px;
              }
            }
          }
        }
      }
    }
  }
}
</style>
