<template>
<div class="airwallex-page-wrapper">
  <channel-logo v-if="isPc"></channel-logo>
  <div class="content-wrapper">
    <channel-order :coin="initParams.coinNums" :currency="initParams.currency_symbol" :amount="initParams.amount" :in-debt="initParams.inDebt"></channel-order>
    <channel-wrapper>
      <section class="airewallex-wrapper">
        <div class="inner-wrapper">
          <div ref="card" id="drop-in"></div>
        </div>
      </section>
    </channel-wrapper>
  </div>
  <channel-logo v-if="isMobile"></channel-logo>
</div>
</template>

<script>
import { init, createElement } from '@airwallex/components-sdk'
import ChannelLogo from '@/views/paymethod/channelLogo'
import ChannelWrapper from '@/views/paymethod/channelWrapper'
import ChannelOrder from '@/views/paymethod/channelOrder'
import { mapState } from 'vuex'

export default {
  name: 'airwallex',
  components: { ChannelLogo, ChannelWrapper, ChannelOrder },
  computed: {
    ...mapState(['isPc', 'isMobile'])
  },
  data () {
    return {
      airwalexInstance: '',
      initParams: {}
    }
  },
  methods: {
    async initAirwallext () {
      let params
      try {
        params = JSON.parse(sessionStorage.getItem('params') || '{}')
        this.initParams = params

        await init({
          enabledElements: ['payments'],
          env: params.env, // Setup which Airwallex env('staging' | 'demo' | 'prod') to integrate with
          origin: window.location.origin // Setup your event target to receive the browser events message
        })

        const element = await createElement('dropIn', {
          intent_id: params.intent_id,
          client_secret: params.client_secret,
          currency: params.currency,
          mode: params.mode,
          cvcRequired: true,
          // recurringOptions: {
          //   card: {
          //     next_triggered_by: 'customer',
          //     requires_cvc: true
          //   }
          // },
          // if you want to use apple pay, please pass merchant country code in applePayRequestOptions
          // applePayRequestOptions: {
          //   countryCode: 'replace-with-your-country-code'
          // },
          // // if you want to use google pay, please pass merchant country code in googlePayRequestOptions
          // googlePayRequestOptions: {
          //   countryCode: 'replace-with-your-country-code'
          // },
          // theme field is optional, you can customize dropIn element style here
          theme: {
            palette: {
              primary: '#00112c' // brand color, the default value is #612FFF
            }
          },
          methods: [params.payment_method],
          customer_id: params.customer_id
        })

        element.mount('drop-in')
        this.airwalexInstance = element

        const domElement = this.$refs.card
        if (domElement.addEventListener) {
          domElement.addEventListener('onReady', this.onReady)
          domElement.addEventListener('onSuccess', this.onSuccess)
          domElement.addEventListener('onError', this.onError)
        }
      } catch (e) {
        console.error(e.message)
        console.log(`Airwallext 组件初始化失败，错误信息：${e.message}。`)
      }
    },
    onReady () {
      console.log('cmp ready!')
    },
    onSuccess (event) {
      this.$router.replace('/completed?ir=aw')
    },
    onError (event) {
      // https://www.airwallex.com/docs/api?v=2019-09-09#/Errors
      const { error } = event.detail

      switch (error.code) {
        // case 'unauthorized': {
        //   this.$toast.err(error.message)
        //   break
        // }
        default: {
          this.$toast.err(error.message)
        }
      }
    }
  },
  created () {
    this.initAirwallext()
  },
  beforeDestroy () {
    sessionStorage.removeItem('params')
  }
}
</script>

<style scoped lang="scss">
.airwallex-page-wrapper{
  background-color: rgb(240, 242, 245);
  overflow-y: scroll;
  height: 100%;
  width: 100%;
  .content-wrapper{
    .airewallex-wrapper {
      height: 100%;
      width: 100%;
      box-sizing: border-box;
      overflow-y: auto;
      background-color: rgb(240, 242, 245);

      .inner-wrapper{
        max-width: 1200PX;
        margin: 0 auto;
      }
    }
  }
}
@media screen and (min-width: 1200PX){
  .airwallex-page-wrapper{
    .content-wrapper{
      display: flex;
      flex-direction: row-reverse;
      align-items: flex-start;
      max-width: 1200PX;
      margin: 30px auto 0;
    }
  }
}
</style>
