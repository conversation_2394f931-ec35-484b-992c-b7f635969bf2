import { service } from '../../server/http'

const ErrorMap = {
  CVC_VERIFICATION_FAILED: 'channel-pay-error-cvc', // 无效的CVV或有效期，请检查您的卡详细信息
  NOT_ENOUGH_MONEY: 'channel-pay-error-no-money', // 余额不足
  UNSAFE_PAYMENT_ENVIRONMENT: 'channel-pay-error-environment-unsafe', // 支付失败，请检查网络环境和卡信息，请确认是否改卡是否是本人正在使用，尽量使用常用卡或者常用IP支付，请尝试使用其他卡或联系您的银行以获取进一步支持。
  CARD_MAX_AMOUNT: 'channel-pay-error-max-amount', // 已达到该张卡的支付金额上限
  CARD_MAX_PAY_TIMES: 'channel-pay-error-max-pay-times', // 已达到该张卡的支付次数上限
  CARD_INVALID_NUMBER: 'channel-pay-error-invalid_number', // 卡号无效
  CARD_HAS_EXPIRED: 'channel-pay-error-has-expired', // 该卡已过期
  NETWORK_ERROR: 'channel-pay-error-network-error', // 网络问题，请返回重新下单
  TRANSACTION_NOT_ALLOWED: 'channel-pay-error-not-allowed', // 交易不被允许，请勿重试，请尝试使用其他卡或联系您的银行以获取进一步支持。
  OTHER_ERROR: 'channel-pay-error-other' // 支付失败，请重试
}

export default {
  data () {
    return {
      clickPayTimes: Number(sessionStorage.getItem('7x9FkL2pQm') || 0)
    }
  },
  methods: {
    basicShowError (whoAmI, result) {
      switch (whoAmI) {
        case 'ad': {
          const CodeMap = {
            CVC_VERIFICATION_FAILED: { t1: 24 },
            NOT_ENOUGH_MONEY: { t1: 12, t2: 51, t3: [24, 25, 26, 27, 28, 29, 30] },
            UNSAFE_PAYMENT_ENVIRONMENT: { t1: [20, 31, 2, 14], t2: ['05', 13, 83, 59, 88] },
            CARD_MAX_AMOUNT: { t1: 28, t2: 61 },
            CARD_MAX_PAY_TIMES: { t1: 29 },
            CARD_INVALID_NUMBER: { t1: 8, t2: [14, 15] },
            CARD_HAS_EXPIRED: { t1: 6, t2: 54, t3: 1 },
            NETWORK_ERROR: { t1: '905_3' },
            TRANSACTION_NOT_ALLOWED: { t1: [23, 22, 25], t2: [1, 3, 12, 41, 43, 46, 57, 58, 62, 63, 70, 82], t3: [3, 21] }
          }
          // console.log(result)
          const errorList = []
          if (result.refusalReasonCode) errorList.push(`t1_${result.refusalReasonCode}`)
          if (result.RawCode) errorList.push(`t2_${result.RawCode}`)
          if (result.MacCode) errorList.push(`t3_${result.MacCode}`)

          // console.log(errorList)
          this.showMessage(CodeMap, errorList)
          break
        }
        case 'cko': {
          const CodeMap = {
            CVC_VERIFICATION_FAILED: { t1: [20087, 20100] },
            NOT_ENOUGH_MONEY: { t1: 20051, t3: [24, 25, 26, 27, 28, 29, 30] },
            UNSAFE_PAYMENT_ENVIRONMENT: { t1: [20001, 20002, 20003, 20005, 20012, 20046, 20059, 30004, 30020, 30034] },
            CARD_MAX_AMOUNT: { t1: [20061, 30021] },
            CARD_MAX_PAY_TIMES: { t1: [20065, 30022] },
            CARD_INVALID_NUMBER: { t1: [20014, 30015] },
            CARD_HAS_EXPIRED: { t1: 30033, t3: 1 },
            // NETWORK_ERROR: { },
            TRANSACTION_NOT_ALLOWED: { t1: [20057, 20091, '2006P', 20103, 30041, 30043, 40101], t3: [3, 21] }
          }

          const errorList = []
          if (result.code) errorList.push(`t1_${result.code}`)
          if (result.raw_code) errorList.push(`t2_${result.raw_code}`)
          if (result.mac_code) errorList.push(`t3_${result.mac_code}`)

          // console.log(errorList)
          this.showMessage(CodeMap, errorList)
          break
        }
      }
    },
    showMessage (CodeMap, err = []) {
      if (!err.length) return this.$toast.err(this.$t(ErrorMap.OTHER_ERROR))

      // 依次对目标错误码遍历
      for (const targetStr of err) {
        const [targetType] = targetStr.split('_')
        // console.log(targetStr)

        for (const [findKey, typeObj] of Object.entries(CodeMap)) {
          const needTraverseListOrKey = typeObj[targetType]
          const proceedTraverseList = Array.isArray(needTraverseListOrKey)
            ? needTraverseListOrKey.map(item => `${targetType}_${item}`)
            : [`${targetType}_${needTraverseListOrKey}`]
          if (proceedTraverseList.includes(targetStr)) return this.$toast.err(this.$t(ErrorMap[findKey]), 4000)
          // console.log(proceedTraverseList, targetStr, proceedTraverseList.includes(targetType))
        }
      }

      this.$toast.err(this.$t(ErrorMap.OTHER_ERROR), 4000)
    },
    async prefetchValidation (channel) {
      // 7x9FkL2pQm 存点击次数
      // 3zRtY8vXwN 存订单信息
      sessionStorage.setItem('7x9FkL2pQm', ++this.clickPayTimes)
      if (this.clickPayTimes < 3) return

      const orderTempInfo = JSON.parse(sessionStorage.getItem('3zRtY8vXwN') || '{}')
      if (!(orderTempInfo.payment_host && orderTempInfo.order_id && orderTempInfo.payment_order_id)) return // 如果缺少订单和url都直接返回
      let { payment_host: url, order_id: outTradeNo, payment_order_id: orderId } = orderTempInfo
      url += '/api/payment/pay_risk_before'

      return service.post(url, {
        order_id: orderId,
        out_trade_no: outTradeNo,
        channel
      })
        .then(res => {
          const { code } = res
          switch (code) {
            case 0: {
              break
            }
            case 120012: {
              this.$toast.err(this.$t('prefetch-safety-error').replace(/<br\/>/g, ''))
              sessionStorage.removeItem('ppParams')
              setTimeout(() => this.$router.replace('/'), 500)
              // eslint-disable-next-line prefer-promise-reject-errors
              return Promise.reject('120012')
            }
            default: {
              this.$toast.err(this.$t('cb_page_title_err'))
            }
          }
        })
    }
  },
  beforeDestroy () {
    sessionStorage.removeItem('7x9FkL2pQm')
    sessionStorage.removeItem('3zRtY8vXwN')
  }
  // mounted () {
  //   this.basicShowError({
  //     rawCode: '05'
  //   })
  // }
}
