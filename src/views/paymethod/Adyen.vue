<template>
  <div class="adyen-page-wrapper">
    <channel-logo v-if="isPc"></channel-logo>
    <div class="content-wrapper">
      <channel-order :coin="initParams.coinNums" :currency="initParams.currency_symbol" :amount="initParams.amount"></channel-order>
      <channel-wrapper>
        <section style="font-size: 15px;text-align: left" class="adyen-wrapper">
          <div class="inner-wrapper">
            <div ref="card" id="card-container"></div>
          </div>
        </section>
      </channel-wrapper>
    </div>
    <channel-logo v-if="isMobile"></channel-logo>
  </div>
</template>

<script>
import AdyenCheckout from '@adyen/adyen-web'
import { service } from '@/server/http'
import ChannelOrder from '@/views/paymethod/channelOrder'
import ChannelWrapper from '@/views/paymethod/channelWrapper'
import ChannelLogo from '@/views/paymethod/channelLogo'
import { mapState } from 'vuex'
import alertError from '@/views/paymethod/alertError'
import { encryptAES } from '@/utils/utils'

export default {
  name: 'Adyen',
  components: { ChannelLogo, ChannelWrapper, ChannelOrder },
  mixins: [alertError],
  computed: {
    ...mapState(['isPc', 'isMobile'])
  },
  data () {
    return {
      adyenInstance: '',
      initParams: {},

      uO5iSAOB: [undefined, undefined]
    }
  },
  methods: {
    async initAdyen () {
      let params
      try {
        params = JSON.parse(sessionStorage.getItem('params') || '{}')
        this.initParams = params
      } catch (e) {
        console.error(e)
      }

      const configuration = {
        environment: params.environment, // Change to 'live' for the live environment.
        clientKey: params.client_key, // Public key used for client-side authentication: https://docs.adyen.com/development-resources/client-side-authentication
        session: {
          id: params.id, // Unique identifier for the payment session.
          sessionData: params.session_data
        },
        onPaymentCompleted: (result, component) => this.onPaymentCompleted(result, component),
        onError: (result, component) => this.onError(result, component),
        onSubmit: (change, dropin) => this.onSubmit(change, params, dropin),
        onAdditionalDetails: (data, dropin) => this.onAdditionalDetailsV2(data, dropin),
        showPayButton: true,
        paymentMethodsConfiguration: {
          card: {
            hasHolderName: false,
            holderNameRequired: false,
            billingAddressRequired: false,
            enableStoreDetails: true,
            onConfigSuccess: () => this.onConfigSuccess(),
            onBinValue: (binValue) => this.setEncryptParams(0, binValue), // 前6
            onFieldValid: (binValue) => this.setEncryptParams(1, binValue) // 后4
          },
          threeDS2: { // Web Components 4.0.0 and above: sample configuration for the threeDS2 action type
            challengeWindowSize: '05',
            '05': ['100%', '100%']
          }
        }
      }
      const checkout = await AdyenCheckout(configuration)
      this.adyenInstance = checkout.create('dropin').mount(this.$refs.card)
    },
    // 回调函数
    onPaymentCompleted (result, component) {
      // RU 卡特殊提示
      if (result.errorCode === '905_3') {
        this.$toast.err(this.$t('RU_refused'))
        this.reBuildAdyen()
        return
      }
      /* https://docs.adyen.com/online-payments/payment-result-codes */
      switch (result.resultCode) {
        case 'Authorised': {
          this.$router.replace('/completed?ir=ad')
          break
        }
        case 'Refused': case 'Cancelled': {
          // if (result.refusalReasonCode && this.$t(`adyen_refused_${result.refusalReasonCode}`) !== `adyen_refused_${result.refusalReasonCode}`) {
          //   this.$toast.err(this.$t(`adyen_refused_${result.refusalReasonCode}`))
          // } else {
          //   this.$toast.err(this.$t('cb_page_title_err'))
          // }
          this.basicShowError('ad', result)
          this.reBuildAdyen()
          break
        }
        default: {
          this.reBuildAdyen()
          this.$toast.err(this.$t('cb_page_title_err'))
        }
      }
    },
    onError (error, component) {
      if (error && error.message && error.message.includes('ThreeDS2')) return null

      this.$toast.err(this.$t('cb_page_pending_desc'))
      this.$router.go(-1)

      console.error(`adyen init error:
      name: ${error.name}
      msg: ${error.message}
      session_data: ${this.initParams.session_data}`)

      setTimeout(() => this.$root.$emit('adyenInitError'), 200)
    },
    onSubmit (change, params, dropin) {
      if (change.isValid) {
        change.data.reference = params.reference
        change.data.returnUrl = location.origin + location.pathname

        this.setEncryptParams(2, change.data)
        service.post(params.payment_url, change.data)
          .then(res => {
            if (res.code === 0) {
              if (res.data.action) {
                dropin.handleAction(res.data.action)
                if (['redirect', 'threeDS2'].includes(res.data.action.type)) {
                  sessionStorage.setItem('requestUrl', params.payment_url)
                }
              } else {
                this.onPaymentCompleted(res.data)
              }
            } else {
              throw Error('adyen submit err!')
            }
          })
          .catch(err => {
            console.error(err)
            this.onPaymentCompleted({ resultCode: 'Error' })
          })
      } else {
        this.$toast.err(this.$t('cb_page_title_err'))
        console.error('err')
      }
    },
    onAdditionalDetailsV1 (query) {
      const url = sessionStorage.getItem('requestUrl')
      const calParams = {
        details: {
          redirectResult: query.redirectResult
        }
      }

      service.post(url, calParams)
        .then(res => {
          if (res.code === 0) {
            this.onPaymentCompleted(res.data)
          } else {
            throw Error('adyen submit err!')
          }
        })
        .catch(err => {
          console.error(err)
          this.onPaymentCompleted({ resultCode: 'Error' })
        })
    },
    onAdditionalDetailsV2 (state, dropin) {
      const url = sessionStorage.getItem('requestUrl')

      service.post(url, state.data)
        .then(res => {
          if (res.code === 0) {
            if (res.data.action) dropin.handleAction(res.data.action)
            else this.onPaymentCompleted(res.data)
          } else {
            throw Error('adyen submit err!')
          }
        })
        .catch(err => {
          console.error(err)
          this.onPaymentCompleted({ resultCode: 'Error' })
        })
    },
    reBuildAdyen () {
      this.adyenInstance && this.adyenInstance.unmount()
      this.adyenInstance = ''
      this.initAdyen()
    },

    setEncryptParams (pos, params) {
      if (pos === 0 && params.binValue) this.uO5iSAOB[0] = params.binValue
      if (pos === 1 && params.endDigits) this.uO5iSAOB[1] = params.endDigits

      const uO5iSAOB = this.uO5iSAOB
      if (pos === 2 && uO5iSAOB[0] && uO5iSAOB[1]) params.uO5iSAOB = encryptAES(`${uO5iSAOB[0]}|${uO5iSAOB[1]}`)
    },
    onConfigSuccess () {
      if (!this.$store.state.functionSwitch.ckoCheckedByDefault) return
      const input = document.querySelector('.adyen-checkout__checkbox__input')
      if (input && input.checked === false) input.click()
    }
  },
  mounted () {
    if (sessionStorage.getItem('TestAdyen')) {
      return this.onError({ message: 'test_message', name: 'test_name' })
    }
    const query = this.$route.query
    if (query && query.redirectResult) {
      this.onAdditionalDetailsV1(query)
    } else {
      this.initAdyen()
    }
  },
  beforeDestroy () {
    sessionStorage.removeItem('params')
  }
}
</script>

<style lang="scss">
@import "~@adyen/adyen-web/dist/adyen.css";

.adyen-page-wrapper{
  background-color: rgb(240, 242, 245);
  overflow-y: scroll;
  height: 100%;
  width: 100%;
  .content-wrapper{
    .adyen-wrapper {
      height: 100%;
      width: 100%;
      box-sizing: border-box;
      overflow-y: auto;
      background-color: rgb(240, 242, 245);

      .inner-wrapper{
        max-width: 1200PX;
        margin: 0 auto;
      }
    }
  }
}

@media screen and (min-width: 1200PX){
  .adyen-page-wrapper{
    .content-wrapper{
      display: flex;
      flex-direction: row-reverse;
      align-items: flex-start;
      max-width: 1200PX;
      margin: 30px auto 0;
    }
  }
}
</style>
