<template>
  <div class="checkout-page-wrapper">
    <channel-logo v-if="isPc"></channel-logo>
    <div class="content-wrapper">
      <channel-order :coin="initParams.coinNums" :currency="initParams.currency_symbol" :amount="initParams.amount" :in-debt="initParams.inDebt"></channel-order>
      <channel-wrapper>
        <ul class="card-option-list">
          <!--  记住的卡号  -->
          <template v-if="historyCard.length">
            <li v-for="historyObj in historyCard" :key="historyObj.key" @click="toggle(historyObj.key)" :class="['history-card-item', {active: historyObj.key === chosenIndex}]">
              <div class="card-info">
                <span :class="['selected-status', {active: historyObj.key === chosenIndex}]"></span>
                <img v-if="historyObj.cardOrg.toLowerCase() === 'amex'" src="https://kg-web-cdn.akamaized.net/prod/web-pay-unique/dist_online/static/img/amex.bf5ac3b0.svg" alt="">
                <img v-if="historyObj.cardOrg.toLowerCase() === 'jcb'" src="https://kg-web-cdn.akamaized.net/prod/web-pay-unique/dist_online/static/img/jcb.ab1fb383.svg" alt="">
                <img v-if="historyObj.cardOrg.toLowerCase() === 'mastercard'" src="https://kg-web-cdn.akamaized.net/prod/web-pay-unique/dist_online/static/img/mastercard.1c73bade.svg" alt="">
                <img v-if="historyObj.cardOrg.toLowerCase() === 'visa'" src="https://kg-web-cdn.akamaized.net/prod/web-pay-unique/dist_online/static/img/visa.ccab0c13.svg" alt="">

                <img v-if="historyObj.cardOrg.toLowerCase() === 'mada'" src="https://kg-web-cdn.akamaized.net/prod/web-pay-unique_koa/dist_online/static/image/mada.svg" alt="">
                <img v-if="historyObj.cardOrg.toLowerCase() === 'diners'" src="https://kg-web-cdn.akamaized.net/prod/web-pay-unique_koa/dist_online/static/image/diners.svg" alt="">
                <img v-if="historyObj.cardOrg.toLowerCase() === 'discover'" src="https://kg-web-cdn.akamaized.net/prod/web-pay-unique_koa/dist_online/static/image/discover.svg" alt="">
                <span class="card-number">•••• {{ historyObj.cardSummery }}</span>
              </div>
              <!-- 展开部分  -->
              <template v-if="chosenIndex === historyObj.key">
                <div class="form-row-wrapper">
                  <div class="form-row-item date-wrapper">
                    <span>Expiry date</span>
                    <div class="disabled-input">{{ historyObj.cardExpiry }}</div>
                  </div>
                  <div class="form-row-item cvc-wrapper">
                    <span :class="[{'cvc-error': showCvcError}]">CVC / CVV</span>
                    <input @input="fixCvC" v-model="cvc" placeholder="3 or 4 digits" @focus="showCvcError = false" :class="[{'error-cvc_input': showCvcError}]">
                    <div v-if="!showCvcError" class="cvc-find-position-wrapper" dir="ltr">
                      <img v-if="historyObj.cardOrg === 'AMEX'" src="https://kg-web-cdn.akamaized.net/prod/web-pay-unique/dist_online/static/img/field_cvc_position_tips--front.9b9669cd.svg">
                      <img v-else src="https://kg-web-cdn.akamaized.net/prod/web-pay-unique/dist_online/static/img/field_cvv_position_tips--back.1457d81b.svg">
                    </div>

                    <template v-if="showCvcError">
                      <img class="error-cvc__red-no" alt="field_error" src="https://kg-web-cdn.akamaized.net/prod/web-pay-unique/dist_online/static/img/field_error.5f6b2397.svg">
                      <span class="error-cvc_span">{{ $t('channel-checkout-cvc-error') }}</span>
                    </template>
                  </div>
                </div>
                <button @click="payByHistoryCard(historyObj)">
                  <img src="https://kg-web-cdn.akamaized.net/prod/web-pay-unique/dist_online/static/img/lock.b2cb557c.svg" alt="" aria-hidden="true">
                  Pay {{initParams.currency_symbol}}{{ initParams.amount }}
                </button>
              </template>
            </li>
          </template>
          <!--  新卡支付  -->
          <li class="new-card-item" @click="toggle(newCardTxt)">
            <div class="card-info">
              <span :class="['selected-status', {active: newCardTxt === chosenIndex}]"></span>
              <img src="https://kg-web-cdn.akamaized.net/prod/web-pay-unique/dist_online/static/img/card.f49547d7.svg" alt="">
              <span class="card-number">Credit Card</span>

              <div class="support-card-list" v-if="newCardTxt !== chosenIndex">
<!--                <img src="https://kg-web-cdn.akamaized.net/prod/web-pay-unique/dist_online/static/img/amex.bf5ac3b0.svg" alt="">-->
                <img src="https://kg-web-cdn.akamaized.net/prod/web-pay-unique/dist_online/static/img/jcb.ab1fb383.svg" alt="">
                <img src="https://kg-web-cdn.akamaized.net/prod/web-pay-unique/dist_online/static/img/mastercard.1c73bade.svg" alt="">
                <img src="https://kg-web-cdn.akamaized.net/prod/web-pay-unique/dist_online/static/img/visa.ccab0c13.svg" alt="">
                <span>+3</span>
              </div>
            </div>
            <!-- 展开部分  -->
            <div class="new-card-wrapper" v-show="chosenIndex === newCardTxt">
              <section class="checkout-wrapper">
                <div class="inner-wrapper">
                  <div id="payments"></div>
                </div>
              </section>
              <div v-show="checkoutInstance" class="operation">
                <label>
                  <span>
                    <input type="checkbox" v-model="recordCardNum">
                    <img v-if="recordCardNum" src="https://kg-web-cdn.akamaized.net/prod/web-pay-unique/dist_online/static/img/duigou.9e50b57c.svg" alt="">
                  </span>
                  {{ $t('channel-checkout-save-card-number') }}
                </label>
                <button id="submit" @click="payByNewCard">
                  <img src="https://kg-web-cdn.akamaized.net/prod/web-pay-unique/dist_online/static/img/lock.b2cb557c.svg" alt="" aria-hidden="true">
                  Pay {{initParams.currency_symbol}}{{ initParams.amount }}
                </button>
              </div>
            </div>
          </li>
        </ul>
      </channel-wrapper>
    </div>
    <channel-logo v-if="isMobile"></channel-logo>
  </div>
</template>

<script>
import { loadCheckoutWebComponents } from '@checkout.com/checkout-web-components'
import ChannelOrder from '@/views/paymethod/channelOrder'
import ChannelWrapper from '@/views/paymethod/channelWrapper'
import ChannelLogo from '@/views/paymethod/channelLogo'
import { mapState } from 'vuex'
import { service } from '@/server/http'
import alertError from "@/views/paymethod/alertError";
const newCardTxt = 'newCard'

export default {
  name: 'checkout',
  components: { ChannelLogo, ChannelWrapper, ChannelOrder },
  mixins: [alertError],
  computed: {
    ...mapState(['isPc', 'isMobile']),
    cvcValidated () {
      const length = this.cvc.length
      return length >= 3 && length <= 4
    }
  },
  watch: {
    loading (newValue, oldValue) {
      if (newValue === true && oldValue === false) this.$loading.show()
      if (newValue === false && oldValue === true) this.$loading.hide()
    }
  },
  data () {
    return {
      checkoutInstance: '',
      initParams: {},

      newCardTxt,
      historyCard: [],
      chosenIndex: -1,
      cvc: '',
      showCvcError: false,
      recordCardNum: false,
      loading: false,

      isFirstPayFinished: true
    }
  },
  methods: {
    async prepareParams () {
      let params
      try {
        params = JSON.parse(sessionStorage.getItem('params') || '{}')
        this.initParams = params
        if (params.sources) {
          this.historyCard = params.sources.map(historyItem => {
            const key = Object.keys(historyItem)[0]
            const obj = historyItem[key]

            return { key, ...obj }
          })
        }

        if (this.historyCard.length) {
          this.chosenIndex = this.historyCard[0].key
        } else {
          this.toggle(newCardTxt)
        }
      } catch (e) {
        console.error(e)
      }
    },

    async initCheckout () {
      const langChangeMap = { zh_tw: 'zh-tw', zh_cn: 'zh' }
      const calcLang = langChangeMap[this.$i18n.locale] || this.$i18n.locale
      const params = this.initParams
      const cko = await loadCheckoutWebComponents({
        paymentSession: params.session_data,
        publicKey: params.client_key,
        environment: params.env,
        appearance: {
          focusOutlineWidth: '0'
        },
        locale: calcLang,
        onReady: () => this.onReady(),
        onPaymentCompleted: (result, component) => this.onPaymentCompleted(result, component),
        onChange: component => this.onChange(component),
        onError: (component, error) => this.onError(component, error),
        onSubmit: (component) => this.onSubmit(component),
        componentOptions: {
          card: {
            displayCardholderName: 'hidden'
          }
        }
      })
      const ckoInstance = await cko.create('flow', {
        showPayButton: false
      })
      this.checkoutInstance = ckoInstance.mount('#payments')
    },
    onChange (component) {
      console.log('onChange', 'isValid: ', component.isValid(), ' for ', component.type)
    },
    onReady () {
      console.log('onReady')
    },
    onSubmit (cmp) {
      console.log('onSubmit')
      if (this.loading) this.loading = false
    },
    onPaymentCompleted (component, paymentResponse) {
      if (this.loading) this.loading = false
      this.isFirstPayFinished = true
      this.$router.replace('/completed?ir=cko')
    },
    onError (component, error) {
      if (this.loading) this.loading = false
      this.isFirstPayFinished = true
      // 组件初始化
      if (error.type === 'Integration') {
        if (error.details && error.details.includes('PaymentSession Response needs to be provided')) {
          console.error('checkout: 组件初始化失败!')
          this.$router.go(-1)
          setTimeout(() => this.$root.$emit('adyenInitError'), 200)
        }
      }
      // 点击提交按钮
      if (error.type === 'Submit') {
        switch (error.details) {
          case 'Payment Method not valid': {
            // 未填写任何信息提交
            break
          }
        }
      }
      // 开始请求
      if (error.type === 'Request') {
        // 请求失败
        if (error.status !== 200 && error.details.paymentId) return this.fetchErrorMessage(error.details.paymentId)
        // if (error.status !== 200) return this.$toast.err(this.$t('cb_page_title_err'))
        // const { status, decline_reason: declineReason } = error.details
        // if (status === 'Declined') {
        //   switch (declineReason) {
        //     // case 'not_enough_funds': {
        //     //   break
        //     // }
        //     default: {
        //       this.$toast.err(this.$t('cb_page_title_err'))
        //     }
        //   }
        // }
      }

      console.error('initCheckout: 未知的错误！' + error.message)
    },
    fetchErrorMessage (paymentId) {
      if (!this.initParams.ext_detail_url) return null
      const initParams = this.initParams
      service.get(initParams.ext_detail_url, {
        params: {
          sid: paymentId
        }
      })
        .then(res => {
          const { code, data } = res
          if (code === 0) {
            this.basicShowError('cko', data)
          }
        })
    },

    fixCvC (e) {
      const isNaN = Number.isNaN(
        Number(e.data)
      )

      if (isNaN) {
        const stringCvc = String(this.cvc)
        const length = stringCvc.length
        this.cvc = stringCvc.slice(0, length - 1)
      }

      const stringCvc = String(this.cvc)
      if (stringCvc.length > 4) {
        this.cvc = stringCvc.slice(0, 4)
      }
    },
    payByHistoryCard (historyItem) {
      if (!this.cvcValidated) {
        this.showCvcError = true
        return null
      }

      this.loading = true
      const initParams = this.initParams
      service.post(initParams.payment_url, {
        reference: initParams.reference,
        source_id: historyItem.key,
        cvv: this.cvc
      })
        .then(res => {
          const { code, data } = res
          if (code === 0) {
            if (data.response_code) {
              switch (data.response_code) {
                case '10000': {
                  this.onPaymentCompleted()
                  break
                }
                default: {
                  this.basicShowError('cko', data)
                }
              }
            } else {
              location.href = data.redirect_url
              // this.$toast.err(this.$t('cb_page_title_err'))
              // this.$router.go(-1)
            }
          }
        })
        .finally(() => (this.loading = false))
    },
    payByNewCard () {
      if (this.loading) return null
      if (!this.isFirstPayFinished) return null
      this.isFirstPayFinished = false
      this.loading = true
      const initParams = this.initParams
      if (this.recordCardNum) {
        service.post(initParams.store_card_url, {
          reference: initParams.reference
        })
      }
      this.checkoutInstance.submit()
    },
    async toggle (key) {
      this.chosenIndex = key

      // 新卡初始化
      try {
        if (key === newCardTxt && !this.checkoutInstance) await this.initCheckout()
      } catch (e) {
        this.onError('', e)
      }
    },
    delJsScript () {
      const srcToRemove = 'https://checkout-web-components.checkout.com/index.js'
      const scripts = document.getElementsByTagName('script')
      const scriptsArray = Array.prototype.slice.call(scripts)
      scriptsArray.forEach(function (script) {
        if (script.src === srcToRemove) {
          script.parentNode.removeChild(script)
        }
      })
    }
  },
  created () {
    if (this.$store.state.functionSwitch.ckoCheckedByDefault) this.recordCardNum = true
    this.prepareParams()
  },
  beforeDestroy () {
    this.delJsScript()
    sessionStorage.removeItem('params')
  }
}
</script>

<style scoped lang="scss">
button{
  display: inline-flex;
  -webkit-box-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  align-items: center;
  width: 100%;
  white-space: nowrap;
  outline: none;
  appearance: none;
  border-radius: 6px;
  border-style: none;
  font-weight: bold;
  font-family: inherit;
  box-shadow: none;
  background: #00112c;
  color: rgb(255, 255, 255);
  cursor: pointer;
  transition: all .3s;

  &:hover{
    background-color: rgb(28, 47, 69);
  }
  &:active{
    background: rgb(58, 74, 92);
  }
}

.checkout-page-wrapper{
  background-color: rgb(240, 242, 245);
  overflow-y: scroll;
  height: 100%;
  width: 100%;
  .content-wrapper{

    .card-option-list{
      .history-card-item{
        background-color: white;
        border-radius: 24px;
        transition: height .5s;
        margin-bottom: 16px;
        text-align: center;
        overflow: hidden;

        .card-info{
          text-align: left;
          display: flex;
          align-items: center;
          padding: 24px 32px 24px 28px;

          .selected-status{
            width: 32px;
            height: 32px;
            border: 1px solid #b9c4c9;
            border-radius: 50%;
            margin-right: 28px;

            &.active{
              border: none;
              background-color: #06f;
              transition: all .3s ease-out;
              position: relative;
              display: flex;
              align-items: center;
              justify-content: center;

              &:after{
                position: absolute;
                width: 12px;
                height: 12px;
                background-color: white;
                content: '';
                border-radius: 50%;
              }
            }
          }

          img{
            width: 76px;
            height: 48px;
            margin-right: 16px;
            display: inline-block;
          }

          .card-number{
            font-weight: 500;
            color: #00112c;
            font-size: 28px;
          }

          .support-card-list{
            margin-left: auto;
            display: flex;
            align-items: center;

            img{
              width: 48px;
              height: 32px;
              margin: 0 4px;
            }

            span{
              color: #99a3ad;
              font-size: 22px;
              margin-left: 3px;
            }
          }
        }

        .form-row-wrapper{
          display: flex;
          align-items: flex-start;
          justify-content: space-between;
          padding: 0 32px;
          text-align: left;

          .form-row-item{
            flex: 1;
            width: 0;
            position: relative;
            display: flex;
            flex-direction: column;

            span{
              height: 26px;
              font-size: 23px;
              line-height: 1;
              font-weight: normal;
              margin-bottom: 10px;
              display: inline-block;
            }

            .disabled-input{
              padding: 10px 16px;
              border: 1px solid #e6e9eb;
              background: #e6e9eb;
              line-height: 50px;
              width: 100%;
              border-radius: 10px;
              font-size: 30px;
              font-weight: normal;
              transition: all .2s;

              &:active{
                border-color: #06f;
                box-shadow: 0 0 6px 2px #06f;
              }
            }

            input{
              appearance: none;
              -webkit-appearance:none;
              font-weight: 400;
              color: #8C8C8C;
              width: 100%;

              box-sizing: border-box;
              transition: border-color 0.15s ease 0s, box-shadow 0.15s ease 0s, background 0.15s ease 0s;
              background: rgb(252, 252, 253);
              box-shadow: rgb(235, 236, 240) 0px 0px 0px 1px;
              position: relative;

              line-height: 24px;
              -webkit-tap-highlight-color: transparent !important;

              height: 76px;
              border-radius: 10px;
              text-indent: 10px;
              font-size: 30px;
              border: 1px solid #e6e9eb;

              &:active, &:focus{
                appearance: none;
                -webkit-appearance:none;
                outline: none;
                border-color: #06f;
                box-shadow: 0 0 4px 2px #06f;
              }

              &.error-cvc_input{
                border: 1px solid #ad283e;
              }

              &::-webkit-input-placeholder{
                color: rgb(202, 211, 214);
                font-size: 32px;
              }
            }
            input::-webkit-outer-spin-button,
            input::-webkit-inner-spin-button {
              -webkit-appearance: none !important;
              margin: 0;
            }
            input[type=number]{-moz-appearance:textfield;}

            .cvc-find-position-wrapper{
              position: absolute;
              right: 10px;
              top: 78px;
              transform: translateY(-50%);
            }

            .error-cvc__red-no{
              position: absolute;
              right: 30px;
              top: 74px;
              transform: translateY(-50%);
            }

            .error-cvc_span{
              color: #d10244;
              margin-top: 8px;
              display: inline-flex;
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Fira Sans', 'Droid Sans', 'Helvetica Neue', 'Noto Sans', 'Liberation Sans', Arial, sans-serif;
              font-size: 22px;
              font-weight: 400;
              line-height: 30px;
            }

            &.cvc-wrapper{
              margin-left: 50px;

              .cvc-error{
                color: #d10244;
              }
            }
          }
        }

        button{
          width: calc(100% - 64px);
          margin: 48px auto 30px;
          padding: 30px;
          line-height: 36px;
          font-size: 30px;
          font-weight: 500;
          border-radius: 12px;
          display: flex;
          align-items: center;

          img{
            display: inline-block;
            width: 32px;
            height: 32px;
            margin-right: 24px;
          }
        }

        &:nth-of-type(n+1){
          margin-top: 4px;
        }
      }

      .new-card-item{
        @extend .history-card-item;

        .new-card-wrapper{
          background-color: white;
          overflow: hidden;
          border-radius: 10px;
          padding-bottom: 24px;

          ::v-deep{
            #cardAccordionContainer{
              border: none;
              background-color: white;
              border-radius: 0;

              #cardAccordionButton{
                display: none;
              }

              .fy1visq{
                padding-bottom: 0;
              }

              .f1jh0b9w{
                display: none;
              }

              .fl2ed1u{
                display: flex;
              }
            }
          }

          .operation{
            padding: 0 48px;
            display: flex;
            flex-direction: column;
            label{
              color: #000000;
              display: inline-flex;
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Fira Sans', 'Droid Sans', 'Helvetica Neue', 'Noto Sans', 'Liberation Sans', Arial, sans-serif;
              font-size: 20px;
              font-weight: 500;
              letter-spacing: 0;
              line-height: 20px;
              text-align: left;
              word-break: break-word;
              align-items: center;

              span{
                width: 32px;
                height: 32px;
                display: inline-flex;
                border: 1px solid #b9c4c9;
                border-radius: 6px;
                -webkit-box-align: center;
                align-items: center;
                -webkit-box-pack: center;
                justify-content: center;
                background: rgb(255, 255, 255);
                flex-shrink: 0;
                margin-right: 16px;
                position: relative;
              }

              img{
                color: #2A2A2A;
                background-color: #2A2A2A;
                width: 100%;
                height: 100%;
                display: inline-block;
                position: absolute;
                left: 50%;
                top: 50%;
                transform: translate(-50%, -50%);
                border-radius: 4px;
              }

              input{
                -webkit-appearance: none;
              }
            }

            #submit{
              margin-bottom: 16px;
              margin-top: 24px;
              width: 100%;

              img{
                display: inline-block;
                width: 32px;
                height: 32px;
                margin-right: 24px;
              }
            }
          }
        }
      }
    }

    .checkout-wrapper {
      height: 100%;
      width: 100%;
      box-sizing: border-box;
      overflow-y: auto;
      background-color: rgb(240, 242, 245);

      .inner-wrapper{
        max-width: 1200PX;
        margin: 0 auto;
      }
    }
  }
}

@media screen and (min-width: 1200PX){
  .checkout-page-wrapper{

    .content-wrapper{
      display: flex;
      flex-direction: row-reverse;
      align-items: flex-start;
      max-width: 1200PX;
      margin: 30px auto 0;

      .card-option-list{
        .history-card-item,
        .new-card-item{
          border-radius: 12px;
          margin-bottom: 8px;

          .card-info{
            padding: 12px 16px 12px 14px;

            .selected-status{
              width: 16px;
              height: 16px;
              margin-right: 14px;

              &.active{
                &:after{
                  width: 6px;
                  height: 6px;
                }
              }
            }

            img{
              width: 40px;
              height: 26px;
              margin-right: 8px;
            }

            .card-number{
              font-size: 14px;
            }

            .support-card-list{
              img{
                width: 24px;
                height: 16px;
                margin: 0 2px;
              }

              span{
                font-size: 13px;
                margin-left: 2px;
              }
            }
          }

          .form-row-wrapper{
            padding: 0 16px;

            .form-row-item{
              span{
                height: 13px;
                font-size: 12px;
                line-height: 1;
                margin-bottom: 5px;
              }

              .disabled-input{
                padding: 5px 8px;
                border: 1px solid #e6e9eb;
                line-height: 25px;
                border-radius: 5px;
                font-size: 15px;

                &:active{
                  border-color: #06f;
                  box-shadow: 0 0 3px 1px #06f;
                }
              }

              input{
                line-height: 12px;
                -webkit-tap-highlight-color: transparent !important;
                height: 38px;
                border-radius: 5px;
                text-indent: 5px;
                font-size: 15px;

                &:active, &:focus{
                  box-shadow: 0 0 2px 1px #06f;
                }

                &::-webkit-input-placeholder{
                  font-size: 16px;
                }
              }

              .cvc-find-position-wrapper{
                right: 8px;
                top: 34px;
              }

              .error-cvc__red-no{
                position: absolute;
                right: 15px;
                top: 37px;
                transform: translateY(-50%);
              }

              .error-cvc_span{
                margin-top: 4px;
                font-size: 12px;
                line-height: 15px;
              }

              &.cvc-wrapper{
                margin-left: 25px;

                .cvc-error{
                  color: #d10244;
                }
              }
            }
          }

          button{
            width: calc(100% - 32px);
            margin: 24px auto 15px;
            padding: 15px;
            line-height: 18px;
            font-size: 15px;
            border-radius: 6px;

            img{
              width: 16px;
              height: 16px;
              margin-right: 12px;
            }
          }

          &:nth-of-type(n+1){
            margin-top: 4px;
          }
        }

        .new-card-item{
          .new-card-wrapper{
            padding-bottom: 12px;

            .operation{
              padding: 0 24px;
              label{
                font-size: 12px;
                line-height: 1;

                span{
                  width: 16px;
                  height: 16px;
                  border-radius: 3px;
                  margin-right: 8px;

                  img{
                    border-radius: 2px;
                  }
                }
              }

              #submit{
                margin-bottom: 8px;
                margin-top: 12px;

                img{
                  display: inline-block;
                  width: 16px;
                  height: 16px;
                  margin-right: 12px;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
