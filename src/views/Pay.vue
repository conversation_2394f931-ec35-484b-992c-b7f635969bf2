<template>
  <div :class="['shopping-wrapper', $gameName, { sdk: IS_CHECKOUT_SDK }]" id="page-pay-wrapper">
    <template v-if="isPc && !IS_CHECKOUT_SDK">
      <section class="pc-content-wrapper">
        <add-screen-btn></add-screen-btn>
        <!-- iOS safari... -->
        <add-ios-safari-btn></add-ios-safari-btn>
        <div class="page-title">{{ $vt('pageTitle') }} <span>{{ $t('mobile_available') }} <i></i></span></div>
        <div class="content-center content-center__main">
          <div class="left-part">
            <div class="logo"></div>
            <div class="name"></div>
            <p class="description">
              <span>{{ $vt('whatIsDiamondTitle') }}</span>
              <span v-if="$store.state.functionSwitch.showPcDiscountTips">{{ $vt('discount95Tips') }}</span>
            </p>
            <div class="charge-construction" @click="$root.$emit('showPop', 'ChargeConstruction')">
              <i></i>{{ $t('construction_title') }}
            </div>
          </div>
          <div class="right-part">
            <login-module></login-module>
            <entrance-of-boon v-if="boon"></entrance-of-boon>
            <coupon-choose></coupon-choose>
            <diamond-choose-k-o-a></diamond-choose-k-o-a>
            <channel-choose></channel-choose>
            <checkout-counter-cn v-if="$store.state.gameinfo.isCn"></checkout-counter-cn>
            <checkout-counter v-else></checkout-counter>
            <div class="shop-btn">
              <span class="click-btn" :class="[{disable: requestLoading || $store.getters['riskPolicy/forbiddenAccess']}]" @click="judgeRisk()">{{ $t('shop_now') }} <i v-if="vip.isNewUser || !isLogin"></i></span>
            </div>

            <common-part v-if="$store.state.country === 'DE' && isLogin">
              <private-permission></private-permission>
            </common-part>
          </div>
        </div>
      </section>

      <common-footer-cn v-if="$store.state.gameinfo.isCn"></common-footer-cn>
      <CommonFooterPuzala v-if="$store.state.gameinfo.mainBody"></CommonFooterPuzala>
      <common-footer v-else></common-footer>
    </template>
    <template v-if="isMobile && !IS_CHECKOUT_SDK">
      <div class="mobile-body-wrapper">
        <add-screen-btn></add-screen-btn>
        <!-- iOS safari... -->
        <add-ios-safari-btn></add-ios-safari-btn>
        <login-module></login-module>
        <entrance-of-boon v-if="boon"></entrance-of-boon>
        <coupon-choose></coupon-choose>
        <diamond-choose-k-o-a></diamond-choose-k-o-a>
        <channel-choose></channel-choose>
        <common-part v-if="$store.state.country === 'DE' && isLogin">
          <private-permission></private-permission>
        </common-part>
        <refund-policy v-else-if="showMobilePolicy"></refund-policy>
        <common-footer-cn v-if="$store.state.gameinfo.isCn"></common-footer-cn>
      </div>
      <checkout-footer @purchaseGoods="judgeRisk()" :request-loading="requestLoading"></checkout-footer>
    </template>

    <!--  通用SDK接入到商城，共用一套代码  -->
    <template v-if="IS_CHECKOUT_SDK">
      <div class="sdk-body-wrapper">
        <direct-gift-package></direct-gift-package>
        <coupon-choose></coupon-choose>
        <channel-choose></channel-choose>
        <common-part v-if="$store.state.country === 'DE' && isLogin">
          <private-permission></private-permission>
        </common-part>
        <checkout-counter-s-d-k @purchaseGoods="judgeRisk()" :request-loading="requestLoading"></checkout-counter-s-d-k>
        <!-- login 放最下面，等上面组件初始化完成 -->
        <login-module v-show="false"></login-module>
      </div>
      <checkout-footer @purchaseGoods="judgeRisk()" :request-loading="requestLoading"></checkout-footer>
      <common-footer></common-footer>
    </template>
  </div>
</template>

<script>
import CommonFooter from '@/components/CommonFooter'
import LoginModule from '@/components/LoginModule'
import ChannelChoose from '@/components/ChannelChoose'
import CouponChoose from '@/components/coupon/CouponChoose'
import CheckoutCounter from '@/components/CheckoutCounterTax'
import CheckoutCounterCn from '@/components/CheckoutCounterCN'
import { ameDoByGet } from '@/server'
import CheckoutFooter from '@/components/mobile/CheckoutFooterTax'
import AddScreenBtn from '@/components/AddScreenBtn'
import AddIosSafariBtn from '@/components/AddIosSafariBtn'
import DiamondChooseKOA from '@/components/DiamondChooseKOA'
import EntranceOfBoon from '@/components/EntranceOfBoon'
import CommonPart from '@/components/common/CommonPart.vue'
import DirectGiftPackage from '@/components/product/DirectGiftPackage'
import CheckoutCounterSDK from '@/components/CheckoutCounterSDK.vue'

import PayMixin from '@/views/PayMixin'

export default {
  name: 'Pay',
  components: {
    CheckoutCounterSDK,
    CommonPart,
    RefundPolicy: () => import(/* webpackChunkName: 'chunk-functions' */'@/components/mobile/RefundPolicy'),
    EntranceOfBoon,
    DiamondChooseKOA,
    AddScreenBtn,
    AddIosSafariBtn,
    CheckoutFooter,
    CheckoutCounter,
    CouponChoose,
    ChannelChoose,
    LoginModule,
    CommonFooter,
    CheckoutCounterCn,
    CommonFooterCn: () => import(/* webpackChunkName: 'chunk-functions' */'@/components/game/koa/CommonFooter.vue'),
    CommonFooterPuzala: () => import(/* webpackChunkName: 'chunk-functions' */'@/components/common/CommonFooterPuzala.vue'),
    PrivatePermission: () => import(/* webpackChunkName: 'chunk-functions' */'@/components/privatePermission.vue'),
    DirectGiftPackage
  },
  mixins: [PayMixin],
  methods: {
    showDiamondPop () {
      this.$root.$on('showWhatIsDiamondPop', () => {
        const gallery = this.$imageLoader('whatsDiamond', [])

        // 创建img标签加载图片
        const img = new Image()
        img.src = gallery[0].imageUrl
        this.$loading.show()
        // 监听图片加载完成事件
        img.onload = () => {
          this.$root.$emit('showPop', 'WhatIsDiamond')
          this.$loading.hide()
        }
        img.onerror = () => {
          this.$loading.hide()
        }
      })
    },
    initShowDoubleExperience () {
      if (this.IS_CHECKOUT_SDK) return null
      ameDoByGet({
        p0: 'web',
        p1: 11,
        p2: 2195,
        p3: 'api'
      })
        .then(res => {
          const { code, data } = res
          if (code === 0) {
            this.$store.commit('formdata/switchDoubleExperience', data.double_flage || false)
          }
        })
    }
  },
  created () {
    this.isKOA && this.initShowDoubleExperience()
    this.showDiamondPop()
  }
}
</script>

<style scoped lang="scss">
@import "~@/utils/utils.scss";
.shopping-wrapper {
  width: 100%;
  box-sizing: border-box;
  margin: 0 auto;
  text-align: left;

  .content-center {
    margin: 0 auto;
    @media screen and (min-width: 940px) {
      width: 940PX;

      .left-part {
        display: none;
      }
    }

    @media screen and (min-width: 1200px) {
      width: 1200PX;
      .left-part {
        display: block;
      }
    }
  }

  .page-title {
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    color: #FFFFFF;
    @media screen and (min-width: 940.9px) {
      font-size: 30px;
      line-height: 30px;
      height: 55px;
      padding-left: 33px;
      margin-top: 18px;
    }

    position: relative;
    span{
      vertical-align: top;
      height: 24PX;
      line-height: 24PX;
      background: #FDDB70;
      font-size: 14PX;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #633B00;
      padding-right: 8PX;
      padding-left: 24PX;
      display: inline-block;
      margin: 15PX 10PX 0;
      transform: scale(.95) translateY(-50%);
      transform-origin: left top;
      position: relative;

      i{
        @include bgCenter('koa/mobile_yingdao.png', 18PX, 18PX);
        display: inline-block;
        position: absolute;
        left: 6PX;
        top: 50%;
        transform: translateY(-50%);
      }
    }
  }

  .content-center__main {
    margin-top: 7px;
    display: flex;
    justify-content: center;

    /* left-part所有的都用PX */
    .left-part {
      width: 260PX;
      background-color: rgba(255, 255, 255, 0.07);
      overflow: hidden;
      position: relative;
      flex-shrink: 0;
      max-height: 80vh;

      .logo {
        //@include bgCenterForSS('ss-logo.png', 142PX, 142PX);
        margin: 39PX auto 0;
      }

      .name {
        //@include bgCenterForSS('ss-name.png', 190PX, 87PX);
        margin: 36PX auto 0;
      }

      .description {
        font-size: 16PX;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #919090;
        line-height: 22PX;
        width: 206PX;
        margin: 30PX auto 0;

        span{
          display: block;
          //text-indent: 30PX;
          &:nth-of-type(n+2){
            margin-top: 6PX;
          }
        }
      }

      .charge-construction {
        font-size: 18PX;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #FF813C;
        line-height: 25PX;
        letter-spacing: 1PX;
        position: absolute;
        bottom: 50PX;
        left: 50%;
        transform: translateX(-50%);
        margin: 0 auto;
        cursor: pointer;
        text-decoration: underline;
        width: 90%;
        text-align: center;

        i {
          display: inline-block;
          margin: 0 3PX;
          cursor: pointer;
          //@include bgCenter('charge-construction-flag.png', 19PX, 19PX);
          flex-shrink: 0;
          position: relative;
          top: 3PX;
        }
      }
    }

    .right-part {
      width: 0;
      flex-grow: 1;
      padding-bottom: 30PX;
      .shop-btn {
        width: 100%;
        text-align: center;
        margin-top: 15PX;
        overflow: hidden;

        span {
          font-size: 30PX;
          height: 63PX;
          line-height: 63PX;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: #FFFFFF;
          background: #FF5E0F;
          border-radius: 8PX;
          display: inline-block;
          margin: 0 auto;
          padding: 0 61PX;
          cursor: pointer;

          &.disable {
            opacity: .4;
            cursor: not-allowed;
          }
        }
      }
    }
  }
}
@include setPcContent{
  .shopping-wrapper{
    .pc-content-wrapper{
      padding-top: 18PX;
      min-height: calc(100vh - 40PX);
    }

    .content-center{
      min-height: 500PX;
    }
  }
}
@include setMobileContent{
  .shopping-wrapper{
    display: flex;
    flex-direction: column;
    height: 100%;

    .mobile-body-wrapper{
      overflow-y: auto;
      padding-top: 20px;
      flex-grow: 1;
      padding-bottom: 100px;
    }
  }
}

/* 各游戏 */
.shopping-wrapper.koa{
  .content-center__main{
    .left-part{
      .logo {
        @include bgCenter('koa/login/koa-logo.png', 140PX, 140PX);
        margin: 39PX auto 0;
      }

      .name {
        @include bgCenter('koa/login/koa-name.png', 190PX, 18PX);
        margin: 29PX auto 0;
      }

      .charge-construction{
        color: #FEB522;
        i {
          @include bgCenter('koa/charge-construction-flag.png', 19PX, 19PX);
        }
      }
    }
    .right-part{
      .shop-btn{
        overflow: visible;
        span{
          background: linear-gradient(180deg, #F6E190 0%, #CBA455 100%);
          font-size: 24PX;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: #633B00;
          border-radius: 0;
          position: relative;

          i{
            position: absolute;
            right: 0;
            top: 0;
            @include bgCenter('koa/pay-btn-gift.png', 44PX, 46PX);
            display: inline-block;
            transform: translate(50%, -50%);
          }
        }
      }
    }
  }
}
.shopping-wrapper.aof{
  .content-center__main {
    .left-part {
      min-height: 60vh;
      .logo {
        @include bgCenter('koa/aof/aof-logo.png', 140px, 140px);
        margin: 39px auto 0;
      }

      .name {
        @include bgCenter('koa/aof/aof-name.png', 216PX, 20px);
        background-size: contain;
        margin: 21PX auto 0;
      }

      .charge-construction{
        color: #FEB522;
        i {
          @include bgCenter('koa/charge-construction-flag.png', 19PX, 19PX);
        }
      }
    }

    .right-part{
      .shop-btn{
        overflow: visible;
        span{
          background: linear-gradient(180deg, #F6E190 0%, #CBA455 100%);
          font-size: 24PX;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: #633B00;
          border-radius: 0;
          position: relative;

          i{
            position: absolute;
            right: 0;
            top: 0;
            @include bgCenter('koa/pay-btn-gift.png', 44PX, 46PX);
            display: inline-block;
            transform: translate(50%, -50%);
          }
        }
      }
    }
  }
}
.shopping-wrapper.rom{
  .content-center__main {
    .left-part {
      .logo {
        @include bgCenter('koa/rom/logo.png', 140px, 140px);
      }

      .name {
        @include bgCenter('koa/rom/name.png', 231PX, 30PX);
      }

      .charge-construction{
        color: #FEB522;
        i {
          @include bgCenter('koa/charge-construction-flag.png', 19PX, 19PX);
        }
      }
    }

    .right-part{
      .shop-btn{
        overflow: visible;
        span{
          background: linear-gradient(180deg, #F6E190 0%, #CBA455 100%);
          font-size: 24PX;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: #633B00;
          border-radius: 0;
          position: relative;

          i{
            position: absolute;
            right: 0;
            top: 0;
            @include bgCenter('koa/pay-btn-gift.png', 44PX, 46PX);
            display: inline-block;
            transform: translate(50%, -50%);
          }
        }
      }
    }
  }
}
.shopping-wrapper.dc{
  .page-title {
    span{
      background: rgb(53, 121, 209);
      color: #F4FBFF;
      @extend .dc-stroke;
      @extend .dc-btn-decoration;
      line-height: 1.5;

      i{
        @include bgCenterForDC('pwa/pwa-install-guide.png', 18PX, 18PX);
      }
    }
  }
  .content-center__main{
    .left-part{
      background: linear-gradient(to bottom, black, rgba(0,0,0,0));
      min-height: 80vh;
      .logo {
        @include bgCenterForDC('pc/logo.png', 140PX, 140PX);
        margin: 39PX auto 0;
      }

      .name {
        @include bgCenterForDC('pc/name.png', 128px, 64px);
        margin: 20px auto 0;
      }

      .description{
        color: #F4FBFF;
      }

      .charge-construction{
        color: #FFCC66;
        i {
          @include bgCenterForDC('pc/charge-construction-flag.png', 19PX, 19PX);
          margin-right: 4px;
        }
      }
    }
    .right-part{
      .shop-btn{
        overflow: visible;
        text-align: left;
        padding-left: 230px;
        span{
          background: rgba(255, 210, 53);
          font-size: 24PX;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: #393A3E;
          border-radius: 0;
          position: relative;
          width: 426px;
          text-align: center;

          i{
           display: none;
          }

          @extend .dc-btn-decoration;
        }
      }
    }
  }
}
.shopping-wrapper.ssv{
  .page-title {
    span{
      height: 26PX;
      background: #FF5E0F;
      border-radius: 5PX;
      line-height: 26PX;
      position: absolute;
      top: 0;
      margin: 0 16PX;
      transform: scale(.95);
      transform-origin: left top;
      color: #FFFFFF;

      i{
        @include bgCenterForSSV('pc/mobile_yingdao.png', 14PX, 19PX)
      }
    }
  }
  .content-center__main{
    .left-part{
      background-color: rgba(255, 255, 255, 0.07);
      min-height: 80vh;
      .logo {
        @include bgCenterForSSV('pc/ss-logo.png', 140PX, 140PX);
        margin: 39PX auto 0;
      }

      .name {
        @include bgCenterForSSV('pc/ss-name.png', 190PX, 87PX);
        margin: 20px auto 0;
      }

      .description{
        color: #919090;
      }

      .charge-construction{
        color: #FF813C;
        i {
          @include bgCenterForSSV('pc/charge-construction-flag.png', 19PX, 19PX);
          margin-right: 4px;
        }
      }
    }
    .right-part{
      .shop-btn{
        overflow: visible;
        text-align: left;
        padding-left: 230px;
        span{
          color: #FFFFFF;
          background: #FF5E0F;
          font-size: 24PX;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          position: relative;
          width: 426PX;
          text-align: center;

          i{
            display: none;
          }
        }
      }
    }
  }
}

.shopping-wrapper.sdk{
  @include flexCenter;
  flex-direction: column;
  height: 100%;
  .sdk-body-wrapper{
    width: 100%;
    height: 0;
    flex-grow: 1;
  }
  .checkout-footer-wrapper{
    width: 100%;
    position: relative;
  }
  .copyright{
    display: none;
  }

  @include setPcContent{
    .copyright{
      display: flex;
      width: 100%;
    }
  }
}
.shopping-wrapper.ssv2{
  .page-title {
    span{
      height: 26PX;
      background: #FF5E0F;
      border-radius: 5PX;
      line-height: 26PX;
      position: absolute;
      top: 0;
      margin: 0 16PX;
      transform: scale(.95);
      transform-origin: left top;
      color: #FFFFFF;

      i{
        @include bgCenterForSS('pc/mobile_yingdao.png', 14PX, 19PX)
      }
    }
  }
  .content-center__main{
    .left-part{
      background-color: rgba(255, 255, 255, 0.07);
      min-height: 80vh;
      .logo {
        @include bgCenterForSSV2('ss-logo.png', 140PX, 140PX);
        margin: 39PX auto 0;
      }

      .name {
        @include bgCenterForSSV2('ss-name.png', 142PX, 103PX);
        margin: 20px auto 0;
      }

      .description{
        color: #919090;
      }

      .charge-construction{
        color: #FF813C;
        i {
          @include bgCenterForSS('pc/charge-construction-flag.png', 19PX, 19PX);
          margin-right: 4px;
        }
      }
    }
    .right-part{
      .shop-btn{
        overflow: visible;
        text-align: left;
        padding-left: 230px;
        span{
          color: #FFFFFF;
          background: #FF5E0F;
          font-size: 24PX;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          position: relative;
          width: 426PX;
          text-align: center;

          i{
            display: none;
          }
        }
      }
    }
  }
}
</style>
