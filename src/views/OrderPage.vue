<template>
  <div class="order-page-wrapper" @click="closeAllSlide">
    <header>
      <div class="logo"></div>
      <div class="right">
        <div class="toggle">
          <div class="now-lang" @click.stop="menuToggle('showToggleLang')">
            {{ langObj[$i18n.locale] }}
            <i :class="{'caret-reverse':showToggleLang}"></i>
          </div>
          <div class="options" v-if="showToggleLang">
              <span v-for="[key,langItem] of Object.entries(langObj)" :key="key" @click="toggleLang(key)">
                {{ langItem }}
              </span>
          </div>
        </div>
        <div class="divider"></div>

        <div class="user-info">
          <div class="info-container" @click.stop="menuToggle('showToggleLogin')">
            <div class="avatar" v-lazy:backgroundImage="userinfo.icon"></div>
            <div :class="[{'no-name': !userinfo.name},'name']">{{ userinfo.name }}</div>
            <i :class="{'caret-reverse': showToggleLogin}"></i>
          </div>
          <div class="options" v-if="showToggleLogin">
            <template v-if="userinfo.isLogin">
              <span @click="logOut">{{ $t('logout') }}</span>
              <span v-if="loginToken && !onlyOneRole" :class="[$i18n.locale]" @click="openUidListPop(true)">{{ $t('switch_character') }}</span>
            </template>
            <span v-else @click="navToLogin($i18n.locale, 2031)">{{ $t('login') }}</span>
          </div>
        </div>
      </div>
    </header>
    <div class="content-wrap">
      <div class="content-title">{{ $t('txt_clear_card_title') }}</div>
      <div class="content-body clear-cache-wrapper">
        <div class="icon"></div>
        <div class="txt">{{ $t('txt_clear_card_desc') }}</div>
        <div class="btn" @click="clearCardCache">{{ $t('txt_clear_card_btn') }}</div>
      </div>
    </div>
    <div class="content-wrap list-wrap">
      <div class="content-title">{{ $t('order-page-title') }}</div>
      <div class="content-body pc-scroll"
          infinite-scroll-distance="100"
          v-infinite-scroll="() => !isPc && togglePage()">
        <section class="order-list-wrapper">
          <template v-for="(orderItem, orderIndex) in orderList">
            <div :key="orderIndex"
              :class="['order-item', {'order-item__open': activeIndex === orderIndex || isPc}]">
              <div class="row-1">
                <div class="order-id">{{ $t('order-page-pay-order') }}：{{ orderItem.order_id }}</div>
                <div class="order-status">{{ $t(orderResultMapKey[orderItem.order_status]) }}</div>
              </div>
              <div class="field">{{ $t('order-page-pay-amount') }}：{{ orderItem.price }} {{ orderItem.currency }}</div>
              <div class="field">{{ $t('order-page-pay-date') }}：{{ orderItem.created_at }}</div>
              <div class="field">{{ $t('order-page-pay-platform') }}：{{ orderItem.source }}</div>
              <div class="field">{{ $t('order-page-pay-method') }}：{{ orderItem.channel_name }}</div>
              <div class="field">
                {{ $t('order-page-pay-discount') }}：
                <template v-if="orderItem.act_type === ''"></template>
                <template v-if="orderItem.act_type === 'deduct'">{{ orderItem.discount }} {{ orderItem.currency }}</template>
                <template v-if="['fixed_discount', 'first_pay', 'coupon'].includes(orderItem.act_type)"> {{ orderItem.discount | rate }} OFF</template>
              </div>

              <div class="toggle-btn" v-if="!isPc" @click="activeIndex = (activeIndex === orderIndex) ? -1 : orderIndex"></div>
            </div>
          </template>
          <div v-if="!orderList.length" class="no-order-wrapper">
              <div class="no-order-image"></div>
              <div class="no-order-txt">{{ $t('nothingHere') }}</div>
          </div>
        </section>
      </div>
    </div>
    <footer v-if="isPc && totalPages > 1">
      <paginate
        :value="pageIndex"
        :page-count="totalPages"
        :click-handler="togglePage"
        prev-text="<"
        next-text=">"
        container-class='paginate-wrapper'>
      </paginate>
    </footer>

    <toggle-info v-if="showToggleUidPop" :uidList='uidList' @close="showToggleUidPop = false" @choose="uid => loadUserInfo(uid)"></toggle-info>
  </div>
</template>

<script>
import Vue from 'vue'
import Paginate from 'vuejs-paginate'
import { langObj } from '@/utils/i18n'
import { mapState } from 'vuex'
import { decryptAES, navToLogin } from '@/utils/utils'
import { ameDoByGet, fetchUidList, getUserInfoForToken, ameDoByGetCommon } from '@/server'
import ToggleInfo from '@/components/toggleInfo'
import { OrderPageOpenidKey, OrderPageTokenKey, OrderPageLangKey } from '@/config/OrderPageConf'
import infiniteScroll from 'vue-infinite-scroll'
Vue.component('paginate', Paginate)
const orderResultMapKey = {
  1: 'order-page-status-ok',
  '-1': 'order-page-status-pending'
}

export default {
  name: 'OrderPage',
  components: { ToggleInfo },
  data () {
    return {
      activeIndex: -1,
      isPc: true,
      orderList: [],
      pageSize: 10,
      pageIndex: 1,
      totalPages: 1,

      showToggleLang: false,
      showToggleLogin: false,
      showToggleUidPop: false,
      langObj,
      uidList: [],
      onlyOneRole: true,
      loginToken: '',
      orderResultMapKey,
      busy: false
    }
  },
  filters: {
    rate (value) {
      return ((1 - value) * 100).toFixed(0) + '%'
    }
  },
  directives: { infiniteScroll },
  methods: {
    menuToggle (key) {
      if (this[key]) {
        this[key] = false
        return null
      }
      this.closeAllSlide()
      this[key] = true
    },
    toggleLang (key) {
      this.$i18n.locale = key
      localStorage.setItem(OrderPageLangKey, key)
    },
    closeAllSlide () {
      this.showToggleLogin = this.showToggleLang = false
    },

    navToLogin,
    logOut () {
      localStorage.removeItem(OrderPageOpenidKey)
      sessionStorage.removeItem(OrderPageTokenKey)
      localStorage.removeItem(OrderPageTokenKey)
      window.location.href = location.origin + location.pathname
    },
    openUidListPop (open) {
      const fetchData = () => {
        this.$loading.show()
        const params = { token: this.loginToken }
        fetchUidList(params)
          .then(res => {
            const { code, data } = res
            sessionStorage.removeItem(OrderPageTokenKey)
            if (code === 0 && data.uid_list && data.uid_list.length >= 1) {
              this.uidList = data.uid_list
              if (data.uid_list.length === 1) {
                this.loadUserInfo(data.uid_list[0].uid)
              } else {
                this.onlyOneRole = false
                this.showToggleUidPop = true
              }
            } else {
              throw new Error(`get error data.uid_list: ${data.uid_list}`)
            }
          })
          .catch(err => console.error(err))
          .finally(() => this.$loading.hide())
      }

      /* case: 1 直接打开 */
      if (open) return fetchData()

      /* case 2：进入页面判断 */
      if (!location.href.includes('token')) {
        this.loadUserInfo()
        return null
      }

      fetchData()
    },
    loadUserInfo (uid) {
      this.closeAllSlide()
      // 获取用户信息
      const userInvalidError = this.$t('login_fail_2')

      const params = {}
      // case 1 uid + fipdToken
      if (uid) {
        params.uid = uid
        params.token = this.loginToken
      }
      // case 2 openid
      const openid = localStorage.getItem(OrderPageOpenidKey)
      if (!uid && openid) {
        params.openid = openid
      }
      if (JSON.stringify(params) === '{}') {
        return null
      }

      this.$loading.show()
      getUserInfoForToken(params)
        .then((res) => {
          let { data, code } = res
          if (code === 0) {
            try {
              const secretKey = this.$gcbk('ids.secretKey')
              if (typeof data === 'string') data = JSON.parse(decryptAES(data, secretKey))
            } catch (e) {
              console.error(`解密失败！${openid || this.uid}`)
            }
            this.$store.commit('orderPage/setUserInfo', data)
            if (uid) this.resetPage()
            this.initList()
          } else {
            this.$toast.err(userInvalidError)
          }
        })
        .catch(err => {
          this.$toast.err(userInvalidError)
          console.error(err)
          setTimeout(() => this.logOut(), 1500)
        })
        .finally(() => this.$loading.hide())
    },
    resetPage () {
      this.orderList = []
      this.orderList.length = 0
      this.totalPages = 1
      this.pageIndex = 1
      this.busy = true
    },

    togglePage (index) {
      if (this.isPc) {
        this.pageIndex = index
      } else {
        const nextIndex = this.pageIndex + 1
        if (nextIndex > this.totalPages) return null
        this.pageIndex = nextIndex
      }
      this.initList()
    },
    initList () {
      this.busy = true
      const params = {
        p0: 'web',
        p1: 7,
        p2: 1122,
        p3: 'api',
        game: 'koa',
        page_size: this.pageSize,
        page: this.pageIndex,
        token: localStorage.getItem(OrderPageTokenKey)
      }
      ameDoByGet(params)
        .then(res => {
          const { code, data } = res
          if (code === 0) {
            const { total, result = [] } = data
            if (this.isPc) {
              this.orderList = result
            } else {
              this.orderList.push(...result)
            }
            this.totalPages = Math.ceil(+total / params.page_size)
          }
        })
        .finally(() => {
          this.busy = false
        })
    },

    initPage () {
      const { openid, l, token } = this.$route.query
      // openid && localStorage.setItem(OrderPageOpenidKey, openid)
      l && localStorage.setItem(OrderPageLangKey, l)
      token && sessionStorage.setItem(OrderPageTokenKey, token)
      token && localStorage.setItem(OrderPageTokenKey, token)
      if (token || openid || l) return this.$router.replace('/order')

      const localOpenid = localStorage.getItem(OrderPageOpenidKey)
      const localLang = localStorage.getItem(OrderPageLangKey)
      const localToken = this.loginToken = sessionStorage.getItem(OrderPageTokenKey)

      const nowLang = localLang || this.$i18n.locale
      const finalLang = Object.keys(langObj).find(key => key === nowLang) || 'en'
      this.toggleLang(finalLang)

      if (localToken) return this.openUidListPop(true)

      if (localOpenid) this.loadUserInfo()
    },
    clearCardCache () {
      if (!this.userinfo.isLogin) {
        navToLogin(this.$i18n.locale, 2031)
        return
      }
      if (this.busy) return
      this.busy = true
      const params = {
        p0: 'web',
        p1: 7,
        p2: 1145,
        p3: 'api',
        game: 'koa'
      }
      ameDoByGetCommon(params)
        .then(res => {
          if (res.code === 0) {
            this.$toast.err(this.$t('txt_clear_card_tips_suc'))
          } else {
            this.$toast.err(this.$t('txt_clear_card_tips_fail'))
          }
        })
        .finally(() => {
          this.busy = false
        })
    }
  },
  computed: {
    ...mapState('orderPage', ['userinfo'])
  },
  created () {
    this.initPage()

    const windowResize = () => {
      this.isPc = window.innerWidth > 940
    }
    windowResize()
    window.addEventListener('resize', () => windowResize())
    this.$root.$on('bodyClick', () => this.closeAllSlide())
  },
  mounted () {
    const scroll = document.querySelector('.pc-scroll')
    scroll && scroll.addEventListener('scroll', () => this.closeAllSlide())
  }
}
</script>

<style scoped lang="scss">
@import "~@/utils/utils.scss";

.order-page-wrapper{
  display: flex;
  flex-direction: column;
  height: 100%;

  header{
    height: 90px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    background: rgba(0,0,0, .6);
    flex-shrink: 0;

    .logo{
      @include bgCenter('koa/order/logo.png', 266px, 25px);
    }

    .right{
      display: flex;
      align-items: center;
      justify-content: center;

      /* 通用 */
      i {
        @include bgCenter('koa/order/toggle-arrow.png', 24px, 24px);
        display: inline-block;
        margin-left: 20px;
        transition: all .2s;

        &.caret-reverse {
          transform-origin: center center;
          transform: rotate(180deg);
        }
      }
      .options {
        display: flex;
        flex-direction: column;
        position: absolute;
        bottom: -26.5px;
        transform: translate(-50%, 100%);
        left: 50%;
        background-color: black;
        text-align: left;
        z-index: 100;
        min-width: 160px;
        padding-top: 10px;
        padding-bottom: 10px;

        span {
          display: inline-block;
          line-height: 46px;
          cursor: pointer;
          font-size: 18px;
          font-weight: 600;
          color: #FFFFFF;
          white-space: nowrap;
          padding-left: 15px;
          padding-right: 10px;

          &:hover {
            background-color: rgba(255, 255, 255, .1);
          }

          &.fr{
            transform: scale(.75);
            transform-origin: left center;
          }

          &.ru{
            transform: scale(.87);
            transform-origin: left center;
          }
        }
      }

      .toggle{
        position: relative;
        height: 100%;
        display: flex;
        align-items: center;
        font-weight: bolder;

        .now-lang{
          font-size: 24px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #FFFFFF;
          line-height: 33px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }

      .divider{
        width: 0;
        height: 40px;
        opacity: 0.5;
        border-left: 1px dashed #FFFFFF;
        margin: 0 40px;
      }

      .user-info {
        height: 100%;
        position: relative;

        .info-container {
          height: 100%;
          display: flex;
          align-items: center;
          cursor: pointer;

          .avatar {
            border-radius: 50%;
            flex-shrink: 0;
            overflow: hidden;
            display: flex;
            @include bgCenter('koa/login/home-default-image.png', 51px, 51px);

            &[lazy=error], &[lazy=loading] {
              background-image: url("~@/assets/koa/login/home-default-image.png") !important;
            }
          }

          .name {
            font-size: 14px;
            font-weight: normal;
            color: #FFFFFF;
            white-space: nowrap;
            margin-left: 10px;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 120px;

            &.no-name {
              width: 0;
            }
          }
        }

        .options {
          bottom: -19.5px;
        }
      }
    }
  }

  .content-wrap {
    margin: 20px;
    background: rgba(0,0,0,0.5);
    border: 1px solid #434343;
    text-align: left;
    display: flex;
    flex-direction: column;
    &+.content-wrap {
      margin-top: 0;
    }
    .content-title {
      font-size: 34px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      margin: 0 25px;
      border-bottom: 1px solid #434343;
      color: #FFFFFF;
      line-height: 48px;
      display: flex;
      align-items: center;
      padding: 15px 0;
    }
  }
  .list-wrap {
    flex: 1;
    overflow: hidden;
  }
  .clear-cache-wrapper {
    align-items: center;
    padding: 20px 25px;
    line-height: 40px;
    div {
      display: inline-block;
    }
    .icon {
      @include bgCenter('koa/order/icon-card.png', 26px, 22px);
    }
    .txt {
      margin-left: 4px;
      margin-right: 20px;
      font-size: 28px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: rgba(255,255,255, .7);
      line-height: 1;
    }
    .btn {
      height: 40px;
      padding: 0 15px;
      border-radius: 4px;
      border: 1px solid #ffffff;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #ffffff;
      line-height: 1;
      transition: all .1s linear;
      cursor: pointer;
      &:hover {
        transform: scale(.95);
      }
    }
  }

  .pc-scroll{
    height: 0;
    flex: 1;
    overflow-y: scroll;

    .order-list-wrapper{

      .order-item{
        padding: 20px 24px;
        border-radius: 4px;
        position: relative;
        transition: all .3s ease-in-out;
        height: calc(20px * 2 + 48px * 3 - 8px);
        overflow: hidden;
        &+.order-item {
          border-top: 1px solid #434343;
        }

        div{
          white-space: nowrap;
          font-size: 28px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #ffffff;
          line-height: 40px;
          text-align: left;

          &.field{
            margin-top: 8px;
            transition: opacity .3s;
            &:nth-of-type(n+4){
              opacity: 0;
            }
          }
        }

        .row-1{
          display: flex;
          align-items: center;
          justify-content: space-between;

          .order-id{
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .order-status{
            flex-shrink: 0;
            margin-left: 30px;
            color: #ffffff;
          }
        }

        .toggle-btn{
          position: absolute;
          right: 24px;
          bottom: 25px;
          cursor: pointer;

          @include bgCenter('koa/order/toggle-default.png', 30px, 30px);
          transition: transform .3s;
        }

        &.order-item__open{
          height: calc(20px * 2 + 48px * 6 - 8px);

          .field{
            &:nth-of-type(n+4){
              opacity: 1;
            }
          }

          .toggle-btn{
            transform: rotate(180deg);
          }
        }
      }

      .no-order-wrapper{
        height: calc(80vh - 100px);
        display: flex;
        flex-direction: column;
        justify-content: center;

        .no-order-image{
          @include bgCenter('koa/order/no-order.png', 72px, 72px);
          margin: 0 auto;
          flex-shrink: 0;
        }

        .no-order-txt{
          font-size: 28px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #FFFFFF;
          line-height: 40px;
          margin-top: 20px;
          text-align: center;
        }
      }
    }
  }
}

@include setPcContent{
  .order-page-wrapper{
    header{
      height: 70px;
      padding: 0 30px;

      .logo{
        @include bgCenter('koa/order/logo.png', 266px, 25px);
        transform: scale(.9);
        transform-origin: left center;
      }

      .right{
        display: flex;
        align-items: center;
        justify-content: center;

        /* 通用 */
        i {
          @include bgCenter('koa/order/toggle-arrow.png', 20px, 20px);
          display: inline-block;
          margin-left: 16px;
          transition: all .2s;

          &.caret-reverse {
            transform-origin: center center;
            transform: rotate(180deg);
          }
        }
        .options {
          display: flex;
          flex-direction: column;
          position: absolute;
          bottom: -18.5px;
          transform: translate(-50%, 100%);
          left: 50%;
          background-color: black;
          text-align: left;
          z-index: 100;
          min-width: 150px;
          padding-top: 10px;
          padding-bottom: 10px;

          span {
            display: inline-block;
            line-height: 46px;
            padding-left: 15px;
            cursor: pointer;
            font-size: 16px;
            color: #FFFFFF;
            white-space: nowrap;

            &:hover {
              background-color: rgba(255, 255, 255, .1);
            }

            &.fr{
              transform: scale(.75);
              transform-origin: left center;
            }

            &.ru{
              transform: scale(.87);
              transform-origin: left center;
            }
          }
        }

        .toggle{
          position: relative;
          height: 100%;
          display: flex;
          align-items: center;
          font-weight: bolder;
          cursor: pointer;

          .now-lang{
            font-size: 17px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #FFFFFF;
            line-height: 33px;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }

        .divider{
          width: 0;
          height: 25px;
          opacity: 0.5;
          border-left: 1px dashed #FFFFFF;
          margin: 0 40px;
        }

        .user-info {
          height: 100%;
          position: relative;

          .info-container {
            height: 100%;
            display: flex;
            align-items: center;
            cursor: pointer;

            .avatar {
              border-radius: 50%;
              flex-shrink: 0;
              overflow: hidden;
              display: flex;
              @include bgCenter('koa/login/home-default-image.png', 45px, 45px);

              &[lazy=error], &[lazy=loading] {
                background-image: url("~@/assets/koa/login/home-default-image.png") !important;
              }
            }

            .name {
              font-size: 15px;
              font-weight: normal;
              color: #FFFFFF;
              white-space: nowrap;
              margin-left: 10px;
              overflow: hidden;
              text-overflow: ellipsis;
              max-width: 120px;

              &.no-name {
                width: 0;
              }
            }
          }

          .options {
            bottom: -9.5px;
          }
        }
      }
    }
    .pc-section-title{
      font-size: 24px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #FFFFFF;
      line-height: 33px;
      text-align: left;
      max-width: 1200px;
      width: 100%;
      margin: 10px auto 0;
      background: #121B32;
      border-radius: 4px;
      padding: 14px 20px;
    }
    .content-wrap {
      width: 100%;
      max-width: 1200PX;
      margin: 20PX auto;
      .content-title {
        font-size: 24PX;
        margin: 0 30PX;
        border-bottom: 1PX solid #434343;
        line-height: 33PX;
      }
    }

    .clear-cache-wrapper {
      height: 60PX;
      padding: 0 30PX;
      .icon {
        @include bgCenter('koa/order/icon-card.png', 20PX, 17PX);
      }
      .txt {
        margin-left: 4PX;
        margin-right: 20PX;
        font-size: 16PX;
      }
      .btn {
        height: 30PX;
        padding: 0 10PX;
        border-radius: 4PX;
        border: 1PX solid #ffffff;
        font-size: 16PX;
        &:hover {
          transform: scale(.95);
        }
      }
    }

    .pc-scroll{
      .order-list-wrapper{
        max-width: 1200px;
        width: 100%;
        margin: 0 auto 30px;
        padding: 0;

        .order-item{
          padding: 20px 30px;
          border-radius: 0;
          height: calc(20px * 2 + 34px * 3 - 12px);
          border: 1px solid transparent;

          div{
            font-size: 16px;
            font-family: PingFangSC-Regular, PingFang SC;
            line-height: 22px;

            &.field{
              margin-top: 12px;
            }
          }

          &.order-item__open{
            height: calc(20px * 2 + 34px * 6 - 12px);

            .field{
              &:nth-of-type(n+4){
                opacity: 1;
              }
            }
          }

          &:hover{
            border: 1px solid rgba(255, 255, 255, 1);
          }
        }

        .no-order-wrapper{
          height: auto;
          margin-top: 60px;

          .no-order-image{
            @include bgCenter('koa/order/no-order.png', 48px, 48px);
          }

          .no-order-txt{
            font-size: 16px;
            line-height: 22px;
            margin-top: 10px;
          }
        }
      }
    }

    footer{
      height: 77px;
      flex-shrink: 0;

      .paginate-wrapper{
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto;

        ::v-deep{
          li{
            width: 32px;
            height: 32px;
            border-radius: 4px;
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #ffffff;
            border: 1px solid rgba(69, 100, 116, 1);
            margin: 0 8px;
            background: rgba(0, 0, 0, 0.5);

            a{
              width: 100%;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
            }

            &.active{
              background: transparent;
              border-color: #E5DBCF;
              color: rgba(255, 255, 255, 1);
            }

            &.disabled{
              border: none;
              background: transparent;
              margin: 0;
              a{
                cursor: default;
              }
            }

            &:first-of-type, &:last-of-type{
              background: rgba(229, 219, 207, 1);
              color: #000;
              margin: 0 8px;

              &.disabled{
                opacity: .5;
              }
            }
          }
        }
      }
    }
  }
}
</style>
