<script>
import checkoutCounter from '@/components/CheckoutCounter.js'

export default {
  name: 'CheckoutCounterSDK',
  props: ['requestLoading'],
  mixins: [checkoutCounter]
}
</script>

<template>
  <common-part class="checkout-counter-sdk-b" :labelFont="$t('totalPrice')" :class="[$gameName]" id="checkout-counter-sdk-b">
    <div v-if="expandMode" class="expand" :class="[$gameName]">
      <!--sub total-->
      <div class="price-wrapper">
        <span class="sub-total">{{ $t('tax-sub-total') }}:&nbsp;&nbsp;</span>
        <span class="now-price" :class="{'is-ar-zone': isArZone}">{{ FinalPriceState.rawNowPrice }}</span>
        <span v-if="FinalPriceState.rawOriginPrice" :class="['origin-price', {'is-ar-zone': isArZone}]">{{ FinalPriceState.rawOriginPrice }}</span>
        <div v-if="FinalPriceState.offCountTips" class="off-count-tips" v-html="FinalPriceState.offCountTips"></div>
      </div>
      <!--Tax-->
      <div class="tax-wrapper" v-if="taxCost">{{ $t('tax-txt') }}:&nbsp;&nbsp;{{ taxCost }} {{ currencyUnit }}</div>
      <!--额外费用-->
      <div class="tax-wrapper" v-if="extraCost">{{ $t('extra-txt') }}:&nbsp;&nbsp;{{ extraCost }} {{ currencyUnit }}</div>
      <!--最终价格-->
      <div class="final-price">{{ FinalPriceState.finalNowPrice }}</div>
      <span class="rate" @click="expandMode = !expandMode" :class="{active: expandMode}">+ {{ $t('tax-txt') }}<i></i></span>
    </div>
    <div v-else class="normal" :class="[$gameName]">
      <div class="price-wrapper">
        <span class="now-price" :class="{'is-ar-zone': isArZone}">{{ FinalPriceState.finalNowPrice || '-' }}</span>
        <span v-if="FinalPriceState.finalOriginPrice" class="origin-price" :class="{'is-ar-zone': isArZone}">{{ FinalPriceState.finalOriginPrice }}</span>
        <div v-if="FinalPriceState.offCountTips" class="off-count-tips" v-html="FinalPriceState.offCountTips"></div>
      </div>
      <span class="rate" v-if="showTaxBtn" @click="expandMode = !expandMode" :class="{active: expandMode}">+ {{ $t('tax-txt') }}<i></i></span>
    </div>
    <div class="click-btn" :class="[{disable: requestLoading || $store.getters['riskPolicy/forbiddenAccess']}]" @click="$emit('purchaseGoods')">
      {{ $t('shop_now') }}
      <i v-if="vip.isNewUser"></i>
    </div>
  </common-part>
</template>

<style scoped lang="scss">
@import "~@/utils/utils.scss";
.normal{
  .price-wrapper {
    font-weight: bold;
    color: #FFFFFF;
    line-height: 38px;
    @include flexCenter;
    .is-ar-zone {
      display: inline-block;
    }
    .now-price{
      font-size: 28PX;
    }
    .origin-price {
      text-decoration: line-through;
      font-weight: bold;
      color: #989DA6;
      font-size: 18PX;
      margin-left: 10PX;
    }
    .off-count-tips {
      display: inline-block;
      background: #FEB522;
      position: relative;
      font-weight: bold;
      color: #735100;
      font-size: 10PX;
      top: -19PX;
      left: 5PX;
      padding: 0 6PX;
      border-radius: 4PX;
      height: 18PX;
      line-height: 18PX;

      ::v-deep{
        .diamond-icon {
          @include bgCenterForKoaIcon('koa/diamond/diamond.png', 11px, 9px);
          display: inline-block;
          margin-left: 1px;
          position: relative;
        }
      }
    }
  }
  .rate{
    font-size: 18PX;
    font-weight: 400;
    color: #FEB522;
    line-height: 1;
    cursor: pointer;
    margin-top: 8px;
    i{
      @include bgCenter('common/icon/tax-arrow.png', 20PX, 20PX);
      display: inline-block;
      margin-left: 4PX;
      transition: all .3s;
    }

    &.active i{
      transform: rotate(180deg);
    }
  }
  @include flexCenter;
  flex-direction: column;
  align-items: flex-start;
}
.expand{
  @extend .normal;
  .price-wrapper{
    line-height: 30PX;

    .sub-total{
      font-size: 18PX;
      //font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #FFFFFF;
      line-height: 25PX;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .now-price{
      font-weight: normal;
      font-size: 18px;
    }
    .origin-price{
      font-size: 15px;
    }
    .off-count-tips {
      top: -10px;
    }
  }
  .tax-wrapper{
    margin-top: 2PX;
    font-size: 18PX;
    font-weight: 400;
    color: #FFFFFF;
    line-height: 30PX;
  }
  .final-price{
    font-size: 28PX;
    font-weight: 600;
    color: #FFFFFF;
    line-height: 38px;
    @include flexCenter;
  }
}
.click-btn {
  margin-top: 24px;
  width: 426px;
  text-align: center;
  font-size: 24PX;
  height: 63PX;
  line-height: 63PX;
  //font-family: PingFangSC-Semibold, PingFang SC;
  font-weight: 600;
  color: #FFFFFF;
  //background: #FF5E0F;
  display: inline-block;
  cursor: pointer;

  &.disable {
    opacity: .4;
    cursor: not-allowed;
  }
}

.dc{
  .click-btn{
    color: #393A3E;
    background: rgba(255, 210, 53);
    @extend .dc-btn-decoration;
  }
  .price-wrapper{
    .off-count-tips {
      background: #FFE14D;
      color: #393A3E;

      ::v-deep{
        .diamond-icon{
          @include bgCenterForDC('diamond/diamond-icon.png', 12PX, 12PX);
        }
      }
    }
  }
}

/* 移动端不展示 */
@include setMobileContent{
  .checkout-counter-sdk-b{
    display: none;
  }
}
</style>
