<template>
  <div :class="['checkout-footer-wrapper', $gameName, { sdk: IS_CHECKOUT_SDK }]" id="checkout-footer-wrapper" @click.stop>
    <transition name="fade">
      <div class="expand-part" v-if="expandMode">
        <div class="pop-title">{{ $t('tax-details') }}</div>
        <div class="divider"></div>
        <div class="value-wrapper">
          <div class="origin-price">
            <span>{{ $t('tax-price') }}<template v-if="IS_CHECKOUT_SDK">：</template></span>
            <span>{{ chosenDiamond.nowPrice || chosenDiamond.level_currency_price }}{{ currencyUnit }}</span>
          </div>
          <div v-if="FinalPriceState.feType && hideDiscountRow" class="discount">
            <span>{{ $t('tax-discount')}} <template v-if="IS_CHECKOUT_SDK">：</template></span>
            <span>
            <template>
              <template v-if="['first_pay', 'discount_coupon'].includes(chosenCoupon.feType)">
                 - {{chosenCoupon.discount_amount }} {{ currencyUnit }}
              ({{ chosenCoupon.rateWidthOutPercent }}% OFF)
              </template>
              <template v-if="chosenCoupon.feType==='cash_coupon'">
                 - {{chosenCoupon.discount_amount }} {{ currencyUnit }}
              </template>
              <template v-if="FinalPriceState.feType==='fixed_discount_coupon'">
                 - {{FinalPriceState.offCountAmount }} {{ currencyUnit }}
              </template>
            </template>
          </span>
          </div>
          <div class="tax" v-if="taxCost">
            <span>{{ $t('tax-txt') }}<template v-if="IS_CHECKOUT_SDK">：</template></span>
            <span>{{ taxCost }}{{ currencyUnit }}</span>
          </div>
          <div class="tax" v-if="extraCost">
            <span>{{ $t('extra-txt') }}<template v-if="IS_CHECKOUT_SDK">：</template></span>
            <span>{{ extraCost }}{{ currencyUnit }}</span>
          </div>
        </div>
        <div class="divider"></div>
      </div>
    </transition>
    <div class="common-part">
      <div class="total-price">
        <div class="row-1">
          <span class="now-price" :class="{'is-ar-zone': isArZone}">{{ FinalPriceState.finalNowPrice }}</span>
          <span v-if="showTaxBtn" @click="expandMode = !expandMode" class="rate" :class="{active: expandMode}">+ {{ $t('tax-txt') }}<i></i></span>
        </div>
        <div class="row-2">
          <span v-if="FinalPriceState.finalOriginPrice" :class="['origin-price', {'is-ar-zone': isArZone}]">{{ FinalPriceState.finalOriginPrice }}</span>
          <span v-html="FinalPriceState.offCountTips" v-if="FinalPriceState.offCountTips" class="off-count-tips" :class="{ 'off-count-left': !hideDiscountRow }"></span>
        </div>
      </div>
      <div class="btn click-btn" :class="[{disable: requestLoading || $store.getters['riskPolicy/forbiddenAccess']}, $i18n.locale]"  @click="$emit('purchaseGoods')">
        <span>{{ $t('shop_now') }}</span>
        <i v-if="vip.isNewUser"></i>
      </div>
    </div>
  </div>
</template>

<script>
import {mapGetters, mapState} from 'vuex'

export default {
  name: 'CheckoutFooterTax',
  props: ['requestLoading'],
  data () {
    return {
      expandMode: false
    }
  },
  computed: {
    ...mapState(['urlParams', 'isArZone', 'currencyUnit', 'IS_CHECKOUT_SDK']),
    ...mapState('formdata', ['chosenChannel', 'chosenDiamond', 'chosenCoupon', 'vip']),
    ...mapState('gameinfo', ['defaultDiscount', 'gameCode']),
    ...mapState('userinfo', ['isLogin']),
    ...mapGetters('formdata', ['FinalPriceState', 'getRebateCoin', 'getSDKRebateCoin']),
    taxCost () {
      return this.chosenCoupon.taxation || this.FinalPriceState.taxation || this.chosenDiamond.taxation
    },
    extraCost () {
      return this.chosenCoupon.extra_fee_amount || this.FinalPriceState.extra_fee_amount || this.chosenDiamond.extra_fee_amount
    },
    showTaxBtn () {
      return this.taxCost || this.extraCost
    },
    hideDiscountRow(){
      if (!this.FinalPriceState.feType) return false
      return !this.FinalPriceState.feType.includes('rebate')
    }
  },
  watch: {
    showTaxBtn (newValue) {
      if (!newValue) this.expandMode = false
    }
  },
  mounted () {
    this.$root.$on('BodyClick', () => {
      this.expandMode = false
    })
  }
}
</script>

<style scoped lang="scss">
@import "~@/utils/utils.scss";
.fade-enter-active, .fade-leave-active{
  transition: all .3s;
}
.fade-enter{
  transform: translateY(-80%)!important;
  opacity: 0;
}
.fade-leave-to{
  transform: translateY(-80%)!important;
  opacity: 0;
}

.is-ar-zone {
  display: inline-block;
}
.checkout-footer-wrapper{
  background: #192136;
  position: relative;

  .expand-part{
    padding: 0 42px 0;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    transform: translateY(calc(-100% + 2px));
    border-top-right-radius: 20px;
    border-top-left-radius: 20px;
    background: #192136;
    z-index: 10;

    .pop-title{
      font-size: 28px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #FFFFFF;
      line-height: 40px;
      padding: 20px 0;
    }

    .value-wrapper{
      padding-top: 5px;
      padding-bottom: 20px;

      div{
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 15px;
        span:first-of-type{
          font-size: 20px;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: #FFFFFF;
          line-height: 28px;
        }

        span:last-of-type{
          font-size: 24px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #FEB522;
          line-height: 33px;
        }
      }
    }

    .divider{
      height: 1px;
      opacity: 0.3;
      border: 1px solid #979797;
    }
  }

  .common-part{
    display: flex;
    align-items: center;
    height: 120px;
    flex-shrink: 0;
    padding: 0 42px 0;
    position: relative;
    z-index: 2;
    background: #192136;

    .total-price{
      display: flex;
      white-space: nowrap;
      align-items: flex-start;
      justify-content: center;
      flex-direction: column;

      .row-1{
        display: flex;
        align-items: center;
        .now-price{
          font-size: 36px;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: #FEB522;
          line-height: 50px;
        }
        .rate{
          font-size: 18px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #FEB522;
          line-height: 25px;
          margin-left: 20px;
          display: flex;
          align-items: center;

          i{
            @include bgCenter('common/icon/tax-arrow.png', 16px, 16px);
            display: inline-block;
            margin-left: 4px;
            transition: all .3s;
          }

          &.active i{
            transform: rotate(180deg);
          }
        }
      }

      .row-2{
        display: flex;
        align-items: center;

        .origin-price {
          text-decoration: line-through;
          font-weight: 400;
          color: #BCBCBC;
          font-size: 18px;
          font-family: PingFangSC-Regular, PingFang SC;
          line-height: 25px;
        }

        .off-count-tips{
          height: 20px;
          background: #FEB522;
          border-radius: 4px;
          font-size: 14px;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          line-height: 20px;
          padding: 0 8px;
          margin-left: 10px;
          color: #633B00;

          ::v-deep{
            .diamond-icon {
              @include bgCenter('koa/diamond/diamond.png', 13px, 11px);
              display: inline-block;
              margin-left: 2px;
              position: relative;
            }
          }
        }

        .off-count-left {
          margin-left: 2px;
        }
      }
    }

    .btn{
      width: 227px;
      height: 73px;
      font-size: 30px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      text-align: center;
      line-height: 73px;
      margin-left: auto;

      background: linear-gradient(180deg, #F6E190 0%, #CBA455 100%);
      border-radius: 0;
      color: #633B00;
      position: relative;

      &.de, &.id, &.vi, &.my{
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 25px;
        line-height: 30px;
        padding: 0 3px;
      }

      &.disable {
        opacity: .4;
        cursor: not-allowed;
      }

      i{
        position: absolute;
        right: 0;
        top: 0;
        @include bgCenter('koa/pay-btn-gift.png', 44px, 46px);
        display: inline-block;
        transform: translate(50%, -50%);
      }
    }
  }
}

.checkout-footer-wrapper.dc{
  background-color: rgb(50, 50, 72);

  .common-part{
    background: rgb(50, 50, 72);

    .row-1{
      .now-price{
        color: #FFE14D;
        @extend .dc-stroke;
      }
    }

    .row-2{
      .off-count-tips{
        background: #FFE14D;
        color: #393A3E;
        ::v-deep{
          .diamond-icon {
            @include bgCenterForDC('diamond/diamond-icon.png', 14px, 14px);
            margin-left: 0;
            display: inline-block;
            top: 1px;
          }
        }
      }
    }

    .btn{
      @include bgCenterForDC('checkout/checkout-foooter-btn.png', 227px, 73px);
      color: #393A3E;
      i{
        display: none;
      }
    }
  }
  .expand-part{
    background: rgb(50, 50, 72);
  }
}
.checkout-footer-wrapper.ssv{
  background: #404040;
  .expand-part{
    background: #404040;

    .value-wrapper{
      div{
        span:last-of-type{
          color: #FF5E0F;
        }
      }
    }
  }
  .common-part{
    background: #404040;

    .total-price{
      .row-1{
        .now-price{
          color: #FF5E0F;
        }
        .rate{
          color: #FF5E0F;
          i{
            @include bgCenterForSSV('checkout/tax-arrow.png', 16px, 16px);
          }
        }
      }
      .row-2{
        .off-count-tips{
          background: #FF5E0F;
          border-radius: 4px;
          font-weight: 600;
          color: #FFFFFF;
        }
      }
    }

    .btn{
      background: #FF5E0F;
      color: #FFFFFF;
      border-radius: 8px;
      i{
        display: none;
      }
    }
  }
}
.checkout-footer-wrapper.sdk{
  @include setPcContent{
    display: none;
  }
}
.checkout-footer-wrapper.ssv2{
  background: #404040;
  position: relative;

  .expand-part{
    padding: 0 42px 0;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    transform: translateY(calc(-100% + 2px));
    border-top-right-radius: 20px;
    border-top-left-radius: 20px;
    background: #404040;
    z-index: 10;

    .pop-title{
      font-size: 28px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #FFFFFF;
      line-height: 40px;
      padding: 20px 0;
    }

    .value-wrapper{
      padding-top: 5px;
      padding-bottom: 20px;

      div{
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 15px;
        span:first-of-type{
          font-size: 20px;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: #FFFFFF;
          line-height: 28px;
        }

        span:last-of-type{
          font-size: 24px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #FF5E0F;
          line-height: 33px;
        }
      }
    }

    .divider{
      height: 1px;
      opacity: 0.3;
      border: 1px solid #979797;
    }
  }

  .common-part{
    display: flex;
    align-items: center;
    height: 120px;
    flex-shrink: 0;
    padding: 0 42px 0;
    position: relative;
    z-index: 2;
    background: #404040;

    .total-price{
      display: flex;
      white-space: nowrap;
      align-items: flex-start;
      justify-content: center;
      flex-direction: column;

      .row-1{
        display: flex;
        align-items: center;
        .now-price{
          font-size: 36px;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: #FF5E0F;
          line-height: 50px;
        }
        .rate{
          font-size: 18px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #FF5E0F;
          line-height: 25px;
          margin-left: 20px;
          display: flex;
          align-items: center;

          i{
            @include bgCenterForSS('checkout/tax-arrow.png', 16px, 16px);
            display: inline-block;
            margin-left: 4px;
            transition: all .3s;
          }

          &.active i{
            transform: rotate(180deg);
          }
        }
      }

      .row-2{
        display: flex;
        align-items: center;

        .origin-price {
          text-decoration: line-through;
          font-weight: 400;
          color: #BCBCBC;
          font-size: 18px;
          font-family: PingFangSC-Regular, PingFang SC;
          line-height: 25px;
        }

        .off-count-tips{
          height: 20px;
          background: #FF5E0F;
          border-radius: 4px;
          font-size: 14px;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: #FFFFFF;
          line-height: 20px;
          padding: 0 8px;
          margin-left: 10px;
        }
      }
    }

    .btn{
      width: 227px;
      height: 73px;
      background: #FF5E0F;
      border-radius: 8px;
      font-size: 30px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #FFFFFF;
      text-align: center;
      line-height: 73px;
      margin-left: auto;

      &.de{
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 25px;
        line-height: 30px;
        padding: 0 3px;
      }

      &.disable {
        opacity: .4;
        cursor: not-allowed;
      }

      i{
        display: none;
      }
    }
  }
}
</style>
