<template>
<span class="outer" ref="outer">
  <span class="inner" :style="`transform: scale(${scale})`" ref="inner">
    <slot></slot>
  </span>
</span>
</template>

<script>
export default {
  name: 'OverSizeScale',
  data () {
    return {
      scale: 1
    }
  },
  methods: {
    calc () {
      const outer = this.$refs.outer.offsetWidth
      const inner = this.$refs.inner.offsetWidth
      if (inner > outer) this.scale = outer / inner
    }
  },
  mounted () {
    this.calc()
  }
}
</script>

<style scoped lang="scss">
.outer{
  width: 100%;
  overflow: hidden;
  display: inline-block;

  .inner{
    transform-origin: left center;
    display: inline-block;
    white-space: nowrap;
    line-height: 1;
  }
}
</style>
