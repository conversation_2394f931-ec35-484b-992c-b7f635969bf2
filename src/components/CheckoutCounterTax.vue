<template>
  <common-part v-if="expandMode" :class="['expand', $gameName]" id="checkout-counter-expand">
    <template #label>
      <div class="sub-total">{{ $t('tax-sub-total') }}</div>
      <div class="tax" v-if="taxCost">{{ $t('tax-txt') }}</div>
      <div class="tax" v-if="extraCost">{{ $t('extra-txt') }}</div>
      <div class="total"> {{ $t('totalPrice') }}</div>
      <span class="rate" @click="expandMode = !expandMode" :class="{active: expandMode}">+ {{ $t('tax-txt') }}<i></i></span>
    </template>
    <!--sub total-->
    <div class="price-wrapper">
      <span class="now-price" :class="{'is-ar-zone': isArZone}">{{ FinalPriceState.rawNowPrice }}</span>
      <span v-if="FinalPriceState.rawOriginPrice" :class="['origin-price', {'is-ar-zone': isArZone}]">{{ FinalPriceState.rawOriginPrice }}</span>
      <div v-if="FinalPriceState.offCountTips" class="off-count-tips" v-html="FinalPriceState.offCountTips"></div>
    </div>
    <!--Tax-->
    <div class="tax-wrapper" v-if="taxCost">{{ taxCost }} {{ currencyUnit }}</div>
    <!--额外费用-->
    <div class="tax-wrapper" v-if="extraCost">{{ extraCost }} {{ currencyUnit }}</div>
    <!--最终价格-->
    <div class="final-price">{{ FinalPriceState.finalNowPrice }}</div>
  </common-part>
  <common-part v-else :class="['normal', $gameName]" id="checkout-counter-normal">
    <template #label>
      <div class="total">{{ $t('totalPrice') }}</div>
      <span class="rate" v-if="showTaxBtn" @click="expandMode = !expandMode" :class="{active: expandMode}">+ {{ $t('tax-txt') }}<i></i></span>
    </template>
    <div class="total-price" id="total-price">
      <span class="now-price" :class="{'is-ar-zone': isArZone}">{{ FinalPriceState.finalNowPrice }}</span>
      <span v-if="FinalPriceState.finalOriginPrice" class="origin-price" :class="{'is-ar-zone': isArZone}">{{ FinalPriceState.finalOriginPrice }}</span>
      <div v-if="FinalPriceState.offCountTips" class="off-count-tips" v-html="FinalPriceState.offCountTips"></div>
    </div>
  </common-part>
</template>

<script>
import checkoutCounter from '@/components/CheckoutCounter.js'

export default {
  name: 'CheckoutCounterTax',
  mixins: [checkoutCounter]
}
</script>

<style scoped lang="scss">
@import "~@/utils/utils.scss";
.normal{
  color:#000;
  ::v-deep{
    .label{
      position: relative;
      .total{
        font-size: 22PX;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #FFFFFF;
        line-height: 30PX;
      }
      .rate{
        font-size: 16PX;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #FEB522;
        line-height: 22PX;
        cursor: pointer;
        i{
          @include bgCenter('common/icon/tax-arrow.png', 14PX, 14PX);
          display: inline-block;
          margin-left: 2PX;
          transition: all .3s;
        }

        &.active i{
          transform: rotate(180deg);
        }
      }
    }
  }

  .total-price {
    font-weight: bold;
    color: #FFFFFF;
    line-height: 30PX;

    .is-ar-zone {
      display: inline-block;
    }

    .now-price{
      font-size: 26PX;
    }

    .origin-price {
      text-decoration: line-through;
      font-weight: bold;
      color: #7F7F7F;
      font-size: 16PX;
      margin-left: 10PX;
    }

    .off-count-tips {
      display: inline-block;
      background: #FEB522;
      position: relative;
      font-weight: bold;
      color: #735100;
      font-size: 10PX;
      top: -19PX;
      left: 5PX;
      padding: 0 6PX;
      border-radius: 4PX;
      height: 18PX;
      line-height: 18PX;

      &:after,&:before{
        content: ' ';
        height: 2PX;
        width: 2PX;
        background-color: black;
        left: 30%;
        position: absolute;
        z-index: 1;
        transform: translateX(-50%);
      }

      &:after{
        top: 0;
        border-bottom-left-radius: 50%;
        border-bottom-right-radius: 50%;
      }

      &:before{
        bottom: 0;
        border-top-left-radius: 50%;
        border-top-right-radius: 50%;
      }

      ::v-deep{
        .diamond-icon {
          @include bgCenterForKoaIcon('koa/diamond/diamond.png', 11px, 9px);
          display: inline-block;
          margin-left: 1px;
          position: relative;
        }
      }
    }
  }
}
.expand{
  @extend .normal;

  .sub-total{
    font-size: 18PX;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #FFFFFF;
    line-height: 25PX;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .tax{
    font-size: 18PX;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #FFFFFF;
    line-height: 25PX;
    margin-top: 2PX;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .total{
    margin-top: 8px;
    line-height: 42PX!important;
  }

  .price-wrapper{
    display: flex;
    align-items: flex-end;
    height: 25PX;
    position: relative;

    .now-price{
      font-size: 18PX;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #FFFFFF;
      line-height: 25PX;
    }

    .origin-price{
      font-size: 16PX;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #7F7F7F;
      line-height: 22PX;
      margin-left: 20PX;
      text-decoration: line-through;
    }

    .off-count-tips{
      line-height: 16PX;
      background: #FEB522;
      border-radius: 3PX;
      padding: 0 5PX;
      font-size: 10PX;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #735100;
      position: relative;
      align-self: self-start;
      top: -14PX;
      left: -24PX;

      ::v-deep{
        .diamond-icon {
          @include bgCenterForKoaIcon('koa/diamond/diamond.png', 11px, 9px);
          display: inline-block;
          margin-left: 1px;
          position: relative;
        }
      }
    }
  }

  .tax-wrapper{
    margin-top: 2PX;
    font-size: 18PX;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #FFFFFF;
    line-height: 25PX;
  }

  .final-price{
    font-size: 30PX;
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    color: #FFFFFF;
    line-height: 42PX;
    margin-top: 8PX;
  }
}

/* dc */
.normal.dc{
  .off-count-tips{
    background: #FFE14D;
    color: #393A3E;
    ::v-deep{
      .diamond-icon {
        @include bgCenterForDC('diamond/diamond-icon.png', 13px, 13px);
        position: relative;
        top: 2px;
        margin-left: 0;
      }
    }
  }
}
.expand.dc{
  .price-wrapper{
    .off-count-tips{
      background: #FFE14D;
      color: #393A3E;
      ::v-deep{
        .diamond-icon {
          @include bgCenterForDC('diamond/diamond-icon.png', 13px, 13px);
          position: relative;
          top: 2px;
          margin-left: 0;
        }
      }
    }
  }
}

/* ssv */
.normal.ssv{
  ::v-deep{
    .label{
      .rate{
        color: #FF5E0F;
        i{
          @include bgCenterForSSV('checkout/tax-arrow.png', 14PX, 14PX);
          display: inline-block;
          margin-left: 2PX;
          transition: all .3s;
        }

        &.active i{
          transform: rotate(180deg);
        }
      }
    }
  }
  .total-price {
    color: #FF5E0F;
  }
  .origin-price{
    color: #F5895A;
  }
  .off-count-tips {
    background: rgb(255,93,6);
    color: #FFFFFF;
  }
}
.expand.ssv{
  .price-wrapper{
    .origin-price{
      color: #7F7F7F;
    }
  }
  .final-price{
    color: #FF5E0F;
  }
}

.normal.ssv2{
  ::v-deep{
    .label{
      .rate{
        color: #FF5E0F;
        i{
          @include bgCenterForSSV('checkout/tax-arrow.png', 14PX, 14PX);
          display: inline-block;
          margin-left: 2PX;
          transition: all .3s;
        }

        &.active i{
          transform: rotate(180deg);
        }
      }
    }
  }
  .total-price {
    color: #FF5E0F;
  }
  .origin-price{
    color: #F5895A;
  }
  .off-count-tips {
    background: rgb(255,93,6);
    color: #FFFFFF;
  }
}
.expand.ssv2{
  .price-wrapper{
    .origin-price{
      color: #7F7F7F;
    }
  }
  .final-price{
    color: #FF5E0F;
  }
}
</style>
