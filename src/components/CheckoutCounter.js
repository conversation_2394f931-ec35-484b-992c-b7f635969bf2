import CommonPart from '@/components/common/CommonPart.vue'
import { mapGetters, mapState } from 'vuex'

export default {
  name: 'CheckoutCounterTax',
  components: { CommonPart },
  data () {
    return {
      expandMode: false
    }
  },
  computed: {
    ...mapState(['urlParams', 'isArZone', 'currencyUnit', 'IS_CHECKOUT_SDK']),
    ...mapState('formdata', ['chosenChannel', 'chosenDiamond', 'chosenCoupon', 'vip']),
    ...mapState('gameinfo', ['defaultDiscount']),
    ...mapState('userinfo', ['isLogin']),
    ...mapGetters('formdata', ['FinalPriceState', 'getRebateCoin', 'getSDKRebateCoin']),
    taxCost () {
      return this.chosenCoupon.taxation || this.FinalPriceState.taxation || this.chosenDiamond.taxation
    },
    extraCost () {
      return this.chosenCoupon.extra_fee_amount || this.FinalPriceState.extra_fee_amount || this.chosenDiamond.extra_fee_amount
    },
    showTaxBtn () {
      return this.taxCost || this.extraCost
    }
  },
  watch: {
    showTaxBtn (newValue) {
      if (!newValue) this.expandMode = false
    }
  }
}
