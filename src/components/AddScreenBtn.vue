<template>
  <div :class="['add-screen-pc-wrapper', [$gameName]]">
    <transition name="addScreen">
      <div v-if="deferredPrompt&&$store.state.isMobile" @click="goInstall" class="add-to-main-screen__mobile">{{  $t('add-to-screen')  }}</div>
    </transition>

    <div v-if="deferredPrompt&&$store.state.isPc" class="add-to-main-screen__pc">
      {{ $t('add_screen_des1') }}
      {{ $t('add_screen_des2') }}
      <span @click="goInstall" class="click-btn">{{  $t('add-to-screen')  }}</span>
    </div>
  </div>
</template>

<script>
function displayMode () {
  const isStandalone = window.matchMedia('(display-mode: standalone)').matches
  if (document.referrer.startsWith('android-app://')) {
    return 'twa'
  } else if (navigator.standalone || isStandalone) {
    return 'standalone'
  }
  return 'browser'
}
export default {
  name: 'AddScreenBtn',
  data () {
    return {
      deferredPrompt: window.__deferredPrompt || undefined
    }
  },
  methods: {
    showBtn () {
      window.addEventListener('beforeinstallprompt', (e) => {
        // 防止 Chrome 67 及更早版本自动显示安装提示
        e.preventDefault()
        // 稍后再触发此事件
        this.deferredPrompt = e
      })
    },
    goInstall () {
      this.$gtag.event('click_chrome_install', { event_label: this.$store.state.isMobile ? 'mobile' : 'pc' })
      this.deferredPrompt.prompt()
      // 等待用户反馈
      this.deferredPrompt.userChoice
        .then((choiceResult) => {
          if (choiceResult.outcome === 'accepted') {
            console.log('User accepted the A2HS prompt')
            const displayModeCheck = setInterval(() => {
              if (displayMode() === 'standalone') {
                clearInterval(displayModeCheck)
                this.$root.$emit('installSuccessful')
              }
            }, 1000)
          } else {
            console.log('User dismissed the A2HS prompt')
          }
          this.deferredPrompt = undefined
        })
    }
  },
  created () {
    const ua = (navigator.userAgent || '').toLowerCase()
    ua.includes('chrome') && this.showBtn()

    if (this.$gcbk('switch.enableAnimation', false) && this.$store.state.isPc) {
      const unwatch = this.$watch('deferredPrompt', (val) => {
        if (val) {
          this.$nextTick(() => gsap && gsap.from('.add-to-main-screen__pc', { height: 0, duration: .4, clearProps: 'height' }))
          unwatch()
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
@import "~@/utils/utils.scss";
@include setMobileContent{
  .addScreen-enter{
    transform: translateY(-100%);
    opacity: 0;
  }
  .addScreen-leave-to{
    transform: translateY(100%);
    opacity: 0;
  }
  .addScreen-enter-active,
  .addScreen-leave-active{
    transition: all .3s;
  }

  .add-to-main-screen__mobile{
    height: 50px;
    top: 10px;
    right: 15px;
    border-radius: 35px;
    font-size: 24px;
    line-height: 50px;
    padding: 0 30px;
    position: fixed;
    background-color: rgba(255, 255, 255, .7);
    cursor: pointer;
    z-index: 100;
  }
}
@include setPcContent{
  .add-to-main-screen__pc{
    text-align: center;
    height: 60PX;
    line-height: 60PX;
    background-color: black;
    margin-top: -18PX;
    font-size: 16PX;
    font-family: PingFangSC-Light, PingFang SC;
    font-weight: 300;
    color: #B0B0B0;

    span{
      background: #FF5E0F;
      border-radius: 18PX;
      line-height: 36PX;
      height: 36PX;
      font-size: 16PX;
      margin: 0 30PX;
      padding: 0 25PX;
      color: #FFFFFF;
      display: inline-block;
      cursor: pointer;
    }
  }
}

.dc{
  .add-to-main-screen__pc{
    display: flex;
    align-items: center;
    justify-content: center;

    span{
      @extend .dc-btn-decoration;
      @extend .dc-stroke;
      border-radius: 0;
      @include flexCenter;
    }
  }
}
</style>
