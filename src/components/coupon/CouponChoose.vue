<template>
  <common-part :class="['coupon-bar-wrapper', $gameName]" :label-font="$t('coupon')" id="coupon-bar-wrapper">
    <!--登录-->
    <div v-if="$store.state.userinfo.isLogin" @click="activity.showPop = true" style="display: inline-block">
      <div v-if="!activity.isFirstPayUsed" class="coupons-wrapper coupons-wrapper__unavailable">
        <div class="left">
          <span v-if="availableCouponNum === 0">{{ $t('coupon_desc_unavailable')  }}</span>
          <span v-else>{{ $t('coupon_nums', {0: availableCouponNum}) }}</span>
        </div>
      </div>
      <template v-else>
        <!--没有优惠券 -->
        <div v-if="availableCouponNum === 0" class="coupons-wrapper coupons-wrapper__no_coupon">
          {{ $t('coupon_desc_unavailable')  }}
        </div>
        <!--有优惠券 (首冲用了&&没有选中其他优惠券 || 没有使用首冲) -->
        <div v-if="availableCouponNum > 0 && !chosenCoupon.FE_INDEX"  class="coupons-wrapper coupons-wrapper__available">
          <span>{{ $t('coupon_nums', {0: availableCouponNum})  }}</span> <i></i>
        </div>
        <!--选中了优惠券 -->
        <div v-if="availableCouponNum > 0 && chosenCoupon.FE_INDEX" class="coupons-wrapper coupons-wrapper__chosen">
          <div class="left">
            <span>{{ $t('coupon_desc_chosen_erver')  }}</span>
          </div>
          <div class="right">
            <over-size-scale :key="chosenCoupon.FE_INDEX">
              <span>
            <template v-if="chosenCoupon.feType==='discount_coupon'">{{ $t('coupon_discount', { 0: chosenCoupon.rateWidthOutPercent }) }}</template>
            <template v-if="chosenCoupon.feType==='cash_coupon'"><span :class="{'is-ar-zone': isArZone}">{{ chosenCoupon.deduct_price}} {{ currencyUnit }}</span> OFF</template>
            <template v-if="chosenCoupon.feType==='rebate_coupon'"> <span>{{ $t('bonus_tips') }} </span>{{ chosenCoupon.rate }}<i class="diamond-icon"></i></template>
          </span>
            </over-size-scale>
            <i></i>
          </div>
        </div>
      </template>
    </div>
    <!--未登录-->
    <div v-else class="coupons-wrapper coupons-wrapper__not-login" @click="$root.$emit('ClickPayButNotLogin')">{{ $t('view_coupons_after_login') }}</div>

    <coupon-choose-pop
      v-if="activity.showPop"
      :had-obtained-list="activity.hadObtainedList"
      :not-obtained-list="activity.notObtainedList"
      :last-index="activity.chosenIndex"
      :is-first-charge-used="activity.isFirstPayUsed"
      @close="closeCouponPop">
    </coupon-choose-pop>
  </common-part>
</template>

<script>
import CommonPart from '@/components/common/CommonPart.vue'
import { getActivityListForToken } from '@/server'
import { mapGetters, mapState } from 'vuex'
import CouponChoosePop from '@/components/coupon/CouponChoosePop'
import OverSizeScale from '@/components/OverSizeScale.vue'
import device from 'current-device'
// import { logForBtnClick, logForClickTokenChanel } from '@/lib/logHelper'
const getRate = value => ((1 - value) * 100).toFixed(0)
const getDiamondRate = value => ((value - 1) * 100) && ((value - 1) * 100).toFixed(0)
const getDiamondSend = value => value.coin - value.level_coin

export default {
  name: 'CouponChoose',
  components: { OverSizeScale, CommonPart, CouponChoosePop },
  data () {
    return {
      activity: {
        notObtainedList: [],
        hadObtainedList: [], // 包括首冲的券（如果有的话第一个是）、优惠券
        chosenIndex: -1,
        showPop: false,

        isFirstPayUsed: true,
        timeInterval: undefined,

        isLoadingCoupon: false
      }
    }
  },
  computed: {
    ...mapState(['urlParams', 'isArZone', 'currencyUnit']),
    ...mapState('formdata', ['chosenChannel', 'chosenCoupon', 'chosenDiamond', 'isFixedRebateWork']),
    ...mapGetters('formdata', ['FinalPriceState']),
    availableCouponNum () {
      const l = this.activity.hadObtainedList
      return (l && l.length) ? l.filter(item => item.feType.includes('_coupon') && (('leaveCount' in item && item.leaveCount > 0) || !('leaveCount' in item))).length : 0
    }
  },
  methods: {
    initActInfo (keepPop) {
      // 置空上一次加载的数据
      const { timeInterval } = this.activity
      if (timeInterval) clearInterval(timeInterval)
      this.activity = {
        notObtainedList: [],
        hadObtainedList: [], // 包括首冲的券（如果有的话第一个是）、优惠券
        chosenIndex: -1,
        showPop: false,
        isFirstPayUsed: true,
        timeInterval: undefined
      }
      this.$store.commit('formdata/resetCouponInfo')

      // 默认券
      const price = this.chosenDiamond.price
      this.$root.$emit('setDefaultDiscountInfo', {
        price,
        discount_price: (price * 0.95).toFixed(2),
        feType: 'fixed_discount_coupon',
        FE_INDEX: 'fixed_discount_coupon_1',
        rateWidthOutPercent: 5,
        type: 'fixed_discount'
      })

      // 切换券后保持弹窗打开
      if (sessionStorage.getItem('reopenCoupon') || keepPop) {
        sessionStorage.removeItem('reopenCoupon')

        this.$nextTick(() => {
          this.activity.showPop = true
        })
      }
    },
    couponSort (coupons) {
      const items = coupons
      items.sort((a, b) => {
        if (a.coupon_expire_time > b.coupon_expire_time) {
          return 1
        } else if (a.coupon_expire_time === b.coupon_expire_time) {
          if (parseFloat(a.rateWidthOutPercent) < parseFloat(b.rateWidthOutPercent)) {
            return 1
          } else if (parseFloat(a.rateWidthOutPercent) === parseFloat(b.rateWidthOutPercent)) {
            return 0
          } else {
            return -1
          }
        } else {
          return -1
        }
      })
      return items
    },
    loadActivity (isToggleRebate = false) {
      // 兼容活动只在ss、koa上，其他游戏不请求链接。测试环境暂时还没兼容。
      const params = {}
      params.price = this.chosenDiamond.price
      params.product_id = this.chosenDiamond.product_id

      const { type, chosenNum } = this.chosenDiamond
      if (type === 2) params.custom_multiple = chosenNum

      if (!params.product_id) return null
      if (this.chosenChannel) {
        params.channel_id = this.chosenChannel.channel_id
        params.sub_channel_id = this.chosenChannel.sub_channel_id
      }

      // sdk 添加 package_type 参数，来自 api/product 接口
      if (this.$store.state.IS_CHECKOUT_SDK) {
        params.package_type = this.chosenDiamond.package_type
      }

      // sdk为了优先展示，可能ip接口还没回来
      if (this.$store.state.IS_CHECKOUT_SDK && !this.$store.state.country) {
        params.country = 'US'
        params.currency = 'USD'
      }

      this.$loading.show()
      this.couponLoading = true
      getActivityListForToken(params)
        .then(res => {
          this.initActInfo()
          const { code, data, message } = res

          if (isToggleRebate) {
            if (this.isFixedRebateWork) data.fixed_discount = []
            else data.fixed_rebate = []
            this.$store.commit('formdata/toggleCoupon')
          }

          if (code === 0) {
            // 特别档位 台湾 mycard 不适用任何优惠券
            if (this.$store.getters['formdata/TWMyCard']) {
              data.first_pay = data.coupon = data.deduct = data.fixed_discount = []
            }
            this.$store.commit('formdata/setIsInit', true)
            if (this.$store.state.gameinfo.isKOA) this.$store.commit('formdata/setFirstPayProducts', data.range_first_pay || [])

            if (this.$gameName === 'foundation') this.fixFoundationCoupon(params, data)
            this.adapterCouponType(data)
            // 首冲券
            let firstPayOrigin = data.first_pay || []
            firstPayOrigin = firstPayOrigin.map((item, index) => ({
              ...item,
              feType: 'first_pay',
              rateWidthOutPercent: getRate(item.discount),
              rate: `${getRate(item.discount)}%`,
              FE_INDEX: `first_pay_${index}`,
              productId: params.product_id
            }))
            // 兼容后端，返回首冲discount为0代表没有首冲
            if (data.first_pay && data.first_pay.length && !data.first_pay[0].discount) firstPayOrigin = []
            // 首冲返钻
            if ((data.first_pay_rebate || []).length) {
              firstPayOrigin = (data.first_pay_rebate || []).map((item, index) => ({
                ...item,
                feType: 'first_pay_rebate',
                rate: `${getDiamondSend(item)}`,
                FE_INDEX: `first_pay_rebate_${index}`,
                productId: params.product_id
              }))
            }
            // 折扣券 打折
            let couponOrigin = data.coupon || []
            couponOrigin = couponOrigin.map((item, index) => ({
              ...item,
              feType: 'discount_coupon',
              rateWidthOutPercent: getRate(item.discount),
              rate: `${getRate(item.discount)}%`,
              FE_INDEX: `discount_coupon_${index}`,
              productId: params.product_id
            }))
            const couponOriginCanUse = couponOrigin.filter(item => item.is_received && item.is_invalid) // 已获取，可用
            const couponOriginCanNotUse = couponOrigin.filter(item => item.is_received && !item.is_invalid) // 已获取，不可用
            // 抵扣券 抵钱
            let deductOrigin = data.deduct || []
            deductOrigin = deductOrigin.map((item, index) => ({
              ...item,
              feType: 'cash_coupon',
              FE_INDEX: `cash_coupon_${index}`,
              productId: params.product_id
            }))
            const deductOriginCanUse = deductOrigin.filter(item => item.is_received && item.is_invalid) // 已获取，可用
            const deductOriginCanNotUse = deductOrigin.filter(item => item.is_received && !item.is_invalid) // 已获取，不可用
            // 钻石券
            let rebateOrigin = data.rebate || []
            rebateOrigin = rebateOrigin.map((item, index) => ({
              ...item,
              feType: 'rebate_coupon',
              FE_INDEX: `rebate_coupon_${index}`,
              rate: `${getDiamondRate(item.discount)}%`,
              rateWidthOutPercent: getDiamondRate(item.discount),
              productId: params.product_id
            }))
            rebateOrigin = this.couponSort(rebateOrigin)
            const rebateOriginCanUse = rebateOrigin.filter(item => item.is_received && item.is_invalid) // 已获取，可用
            const rebateOriginCanNotUse = rebateOrigin.filter(item => item.is_received && !item.is_invalid) // 已获取，不可用
            // 长期XX折活动
            let defaultDiscountOrigin = data.fixed_discount || []
            defaultDiscountOrigin = defaultDiscountOrigin.map((item, index) => ({
              ...item,
              feType: 'fixed_discount_coupon',
              FE_INDEX: `fixed_discount_coupon_${index}`,
              rateWidthOutPercent: getRate(item.discount)
            }))
            if (defaultDiscountOrigin.length) this.$store.commit('formdata/setFixedCoupon', defaultDiscountOrigin[0])

            // 默认返钻
            let fixedRebateOrigin = data.fixed_rebate || []
            fixedRebateOrigin = fixedRebateOrigin.map((item, index) => ({
              ...item,
              feType: 'fixed_rebate',
              rateWidthOutPercent: getDiamondRate(item.discount),
              rate: `${getDiamondRate(item.discount)}%`,
              FE_INDEX: `fixed_rebate_${index}`,
              productId: params.product_id
            }))
            this.$store.commit('formdata/setFixedRebate', fixedRebateOrigin.length ? fixedRebateOrigin[0] : {})

            // 默认动态返钻
            let fixedDynamicRebateOrigin = data.product_fixed_rebate || []
            fixedDynamicRebateOrigin = fixedDynamicRebateOrigin.map((item, index) => ({
              ...item,
              feType: 'fixed_dynamic_rebate',
              rateWidthOutPercent: getDiamondRate(item.discount),
              rate: `${getDiamondRate(item.discount)}%`,
              FE_INDEX: `fixed_dynamic_rebate_${index}`,
              productId: params.product_id
            }))
            this.$store.commit('formdata/setFixedDynamicRebate', {
              chosen: fixedDynamicRebateOrigin[0] || {},
              all: data.range_product_fixed_rebate || []
            })

            /* 倒计时 */
            this.calcLeaveTime([...couponOrigin, ...deductOrigin, ...rebateOrigin].filter(item => item.is_received))

            /* 左边一行列表 备注：如果有firstPayOrigin 作为第一个券，选中不可改 */
            const leftObtainedList = [...firstPayOrigin, ...rebateOriginCanUse, ...deductOriginCanUse, ...couponOriginCanUse, ...rebateOriginCanNotUse, ...deductOriginCanNotUse, ...couponOriginCanNotUse]
            // if (isSS) leftObtainedList = [...firstPayOrigin, ...couponOriginCanUse, ...deductOriginCanUse, ...couponOriginCanNotUse, ...deductOriginCanNotUse]
            // if (isKOA) leftObtainedList = [...firstPayOrigin, ...couponOrigin]

            /* 拥有的优惠券 */
            this.activity.isFirstPayUsed = firstPayOrigin.length === 0
            this.$store.commit('formdata/setFirstPayStatus', this.activity.isFirstPayUsed)
            this.activity.hadObtainedList = leftObtainedList

            // 拥有且可用券（任何类型：目前有首冲、优惠、满减）,如果有改成选中第一个
            const availableList = [...rebateOriginCanUse, ...deductOriginCanUse, ...couponOriginCanUse]
            // if (isSS) availableList = [...firstPayOrigin, ...deductOriginCanUse, ...couponOriginCanUse]
            // if (isKOA) availableList = [...firstPayOrigin, ...couponOrigin].filter(item => item.is_received)
            // !__HaveHigherPriorityActivity：如果发现上次返钻活动有可用的档位，直接不初始化chosenIndex。（返钻活动已下线）
            // firstPayOrigin 如果有也会将chosenIndex 置为0
            if (firstPayOrigin.length) {
              this.$store.commit('formdata/setChosenCoupon', leftObtainedList[0])
            } else {
              // sdk2 固定折扣/返钻的优先级比优惠券高,所以不能选中优惠券
              const canNotChooseCouponByDefault = this.$store.state.IS_CHECKOUT_SDK_V2 && [...defaultDiscountOrigin, ...fixedRebateOrigin].length > 0
              if (availableList.length && !canNotChooseCouponByDefault) {
                let index = 0

                // 恢复切换前选中的券
                const reChooseCouponId = sessionStorage.getItem('reChooseCoupon')
                if (reChooseCouponId) {
                  index = leftObtainedList.findIndex(item => item.coupon_id === +reChooseCouponId)
                  index = Math.max(0, index)
                }
                this.$store.commit('formdata/setChosenCoupon', leftObtainedList[index])
              }
            }
            this.parsingSdk2Coupon(availableList)

            // 抵扣券适配ss周年庆活动
            this.activity.hadObtainedList = this.activity.hadObtainedList.map(item => {
              if (item.discount_range) {
                const isCheckoutSdk = this.$store.state.IS_CHECKOUT_SDK
                // 直购 && 优惠券
                const diamondNumArr = (isCheckoutSdk && item.feType.includes('_coupon')) ? item.discount_price_range.split('-') : item.discount_range.split('-')
                const currency = this.$store.state.currency
                /* isCheckoutSdk 只设置了default的最大最小，其他都没设置 */

                switch (this.$store.state.gameinfo.gameCode) {
                  case 'KOA': case 'MO': {
                    if (item.feType === 'cash_coupon') item.langValue = this.$t('min_cash_available_num', { 0: diamondNumArr[0], 1: this.$vt('tokenName') })
                    if (item.feType === 'discount_coupon') item.langValue = this.$t('max_cash_available_num', { 0: diamondNumArr[1], 1: this.$vt('tokenName') })
                    if (item.feType === 'rebate_coupon') item.langValue = this.$t('max_cash_available_num', { 0: diamondNumArr[1], 1: this.$vt('tokenName') })
                    break
                  }
                  default: {
                    // 复用gog的逻辑
                    if (diamondNumArr.length > 1) {
                      if (diamondNumArr[0] === '0') item.langValue = this.$t('max_cash_available_num', { 0: diamondNumArr[1], 1: this.$vt('tokenName') })
                      else if (diamondNumArr[1] === '0') item.langValue = this.$t('min_cash_available_num', { 0: diamondNumArr[0], 1: this.$vt('tokenName') })
                      else item.langValue = this.$t('btw_cash_available_num2', { 1: diamondNumArr[0], 2: diamondNumArr[1], 0: this.$vt('tokenName') })
                    } else {
                      item.langValue = this.$t('cash-num-eq-to', { 0: diamondNumArr[0], 1: this.$vt('tokenName') })
                    }
                  }
                }

                // 直购收银台单独的逻辑
                if (this.$store.state.IS_CHECKOUT_SDK) {
                  if (diamondNumArr.length > 1) {
                    if (diamondNumArr[0] === '0') item.langValue = this.$t('max_cash_available_num', { 0: currency, 1: diamondNumArr[1] })
                    else if (diamondNumArr[1] === '0') item.langValue = this.$t('min_cash_available_num', { 0: currency, 1: diamondNumArr[0] })
                    else item.langValue = this.$t('btw_cash_available_num', { 0: currency, 1: diamondNumArr[0], 2: diamondNumArr[1] })
                  } else {
                    item.langValue = this.$t('cash-num-eq-to', { 0: currency, 1: diamondNumArr[0] })
                  }
                }
              }
              return item
            })

            /* 不可领优惠券 */
            const notObtainedList = [...rebateOrigin, ...deductOrigin, ...couponOrigin].filter(item => !item.is_received)
            const kind = {
              'login_0.9': [],
              'comm_third_0.8': [],
              'comm_third_0.9': []
            }
            const langKeyMap = {
              'login_0.9': 'login_gain_coupon',
              'comm_third_0.9': 'invite_gain_coupon',
              'comm_third_0.8': 'invite_gain_coupon'
            }
            notObtainedList.forEach(item => {
              if (item.type.includes('login_') && item.discount === 0.9) kind['login_0.9'].push(item)
              if (item.type.includes('comm_third') && item.discount === 0.8) kind['comm_third_0.8'].push(item)
              if (item.type.includes('comm_third') && item.discount === 0.9) kind['comm_third_0.9'].push(item)
            })
            for (const [key, value] of Object.entries(kind)) {
              value.length && this.activity.notObtainedList.push(
                {
                  ...value[0],
                  num: value.length,
                  langKey: langKeyMap[key]
                }
              )
            }
            this.activity.notObtainedList = this.activity.notObtainedList.sort((a, b) => a.discount - b.discount)

            this.$root.$emit('activityInitEnd')
          } else {
            throw Error(message)
          }
        })
        .catch(err => {
          this.initActInfo()
          this.$toast.err(this.$t('network_err'))
          console.error(`优惠券初始化失败：${err.message}`)
        })
        .finally(() => {
          this.couponLoading = false
          this.$loading.hide()
          sessionStorage.removeItem('reChooseCoupon')
        })
    },
    fixActivityInfo () {
      if (this.couponLoading) return null
      const couponFeIndex = this.chosenCoupon.FE_INDEX

      // 兼容活动只在ss、koa上，其他游戏不请求链接。测试环境暂时还没兼容。
      const params = {}
      params.price = this.chosenDiamond.price
      params.product_id = this.chosenDiamond.product_id

      const { type, chosenNum } = this.chosenDiamond
      if (type === 2) params.custom_multiple = chosenNum

      if (!params.product_id) return null

      if (this.chosenChannel) {
        params.channel_id = this.chosenChannel.channel_id
        params.sub_channel_id = this.chosenChannel.sub_channel_id
      }

      // sdk 添加 package_type 参数，来自 api/product 接口
      if (this.$store.state.IS_CHECKOUT_SDK) {
        params.package_type = this.chosenDiamond.package_type
      }

      // sdk为了优先展示，可能ip接口还没回来
      if (this.$store.state.IS_CHECKOUT_SDK && !this.$store.state.country) {
        params.country = 'US'
        params.currency = 'USD'
      }

      // this.$loading.show()
      getActivityListForToken(params)
        .then(res => {
          // 如果修复之前是开启 一样开启
          this.initActInfo(this.activity.showPop)
          const { code, data, message } = res
          if (code === 0) {
            if (this.$store.getters['formdata/TWMyCard']) {
              data.first_pay = data.coupon = data.deduct = data.fixed_discount = []
            }
            this.$store.commit('formdata/setIsInit', true)
            if (this.$gameName === 'foundation') this.fixFoundationCoupon(params, data)
            this.adapterCouponType(data)
            // 首冲券
            let firstPayOrigin = data.first_pay || []
            firstPayOrigin = firstPayOrigin.map((item, index) => ({
              ...item,
              feType: 'first_pay',
              rateWidthOutPercent: getRate(item.discount),
              rate: `${getRate(item.discount)}%`,
              FE_INDEX: `first_pay_${index}`,
              productId: params.product_id
            }))
            // 兼容后端，返回首冲discount为0代表没有首冲
            if (data.first_pay && data.first_pay.length && !data.first_pay[0].discount) firstPayOrigin = []
            // 首冲返钻
            if ((data.first_pay_rebate || []).length) {
              firstPayOrigin = (data.first_pay_rebate || []).map((item, index) => ({
                ...item,
                feType: 'first_pay_rebate',
                rate: `${getDiamondSend(item)}`,
                FE_INDEX: `first_pay_rebate_${index}`,
                productId: params.product_id
              }))
            }
            // 折扣券 打折
            let couponOrigin = data.coupon || []
            couponOrigin = couponOrigin.map((item, index) => ({
              ...item,
              feType: 'discount_coupon',
              rateWidthOutPercent: getRate(item.discount),
              rate: `${getRate(item.discount)}%`,
              FE_INDEX: `discount_coupon_${index}`,
              productId: params.product_id
            }))
            const couponOriginCanUse = couponOrigin.filter(item => item.is_received && item.is_invalid) // 已获取，可用
            const couponOriginCanNotUse = couponOrigin.filter(item => item.is_received && !item.is_invalid) // 已获取，不可用
            // 抵扣券 抵钱
            let deductOrigin = data.deduct || []
            deductOrigin = deductOrigin.map((item, index) => ({
              ...item,
              feType: 'cash_coupon',
              FE_INDEX: `cash_coupon_${index}`,
              productId: params.product_id
            }))
            const deductOriginCanUse = deductOrigin.filter(item => item.is_received && item.is_invalid) // 已获取，可用
            const deductOriginCanNotUse = deductOrigin.filter(item => item.is_received && !item.is_invalid) // 已获取，不可用
            // 钻石券
            let rebateOrigin = data.rebate || []
            rebateOrigin = rebateOrigin.map((item, index) => ({
              ...item,
              feType: 'rebate_coupon',
              FE_INDEX: `rebate_coupon_${index}`,
              rate: `${getDiamondRate(item.discount)}%`,
              rateWidthOutPercent: getDiamondRate(item.discount),
              productId: params.product_id
            }))
            rebateOrigin = this.couponSort(rebateOrigin)
            const rebateOriginCanUse = rebateOrigin.filter(item => item.is_received && item.is_invalid) // 已获取，可用
            const rebateOriginCanNotUse = rebateOrigin.filter(item => item.is_received && !item.is_invalid) // 已获取，不可用
            // 长期XX折活动
            let defaultDiscountOrigin = data.fixed_discount || []
            defaultDiscountOrigin = defaultDiscountOrigin.map((item, index) => ({
              ...item,
              feType: 'fixed_discount_coupon',
              FE_INDEX: `fixed_discount_coupon_${index}`,
              rateWidthOutPercent: getRate(item.discount)
            }))
            if (defaultDiscountOrigin.length) this.$store.commit('formdata/setFixedCoupon', defaultDiscountOrigin[0])

            // 默认返钻
            let fixedRebateOrigin = data.fixed_rebate || []
            fixedRebateOrigin = fixedRebateOrigin.map((item, index) => ({
              ...item,
              feType: 'fixed_rebate',
              rateWidthOutPercent: getDiamondRate(item.discount),
              rate: `${getDiamondRate(item.discount)}%`,
              FE_INDEX: `fixed_rebate_${index}`,
              productId: params.product_id
            }))
            this.$store.commit('formdata/setFixedRebate', fixedRebateOrigin.length ? fixedRebateOrigin[0] : {})

            // 默认动态返钻
            let fixedDynamicRebateOrigin = data.product_fixed_rebate || []
            fixedDynamicRebateOrigin = fixedDynamicRebateOrigin.map((item, index) => ({
              ...item,
              feType: 'fixed_dynamic_rebate',
              rateWidthOutPercent: getDiamondRate(item.discount),
              rate: `${getDiamondRate(item.discount)}%`,
              FE_INDEX: `fixed_dynamic_rebate_${index}`,
              productId: params.product_id
            }))
            this.$store.commit('formdata/setFixedDynamicRebate', {
              chosen: fixedDynamicRebateOrigin[0] || {},
              all: data.range_product_fixed_rebate || []
            })

            /* 倒计时 */
            this.calcLeaveTime([...couponOrigin, ...deductOrigin, ...rebateOrigin].filter(item => item.is_received))

            /* 左边一行列表 备注：如果有firstPayOrigin 作为第一个券，选中不可改 */
            const leftObtainedList = [...firstPayOrigin, ...rebateOriginCanUse, ...deductOriginCanUse, ...couponOriginCanUse, ...rebateOriginCanNotUse, ...deductOriginCanNotUse, ...couponOriginCanNotUse]

            /* 拥有的优惠券 */
            this.activity.isFirstPayUsed = firstPayOrigin.length === 0
            this.$store.commit('formdata/setFirstPayStatus', this.activity.isFirstPayUsed)
            this.activity.hadObtainedList = leftObtainedList

            // 拥有且可用券（任何类型：目前有首冲、优惠、满减）,如果有改成选中第一个
            const availableList = [...rebateOriginCanUse, ...deductOriginCanUse, ...couponOriginCanUse]

            if (couponFeIndex) {
              if (firstPayOrigin.length) {
                if (couponFeIndex === leftObtainedList[0].FE_INDEX) this.$store.commit('formdata/setChosenCoupon', leftObtainedList[0])
              } else {
                if (availableList.length) {
                  const findLastIndex = leftObtainedList.findIndex(item => item.FE_INDEX === couponFeIndex) || 0
                  this.$store.commit('formdata/setChosenCoupon', leftObtainedList[findLastIndex])
                }
              }
            }
            this.parsingSdk2Coupon(availableList)

            // 抵扣券适配ss周年庆活动
            this.activity.hadObtainedList = this.activity.hadObtainedList.map(item => {
              if (item.discount_range) {
                const isCheckoutSdk = this.$store.state.IS_CHECKOUT_SDK
                // 直购 && 优惠券
                const diamondNumArr = (isCheckoutSdk && item.feType.includes('_coupon')) ? item.discount_price_range.split('-') : item.discount_range.split('-')
                const currency = this.$store.state.currency
                /* isCheckoutSdk 只设置了default的最大最小，其他都没设置 */

                switch (this.$store.state.gameinfo.gameCode) {
                  case 'KOA': case 'MO': {
                    if (item.feType === 'cash_coupon') item.langValue = this.$t('min_cash_available_num', { 0: diamondNumArr[0], 1: this.$vt('tokenName') })
                    if (item.feType === 'discount_coupon') item.langValue = this.$t('max_cash_available_num', { 0: diamondNumArr[1], 1: this.$vt('tokenName') })
                    if (item.feType === 'rebate_coupon') item.langValue = this.$t('max_cash_available_num', { 0: diamondNumArr[1], 1: this.$vt('tokenName') })
                    break
                  }
                  default: {
                    // 复用gog的逻辑
                    if (diamondNumArr.length > 1) {
                      if (diamondNumArr[0] === '0') item.langValue = this.$t('max_cash_available_num', { 0: diamondNumArr[1], 1: this.$vt('tokenName') })
                      else if (diamondNumArr[1] === '0') item.langValue = this.$t('min_cash_available_num', { 0: diamondNumArr[0], 1: this.$vt('tokenName') })
                      else item.langValue = this.$t('btw_cash_available_num2', { 1: diamondNumArr[0], 2: diamondNumArr[1], 0: this.$vt('tokenName') })
                    } else {
                      item.langValue = this.$t('cash-num-eq-to', { 0: diamondNumArr[0], 1: this.$vt('tokenName') })
                    }
                  }
                }

                // 直购收银台单独的逻辑
                if (this.$store.state.IS_CHECKOUT_SDK) {
                  if (diamondNumArr.length > 1) {
                    if (diamondNumArr[0] === '0') item.langValue = this.$t('max_cash_available_num', { 0: currency, 1: diamondNumArr[1] })
                    else if (diamondNumArr[1] === '0') item.langValue = this.$t('min_cash_available_num', { 0: currency, 1: diamondNumArr[0] })
                    else item.langValue = this.$t('btw_cash_available_num', { 0: currency, 1: diamondNumArr[0], 2: diamondNumArr[1] })
                  } else {
                    item.langValue = this.$t('cash-num-eq-to', { 0: currency, 1: diamondNumArr[0] })
                  }
                }
              }
              return item
            })

            /* 不可领优惠券 */
            const notObtainedList = [...rebateOrigin, ...deductOrigin, ...couponOrigin].filter(item => !item.is_received)
            const kind = {
              'login_0.9': [],
              'comm_third_0.8': [],
              'comm_third_0.9': []
            }
            const langKeyMap = {
              'login_0.9': 'login_gain_coupon',
              'comm_third_0.9': 'invite_gain_coupon',
              'comm_third_0.8': 'invite_gain_coupon'
            }
            notObtainedList.forEach(item => {
              if (item.type.includes('login_') && item.discount === 0.9) kind['login_0.9'].push(item)
              if (item.type.includes('comm_third') && item.discount === 0.8) kind['comm_third_0.8'].push(item)
              if (item.type.includes('comm_third') && item.discount === 0.9) kind['comm_third_0.9'].push(item)
            })
            for (const [key, value] of Object.entries(kind)) {
              value.length && this.activity.notObtainedList.push(
                {
                  ...value[0],
                  num: value.length,
                  langKey: langKeyMap[key]
                }
              )
            }
            this.activity.notObtainedList = this.activity.notObtainedList.sort((a, b) => a.discount - b.discount)
          } else {
            throw Error(message)
          }
        })
        .catch(err => {
          this.initActInfo()
          this.$toast.err(this.$t('network_err'))
          console.error(`优惠券初始化失败：${err.message}`)
        })
        // .finally(() => this.$loading.hide())
    },
    calcLeaveTime (list) {
      const fixDate = p => p < 10 ? `0${Math.floor(p)}` : Math.floor(p)
      const getLeaveDateTxt = count => `${Math.floor(count / 3600 / 24)}d ${fixDate(count / 3600 % 24)} : ${fixDate((count / 60) % 60)} : ${fixDate(count % 60)}`
      const newList = list.filter(item => item.coupon_expire_time && (item.coupon_expire_time > 0))
      for (const value of Object.values(newList)) {
        const count = value.leaveCount = value.coupon_expire_time
        value.showLeaveDate = getLeaveDateTxt(count)
      }

      this.activity.timeInterval = setInterval(() => {
        for (const value of Object.values(newList)) {
          const count = value.leaveCount - 1
          if (count >= 0) {
            value.leaveCount--
            value.showLeaveDate = getLeaveDateTxt(count)
          }
          if (count === 0) {
            // 如果倒计时这个为0 且该券选中了 去掉选中状态
            if (this.chosenCoupon.FE_INDEX === value.FE_INDEX) {
              // this.activity.chosenIndex = -1
              this.$store.commit('formdata/setChosenCoupon', {})
              this.$root.$emit('couponChoose')
              // this.$root.$emit('chosenTicketTimeOut')
            }
          }
        }
      }, 1000)
    },
    closeCouponPop (value) {
      this.activity.showPop = false
      this.$root.$emit('TicketPopClose')
    },

    parsingSdk2Coupon (couponList) {
      if (this.$store.state.IS_CHECKOUT_SDK_V2) this.$root.$emit('updateSdk2CouponList', couponList)
    },
    fixFoundationCoupon (requestParams, returnData) {
      /* foundation 每个档位都有单独的首冲 */
      const firstCouponList = returnData.coin_level_first_pay
      if (!firstCouponList || !firstCouponList.length) return null

      const rangeList = firstCouponList.map(item => {
        item.rate = `${getDiamondSend(item)}`
        item.feType = 'first_pay_rebate'
        return item
      })
      if (rangeList.length) {
        this.$store.commit('formdata/setFirstPayProducts', [{
          product_discount_range: rangeList
        }])
      }
      const filterFirstPayList = rangeList.filter(item => item.product_id === requestParams.product_id) || []
      if (filterFirstPayList.length) {
        filterFirstPayList[0].act_type = 'coin_level_first_pay'
        returnData.first_pay_rebate = filterFirstPayList
      }
    },
    adapterCouponType (data) {
      if (this.$store.state.IS_CHECKOUT_SDK_V2) {
        data.first_pay = data.direct_first_pay
        data.first_pay_rebate = data.direct_first_pay_rebate
        data.fixed_discount = data.direct_fixed_discount
        data.fixed_rebate = data.direct_fixed_rebate
        Reflect.deleteProperty(data, 'direct_first_pay')
        Reflect.deleteProperty(data, 'direct_first_pay_rebate')
        Reflect.deleteProperty(data, 'direct_fixed_discount')
        Reflect.deleteProperty(data, 'direct_fixed_rebate')
      }
    }
  },
  created () {
    const objUrl = '$store.state.formdata.chosenDiamond.product_id'
    this.unwatch = this.$watch(objUrl, value => value && this.loadActivity(), { immediate: true })

    // 如果新旧值都有，说明是切换的当前的数量，避免请求两次。
    const objDiamondUrl = '$store.state.formdata.chosenDiamond.totalDiamond'
    this.unwatch = this.$watch(objDiamondUrl, (newValue, oldValue) => newValue && oldValue && this.loadActivity())

    this.$root.$once('loginSuccess', () => this.loadActivity())

    const FE_CHANNEL_ID = '$store.state.formdata.chosenChannel.FE_CHANNEL_ID'
    this.unwatch = this.$watch(FE_CHANNEL_ID, newValue => newValue && this.fixActivityInfo())

    // 切换优惠券类型 koa
    this.$root.$on('couponToggleSuccess', () => this.loadActivity())
    // ss 小档位重置
    this.$root.$on('reloadActivity', () => this.loadActivity())
    // 切换优惠券类型 gog
    this.$root.$on('toggleFixedCoupon', (flag) => this.loadActivity(flag))
  },
  mounted () {
    if (device.ios()) this.$watch('activity.showPop', (value) => this.$root.$emit('showCouponPop', value))
  },
  beforeDestroy () {
    this.unwatch && this.unwatch()
  }
}
</script>
<!--500000350-->

<style scoped lang="scss">
@import "~@/utils/utils.scss";

::v-deep{
  .body{
    display: flex;
  }
}
.coupons-wrapper {
  transition: background .5s;
  border-radius: 4px;
  cursor: pointer;
}
.coupons-wrapper__not-login {
  @include bgCenter('koa/coupon/pay-coupon-bg_mobile_login.png', 672px, 68px);
  position: relative;
  font-size: 22px;
  font-family: SourceHanSansSC;
  font-weight: 400;
  color: #808080;
  padding-left: 48px;
  line-height: 68px;

  &:after {
    content: '';
    @include bgCenter('koa/coupon/pay-coupon-bg_mobile_login_tanhao.png', 23px, 23px);
    position: absolute;
    left: 13px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1;
  }
}
.coupons-wrapper__available {
  @include bgCenter('koa/coupon/pay-coupon-bg_mobile-available.png', 672px, 68px);
  font-size: 24px;
  font-family: SourceHanSansSC;
  font-weight: 600;
  color: #633B00;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-left: 21px;
  padding-right: 21px;

  i {
    @include bgCenter('koa/coupon/pay-coupon-bg_mobile-available-arrow.png', 24px, 24px);
    display: inline-block;
  }
}
.coupons-wrapper__chosen {
  @include bgCenter('koa/coupon/pay-coupon-bg_mobile-chosen.png', 672px, 68px);
  padding-left: 21px;
  padding-right: 20px;
  display: flex;

  .left {
    width: 76.6%;
    display: flex;
    align-items: center;
    justify-content: flex-start;

    span {
      font-size: 24px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #633B00;
      display: inline-block;
      white-space: nowrap;
      position: relative;
      margin-right: 30px;

      &:after{
        content: '';
        @include bgCenter('koa/coupon/coupon_chosen_gou.png', 24px, 24px);
        position: absolute;
        right: -34px;
        top: 50%;
        transform: translateY(-50%);
      }
    }
  }

  .right {
    flex: 1;
    padding-left: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 24px;
    font-family: PingFang;
    font-weight: 600;
    color: #633B00;
    flex-wrap: nowrap;
    white-space: nowrap;

    i {
      @include bgCenter('koa/coupon/pay-coupon-bg_mobile-chosen-arrow.png', 24px, 24px);
    }
    span {
      ::v-deep{
        i.diamond-icon {
          @include bgCenterForKoaIcon('koa/diamond/diamond.png', 22px, 18px);
          display: inline-block;
          margin: 0 2px;
        }
      }
    }
  }
}
.coupons-wrapper__unavailable {
  //@include bgCenter('pay-coupon-bg_mobile_login.png', 672px, 68px);
  //font-size: 22px;
  //font-family: SourceHanSansSC;
  //font-weight: 400;
  //color: #808080;
  //padding-left: 13px;
  //line-height: 68px;
  @extend .coupons-wrapper__chosen;
  .left span:after{
    display: none;
  }
}
.coupons-wrapper__no_coupon{
  @include bgCenter('koa/coupon/pay-coupon-bg_mobile_login.png', 672px, 68px);
  font-size: 24px;
  font-family: PingFangSC-Semibold, PingFang SC;
  font-weight: 600;
  color: #818080;
  padding-left: 20px;
  display: flex;
  align-items: center;
}
@include setPcContent{
  .coupons-wrapper {
    border-radius: 4PX;
  }

  .coupons-wrapper__not-login {
    @include bgCenter('koa/coupon/pay-coupon-bg_pc_login.png', 425PX, 44PX);
    font-size: 14PX;
    font-family: PingFangSC-Regular, PingFang SC;
    padding-left: 41PX;
    line-height: 44PX;
    color: #818080;

    &:after {
      content: '';
      @include bgCenter('koa/coupon/pay-coupon-bg_mobile_login_tanhao.png', 20PX, 20PX);
      left: 12PX;
    }
  }

  .coupons-wrapper__available {
    @include bgCenter('koa/coupon/pay-coupon-bg_pc-available.png', 425PX, 43PX);
    font-size: 14PX;
    padding-left: 12PX;
    padding-right: 12PX;

    i {
      @include bgCenter('koa/coupon/pay-coupon-bg_pc-available-arrow.png', 18PX, 18PX);
    }
  }

  .coupons-wrapper__chosen,
  .coupons-wrapper__unavailable {
    @include bgCenter('koa/coupon/pay-coupon-bg_pc-chosen.png', 420PX, 43PX);
    padding-left: 12PX;
    padding-right: 12PX;

    .left {
      span {
        font-size: 14PX;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #633B00;
        margin-right: 20PX;

        &:after{
          content: '';
          @include bgCenter('koa/coupon/coupon_chosen_gou.png', 14PX, 14PX);
          position: absolute;
          right: -20PX;
          top: 50%;
          transform: translateY(-50%);
        }
      }
    }

    .right {
      padding-left: 10PX;
      font-size: 14PX;
      font-family: PingFang;

      i {
        @include bgCenter('koa/coupon/pay-coupon-bg_mobile-chosen-arrow.png', 18PX, 18PX);
      }
      span {
        ::v-deep{
          i.diamond-icon {
            @include bgCenterForKoaIcon('koa/diamond/diamond.png', 14px, 11px);
            display: inline-block;
            margin: 0 2px;
          }
        }
      }
    }
  }

  .coupons-wrapper__no_coupon{
    @include bgCenter('koa/coupon/pay-coupon-bg_pc_login.png', 425PX, 44PX);
    padding-left: 12PX;
    font-size: 14PX;
  }
}
@include setMobileContent{
  ::v-deep{
    .label{
      display: none;
    }
  }
}

/* dc */
.coupon-bar-wrapper.dc{
  .coupons-wrapper {
    border-radius: 0;
  }
  .coupons-wrapper__not-login {
    @include bgCenterForDC('coupon/coupon-bar__not-login-m.png', 672px, 82px);
    color: #8E9CC7;
    line-height: 82px;

    &:after {
      @include bgCenterForDC('coupon/coupon-bar-icon-exclamation-mark.png', 30px, 30px);
    }
  }
  .coupons-wrapper__available {
    @include bgCenterForDC('coupon/pay-coupon-bg_mobile-available.png', 675px, 82px);
    font-size: 24px;
    line-height: 82px;
    color: #F4FBFF;
    @extend .dc-stroke;

    i {
      @include bgCenterForDC('coupon/pay-coupon-bg_mobile-available-arrow.png', 24px, 24px);
    }
  }
  .coupons-wrapper__chosen, .coupons-wrapper__unavailable{
    @include bgCenterForDC('coupon/pay-coupon-bg_mobile-chosen.png', 675px, 82px);
    .left{
      span{
        color: #4A402C;
        line-height: 1;

        &:after{
          @include bgCenterForDC('coupon/coupon_chosen_gou.png', 24px, 24px);
        }
      }
    }

    .right{
      color: #4A402C;
      //i {
      //  @include bgCenterForDC('coupon/pay-coupon-bg_mobile-chosen-arrow.png', 24px, 24px);
      //}
      ::v-deep{
        .diamond-icon {
          @include bgCenterForDC('diamond/diamond-icon.png', 22px, 22px);
        }
      }
    }
  }
  .coupons-wrapper__no_coupon{
    @include bgCenterForDC('coupon/pay-coupon-bg_mobile_login.png', 675px, 82px);
    color: #8E9CC7;
    line-height: 1;
  }

  @include setPcContent{
    .coupons-wrapper__not-login{
      @include bgCenterForDC('coupon/coupon-bar__not-login-m.png', 428px, 47px);
      line-height: 47px;
      padding-left: 37px;
      &:after {
        @include bgCenterForDC('coupon/coupon-bar-icon-exclamation-mark.png', 14px, 14px);
      }
    }
    .coupons-wrapper__available{
      @include bgCenterForDC('coupon/pay-coupon-bg_mobile-available-pc.png', 428px, 47px);
      font-size: 14px;
      line-height: 47px;

      i {
        @include bgCenterForDC('coupon/pay-coupon-bg_mobile-available-arrow.png', 18px, 18px);
      }
    }
    .coupons-wrapper__chosen, .coupons-wrapper__unavailable{
      @include bgCenterForDC('coupon/pay-coupon-bg_mobile-chosen-pc.png', 428px, 47px);
      .left{
        span{
          &:after{
            @include bgCenterForDC('coupon/coupon_chosen_gou.png', 14px, 14px);
          }
        }
      }
      .right{
        ::v-deep{
          .diamond-icon {
            @include bgCenterForDC('diamond/diamond-icon.png', 16px, 16px);
          }
        }
      }
    }
    .coupons-wrapper__no_coupon{
      @include bgCenterForDC('coupon/pay-coupon-bg_mobile_login-pc.png', 428px, 47px);
    }
  }
}
/* ssv */
.coupon-bar-wrapper.ssv{
  .coupons-wrapper__available {
    @include bgCenterForSSV('coupon/pay-coupon-bg_mobile-available.png', 672px, 68px);
    color: #FF5A00;

    i {
      @include bgCenterForSSV('coupon/pay-coupon-bg_mobile-available-arrow.png', 23px, 20px);
    }
  }

  @include setPcContent{
    .coupons-wrapper__available{
      @include bgCenterForSSV('coupon/pay-coupon-bg_pc-available.png', 425PX, 43PX);
      font-size: 16PX;
      padding-left: 14PX;
      padding-right: 10PX;

      i {
        @include bgCenterForSSV('coupon/pay-coupon-bg_pc-available-arrow.png', 19PX, 17PX);
      }
    }
  }
}

/* ssv2 */
.coupon-bar-wrapper.ssv2{
  .coupons-wrapper__not-login {
    @include bgCenterForSS('coupon/pay-coupon-bg_mobile_login.png', 672px, 68px);
    position: relative;
    font-size: 22px;
    font-weight: 400;
    color: #808080;
    padding-left: 48px;
    line-height: 68px;

    &:after {
      content: '';
      @include bgCenterForSS('coupon/pay-coupon-bg_mobile_login_tanhao.png', 23px, 23px);
      position: absolute;
      left: 13px;
      top: 50%;
      transform: translateY(-50%);
      z-index: 1;
    }
  }
  .coupons-wrapper__available {
    @include bgCenterForSS('coupon/pay-coupon-bg_mobile-available.png', 672px, 68px);
    font-size: 22px;
    font-family: SourceHanSansSC;
    font-weight: 400;
    color: #FF5A00;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-left: 20px;
    padding-right: 18px;

    i {
      @include bgCenterForSS('coupon/pay-coupon-bg_mobile-available-arrow.png', 23px, 20px);
      display: inline-block;
    }
  }
  .coupons-wrapper__unavailable,
  .coupons-wrapper__chosen {
    @include bgCenterForSS('coupon/pay-coupon-bg_mobile-chosen.png', 672px, 68px);
    padding-left: 10px;
    padding-right: 18px;
    display: flex;

    .left {
      width: 76.6%;
      display: flex;
      align-items: center;
      justify-content: flex-start;

      span {
        font-size: 18px;
        font-family: SourceHanSansSC;
        font-weight: 400;
        color: #FF5400;
        line-height: 44px;
        border: 1px solid #FF5E0F;
        border-radius: 4px;
        display: inline-block;
        padding: 0 52px;
        white-space: nowrap;

        &:after{
          display: none;
        }
      }
    }

    .right {
      flex: 1;
      padding-left: 10px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 24px;
      font-family: PingFang;
      font-weight: 400;
      color: #FF5E0F;
      flex-wrap: nowrap;
      white-space: nowrap;

      i {
        @include bgCenterForSS('coupon/pay-coupon-bg_mobile-chosen-arrow.png', 10px, 14px);
        margin-left: 15px;
      }
    }
  }
  .coupons-wrapper__no_coupon{
    @include bgCenterForSS('coupon/pay-coupon-bg_mobile_login.png', 672px, 68px);
    font-size: 24px;
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    color: #818080;
    padding-left: 20px;
    display: flex;
    align-items: center;
  }

  @include setPcContent{
    .coupons-wrapper {
      border-radius: 4PX;
    }

    .coupons-wrapper__not-login {
      @include bgCenterForSS('coupon/pay-coupon-bg_pc_login.png', 425PX, 43PX);
      font-size: 16PX;
      font-family: SourceHanSansSC;
      padding-left: 41PX;
      line-height: 43PX;

      &:after {
        content: '';
        @include bgCenterForSS('coupon/pay-coupon-bg_mobile_login_tanhao.png', 21PX, 21PX);
        left: 12PX;
      }
    }

    .coupons-wrapper__available {
      @include bgCenterForSS('coupon/pay-coupon-bg_pc-available.png', 425PX, 43PX);
      font-size: 16PX;
      padding-left: 14PX;
      padding-right: 10PX;

      i {
        @include bgCenterForSS('coupon/pay-coupon-bg_pc-available-arrow.png', 19PX, 17PX);
      }
    }

    .coupons-wrapper__unavailable,
    .coupons-wrapper__chosen {
      @include bgCenterForSS('coupon/pay-coupon-bg_pc-chosen.png', 425PX, 43PX);
      padding-left: 10PX;
      padding-right: 7PX;

      .left {

        span {
          font-size: 12PX;
          color: #FF5400;
          line-height: 26PX;
          border: 1PX solid #FF5E0F;
          border-radius: 3PX;
          padding: 0 13PX;
        }
      }

      .right {
        padding-left: 10PX;
        font-size: 15PX;
        font-family: PingFang;

        i {
          @include bgCenterForSS('coupon/pay-coupon-bg_mobile-chosen-arrow.png', 10PX, 13PX);
          margin-left: 6PX;
        }
      }
    }
    .coupons-wrapper__no_coupon{
      @include bgCenterForSS('coupon/pay-coupon-bg_pc_login.png', 425PX, 44PX);
      padding-left: 12PX;
      font-size: 14PX;
    }
  }
}
</style>
