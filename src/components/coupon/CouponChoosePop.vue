<template>
  <div :class="['ticket-chosen-wrapper', $gameName]" id="ticket-chosen-wrapper">
    <div class="pop-main">
      <div class="pop-close" @click="$emit('close')"></div>
      <div class="pop-title">
        <h3>{{ $t('coupon') }}</h3>
        <span>{{ $t('discount_offer_tips') }}</span>
      </div>
      <div class="divider"></div>
      <div class="nav-btn-wrapper">
        <div :class="['nav',{'nav-active': navIndex===0}]" @click="navIndex=0">{{ $t('nav_my_coupon') }}</div>
        <div :class="['nav',{'nav-active': navIndex===1}]" @click="navIndex=1">{{ $t('nav_other_coupon') }}</div>
      </div>
      <div class="main-container">
        <template v-if="navIndex===0">
          <coupon-choose-list
            :key="0"
            :coupon-list="hadObtainedList"
            :is-first-charge-used="isFirstChargeUsed"
            :reach="true"
            :temp-chosen-coupon.sync="tempChosenCoupon">
          </coupon-choose-list>
          <div :class="['btn-confirm', 'click-btn', {'btn-confirm__unavailable': !isFirstChargeUsed }]" @click="chooseCoupon">{{ $t('modalBtnOk') }}</div>
        </template>
        <coupon-choose-list
          v-else
          :key="1"
          :coupon-list="notObtainedList"
          :is-first-charge-used="isFirstChargeUsed"
          :reach="false">
        </coupon-choose-list>
      </div>

      <p class="coupon-repeat-tips">
        *{{ $t('construction_faq_q5a1') }}
        <template v-if="$store.state.country === 'RU'">Купоны не поддерживают Huawei Pay.</template>
      </p>
    </div>
  </div>
</template>

<script>
import CouponChooseList from '@/components/coupon/CouponChooseList'
import { mapState } from 'vuex'

export default {
  name: 'CouponChoosePop',
  components: { CouponChooseList },
  props: ['hadObtainedList', 'notObtainedList', 'isFirstChargeUsed', 'lastIndex'],
  data () {
    return {
      navIndex: 0,

      tempChosenCoupon: this.$store.state.formdata.chosenCoupon
    }
  },
  computed: {
    ...mapState('formdata', ['switchToggleState'])
  },
  methods: {
    chooseCoupon () {
      if (!this.isFirstChargeUsed) {
        // this.$tips.show('首冲活动与此活动互斥！')
        // this.$emit('close')
        return null
      }
      if (this.tempChosenCoupon) this.$store.commit('formdata/setChosenCoupon', this.tempChosenCoupon)
      this.$emit('close')
      this.$root.$emit('couponChoose')

      if (this.tempChosenCoupon.FE_INDEX) {
        this.$root.$emit('couponChosen')
      }
    }
  },
  mounted () {
    if (this.$gcbk('switch.enableAnimation', false)) {
      gsap && gsap.from('.ticket-chosen-wrapper .pop-main', { top: '45%', duration: .4, ease: 'back', clearProps: true })
      gsap && gsap.from('.ticket-chosen-wrapper', { backgroundColor: 'rgba(0, 0, 0, 0)', duration: .4, clearProps: true })
    }
  }
}
</script>

<style scoped lang="scss">
@import "~@/utils/utils.scss";
.is-ar-zone{
  display: inline-block;
}

.ticket-chosen-wrapper {
  user-select: none;
  height: 100%;
  width: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  position: fixed;
  z-index: 100;
  left: 0;
  top: 0;
  * {
    ::-webkit-scrollbar {
      display: none;
    }
  }

  .pop-main {
    background-color: #383838;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    @include setPropByBp(
      $m: (width: calc(100% - 86px), border-radius: 15px,max-width: 750px)
    );

    .pop-close {
      position: absolute;
      cursor: pointer;
      @include setMobileContent{
        @include bgCenter('koa/coupon/shopping-modal-close.png', 24px, 22px);
        right: 40px;
        top: 27px;
      }
    }

    .pop-title {
      color: #000000;
      text-align: center;
      margin-top: 14px;
      display: flex;
      flex-direction: column;

      h3 {
        white-space: nowrap;
        font-family: PingFang SC;
        font-weight: bold;
        line-height: 40px;
        font-size: 28px;
        color: #FFFFFF;
      }

      span {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        font-family: PingFang SC;
        font-weight: bold;
        color: #8C8C8C;
        line-height: 22px;
        font-size: 16px;
      }
    }

    .divider {
      width: calc(100% - 60px);
      height: 1px;
      background: #D8D8D8;
      margin: 15px auto 0;
      opacity: .4;
    }

    .nav-btn-wrapper {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 10px;

      .nav {
        cursor: pointer;
        font-family: PingFang SC;
        font-weight: bold;
        line-height: 33px;
        font-size: 24px;
        color: #8C8C8C;
        &.nav-active {
          position: relative;
          color: #FFFFFF;

          &:after {
            content: '';
            width: 80%;
            background: #F18648;
            position: absolute;
            bottom: -4px;
            height: 2px;
            left: 50%;
            transform: translateX(-50%);
          }
        }

        &:last-of-type {
          margin-left: 84px;
        }
      }
    }

    .main-container{
      text-align: center;
      .btn-confirm {
        text-align: center;
        background: #FF5A00;
        font-family: PingFang SC;
        font-weight: 400;
        color: #F1F1F1;
        cursor: pointer;

        margin: 30px auto 0;
        font-size: 30px;
        padding: 0 80px;
        line-height: 72px;
        border-radius: 8px;
        display: inline-block;
        white-space: nowrap;

        &.btn-confirm__unavailable{
          opacity: .3;
          cursor: auto;
        }
      }
    }

    .coupon-repeat-tips{
      width: 90%;
      font-size: 16px;
      line-height: 22px;
      margin: 20px auto 20px;
      text-align: center;
      color: #8C8C8C;
    }
  }

  @include setPcContent{
    .pop-main{
      border-radius: 15PX;
      width: 665PX;

      .pop-close{
        right: 22PX;
        top: 19PX;
        @include bgCenter('koa/coupon/shopping-modal-close.png', 23PX, 23PX);
      }

      .pop-title{
        margin-top: 14PX;

        h3{
          line-height: 33PX;
          font-size: 24PX;
        }

        span{
          font-size: 13PX;
          line-height: 18PX;
        }
      }
      .divider{
        width: 618PX;
        height: 1PX;
        margin: 17PX auto 0;
      }
      .nav-btn-wrapper {
        margin-top: 15PX;

        .nav {
          line-height: 25PX;
          font-size: 18PX;
          color: #8C8C8C;

          &.nav-active {
            &:after {
              bottom: -4PX;
              height: 2PX;
            }
          }

          &:last-of-type {
            margin-left: 81PX;
          }
        }
      }

      .main-container{
        .btn-confirm {
          margin: 17PX auto 0;
          font-size: 24PX;
          line-height: 53PX;
          border-radius: 4PX;
          padding: 0 60PX;
          display: inline-block;
        }
      }

      .coupon-repeat-tips{
        font-size: 13PX;
        margin: 20PX auto 15PX;
        line-height: 17PX;
      }
    }
  }
}

.ticket-chosen-wrapper.dc{
  .pop-main{
    background: #C3CBE1;
    border-radius: 0;
    border: 1px solid #979797;

    .pop-close {
      @include bgCenterForDC('coupon/pop-close.png', 32px, 32px);
    }

    .divider{
      background: #ABB4CD;
    }

    .pop-title{
      h3{
        color: #F4FBFF;
        @extend .dc-stroke;
      }
      span{
        color: #525280;
      }
    }

    .nav-btn-wrapper {
      .nav {
        color: #7C83A6;

        &.nav-active {
          color: #525280;

          &:after {
            background: #D9AD00;
          }
        }
      }
    }

    .main-container{
      .btn-confirm {
        border-radius: 0;
        color: #F4FBFF;
        margin-top: 0;
        @extend .dc-stroke;
        @extend .dc-btn-decoration;
        width: 220px;
        height: 72px;
        @include flexCenter
      }
    }

    .coupon-repeat-tips{
      color: #525280;
    }
  }

  @include setPcContent{
    .pop-main{
      .pop-close {
        width: 32px;
        height: 32px;
      }

      .main-container{
        .btn-confirm {
          width: 220px;
          height: 60px;
        }
      }
    }
  }
}
</style>
