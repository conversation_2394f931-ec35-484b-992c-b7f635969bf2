<script>
import CommonPart from '@/components/common/CommonPart.vue'

export default {
  name: 'sdk2Tip',
  components: { CommonPart }
}
</script>

<template>
  <common-part class="tips-part">
    <div class="tips">
      You are purchasing a digital license for this product. For full terms, see <span @click="$root.$emit('showPop', 'sdk2Tips', { type: 'policy' })">purchase policy</span>.
    </div>
  </common-part>
</template>

<style scoped lang="scss">
.tips-part{
  margin-top: 12px !important;

  .tips{
    font-family: SourceHanSansCN, SourceHanSansCN;
    font-weight: 400;
    font-size: 22px;
    color: #6A6A6A;
    line-height: 32px;
    //margin-top: 7px;

    span{
      color: #FF5E0F;
      text-decoration: underline;
    }
  }

  ::v-deep{
    .label{
      display: none;
    }
  }
}
</style>
