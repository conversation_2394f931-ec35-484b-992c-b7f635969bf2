<script>
export default {
  name: 'sdk2UserAndGameInfo',
  data () {
    return {
      gameName: this.$gcbk('gameinfo.gameName'),
      gameLogo: this.$gcbk('images.logoPath')
    }
  }
}
</script>

<template>
  <section class="info-wrapper">
    <img v-lazy="gameLogo" class="logo"/>
    <div class="info">
      <div class="game-name">{{ gameName }}</div>
      <div class="user-name">{{ $t('my-role') }}: {{ $store.state.userinfo.name || '-' }}</div>
    </div>
  </section>
</template>

<style scoped lang="scss">
@import "~@/utils/utils.scss";

.info-wrapper{
  position: absolute;
  width: calc(100% - 50px);
  top: -18px;
  left: 50%;
  transform: translateX(-50%);
  border-radius: 15px; /* 圆角效果 */
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); /* 阴影效果 */
  background: rgba(255, 255, 255, 0.8); /* 半透明白色背景 */
  backdrop-filter: blur(15px); /* 毛玻璃效果 */
  -webkit-backdrop-filter: blur(15px); /* 兼容性处理 */
  border: 2px solid white;
  padding: 20px 24px;
  @include flexCenter;
  justify-content: flex-start;

  .logo{
    width: 88px;
    display: inline-block;
  }

  .info{
    margin-left: 12px;
    .game-name{
      font-family: SourceHanSansCN, SourceHanSansCN;
      font-weight: bold;
      font-size: 32px;
      color: #2D2D2D;
      line-height: 46px;
    }
    .user-name{
      font-family: SourceHanSansCN, SourceHanSansCN;
      font-weight: bold;
      font-size: 24px;
      color: #939393;
      line-height: 35px;
    }
  }
}
</style>
