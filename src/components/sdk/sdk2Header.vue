<script>
import { jumpToUrl } from '@/utils/utils'
import { backAppGame } from '@/utils/utilsSdk2'

export default {
  name: 'sdk2Header',
  methods: {
    goStore () {
      jumpToUrl(process.env[`VUE_APP_PREFIX_STORE_${this.$gameName.toUpperCase()}`])
    },
    backAppGame
  }
}
</script>

<template>
  <header>
    <div @click="backAppGame" class="btn-back">
      <i></i>
    </div>
    <div class="fp-logo"></div>
    <div @click="goStore" class="toggle-btn">
      <i class="diamond-toggle"></i>
      Topup
      <img class="diamond-icon" v-lazy="$gcbk('images.iconDiamond')"/>
    </div>
  </header>
</template>

<style scoped lang="scss">
@import "~@/utils/utils.scss";
header {
  background: black;
  width: 100%;
  flex-shrink: 0;
  height: 112px;
  @include flexCenter;
  justify-content: flex-start;
  padding-left: 10px;
  padding-right: 30px;
  padding-bottom: 18px;

  .btn-back{
    font-weight: 600;
    font-size: 28px;
    color: #F4FBFF;
    line-height: 40px;
    text-stroke: 1px #000000;
    text-align: justify;
    font-style: normal;
    @include flexCenter;
    margin-left: 20px;

    i{
      @include bgCenterForSdk2('header/icon-back.png', 48px, 48px);
      display: inline-block;
    }
  }
  .fp-logo{
    display: inline-block;
    height: 35px;
    margin-left: 116px;

    @include bgCenterForSdk2('header/sdk2-title-image.png', 308px, 41px);
  }
  //.tip-btn{
  //  display: inline-block;
  //  @include bgCenterForSdk2('header/diamond-placeholder.png', 20px, 20px);
  //}

  .toggle-btn{
    margin-left: auto;
    width: 183px;
    flex-shrink: 0;
    @include flexCenter;
    font-family: SourceHanSansCN, SourceHanSansCN;
    font-weight: bold;
    font-size: 24px;
    color: white;
    border-radius: 10px;
    border: 2px solid #FFFFFF;
    backdrop-filter: blur(10px);
    height: 54px;

    .diamond-toggle{
      @include bgCenterForSdk2('header/icon-toggle.png', 24px, 24px);
      margin-right: 6px;
    }
    .diamond-icon{
      width: 46px;
      height: 46px;
      margin-left: 2px;
      background-size: cover;
      display: inline-block;
    }
  }
}
</style>
