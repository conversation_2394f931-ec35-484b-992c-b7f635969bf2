<script>
import CommonPart from '@/components/common/CommonPart.vue'
import { mapGetters, mapState } from 'vuex'
import OverSizeScale from '@/components/OverSizeScale.vue'

export default {
  name: 'sdk2PackageInfo',
  components: { OverSizeScale, CommonPart },
  data () {
    return {
      goodsName: '-'
    }
  },
  computed: {
    ...mapState(['currencyUnit']),
    ...mapGetters('formdata', ['FinalPriceState']),
    ...mapState('formdata', ['chosenCoupon', 'defaultRebateInfo', 'defaultDiscountInfo', 'chosenDiamond']),
    calState () {
      const temp = {
        type: '', // 传给后端用的
        isShow: false,
        description: '',
        discountPrice: '' // 折扣显示降低多少价格
      }

      const chosenCoupon = this.chosenCoupon
      const defaultCoupon = this.defaultDiscountInfo

      // 优惠券
      if (chosenCoupon.type === 'direct_first_pay' || (!chosenCoupon.feType && defaultCoupon.type === 'direct_fixed_discount')) {
        const tempCoupon = chosenCoupon.type === 'direct_first_pay' ? chosenCoupon : defaultCoupon
        temp.isShow = true

        temp.discountPrice = `- ${tempCoupon.discount_amount}${this.currencyUnit}`
        temp.description = this.$t(`sdk2_bonus_${tempCoupon.type}`, { 0: `${tempCoupon.rateWidthOutPercent}% OFF` })
        temp.type = tempCoupon.type
      }

      // 返钻券
      const defaultRebate = this.defaultRebateInfo
      if (chosenCoupon.type === 'direct_first_pay_rebate' || (!chosenCoupon.feType && defaultRebate.type === 'direct_fixed_rebate')) {
        const tempCoupon = chosenCoupon.type === 'direct_first_pay_rebate' ? chosenCoupon : defaultRebate
        temp.isShow = true

        const num = Math.floor(tempCoupon.coin - tempCoupon.level_coin)
        const txt = `${num} ${this.$vt('tokenName')}`
        temp.description = this.$t(`sdk2_bonus_${tempCoupon.type}`, { 0: txt })
        temp.type = tempCoupon.type
      }
      return temp
    }
  },
  created () {
    this.$root.$on('updateSdk2PackageName', (name) => {
      window.defaultPackageName = this.goodsName = name
    })
    window._calState = () => this.calState
  }
}
</script>

<template>
  <common-part :class="['package-part', $gameName]" :label-font="$t('sdk2_product_name')" id="package-part">
    <div class="package-wrapper">
      <div class="package-icon"></div>
      <div class="info-wrapper">
        <div class="name">{{ goodsName || '-' }}</div>
        <div class="price">{{ chosenDiamond.no_tax_price }}{{ currencyUnit }}</div>
      </div>
    </div>
    <div v-if="calState.isShow" class="default-coupon">
      <div class="coupon-icon"></div>
      <div class="coupon-desc">
        <over-size-scale>{{ calState.description }}</over-size-scale>
      </div>
      <div class="tips-btn" @click="$root.$emit('showPop', 'sdk2Tips', { type: 'constructions' })"></div>
      <div class="discount" v-if="calState.discountPrice">{{ calState.discountPrice }}</div>
    </div>
  </common-part>
</template>

<style scoped lang="scss">
@import "~@/utils/utils.scss";
  .package-part{
    margin-top: 0 !important;
    .package-wrapper {
      @include flexCenter;
      justify-content: flex-start;
      background: #FFFFFF;
      border-radius: 10px;
      border: 2px solid #D1D1D1;
      height: 120px;
      padding: 20px 24px;

      .package-icon {
        //width: 80px;
        //height: 80px;
        @include bgCenterForSdk2('info/default-package-gift.png', 80px, 80px);
      }

      .info-wrapper {
        margin-left: 12px;

        .name {
          font-family: SourceHanSansCN, SourceHanSansCN;
          font-weight: bold;
          font-size: 28px;
          color: #282828;
          line-height: 40px;
        }

        .price {
          font-family: SourceHanSansCN, SourceHanSansCN;
          font-weight: bold;
          font-size: 24px;
          color: #939393;
          line-height: 35px;
        }
      }
    }

    .default-coupon{
      background: #FFF4EA;
      border-radius: 10px;
      border: 2px solid #F1DFD2;
      margin-top: 32px;
      padding: 17px 14px;
      @include flexCenter;
      justify-content: flex-start;

      .coupon-icon{
        @include bgCenterForSdk2('info/coupon-icon.png', 56px, 56px);
      }

      .coupon-desc{
        font-family: SourceHanSansCN, SourceHanSansCN;
        font-weight: bold;
        font-size: 24px;
        color: #414141;
        //line-height: 28px;
        margin-left: 12px;
        max-width: 400px;
        @include flexCenter;
      }

      .tips-btn{
        @include bgCenterForSdk2('info/coupon-tip-icon.png', 24px, 24px);
        display: inline-block;
        margin-left: 8px;
      }
      .discount{
        font-family: SourceHanSansCN, SourceHanSansCN;
        font-weight: bold;
        font-size: 24px;
        color: #FF5E0F;
        line-height: 35px;
        margin-left: auto;
      }
    }
  }
</style>
