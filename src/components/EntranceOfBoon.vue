<template>
  <common-part class="entrance-part">
    <div class="entrance-wrapper" @click="go">
      <div v-if="$store.state.formdata.showDoubleExperience" class="toggle-coupon-act-level-up"></div>
      <div v-else class="award" :class="{rotate: rotate}"></div>
      {{ $t('boon-page-title') }}
      <i></i>
    </div>
  </common-part>
</template>

<script>
import CommonPart from '@/components/common/CommonPart.vue'
import { mapState } from 'vuex'
import { ameDoByGet, ameHoldByGet, getAmeDo } from '@/server'
import { getPWADisplayMode } from '@/utils/utils'
const { projectId, pwaOpenAction, loginAction, getLoginReward, getPwaReward } = window.$gcbk('apiParams.boonAme', {})

export default {
  name: 'EntranceOfBoon',
  components: { CommonPart },
  data () {
    return {
      clicked: false,
      gain: false // 获取两个
    }
  },
  computed: {
    ...mapState('userinfo', ['isLogin']),
    rotate () {
      return !this.isLogin || (!this.clicked && !this.gain)
    }
  },
  methods: {
    getStatus () {
      const params = {
        p0: 'web',
        p1: projectId,
        p2: `${getLoginReward},${getPwaReward}`
      }
      this.$loading.show()
      ameHoldByGet(params)
        .then(res => {
          const { data = [], code } = res
          if (code === 0) {
            if (data.length === 2) this.gain = true

            // gotDailyReward
            if (this.$store.state.gameinfo.gameCode === 'KOA') {
              this.gain = this.gain && this.$store.state.formdata.gotDailyReward
            }
          }
        })
        .finally(() => this.$loading.hide())
      // this.topupApi(true)
    },
    go () {
      this.clicked = true
      this.$root.$emit('showPop', 'BoonPop')
    },

    // 登录成功 初始化福利中心
    informServer (taskId) {
      const ameParams = { p0: 'web', p1: projectId }
      ameDoByGet({ p2: taskId, ...ameParams })
        .then(res => {
          const { code } = res
          if (code === 0) this.getReward(taskId)
        })
    },
    getReward (taskId) {
      const params = { p2: +taskId + 1 }
      const ameParams = { p0: 'web', p1: projectId }
      this.$loading.show()
      getAmeDo({ ...params, ...ameParams })
        .finally(() => this.$loading.hide())
    },

    checkAddBtn(){
      // pwa安装按钮样式调整
      this.setInterval = setInterval(() => {
        const btn =document.querySelector('.add-to-main-screen__mobile')
        const body = document.querySelector('.mobile-body-wrapper')
        if (btn) {
          btn.style.zoom = 0.75
          body.style.paddingTop = '1.3rem'
        } else {
          body.style.paddingTop = '0.53333rem'
        }
      }, 1000)
    }
  },
  created () {
    this.$root.$on('loginEnd', (state) => {
      // 福利中心
      if (getPWADisplayMode === 'standalone') this.informServer(pwaOpenAction)
      else {
        window.$event = this.$root
        this.$root.$on('installSuccessful', () => this.informServer(pwaOpenAction))
      }
      this.informServer(loginAction)

      this.getStatus()
    })

    if (this.$store.state.isMobile) this.checkAddBtn()
  },
  beforeDestroy() {
    if (this.setInterval) clearInterval(this.setInterval)
  }
}
</script>

<style lang="scss">
@import "~@/utils/utils.scss";

@keyframes shake {
  70%, 80% {
    transform: rotate(12deg);
  }

  75% {
    transform: rotate(-12deg);
  }

  65%, 85% {
    transform: rotate(0);
  }
}

.entrance-part {
  margin-top: 20px !important;

  & + .common-part-wrapper {
    margin-top: 12px;
  }

  .entrance-wrapper {
    font-size: 20px;
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    color: #FFEA99;
    line-height: 34px;
    text-indent: 47px;
    background: linear-gradient(to bottom, #F6812F, #EE2601);
    border-top-right-radius: 17px;
    border-bottom-right-radius: 17px;
    padding-right: 34px;
    display: inline-block;

    position: relative;
    margin-left: 20px;
    cursor: pointer;
    // display: flex;
    // align-items: center;
    // @include bgCenter('koa/koa-boon.png', auto, 63px);

    .award {
      // @include bgCenter('koa/boon-entrance-award.png', 159px, 89px);
      @include bgCenter('koa/boon/boon-entrance-award.png', 91px, 50px);
      position: absolute;
      left: -44px;
      bottom: -4px;
    }

    //.award-turntable {
    //  @include bgCenter('koa/turntable/back.png', 47px, 54px);
    //  position: absolute;
    //  display: flex;
    //  justify-content: center;
    //  left: -25px;
    //  bottom: 0px;
    //
    //  .award-turntable-turn {
    //    @include bgCenter('koa/turntable/turn.png', 46px, 46px);
    //    position: absolute;
    //    animation: turntable-rotate 4s linear infinite;
    //  }
    //
    //  .award-turntable-pointer {
    //    @include bgCenter('koa/turntable/pointer.png', 46px, 46px);
    //    position: absolute;
    //  }
    //
    //  @keyframes turntable-rotate {
    //    from {
    //      transform: rotate(0deg);
    //    }
    //    to {
    //      transform: rotate(360deg);
    //    }
    //  }
    //}

    i {
      @include bgCenter('koa/boon/boon-entrance-arrow.png', 8px, 8px);
      display: inline-block;
      position: absolute;
      right: 12px;
      top: 50%;
      transform: translateY(-50%);
    }

    .rotate {
      animation: shake 2s linear infinite alternate-reverse;
    }

    .toggle-coupon-act-level-up{
      @include bgCenter('koa/activity/act-toggle-coupon-level.png', 66px, 82px);
      position: absolute;
      bottom: -6px;
      left: -17px;
    }
  }

  // pc
  @include setPcContent{
    margin-top: 20px!important;

    & + .common-part-wrapper {
      margin-top: 18px;
    }
  }
  @include setMobileContent{
    .label{
      display: none;
    }
  }
}
</style>
