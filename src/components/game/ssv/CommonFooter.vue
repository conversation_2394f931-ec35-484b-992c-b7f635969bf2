<template>
  <div class="copyright">
    <img src="@/assets/common/icon/fp-logo.png" alt="funplus" style="vertical-align: text-bottom; padding-right: 10px;">
    <p>
      © FUNPLUS INTERNATIONAL AG(Bahnhofstrasse 2, 6300 Zug) - ALL RIGHTS RESERVED
      <a :href="urlCalc(1)" target="_blank" style="color:#ddb463">{{ isJP ? 'プライバシーポリシー' : 'Privacy Policy' }}, </a>
      <a :href="urlCalc(0)" target="_blank" style="color:#ddb463">{{ isJP ? '利用規約' : 'Terms and Conditions' }}</a>
      <template v-if="!isJP">
        and <a href="https://funplus.com/terms-conditions/#section-13" target="_blank" style="color:#ddb463">Refund Policy</a>.
      </template>
    </p>
  </div>
</template>

<script>
export default {
  name: 'CommonFooter',
  computed: {
    urlCalc () {
      const country = (this.$store.state.country || '').toLowerCase()
      const global = ['https://funplus.com/terms-conditions', 'https://funplus.com/privacy-policy/']
      const special = {
        tw: ['https://privacy.sosgame.tw/terms-conditions.html', 'https://privacy.sosgame.tw/privacy-policy.html'],
        kr: ['https://funplus.com/terms-conditions-en-as/kr/', 'https://funplus.com/privacy-policy-en-as/kr/'],
        jp: ['https://funplus.com/terms-conditions-en-as/ja/', 'https://funplus.com/privacy-policy-en-as/ja/ ']
      }
      return seq => {
        let finalUrl = global[seq]
        if (special[country]) finalUrl = special[country][seq]
        if (this.$i18n.locale === 'ja') finalUrl = special.jp[seq]
        return finalUrl
      }
    },
    isJP () {
      return this.$i18n.locale === 'ja' || this.$store.state.country === 'JP'
    }
  }
}
</script>

<style scoped lang="scss">
.copyright{
  height: 40PX;
  font-size: 13PX;
  line-height: 40PX;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #c5c5c5;;
  background-color: black;

  img{
    height: 20PX;
    position: relative;
    top: -2PX;
  }
}
</style>
