<template>
  <container :title="$t('boon-page-title') " class="arrears-reminder-wrapper" :class="[$i18n.locale]">
    <div class="divider"></div>
    <div class="tab-wrapper">
      <div :key="index" v-for="(item, index) in tabList" class="tab"
      :class="[
      `tab-${index}`,
      chosenIndex === index ? 'chosen-active' : '',
      item.title === 'boon-ss-install-title' && !gotInstallReward ? 'dot-active' : '',
      item.title === 'boon-ss-login-title' && !gotLoginReward ? 'dot-active' : ''
      ]"
        @click="chosenIndex = index">
        <span>{{ $t(item.title) }}</span>
      </div>
    </div>
    <Swiper class="my-swiper-wrapper" :options="swiperOptions">
      <!--快捷-->
      <SwiperSlide key="install">
        <div class="charge-desc">
          <div class="row-1">{{ $t('boon-task-2-title') }}</div>
          <div class="row-2">{{ $t('boon-p1-title') }}</div>
        </div>
        <div class="gift-image">
          <img :src="require(`@/assets/ss/${$gameName === 'ss' ? 'boon' : $gameName}/boon-install-award.png`)" alt="">
        </div>
        <div class="login-reward-btn action-btn">
          <template v-if="!isLogin">
            <span class="click-btn" @click="focusInput">{{ $t('boon-login') }}</span>
          </template>
          <template v-else-if="!hadInstall">
            <p class="browser-forbidden" v-if="!calcShowInstall">{{ $t('boon-browser-forbidden') }}</p>
            <span v-else class="click-btn" @click="install">{{ $t('boon-task-2-add') }}</span>
          </template>
          <template v-else>
            <span class="forbidden" v-if="gotInstallReward"></span>
            <span class="todo click-btn" v-else @click="getReward(getPwaReward)">{{ $t('boon-gain') }}</span>
          </template>
        </div>
      </SwiperSlide>

      <!--登录-->
      <SwiperSlide key="login">
        <div class="charge-desc">
          <div class="row-1">{{ $t('boon-task-1-title') }}</div>
          <div class="row-2">{{ $t('boon-p1-title') }}</div>
        </div>
        <div class="gift-image">
          <img :src="require(`@/assets/ss/${$gameName === 'ss' ? 'boon' : $gameName}/boon-login-award.png`)" alt="">
        </div>
        <div class="login-reward-btn action-btn">
          <span v-if="!isLogin" class="click-btn" @click="focusInput">{{ $t('boon-login') }}</span>
          <span v-else-if="!hadLogin" class="click-btn" @click="focusInput">{{ $t('boon-go-charge-short') }}</span>
          <template v-else>
            <span class="forbidden" v-if="gotLoginReward"></span>
            <span class="todo click-btn" v-else @click="getReward(getLoginReward)">{{ $t('boon-gain') }}</span>
          </template>
        </div>
      </SwiperSlide>
    </Swiper>
  </container>
</template>

<script>
import Container from '@/components/pop/container'
import { getAmeDo, ameHoldByGet } from '@/server'
import { Swiper, SwiperSlide } from 'vue-awesome-swiper'
import 'swiper/swiper-bundle.css'

import UAParser from 'ua-parser-js'
import { mapState } from 'vuex'
const { projectId, loginAction, getLoginReward, pwaOpenAction, getPwaReward } = window.$gcbk('apiParams.boonAme')

const ameParams = { p0: 'web', p1: projectId }
function displayMode () {
  const isStandalone = window.matchMedia('(display-mode: standalone)').matches
  if (document.referrer.startsWith('android-app://')) {
    return 'twa'
  } else if (navigator.standalone || isStandalone) {
    return 'standalone'
  }
  return 'browser'
}

export default {
  name: 'BoonPop',
  components: { Container, Swiper, SwiperSlide },
  data () {
    const instance = this
    return {
      hadLogin: false, // 1097
      gotLoginReward: false, // 1098
      hadInstall: false, // 1099
      gotInstallReward: false, // 1100
      deferredPrompt: window.__deferredPrompt || undefined,
      showMobileSafariGuide: false,

      progressPercent: 0,

      chosenIndex: 0,
      swiperInstance: undefined,
      swiperOptions: {
        autoplay: false,
        on: {
          slideChangeTransitionStart: function () {
            instance.chosenIndex = this.activeIndex
          },
          init: function () {
            // Swiper初始化了
            instance.swiperInstance = this
          }
        }
      },
      getLoginReward,
      getPwaReward,
      tabList: [
        { title: 'boon-ss-install-title' },
        { title: 'boon-ss-login-title' }
      ],
    }
  },
  methods: {
    onClose () {
      this.$root.$emit('closePop')
    },
    showInstallPart () {
      window.addEventListener('beforeinstallprompt', (e) => {
        // 防止 Chrome 67 及更早版本自动显示安装提示
        e.preventDefault()
        // 稍后再触发此事件
        this.deferredPrompt = e
      })
    },
    resetStatus () {
      const params = {
        p0: 'web',
        p1: projectId,
        p2: `${loginAction},${getLoginReward},${pwaOpenAction},${getPwaReward}`
      }
      this.$loading.show()
      ameHoldByGet(params)
        .then(res => {
          const { data, code } = res
          if (code === 0) {
            const result = {}
            for (const value of Object.values(data)) result[value.task_id] = value

            this.hadLogin = loginAction in result
            this.gotLoginReward = getLoginReward in result
            this.hadInstall = pwaOpenAction in result
            this.gotInstallReward = getPwaReward in result
          }
        })
        .finally(() => this.$loading.hide())
    },
    // 领取奖励
    getReward (taskId) {
      const params = { p2: taskId }
      this.$loading.show()
      getAmeDo({ ...params, ...ameParams })
        .then(res => {
          const { code, data = [] } = res
          if (code === 0 && data.length) {
            if (taskId === getPwaReward) this.gotInstallReward = true
            if (taskId === getLoginReward) this.gotLoginReward = true
          }
        })
        .finally(() => this.$loading.hide())
    },
    focusInput () {
      this.$root.$emit('closePop')
      this.$root.$emit('ClickPayButNotLogin')
      // const input = document.querySelector('#uidInput')
      // setTimeout(() => {
      //   input.focus()
      // }, 100)
    },
    install () {
      this.$root.$emit('closePop')
      // 如果是谷歌浏览器
      if (this.deferredPrompt) {
        this.deferredPrompt.prompt()
        // 等待用户反馈
        this.deferredPrompt.userChoice
          .then((choiceResult) => {
            if (choiceResult.outcome === 'accepted') {
              console.log('User accepted the A2HS prompt')

              const displayModeCheck = setInterval(() => {
                if (displayMode() === 'standalone') {
                  clearInterval(displayModeCheck)
                  this.$root.$emit('installSuccessful')
                }
              }, 1000)
            } else {
              console.log('User dismissed the A2HS prompt')
            }
            this.deferredPrompt = undefined
          })
      } else {
        setTimeout(() => {
          this.$root.$emit('mobileSafariGuide')
        }, 500)
      }
    }
  },
  computed: {
    ...mapState('userinfo', ['isLogin']),
    ...mapState(['userinfo']),
    calcShowInstall () {
      const parser = new UAParser(navigator.userAgent)
      const result = parser.getResult()
      const { browser } = result

      const whiteBrowser = browser.name === 'Mobile Safari' || ((browser.name || '').includes('Chrome') && this.deferredPrompt)
      if (!this.isLogin) return true
      if (!this.hadInstall) return whiteBrowser
      return true
    }
  },
  created () {
    this.$root.$on('loginSuccess', () => {
      setTimeout(() => this.resetStatus(), 2000)
    })
    if (this.isLogin) {
      this.resetStatus()
    } else {
    }

    this.$watch('chosenIndex', (index) => {
      this.swiperInstance.slideTo(index, 200, false)
    })

    this.showInstallPart()
  }
}
</script>

<style scoped lang="scss">
@import "~@/utils/utils.scss";

.arrears-reminder-wrapper {
  border-radius: 15px;
  background-color: #383838;
  padding-top: 18px!important;
  background-image: url("~@/assets/ss/boon/boon-pop-bg_m.png");
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  border: 1px solid #848484;

  .divider{
    width: 100%;
    height: 2px;
    background: #D8D8D8;
    opacity: 0.2;
    margin: 18px auto 0;
  }

  .tab-wrapper{
    display: flex;
    align-items: center;
    justify-content: center;

    font-size: 24px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #AEAEAE;
    padding-top: 16px;
    line-height: 1.1;

    .tab{
      flex-grow: 1;
      width: 0;
      min-height: 33px;
      padding: 3px 20px;
      align-items: center;
      justify-content: center;
      display: inline-block;
      cursor: pointer;

      word-break: break-all;
    }

    .dot-active{
      span{
        display: inline-block;
        position: relative;

        &:after{
          display: inline-block;
          content: ' ';
          width: 10px;
          height: 10px;
          background: #D20202;
          border-radius: 50%;
          position: absolute;
          top: 0;
          right: 0;
          transform: translate(100%, -100%);
        }
      }
    }

    .chosen-active{
      position: relative;
      color: white;
      font-weight: bold;

      &:before{
        display: inline-block;
        content: ' ';
        width: 70px;
        height: 4px;
        background: #FF5E0F;
        border-radius: 2px;
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translate(-50%, 100%);
      }
    }
  }

  .my-swiper-wrapper{
    .charge-desc{
      font-size: 40px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #FF5E0F;
      line-height: 56px;
      padding-top: 48px;

      .row-2{
        color: white;
      }
    }

    .gift-image{
      margin-top: 106px;
      padding: 0 30px;
      img{
        display: inline-block;
        width: 100%;
        height: auto;
      }
    }
  }

  .action-btn{
    span{
      margin: 165px auto 0;
      height: 70px;
      padding: 0 20px;
      background-color: #FF5E0F;
      border-radius: 8px;
      min-width: 300px;

      font-size: 30px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #FFFFFF;
      line-height: 70px;
      display: inline-block;

      &.forbidden{
        background-color: transparent;
        @include bgCenterForSSV('boon/boon-award-get.png',349px, 263px);
        margin-top: 0;
        position: relative;
        top: -60px;
      }
    }

    .browser-forbidden{
      font-size: 22px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #FF5E0F;
      line-height: 30px;
      margin-top: 56px;
      padding: 0 43px;
    }
  }

  ::v-deep{
    padding-bottom: 14px;
    .content{
      margin-top: 0;
      padding-left: 20px;
      padding-right: 20px;
      max-height: none;
    }
    .footer-wrapper{
      display: none;
    }

    .swiper-slide{
      max-height: 770px;
      overflow-y: scroll;
      min-height: 100%;
    }
  }
}

@include setPcContent{
  .arrears-reminder-wrapper {
    border-radius: 15PX;
    padding-top: 27PX !important;
    border: 1PX solid #848484;

    .divider {
      margin-top: 21PX;
      height: 2PX;
    }

    .tab-wrapper {
      font-size: 18PX;
      line-height: 25PX;
      padding-top: 14PX;

      .tab{
        min-height: 25PX;
        padding: 3PX 20PX;
      }

      .dot-active{
        span{
          &:after{
            width: 8PX;
            height: 8PX;
          }
        }
      }

      .chosen-active{
        color: white;
        &:before{
          width: 50PX;
          height: 4PX;
          border-radius: 2PX;
        }
      }
    }

    .my-swiper-wrapper{
      margin-top: 6PX;
      .charge-desc{
        font-size: 30PX;
        line-height: 42PX;
        padding-top: 50PX;
      }
      .gift-image{
        margin-top: 67PX;
        padding: 0 120PX;
      }
    }

    .action-btn{
      span{
        margin-top: 77PX;

        height: 50PX;
        padding: 0 20PX;
        background-color: #FF5E0F;
        border-radius: 8PX;
        min-width: 200PX;
        line-height: 50PX;
        font-size: 20PX;
        cursor: pointer;

        &.forbidden{
          @include bgCenterForSSV('boon/boon-award-get.png',243PX, 184PX);
          top: -50PX;
        }
      }
    }

    .browser-forbidden{
      font-size: 16PX;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #FF5E0F;
      line-height: 22PX;
      margin-top: 37PX;
      padding: 0 43PX;
    }

    ::v-deep{
      padding-bottom: 14px;
      .content{
        margin-top: 0;
        padding-left: 20PX;
        padding-right: 20PX;
      }
      .footer-wrapper{
        display: none;
      }

      .swiper-slide{
        max-height: 500PX;
        overflow-y: scroll;
        height: 100%;
      }

      .title{
        font-size: 24PX;
        line-height: 33PX;
        i{
          right: 20PX;
          height: 26PX;
          width: 26PX;
        }
      }
    }
  }
}
</style>
