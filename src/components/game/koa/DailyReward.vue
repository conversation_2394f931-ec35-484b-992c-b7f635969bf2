<template>
  <container-v2>
    <div class="daily-reward-wrapper">
      <template v-if="type === 'box'">
        <div class="title">{{ $t('title-daily-rewards') }}</div>
        <div class="daily-reward-box"></div>
        <div class="btn" @click="openBox">{{ $t('btn-open-now') }}</div>
        <div class="tips">{{ $t('subtitle-daily-rewards') }}</div>
      </template>
      <template v-if="type === 'exp'">
        <div class="title">{{ $t('btn-congratulations') }}</div>
        <div class="name" v-html="$t('text-obtain-vip-exp', { 0: `<span>${expNum}</span>` })"></div>
        <div class="daily-reward-exp"></div>
        <div class="btn btn-exp" @click="close">{{ $t('btn-accept') }}</div>
        <div class="go-vip" @click="clickGoVip">{{ $t('btn-check-vip') }}</div>
      </template>
      <template v-if="type === 'coupon'">
        <div class="title">{{ $t('btn-congratulations') }}</div>
        <div class="name">{{ couponName }}</div>
        <div :class="['daily-reward-' + name]"></div>
        <div class="btn btn-coupon" @click="close">{{ $t('btn-accept') }}</div>
      </template>

      <div class="close" @click="close"></div>
    </div>
  </container-v2>
</template>

<script>
import ContainerV2 from '@/components/pop/containerV2.vue'
import { getAmeDo } from '@/server'
const couponNameMap = { '9_day_vip_72': 'text-10-off-coupons', '95_day_vip_72': 'text-5-off-coupons' }

export default {
  name: 'DailyReward',
  components: { ContainerV2 },
  props: ['option'],
  data () {
    let type = ''
    let expNum = 0
    let name = ''
    let couponName = ''

    if (this.option.type) type = this.option.type
    else {
      const reward = this.option.reward
      name = reward.item_list[0].item_name
      if (name.includes('_exp')) {
        type = 'exp'
        expNum = reward.item_list[0].item_nums
      }
      if (name.includes('day_vip')) {
        type = 'coupon'
        couponName = this.$t(couponNameMap[name])
      }
    }
    return {
      type,
      name,

      expNum,
      couponName
    }
  },
  computed: {
    vipIntroducePageUrl () {
      // let base = process.env.VUE_APP_VipIntroducePageUrl + '?l=' + this.$i18n.locale
      // if (this.userinfo.openid) base += `&openid=${encodeURIComponent(this.userinfo.openid)}`
      const base = process.env[`VUE_APP_VipIntroducePageUrl_${this.$gameName}`] + '?l=' + this.$i18n.locale
      return base
    }
  },
  methods: {
    openBox () {
      if (!this.$store.state.userinfo.isLogin) {
        this.$root.$emit('closePop')
        this.$root.$emit('ClickPayButNotLogin')
        return null
      }

      this.$loading.show()
      getAmeDo({ p0: 'web', p1: 11, p2: 1422 })
        .then(res => {
          const { code, data = [] } = res
          if (code === 0 && data.length) {
            this.$store.commit('formdata/setDailyRewardStatus', true)
            this.$root.$emit('closePop')

            setTimeout(() => {
              this.$root.$emit('showPop', 'DailyReward', { reward: data[0] })
            }, 0)
          } else {
            this.$toast.err(this.$t('network_err'))
          }
        })
        .catch(() => this.$toast.err(this.$t('network_err')))
        .finally(() => this.$loading.hide())
    },
    close () {
      this.$root.$emit('closePop')
    },
    clickGoVip () {
      window.open(this.vipIntroducePageUrl, '_target')
    }
  }
}
</script>

<style scoped lang="scss">
@import "~@/utils/utils.scss";
.daily-reward-wrapper{
    background-color: rgba(44, 55, 87);
    border-radius: 20px;
    padding-top: 60px;
    padding-bottom: 30px;
    position: relative;

    .title{
      font-size: 40px;
      font-family: SourceHanSansCN-Heavy, SourceHanSansCN;
      font-weight: 800;
      color: #FFFFFF;
      line-height: 58px;
      letter-spacing: 2px;
    }

    .daily-reward-box{
      @include bgCenter('koa/dailyReward/daily-reward-box.png', 440px, 440px);
      margin: 22px auto 0;
    }

    .btn{
      width: 320px;
      height: 70px;
      background: #EED585;
      border-radius: 45px;

      font-size: 30px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #402600;

      display: flex;
      align-items: center;
      justify-content: center;

      margin: -20px auto 0;
    }

    .tips{
      font-size: 28px;
      font-family: SourceHanSansCN-Regular, SourceHanSansCN;
      font-weight: 400;
      color: #A4A4A4;
      line-height: 40px;
      margin-top: 20px;
    }

    .name{
      font-size: 30px;
      font-family: SourceHanSansCN-Medium, SourceHanSansCN;
      font-weight: 500;
      color: #EED585;
      line-height: 44px;
      margin-top: 6px;

      ::v-deep{
        span{
          font-size: 40px;
          color: #FF2516;
          margin: 0 5px;
        }
      }
    }

    .daily-reward-exp{
      @include bgCenter('koa/dailyReward/daily-reward-exp.png', 445px, 400px);
      margin: 22px auto 0;
    }

    .btn-exp{
      margin: -60px auto 0;
    }

    .go-vip{
      font-size: 20px;
      font-family: SourceHanSansCN-Regular, SourceHanSansCN;
      font-weight: 400;
      color: #969696;
      line-height: 29px;
      margin-top: 20px;
      text-decoration: underline;
    }

    .daily-reward-95_day_vip_72{
      @include bgCenter('koa/dailyReward/daily-reward-95_day_vip_72.png', 403px, 221px);
      margin: 78px auto 0;
    }

    .daily-reward-9_day_vip_72{
      @include bgCenter('koa/dailyReward/daily-reward-9_day_vip_72.png', 403px, 221px);
      margin: 78px auto 0;
    }

    .btn-coupon{
      margin-top: 79px;
    }

    .close{
      @include bgCenter('koa/dailyReward/daily-reward-close.png', 54px, 54px);
      position: absolute;

      bottom: calc(-30px - 54px);
      left: 50%;
      transform: translateX(-50%);
      cursor: pointer;
    }
  }

@include setPcContent{
  .daily-reward-wrapper{
    padding-top: 40px;
    padding-bottom: 35px;

    .title{
      font-size: 30px;
      line-height: 44px;
      letter-spacing: 1px;
    }

    .daily-reward-box{
      @include bgCenter('koa/dailyReward/daily-reward-box.png', 362px, 387px);
      margin: -59px auto 0;
    }

    .btn{
      width: 260px;
      height: 50px;
      border-radius: 25px;
      font-size: 20px;
      margin: -44px auto 0;
      cursor: pointer;
    }

    .tips{
      font-size: 18px;
      line-height: 26px;
      margin-top: 9px;
    }

    .name{
      font-size: 20px;
      line-height: 44px;
      margin-top: 0;

      ::v-deep{
        span{
          font-size: 30px;
          color: #FF2516;
        }
      }
    }

    .daily-reward-exp{
      @include bgCenter('koa/dailyReward/daily-reward-exp.png', 329px, 295px);
      margin: 10px auto 0;
    }

    .btn-exp{
      margin: -50px auto 0;
    }

    .go-vip{
      font-size: 18px;
      line-height: 26px;
      margin-top: 10px;
      cursor: pointer;
    }

    .daily-reward-95_day_vip_72{
      @include bgCenter('koa/dailyReward/daily-reward-95_day_vip_72.png', 311px, 172px);
      margin: 39px auto 0;
    }

    .daily-reward-9_day_vip_72{
      @include bgCenter('koa/dailyReward/daily-reward-9_day_vip_72.png', 311px, 172px);
      margin: 39px auto 0;
    }

    .btn-coupon{
      margin-top: 65px;
    }

    .close{
      @include bgCenter('koa/dailyReward/daily-reward-close.png', 42px, 42px);
      bottom: calc(-20px - 42px);
    }
  }
}
</style>
