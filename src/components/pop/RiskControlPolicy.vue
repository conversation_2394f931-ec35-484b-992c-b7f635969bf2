<template>
  <container :title="$t('text_tips')" :hide-close="true" class="arrears-reminder-wrapper" id="risk-policy-wrapper" :class="[$i18n.locale, $gameName]">
    <div class="desc">
      <div v-if="key === 'always_banned'" v-html="$t('risk_policy_forbidden_forever')"></div>
      <div v-if="['banned_adyen', 'banned_pingpong', 'banned_uid'].includes(key)" v-html="$t('risk_policy_forbidden_some', { 0: `<span>${leaveDate}</span>`})"></div>
      <div v-if="['access_warn', 'use_adyen', 'use_pingpong', 'use_wxpay', 'use_alipay', 'use_paypal', 'access_warn_black_room'].includes(key)" v-html="$t('risk_policy_pay_tip')"></div>
    </div>
    <template #footerBtn>
      <div class="custom-btn btn-ok" @click="close">{{ $t('confirm-btn') }}</div>
    </template>
  </container>
</template>

<script>
import Container from '@/components/pop/container'

export default {
  name: 'RiskControlPolicy',
  props: {
    option: {
      type: Object,
      default: () => ({})
    }
  },
  data () {
    const { key, value, cb } = this.option
    return {
      key,
      value,
      cb,

      interval: '',
      leaveDate: ''
    }
  },
  components: { Container },
  methods: {
    close () {
      this.$root.$emit('closePop')
      this.interval && clearInterval(this.interval)
      this.cb && this.cb()
    },
    calcLeaveTime () {
      const fixDate = p => p < 10 ? `0${Math.floor(p)}` : Math.floor(p)
      const getLeaveDateTxt = count => `${Math.floor(count / 3600 / 24)}d ${fixDate(count / 3600 % 24)} : ${fixDate((count / 60) % 60)} : ${fixDate(count % 60)}`

      this.leaveDate = getLeaveDateTxt(this.value)
      setInterval(() => {
        if (this.value === 0) {
          this.close()
          clearInterval(this.interval)
          return
        }
        this.leaveDate = getLeaveDateTxt(--this.value)
      }, 1000)
    }
  },
  created () {
    if (['banned_uid', 'banned_adyen', 'banned_pingpong'].includes(this.key)) {
      this.calcLeaveTime()
    }
  },
  beforeDestroy () {
    if (this.interval) clearInterval(this.interval)
  }
}
</script>

<style scoped lang="scss">
@import "~@/utils/utils.scss";

.arrears-reminder-wrapper {
  border-radius: 20px;
  background-color: #383838;
  padding-top: 30px!important;

  .desc{
    text-align: left;
    font-size: 18px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #FFFFFF;
    line-height: 25px;

    ::v-deep{
      span{
        color: red;
        padding-right: 4px;
        font-weight: bold;
      }
    }
  }

  ::v-deep{
    .content{
      margin-top: 20px;
    }

    .footer-wrapper{
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 30px;

      .custom-btn{
        min-width: 200px;
        border-radius: 10px;
        font-size: 20px;
        padding: 11px 0;
        line-height: 28px;
        cursor: pointer;

        &.btn-ok{
          margin-left: 32px;
          background: #FE6917;
          color: #FFFFFF;
        }
      }
    }
  }

  @include setPcContent{
    padding-bottom: 30PX;

    .desc{
      font-size: 18PX;
      line-height: 25PX;
    }

    ::v-deep{
      .content{
        margin-top: 20PX;
      }
      .footer-wrapper{
        margin-top: 30PX;

        .custom-btn{
          min-width: 200PX;
          border-radius: 10PX;
          font-size: 20PX;
          padding: 11PX 0;
          line-height: 28PX;

          &.btn-ok{
            margin-left: 32PX;
          }
        }
      }
    }
  }

  &.ar{
    .desc, .debt{
      text-align: right;
      direction: rtl;
    }
  }
}

.arrears-reminder-wrapper.dc{
  .desc{
    @extend .dc-stroke
  }
  ::v-deep{
    .content{
      margin-top: 20px;
    }

    .footer-wrapper{
      .custom-btn{
        &.btn-ok{
          @extend .dc-stroke;
          @extend .dc-btn-decoration;
          border-radius: 0;
        }
      }
    }
  }
}
</style>
