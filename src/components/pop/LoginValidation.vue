<template>
  <container :class="['custom-diamond-wrapper', $gameName]">
    <div v-if="showHowToCatPage" class="cat-page">
      <div class="title">{{ $t('login-validation-main-how-cat') }}</div>
      <div class="back-btn" @click="showHowToCatPage = false"></div>
      <img :src="$imageLoader('loganFindValidationCode')" alt="">
      <div class="time-tips" v-html="$t('login-validation-date-construction').replace('30', `<span>${loginValidationExpireCount}</span>`)">
      </div>
    </div>
    <div v-else class="main-page">
      <div class="title">{{ $t('login-validation-main-title') }}</div>
      <div class="send-info">
        <div class="tips" v-html="$t('login-validation-main-send-over', { 0: htmlUsername })"></div>
        <div v-if="canResend" class="send-btn" @click="resend">{{ $t('login-validation-main-resend') }}</div>
        <div v-if="leaveCount > 0" class="leave-count">（{{ leaveCount }}s）</div>
      </div>
      <div class="input-wrapper">
        <input v-model="inputCode" type="number" :placeholder="$t('login-validation-main-placeholder')" autofocus @input="fixInput" :class="$i18n.locale">
      </div>
      <div class="safe-tips" v-html="$t('login-validation-safety-construction')"></div>

      <div :class="['confirm-btn', {'confirm-btn_validated': rawValidate}]" @click="checkCode">{{ $t('login-validation-main-confirm') }}</div>
      <div class="cat-code-tips" @click="showHowToCatPage = true">{{ $t('login-validation-main-how-cat') }}</div>
      <div class="close" @click="close"></div>
    </div>
  </container>
</template>

<script>
import Container from '@/components/pop/containerV2'
import { checkCode, sendCode } from '@/server'
import { dealSmDeviceId } from '@/utils/utils'
let deviceId = ''
dealSmDeviceId((id) => { deviceId = id })

export default {
  name: 'ChannelKlarnaPopup',
  components: { Container },
  props: ['option'],
  computed: {
    htmlUsername () {
      return `<span>${this.option.username}</span>`
    },
    rawValidate () {
      const inputCode = this.inputCode
      return String(inputCode).length === 6
    },
    canResend () {
      return this.leaveCount === 0 && this.leaveTimes > 0
    }
  },
  data () {
    return {
      inputCode: '',
      showHowToCatPage: false,
      leaveCount: 59,
      leaveTimes: this.option.remaining_verification_attempts || 0,
      countInterval: '',
      loginValidationExpireCount: this.$gcbk('ids.loginValidationExpireCount', 30)
    }
  },
  methods: {
    countTime (leaveCount) {
      this.leaveCount = leaveCount || 59
      this.countInterval = setInterval(() => {
        this.leaveCount--

        if (this.leaveCount === 0) {
          clearInterval(this.countInterval)
          this.countInterval = null
        }
      }, 1000)
    },
    resend () {
      this.$loading.show()
      sendCode({ fp_device_id: deviceId, openid: this.option.openid })
        .then(res => {
          const { code } = res

          switch (code) {
            case 0: {
              this.leaveTimes = res.data.remaining_verification_attempts
              // this.$toast.err('验证码已发送')
              this.countTime()
              break
            }
            // case 5011: {
            //   const successCb = this.option.successCb
            //   if (successCb) successCb()
            //   this.$root.$emit('closePop')
            //   break
            // }
            default: {
              this.$toast.err(this.$t('login-validation-error-code'))
            }
          }
        })
        .finally(() => this.$loading.hide())
    },
    async checkCode () {
      if (!this.rawValidate) return null

      this.$loading.show()
      checkCode({ code: +this.inputCode, fp_device_id: deviceId, openid: this.option.openid })
        .then(res => {
          const { code } = res
          this.inputCode = ''
          switch (code) {
            case 0: case 5011: {
              const successCb = this.option.successCb
              if (successCb) successCb()
              this.$root.$emit('closePop')
              break
            }
            case 5009: {
              this.$toast.err(this.$t('login-validation-error-expire'))
              break
            }
            case 5010: {
              this.$toast.err(this.$t('login-validation-error-code'))
              break
            }
            default: {
              this.$toast.err(this.$t('login-validation-error-text'))
            }
          }
        })
        .finally(() => this.$loading.hide())
    },
    close () {
      this.$root.$emit('closePop')
      const failCb = this.option.failCb
      if (failCb) failCb()
    },
    fixInput (e) {
      const { data } = e
      const re = /^\d$/.test(data)
      if (data === 'e' || data === '.' || data === 'E' || !re) {
        e.target.value = ''
      }

      const length = this.inputCode.length
      if (length > 6) this.inputCode = this.inputCode.slice(0, 6)
    }
  },
  created () {
    const leaveCount = ((this.option && this.option.send_code_cd) || 59)
    this.countTime(leaveCount)
  },
  beforeDestroy () {
    if (this.countInterval) {
      this.countInterval = null
      clearInterval(this.countInterval)
    }
  }
}
</script>

<style scoped lang="scss">
@import "~@/utils/utils.scss";
.custom-diamond-wrapper {
  background-color: #2C3757;
  padding-bottom: 28px;
  padding-top: 20px;

  /* common */
  .title{
    font-size: 32px;
    font-family: SourceHanSansCN, SourceHanSansCN;
    font-weight: bold;
    color: #FFFFFF;
    line-height: 46px;
    max-width: 80%;
    margin: 0 auto;
  }

  .main-page{
    .send-info{
      margin-top: 24px;
      display: flex;
      align-items: center;
      justify-content: center;

      .tips{
        font-size: 24px;
        font-family: SourceHanSansCN, SourceHanSansCN;
        font-weight: 400;
        color: #FFFFFF;
        line-height: 35px;

        ::v-deep{
          span{
            margin: 0 8px;
            color: #FEB522;
          }
        }
      }

      .send-btn{
        font-size: 24px;
        font-family: SourceHanSansCN, SourceHanSansCN;
        font-weight: bold;
        color: #FEB522;
        line-height: 35px;
        margin-left: 8px;
        text-decoration: underline;
        cursor: pointer;
      }

      .leave-count{
        @extend .tips;
      }
    }

    .input-wrapper{
      margin-top: 30px;
      input{
        width: calc(100% - 300px);
        height: 60px;
        line-height: 60px;
        background: transparent;
        border: 1px solid #FFFFFF;
        appearance: none;
        -webkit-appearance:none;
        color: white;
        font-size: 24px;
        text-align: center;

        &:active, &:focus{
          appearance: none;
          -webkit-appearance:none;
          outline: none;
          border: 1px solid #FFFFFF;
        }

        &::-webkit-input-placeholder{
          font-size: 18px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #999999;
          text-align: center;
        }
      }
    }

    .safe-tips{
      font-size: 18px;
      font-family: SourceHanSansCN, SourceHanSansCN;
      font-weight: 400;
      color: #FEB522;
      line-height: 26px;
      width: calc(100% - 192px);
      margin: 20px auto 0;
    }

    .confirm-btn{
      font-size: 28px;
      font-family: SourceHanSansCN, SourceHanSansCN;
      font-weight: bold;
      color: #633B00;
      width: 240px;
      height: 64px;
      background: linear-gradient(to bottom, #F6E190, #CBA455);
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 40px auto 0;
      opacity: 0.6;
      cursor: not-allowed;

      &.confirm-btn_validated{
        opacity: 1;
        cursor: pointer;
      }
    }

    .cat-code-tips{
      font-size: 20px;
      font-family: SourceHanSansCN, SourceHanSansCN;
      font-weight: 400;
      color: #FFFFFF;
      margin: 26px auto 0;
    }

    .close{
      @include bgCenter('common/login-validate/login-validate-close.png', 26px, 26px);
      position: absolute;
      cursor: pointer;
      top: 29px;
      right: 25px;
    }
  }

  .cat-page{
    img{
      display: inline-block;
      width: calc(100% - 40px);
      margin: 30px auto 0;
    }

    .time-tips{
      font-size: 24px;
      font-family: SourceHanSansCN, SourceHanSansCN;
      font-weight: 400;
      color: #FFFFFF;

      ::v-deep{
        span{
          color: #FEB522;
          font-weight: bold;
          margin: 0 8px;
        }
      }
    }

    .back-btn{
      @include bgCenter('common/login-validate/login-validate-back.png', 32px, 32px);
      position: absolute;
      cursor: pointer;
      top: 22px;
      left: 30px;
    }
  }
}
@include setPcContent{
  .custom-diamond-wrapper {
    padding-top: 24px;

    .title{
      font-size: 24px;
      line-height: 35px;
    }

    .main-page{
      .send-info{
        margin-top: 20px;
        .tips{
          font-size: 16px;
          line-height: 24px;

          ::v-deep{
            span{
              margin: 0 6px;
              color: #FEB522;
            }
          }
        }
        .send-btn{
          line-height: 24px;
          font-size: 16px;
        }

        .leave-count{
          @extend .tips;
        }
      }
      .input-wrapper{
        margin-top: 20px;
        input{
          width: calc(100% - 220px);
          height: 50px;
          line-height: 50px;
          font-size: 18px;

          &::-webkit-input-placeholder{
            font-size: 18px;
          }
        }
      }
      .safe-tips{
        font-size: 14px;
        line-height: 20px;
        margin-top: 10px;
        width: calc(100% - 142px);
      }
      .confirm-btn{
        margin-top: 20px;
        width: 200px;
        height: 50px;
        font-size: 20px;
      }
      .cat-code-tips{
        margin-top: 10px;
        font-size: 14px;
      }
      .close{
        @include bgCenter('common/login-validate/login-validate-close.png', 22px, 22px);
        position: absolute;
        cursor: pointer;
        top: 21px;
        right: 25px;
      }
    }

    .cat-page{
      img{
        width: calc(100% - 60px);
        margin: 30px auto 0;
      }

      .time-tips{
        font-size: 16px;
        line-height: 24px;

        ::v-deep{
          span{
            margin: 0 4px;
          }
        }
      }
    }
  }
}

.custom-diamond-wrapper.ssv{
  background-color: #363535;

  .main-page{
    .confirm-btn{
      background: #FF5E0F;
      color: #FFFFFF;
      border-radius: 12px;
    }
  }
}
.custom-diamond-wrapper.dc{
  background: #C3CBE1;
  border: 1px solid #979797;

  .title{
    @extend .dc-stroke
  }

  .main-page{
    .send-info{
      @extend .dc-stroke;
    }
    .input-wrapper{
      input{
        background: #34353D;
        border: 1px solid #000000;
      }
    }
    .safe-tips{
      color: #525280;
    }
    .confirm-btn{
      @extend .dc-btn-decoration;
      @extend .dc-stroke;
      color: #F4FBFF;
    }
    .cat-code-tips{
      color: #525280;
    }
    .close{
      @include bgCenterForDC('coupon/pop-close.png', 36px, 36px);
    }
  }
  .cat-page{
    .time-tips{
      @extend .dc-stroke
    }
  }

  @include setPcContent{
    .main-page{
      .close{
        @include bgCenterForDC('coupon/pop-close.png', 27px, 27px);
      }
    }
  }
}
</style>
