<script>
import ContainerV2 from '@/components/pop/containerV2.vue'
import OverSizeScale from '@/components/OverSizeScale.vue'

export default {
  name: 'sdk2Tips',
  props: ['option'],
  data () {
    return {
      type: this.option.type
    }
  },
  components: { OverSizeScale, ContainerV2 }
}
</script>

<template>
  <container-v2 class="sdk2-constructions">
    <div class="title">
      <div class="txt" v-if="type === 'constructions'">
        <over-size-scale>
          {{ $vt('sdk2_construction_title') }}
        </over-size-scale>
      </div>
      <i @click="$root.$emit('closePop')" class="close"></i>
    </div>
    <div class="content">
      <template v-if="type  === 'policy'">
        BY CLICKING “PAY NOW”, YOU CONFIRM:
        THAT YOU HAVE READ, AGREE AND UNDERSTAND THAT FOR EACH DIGITAL GOOD PURCHASE, YOU WILL ONLY RECEIVE A LICENSE, SUBJECT TO THE RIGHTS AND RESTRICTIONS SET OUT IN OUR <a href="https://funplus.com/usage-rules-for-digital-goods/" target="_blank">USAGE RULES</a> FOR DIGITAL GOODS.
        THAT YOU AGREE TO THE IMMEDIATE FULFILMENT OF THE CONTRACT AND ACCEPT THAT YOU THEREBY LOSE YOUR WITHDRAWAL RIGHT (SEE <a target="_blank" href="https://funplus.com/terms-conditions/en/#section-15">REFUND POLICY</a>).
      </template>
      <template v-if="type  === 'constructions'">
        <div v-html="$vt('sdk2_construction_content')"></div>
      </template>
    </div>
    <div class="footer">
      <div class="btn click-btn" @click="$root.$emit('closePop')">OK</div>
    </div>
  </container-v2>
</template>

<style scoped lang="scss">
@import "~@/utils/utils.scss";

.sdk2-constructions{
  background: #2F2F2F;
  border-radius: 10px;
  padding: 30px 40px 40px;

  .title{
    position: relative;
    @include flexCenter;
    height: 40px;

    .txt{
      font-family: SourceHanSansCN, SourceHanSansCN;
      font-weight: bold;
      font-size: 30px;
      color: #FFFFFF;
      width: 530px;
      display: inline-block;
      overflow: hidden;
      @include flexCenter
    }

    .close{
      @include bgCenter('common/login-validate/login-validate-close.png', 30px, 30px);
      position: absolute;
      cursor: pointer;
      top: 50%;
      transform: translateY(-50%);
      right: 0;
    }
  }

  .content{
    font-size: 24px;
    padding: 20px 0 30px;
    color: #FFFFFF;
    line-height: 36px;
    text-align: justify;

    a{
      color: #FF5E0F;
      text-decoration: underline;
    }
  }

  .footer{
    @include flexCenter;
    .btn{
      width: 216px;
      height: 70px;
      background: #FF5E0F;
      border-radius: 10px;
      @include flexCenter;
      color: white;
      font-size: 30px;
      font-weight: bold;
    }
  }
}
</style>
