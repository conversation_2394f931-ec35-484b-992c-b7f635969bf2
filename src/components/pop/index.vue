<template>
  <section>
    <transition name="bg">
      <div v-if="showOuter" class="pop-bg"></div>
    </transition>
    <transition name="cmp">
      <component v-if="cmpName && showInner" :is="cmpName" :option="option"></component>
    </transition>
  </section>
</template>

<script>
import BackendPopup from '@/components/pop/BackendPopup.vue'
import ChargeConstruction from '@/components/pop/ChargeConstruction'
import CallbackPendingTips from '@/components/pop/CallbackPendingTips'
import ArrearsReminder from '@/components/pop/ArrearsReminder'
import IosForbidden from '@/components/pop/IosForbidden'
import CustomDiamond from '@/components/pop/CustomDiamond'
import AvatarBonusPop from '@/components/pop/AvatarBonusPop'
import DailyReward from '@/components/game/koa/DailyReward.vue'
import { StorageUtils } from '@/utils/storageUtils'

import { getAmeDo } from '@/server'

export default {
  name: 'index',
  components: {
    BackendPopup,
    CallbackPendingTips,
    ChargeConstruction,
    ArrearsReminder,
    WhatIsDiamond: () => import(/* webpackChunkName: 'chunk-functions' */ '@/components/pop/WhatIsDiamond'),
    IosForbidden,
    BoonPop: () => import(/* webpackChunkName: 'chunk-functions' */ '@/components/game/koa/BoonPop.vue'),
    SSVBoonPop: () => import(/* webpackChunkName: 'chunk-functions' */ '@/components/game/ssv/BoonPop.vue'),
    CustomDiamond,
    AvatarBonusPop,
    RiskControlPolicy: () => import(/* webpackChunkName: 'chunk-functions' */ '@/components/pop/RiskControlPolicy'),
    DailyReward,
    LoginValidation: () => import(/* webpackChunkName: 'chunk-functions' */ '@/components/pop/LoginValidation'),
    PrivacyPolicy: () => import(/* webpackChunkName: 'chunk-functions' */ '@/components/pop/PrivacyPolicy.vue'),
    PrivateConfirmPop: () => import(/* webpackChunkName: 'chunk-functions' */'@/components/pop/privateConfirmPop'),
    sdk2Tips: () => import(/* webpackChunkName: 'chunk-sdk2' */ '@/components/pop/sdk2Tips')
  },
  data () {
    return {
      showOuter: false,
      showInner: false,
      cmpName: '',
      option: {},
      cacheList: [],

      game: this.$store.state.gameinfo.gameProject.split('_')[0],
      gameEnv: process.env.VUE_APP_PROD_ENV.toLowerCase(),
      popupList: [],
      ymd: '',
      lang: ''
    }
  },
  methods: {
    showLoginValidation (params) {
      if (this.cmpName) {
        const validationPop = { name: 'LoginValidation', option: params }
        if (this.cmpName === 'BackendPopup') {
          this.cacheList.unshift(validationPop)
        } else {
          const lastPop = { name: this.cmpName, option: this.option }
          this.cacheList.unshift(lastPop)
          this.cacheList.unshift(validationPop)
          this.$root.$emit('closePop')
        }
      } else {
        this.$root.$emit('showPop', 'LoginValidation', params)
      }
    },
    showOtherPop () {
      this.$root.$once('loginSuccess', async () => {
        const country = this.$store.state.country
        const targetArr = 'NL,BE,LU,DK,IE,GR,SE,FI,AT,CY,EE,LV,LT,PL,CS,SK,SI,HU,MT,RO,BG,HR,DE,FR,IT,ES,PT,CA,GB,AU,NZ,US,KR,TW,JP'.split(',')
        // 其他国家直接返回
        if (!targetArr.includes(country)) return null

        // 查看本地信息，已经点过就不弹了
        const history = StorageUtils.getLocalStorage('confirmPrivacyPolicy')
        if (history === '1') return null

        // 查看服务端信息，已经点过就不弹了
        const params = {
          p0: 'web',
          p1: 7,
          p2: '1066',
          silence: true
        }
        const { data: { agree } } = await getAmeDo(params)
        if (agree) return StorageUtils.setLocalStorage('confirmPrivacyPolicy', '1')

        // 如果未同意，弹窗
        this.$store.commit('setPrivacyPolicyStatus', false)
        this.$root.$emit('showPop', 'PrivacyPolicy')
      })
    }
  },
  created () {
    const broswerLang = navigator.language || navigator.userLanguage
    if (broswerLang.toLowerCase() === 'zh-tw') this.lang = 'zh_tw'
    else if (broswerLang.startsWith('zh')) this.lang = 'zh_cn'
    else this.lang = broswerLang.split('-')[0]

    this.$root.$on('backendPopup', (ip) => {
      if (this.$store.state.IS_CHECKOUT_SDK_V2) return
      const params = Object.assign(
        {
          p0: 'web',
          p1: 9,
          p2: '1653',
          p3: 'api',
          game: this.game
        },
        process.env.VUE_APP_PROD_ENV === 'ONLINE' ? {} : { gameEnv: this.gameEnv }
      )
      const date = new Date(Date.now() + new Date().getTimezoneOffset() * 60 * 1000)
      const ymd = `${date.getFullYear()}/${date.getMonth() + 1}/${date.getDate()}`
      getAmeDo(params).then((response) => {
        if (response.code === 0) {
          if (this.$route.name !== 'Pay') return null
          this.popupList = response.data
          for (let i = 0; i < this.popupList.length; i++) {
            const popup = this.popupList[i]
            let popupSrc = null
            if (popup.mode == 1 && localStorage.getItem(`${this.game}_${popup.id}`) === ymd) {
              this.popupList.splice(i, 1)
              i--
              continue
            }
            if (popup.category == 1) {
              if (!popup.file_details[ip.country]) {
                this.popupList.splice(i, 1)
                i--
                continue
              } else popupSrc = popup.file_details[ip.country]
            } else {
              popupSrc = popup.file_details[this.$i18n.locale || this.lang] || popup.file_details.en
            }
            if (!popupSrc) {
              this.popupList.splice(i, 1)
              i--
              continue
            }
          }
          if (this.popupList.length) {
            this.$root.$emit(
              'showPop',
              'BackendPopup',
              Object.assign(ip, { popupList: this.popupList })
            )
          }
        }
      })

      // this.$root.$emit("showPop", "BackendPopup", ip);
    })
    this.$root.$on('showPop', (name, option) => {
      if (name === 'BoonPop') name = window.$gcbk('ids.boonPopName', 'BoonPop')
      // 如果有多个,添加到缓存
      if (this.cmpName) return this.cacheList.push({ name, option })

      this.option = option
      this.cmpName = name

      this.showOuter = true
      setTimeout(() => {
        this.showInner = true
      }, 0)
    })
    this.$root.$on('closePop', (name) => {
      this.cmpName = ''
      this.showInner = false
      this.option = {}
      setTimeout(() => {
        this.showOuter = false

        if (this.cacheList.length) {
          const newItem = this.cacheList.shift()
          setTimeout(() => this.$root.$emit('showPop', newItem.name, newItem.option), 300)
        }
      }, 0)
    })

    this.$root.$on('saveValidation', this.showLoginValidation)

    // 隐私协议
    if (this.$store.state.functionSwitch.showPopPolicy || this.$gameName === 'dc') this.showOtherPop()
  }
}
</script>

<style scoped lang="scss">
.bg-enter, .bg-leave-to {
  opacity: 0;
}
.bg-enter-active, .bg-leave-active{
  transition: opacity .3s;
}
.pop-bg {
  height: 100%;
  width: 100%;
  position: fixed;
  left: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100001;
}

.cmp-enter {
  opacity: 0;
  transform: translate(-50%, calc(-50% - 50PX)) !important;
}
.cmp-leave-to{
  opacity: 0;
  transform: translate(-50%, -50%) scale(0.9) !important;
}
.cmp-enter-active, .cmp-leave-active{
  transition: all .3s ease;
}
</style>
