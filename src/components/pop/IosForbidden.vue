<template>
  <container :hide-close="true" class="ios-pop">
    <div class="description">{{ $t('pkg_not_allow') }} <p v-html="calcTxt"></p> </div>
  </container>
</template>

<script>
import Container from '@/components/pop/container'

export default {
  name: 'IosForbidden',
  components: { Container },
  props: {
    option: Object
  },
  data () {
    return {
      interval: undefined,
      leaveCount: 3
    }
  },
  computed: {
    calcTxt () {
      return this.$t('channel_not_allow').replace('3', `<span>${this.leaveCount}</span>`)
    }
  },
  mounted () {
    const interval = setInterval(() => {
      this.leaveCount = this.leaveCount - 1

      if (this.leaveCount === 0) {
        clearInterval(interval)
        this.$root.$emit('closePop')
        setTimeout(() => {
          const openid = this.option.openid
          window.location.href = `${process.env.VUE_APP_OlD_STORE_URL_KOA}?openid=${encodeURIComponent(openid)}&from=paykoa`
        }, 0)
      }
    }, 1000)
  }
}
</script>

<style scoped lang="scss">
.ios-pop {
  background-color: white;
  border-radius: 0;

  .description {
    font-size: 28px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #303030;
    line-height: 40px;

    ::v-deep{
      span{
        color: #FF5E0F;
      }
    }
  }
}

::v-deep {
  .content{
    margin-top: 0!important;
  }
  .footer-wrapper {
    display: none;
  }
}
</style>
