<template>
  <container :title="$t('construction_faq')" :class="['faq-wrapper', $gameName]" id="faq-wrapper">
    <div class="construction" :class="[$gameName]">
      <div class="faq-wrap">
        <template v-if="$gameName === 'koa'">
          <div class="faq-list" v-for="(item, k) in new Array(7).fill('')" :key="k">
            {{ $t(`koa_construction_faq_q${k + 1}`) }}
            <br>
            <div>
              <template v-for="(item, i) in $t(`koa_construction_faq_q${k + 1}a`).split('<br/>')">
                <template v-if="k === 2">
                  <template v-if="item.indexOf('{0}') > -1">
                    {{ item.split('{0}')[0] }}<a target="__blank" href="https://koa.kingsgroupgames.com/pc/activity" :key="i">https://koa.kingsgroupgames.com/pc/activity</a>{{ item.split('{0}')[1] }}
                  </template>
                  <template v-else-if="item.indexOf('{1}') > -1">
                    {{ item.split('{1}')[0] }}<a target="__blank" href="https://koa.kingsgroupgames.com" :key="i">https://koa.kingsgroupgames.com</a>{{ item.split('{1}')[1] }}
                  </template>
                  <template v-else>{{ item }}</template>
                  <br :key="`${i}_${k}`">
                </template>
                <template v-else-if="k===4">{{ item }} <br :key="i + '5a1'"> {{$t('koa_construction_faq_q5a1')}} <br :key="i"></template>
                <template v-else>{{ item }} <br :key="i"></template>
              </template>
            </div>
          </div>
        </template>
        <template v-else-if="$gameName === 'aof' || $gameName === 'rom'">
          <div class="faq-list" v-for="(item, k) in new Array(7).fill('')" :key="k">
            {{ $t(`${$store.state.gameinfo.game}_construction_faq_q${k + 1}`) }}
            <br>
            <div>
              <template v-for="(item, i) in $t(`${$store.state.gameinfo.game}_construction_faq_q${k + 1}a`).split('<br/>')">
                <template v-if="k === 2">
                  <template v-if="item.indexOf('{0}') > -1">
                    {{ item.split('{0}')[0] }}<a target="__blank" href="https://koa.kingsgroupgames.com/pc/activity" :key="i">https://koa.kingsgroupgames.com/pc/activity</a>{{ item.split('{0}')[1] }}
                  </template>
                  <template v-else-if="item.indexOf('{1}') > -1">
                    {{ item.split('{1}')[0] }}<a target="__blank" href="https://koa.kingsgroupgames.com" :key="i">https://koa.kingsgroupgames.com</a>{{ item.split('{1}')[1] }}
                  </template>
                  <template v-else>{{ item }}</template>
                  <br :key="`${i}_${k}`">
                </template>
                <template v-else-if="k===4">{{ item }} <br :key="i + '5a1'"> {{$t(`${$store.state.gameinfo.game}_construction_faq_q5a1`)}} <br :key="i"></template>
                <template v-else>{{ item }} <br :key="i"></template>
              </template>
            </div>
          </div>
        </template>
        <template v-else-if="$gameName === 'dc'">
          <div class="faq-list" v-for="(item, k) in new Array(6).fill('')" :key="k">
            {{ $t(`${$store.state.gameinfo.game}_construction_faq_q${k + 1}`) }}
            <br>
            <div>
              <template v-for="(item, i) in $t(`${$store.state.gameinfo.game}_construction_faq_q${k + 1}a`).split('<br/>')">
                <template v-if="k===2">{{ item }} <br :key="i + '2a1'"> {{$t(`${$store.state.gameinfo.game}_construction_faq_q3a1`)}} <br :key="i"></template>
                <template v-else>{{ item }} <br :key="i"></template>
              </template>
            </div>
          </div>
        </template>
        <template v-else-if="$gameName === 'ssv'">
          <div class="faq-list" v-for="(item, k) in new Array(9).fill('')" :key="k">
            {{ $t(`construction_faq_q${k + 1}`) }}
            <br>
            <div>
              <template v-for="(item, i) in $t(`construction_faq_q${k + 1}a`).split('<br/>')">
                <template v-if="k === 3">
                  <template v-if="item.indexOf('{0}') > -1">
                    {{ item.split('{0}')[0] }}<a target="__blank" href="https://stateofsurvival.game/pc/activity" :key="i">https://stateofsurvival.game/pc/activity</a>{{ item.split('{0}')[1] }}
                  </template>
                  <template v-else-if="item.indexOf('{1}') > -1">
                    {{ item.split('{1}')[0] }}<a target="__blank" href="https://stateofsurvival.game/" :key="i">https://stateofsurvival.game/</a>{{ item.split('{1}')[1] }}
                  </template>
                  <template v-else>{{ item }}</template>
                  <br :key="`${i}_${k}`">
                </template>
                <template v-else-if="k===4">{{ item }} <br :key="i + '5a1'"> {{$t(`construction_faq_q5a1`)}} <br :key="i"></template>
                <template v-else>{{ item }} <br :key="i"></template>
              </template>
            </div>
          </div>
          <div class="faq-list" key="10">
            {{ $t('construction_faq_q10') }}
            <br>
            <div>
              {{ $t('construction_faq_q10a') }}
            </div>
          </div>
        </template>
        <template v-else-if="$gameName === 'ssv2'">
          <div class="faq-list" v-for="(item, k) in new Array(6).fill('')" :key="k">
            {{ $t(`${prefix}${k + 1}`) }}
            <br>
            <div>
              <template v-for="(item, i) in $t(`${prefix}${k + 1}a`).split('<br/>')">
                <template v-if="k===2">{{ item }} <br :key="i + '5a1'"> {{$t(`${prefix}3a1`)}} <br :key="i"></template>
                <template v-else>{{ item }} <br :key="i"></template>
              </template>
            </div>
          </div>
        </template>
        <template v-else-if="$gameName === 'foundation'">
          <div class="faq-list" v-for="(item, k) in new Array(6).fill('')" :key="k">
            {{ $t(`fnd_construction_faq_q${k + 1}`) }}
            <br>
            <div>
              <template v-for="(item, i) in $t(`fnd_construction_faq_q${k + 1}a`).split('<br/>')">
                <template v-if="k===3">{{ item }} <br :key="i + '3a1'"> {{$t(`fnd_construction_faq_q3a1`)}} <br :key="i"></template>
                <template v-else>{{ item }} <br :key="i"></template>
              </template>
            </div>
          </div>
        </template>
        <template v-else-if="$gameName === 'ssd'">
          <div class="faq-list" v-for="(item, k) in new Array(6).fill('')" :key="k">
            {{ $t(`ssd_construction_faq_q${k + 1}`) }}
            <br>
            <div>
              <template v-for="(item, i) in $t(`ssd_construction_faq_q${k + 1}a`).split('<br/>')">
                <template v-if="k===2">{{ item }} <br :key="i + '3a1'"> {{$t(`ssd_construction_faq_q3a1`)}} <br :key="i"></template>
                <template v-else>{{ item }} <br :key="i"></template>
              </template>
            </div>
          </div>
        </template>
        <template v-else>
          <div class="faq-list" v-for="(item, k) in new Array(6).fill('')" :key="k">
            {{ $t(`${$store.state.gameinfo.game}_construction_faq_q${k + 1}`) }}
            <br>
            <div>
              <template v-for="(item, i) in $t(`${$store.state.gameinfo.game}_construction_faq_q${k + 1}a`).split('<br/>')">
                <template v-if="k===2">{{ item }} <br :key="i + '2a1'"> {{$t(`${$store.state.gameinfo.game}_construction_faq_q3a1`)}} <br :key="i"></template>
                <template v-else>{{ item }} <br :key="i"></template>
              </template>
            </div>
          </div>
        </template>
      </div>
    </div>
    <div class="mask"></div>
  </container>
</template>

<script>
import Container from '@/components/pop/container'
export default {
  name: 'ChargeConstruction',
  components: { Container },
  computed: {
    prefix(){
      const prefixMap = {
        ssv2: 'ssv2_construction_faq_q',
      }
      return prefixMap[this.$gameName]
    }
  }
}
</script>

<style scoped lang="scss">
@import "~@/utils/utils.scss";
.construction {
  text-align: left;
  height: 60vh;
  overflow: auto;
  p:nth-of-type(n+2){
    @include setPcContent{
      margin-top: 10PX;
    }
    @include setMobileContent{
      margin-top: 15px;
    }
  }
  @include setPropByBp(
    $m: (padding-bottom: 60px),
    $p: (padding-bottom: 40PX),
  );
}
.mask {
  @include setPropByBp(
    $m: (height: 60px),
    $p: (height: 40PX),
  );
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  background: linear-gradient(to bottom, rgba(56, 56, 56, 0), rgba(56, 56, 56, 1));
}
.faq-wrap {
  .faq-title {
    text-align: center;
    color: #ffffff;
  }
  .faq-list {
    margin-top: 10PX;
    a {
      color: #919090;
      text-decoration: underline;
      &:active, &:focus, &:visited {
        color: #919090;
      }
    }
  }
}

.faq-wrapper.dc{
  .mask {
    background: linear-gradient(to bottom, rgba(195,203,225, 0), rgba(195,203,225, 1));
  }
  .faq-wrap {
    .faq-list{
      color: #525280;
    }
  }
}
.faq-wrapper.ssv{

}
</style>
