<template>
  <section class="pop-container pop-container-v1" :class="[size, $gameName]">
    <h3 v-if="!hideHeader" class="title">{{ option.title || title }} <i v-if="!hideClose" @click="$root.$emit('closePop')"></i></h3>
    <div v-if="!customContent" class="content">
      <slot></slot>
    </div>
    <slot v-else></slot>
    <div v-if="!hideFooter" class="footer-wrapper">
      <slot name="footerBtn">
        <div class="btn-confirm click-btn" @click="confirmClick">{{ option.confirmBtnTxt || $t('modalBtnOk') }}</div>
      </slot>
    </div>
  </section>
</template>

<script>
export default {
  name: 'container',
  props: {
    title: String,
    option: {
      type: Object,
      default: () => ({})
    },
    hideClose: <PERSON>olean,
    hideHeader: <PERSON><PERSON><PERSON>,
    hideFooter: <PERSON><PERSON><PERSON>,
    customContent: Boolean,
    size: String,
    customClass: String
  },
  methods: {
    confirmClick () {
      if (this.option.confirmBtnCb) this.option.confirmBtnCb()
      this.$root.$emit('closePop')
    }
  }
}
</script>

<style scoped lang="scss">
@import "~@/utils/utils.scss";

.pop-container{
  background-color: #383838;
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 100001;
  display: inline-block;
  padding: 30px 0 42px;
  width: calc(100% - 48px);
  max-width: 750px;

  .title{
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    color: #FFFFFF;
    line-height: 40px;
    letter-spacing: 1px;
    position: relative;
    font-size: 28px;

    i{
      @include bgCenter('koa/pop/pop-close.png', 36px, 36px);
      display: inline-block;
      position: absolute;
      cursor: pointer;
      top:50%;
      transform: translateY(-50%);
      right: 30px;
    }
  }

  .content{
    box-sizing: border-box;
    overflow-y: auto;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #919090;
    position: relative;
    margin-top: 30px;
    min-height: 100px;
    max-height: 60vh;
    padding: 0 60px;
    font-size: 24px;
    line-height: 33px;
  }

  .btn-confirm {
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    cursor: pointer;
    line-height: 72px;
    width: 220px;
    height: 72px;
    margin: 28px auto 0;
    background: linear-gradient(180deg, #F6E190 0%, #CBA455 100%);
    @include flexCenter;
    font-size: 28px;
  }
}
@include setPcContent{
  .pop-container{
    padding: 27px 0 30px;
    width: 654px;

    .title{
      font-size: 22px;
      line-height: 29px;
      max-width: 90%;
      margin: 0 auto;

      i{
        right: 0;
        @include bgCenter('koa/pop/pop-close.png', 25px, 25px);
      }
    }

    .content{
      margin-top: 25px;
      min-height: 80px;
      max-height: 60vh;
      padding: 0 39px;
      font-size: 16px;
      line-height: 21px
    }

    .btn-confirm{
      font-size: 24px;
      margin: 20px auto 0;

      width: 220px;
      height: 60px;
    }
  }
}

.pop-container.dc{
  background: #C3CBE1;
  .title{
    @extend .dc-stroke;
    line-height: 1.2;

    i{
      @include bgCenterForDC('coupon/pop-close.png', 36px, 36px);
    }
  }
  .btn-confirm {
    color: #F4FBFF;
    font-size: 28px;
    white-space: nowrap;
    padding: 0;
    width: 220px;
    height: 72px;
    @extend .dc-btn-decoration;
    @include flexCenter;
    @extend .dc-stroke;
  }

  @include setPcContent{
    .title{
      i{
        @include bgCenterForDC('coupon/pop-close.png', 30px, 30px);
      }
    }

    .btn-confirm {
      color: #F4FBFF;
      font-size: 24px;
      white-space: nowrap;
      padding: 0;
      width: 220px;
      height: 60px;
    }
  }
}
.pop-container.ssv{
  .btn-confirm {
    background: #FF5E0F;
    font-size: 30px;
    line-height: 72px;
    padding: 0 79px;
    height: 72px;
    border-radius: 8px;
    margin: 28px auto 0;
    color: #FFFFFF;
  }

  @include setPcContent{
    .btn-confirm {
      font-size: 24px;
      line-height: 53px;
      padding: 0 59px;
      height: 53px;
      border-radius: 8px;
      margin: 20px auto 0
    }
  }
}
.pop-container.ssv2{
  .btn-confirm{
    color: #FFFFFF;
    background: #FF5E0F;
    border-radius: 8px
  }
}
</style>
