<script>
import ContainerV2 from '@/components/pop/containerV2.vue'
export default {
  name: 'privateConfirmPop',
  components: { ContainerV2 },
  props: {
    option: {
      type: Object,
      default: () => {}
    }
  },
  methods: {
    closePop () {
      if (this.option.no) this.option.no()
      this.$root.$emit('closePop')
    },
    confirm () {
      if (this.option.ok) this.option.ok()

      this.$root.$emit('changeDePopPrivacy')
      this.$root.$emit('closePop')
    }
  }
}
</script>

<template>
  <container-v2 class="custom-pop">
    <div class="header">
      <span>DURCHLESEN！</span>
      <i @click="closePop"></i>
    </div>
    <div class="content">
      <div class="strong">KEIN WIDERRUFSRECHT！</div>
      Wenn du auf "Kaufen" tippst, stimmst du der unmittelbaren Erfüllung des Vertrags zu und akzeptierst, dass du dadurch dein Widerrufsrecht verlierst .(siehe auch <a href="https://funplus.com/terms-conditions/#section-13" target="_blank">Rückerstattungsbedingungen</a>).
    </div>
    <div class="footer">
      <div @click="closePop" class="btn-cancel btn">ABBRECHEN</div>
      <div class="btn-confirm btn" @click="confirm">Zustimmen und fortfahren.</div>
    </div>
  </container-v2>
</template>

<style scoped lang="scss">
@import "~@/utils/utils.scss";
.pop-container{
  height: 450px;
  background: rgb(50, 30, 9);
  border-radius: 10px;
  padding-top: 10px;
  display: flex;
  flex-direction: column;
  padding-bottom: 40px;
  border: 1px solid #827350;

  .header{
    height: 80px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;

    span{
      font-family: SourceHanSansCN, SourceHanSansCN;
      font-weight: bold;
      font-size: 32px;
      color: white;
      max-width: 80%;
      line-height: 1;
    }

    i{
      display: inline-block;
      @include bgCenterForCommon('pop/close-popup.png',29px,30px);
      position: absolute;
      right: 30px;
    }
  }
  .content{
    flex: 1;
    margin-bottom: 10px;
    overflow-y: scroll;
    padding: 0 30px;

    font-family: SourceHanSansCN, SourceHanSansCN;
    font-weight: 500;
    font-size: 24px;
    color: white;
    line-height: 35px;
    letter-spacing: 1px;
    text-align: left;

    .strong{
      font-family: SourceHanSansCN, SourceHanSansCN;
      font-weight: bold;
      font-size: 24px;
      color: #FEB522;
      margin-bottom: 8px;
    }

    a{
      color: white;
      text-decoration: underline;
    }
  }
  .footer{
    display: flex;
    align-items: center;
    justify-content: center;

    .btn{
      border-radius: 2px;
      font-family: PingFangSC, PingFang SC;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      font-weight: 600;
      line-height: 1;
      text-shadow: 0px 4px 4px rgba(0,0,0,0.7);
    }

    .btn-cancel{
      color: white;
      @include bgCenterForCommon('privacy/pop-privacy-cancel-bg-m.png', 260px, 60px)
    }

    .btn-confirm{
      color: #FFFFFF;
      margin-left: 40px;
      @include bgCenterForCommon('privacy/pop-privacy-confirm-bg-m.png', 260px, 60px);
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  .checkbox{
    margin-top: 10px;
    margin-bottom: 30px;
  }
}

@include setPcContent{
  .pop-container{
    height: 340px;
    border-radius: 5px;
    padding-bottom: 30px;

    .header{
      height: 60px;

      span{
        font-size: 26px;
      }

      i{
        @include bgCenterForCommon('pop/close-popup.png',20px,20px);
        cursor: pointer;
      }
    }

    .content{
      font-size: 18px;
      line-height: 24px;
    }

    .footer{
      .btn{
        border-radius: 1px;
        width: 200px;
        height: 40px;
        font-size: 16px;
        cursor: pointer;
      }

      .btn-cancel{
        @include bgCenterForCommon('privacy/pop-privacy-cancel-bg-pc.png', 180px, 40px)
      }

      .btn-confirm{
        margin-left: 30px;
        @include bgCenterForCommon('privacy/pop-privacy-confirm-bg-pc.png', 180px, 40px)
      }
    }
  }
}
</style>
