<template>
  <container :title="$t('pending_page_pop_title')" :option="option">
    <div class="err-tips-pop">
      <div class="pop-msg">{{ $t('pending_page_pop_tips') }}</div>
    </div>
  </container>
</template>

<script>
import Container from '@/components/pop/container'
export default {
  name: 'CallbackPendingTips',
  components: { Container },
  data () {
    return {
      option: {
        title: this.$t('pending_page_pop_title'),
        confirmBtnTxt: this.$t('cb_back_home'),
        confirmBtnCb: () => this.$router.replace('/')
      }
    }
  }
}
</script>

<style scoped lang="scss">
@import "~@/utils/utils.scss";

.err-tips-pop{
  text-align: center;
  .pop-msg{
    font-size: 26px;
    line-height: 30px;
    font-weight: 500;
    font-family: PingFangSC-Regular, PingFang SC;

    @include setPropByBp(
      $m:( font-size: 26px, font-weight: 400, color: #8C8C8C, line-height: 37px,),
      $p:( font-size: 20px, font-weight: 400, color: #8C8C8C, line-height: 28px,),
    )
  }
}
</style>
