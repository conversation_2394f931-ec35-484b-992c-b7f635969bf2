const images = [
    // 'https://kg-web-cdn.akamaized.net/prod/versionAdmin/source/web-pay-unique/dev/coupon-item.svg',
    // 'https://kg-web-cdn.akamaized.net/prod/versionAdmin/source/web-pay-unique/dev/coupon-item-chosen.svg'
    require('@/assets/common/coupon/coupon-item.png'),
    require('@/assets/common/coupon/coupon-item-chosen.png')
]

const loadImage = (src) => {
    if (!src) return Promise.resolve()
    return new Promise((resolve, reject) => {
        const link = document.createElement('link')
        link.as = 'image'
        link.rel = 'preload'
        link.href = src
        document.head.appendChild(link)
        
        link.onload = resolve
        link.onerror = reject
        setTimeout(reject, 5000)
    })
}

const loadImages = async () => {
    while (images.length) {
        try {
            await loadImage(images.shift())
        } catch (error) {
            console.error(error)
        }
    }
}

Promise.all(Array.from({ length: 1 }, loadImages))
