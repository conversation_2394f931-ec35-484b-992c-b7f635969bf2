<template>
  <div class="common-part-wrapper" :class="[$gameName, { sdk: $store.state.IS_CHECKOUT_SDK }]">
    <div class="label">
      <slot name="label">{{ labelFont }}</slot>
    </div>
    <div class="body">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CommonPart',
  props: {
    labelFont: {
      type: String,
      default: ''
    }
  }
}
</script>

<style scoped lang="scss">
@import "~@/utils/utils.scss";

.common-part-wrapper{
  display: flex;
  width: 100%;
  margin: 0 auto;

  @include setPropByBp(
    $m: (flex-direction: column,width: calc(100% - 80px),margin-top: 24px),
    $p: (margin-top: 35px)
  );
  .label{
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    color: #CACACA;
    flex-shrink: 0;

    @include setPropByBp(
      $m: (font-size: 28px,line-height: 40px, margin-bottom: 8px,text-align: left),
      $p: (width: 233px,font-size: 18px,padding-right: 28px,line-height: 30px,text-align: right)
    );
  }

  .body{
    flex: 1;
  }
}

.common-part-wrapper.dc{
  .label{
    color: #F4FBFF;
  }
}
.common-part-wrapper.sdk{
  flex-direction: column;
  max-width: 940PX;
  margin-top: 36px;

  .label{
    text-align: left;
    margin-bottom: 16px;
    width: 100%;
  }

  @include setPcContent{
    margin-top: 24px;

    .label{
      margin-bottom: 8px;
    }
  }
}
</style>
