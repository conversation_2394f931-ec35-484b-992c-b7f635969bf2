<template>
<transition name="message">
  <section v-if="message" class="msg-wrapper">{{ message }}</section>
</transition>
</template>

<script>
export default {
  name: 'message',
  data () {
    return {
      message: ''
    }
  },
  methods: {
    open (msg, duration, cb) {
      this.message = msg
      setTimeout(() => {
        this.message = ''
        setTimeout(() => { cb() }, 300)
      }, duration)
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/utils/utils.scss";

.message-enter-active,.message-leave-active{
  transition: all .36s ease;
}

.message-enter{
  opacity: 0;
  transform: translate(-50%,calc(-50% - 50px)) !important;
}

.message-leave-to{
  opacity: 0;
  transform: translate(-50%,calc(-50% - 50px)) !important;
}

  .msg-wrapper{
    position: fixed;
    left: 50%;
    top: 50%;
    transform: translate(-50%,-50%);
    z-index: 10000000;
    color: #f9f9f9;
    background-color: rgba(0,0,0,.75);
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;

    @include setPropByBp(
      $p: (max-width: 700px, min-height: 40px,padding: 14px 20px,font-size: 16px,border-radius: 12px),
      $m: (max-width: calc(100% - 100px), min-width: 220px, line-height: 36px,min-height: 75px, padding: 12px 30px,font-size: 24px,border-radius: 16px)
    );
  }
</style>
