import message from './toast.vue'
import Vue from 'vue'

const getVmSingleton = function (cmp) {
  let $vm = null
  return function () {
    if ($vm) return $vm
    else {
      const Constructor = Vue.extend(cmp)
      $vm = new Constructor({
        el: document.createElement('div')
      })
      document.body.appendChild($vm.$el)
      return $vm
    }
  }
}

const messageCmp = {
  install (Vue, options) {
    const vm = getVmSingleton(message)()
    const obj = {}
    const queue = []
    let isPending = false

    const openVm = (msg, duration) => {
      isPending = true
      vm.open(msg, duration, obj.next.bind(obj))
    }
    const isTipsExit = target => queue.some(item => item.msg === target) || vm.message === target;

    ['err', 'success'].forEach(key => {
      obj[key] = function (msg, duration = 2000) {
        if (isTipsExit(msg)) return null
        if (queue.length || isPending) queue.push({ msg, duration })
        else openVm(msg, duration)
      }
    })

    obj.next = function () {
      isPending = false
      if (queue.length) {
        const m = queue.shift()
        openVm(m.msg, m.duration)
      }
    }

    Vue.prototype.$toast = obj
    window.$toast = obj
  }
}

export default messageCmp
