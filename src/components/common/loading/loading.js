import loadingCmp from './loading.vue'

// import loadingComponentMobile from '@/components/mobile/common/loading'
// import loadingComponentPc from '@/components/pc/common/loading'
// import { isMobile } from "@/utils/utils";

const Loading = {
  install (Vue, options) {
    const loading = {}
    const getVm = (function () {
      let $vm = null
      return function () {
        if ($vm) return $vm
        else {
          // let cmp = isMobile ? loadingComponentMobile : loadingComponentPc
          const cmp = loadingCmp
          const Constructor = Vue.extend(cmp)
          $vm = new Constructor({
            el: document.createElement('div')
          })
          document.body.appendChild($vm.$el)
          return $vm
        }
      }
    })()
    const vm = getVm()
    loading.show = function () {
      vm.showLoading()
    }
    loading.hide = function () {
      vm.hideLoading()
    }
    Vue.prototype.$loading = loading
    if (options) console.log(options)
  }
}

export default Loading
