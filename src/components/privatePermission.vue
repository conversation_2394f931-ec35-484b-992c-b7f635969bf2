<script>
import { getAmeDo } from '@/server'

export default {
  name: 'privatePermission',
  computed: {
    calcPop () {
      return !this.isCheck || !this.isPop
    }
  },
  watch: {
    calcPop (val) {
      window.__needDEPop = val
    }
  },
  data () {
    return {
      isCheck: false,
      isPop: false
    }
  },
  methods: {
    initState () {
      const params = {
        p0: 'web',
        p1: 9,
        p2: 2531,
        p3: 'api',
        game: this.$store.state.gameinfo.gameProject.split('_')[0]
      }
      getAmeDo(params)
        .then(res => {
          const { data, code } = res
          if (code === 0) {
            window.__needDEPop = true

            this.isCheck = data.check
            this.isPop = data.popup
          }
        })
    },
    notifyServer (type) {
      const params = {
        p0: 'web',
        p1: 9,
        p2: 2532,
        p3: 'api',
        game: this.$store.state.gameinfo.gameProject.split('_')[0]
      }
      if (type === 'check') {
        params.set_type = 0
        params.set_status = Number(this.isCheck)
      }
      if (type === 'pop') {
        params.set_type = 1
        params.set_status = 1

        // 1选中 0 不选中
        this.isCheck = true
      }

      getAmeDo(params)
        .then(res => {
          const { code } = res
          if (code !== 0) {
            if (type === 'pop') this.isPop = false
            if (type === 'check') this.isCheck = !this.isCheck
          }
        })
    }
  },
  created () {
    this.initState()
    this.$root.$on('changeDePopPrivacy', () => this.notifyServer('pop'))
  }
}
</script>

<template>
  <div class="checkbox">
    <label>
      <input @change="notifyServer('check')" v-model="isCheck" type="checkbox">
      Ich habe
      <a href="https://funplus.com/terms-conditions/#section-13" target="_blank">Rückerstattungsrichtlinie</a>
      gelesen und stimme zu.
    </label>
  </div>
</template>

<style scoped lang="scss">
@import "~@/utils/utils.scss";

.checkbox{
  margin-bottom: 30px;
  label{
    text-align: left;
    font-family: PingFang-SC, PingFang-SC;
    font-weight: 500;
    font-size: 22px;
    color: #FFFFFF;
    font-style: normal;
    display: flex;

    a{
      display: inline;
      color: #FEB522;
      text-decoration: underline;
      margin: 0 3px;
    }

    input{
      appearance: none;
      -webkit-appearance:none;

      width: 30px;
      height: 30px;
      background: white;
      border-radius: 4px;
      margin-right: 8px;
      flex-shrink: 0;
      border: 1px solid #666666;

      &:checked{
        @include bgCenterForCommon('privacy/privacyPermissionCheckBg.png', 30px, 30px);
      }
    }
  }
}

@include setPcContent{
  .checkbox{
    cursor: pointer;
    margin-bottom: 20px;
    label{
      font-size: 18px;
      line-height: 23px;
      cursor: pointer;

      input{
        width: 20px;
        height: 20px;
        border-radius: 3px;
        margin-right: 5px;

        &:checked{
          @include bgCenterForCommon('privacy/privacyPermissionCheckBg.png', 20px, 20px);
        }
      }
    }
  }
}
</style>
