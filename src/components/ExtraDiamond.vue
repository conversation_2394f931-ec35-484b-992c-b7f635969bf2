<template>
  <div class="extra-diamond-wrapper" @click.stop>
    <div class="select-wrapper">
      <div :class="['label-wrapper', {'label-wrapper-active': chosenSpecialDiamond.product_id}]" @click="showList = !showList">
        <div class="my-card-logo">
          <img src="../assets/common/channel/mycard-logo.png" alt="">
        </div>
        <div class="right-part">
          <div class="chosen-diamond" v-if="chosenSpecialDiamond.product_id">
            <span class="products">{{ chosenSpecialDiamond.coin }} <i></i></span>
            <span>{{ chosenSpecialDiamond.price }} {{ chosenSpecialDiamond.currency_symbol }}</span>
          </div>
          <div class="more" v-else>{{ $t('load_more') }}</div>
          <i :class="[{'toggle-active': showList}]"></i>
        </div>
      </div>
      <transition name="option">
        <div class="option-list" v-if="showList">
          <div :class="['option-item', { 'option-item-active': diamondItem.product_id === chosenSpecialDiamond.product_id }]" @click="toggleStatus(index)" v-for="(diamondItem, index) in extraList" :key="index">
            <span class="products">{{ diamondItem.coin }} <i></i></span>
            <span>{{ diamondItem.price }} {{ diamondItem.currency_symbol }}</span>
          </div>
        </div>
      </transition>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'

export default {
  name: 'extraDiamond',
  data () {
    return {
      showList: false,
      extraList: []
    }
  },
  computed: {
    ...mapState('formdata', ['chosenDiamond']),
    chosenSpecialDiamond () {
      return this.$store.getters['formdata/TWMyCard'] ? this.chosenDiamond : {}
    }
  },
  methods: {
    toggleStatus (index) {
      this.$store.commit('formdata/setChosenDiamond', this.extraList[index])
      this.showList = false
    }
  },
  created () {
    this.$root.$on('updateSpecialDiamond', value => {
      this.extraList = value
    })
    this.$root.$on('BodyClick', () => {
      this.showList = false
    })
  }
}
</script>

<style scoped lang="scss">
@import "~@/utils/utils.scss";

.option-enter-active{
  transform: translateY(40px);
  transition: all .3s ease-in-out;
}
.option-enter-to{
  transform: translateY(0);
}

.option-leave-active {
  transition: all .3s;
}

.option-leave-to{
  opacity: 0;
}

.extra-diamond-wrapper{
  display: inline-block;
  position: relative;
  z-index: 1;
  width: 100%;
  margin-top: 20px;

  .label-wrapper{
    display: flex;
    align-items: center;
    justify-content: space-between;
    z-index: 2;
    position: relative;
    background: linear-gradient(to bottom, rgba(198, 165, 88, .2), rgb(158, 113, 61));
    border: 1px solid #988B60;
    height: 54px;
    padding:0 20px;
    border-radius: 4px;
    cursor: pointer;

    .my-card-logo{
      height: 43px;
      display: flex;
      img{
        height: 100%;
      }
    }

    .right-part{
      display: flex;
      align-items: center;
      justify-content: center;

      .more{
        font-size: 26px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #FFFFFF;
      }

      .chosen-diamond{
        display: flex;
        justify-content: space-between;
        align-items: center;
        cursor: pointer;
        font-size: 28px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #FFFFFF;

        span{
          display: flex;
          align-items: center;
          white-space: nowrap;

          i{
            @include bgCenterForSSV('diamond/diamond.png', 29px, 29px);
            margin-left: 4px;
            margin-right: 20px;
          }
        }
      }

      i{
        @include bgCenterForCommon('channel/mycard-toggle.png', 32px, 32px);
        transition: all .3s;
        &.toggle-active{
          transform: rotate(450deg);
        }
      }
    }

    &.label-wrapper-active{
      position: relative;
      box-sizing: border-box;
      border-color: #FF5E0F;
      &:before{
        display: inline-block;
        content: ' ';
        position: absolute;
        right: -1px;
        top: -1px;
        z-index: 1;
        @include bgCenterForCommon('channel/choose-active.png', 29px, 29px);
      }
    }
  }

  .option-list{
    position: absolute;
    top: 100%;
    width: 100%;
    z-index: 1;
    background: #000000;
    border-radius: 4px;
    border: 1px solid #988B60;
    margin-top: 10px;

    .option-item{
      display: flex;
      justify-content: space-between;
      align-items: center;
      cursor: pointer;
      line-height: 53px;
      font-size: 28px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #FFFFFF;
      border-bottom: 1px solid #988B60;
      padding: 0 20px;
      transition: all .3s;

      span{
        display: flex;
        align-items: center;
        white-space: nowrap;

        i{
          @include bgCenterForSSV('diamond/diamond.png', 18px, 18px);
          margin-left: 5px;
        }
      }

      &:last-of-type{
        border: none;
      }

      &.option-item-active, &:hover{
        background: rgba(255, 255, 255, .2);
        color: #FF5E0F;
      }
    }
  }
}

@include setPcContent {
  .extra-diamond-wrapper{
    width: 426px;
    .label-wrapper{
      height: 44px;
      .my-card-logo{
        height: 33px;
      }
      .right-part{
        .more{
          font-size: 18px;
        }
        .chosen-diamond{
          font-size: 18px;

          span{
            i{
              @include bgCenterForSSV('diamond/diamond.png', 18px, 18px);
              margin-right: 30px;
              margin-left: 2px;
            }
          }
        }
        i{
          @include bgCenterForCommon('channel/mycard-toggle.png', 24px, 24px)
        }
      }
      &.label-wrapper-active{
        &:before{
          @include bgCenterForCommon('channel/choose-active.png', 24px, 24px);
        }
      }
    }
    .option-list{
      margin-top: 4px;
      overflow-y: scroll;
      max-height: 230px;

      .option-item{
        line-height: 40px;
        font-size: 18px;

        span{
          i{
            margin-left: 2px;
          }
        }
      }
    }
  }
}
</style>
