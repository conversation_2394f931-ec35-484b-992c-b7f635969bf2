// 全局SCSS变量文件

// 颜色变量
$primary-color: #007bff;
$secondary-color: #6c757d;
$success-color: #28a745;
$danger-color: #dc3545;
$warning-color: #ffc107;
$info-color: #17a2b8;
$light-color: #f8f9fa;
$dark-color: #343a40;

// 主题颜色
$theme-primary: #007bff;
$theme-secondary: #6c757d;
$theme-accent: #ffc107;

// 文本颜色
$text-primary: #212529;
$text-secondary: #6c757d;
$text-muted: #868e96;
$text-white: #ffffff;

// 背景颜色
$bg-primary: #ffffff;
$bg-secondary: #f8f9fa;
$bg-dark: #343a40;
$bg-light: #f8f9fa;

// 边框颜色
$border-color: #dee2e6;
$border-light: #e9ecef;
$border-dark: #adb5bd;

// 字体大小
$font-size-xs: 0.75rem;
$font-size-sm: 0.875rem;
$font-size-base: 1rem;
$font-size-lg: 1.25rem;
$font-size-xl: 1.5rem;

// 字体权重
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-bold: 700;

// 间距
$spacer: 1rem;
$spacer-xs: 0.25rem;
$spacer-sm: 0.5rem;
$spacer-md: 1rem;
$spacer-lg: 1.5rem;
$spacer-xl: 3rem;

// 边框半径
$border-radius: 0.25rem;
$border-radius-sm: 0.125rem;
$border-radius-lg: 0.5rem;
$border-radius-xl: 1rem;

// 阴影
$box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
$box-shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
$box-shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);

// 断点
$breakpoint-xs: 0;
$breakpoint-sm: 576px;
$breakpoint-md: 768px;
$breakpoint-lg: 992px;
$breakpoint-xl: 1200px;

// Z-index
$zindex-dropdown: 1000;
$zindex-sticky: 1020;
$zindex-fixed: 1030;
$zindex-modal-backdrop: 1040;
$zindex-modal: 1050;
$zindex-popover: 1060;
$zindex-tooltip: 1070;

// 动画时间
$transition-base: all 0.2s ease-in-out;
$transition-fade: opacity 0.15s linear;
$transition-collapse: height 0.35s ease;

// 游戏特定颜色
$koa-primary: #8B4513;
$koa-secondary: #DAA520;
$dc-primary: #000000;
$dc-secondary: #FF0000;
$foundation-primary: #4A90E2;
$foundation-secondary: #7ED321;

// 支付相关颜色
$payment-success: #28a745;
$payment-error: #dc3545;
$payment-warning: #ffc107;
$payment-info: #17a2b8;

// 按钮颜色
$btn-primary-bg: $primary-color;
$btn-primary-border: $primary-color;
$btn-secondary-bg: $secondary-color;
$btn-secondary-border: $secondary-color;

// 表单颜色
$input-bg: #ffffff;
$input-border-color: #ced4da;
$input-focus-border-color: #80bdff;
$input-focus-box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);

// 卡片样式
$card-bg: #ffffff;
$card-border-color: rgba(0, 0, 0, 0.125);
$card-border-radius: $border-radius;
$card-box-shadow: $box-shadow;

// 导航样式
$navbar-bg: #ffffff;
$navbar-border-color: #dee2e6;
$navbar-brand-color: $text-primary;
$navbar-nav-link-color: $text-secondary;

// 模态框样式
$modal-backdrop-bg: #000000;
$modal-backdrop-opacity: 0.5;
$modal-content-bg: #ffffff;
$modal-content-border-color: rgba(0, 0, 0, 0.2);

// 工具类
$enable-rounded: true;
$enable-shadows: true;
$enable-gradients: false;
$enable-transitions: true;
$enable-reduced-motion: true;
$enable-grid-classes: true;
$enable-caret: true;
$enable-button-pointers: true;
$enable-rfs: true;
$enable-validation-icons: true;
$enable-negative-margins: false;
$enable-deprecation-messages: true;
$enable-important-utilities: true;
