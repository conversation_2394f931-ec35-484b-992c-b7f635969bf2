@import "~@/utils/utils.scss";
@import "standard.scss";

.foundation-btn-decoration{
  color: #3F342E;
  background: #FFE074;
  box-shadow: 0px 3px 4px -2px rgba(0,0,0,0.4);
  border-radius: 2px;
  border: 2px solid #FFF4AB;
}
@mixin foundation-btn-decoration{
  color: #3F342E;
  background: #FFE074;
  box-shadow: 0px 3px 4px -2px rgba(0,0,0,0.4);
  border-radius: 2px;
  border: 2px solid #FFF4AB;
}

/* 1、入口 */
#app.foundation{
  background-image: url(https://kg-web-cdn.akamaized.net/prod/versionAdmin/source/web-pay-hub/foundation-bg2.jpg);
  background-color: rgb(40, 80, 80);
}
/* 2、基础组件 */
.common-part-wrapper.foundation{
  .label{
    color: #EEF2D5 !important;
  }
}
.pop-container-v1.foundation{
  background: #D8DBD4 !important;
  border-radius: 0!important;
  .title{
    line-height: 1.2;
    color: #3F342E;

    i{
      @include bgCenterForFoundation('coupon/pop-close.png', 36px, 36px);
    }
  }
  .btn-confirm {
    font-size: 27px;
    white-space: nowrap;
    padding: 0;
    width: 220px;
    height: 72px;
    @include foundation-btn-decoration;
    @include flexCenter;
  }

  @include setPcContent{
    .title{
      i{
        @include bgCenterForFoundation('coupon/pop-close.png', 31px, 31px);
      }
    }

    .btn-confirm {
      font-size: 24px;
      white-space: nowrap;
      padding: 0;
      width: 220px;
      height: 60px;
    }
  }
}
/* 3、常用业务 */
/* 登录 */
#login-part-wrapper.foundation{
  .login-status__login{
    .avatar{
      width: 100px;
      height: 100px;
      &[lazy=error], &[lazy=loading]{
        background-image: url("~@/assets/foundation/login/home-default-image.png") !important;
        background-color: transparent;
      }
      margin-right: 20px;
    }
    .user-info{
      .row-1{
        .name{
          font-size: 28px;
        }
        .toggle{
          text-decoration: none;
          padding: 3px 11px 3px 34px;
          font-size: 20px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #633B00;
          line-height: 28px;
          position: relative;
          margin-left: 10px;
          white-space: nowrap;
          flex-shrink: 0;
          @include foundation-btn-decoration;
          &:after{
            @include bgCenterForKoa('koa/login/toggle-account.png', 16px, 14px);
            content: ' ';
            display: inline-block;
            position: absolute;
            left: 12px;
            width: 14px;
            height: 14px;
            top: 50%;
            transform: translateY(-50%);
          }
        }
      }
      .row-2{
        margin-top: 12px;
        font-size: 20px;

        .server{
          margin-left: 30px;
        }
      }
    }
  }
  .login-status__not-login{
    .login-input-wrapper{
      border-color: #507970;
      margin-right: 12px;
      background: linear-gradient(to bottom, #142029 0%, #182D33 100%);
      input{
        color: #EEF2D5;
      }

      i{
        @include bgCenterForFoundation('login/login-tips-btn.png', 39px, 39px);
      }
    }
    .btn-login{
      font-weight: 600;
      border-radius: 0;
      width: 216px;
      text-align: center;
      padding: 0 10px;
      height: 64px;
      @include foundation-btn-decoration;
    }
  }

  @include setPcContent(){
    .login-status__not-login{
      .login-input-wrapper{
        max-width: 428PX;

        i{
          width: 25px;
          height: 25px;
        }
      }
      .btn-login{
        margin-left: 0;
        height: 50px;
        width: auto;
        min-width: 150px;
        font-size: 22px;
        padding: 0 10px;
        line-height: 48px;
      }
    }
    .login-status__login{
      .avatar{
        width: 70px;
        height: 70px;
      }
      .user-info{
        margin-left: 10px;
        .row-1{
          .name{
            font-size: 20PX;
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 600;
            color: #FFFFFF;
            line-height: 28PX;
          }

          .toggle{
            padding: 0 10PX 0 30PX;
            font-size: 16PX;
            line-height: 25PX;
            margin-left: 28PX;
            &:after{
              @include bgCenterForKoa('koa/login/toggle-account.png', 14PX, 14PX);
              width: 14PX;
              height: 14PX;
              left: 10PX;
            }
          }
        }
        .row-2{
          margin-top: 10px;
          font-size: 16PX;

          .server{
            margin-left: 20PX;
          }
        }
      }
    }
  }
}
/* 优惠券 */
#coupon-bar-wrapper.foundation{
  .coupons-wrapper {
    border-radius: 0;
  }
  .coupons-wrapper__not-login {
    @include bgCenterForFoundation('coupon/coupon-bar__not-login-m.png', 672px, 78px);
    color: #3F342E;
    line-height: 78px;

    &:after {
      @include bgCenterForFoundation('coupon/coupon-bar-icon-exclamation-mark.png', 30px, 30px);
    }
  }
  .coupons-wrapper__available {
    @include bgCenterForFoundation('coupon/pay-coupon-bg_mobile-available.png', 675px, 78px);
    font-size: 24px;
    line-height: 82px;
    color: #3F342E;

    i {
      @include bgCenterForFoundation('coupon/pay-coupon-bg_mobile-available-arrow.png', 24px, 24px);
    }
  }
  .coupons-wrapper__chosen, .coupons-wrapper__unavailable{
    @include bgCenterForFoundation('coupon/pay-coupon-bg_mobile-chosen.png', 675px, 78px);
    .left{
      span{
        color: #3F342E;
        line-height: 1;

        &:after{
          @include bgCenterForFoundation('coupon/coupon_chosen_gou.png', 24px, 24px);
        }
      }
    }

    .right{
      color: #4A402C;
      &>span{
        display: flex;
        align-items: center;
      }
      .diamond-icon {
        @include bgCenterForFoundation('diamond/diamond-icon.png', 22px, 22px);
      }
    }
  }
  .coupons-wrapper__no_coupon{
    @include bgCenterForFoundation('coupon/pay-coupon-bg_mobile_login.png', 675px, 78px);
    color: #959595;
    line-height: 1;
  }

  @include setPcContent{
    .coupons-wrapper__not-login{
      @include bgCenterForFoundation('coupon/coupon-bar__not-login-pc.png', 428px, 47px);
      line-height: 47px;
      padding-left: 37px;
      &:after {
        @include bgCenterForFoundation('coupon/coupon-bar-icon-exclamation-mark.png', 14px, 14px);
      }
    }
    .coupons-wrapper__available{
      @include bgCenterForFoundation('coupon/pay-coupon-bg_mobile-available-pc.png', 428px, 47px);
      font-size: 14px;
      line-height: 47px;

      i {
        @include bgCenterForFoundation('coupon/pay-coupon-bg_mobile-available-arrow.png', 18px, 18px);
      }
    }
    .coupons-wrapper__chosen, .coupons-wrapper__unavailable{
      @include bgCenterForFoundation('coupon/pay-coupon-bg_mobile-chosen-pc.png', 428px, 47px);
      .left{
        span{
          &:after{
            @include bgCenterForFoundation('coupon/coupon_chosen_gou.png', 14px, 14px);
          }
        }
      }
      .right{
        .diamond-icon {
          @include bgCenterForFoundation('diamond/diamond-icon.png', 16px, 16px);
        }
      }
    }
    .coupons-wrapper__no_coupon{
      @include bgCenterForFoundation('coupon/pay-coupon-bg_mobile_login-pc.png', 428px, 47px);
    }
  }
}
#ticket-chosen-wrapper.foundation{
  .pop-main{
    background: #D8DBD4;
    border-radius: 0;

    .pop-close {
      top: 20px;
      right: 30px;
      @include bgCenterForFoundation('coupon/pop-close.png', 40px, 40px);
    }

    .divider{
      background: #939393;
    }

    .pop-title{
      h3{
        color: #3F342E;
      }
      span{
        color: #8B8780;
      }
    }

    .nav-btn-wrapper {
      .nav {
        color: #8B8780;

        &.nav-active {
          color: #3F342E;

          &:after {
            background: #3F342E;
          }
        }
      }
    }

    .main-container{
      .btn-confirm {
        margin-top: 0;
        @extend .foundation-btn-decoration;
        width: 220px;
        height: 72px;
        @include flexCenter;
        font-size: 28px;
        font-weight: bold;
      }
    }

    .coupon-repeat-tips{
      color: #141414;
    }
  }

  @include setPcContent{
    .pop-main{
      .pop-close {
        width: 32px;
        height: 32px;
      }

      .main-container{
        .btn-confirm {
          width: 220px;
          height: 60px;
          font-size: 23px;
        }
      }
    }
  }
}
#ticket-wrapper.foundation{
  .ticket-list {
    .item {
      @include bgCenterForFoundation('coupon/coupon-pop-list-item.png', 614px, 88px);

      &.item__active {
        @include bgCenterForFoundation('coupon/coupon-pop-list-item-active.png', 614px, 88px);
      }

      .left {
        .title{
          color: #141414;
          @include flexCenter;
        }
        i.diamond-icon {
          @include bgCenterForFoundation('diamond/diamond-icon.png', 18px, 18px);
        }
      }

      .right{
        .desc{
          color: #141414;
        }
        .time {
          color: #141414;
        }
      }
    }
  }

  @include setPcContent{
    .ticket-list {
      .item {
        @include bgCenterForFoundation('coupon/coupon-pop-list-item-pc.png', 564px, 58px);
        .left{
          i.diamond-icon {
            @include bgCenterForFoundation('diamond/diamond-icon.png', 17px, 17px);
          }
        }
        &.item__active {
          @include bgCenterForFoundation('coupon/coupon-pop-list-item-active-pc.png', 564px, 58px);
        }
      }
    }
  }
}
/* 档位 */
#diamond-part-wrapper.foundation{
  .label-wrap {
    .label{
      text-decoration: none;
      @include flexCenter;
      i{
        @include bgCenterForFoundation('diamond/diamond-icon.png', 30px, 30px);
        display: inline-block;
        margin-left: 6px;
      }
    }

    .charge-construction {
      color: #EEF2D5;
      line-height: 1;
      font-size: 18px;

      i {
        @include bgCenterForFoundation('diamond/charge-construction-flag-diamond.png', 20px, 20px);
      }
    }
  }
  .diamond-list-wrapper{
    .diamond-item{
      @include bgCenterForFoundation('diamond/diamond-bg_mobile.png', 216px, 168px);
      background-color: transparent;

      .top{
        height: 121px;
        position: relative;
        .image{
          width: 216px;
          height: 93px;
          top: 4px;

          @for $i from 1 to 10 {
            &.image_#{$i - 1} {
              background-image: url(~@/assets/foundation/diamond/diamond-#{$i}.png);
              background-size: cover;
            }
          }
        }
        .coin-num {
          height: 28px;
          font-weight: bold;
          font-size: 20px;
          color: #3F342E;
          line-height: 29px;
          text-shadow: none;
          width: 100%;
          position: absolute;
          bottom: -5px;
          @include flexCenter;
          flex-direction: row-reverse;

          i{
            @include bgCenterForFoundation( 'diamond/diamond-icon.png', 23px, 23px);
            display: inline-block;
            margin: 0;
          }
        }
      }

      .bottom{
        .now{
          color: #FFFFFF;
        }
        .origin {
          color: wheat;
        }
      }

      .common-bonus{
        @extend .round-common-bonus;
      }

      &.diamond-item__active{
        &:after{
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          @include bgCenterForFoundation('diamond/choose-active_mobile.png', 216px, 168px);
        }
      }
    }
  }

  @include setPcContent{
    .label-wrap {
      .label{
        justify-content: flex-end;
        i{
          @include bgCenterForFoundation('diamond/diamond-icon.png', 23px, 23px);
          margin-left: 0;
        }
      }
    }
    .diamond-list-wrapper{
      .diamond-item{
        @include bgCenterForFoundation('diamond/diamond-bg_pc.png', 134px, 118px);
        background-color: transparent;

        .top{
          height: 81px;
          position: relative;
          flex-grow: 1;
          .image{
            width: 134px;
            height: 68px;
            top: 4px;
            @for $i from 1 to 10 {
              &.image_#{$i - 1} {
                background-image: url(~@/assets/foundation/diamond/pc/diamond-#{$i}.png);
              }
            }
          }
          .coin-num {
            height: 20px;
            font-size: 14px;
            color: #3F342E;
            bottom: 0;
            left: 0;

            i{
              @include bgCenterForFoundation( 'diamond/diamond-icon.png', 16px, 16px);
            }
          }
        }

        .bottom{
          height: 28px;
          flex-grow: 0;
          .now{
            color: #FFFFFF;
            font-size: 15px;
            line-height: 1;
          }
          .origin{
            line-height: 1;
          }
        }

        &.diamond-item__active{
          &:after{
            @include bgCenterForFoundation('diamond/choose-active_pc.png', 134px, 118px);
          }
        }
      }
    }
  }
}
/* 渠道 */
#channel-part-wrapper.foundation{
  .channel-list{
    .channel-btn {
      background: #E7EDFF;
      border-radius: 0;

      &.channel-chosen__active{
        border: none;

        &:after{
          @include bgCenterForFoundation('channel/channel-chosen-bg-m.png', 100%, 100%);
          top: 0;
          left: 0;
          z-index: 0;
        }
      }
    }
    @include setPcContent{
      .channel-btn {
        &.channel-chosen__active:after{
          @include bgCenterForFoundation('channel/channel-chosen-bg-pc.png', 100%, 100%);
        }
      }
    }
  }
}

/* 4、pc */
.add-screen-pc-wrapper.foundation{
  .add-to-main-screen__pc{
    display: flex;
    align-items: center;
    justify-content: center;
    background: #202932;
    color: #EEF2D5;

    span{
      @include foundation-btn-decoration;
      border-radius: 0;
      @include flexCenter;
      font-weight: bold;
      color: #3F342E;
      line-height: 58PX;
    }
  }
}
#page-pay-wrapper.foundation{
  .pc-content-wrapper{
    .page-title {
      span{
        background: #FFE074;
        box-shadow: 0px 3px 4px -2px rgba(0,0,0,0.4);
        border-radius: 2px;
        border: 2px solid #FFF4AB;
        box-sizing: content-box;

        i{
          @include bgCenterForFoundation('pwa/pwa-install-guide.png', 18PX, 18PX);
        }
      }
    }
    .content-center__main{
      .left-part{
        background: rgba(15,32,35, .7);
        min-height: 80vh;
        .logo {
          @include bgCenterForFoundation('pc/logo.png', 140PX, 140PX);
          margin: 39PX auto 0;
        }

        .name {
          @include bgCenterForFoundation('pc/name.png', 140px, 66px);
          margin: 20px auto 0;
        }

        .description{
          span{
            color: #EEF2D5;
          }
        }

        .charge-construction{
          color: #EEF2D5;
          i {
            @include bgCenterForFoundation('pc/charge-construction-flag.png', 19PX, 19PX);
            margin-right: 4px;
          }
        }
      }
      .right-part{
        .shop-btn{
          overflow: visible;
          text-align: left;
          padding-left: 230px;
          span{
            font-size: 24PX;
            font-weight: 600;
            width: 426px;
            text-align: center;
            @include foundation-btn-decoration;

            i{
              display: none;
            }
          }
        }
      }
    }
  }
}
/* pc结算 */
#checkout-counter-normal.foundation{
  .label{
    .total{
      color: #EEF2D5 !important;
    }
  }
  .total-price{
    .now-price{
      font-size: 29px;
      text-shadow: 0px 2px 6px rgba(0,0,0,0.5);
      line-height: 30px;
      position: relative;
      top: -3px;
    }
    .off-count-tips{
      background: #FFE074;
      color: #393A3E;
      .diamond-icon {
        @include bgCenterForFoundation('diamond/diamond-icon.png', 13px, 13px);
        position: relative;
        top: 2px;
        margin-left: 0;
      }
      &:after, &:before{
        display: none;
      }
    }
  }
}
#checkout-counter-expand.foundation{
  .price-wrapper{
    .off-count-tips{
      background: #FFE074;
      color: #393A3E;
      .diamond-icon {
        @include bgCenterForFoundation('diamond/diamond-icon.png', 13px, 13px);
        position: relative;
        top: 2px;
        margin-left: 0;
      }
    }
  }
}
/* 5、移动 */
#checkout-footer-wrapper.foundation{
  background-color: #202932;

  .common-part{
    background: #202932;

    .total-price{
      .row-1{
        .now-price{
          color: #FFE074;
        }
      }

      .row-2{
        .off-count-tips{
          background: #FFE074;
          color: #3F342E;
          .diamond-icon {
            @include bgCenterForFoundation('diamond/diamond-icon.png', 14px, 14px);
            margin-left: 0;
            display: inline-block;
            top: 1px;
          }
        }
      }
    }

    .btn{
      @include foundation-btn-decoration;
      i{
        display: none;
      }
    }
  }
  .expand-part{
    background-color: #202932;
  }
}

/* 6、其他功能 */
#add-ios-wrapper.foundation{
  .ios-guide{
    .logo{
      background-image: url("~@/assets/foundation/pwa/icon_guide_logo.png");
    }
    .phone{
      background-image: url("~@/assets/foundation/pwa/icon_guide_phone.png");
    }
  }
}
#faq-wrapper.foundation{
  .mask {
    background: linear-gradient(to bottom, rgba(195,203,225, 0), #D8DBD4);
  }
  .faq-wrap {
    .faq-list{
      color: #3F342E;
    }
  }
}
#what-is-diamond-wrapper.foundation{
  border-radius: 0;
  box-shadow: none;

  .description{
    color: #70716a !important;
  }
}
#risk-policy-wrapper.foundation{
  .desc{
    color: #3F342E;
  }
  .content{
    margin-top: 20px;
  }

  .footer-wrapper{
    .custom-btn{
      &.btn-ok{
        @include foundation-btn-decoration;
        border-radius: 0;
        font-weight: bold;
      }
    }
  }
}

/* 7、其他页面 */
#payment-callback-page.foundation{
  .image{
    &.image-status__pending {
      background-image: url('~@/assets/foundation/cb/cb-pending-image-status.png')!important;
    }
    &.image-status__fail {
      background-image: url('~@/assets/foundation/cb/cb-fail-image-status.png')!important;
    }
    &.image-status__completed {
      background-image: url('~@/assets/foundation/cb/cb-completed-image-status.png')!important;
    }
  }
  .info-wrapper{
    .title-desc__pending{
      text-align: center;
    }
    .btn-wrapper {
      .btn-status {
        border-radius: 0;
        color: #1C1E21;

        &.btn-status-not-pay {
          background: rgb(241, 244, 235);
          border: none;
        }

        &.btn-status-has-pay {
          @include foundation-btn-decoration;
          @include setPropByBp(
                  $p:(margin-left: 67px),
                  $m:(margin-left: 65px),
          );
        }
      }
    }
  }
  .btn-back{
    font-weight: bold;
    padding: 0 54px;
    @include foundation-btn-decoration;
  }

  @include setPcContent{
    .btn-back{
      padding: 0 62px;
    }
  }
}

/* sdk */
#direct-package-name.foundation{
  background: linear-gradient( 180deg, #142029 0%, #182D33 100%);
  border: 1px solid #507970;
}
#checkout-counter-sdk-b.foundation{
  .expand, .normal {
    .price-wrapper{
      .off-count-tips{
        background: #FFE074;
        color: #393A3E;
        .diamond-icon {
          @include bgCenterForFoundation('diamond/diamond-icon.png', 13px, 13px);
          position: relative;
          top: 2px;
          margin-left: 0;
        }
      }
    }
  }
  .click-btn{
    @include foundation-btn-decoration;
  }
}
