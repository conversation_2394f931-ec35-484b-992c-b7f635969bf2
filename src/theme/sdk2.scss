@import "~@/utils/utils.scss";

.common-part-wrapper.sdk{
  .label{
    color: #414141 !important;
  }
}
.pop-container-v1{
  background-color: #383838!important;

  .btn-confirm{
    background: #FF5A00!important;
    border-radius: 8px;
    color: #F1F1F1;
  }
}

.coupon-bar-wrapper.sdk{
  .coupons-wrapper{
    border: 1.5px solid #D1D1D1 !important;
    height: 78px;
    border-radius: 10px;
  }
  .coupons-wrapper__chosen, .coupons-wrapper__unavailable{
    .right{
      border-left: 1px dashed #D1D1D1;
      max-width: 250px;
    }
  }
  .coupons-wrapper__available{
    background: #FC9542;
    color: #3F342E;
    i{
      @include bgCenterForFoundation('coupon/pay-coupon-bg_mobile-available-arrow.png', 24px, 24px);
    }
  }
}
.channel-part-wrapper.sdk{
  .channel-list{
    .channel-btn{
      border: 1.5px solid #D1D1D1 !important;

      &.channel-chosen__active{
        border: 4px solid rgb(255, 94, 15) !important;
      }
    }
  }
}
#ticket-chosen-wrapper{
  .pop-main{
    background: #2F2F2F;
    padding-top: 10px;

    .divider{
      background: #D8D8D8;
      opacity: .2;
    }
    .pop-title{
      h3{
        color: #FFFFFF;
      }
      span{
        color: #FFFFFF;
      }
    }
    .nav-btn-wrapper{
      display: none;
    }

    .btn-confirm{
      height: 70px;
      margin-top: 10px;
    }
  }
}
#checkout-footer-wrapper.sdk{
  background: #404040;

  .expand-part{
    background: #404040;

    .value-wrapper{
      div{
        span:last-of-type{
          color: #FF5E0F;
        }
      }
    }
  }

  .common-part{
    background: #404040;

    .total-price{

      .row-1{
        .now-price{
          color: #FF5E0F;
        }
        .rate{
          color: #FF5E0F;

          i{
            @include bgCenterForSS('checkout/tax-arrow.png', 16px, 16px);
          }
        }
      }

      .row-2{
        display: flex;
        align-items: center;

        .off-count-tips{
          background: #FF5E0F;
          color: #FFFFFF;
          @include flexCenter;
          line-height: 1;
        }
      }
    }

    .btn{
      background: #FF5E0F;
      border-radius: 8px;
      font-weight: 500;
      color: #FFFFFF;
      text-align: center;

      i{
        display: none;
      }
    }
  }
}

.payment-callback-page{
  .info-wrapper{
    section{
      .title-desc{
        color: #000!important;
      }
    }
  }
}

/* 其他功能 */
#pop-arrears-reminder{
  box-shadow: none;
  border-color: #333333;
  .desc{
    color: white;
  }

  .btn-cancel{
    color: white;
  }
}

#app.ssdRP .diamond-icon{
  background-image: url("~@/assets/ss/ssd/diamond/diamond-icon.png") !important;
}
