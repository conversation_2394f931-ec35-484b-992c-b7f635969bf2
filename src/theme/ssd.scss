@import "~@/utils/utils.scss";
@import "standard.scss";

@mixin bgCenterForSSD($name, $w,$h) {
  @include bgCenter('ss/ssd/#{$name}', $w, $h)
}
@mixin setMultipleType($name) {
  $defaultPath: 'https://kg-web-cdn.akamaized.net/prod/versionAdmin/source/web-pay-hub';
  @supports (background-image: url("image.webp")) {
    background-image: url(#{$defaultPath}/#{$name}.webp);
  }
  @supports (background-image: url("image.avif")) {
    background-image: url(#{$defaultPath}/#{$name}.avif);
  }
}
@mixin ssdBtnDecoration {
  background: #F3B919;
  border-radius: 8px;
}
.diamond-icon{
  background-image: url("~@/assets/ss/ssd/diamond/diamond-icon.png") !important;
}

/* 1、入口 */
#app.ssd{
  background-color: rgba(20, 22, 24);
  @include setMultipleType('ssd-bg');
}

/* 2、基础组件 */
.pop-container-v1.ssd{
  .btn-confirm {
    color: #FFFFFF;
    background: #FF5E0F;
    border-radius: 8px
  }

  @include setPcContent{
    .btn-confirm {
      border-radius: 6px
    }
  }
}

/* 3、常用业务 */
/* 登录 */
#login-part-wrapper.ssd{
  @extend .standard-login;
  .login-status__login{
    .avatar{
      &[lazy=error], &[lazy=loading]{
        background-image: url("~@/assets/ss/ssd/login/home-default-image.jpeg") !important;
        background-color: transparent;
      }
    }
  }
  .login-status__not-login{
    .btn-login{
      @include ssdBtnDecoration;
      @extend .dc-stroke;
    }
  }
}
/* 优惠券 */
#coupon-bar-wrapper.ssd{
  .coupons-wrapper__not-login {
    @include bgCenterForSS('coupon/pay-coupon-bg_mobile_login.png', 672px, 68px);
    position: relative;
    font-size: 22px;
    font-weight: 400;
    color: #808080;
    padding-left: 48px;
    line-height: 68px;

    &:after {
      content: '';
      @include bgCenterForSS('coupon/pay-coupon-bg_mobile_login_tanhao.png', 23px, 23px);
      position: absolute;
      left: 13px;
      top: 50%;
      transform: translateY(-50%);
      z-index: 1;
    }
  }
  .coupons-wrapper__available {
    @include bgCenterForSS('coupon/pay-coupon-bg_mobile-available.png', 672px, 68px);
    font-size: 22px;
    font-family: SourceHanSansSC;
    font-weight: 400;
    color: #FF5A00;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-left: 20px;
    padding-right: 18px;

    i {
      @include bgCenterForSS('coupon/pay-coupon-bg_mobile-available-arrow.png', 23px, 20px);
      display: inline-block;
    }
  }
  .coupons-wrapper__unavailable,
  .coupons-wrapper__chosen {
    @include bgCenterForSS('coupon/pay-coupon-bg_mobile-chosen.png', 672px, 68px);
    padding-left: 10px;
    padding-right: 18px;
    display: flex;

    .left {
      width: 76.6%;
      display: flex;
      align-items: center;
      justify-content: flex-start;

      span {
        font-size: 18px;
        font-family: SourceHanSansSC;
        font-weight: 400;
        color: #FF5400;
        line-height: 44px;
        border: 1px solid #FF5E0F;
        border-radius: 4px;
        display: inline-block;
        padding: 0 52px;
        white-space: nowrap;

        &:after{
          display: none;
        }
      }
    }

    .right {
      flex: 1;
      padding-left: 10px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 24px;
      font-family: PingFang;
      font-weight: 400;
      color: #FF5E0F;
      flex-wrap: nowrap;
      white-space: nowrap;

      .diamond-icon{
        width: 18px;
        height: 18px;
      }

      i {
        @include bgCenterForSS('coupon/pay-coupon-bg_mobile-chosen-arrow.png', 10px, 14px);
        margin-left: 15px;
      }
    }
  }
  .coupons-wrapper__no_coupon{
    @include bgCenterForSS('coupon/pay-coupon-bg_mobile_login.png', 672px, 68px);
    font-size: 24px;
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    color: #818080;
    padding-left: 20px;
    display: flex;
    align-items: center;
  }

  @include setPcContent{
    .coupons-wrapper {
      border-radius: 4PX;
    }

    .coupons-wrapper__not-login {
      @include bgCenterForSS('coupon/pay-coupon-bg_pc_login.png', 425PX, 43PX);
      font-size: 16PX;
      font-family: SourceHanSansSC;
      padding-left: 41PX;
      line-height: 43PX;

      &:after {
        content: '';
        @include bgCenterForSS('coupon/pay-coupon-bg_mobile_login_tanhao.png', 21PX, 21PX);
        left: 12PX;
      }
    }

    .coupons-wrapper__available {
      @include bgCenterForSS('coupon/pay-coupon-bg_pc-available.png', 425PX, 43PX);
      font-size: 16PX;
      padding-left: 14PX;
      padding-right: 10PX;

      i {
        @include bgCenterForSS('coupon/pay-coupon-bg_pc-available-arrow.png', 19PX, 17PX);
      }
    }

    .coupons-wrapper__unavailable,
    .coupons-wrapper__chosen {
      @include bgCenterForSS('coupon/pay-coupon-bg_pc-chosen.png', 425PX, 43PX);
      padding-left: 10PX;
      padding-right: 7PX;

      .left {

        span {
          font-size: 12PX;
          color: #FF5400;
          line-height: 26PX;
          border: 1PX solid #FF5E0F;
          border-radius: 3PX;
          padding: 0 13PX;
        }
      }

      .right {
        padding-left: 10PX;
        font-size: 15PX;
        font-family: PingFang;

        .diamond-icon{
          width: 15px;
          height: 15px;
          position: relative;
          top: 2px;
        }

        i {
          @include bgCenterForSS('coupon/pay-coupon-bg_mobile-chosen-arrow.png', 10PX, 13PX);
          margin-left: 6PX;
        }
      }
    }
    .coupons-wrapper__no_coupon{
      @include bgCenterForSS('coupon/pay-coupon-bg_pc_login.png', 425PX, 44PX);
      padding-left: 12PX;
      font-size: 14PX;
    }
  }
}
/* 档位 */
#diamond-part-wrapper.ssd{
  .label-wrap {
    .label{
      text-decoration: none;
      @include flexCenter;
      line-height: 1;

      .diamond-icon{
        top: 0;
        margin-left: 4px;
      }
    }

    .charge-construction {
      font-size: 22px;
      text-decoration: none;
      line-height: 1;
      @include flexCenter
    }
  }
  .diamond-list-wrapper{
    .diamond-item{
      @include bgCenterForSSD('diamond/diamond-bg_mobile.png', 216px, 130px);
      background-color: transparent;

      .top{
        height: 87px;
        .image{
          width: 106px;
          height: 70px;
          top: 0;

          @for $i from 1 to 7 {
            &.image_#{$i - 1} {
              background-image: url(~@/assets/ss/ssd/diamond/diamond-#{$i}.png);
            }
          }
        }
        .coin-num {
          font-weight: bold;
          font-size: 16px;
          color: #000000;
          line-height: 1;
          text-shadow: none;
          width: 100%;
          position: absolute;
          bottom: -1px;
          @include flexCenter;
          flex-direction: row-reverse;

          i{
            @include bgCenterForSSD('diamond/diamond-icon.png', 20px, 20px);
            display: inline-block;
            margin: 0;
          }
        }
      }

      .bottom{
        @include flexCenter;
        .now{
          color: #FFFFFF;
          @extend .dc-stroke;
          line-height: 1.1;
        }
        .origin {
          color: #000000;
        }
      }

      .common-bonus{
        @extend .round-common-bonus;
        @include bgCenterForSSD('diamond/bonus.png', 80px, 80px);
        transform: scale(.75);
        left: -20px;
        top: -20px;
      }

      &.diamond-item__active{
        &:after{
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          @include bgCenterForSSD('diamond/choose-active_mobile.png', 216px, 130px);
        }
      }
    }
  }

  @include setPcContent{
    .label-wrap {
      .label{
        justify-content: flex-end;
        i{
          @include bgCenterForSSD('diamond/diamond-icon.png', 23px, 23px);
          margin-left: 0;
        }
      }
    }
    .diamond-list-wrapper{
      .diamond-item{
        @include bgCenterForSSD('diamond/diamond-bg_pc.png', 134px, 108px);
        background-color: transparent;

        .top{
          height: 0;
          position: relative;
          flex-grow: 1;
          .image{
            width: 76px;
            height: 50px;
            top: 5px;
            @for $i from 1 to 7 {
              &.image_#{$i - 1} {
                background-image: url(~@/assets/ss/ssd/diamond/pc/diamond-#{$i}.png);
              }
            }
          }
          .coin-num {
            font-size: 14px;
            bottom: 0;
            left: 0;

            i{
              @include bgCenterForSSD('diamond/diamond-icon.png', 16px, 16px);
              margin-right: 1px;
            }
          }
        }

        .bottom{
          height: 38px;
          flex-grow: 0;
          .now{
            font-size: 15px;
            line-height: 1.3;
          }
          .origin{
            line-height: 1;
          }
        }

        &.diamond-item__active{
          &:after{
            @include bgCenterForSSD('diamond/choose-active_pc.png', 134px, 108px);
          }
        }

        .common-bonus{
          left: -8px;
          top: -8px;
          transform: scale(.6);
        }
      }
    }
  }
}
/* 渠道 */
#channel-part-wrapper.ssd{
  .channel-list{
    .channel-btn{
      &.channel-chosen__active{
        border: 4px solid rgb(255, 94, 15) !important;
      }
    }
  }

  @include setPcContent{
    .channel-list{
      .channel-btn{
        &.channel-chosen__active{
          border-width: 3px !important;
        }
      }
    }

  }
}

/* 4、pc */
.add-screen-pc-wrapper.ssd{
  .add-to-main-screen__pc{
    @include flexCenter;
    background: #000000;
    color: #B0B0B0;

    span{
      @include flexCenter;
      font-weight: bold;
      color: white;
      line-height: 36PX;
      background-color: #F3B919;
      border-radius: 5px;
      @extend .dc-stroke;
    }
  }
}
#page-pay-wrapper.ssd{
  .pc-content-wrapper{
    .page-title {
      span{
        height: 26PX;
        background: #FF5E0F;
        border-radius: 5PX;
        line-height: 26PX;
        position: absolute;
        top: 0;
        margin: 0 16PX;
        transform: scale(.95);
        transform-origin: left top;
        color: #FFFFFF;

        i{
          @include bgCenterForSS('pc/mobile_yingdao.png', 14PX, 19PX)
        }
      }
    }
    .content-center__main{
      .left-part{
        background: rgba(73,73,73,0.3);
        border: 2px solid #464646;
        min-height: 80vh;
        .logo {
          @include bgCenterForSSD('pc/logo.png', 140PX, 140PX);
          margin: 39PX auto 0;
        }

        .name {
          @include bgCenterForSSD('pc/name.png', 194px, 76px);
          margin: 20px auto 0;
        }

        .description{
          span{
            color: #B0B0B0;
          }
        }

        .charge-construction{
          color: #F3B919;
          i {
            @include bgCenterForSSD('pc/charge-construction-flag.png', 19PX, 19PX);
            margin-right: 4px;
          }
        }
      }
      .right-part{
        .shop-btn{
          overflow: visible;
          text-align: left;
          padding-left: 230px;
          span{
            font-size: 24PX;
            font-weight: 600;
            width: 426px;
            text-align: center;
            background: #F3B919;
            border-radius: 8px;
            @extend .dc-stroke;

            i{
              display: none;
            }
          }
        }
      }
    }
  }
}
/* 5、移动 */
#checkout-footer-wrapper.ssd{
  background-color: #404040;

  .common-part{
    background: #404040;

    .total-price{
      .row-1{
        .now-price{
          color: #F3B919;
        }
      }

      .row-2{
        .off-count-tips{
          background: #FF5E0F;
          color: white;
        }
      }
    }

    .btn{
      border-radius: 4px;
      background: #F3B919;
      color: white;
      @extend .dc-stroke;
      i{
        display: none;
      }
    }
  }
  .expand-part{
    background-color: #404040;
    .value-wrapper span:last-of-type{
      color: #F3B919;
    }
  }
}

/* pc结算 */
#checkout-counter-normal.ssd{
  .total-price{
    .now-price{
      font-size: 29px;
      text-shadow: 0px 2px 6px rgba(0,0,0,0.5);
      line-height: 30px;
      position: relative;
      top: -3px;
      color: #F3B919;
    }
    .off-count-tips{
      background: #FF5E0F;
      color: white;
      &:after, &:before{
        display: none;
      }
    }
  }
}
#checkout-counter-expand.ssd{
  .price-wrapper{
    .off-count-tips{
      background: #FF5E0F;
      color: white;
    }
  }
  .final-price{
    color: #F3B919;
  }
}

/* 6、其他功能 */
#add-ios-wrapper.ssd{
  .ios-guide{
    .logo{
      background-image: url("~@/assets/ss/ssd/pwa/icon_guide_logo.png");
    }
    .phone{
      background-image: url("~@/assets/ss/ssd/pwa/icon_guide_phone.png");
    }
  }
}
// 其他暂无
