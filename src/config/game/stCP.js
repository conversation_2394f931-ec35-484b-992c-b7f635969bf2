import { baseKey, functionSwitch } from '@/config/game/defaultConfig'

const localKey = Object.create(baseKey)
localKey.pageTitle = 'st-official-website-title'

const localSwitch = Object.create(functionSwitch)

// localSwitch.fixedDiscountType = 'fixed_rebate'
localSwitch.useThemeFile = true
localSwitch.enableAnimation = true

const config = {
  gameinfo: {
    gameProject: 'st_global',
    gameCode: 'STCP',
    gameLogCode: 'st',
    gameId: '2202',
    blackChannel: [],
    greyChannel: []
  },
  apiParams: {},
  langKey: localKey,
  images: {},
  ids: {
    secretKey: '116 117 96 104 109 112 99 98 109 79 79 112 80 91 111 108 36 112 51 114 99 114 68 102 82 67 50 106 89 102 74 83',
  },
  switch: localSwitch
}

export default config