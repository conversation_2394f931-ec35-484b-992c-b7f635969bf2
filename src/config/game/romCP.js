import { baseKey, functionSwitch } from '@/config/game/defaultConfig'

const localKey = Object.create(baseKey)
localKey.pageTitle = 'rom-official-website-title'

const localSwitch = Object.create(functionSwitch)
localSwitch.useThemeFile = true
localSwitch.enableAnimation = false

const config = {
  gameinfo: {
    gameProject: 'koa_global',
    gameCode: 'ROMCP',
    gameLogCode: 'koa',
    gameId: '2031',
    whiteChannel: [],
    blackChannel: [],
    greyChannel: []
  },
  apiParams: {},
  langKey: localKey,
  images: {},
  ids: {
    gid: 'G-LT6WCT52Q5',
    appId: 'RbWazjW1744102094299',
    secretKey: '108 112 98 96 104 109 112 99 98 109 79 112 80 91 111 108 36 112 51 114 99 114 68 102 82 67 50 106 89 102 74 83'
  },
  switch: localSwitch
}

export default config
