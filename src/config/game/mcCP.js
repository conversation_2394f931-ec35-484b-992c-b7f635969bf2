import { baseKey, functionSwitch } from '@/config/game/defaultConfig'

const localKey = Object.create(baseKey)
localKey.pageTitle = 'mc-official-website-title'

const localSwitch = Object.create(functionSwitch)

// localSwitch.fixedDiscountType = 'fixed_discount'
localSwitch.useThemeFile = true
localSwitch.enableAnimation = true

// 开启切换返钻
localSwitch.switchRebate = false

const config = {
  gameinfo: {
    gameProject: 'mc_global',
    gameCode: 'MCCP',
    gameLogCode: 'mc',
    gameId: '2200',
    blackChannel: [],
    greyChannel: []
  },
  apiParams: {},
  langKey: localKey,
  images: {},
  ids: {
    secretKey: '110 100 96 104 109 112 99 98 109 79 79 112 80 91 111 108 36 112 51 114 99 114 68 102 82 67 50 106 89 102 74 83',
  },
  switch: localSwitch
}

export default config