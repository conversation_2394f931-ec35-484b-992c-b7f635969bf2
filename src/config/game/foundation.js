import { baseKey, functionSwitch } from '@/config/game/defaultConfig'

const localKey = Object.create(baseKey)
localKey.tokenName = 'token_name_fnd'
localKey.whatIsDiamondTitle = 'fnd-top-diamond-tips'
localKey.loginPlaceHolder = 'please-input-uid_fnd'
localKey.howToUseDiamond = 'how-to-use-diamond_fnd'
localKey.pageTitle = 'fnd-official-website-title'
const localSwitch = Object.create(functionSwitch)

localSwitch.boon = false
localSwitch.loginValidation = false
localSwitch.useThemeFile = true
localSwitch.enableAnimation = true
localSwitch.ckoCheckedByDefault = true

const config = {
  gameinfo: {
    gameProject: 'foundation_global',
    gameCode: 'FOUNDATION',
    // gameName: 'King of Avalon',
    gameId: '999999',
    defaultDiscount: false,
    whiteChannel: [],
    blackChannel: [],
    greyChannel: []
  },
  langKey: localKey,
  images: {
    whatsDiamond: [
      { imageUrl: require('@/assets/foundation/diamondGallery/01.jpeg'), jumpUrl: '' }
    ],
    uidTips: require('@/assets/foundation/login/uid-tips.jpeg')
  },
  ids: {
    gid: 'G-3FME4BZMSF',
    appId: '7HjTjCy1739172217783',
    secretKey: '103 112 118 111 101 98 117 106 112 111 96 104 109 112 99 98 109 112 51 114 99 114 68 102 82 67 50 106 89 102 74 83'
  },
  switch: localSwitch
}

export default config
