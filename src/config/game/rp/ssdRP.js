/* RP表示直购礼包 */
import { baseKey, functionSwitch } from '@/config/game/defaultConfig'

const localKey = Object.create(baseKey)
localKey.pageTitle = 'ssd-official-website-title'
localKey.tokenName = 'token_name_ssd'
const localSwitch = Object.create(functionSwitch)

localSwitch.enableAnimation = true

const config = {
  gameinfo: {
    gameProject: 'ts_global',
    gameCode: 'SSD', // 控制api
    gameId: '8612',
    whiteChannel: [],
    blackChannel: [],
    greyChannel: [],
    gameName: 'Tiles Survive',
    appGameDeepLinkIos: 'https://ts-universal-link.kingsgroupgames.com',
    appGameDeepLinkAndroid: 'com.funplus.ts.global://'
  },
  apiParams: {},
  langKey: localKey,
  images: {
    logoPath: require('@/assets/ss/ssd/pc/logo.png'),
    iconDiamond: require('@/assets/ss/ssd/ssdrp/sdk2-diamond-ssd.png')
  },
  ids: {
    gid: 'G-RWBD2X9CPK',
    appId: 'DAk5Rea1745289938511',
    secretKey: '117 116 96 104 109 112 99 98 109 79 79 112 80 91 111 108 36 112 51 114 99 114 68 102 82 67 50 106 89 102 74 83'
  },
  switch: localSwitch
}

export default config
