/* RP表示直购礼包 */
import { baseKey, functionSwitch } from '@/config/game/defaultConfig'

const localKey = Object.create(baseKey)
localKey.pageTitle = 'koa-official-website-title'
const localSwitch = Object.create(functionSwitch)

localSwitch.enableAnimation = true

const config = {
  gameinfo: {
    gameProject: 'koa_global',
    gameCode: 'KOA', // 控制api
    gameId: '2031',
    whiteChannel: [],
    blackChannel: [],
    greyChannel: [],
    gameName: 'King of Avalon',
    appGameDeepLinkIos: 'https://koa-universal-link.kingsgroupgames.com',
    appGameDeepLinkAndroid: 'com.diandian.kingofavalon://'
  },
  apiParams: {},
  langKey: localKey,
  images: {
    logoPath: 'https://kg-web-cdn.akamaized.net/online/user-platform-web/web-pay-hub/dist_online/static/1749024030/img/koa-logo.825114b1.png',
    iconDiamond: require('@/assets/koa/rp/sdk2-diamond-koa.png')
  },
  ids: {
    gid: 'G-LB15MXTLZK',
    appId: 'e3Xnhny1706589599129',
    secretKey: '108 112 98 96 104 109 112 99 98 109 79 112 80 91 111 108 36 112 51 114 99 114 68 102 82 67 50 106 89 102 74 83'
  },
  switch: localSwitch
}

export default config
