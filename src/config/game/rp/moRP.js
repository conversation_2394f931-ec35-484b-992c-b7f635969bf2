/* RP表示直购礼包 */
import { baseKey, functionSwitch } from '@/config/game/defaultConfig'

const localKey = Object.create(baseKey)
localKey.pageTitle = 'mo-official-website-title'
localKey.tokenName = 'token_name_mo'
localKey.sdk2_construction_content = 'sdk2_construction_content_mo'
const localSwitch = Object.create(functionSwitch)

localSwitch.enableAnimation = true

const config = {
  gameinfo: {
    gameProject: 'mo_global',
    gameCode: 'MO', // 控制api
    gameId: '70001',
    whiteChannel: [],
    blackChannel: [],
    greyChannel: [],
    gameName: 'Sea of Conquest',
    appGameDeepLinkIos: 'https://mo-universal-link.kingsgroupgames.com',
    appGameDeepLinkAndroid: 'com.seaofconquest.global://'
  },
  apiParams: {},
  langKey: localKey,
  images: {
    logoPath: 'https://kg-web-cdn.akamaized.net/antiaAutoOnline/user-platform-web/web-pay-unique/dist_online/static/1745563516/img/pc-main-left-logo.004959d4.png',
    iconDiamond: require('@/assets/mo/morp/sdk2-diamond-mo.png')
  },
  ids: {
    gid: 'G-XXHDPBZFJG',
    appId: 'C4GsWnM1712815808618',
    secretKey: '110 112 96 104 109 112 99 98 109 79 79 112 80 91 111 108 36 112 51 114 99 114 68 102 82 67 50 106 89 102 74 83'
  },
  switch: localSwitch
}

export default config
