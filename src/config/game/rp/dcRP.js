/* RP表示直购礼包 */
import { baseKey, functionSwitch } from '@/config/game/defaultConfig'

const localKey = Object.create(baseKey)
localKey.tokenName = 'token_name_dc'
localKey.pageTitle = 'dc-official-website-title'

const localSwitch = Object.create(functionSwitch)

localSwitch.enableAnimation = true

const config = {
  gameinfo: {
    gameProject: 'dcdl_global',
    gameCode: 'DC',
    gameId: '888888',
    whiteChannel: [],
    blackChannel: [],
    greyChannel: [],
    gameName: 'DC: Dark Legion',
    appGameDeepLinkIos: 'https://dc-global-universal-link.kingsgroupgames.com',
    appGameDeepLinkAndroid: 'com.funplus.ts.global://'
  },
  apiParams: {},
  langKey: localKey,
  images: {
    logoPath: 'https://kg-web-cdn.akamaized.net/online/user-platform-web/web-pay-hub/dist_online/static/1743436800/img/logo.d522842e.png',
    iconDiamond: require('@/assets/dc/rp/sdk2-diamond-dc.png')
  },
  ids: {
    gid: 'G-3FME4BZMSF',
    appId: '7HjTjCy1739172217783',
    secretKey: '101 100 101 109 96 104 109 112 99 98 109 112 80 91 111 108 36 112 51 114 99 114 68 102 82 67 50 106 89 102 74 83'
  },
  switch: localSwitch
}

export default config
