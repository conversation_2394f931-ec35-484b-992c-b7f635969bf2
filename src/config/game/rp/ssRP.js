/* RP表示直购礼包 */
import { baseKey, functionSwitch } from '@/config/game/defaultConfig'

const localKey = Object.create(baseKey)
localKey.pageTitle = 'ss-official-website-title'
const localSwitch = Object.create(functionSwitch)

localSwitch.enableAnimation = true

const config = {
  gameinfo: {
    gameProject: 'ss_global',
    gameCode: 'SSRP', // 控制api
    gameId: '30001',
    whiteChannel: [],
    blackChannel: [],
    greyChannel: [],
    gameName: 'State of Survivals',
    appGameDeepLinkIos: 'https://sos-universal-link.kingsgroupgames.com',
    appGameDeepLinkAndroid: 'com.kingsgroup.sos://'
  },
  apiParams: {},
  langKey: localKey,
  images: {
    logoPath: require('@/assets/ss/pc/ss-logo.png'),
    iconDiamond: require('@/assets/ss/ssrp/sdk2-diamond-ss.png')
  },
  ids: {
    gid: 'G-RWBD2X9CPK',
    appId: 'DAk5Rea1745289938511',
    secretKey: '116 116 96 104 109 112 99 98 109 79 79 112 80 91 111 108 36 112 51 114 99 114 68 102 82 67 50 106 89 102 74 83'
  },
  switch: localSwitch
}

export default config
