/* RP表示直购礼包 */
import { baseKey, functionSwitch } from '@/config/game/defaultConfig'

const localKey = Object.create(baseKey)
localKey.pageTitle = 'st-official-website-title'
const localSwitch = Object.create(functionSwitch)

localSwitch.enableAnimation = true

const config = {
  gameinfo: {
    gameProject: 'st_global',
    gameCode: 'ST', // 控制api
    gameId: '2202',
    whiteChannel: [],
    blackChannel: [],
    greyChannel: [],
    gameName: 'Storm Shot',
    appGameDeepLinkIos: 'https://st-universal-link.kingsgroupgames.com',
    appGameDeepLinkAndroid: 'com.sivona.stormshot.e://'
  },
  apiParams: {},
  langKey: localKey,
  images: {
    logoPath: 'https://kg-web-cdn.akamaized.net/prod/web-pay-unique_gog/dist_st_online/static/img/st-logo.41d54e47.png',
    iconDiamond: require('@/assets/st/strp/sdk2-diamond-st.png')
  },
  ids: {
    gid: 'G-K8VLKTWD8Q',
    // appId: 'DAk5Rea1745289938511',
    secretKey: '116 117 96 104 109 112 99 98 109 79 79 112 80 91 111 108 36 112 51 114 99 114 68 102 82 67 50 106 89 102 74 83'
  },
  switch: localSwitch
}

export default config
