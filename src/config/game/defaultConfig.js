/* 1、多语言Key */
const baseKey = {
  /* 钻石轮播弹窗 */
  howToUseDiamond: 'how-to-use-diamond', // 标题
  whatIsDiamondTitle: '$gameName-top-diamond-tips', // 描述
  /* 登录页 */
  loginPlaceHolder: 'please-input-uid_$gameName',
  /* 主页  */
  pageTitle: '$gameName-official-website-title',
  discount95Tips: 'discount95-change-tips',
  tokenName: 'token_name',
  customDiamondLimitTips: 'custom-diamond-limit-$gameName',
  sdk2_construction_title: 'sdk2_construction_title',
  sdk2_construction_content: 'sdk2_construction_content'
}
for (const [key, value] of Object.entries(baseKey)) {
  baseKey[key] = value.replace('$gameName', window.__GAMENAME)
}

const functionSwitch = {
  loginValidation: true,
  showMobilePolicy: true,
  showPopPolicy: false,
  boon: true,
  fixedDiscountType: '',
  smallDiamondDoubleDiscount: false,
  ckoCheckedByDefault: true, // 默认勾选cko记住卡号
  showPcDiscountTips: false,
  useThemeFile: false,
  enableAnimation: false
}
export {
  baseKey,
  functionSwitch
}
