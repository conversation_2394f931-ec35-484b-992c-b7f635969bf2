import { baseKey, functionSwitch } from '@/config/game/defaultConfig'

const localKey = Object.create(baseKey)
localKey.tokenName = 'token_name'
localKey.loginPlaceHolder = 'please-input-uid'
localKey.pageTitle = 'ssv2-official-website-title'
localKey.customDiamondLimitTips = 'custom-diamond-limit-ss'

const localSwitch = Object.create(functionSwitch)

localSwitch.loginValidation = true
localSwitch.smallDiamondDoubleDiscount = true // 开启小档位
localSwitch.showPopPolicy = true
localSwitch.showMobilePolicy = false
localSwitch.fixedDiscountType = 'fixed_rebate'
localSwitch.showPcDiscountTips = true
localSwitch.ckoCheckedByDefault = true

const config = {
  gameinfo: {
    gameProject: 'ssv2_global',
    gameCode: 'SSV2', // 控制api
    gameId: '8519',
    whiteChannel: ['ss_googleplay_material_parkour21'],
    blackChannel: [],
    greyChannel: [],
    mainBody: 'puzala', // 所属主体 funplus/puzala 默认是funplus
    sortName: 'Front War'
  },
  apiParams: {
    boonAme: {
      projectId: 158,
      loginAction: 2646,
      getLoginReward: 2647,
      pwaOpenAction: 2648,
      getPwaReward: 2649
    },
    smallDiamondDiscount: {
      p1: 158,
      p2: 2654,
    },
    biPath: '/sdk/bilog'
  },
  langKey: localKey,
  images: {
    whatsDiamond: [
      { imageUrl: require('@/assets/ss/diamondGallery/ssv-01.jpeg'), jumpUrl: '' }
    ],
    uidTips: require('@/assets/ss/ssv2/find_ss_uid.jpeg'),
    loganFindValidationCode: require('@/assets/ss/ssv2/login/login-validate-game-scrrencut.png')
  },
  ids: {
    gid: 'G-RWBD2X9CPK',
    appId: 'DAk5Rea1745289938511',
    secretKey: '116 116 119 51 96 104 109 112 99 98 109 112 80 91 111 108 36 112 51 114 99 114 68 102 82 67 50 106 89 102 74 83', //ssv2
    minCustomDiamondNum: 10,
    boonPopName: 'SSVBoonPop',
    loginValidationExpireCount: 15,
    minimumDiamondId: 'com.pc.miamond0' // 如果smallDiamondDoubleDiscount生效，最小购买钻石没有固定返钻
  },
  switch: localSwitch
}

export default config
