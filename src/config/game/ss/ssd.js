import { baseKey, functionSwitch } from '@/config/game/defaultConfig'

const localKey = Object.create(baseKey)
localKey.tokenName = 'token_name_ssd'
localKey.loginPlaceHolder = 'please-input-uid_ssd'
localKey.pageTitle = 'ssd-official-website-title'
localKey.howToUseDiamond = 'how-to-use-diamond-ssd'

const localSwitch = Object.create(functionSwitch)

localSwitch.loginValidation = true
localSwitch.showPopPolicy = true
localSwitch.showMobilePolicy = true
localSwitch.enableAnimation = true
localSwitch.useThemeFile = true

const config = {
  gameinfo: {
    gameProject: 'ts_global',
    gameCode: 'SSD', // 控制api
    gameId: '8612',
    whiteChannel: ['ts_googleplay_global', 'ts_ios_global'],
    blackChannel: [],
    greyChannel: [],
    sortName: 'TS'
  },
  apiParams: {
    boonAme: {
      projectId: 168,
      loginAction: 2743,
      getLoginReward: 2744,
      pwaOpenAction: 2745,
      getPwaReward: 2746
    }
  },
  langKey: localKey,
  images: {
    whatsDiamond: [
      { imageUrl: require('@/assets/ss/ssd/diamondGallery/ssd-01.jpeg'), jumpUrl: '' }
    ],
    uidTips: require('@/assets/ss/ssd/find_ss_uid.jpeg'),
    loganFindValidationCode: require('@/assets/ss/ssd/login/login-validate-game-scrrencut.jpeg')
  },
  ids: {
    gid: 'G-RWBD2X9CPK',
    appId: 'DAk5Rea1745289938511',
    secretKey: '117 116 96 104 109 112 99 98 109 79 79 112 80 91 111 108 36 112 51 114 99 114 68 102 82 67 50 106 89 102 74 83',
    boonPopName: 'SSVBoonPop',
    loginValidationExpireCount: 15
  },
  switch: localSwitch
}

export default config
