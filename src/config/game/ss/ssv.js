import { baseKey, functionSwitch } from '@/config/game/defaultConfig'

const localKey = Object.create(baseKey)
localKey.tokenName = 'token_name'
localKey.loginPlaceHolder = 'please-input-uid'
localKey.pageTitle = 'ss-official-website-title'
localKey.whatIsDiamondTitle = 'top-diamond-tips'
localKey.customDiamondLimitTips = 'custom-diamond-limit-ss'

const localSwitch = Object.create(functionSwitch)

// localSwitch.boon = false
localSwitch.loginValidation = true
localSwitch.smallDiamondDoubleDiscount = true
localSwitch.fixedDiscountType = 'fixed_rebate'

const config = {
  gameinfo: {
    gameProject: 'ss_global',
    gameCode: 'SSV', // 控制api
    // gameName: 'King of Avalon',
    gameId: '30001',
    // whiteChannel: ['ss_funplus_pc_vshoot'],
    blackChannel: [],
    greyChannel: []
  },
  apiParams: {
    boonAme: {
      projectId: 21,
      loginAction: 1114,
      getLoginReward: 1115,
      pwaOpenAction: 1116,
      getPwaReward: 1117
    }
  },
  langKey: localKey,
  images: {
    whatsDiamond: [
      { imageUrl: require('@/assets/ss/diamondGallery/01.png'), jumpUrl: '' },
      { imageUrl: require('@/assets/ss/diamondGallery/02.png'), jumpUrl: '' },
      { imageUrl: require('@/assets/ss/diamondGallery/03.png'), jumpUrl: '' }
    ],
    uidTips: require('@/assets/ss/login/ss_uid.png'),
    loganFindValidationCode: require('@/assets/ss/login/login-validate-game-scrrencut.jpeg')
  },
  ids: {
    // gid: 'G-LB15MXTLZK',
    // appId: 'e3Xnhny1706589599129',
    secretKey: '116 116 96 104 109 112 99 98 109 79 79 112 80 91 111 108 36 112 51 114 99 114 68 102 82 67 50 106 89 102 74 83',
    minCustomDiamondNum: 10,
    boonPopName: 'SSVBoonPop'
  },
  switch: localSwitch
}

export default config
