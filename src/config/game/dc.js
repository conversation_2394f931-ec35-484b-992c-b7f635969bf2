import { baseKey, functionSwitch } from '@/config/game/defaultConfig'

const localKey = Object.create(baseKey)
localKey.tokenName = 'token_name_dc'
localKey.howToUseDiamond = 'how-to-use-diamond_dc'
const localSwitch = Object.create(functionSwitch)

localSwitch.boon = false
localSwitch.ckoCheckedByDefault = true

const config = {
  gameinfo: {
    gameProject: 'dcdl_global',
    gameCode: 'DC',
    gameName: 'King of Avalon',
    gameId: '888888',
    defaultDiscount: false,
    whiteChannel: [],
    blackChannel: [],
    greyChannel: []
  },
  langKey: localKey,
  images: {
    whatsDiamond: [
      { imageUrl: require('@/assets/dc/diamondGallery/01.png'), jumpUrl: '' }
    ],
    uidTips: require('@/assets/dc/login/uid-tips.jpeg'),
    loganFindValidationCode: require('@/assets/dc/login/login-validate-game-scrrencut.jpeg')
  },
  ids: {
    gid: 'G-3FME4BZMSF',
    appId: '7HjTjCy1739172217783',
    secretKey: '101 100 101 109 96 104 109 112 99 98 109 112 80 91 111 108 36 112 51 114 99 114 68 102 82 67 50 106 89 102 74 83',
    loginValidationExpireCount: 15
  },
  switch: localSwitch
}

export default config
