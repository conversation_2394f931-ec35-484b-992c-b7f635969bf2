import { baseKey, functionSwitch } from '@/config/game/defaultConfig'

const localKey = Object.create(baseKey)
localKey.customDiamondLimitTips = 'custom-diamond-limit-koa'
localKey.loginPlaceHolder = 'please-input-uid_koa'
const localSwitch = Object.create(functionSwitch)

localSwitch.showMobilePolicy = false
localSwitch.loginValidation = false
localSwitch.ckoCheckedByDefault = true

const config = {
  gameinfo: {
    gameProject: 'koa_global',
    gameCode: 'KOA',
    gameName: 'King of Avalon',
    appId: 'koa.global.prod',
    gameId: '2031',
    defaultDiscount: false,
    whiteChannel: [],
    blackChannel: [],
    greyChannel: [
      { channel: ['ios_cn', 'android_cn'], to: process.env.VUE_APP_CN_ADDRESS_KOA }
    ]
  },
  apiParams: {
    boonAme: {
      projectId: 15,
      loginAction: 1097,
      getLoginReward: 1098,
      pwaOpenAction: 1099,
      getPwaReward: 1100
    }
  },
  langKey: localKey,
  images: {
    whatsDiamond: [
      { imageUrl: require('../../assets/koa/aof/diamondGallery/01.jpg'), jumpUrl: '' },
      { imageUrl: require('../../assets/koa/aof/diamondGallery/02.jpg'), jumpUrl: '' },
      { imageUrl: require('../../assets/koa/aof/diamondGallery/03.jpeg'), jumpUrl: '' },
    ],
    uidTips: require('@/assets/koa/login/sample_koa.png'),
    loganFindValidationCode: require('@/assets/koa/login/login-validate-game-scrrencut.jpeg')
  },
  ids: {
    gid: 'G-LT6WCT52Q5',
    appId: 'RbWazjW1744102094299',
    secretKey: '108 112 98 96 104 109 112 99 98 109 79 112 80 91 111 108 36 112 51 114 99 114 68 102 82 67 50 106 89 102 74 83'
  },
  switch: localSwitch
}

export default config
