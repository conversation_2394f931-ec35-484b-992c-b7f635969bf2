import koa from './game/koa'
import aof from './game/aof'
import rom from './game/rom'
import koaCn from './game/koaCn'
import dc from './game/dc'
import ssv from './game/ss/ssv'
import ssv2 from './game/ss/ssv2'
import ssd from './game/ss/ssd'
import foundation from './game/foundation'

import ssCP from './game/ss/ssCP'
import stCP from './game/stCP'
import mcCP from './game/mcCP'
import gogCP from './game/gogCP'
import romCP from './game/romCP'

import rp from './game/rp/index'

const sourceFun = {
  koa,
  aof,
  rom,
  koaCn,
  dc,
  ssv,
  ssv2,
  foundation,
  ssCP,
  stCP,
  mcCP,
  gogCP,
  romCP,
  ssd,
  ...rp
}
const currentGame = window.__GAMENAME

function loadResource () {
  const content = sourceFun[currentGame]
  if (!content) console.error(`${currentGame} 资源配置文件未注册`)
  return function (key, defaultValue) {
    if (key.includes('.')) {
      const [key1, key2] = key.split('.')
      return (content[key1] && content[key1][key2]) || defaultValue
    }
    return content[key] || defaultValue || {}
  }
}
export default loadResource()
