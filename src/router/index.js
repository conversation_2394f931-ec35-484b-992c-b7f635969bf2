import Vue from 'vue'
import VueRouter from 'vue-router'
import store from '@/store'

Vue.use(VueRouter)

const routes = [
  {
    path: '/',
    name: 'Pay',
    component: () => import(/* webpackChunkName: "pagePay" */ '../views/Pay.vue')
  },
  {
    path: '/pay',
    name: 'Pay',
    component: () => import(/* webpackChunkName: "pagePay" */ '../views/Pay.vue')
  },
  {
    path: '/ad',
    name: 'Adyen',
    component: () => import(/* webpackChunkName: "pageAdyen" */ '../views/paymethod/Adyen')
  },
  {
    path: '/aw',
    name: 'Airwallex',
    component: () => import(/* webpackChunkName: "pageSmall" */ '../views/paymethod/airwallex')
  },
  {
    path: '/pp',
    name: 'pingpong',
    component: () => import(/* webpackChunkName: "pageSmall" */ '../views/paymethod/pingpong')
  },
  {
    path: '/cko',
    name: 'checkout',
    component: () => import(/* webpackChunkName: "pageSmall" */ '../views/paymethod/checkout')
  },
  {
    path: '/pm',
    name: 'payermax',
    component: () => import(/* webpackChunkName: "pageSmall" */ '../views/paymethod/payermax')
  },
  {
    path: '/sp',
    name: 'stripe',
    component: () => import(/* webpackChunkName: "pageSmall" */ '../views/paymethod/stripe.vue')
  },
  {
    path: '/fail',
    name: 'CallbackFail',
    component: () => import(/* webpackChunkName: "pageSmall" */ '../views/PaymentCallback.vue')
  },
  {
    path: '/completed',
    name: 'CallbackCompleted',
    component: () => import(/* webpackChunkName: "pageSmall" */ '../views/PaymentCallback.vue')
  },
  {
    path: '/pending',
    name: 'CallbackPending',
    component: () => import(/* webpackChunkName: "pageSmall" */ '../views/PaymentCallback.vue')
  },
  // {
  //   path: '/boon',
  //   name: 'Boon',
  //   component: () => import(/* webpackChunkName: "Boon" */ '../views/Boon.vue')
  // },
  {
    path: '/common/fail',
    name: 'CallbackFail',
    component: () => import(/* webpackChunkName: "pageSmall" */ '../views/PaymentCallbackCommon.vue')
  },
  {
    path: '/common/completed',
    name: 'CallbackCompleted',
    component: () => import(/* webpackChunkName: "pageSmall" */ '../views/PaymentCallbackCommon.vue')
  },
  {
    path: '/common/pending',
    name: 'CallbackPending',
    component: () => import(/* webpackChunkName: "pageSmall" */ '../views/PaymentCallbackCommon.vue')
  },
  {
    path: '/order',
    name: 'OrderPage',
    component: () => import(/* webpackChunkName: "pageSmall" */ '../views/OrderPage.vue')
  },
  {
    path: '*',
    redirect: '/'
  }
]

store.commit('gameinfo/setGameInfo')
store.commit('functionSwitch/setFunctionInfo')

if (store.state.IS_CHECKOUT_SDK_V2) {
  routes.splice(0, 2, {
    path: '/',
    name: 'Pay',
    component: () => import(/* webpackChunkName: "pageSdkV2" */ '../views/PaySdk2.vue')
  })
}
const router = new VueRouter({
  mode: 'history',
  base: window.__ROUTERPATH || '/',
  routes
})

export default router
