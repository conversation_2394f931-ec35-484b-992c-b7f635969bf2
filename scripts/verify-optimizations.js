#!/usr/bin/env node

/**
 * 优化效果验证脚本
 * 快速检查优化是否正确应用
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

class OptimizationVerifier {
  constructor() {
    this.results = {
      passed: 0,
      failed: 0,
      warnings: 0,
      details: []
    }
  }

  log(type, message, details = '') {
    const icons = {
      pass: '✅',
      fail: '❌',
      warn: '⚠️',
      info: 'ℹ️'
    }
    
    console.log(`${icons[type]} ${message}`)
    if (details) {
      console.log(`   ${details}`)
    }
    
    this.results.details.push({ type, message, details })
    
    if (type === 'pass') this.results.passed++
    else if (type === 'fail') this.results.failed++
    else if (type === 'warn') this.results.warnings++
  }

  async verify() {
    console.log('🔍 开始验证性能优化配置...\n')

    // 检查配置文件
    this.checkConfigFiles()
    
    // 检查依赖包
    this.checkDependencies()
    
    // 检查优化文件
    this.checkOptimizationFiles()
    
    // 检查构建配置
    this.checkBuildConfig()
    
    // 运行构建测试
    await this.testBuild()
    
    // 检查包大小
    this.checkBundleSize()
    
    // 生成报告
    this.generateReport()
  }

  checkConfigFiles() {
    console.log('📁 检查配置文件...')
    
    // 检查vue.config.js
    if (fs.existsSync('vue.config.js')) {
      const config = fs.readFileSync('vue.config.js', 'utf8')
      
      if (config.includes('CompressionPlugin')) {
        this.log('pass', 'Vue配置包含Gzip压缩')
      } else {
        this.log('fail', 'Vue配置缺少Gzip压缩')
      }
      
      if (config.includes('splitChunks')) {
        this.log('pass', 'Vue配置包含代码分割')
      } else {
        this.log('fail', 'Vue配置缺少代码分割')
      }
      
      if (config.includes('paymentSDK')) {
        this.log('pass', 'Vue配置包含支付SDK分离')
      } else {
        this.log('warn', 'Vue配置可能缺少支付SDK分离')
      }
    } else {
      this.log('fail', 'vue.config.js 文件不存在')
    }
    
    // 检查package.json
    if (fs.existsSync('package.json')) {
      const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'))
      
      // 检查关键依赖版本
      const dependencies = pkg.dependencies || {}
      const devDependencies = pkg.devDependencies || {}
      
      if (dependencies.axios && dependencies.axios.includes('1.6')) {
        this.log('pass', 'Axios已升级到安全版本')
      } else {
        this.log('fail', 'Axios版本过旧，存在安全风险')
      }
      
      if (dependencies.vue && dependencies.vue.includes('2.7')) {
        this.log('pass', 'Vue已升级到2.7版本')
      } else {
        this.log('warn', 'Vue版本可能需要升级')
      }
      
      if (devDependencies['compression-webpack-plugin']) {
        this.log('pass', '已安装Gzip压缩插件')
      } else {
        this.log('fail', '缺少Gzip压缩插件')
      }
      
      if (devDependencies['image-webpack-loader']) {
        this.log('pass', '已安装图片优化插件')
      } else {
        this.log('warn', '建议安装图片优化插件')
      }
    } else {
      this.log('fail', 'package.json 文件不存在')
    }
    
    console.log('')
  }

  checkDependencies() {
    console.log('📦 检查依赖包...')
    
    try {
      // 检查是否有安全漏洞
      const auditResult = execSync('npm audit --audit-level=high --json', { 
        encoding: 'utf8',
        stdio: 'pipe'
      })
      
      const audit = JSON.parse(auditResult)
      
      if (audit.metadata.vulnerabilities.high === 0 && audit.metadata.vulnerabilities.critical === 0) {
        this.log('pass', '没有发现高危安全漏洞')
      } else {
        this.log('fail', `发现 ${audit.metadata.vulnerabilities.high} 个高危和 ${audit.metadata.vulnerabilities.critical} 个严重安全漏洞`)
      }
    } catch (error) {
      this.log('warn', '无法运行安全审计', error.message)
    }
    
    // 检查node_modules大小
    try {
      const stats = execSync('du -sh node_modules 2>/dev/null || echo "0B node_modules"', { encoding: 'utf8' })
      const size = stats.trim().split('\t')[0]
      this.log('info', `node_modules 大小: ${size}`)
    } catch (error) {
      this.log('warn', '无法获取node_modules大小')
    }
    
    console.log('')
  }

  checkOptimizationFiles() {
    console.log('🛠️ 检查优化文件...')
    
    const optimizationFiles = [
      'src/utils/performance-monitor.js',
      'src/utils/api-cache.js',
      'src/server/http.optimized.js',
      'config/service-worker.optimized.js',
      'scripts/performance-test.js'
    ]
    
    optimizationFiles.forEach(file => {
      if (fs.existsSync(file)) {
        this.log('pass', `${file} 存在`)
      } else {
        this.log('warn', `${file} 不存在`)
      }
    })
    
    // 检查性能监控是否集成
    if (fs.existsSync('src/main.js')) {
      const mainJs = fs.readFileSync('src/main.js', 'utf8')
      if (mainJs.includes('performance-monitor')) {
        this.log('pass', '性能监控已集成到main.js')
      } else {
        this.log('warn', '性能监控未集成到main.js')
      }
    }
    
    console.log('')
  }

  checkBuildConfig() {
    console.log('🏗️ 检查构建配置...')
    
    if (fs.existsSync('vue.config.js')) {
      const config = fs.readFileSync('vue.config.js', 'utf8')
      
      // 检查生产环境优化
      if (config.includes('productionSourceMap: false')) {
        this.log('pass', '生产环境已禁用source map')
      } else {
        this.log('warn', '建议在生产环境禁用source map')
      }
      
      // 检查性能预算
      if (config.includes('performance')) {
        this.log('pass', '已配置性能预算')
      } else {
        this.log('warn', '建议配置性能预算')
      }
      
      // 检查缓存组配置
      if (config.includes('cacheGroups')) {
        this.log('pass', '已配置缓存组')
      } else {
        this.log('fail', '缺少缓存组配置')
      }
    }
    
    console.log('')
  }

  async testBuild() {
    console.log('🧪 测试构建...')
    
    try {
      console.log('   正在运行构建测试...')
      execSync('npm run build:release', { 
        stdio: 'pipe',
        timeout: 120000 // 2分钟超时
      })
      this.log('pass', '构建测试成功')
    } catch (error) {
      this.log('fail', '构建测试失败', error.message)
      return
    }
    
    console.log('')
  }

  checkBundleSize() {
    console.log('📊 检查包大小...')
    
    const distDir = 'dist_release'
    
    if (!fs.existsSync(distDir)) {
      this.log('warn', '构建目录不存在，跳过包大小检查')
      return
    }
    
    try {
      // 检查JS文件大小
      const jsFiles = this.getFilesWithExtension(distDir, '.js')
      let totalJSSize = 0
      
      jsFiles.forEach(file => {
        const stats = fs.statSync(file)
        totalJSSize += stats.size
      })
      
      const totalJSSizeMB = (totalJSSize / 1024 / 1024).toFixed(2)
      
      if (totalJSSize < 500 * 1024) { // 500KB
        this.log('pass', `JavaScript总大小: ${totalJSSizeMB}MB (目标: <0.5MB)`)
      } else if (totalJSSize < 1024 * 1024) { // 1MB
        this.log('warn', `JavaScript总大小: ${totalJSSizeMB}MB (建议: <0.5MB)`)
      } else {
        this.log('fail', `JavaScript总大小: ${totalJSSizeMB}MB (过大，建议: <0.5MB)`)
      }
      
      // 检查CSS文件大小
      const cssFiles = this.getFilesWithExtension(distDir, '.css')
      let totalCSSSize = 0
      
      cssFiles.forEach(file => {
        const stats = fs.statSync(file)
        totalCSSSize += stats.size
      })
      
      const totalCSSSizeKB = (totalCSSSize / 1024).toFixed(2)
      this.log('info', `CSS总大小: ${totalCSSSizeKB}KB`)
      
      // 检查是否有Gzip文件
      const gzipFiles = this.getFilesWithExtension(distDir, '.gz')
      if (gzipFiles.length > 0) {
        this.log('pass', `发现 ${gzipFiles.length} 个Gzip压缩文件`)
      } else {
        this.log('warn', '没有发现Gzip压缩文件')
      }
      
    } catch (error) {
      this.log('warn', '无法分析包大小', error.message)
    }
    
    console.log('')
  }

  getFilesWithExtension(dir, ext) {
    const files = []
    
    function traverse(currentDir) {
      const items = fs.readdirSync(currentDir)
      
      items.forEach(item => {
        const fullPath = path.join(currentDir, item)
        const stats = fs.statSync(fullPath)
        
        if (stats.isDirectory()) {
          traverse(fullPath)
        } else if (item.endsWith(ext)) {
          files.push(fullPath)
        }
      })
    }
    
    traverse(dir)
    return files
  }

  generateReport() {
    console.log('📋 生成验证报告...\n')
    
    const total = this.results.passed + this.results.failed + this.results.warnings
    const passRate = ((this.results.passed / total) * 100).toFixed(1)
    
    console.log('='.repeat(50))
    console.log('📊 验证结果汇总')
    console.log('='.repeat(50))
    console.log(`✅ 通过: ${this.results.passed}`)
    console.log(`❌ 失败: ${this.results.failed}`)
    console.log(`⚠️  警告: ${this.results.warnings}`)
    console.log(`📈 通过率: ${passRate}%`)
    console.log('='.repeat(50))
    
    if (this.results.failed === 0) {
      console.log('🎉 所有关键检查都已通过！')
      console.log('🚀 可以继续进行性能测试和部署')
    } else {
      console.log('⚠️  发现一些问题需要修复')
      console.log('🔧 请检查失败的项目并重新配置')
    }
    
    // 保存详细报告
    const reportPath = 'verification-report.json'
    fs.writeFileSync(reportPath, JSON.stringify({
      timestamp: new Date().toISOString(),
      summary: {
        passed: this.results.passed,
        failed: this.results.failed,
        warnings: this.results.warnings,
        passRate: parseFloat(passRate)
      },
      details: this.results.details
    }, null, 2))
    
    console.log(`\n📄 详细报告已保存到: ${reportPath}`)
  }
}

// 运行验证
if (require.main === module) {
  const verifier = new OptimizationVerifier()
  verifier.verify().catch(console.error)
}

module.exports = OptimizationVerifier
