#!/bin/bash

# 性能优化应用脚本
# 自动应用第一阶段的性能优化配置

set -e

echo "🚀 开始应用性能优化配置..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查是否在项目根目录
if [ ! -f "package.json" ]; then
    echo -e "${RED}❌ 错误: 请在项目根目录运行此脚本${NC}"
    exit 1
fi

# 检查是否存在优化文件
if [ ! -f "vue.config.optimized.js" ]; then
    echo -e "${RED}❌ 错误: 找不到 vue.config.optimized.js 文件${NC}"
    exit 1
fi

if [ ! -f "package.optimized.json" ]; then
    echo -e "${RED}❌ 错误: 找不到 package.optimized.json 文件${NC}"
    exit 1
fi

# 创建备份目录
BACKUP_DIR="backup-$(date +%Y%m%d-%H%M%S)"
mkdir -p "$BACKUP_DIR"

echo -e "${BLUE}📦 创建备份到 $BACKUP_DIR...${NC}"

# 备份现有文件
if [ -f "vue.config.js" ]; then
    cp vue.config.js "$BACKUP_DIR/"
    echo "  ✓ 备份 vue.config.js"
fi

if [ -f "package.json" ]; then
    cp package.json "$BACKUP_DIR/"
    echo "  ✓ 备份 package.json"
fi

if [ -f "src/server/http.js" ]; then
    cp src/server/http.js "$BACKUP_DIR/"
    echo "  ✓ 备份 src/server/http.js"
fi

if [ -f "config/service-worker.js" ]; then
    cp config/service-worker.js "$BACKUP_DIR/"
    echo "  ✓ 备份 config/service-worker.js"
fi

echo -e "${GREEN}✅ 备份完成${NC}"

# 应用优化配置
echo -e "${BLUE}🔧 应用优化配置...${NC}"

# 替换配置文件
cp vue.config.optimized.js vue.config.js
echo "  ✓ 应用优化的 vue.config.js"

cp package.optimized.json package.json
echo "  ✓ 应用优化的 package.json"

if [ -f "src/server/http.optimized.js" ]; then
    cp src/server/http.optimized.js src/server/http.js
    echo "  ✓ 应用优化的 HTTP 客户端"
fi

if [ -f "config/service-worker.optimized.js" ]; then
    cp config/service-worker.optimized.js config/service-worker.js
    echo "  ✓ 应用优化的 Service Worker"
fi

echo -e "${GREEN}✅ 配置文件应用完成${NC}"

# 检查Node.js版本
echo -e "${BLUE}🔍 检查环境...${NC}"

NODE_VERSION=$(node -v | cut -d'v' -f2)
REQUIRED_NODE="16.0.0"

if [ "$(printf '%s\n' "$REQUIRED_NODE" "$NODE_VERSION" | sort -V | head -n1)" != "$REQUIRED_NODE" ]; then
    echo -e "${YELLOW}⚠️  警告: Node.js版本 $NODE_VERSION 可能不兼容，建议使用 >= $REQUIRED_NODE${NC}"
else
    echo "  ✓ Node.js版本检查通过: $NODE_VERSION"
fi

# 检查npm版本
NPM_VERSION=$(npm -v)
REQUIRED_NPM="8.0.0"

if [ "$(printf '%s\n' "$REQUIRED_NPM" "$NPM_VERSION" | sort -V | head -n1)" != "$REQUIRED_NPM" ]; then
    echo -e "${YELLOW}⚠️  警告: npm版本 $NPM_VERSION 可能不兼容，建议使用 >= $REQUIRED_NPM${NC}"
else
    echo "  ✓ npm版本检查通过: $NPM_VERSION"
fi

# 清理旧的node_modules和lock文件
echo -e "${BLUE}🧹 清理旧依赖...${NC}"

if [ -d "node_modules" ]; then
    rm -rf node_modules
    echo "  ✓ 删除 node_modules"
fi

if [ -f "package-lock.json" ]; then
    rm package-lock.json
    echo "  ✓ 删除 package-lock.json"
fi

if [ -f "yarn.lock" ]; then
    rm yarn.lock
    echo "  ✓ 删除 yarn.lock"
fi

# 安装依赖
echo -e "${BLUE}📦 安装优化后的依赖...${NC}"

npm install

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ 依赖安装成功${NC}"
else
    echo -e "${RED}❌ 依赖安装失败${NC}"
    echo -e "${YELLOW}正在恢复备份...${NC}"
    
    # 恢复备份
    if [ -f "$BACKUP_DIR/vue.config.js" ]; then
        cp "$BACKUP_DIR/vue.config.js" vue.config.js
    fi
    if [ -f "$BACKUP_DIR/package.json" ]; then
        cp "$BACKUP_DIR/package.json" package.json
    fi
    
    exit 1
fi

# 安装性能测试依赖
echo -e "${BLUE}🧪 安装性能测试依赖...${NC}"

npm install -D lighthouse chrome-launcher

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ 性能测试依赖安装成功${NC}"
else
    echo -e "${YELLOW}⚠️  性能测试依赖安装失败，但不影响主要功能${NC}"
fi

# 创建必要的目录
echo -e "${BLUE}📁 创建必要目录...${NC}"

mkdir -p performance-reports
mkdir -p src/utils
mkdir -p scripts

echo "  ✓ 创建目录完成"

# 运行安全审计
echo -e "${BLUE}🔒 运行安全审计...${NC}"

npm audit --audit-level=high

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ 安全审计通过${NC}"
else
    echo -e "${YELLOW}⚠️  发现安全问题，建议运行 'npm audit fix'${NC}"
fi

# 测试构建
echo -e "${BLUE}🏗️  测试构建...${NC}"

npm run build:release

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ 构建测试成功${NC}"
else
    echo -e "${RED}❌ 构建测试失败${NC}"
    echo -e "${YELLOW}请检查配置文件是否正确${NC}"
    exit 1
fi

# 生成优化报告
echo -e "${BLUE}📊 生成优化报告...${NC}"

cat > optimization-report.md << EOF
# 性能优化应用报告

## 应用时间
$(date)

## 备份位置
$BACKUP_DIR/

## 应用的优化
- ✅ Vue配置优化 (代码分割、压缩、缓存)
- ✅ 依赖包升级 (安全修复)
- ✅ HTTP客户端优化 (缓存、重试、并发控制)
- ✅ Service Worker增强 (智能缓存)
- ✅ 性能监控系统
- ✅ 自动化测试工具

## 下一步操作
1. 运行开发服务器: \`npm run serve:release\`
2. 运行性能测试: \`npm run test:performance\`
3. 检查性能监控: 查看浏览器控制台
4. 部署到测试环境验证效果

## 回滚方法
如需回滚，运行以下命令:
\`\`\`bash
cp $BACKUP_DIR/vue.config.js vue.config.js
cp $BACKUP_DIR/package.json package.json
npm install
\`\`\`

## 性能目标
- 首屏加载时间: < 2秒
- PageSpeed分数: > 80分
- JavaScript包大小: < 500KB
- API响应时间: < 1秒
EOF

echo -e "${GREEN}✅ 优化应用完成！${NC}"
echo ""
echo -e "${BLUE}📋 总结:${NC}"
echo "  ✓ 配置文件已更新"
echo "  ✓ 依赖包已升级"
echo "  ✓ 构建测试通过"
echo "  ✓ 备份保存在: $BACKUP_DIR/"
echo "  ✓ 报告生成: optimization-report.md"
echo ""
echo -e "${YELLOW}🚀 下一步操作:${NC}"
echo "  1. 运行开发服务器: npm run serve:release"
echo "  2. 运行性能测试: npm run test:performance"
echo "  3. 查看优化报告: cat optimization-report.md"
echo ""
echo -e "${GREEN}🎉 性能优化应用成功！${NC}"
