#!/usr/bin/env node

/**
 * 性能测试脚本
 * 使用Lighthouse进行自动化性能测试
 */

const lighthouse = require('lighthouse')
const chromeLauncher = require('chrome-launcher')
const fs = require('fs')
const path = require('path')

// 测试配置
const TEST_CONFIG = {
  // 测试URL列表
  urls: [
    'http://localhost:8080', // 开发环境
    'https://your-staging-domain.com', // 测试环境
    'https://your-production-domain.com' // 生产环境
  ],
  
  // 游戏页面测试
  gamePages: [
    '/koa',
    '/dc',
    '/foundation',
    '/sdk2'
  ],
  
  // 性能阈值
  thresholds: {
    performance: 80,
    accessibility: 90,
    bestPractices: 80,
    seo: 80,
    pwa: 70
  },
  
  // Core Web Vitals阈值
  webVitals: {
    lcp: 2500, // ms
    fid: 100,  // ms
    cls: 0.1   // score
  }
}

// Lighthouse配置
const LIGHTHOUSE_CONFIG = {
  extends: 'lighthouse:default',
  settings: {
    onlyAudits: [
      'first-contentful-paint',
      'largest-contentful-paint',
      'first-input-delay',
      'cumulative-layout-shift',
      'speed-index',
      'interactive',
      'metrics',
      'performance-budget',
      'resource-summary',
      'third-party-summary',
      'bootup-time',
      'mainthread-work-breakdown',
      'network-requests',
      'network-rtt',
      'network-server-latency',
      'uses-optimized-images',
      'uses-webp-images',
      'uses-text-compression',
      'unused-css-rules',
      'unused-javascript',
      'modern-image-formats',
      'efficient-animated-content',
      'total-byte-weight'
    ]
  }
}

// Chrome启动选项
const CHROME_FLAGS = [
  '--headless',
  '--disable-gpu',
  '--no-sandbox',
  '--disable-dev-shm-usage',
  '--disable-extensions',
  '--disable-background-timer-throttling',
  '--disable-backgrounding-occluded-windows',
  '--disable-renderer-backgrounding'
]

class PerformanceTester {
  constructor() {
    this.results = []
    this.startTime = Date.now()
  }

  async runTests() {
    console.log('🚀 Starting performance tests...')
    
    try {
      // 启动Chrome
      const chrome = await chromeLauncher.launch({
        chromeFlags: CHROME_FLAGS
      })

      console.log(`Chrome launched on port ${chrome.port}`)

      // 运行所有测试
      for (const baseUrl of TEST_CONFIG.urls) {
        await this.testBaseUrl(baseUrl, chrome.port)
      }

      // 关闭Chrome
      await chrome.kill()

      // 生成报告
      await this.generateReport()
      
      console.log('✅ Performance tests completed!')
      
    } catch (error) {
      console.error('❌ Performance tests failed:', error)
      process.exit(1)
    }
  }

  async testBaseUrl(baseUrl, chromePort) {
    console.log(`\n📊 Testing ${baseUrl}...`)

    // 测试主页
    await this.testPage(baseUrl, chromePort, 'home')

    // 测试游戏页面
    for (const gamePage of TEST_CONFIG.gamePages) {
      const fullUrl = `${baseUrl}${gamePage}`
      await this.testPage(fullUrl, chromePort, `game-${gamePage.replace('/', '')}`)
    }
  }

  async testPage(url, chromePort, pageName) {
    console.log(`  Testing ${pageName}: ${url}`)

    try {
      const result = await lighthouse(url, {
        port: chromePort,
        output: 'json',
        logLevel: 'error'
      }, LIGHTHOUSE_CONFIG)

      const report = this.parseReport(result.lhr, url, pageName)
      this.results.push(report)

      // 输出关键指标
      console.log(`    Performance: ${report.scores.performance}`)
      console.log(`    LCP: ${report.metrics.lcp}ms`)
      console.log(`    FID: ${report.metrics.fid}ms`)
      console.log(`    CLS: ${report.metrics.cls}`)

      // 检查是否达到阈值
      this.checkThresholds(report)

    } catch (error) {
      console.error(`    ❌ Failed to test ${url}:`, error.message)
    }
  }

  parseReport(lhr, url, pageName) {
    const audits = lhr.audits
    
    return {
      url,
      pageName,
      timestamp: new Date().toISOString(),
      scores: {
        performance: Math.round(lhr.categories.performance.score * 100),
        accessibility: Math.round(lhr.categories.accessibility.score * 100),
        bestPractices: Math.round(lhr.categories['best-practices'].score * 100),
        seo: Math.round(lhr.categories.seo.score * 100),
        pwa: lhr.categories.pwa ? Math.round(lhr.categories.pwa.score * 100) : 0
      },
      metrics: {
        fcp: Math.round(audits['first-contentful-paint'].numericValue),
        lcp: Math.round(audits['largest-contentful-paint'].numericValue),
        fid: audits['first-input-delay'] ? Math.round(audits['first-input-delay'].numericValue) : 0,
        cls: Math.round(audits['cumulative-layout-shift'].numericValue * 1000) / 1000,
        speedIndex: Math.round(audits['speed-index'].numericValue),
        tti: Math.round(audits['interactive'].numericValue)
      },
      resources: {
        totalByteWeight: Math.round(audits['total-byte-weight'].numericValue / 1024), // KB
        unusedCSS: audits['unused-css-rules'].details ? 
          Math.round(audits['unused-css-rules'].details.overallSavingsBytes / 1024) : 0,
        unusedJS: audits['unused-javascript'].details ? 
          Math.round(audits['unused-javascript'].details.overallSavingsBytes / 1024) : 0,
        unoptimizedImages: audits['uses-optimized-images'].details ? 
          Math.round(audits['uses-optimized-images'].details.overallSavingsBytes / 1024) : 0
      },
      opportunities: this.extractOpportunities(audits)
    }
  }

  extractOpportunities(audits) {
    const opportunities = []
    
    const opportunityAudits = [
      'uses-optimized-images',
      'uses-webp-images',
      'uses-text-compression',
      'unused-css-rules',
      'unused-javascript',
      'modern-image-formats',
      'efficient-animated-content'
    ]

    opportunityAudits.forEach(auditId => {
      const audit = audits[auditId]
      if (audit && audit.details && audit.details.overallSavingsMs > 100) {
        opportunities.push({
          id: auditId,
          title: audit.title,
          description: audit.description,
          savingsMs: Math.round(audit.details.overallSavingsMs),
          savingsBytes: audit.details.overallSavingsBytes ? 
            Math.round(audit.details.overallSavingsBytes / 1024) : 0
        })
      }
    })

    return opportunities
  }

  checkThresholds(report) {
    const { scores, metrics } = report
    const issues = []

    // 检查分数阈值
    Object.entries(TEST_CONFIG.thresholds).forEach(([category, threshold]) => {
      if (scores[category] < threshold) {
        issues.push(`${category}: ${scores[category]} < ${threshold}`)
      }
    })

    // 检查Core Web Vitals
    if (metrics.lcp > TEST_CONFIG.webVitals.lcp) {
      issues.push(`LCP: ${metrics.lcp}ms > ${TEST_CONFIG.webVitals.lcp}ms`)
    }
    if (metrics.fid > TEST_CONFIG.webVitals.fid) {
      issues.push(`FID: ${metrics.fid}ms > ${TEST_CONFIG.webVitals.fid}ms`)
    }
    if (metrics.cls > TEST_CONFIG.webVitals.cls) {
      issues.push(`CLS: ${metrics.cls} > ${TEST_CONFIG.webVitals.cls}`)
    }

    if (issues.length > 0) {
      console.log(`    ⚠️  Issues: ${issues.join(', ')}`)
    } else {
      console.log(`    ✅ All thresholds met`)
    }
  }

  async generateReport() {
    const reportDir = path.join(process.cwd(), 'performance-reports')
    
    // 创建报告目录
    if (!fs.existsSync(reportDir)) {
      fs.mkdirSync(reportDir, { recursive: true })
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    const reportFile = path.join(reportDir, `performance-report-${timestamp}.json`)
    const summaryFile = path.join(reportDir, `performance-summary-${timestamp}.md`)

    // 保存详细报告
    fs.writeFileSync(reportFile, JSON.stringify({
      timestamp: new Date().toISOString(),
      duration: Date.now() - this.startTime,
      config: TEST_CONFIG,
      results: this.results
    }, null, 2))

    // 生成Markdown摘要
    const summary = this.generateMarkdownSummary()
    fs.writeFileSync(summaryFile, summary)

    console.log(`\n📄 Reports generated:`)
    console.log(`  Detailed: ${reportFile}`)
    console.log(`  Summary: ${summaryFile}`)
  }

  generateMarkdownSummary() {
    const summary = [`# Performance Test Report`]
    summary.push(`\nGenerated: ${new Date().toISOString()}`)
    summary.push(`Duration: ${Math.round((Date.now() - this.startTime) / 1000)}s`)
    
    summary.push(`\n## Summary`)
    
    // 计算平均分数
    const avgScores = this.calculateAverageScores()
    summary.push(`\n### Average Scores`)
    summary.push(`- Performance: ${avgScores.performance}`)
    summary.push(`- Accessibility: ${avgScores.accessibility}`)
    summary.push(`- Best Practices: ${avgScores.bestPractices}`)
    summary.push(`- SEO: ${avgScores.seo}`)
    summary.push(`- PWA: ${avgScores.pwa}`)

    // Core Web Vitals
    const avgMetrics = this.calculateAverageMetrics()
    summary.push(`\n### Core Web Vitals`)
    summary.push(`- LCP: ${avgMetrics.lcp}ms`)
    summary.push(`- FID: ${avgMetrics.fid}ms`)
    summary.push(`- CLS: ${avgMetrics.cls}`)

    // 详细结果
    summary.push(`\n## Detailed Results`)
    this.results.forEach(result => {
      summary.push(`\n### ${result.pageName} (${result.url})`)
      summary.push(`- Performance: ${result.scores.performance}`)
      summary.push(`- LCP: ${result.metrics.lcp}ms`)
      summary.push(`- FID: ${result.metrics.fid}ms`)
      summary.push(`- CLS: ${result.metrics.cls}`)
      summary.push(`- Total Size: ${result.resources.totalByteWeight}KB`)
      
      if (result.opportunities.length > 0) {
        summary.push(`\n#### Optimization Opportunities`)
        result.opportunities.forEach(opp => {
          summary.push(`- ${opp.title}: ${opp.savingsMs}ms / ${opp.savingsBytes}KB`)
        })
      }
    })

    return summary.join('\n')
  }

  calculateAverageScores() {
    const totals = { performance: 0, accessibility: 0, bestPractices: 0, seo: 0, pwa: 0 }
    
    this.results.forEach(result => {
      Object.keys(totals).forEach(key => {
        totals[key] += result.scores[key]
      })
    })

    const count = this.results.length
    return Object.keys(totals).reduce((avg, key) => {
      avg[key] = Math.round(totals[key] / count)
      return avg
    }, {})
  }

  calculateAverageMetrics() {
    const totals = { lcp: 0, fid: 0, cls: 0 }
    
    this.results.forEach(result => {
      totals.lcp += result.metrics.lcp
      totals.fid += result.metrics.fid
      totals.cls += result.metrics.cls
    })

    const count = this.results.length
    return {
      lcp: Math.round(totals.lcp / count),
      fid: Math.round(totals.fid / count),
      cls: Math.round((totals.cls / count) * 1000) / 1000
    }
  }
}

// 运行测试
if (require.main === module) {
  const tester = new PerformanceTester()
  tester.runTests().catch(console.error)
}

module.exports = PerformanceTester
