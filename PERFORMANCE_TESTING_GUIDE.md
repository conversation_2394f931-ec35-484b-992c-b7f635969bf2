# 性能测试指南

## 使用PageSpeed Insights进行性能评估

### 1. 测试准备

访问 [PageSpeed Insights](https://pagespeed.web.dev/) 进行性能测试

### 2. 需要测试的页面

#### 主要页面URL（请根据实际部署地址调整）：
- 主页: `https://your-domain.com/`
- KOA充值页: `https://your-domain.com/koa`
- DC充值页: `https://your-domain.com/dcdarklegion`
- Foundation充值页: `https://your-domain.com/foundation`
- 移动端页面: `https://your-domain.com/` (移动端测试)

### 3. 关键性能指标

#### Core Web Vitals
- **LCP (Largest Contentful Paint)**: 目标 < 2.5s
- **FID (First Input Delay)**: 目标 < 100ms
- **CLS (Cumulative Layout Shift)**: 目标 < 0.1

#### 其他重要指标
- **FCP (First Contentful Paint)**: 目标 < 1.8s
- **TTI (Time to Interactive)**: 目标 < 3.8s
- **Speed Index**: 目标 < 3.4s

### 4. 当前预期问题

基于代码分析，预期会发现以下性能问题：

#### 移动端问题
- 图片资源过大，未进行压缩优化
- JavaScript包体积较大，缺少有效分割
- 第三方脚本阻塞渲染
- 缺少资源预加载

#### 桌面端问题
- 静态资源缓存策略不够优化
- CSS和JS文件未充分压缩
- 字体加载可能导致布局偏移

### 5. 性能优化检查清单

#### 图片优化
- [ ] 使用WebP格式
- [ ] 实现响应式图片
- [ ] 添加图片懒加载
- [ ] 压缩图片文件大小

#### 代码优化
- [ ] 代码分割和懒加载
- [ ] 移除未使用的CSS和JS
- [ ] 压缩和混淆代码
- [ ] 使用Tree Shaking

#### 资源加载优化
- [ ] 添加资源预加载 (preload)
- [ ] 使用资源预连接 (preconnect)
- [ ] 优化关键渲染路径
- [ ] 减少HTTP请求数量

#### 缓存策略
- [ ] 设置适当的缓存头
- [ ] 使用Service Worker缓存
- [ ] 实现静态资源版本控制
- [ ] CDN配置优化

### 6. 测试流程

#### 步骤1: 基线测试
1. 在PageSpeed Insights中输入页面URL
2. 分别测试移动端和桌面端
3. 记录当前性能分数和Core Web Vitals
4. 截图保存测试结果

#### 步骤2: 问题分析
1. 查看"机会"部分的优化建议
2. 分析"诊断"部分的具体问题
3. 重点关注影响Core Web Vitals的因素
4. 识别最大的性能瓶颈

#### 步骤3: 优化实施
1. 按优先级实施优化措施
2. 每次优化后重新测试
3. 对比优化前后的性能数据
4. 记录优化效果

#### 步骤4: 持续监控
1. 建立性能监控基线
2. 定期进行性能测试
3. 设置性能预算和告警
4. 在CI/CD中集成性能测试

### 7. 预期优化效果

#### 优化前预期分数（基于代码分析）
- **移动端**: 40-60分
- **桌面端**: 60-80分

#### 优化后目标分数
- **移动端**: 80+分
- **桌面端**: 90+分

### 8. 具体优化建议

#### 立即可实施的优化
```javascript
// 1. 添加资源预加载
<link rel="preload" href="/static/css/app.css" as="style">
<link rel="preload" href="/static/js/app.js" as="script">

// 2. 优化图片加载
<img src="image.jpg" loading="lazy" alt="description">

// 3. 添加字体预加载
<link rel="preload" href="/fonts/main.woff2" as="font" type="font/woff2" crossorigin>
```

#### 构建配置优化
```javascript
// vue.config.js
module.exports = {
  configureWebpack: {
    optimization: {
      splitChunks: {
        chunks: 'all',
        maxSize: 244000, // 244KB chunks
      }
    }
  },
  chainWebpack: config => {
    // 压缩图片
    config.module
      .rule('images')
      .use('image-webpack-loader')
      .loader('image-webpack-loader')
      .options({
        mozjpeg: { progressive: true, quality: 80 },
        optipng: { enabled: true },
        pngquant: { quality: [0.65, 0.8] }
      });
  }
}
```

### 9. 监控和报告

#### 性能监控工具
- Google Analytics (Core Web Vitals报告)
- Real User Monitoring (RUM)
- Lighthouse CI
- WebPageTest

#### 报告模板
```
性能测试报告 - [日期]

测试页面: [URL]
测试工具: PageSpeed Insights

移动端结果:
- 性能分数: [分数]/100
- LCP: [时间]s
- FID: [时间]ms  
- CLS: [分数]

桌面端结果:
- 性能分数: [分数]/100
- LCP: [时间]s
- FID: [时间]ms
- CLS: [分数]

主要问题:
1. [问题描述]
2. [问题描述]

优化建议:
1. [建议内容]
2. [建议内容]

下一步行动:
- [ ] [具体行动项]
- [ ] [具体行动项]
```

### 10. 性能预算设置

建议设置以下性能预算：
- JavaScript包大小: < 200KB (gzipped)
- CSS文件大小: < 50KB (gzipped)  
- 图片总大小: < 500KB
- 第三方脚本: < 3个
- LCP: < 2.5s
- FID: < 100ms
- CLS: < 0.1

通过PageSpeed Insights的定期测试，可以确保项目性能持续改进并满足用户体验要求。
