const fs = require('fs')

function updateVersionSync () {
  // 同步读取 package.json 文件
  const data = fs.readFileSync('package.json', 'utf8')

  // 解析 package.json 内容为 JSON
  const packageJson = JSON.parse(data)

  // 检查是否存在 version 字段
  if (!packageJson.version) {
    throw new Error('No version found in package.json')
  }

  // 将版本号分割成数组
  const versionSegments = packageJson.version.split('.')
  if (versionSegments.length !== 3) {
    throw new Error('Version number does not have 3 segments')
  }

  // 将最小位加 1
  versionSegments[2] = (parseInt(versionSegments[2], 10) + 1).toString()

  // 重新拼接版本号
  packageJson.version = versionSegments.join('.')

  // 同步将新的 package.json 内容写回文件
  fs.writeFileSync('package.json', JSON.stringify(packageJson, null, 2), 'utf8')

  // 返回新的版本号
  return packageJson.version
}

module.exports = updateVersionSync
