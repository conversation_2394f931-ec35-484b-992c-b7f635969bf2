{"version": 3, "sources": ["webpack:///./src/components/pop/sdk2Tips.vue?599e", "webpack:///./src/components/OverSizeScale.vue?1ade", "webpack:///./src/components/OverSizeScale.vue", "webpack:///src/components/OverSizeScale.vue", "webpack:///./src/components/OverSizeScale.vue?6d5c", "webpack:///./src/components/OverSizeScale.vue?5f43", "webpack:///./src/components/pop/sdk2Tips.vue", "webpack:///src/components/pop/sdk2Tips.vue", "webpack:///./src/components/pop/sdk2Tips.vue?013e", "webpack:///./src/components/pop/sdk2Tips.vue?50b3"], "names": ["render", "_vm", "this", "_c", "_self", "ref", "staticClass", "style", "scale", "_t", "staticRenderFns", "name", "data", "methods", "calc", "outer", "$refs", "offsetWidth", "inner", "mounted", "component", "type", "_v", "_s", "$vt", "_e", "on", "$event", "$root", "$emit", "attrs", "domProps", "props", "option", "components", "OverSizeScale", "ContainerV2"], "mappings": "8GAAA,W,kCCAA,W,yDCAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,OAAO,CAACE,IAAI,QAAQC,YAAY,SAAS,CAACH,EAAG,OAAO,CAACE,IAAI,QAAQC,YAAY,QAAQC,MAAO,oBAAoBN,EAAIO,UAAW,CAACP,EAAIQ,GAAG,YAAY,MAErNC,EAAkB,GCOP,GACfC,KAAA,gBACAC,OACA,OACAJ,MAAA,IAGAK,QAAA,CACAC,OACA,MAAAC,EAAA,KAAAC,MAAAD,MAAAE,YACAC,EAAA,KAAAF,MAAAE,MAAAD,YACAC,EAAAH,IAAA,KAAAP,MAAAO,EAAAG,KAGAC,UACA,KAAAL,SCxBsV,I,wBCQlVM,EAAY,eACd,EACApB,EACAU,GACA,EACA,KACA,WACA,MAIa,OAAAU,E,yECnBf,IAAIpB,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,eAAe,CAACG,YAAY,sBAAsB,CAACH,EAAG,MAAM,CAACG,YAAY,SAAS,CAAe,kBAAbL,EAAIoB,KAA0BlB,EAAG,MAAM,CAACG,YAAY,OAAO,CAACH,EAAG,kBAAkB,CAACF,EAAIqB,GAAG,IAAIrB,EAAIsB,GAAGtB,EAAIuB,IAAI,4BAA4B,QAAQ,GAAGvB,EAAIwB,KAAKtB,EAAG,IAAI,CAACG,YAAY,QAAQoB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAO1B,EAAI2B,MAAMC,MAAM,kBAAkB1B,EAAG,MAAM,CAACG,YAAY,WAAW,CAAgB,WAAdL,EAAIoB,KAAoB,CAACpB,EAAIqB,GAAG,8MAA8MnB,EAAG,IAAI,CAAC2B,MAAM,CAAC,KAAO,qDAAqD,OAAS,WAAW,CAAC7B,EAAIqB,GAAG,iBAAiBrB,EAAIqB,GAAG,+IAA+InB,EAAG,IAAI,CAAC2B,MAAM,CAAC,OAAS,SAAS,KAAO,wDAAwD,CAAC7B,EAAIqB,GAAG,mBAAmBrB,EAAIqB,GAAG,QAAQrB,EAAIwB,KAAoB,kBAAdxB,EAAIoB,KAA2B,CAAClB,EAAG,MAAM,CAAC4B,SAAS,CAAC,UAAY9B,EAAIsB,GAAGtB,EAAIuB,IAAI,kCAAkCvB,EAAIwB,MAAM,GAAGtB,EAAG,MAAM,CAACG,YAAY,UAAU,CAACH,EAAG,MAAM,CAACG,YAAY,gBAAgBoB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAO1B,EAAI2B,MAAMC,MAAM,eAAe,CAAC5B,EAAIqB,GAAG,aAEv1CZ,EAAkB,G,wBCEP,GACfC,KAAA,WACAqB,MAAA,WACApB,OACA,OACAS,KAAA,KAAAY,OAAAZ,OAGAa,WAAA,CAAAC,qBAAAC,qBCZgW,I,wBCQ5VhB,EAAY,eACd,EACApB,EACAU,GACA,EACA,KACA,WACA,MAIa,aAAAU,E", "file": "js/chunk-sdk2.7d34e702.js", "sourcesContent": ["export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./sdk2Tips.vue?vue&type=style&index=0&id=ae5dd5e8&prod&scoped=true&lang=scss\"", "export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./OverSizeScale.vue?vue&type=style&index=0&id=62189d20&prod&scoped=true&lang=scss\"", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('span',{ref:\"outer\",staticClass:\"outer\"},[_c('span',{ref:\"inner\",staticClass:\"inner\",style:(`transform: scale(${_vm.scale})`)},[_vm._t(\"default\")],2)])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n<span class=\"outer\" ref=\"outer\">\n  <span class=\"inner\" :style=\"`transform: scale(${scale})`\" ref=\"inner\">\n    <slot></slot>\n  </span>\n</span>\n</template>\n\n<script>\nexport default {\n  name: 'OverSizeScale',\n  data () {\n    return {\n      scale: 1\n    }\n  },\n  methods: {\n    calc () {\n      const outer = this.$refs.outer.offsetWidth\n      const inner = this.$refs.inner.offsetWidth\n      if (inner > outer) this.scale = outer / inner\n    }\n  },\n  mounted () {\n    this.calc()\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n.outer{\n  width: 100%;\n  overflow: hidden;\n  display: inline-block;\n\n  .inner{\n    transform-origin: left center;\n    display: inline-block;\n    white-space: nowrap;\n    line-height: 1;\n  }\n}\n</style>\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./OverSizeScale.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./OverSizeScale.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./OverSizeScale.vue?vue&type=template&id=62189d20&scoped=true\"\nimport script from \"./OverSizeScale.vue?vue&type=script&lang=js\"\nexport * from \"./OverSizeScale.vue?vue&type=script&lang=js\"\nimport style0 from \"./OverSizeScale.vue?vue&type=style&index=0&id=62189d20&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"62189d20\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('container-v2',{staticClass:\"sdk2-constructions\"},[_c('div',{staticClass:\"title\"},[(_vm.type === 'constructions')?_c('div',{staticClass:\"txt\"},[_c('over-size-scale',[_vm._v(\" \"+_vm._s(_vm.$vt('sdk2_construction_title'))+\" \")])],1):_vm._e(),_c('i',{staticClass:\"close\",on:{\"click\":function($event){return _vm.$root.$emit('closePop')}}})]),_c('div',{staticClass:\"content\"},[(_vm.type  === 'policy')?[_vm._v(\" BY CLICKING “PAY NOW”, YOU CONFIRM: THAT YOU HAVE READ, AGREE AND UNDERSTAND THAT FOR EACH DIGITAL GOOD PURCHASE, YOU WILL ONLY RECEIVE A LICENSE, SUBJECT TO THE RIGHTS AND RESTRICTIONS SET OUT IN OUR \"),_c('a',{attrs:{\"href\":\"https://funplus.com/usage-rules-for-digital-goods/\",\"target\":\"_blank\"}},[_vm._v(\"USAGE RULES\")]),_vm._v(\" FOR DIGITAL GOODS. THAT YOU AGREE TO THE IMMEDIATE FULFILMENT OF THE CONTRACT AND ACCEPT THAT YOU THEREBY LOSE YOUR WITHDRAWAL RIGHT (SEE \"),_c('a',{attrs:{\"target\":\"_blank\",\"href\":\"https://funplus.com/terms-conditions/en/#section-15\"}},[_vm._v(\"REFUND POLICY\")]),_vm._v(\"). \")]:_vm._e(),(_vm.type  === 'constructions')?[_c('div',{domProps:{\"innerHTML\":_vm._s(_vm.$vt('sdk2_construction_content'))}})]:_vm._e()],2),_c('div',{staticClass:\"footer\"},[_c('div',{staticClass:\"btn click-btn\",on:{\"click\":function($event){return _vm.$root.$emit('closePop')}}},[_vm._v(\"OK\")])])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<script>\nimport ContainerV2 from '@/components/pop/containerV2.vue'\nimport OverSizeScale from '@/components/OverSizeScale.vue'\n\nexport default {\n  name: 'sdk2Tips',\n  props: ['option'],\n  data () {\n    return {\n      type: this.option.type\n    }\n  },\n  components: { OverSizeScale, ContainerV2 }\n}\n</script>\n\n<template>\n  <container-v2 class=\"sdk2-constructions\">\n    <div class=\"title\">\n      <div class=\"txt\" v-if=\"type === 'constructions'\">\n        <over-size-scale>\n          {{ $vt('sdk2_construction_title') }}\n        </over-size-scale>\n      </div>\n      <i @click=\"$root.$emit('closePop')\" class=\"close\"></i>\n    </div>\n    <div class=\"content\">\n      <template v-if=\"type  === 'policy'\">\n        BY CLICKING “PAY NOW”, YOU CONFIRM:\n        THAT YOU HAVE READ, AGREE AND UNDERSTAND THAT FOR EACH DIGITAL GOOD PURCHASE, YOU WILL ONLY RECEIVE A LICENSE, SUBJECT TO THE RIGHTS AND RESTRICTIONS SET OUT IN OUR <a href=\"https://funplus.com/usage-rules-for-digital-goods/\" target=\"_blank\">USAGE RULES</a> FOR DIGITAL GOODS.\n        THAT YOU AGREE TO THE IMMEDIATE FULFILMENT OF THE CONTRACT AND ACCEPT THAT YOU THEREBY LOSE YOUR WITHDRAWAL RIGHT (SEE <a target=\"_blank\" href=\"https://funplus.com/terms-conditions/en/#section-15\">REFUND POLICY</a>).\n      </template>\n      <template v-if=\"type  === 'constructions'\">\n        <div v-html=\"$vt('sdk2_construction_content')\"></div>\n      </template>\n    </div>\n    <div class=\"footer\">\n      <div class=\"btn click-btn\" @click=\"$root.$emit('closePop')\">OK</div>\n    </div>\n  </container-v2>\n</template>\n\n<style scoped lang=\"scss\">\n@use \"~@/utils/utils.scss\" as utils;\n\n.sdk2-constructions{\n  background: #2F2F2F;\n  border-radius: 10px;\n  padding: 30px 40px 40px;\n\n  .title{\n    position: relative;\n    @include utils.flexCenter;\n    height: 40px;\n\n    .txt{\n      font-family: SourceHanSansCN, SourceHanSansCN;\n      font-weight: bold;\n      font-size: 30px;\n      color: #FFFFFF;\n      width: 530px;\n      display: inline-block;\n      overflow: hidden;\n      @include utils.flexCenter\n    }\n\n    .close{\n      @include utils.bgCenter('common/login-validate/login-validate-close.png', 30px, 30px);\n      position: absolute;\n      cursor: pointer;\n      top: 50%;\n      transform: translateY(-50%);\n      right: 0;\n    }\n  }\n\n  .content{\n    font-size: 24px;\n    padding: 20px 0 30px;\n    color: #FFFFFF;\n    line-height: 36px;\n    text-align: justify;\n\n    a{\n      color: #FF5E0F;\n      text-decoration: underline;\n    }\n  }\n\n  .footer{\n    @include utils.flexCenter;\n    .btn{\n      width: 216px;\n      height: 70px;\n      background: #FF5E0F;\n      border-radius: 10px;\n      @include utils.flexCenter;\n      color: white;\n      font-size: 30px;\n      font-weight: bold;\n    }\n  }\n}\n</style>\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./sdk2Tips.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./sdk2Tips.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./sdk2Tips.vue?vue&type=template&id=ae5dd5e8&scoped=true\"\nimport script from \"./sdk2Tips.vue?vue&type=script&lang=js\"\nexport * from \"./sdk2Tips.vue?vue&type=script&lang=js\"\nimport style0 from \"./sdk2Tips.vue?vue&type=style&index=0&id=ae5dd5e8&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"ae5dd5e8\",\n  null\n  \n)\n\nexport default component.exports"], "sourceRoot": ""}