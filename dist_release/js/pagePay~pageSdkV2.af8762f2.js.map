{"version": 3, "sources": ["webpack:///./src/views/PayMixin.js", "webpack:///./src/components/ChannelChoose.vue", "webpack:///src/components/ChannelChoose.vue", "webpack:///./src/components/ChannelChoose.vue?f1d4", "webpack:///./src/components/ChannelChoose.vue?28ab", "webpack:///./node_modules/core-js/modules/web.url-search-params.has.js", "webpack:///./src/components/common/CommonPart.vue", "webpack:///src/components/common/CommonPart.vue", "webpack:///./src/components/common/CommonPart.vue?bfa2", "webpack:///./src/components/common/CommonPart.vue?dbe8", "webpack:///./src/components/coupon/CouponChooseList.vue?b7e6", "webpack:///./src/components/ChannelChoose.vue?858b", "webpack:///./node_modules/core-js/modules/web.url-search-params.size.js", "webpack:///./src/components/coupon/CouponChoosePop.vue?f92a", "webpack:///./src/components/LoginModule.vue", "webpack:///./src/utils/uidEncrypto.js", "webpack:///src/components/LoginModule.vue", "webpack:///./src/components/LoginModule.vue?245e", "webpack:///./src/components/LoginModule.vue?1f07", "webpack:///./src/components/product/DirectGiftPackage.vue?8b2e", "webpack:///./src/components/mobile/CheckoutFooterTax.vue?77dc", "webpack:///./src/assets/common/coupon/coupon-item-chosen.png", "webpack:///./src/components/LoginModule.vue?a7df", "webpack:///./src/components/coupon/CouponChoose.vue", "webpack:///./src/components/coupon/CouponChoosePop.vue", "webpack:///./src/components/coupon/CouponChooseList.vue", "webpack:///./src/components/coupon/couponToggle.vue", "webpack:///src/components/coupon/couponToggle.vue", "webpack:///./src/components/coupon/couponToggle.vue?c7a2", "webpack:///./src/components/coupon/couponToggle.vue?473a", "webpack:///src/components/coupon/CouponChooseList.vue", "webpack:///./src/components/coupon/CouponChooseList.vue?6103", "webpack:///./src/components/coupon/CouponChooseList.vue?4bf6", "webpack:///src/components/coupon/CouponChoosePop.vue", "webpack:///./src/components/coupon/CouponChoosePop.vue?cace", "webpack:///./src/components/coupon/CouponChoosePop.vue?66ca", "webpack:///src/components/coupon/CouponChoose.vue", "webpack:///./src/components/coupon/CouponChoose.vue?2842", "webpack:///./src/components/coupon/CouponChoose.vue?193b", "webpack:///./node_modules/core-js/modules/web.url-search-params.delete.js", "webpack:///./src/components/product/DirectGiftPackage.vue", "webpack:///./src/components/FixedCouponSwitch.vue", "webpack:///src/components/FixedCouponSwitch.vue", "webpack:///./src/components/FixedCouponSwitch.vue?9422", "webpack:///./src/components/FixedCouponSwitch.vue?7aef", "webpack:///src/components/product/DirectGiftPackage.vue", "webpack:///./src/components/product/DirectGiftPackage.vue?a0ff", "webpack:///./src/components/product/DirectGiftPackage.vue?05c1", "webpack:///./src/components/coupon/couponToggle.vue?d7f4", "webpack:///./src/components/OverSizeScale.vue?1ade", "webpack:///./src/components/FixedCouponSwitch.vue?424e", "webpack:///./src/components/product/Preload.js", "webpack:///./src/components/common/CommonPart.vue?c8d2", "webpack:///./node_modules/core-js/internals/validate-arguments-length.js", "webpack:///./src/components/OverSizeScale.vue", "webpack:///src/components/OverSizeScale.vue", "webpack:///./src/components/OverSizeScale.vue?6d5c", "webpack:///./src/components/OverSizeScale.vue?5f43", "webpack:///./src/components/mobile/CheckoutFooterTax.vue", "webpack:///src/components/mobile/CheckoutFooterTax.vue", "webpack:///./src/components/mobile/CheckoutFooterTax.vue?744b", "webpack:///./src/components/mobile/CheckoutFooterTax.vue?02fd", "webpack:///./src/components/coupon/CouponChoose.vue?e595", "webpack:///./src/assets/common/coupon/coupon-item.png"], "names": ["methods", "customGetSmDeviceId", "cb", "console", "error", "Promise", "resolve", "reject", "this", "$loading", "show", "timer", "setTimeout", "dealSmDeviceId", "deviceId", "clearTimeout", "then", "finally", "hide", "purchaseGoodsWithDeviceId", "backup", "channel_id", "channelId", "channel_name", "channelName", "sub_channel_id", "subChannelId", "chosenChannel", "<PERSON><PERSON><PERSON><PERSON>", "params", "name", "$vt", "product_id", "price", "nowPriceWithTaxAndExtra", "channel", "return_url", "location", "origin", "fp_device_id", "$router", "options", "base", "replace", "IS_CHECKOUT_SDK", "returnUrl", "window", "__ROUTERPATH", "endsWith", "slice", "$gameName", "ext_info", "JSON", "stringify", "project_code", "type", "chosen<PERSON><PERSON>", "custom_multiple", "utmCampaign", "$store", "state", "urlParams", "utm_campaign", "includes", "index", "indexOf", "tracking_id", "getPWADisplayMode", "getPlatform", "browser_id", "localStorage", "getItem", "ref", "<PERSON><PERSON><PERSON><PERSON>", "browser_info", "terminalType", "innerWidth", "osType", "getOS", "getBrowserInfo", "FinalPriceState", "getters", "chosen<PERSON><PERSON><PERSON><PERSON>", "feType", "act_type", "discount", "discount_price", "coupon_id", "deduct_price", "defaultDiscountInfo", "formdata", "functionSwitch", "fixedDiscountType", "isMinimumDiamondId", "smallD<PERSON>ondDoubleDiscount", "$gcbk", "isNotRebate", "payermaxKey", "vb", "builtInCashier", "order_type", "sessionStorage", "removeItem", "tc", "parse", "sdkType", "package_id", "package_name", "product_name", "game_order_id", "oid", "setItem", "IS_CHECKOUT_SDK_V2", "calcSdk2Info", "_calState", "defaultPackageName", "orderInfo", "method", "amount", "country", "currency", "product_info", "event", "revenue", "requestLoading", "placeOrderCard", "placeOrderToken", "res", "data", "code", "message", "$root", "$emit", "debt", "coin_debt", "$once", "result", "Error", "logForPayResult", "order_id", "pay_url", "url", "URL", "URLSearchParams", "search", "get", "ppParams", "ppToken", "coinNums", "coin_recv", "currency_symbol", "inDebt", "push", "href", "open_with_new_window", "open", "host", "payment_host", "payment_order_id", "out_trade_no", "payment_order_id_sign", "client_secret", "store_card_url", "stripe_client_secret", "catch", "err", "log", "errTipsList", "1", "$t", "2", "3", "$toast", "check_status", "is<PERSON>ogin", "agreePrivacyPolicy", "__needDEPop", "payload", "ok", "no", "me", "judgeRisk", "key", "purchaseGoods", "computed", "mapState", "created", "$on", "history", "pushState", "pathname", "render", "_vm", "_c", "_self", "class", "gameinfo", "gameCode", "sdk", "attrs", "staticClass", "calChannelList", "length", "_l", "FE_CHANNEL_ID", "on", "$event", "toggleStatus", "directives", "rawName", "value", "icon_url", "expression", "arg", "subscript", "whetherShowVipBonus", "_v", "_s", "vip", "channelBonus", "totalDiamond", "coin", "_e", "staticRenderFns", "components", "CommonPart", "props", "activity", "Object", "default", "channelList", "unwatch", "undefined", "isUserChosen", "channelInfo", "isInit", "discountSubChannelId", "isFirstPayUsed", "FE_INDEX", "someChannelNeedHide", "commit", "filter", "item", "loadChannelList", "testParam", "cr", "arr", "final<PERSON><PERSON>", "Math", "ceil", "toFixed", "nowPrice", "getTokenChannelList", "adapterChannel", "lastChannel", "userinfo", "lastList", "lastList2", "channelFlag", "isPc", "$nextTick", "gsap", "from", "height", "duration", "clearProps", "list", "isCn", "delSubChannel", "isWx", "isMobile", "map", "newChosen", "oldChosen", "initLastChannel", "getLastChosenChannel", "<PERSON><PERSON><PERSON><PERSON>", "component", "defineBuiltIn", "uncurryThis", "toString", "validateArgumentsLength", "$URLSearchParams", "URLSearchParamsPrototype", "prototype", "getAll", "$has", "has", "arguments", "$value", "values", "enumerable", "unsafe", "_t", "labelFont", "String", "DESCRIPTORS", "defineBuiltInAccessor", "for<PERSON>ach", "count", "configurable", "icon", "isPCSDK", "logout", "isKOA", "level", "vipIntroducePageUrl", "0", "server", "focusEmphasize", "uid", "domProps", "target", "composing", "stopPropagation", "isShowTipsImg", "active", "$imageLoader", "query", "<PERSON><PERSON><PERSON>", "CryptoJS", "enc", "Utf8", "ivHex", "clone", "GetAesResult", "uidplus", "encodeURIComponent", "AES", "encrypt", "iv", "mode", "CBC", "padding", "pad", "Pkcs7", "sigBytes", "words", "splice", "process", "$i18n", "locale", "openid", "hideErrToast", "game_project", "gameProject", "ticket", "floor", "Date", "now", "fopenid", "logForClickLogin", "getUserInfoForToken", "secret<PERSON>ey", "decryptAES", "e", "whiteChannel", "pkg_channel", "greyChannel", "pkgChannel", "greyItem", "to", "blackChannel", "isSDK", "loginValidation", "isValidation", "goValidation", "isROMCP", "loadVipStatus", "<PERSON><PERSON><PERSON>", "initRiskPolicy", "logForLoginSuccess", "opacity", "xPercent", "$tips", "msgMap", "flag", "emit", "bind", "getCommonInfo", "user_risk_list", "reserve_card_channel_gray_ratio", "switch_global_config", "change_coupon_enable", "point_card_product", "id", "sendCode", "username", "successCb", "failCb", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "avatarFlag", "StorageUtils", "getLocalStorage", "popDate", "getMonth", "getDate", "setLocalStorage", "p0", "p1", "p2", "getAmeDo", "isNewUser", "isFirstToast", "success", "input", "document", "querySelector", "focus", "addEventListener", "module", "exports", "staticStyle", "showPop", "availableCouponNum", "rateWidthOutPercent", "isArZone", "currencyUnit", "rate", "hadObtainedList", "notObtainedList", "chosenIndex", "closeCouponPop", "navIndex", "isFirstChargeUsed", "tempChosenCoupon", "chooseCoupon", "couponList", "leaveCount", "reach", "is_invalid", "choose", "switchToggleState", "change_enable", "lang<PERSON><PERSON>", "num", "langValue", "showLeaveDate", "showEmpty", "beginToggleCoupon", "apply", "tips", "couponItem", "change_type", "toggleCouponSingle", "tempChosen", "mounted", "$el", "scrollIntoView", "behavior", "Coupon<PERSON><PERSON>gle", "onceADay", "findIndex", "CouponChooseList", "top", "ease", "backgroundColor", "getRate", "getDiamondRate", "getDiamondSend", "level_coin", "OverSizeScale", "CouponChoosePop", "timeInterval", "isLoadingCoupon", "mapGetters", "l", "initActInfo", "keepPop", "clearInterval", "couponSort", "coupons", "items", "sort", "a", "b", "coupon_expire_time", "parseFloat", "loadActivity", "isToggleRebate", "package_type", "couponLoading", "getActivityListForToken", "isFixedRebateWork", "fixed_discount", "fixed_rebate", "first_pay", "coupon", "deduct", "range_first_pay", "fixFoundationCoupon", "adapterCouponType", "firstPay<PERSON>rigin", "productId", "first_pay_rebate", "couponOrigin", "couponOriginCanUse", "is_received", "couponOriginCanNotUse", "deduct<PERSON><PERSON><PERSON>", "deductOriginCanUse", "deductOriginCanNotUse", "rebateOrigin", "rebate", "rebateOriginCanUse", "rebateOriginCanNotUse", "defaultDiscountOrigin", "fixedRebateOrigin", "fixedDynamicRebateOrigin", "product_fixed_rebate", "chosen", "all", "range_product_fixed_rebate", "calcLeaveTime", "leftObtainedList", "availableList", "canNotChooseCouponByDefault", "reChooseCouponId", "max", "parsingSdk2Coupon", "discount_range", "isCheckoutSdk", "diamondNumArr", "discount_price_range", "split", "kind", "langKeyMap", "entries", "fixActivityInfo", "couponFeIndex", "findLastIndex", "fixDate", "p", "getLeaveDateTxt", "newList", "setInterval", "requestParams", "returnData", "firstCouponList", "coin_level_first_pay", "rangeList", "product_discount_range", "filterFirstPayList", "direct_first_pay", "direct_first_pay_rebate", "direct_fixed_discount", "direct_fixed_rebate", "Reflect", "deleteProperty", "objUrl", "$watch", "immediate", "objDiamondUrl", "newValue", "oldValue", "device", "ios", "append", "$delete", "v", "k", "entry", "dindex", "found", "<PERSON><PERSON><PERSON><PERSON>", "scopedSlots", "_u", "fn", "isFixedEventOpen", "proxy", "goodsName", "FixedCouponSwitch", "loadDiamondList", "getRedirectProductList", "loadOtherDiamondList", "sku_name", "setDefaultName", "resetSdkStatus", "loginFail", "style", "display", "defaultName", "no_tax_price", "status", "images", "require", "loadImage", "src", "link", "createElement", "as", "rel", "head", "append<PERSON><PERSON><PERSON>", "onload", "onerror", "loadImages", "async", "shift", "Array", "$TypeError", "TypeError", "passed", "required", "scale", "calc", "outer", "$refs", "offsetWidth", "inner", "expandMode", "level_currency_price", "hideDiscountRow", "discount_amount", "offCountAmount", "taxCost", "extraCost", "finalNowPrice", "showTaxBtn", "finalOriginPrice", "offCountTips", "disable", "taxation", "extra_fee_amount", "watch"], "mappings": "gPAMe,QACbA,QAAS,CACPC,oBAAqBC,GACnB,GAAkB,oBAAPA,EAAmB,OAAOC,QAAQC,MAAM,iCAEnD,IAAIC,QAAQ,CAACC,EAASC,KACpBC,KAAKC,SAASC,OAEd,MAAMC,EAAQC,WAAW,IAAMN,EAAQ,IAAK,MAC5CO,gBAAe,SAAUC,GACvBH,GAASI,aAAaJ,GACtBL,EAAQQ,QAGTE,KAAKF,IACCA,GAAUX,QAAQC,MAAM,sBAC7BF,EAAGY,KAEJG,QAAQ,IAAMT,KAAKC,SAASS,SAEjCC,0BAA2BC,EAAQN,GACjC,MACEO,WAAYC,EACZC,aAAcC,EACdC,eAAgBC,GACdlB,KAAKmB,cAEHC,EAAgBpB,KAAKoB,cACrBC,EAAS,CAGbC,KAAMtB,KAAKuB,IAAI,aACfC,WAAYJ,EAAcI,WAC1BC,QAASL,EAAcM,yBAA2BN,EAAcK,OAEhEE,QAASX,EACTH,WAAYC,EACZG,eAAgBC,EAEhBU,WAAYC,SAASC,OACrBC,aAAczB,GAEhB,GAAkC,MAA9BN,KAAKgC,QAAQC,QAAQC,OACvBb,EAAOO,YAAc,IAAI5B,KAAKgC,QAAQC,QAAQC,KAAKC,QAAQ,MAAO,IAC9DnC,KAAKoC,iBAAiB,CACxB,IAAIC,EAAYR,SAASC,OAASQ,OAAOC,aACrCF,EAAUG,SAAS,OAAMH,EAAYA,EAAUI,MAAM,GAAI,IAC7DpB,EAAOO,WAAaS,EAID,QAAnBrC,KAAK0C,WAA0C,QAAnB1C,KAAK0C,YACnCrB,EAAOsB,SAAWC,KAAKC,UAAU,CAC/BC,aAAc,iBAIlB,MAAM,KAAEC,EAAI,UAAEC,GAAchD,KAAKoB,cACpB,IAAT2B,IAAY1B,EAAO4B,gBAAkBD,GAErCpC,IAAQS,EAAOT,OAAS,GAE5B,IAAIsC,EAAclD,KAAKmD,OAAOC,MAAMC,UAAUC,aAC9C,GAAIJ,EAAa,CACf,GAAIA,EAAYK,SAAS,KAAM,CAC7B,MAAMC,EAAQN,EAAYO,QAAQ,KAClCP,EAAcA,EAAYT,MAAM,EAAGe,GAErCnC,EAAOqC,YAAcR,EAGG,eAAtBS,SAAoCtC,EAAOqC,YAAc,OAAOE,QACpEvC,EAAOwC,WAAaC,aAAaC,QAAQ,gBAAkB,GAG3D,MAAMC,EAAM,IAAIC,IAChB5C,EAAO6C,aAAe,CACpBC,aAAc7B,OAAO8B,WAAa,KAAO,KAAO,SAChDC,OAAQL,EAAIM,QAAQhD,QACjBiD,kBAGL,MAAMC,EAAkBxE,KAAKmD,OAAOsB,QAAQ,4BACtCC,EAAe1E,KAAK0E,aAC1B,OAAQF,EAAgBG,QACtB,IAAK,YAAa,IAAK,mBACrBtD,EAAOuD,SAAW,YAClBvD,EAAOwD,SAAWH,EAAaG,SAC/BxD,EAAOyD,eAAiBJ,EAAaI,eACrC,MAEF,IAAK,mBAAoB,IAAK,0BAC5BzD,EAAOuD,SAAW,mBAClBvD,EAAOwD,SAAWH,EAAaG,SAC/BxD,EAAOyD,eAAiBJ,EAAaI,eAEjCJ,EAAaE,WAAUvD,EAAOuD,SAAWF,EAAaE,UAC1D,MAEF,IAAK,kBACHvD,EAAOuD,SAAW,SAClBvD,EAAOwD,SAAWH,EAAaG,SAC/BxD,EAAOyD,eAAiBJ,EAAaI,eACrCzD,EAAO0D,UAAYL,EAAaK,UAChC,MAEF,IAAK,cACH1D,EAAOuD,SAAW,SAClBvD,EAAOwD,SAAWH,EAAaM,aAC/B3D,EAAOyD,eAAiBJ,EAAajD,MACrCJ,EAAO0D,UAAYL,EAAaK,UAChC,MAEF,IAAK,gBACH1D,EAAOuD,SAAW,SAClBvD,EAAOwD,SAAWH,EAAaG,SAC/BxD,EAAOyD,eAAiBJ,EAAaI,eACrCzD,EAAO0D,UAAYL,EAAaK,UAChC,MAGF,IAAK,wBAAyB,IAAK,wBAAyB,CAC1D,MAAME,EAAsBjF,KAAKmD,OAAOC,MAAM8B,SAASD,oBACvD5D,EAAOuD,SAAW,iBAClBvD,EAAOwD,SAAWI,EAAoBJ,SACtCxD,EAAOyD,eAAiBG,EAAoBH,eAC5C,MAEF,IAAK,eAAgB,IAAK,sBACxBzD,EAAOuD,SAAW,eAClB,MAEF,IAAK,uBACHvD,EAAOuD,SAAW,uBAClB,MAEF,QAAS,CAEP,MAAM7B,EAAO/C,KAAKmD,OAAOC,MAAM+B,eAAeC,kBAC1CrC,IAAS1B,EAAOuD,WAClBvD,EAAOuD,SAAW7B,GAIpB,MAAMsC,EAAqBrF,KAAKmD,OAAOC,MAAM+B,eAAeG,4BAA+BlE,EAAcI,aAAexB,KAAKuF,MAAM,wBAC7HC,EAAcH,GAAsBrF,KAAKmD,OAAOsB,QAAQ,qBAC1De,UAAoBnE,EAAOuD,UAYnC,MAAMzD,EAAgBnB,KAAKmB,cACrBsE,EAAc,GAAGtE,EAAcN,cAAcM,EAAcJ,gBAAgBI,EAAcF,wBAO/F,GANIjB,KAAKmD,OAAOC,MAAMsC,GAAGC,gBAAkC,4BAAhBF,IACzCpE,EAAOuE,WAAa,WAItBC,eAAeC,WAAW,aACtB9F,KAAKoC,gBAAiB,CACxB,MAAMiB,EAAYrD,KAAKmD,OAAOC,MAAMC,UAC9B0C,EAAKnD,KAAKoD,MAAM3C,EAAU0C,IAAM,MAClCvB,EAAgByB,UAAS5E,EAAOuD,SAAWJ,EAAgByB,SAC/D5E,EAAO6E,WAAaH,EAAGG,WACvB7E,EAAOC,KAAOD,EAAO8E,aAAeJ,EAAGK,aACvC/E,EAAOgF,cAAgBhD,EAAUiD,IACjCT,eAAeU,QAAQ,YAAaR,EAAGK,cAEzC,GAAIpG,KAAKwG,mBAAoB,CAC3B,MAAMC,EAAenE,OAAOoE,YACxBD,EAAa1D,OAAM1B,EAAOuD,SAAW6B,EAAa1D,MACjD1B,EAAOC,OACVD,EAAOC,KAAOgB,OAAOqE,mBACrBd,eAAeU,QAAQ,YAAajE,OAAOqE,qBAI/C,MAAMvD,EAAQpD,KAAKmD,OAAOC,MACpBwD,EAAY,CAChBC,OAAQ,GAAG7F,KAAeF,KAAaI,IACvC4F,OAAQ,GACRC,QAAS3D,EAAM2D,QACfC,SAAU5D,EAAM4D,SAChBC,aAAc5F,EAAOG,WACrB0F,MAAO,gBACPC,QAAS9F,EAAOI,OAGlBzB,KAAKC,SAASC,OACdF,KAAKoH,gBAAiB,EAEtB,IAAIvH,QAAQC,IACV,GAAIE,KAAKmD,OAAOsB,QAAQ,qBAAsB,OAAO3E,EAAQuH,eAAehG,IAC5EvB,EAAQwH,eAAgBjG,MAEvBb,KAAK+G,IACJ,MAAM,KAAEC,EAAI,KAAEC,EAAI,QAAEC,GAAYH,EAChC,OAAQE,GACN,KAAK,EACH,IAAI5H,QAAQ,CAACC,EAASC,KAChB,cAAeyH,GACjBxH,KAAK2H,MAAMC,MAAM,UAAW,kBAAmB,CAAEC,KAAML,EAAKM,WAAa,IACzE9H,KAAK2H,MAAMI,MAAM,wBAAyBC,IACpCA,EAAQlI,EAAQ,GACfC,EAAOkI,MAAM,mBAGpBnI,EAAQ,KAGTU,KAAK+G,IAEJ,GADAW,6BAAgB,UAAWV,EAAKW,SAAU,IAAKvB,GACnB,kBAAjBY,EAAKY,QAAsB,CACpC,MAAMC,EAAM,IAAIC,IAAId,EAAKY,SACnB/E,EAAY,IAAIkF,gBAAgBF,EAAIG,QAI1C,GAFA3C,eAAeU,QAAQ,aAAc3D,KAAKC,UAAU2E,IACpD3B,eAAeC,WAAW,cACtB0B,EAAKY,QAAQ7E,SAAS,aAAgD,aAAjCF,EAAUoF,IAAI,eAA+B,CACpF,MAAMC,EAAW,CACfC,QAAStF,EAAUoF,IAAI,SACvBG,SAAUpB,EAAKqB,UACf7B,SAAUQ,EAAKR,SACf8B,gBAAiBtB,EAAKsB,gBACtBhC,OAAQU,EAAK/F,OAGH,IAAR8F,IAAWmB,EAASK,QAAS,GACjClD,eAAeU,QAAQ,WAAY3D,KAAKC,UAAU6F,IAClD1I,KAAKgC,QAAQgH,KAAK,WACb,CACL,GAAqC,aAAjC3F,EAAUoF,IAAI,eAEhB,OADA5G,SAASoH,KAAOzB,EAAKY,QAAQjG,QAAQ,wBAAyB,IAAIA,QAAQ,wBAAyB,IAC5F,KAET,GAAIqF,EAAK0B,qBAAsB,OAAO5G,OAAO6G,KAAK3B,EAAKY,QAAS,UAChEvG,SAASoH,KAAOzB,EAAKY,cAGvBZ,EAAKY,QAAQQ,SAAWpB,EAAKqB,UAC7BrB,EAAKY,QAAQU,gBAAkBtB,EAAKsB,gBACpCtB,EAAKY,QAAQgB,KAAO5B,EAAK6B,aACzB7B,EAAKY,QAAQD,SAAWX,EAAK8B,iBAC7B9B,EAAKY,QAAQmB,aAAe/B,EAAKW,SAEjCtC,eAAeU,QAAQ,UAAWiB,EAAKgC,uBACvC3D,eAAeU,QAAQ,MAAOiB,EAAK6B,cAEvB,IAAR9B,IAAWC,EAAKY,QAAQW,QAAS,GAER,aAAzBvB,EAAKY,QAAQzG,UACf6F,EAAKY,QAAQkB,iBAAmB9B,EAAK8B,iBACrC9B,EAAKY,QAAQ9G,KAAOkG,EAAKlG,MAE3BuE,eAAeU,QAAQ,SAAU3D,KAAKC,UAAU2E,EAAKY,UAExB,aAAzBZ,EAAKY,QAAQzG,QACf3B,KAAKgC,QAAQgH,KAAK,OACTxB,EAAKY,QAAQqB,cACtBzJ,KAAKgC,QAAQgH,KAAK,OACTxB,EAAKY,QAAQsB,eACtB1J,KAAKgC,QAAQgH,KAAK,QACTxB,EAAKY,QAAQuB,qBACtB3J,KAAKgC,QAAQgH,KAAK,OAElBhJ,KAAKgC,QAAQgH,KAAK,SAIvBY,MAAMC,IACLlK,QAAQmK,IAAID,EAAInC,WAEpB,MAEF,KAAK,KAAM,CACT,MAAMqC,EAAc,CAClBC,EAAGhK,KAAKiK,GAAG,eACXC,EAAGlK,KAAKiK,GAAG,kBACXE,EAAGnK,KAAKiK,GAAG,sBAEbjK,KAAKoK,OAAOP,IAAIE,EAAYvC,EAAK6C,eAEjCnC,6BAAgB,SAAU,IAAK6B,EAAYvC,EAAK6C,cAAezD,GAC/D,MAEF,QAEE,MADA5G,KAAKoK,OAAOP,IAAI7J,KAAKiK,GAAG,cAClBhC,MAAMP,MAIjBkC,MAAMC,IACL7J,KAAKoH,gBAAiB,EACtBc,6BAAgB,SAAU,IAAK2B,EAAKjD,GACpCjH,QAAQC,MAAM,sBAAsBiK,EAAInC,WAEzCjH,QAAQ,KACPT,KAAKoH,gBAAiB,EACtBpH,KAAKC,SAASS,UAGpB,oBAAqBE,GACnB,GAAIZ,KAAKoH,gBAAkBpH,KAAKmD,OAAOsB,QAAQ,8BAA+B,OAAO,KACrF,IAAKzE,KAAKsK,QAAS,OAAOtK,KAAK2H,MAAMC,MAAM,uBAE3C,IAAK5H,KAAKmD,OAAOC,MAAMmH,mBAAoB,OAAOvK,KAAK2H,MAAMC,MAAM,UAAW,iBAE9E,GAAItF,OAAOkI,YAAa,CACtB,MAAMxC,QAAe,IAAInI,QAAQ,CAACC,EAASC,KACzC,MAAM0K,EAAU,CACdC,GAAIA,IAAM5K,EAAQ,GAClB6K,GAAIA,IAAM7K,EAAQ,IAEpBE,KAAK2H,MAAMC,MAAM,UAAW,oBAAqB6C,KAEnD,IAAKzC,EAAQ,OAAO,KAEtB,GAA2C,OAAvCpF,KAAKC,UAAU7C,KAAKmB,eAAyB,OAAOnB,KAAKoK,OAAOP,IAAI7J,KAAKiK,GAAG,qCAChF,MAAMW,EAAK5K,KACXA,KAAKP,qBAAoB,SAAUa,GACjCsK,EAAGjK,0BAA0BC,EAAQN,OAGzCuK,YACE,MAAQhK,WAAYC,GAAcd,KAAKmB,cACjCqC,EAAQxD,KAAKmD,OAAOsB,QAAQ,sCAAsChB,QAAQ3C,GAChF,IAAe,IAAX0C,EAAc,CAChB,MAAMsH,EAAM,OAAO9K,KAAKmD,OAAOsB,QAAQ,sCAAsCjB,GAC7E,OAAOxD,KAAK2H,MAAMC,MAAM,UAAW,oBAAqB,CAAEkD,MAAKpL,GAAIM,KAAK+K,gBAG1E/K,KAAK+K,kBAGTC,SAAU,IACLC,eAAS,WAAY,CAAC,gBAAiB,gBAAiB,eAAgB,WACxEA,eAAS,CAAC,OAAQ,WAAY,kBAAmB,0BACjDA,eAAS,WAAY,CAAC,eACtBA,eAAS,WAAY,CAAC,WAAY,QAAS,YAC3CA,eAAS,iBAAkB,CAAC,mBAAoB,UAErDzD,OACE,MAAO,CACLJ,gBAAgB,IAGpB8D,UACElL,KAAK2H,MAAMwD,IAAI,iBAAkB,IAAMnL,KAAK+K,cAAc,IAE1DlJ,SAAS2G,QAAU4C,QAAQC,UAAU,GAAI,GAAIxJ,SAASyJ,a,oCC1W1D,IAAIC,EAAS,WAAkB,IAAIC,EAAIxL,KAAKyL,EAAGD,EAAIE,MAAMD,GAAG,OAAOA,EAAG,cAAc,CAACE,MAAM,CAACH,EAAIrI,OAAOC,MAAMwI,SAASC,SAAU,uBAAwBL,EAAI9I,UAAW,CAAEoJ,IAAKN,EAAIrI,OAAOC,MAAMhB,kBAAmB2J,MAAM,CAAC,aAAaP,EAAIvB,GAAG,iBAAiB,GAAK,yBAAyB,CAACwB,EAAG,MAAM,CAACO,YAAY,gBAAgB,CAAER,EAAIS,eAAeC,OAAQV,EAAIW,GAAIX,EAAIS,gBAAgB,SAAStK,EAAQ6B,GAAO,OAAOiI,EAAG,MAAM,CAACX,IAAInJ,EAAQyK,cAAcJ,YAAY,cAAcL,MAAM,CAAC,CAAC,yBAA0BhK,EAAQyK,gBAAkBZ,EAAIrK,cAAciL,gBAAgBC,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOd,EAAIe,aAAa/I,MAAU,CAACiI,EAAG,MAAM,CAACe,WAAW,CAAC,CAAClL,KAAK,OAAOmL,QAAQ,yBAAyBC,MAAO/K,EAAQgL,SAAUC,WAAW,mBAAmBC,IAAI,oBAAoBb,YAAY,yBAA0BrK,EAAQmL,WAAatB,EAAIuB,oBAAoBpL,GAAU8J,EAAG,MAAM,CAACE,MAAM,CAAC,iBAAkB,uBAAuB,CAACF,EAAG,OAAO,CAACO,YAAY,UAAWR,EAAIuB,oBAAoBpL,GAAU8J,EAAG,OAAO,CAACO,YAAY,qBAAqB,CAACR,EAAIwB,GAAG,SAASxB,EAAIyB,GAAGzB,EAAI0B,IAAIC,cAA2C,IAA3B3B,EAAIpK,cAAc2B,KAAayI,EAAIpK,cAAcgM,aAAe5B,EAAIpK,cAAciM,OAAO,KAAK5B,EAAG,OAAOA,EAAG,OAAO,CAACO,YAAY,OAAO,CAACR,EAAIwB,GAAGxB,EAAIyB,GAAGzB,EAAIvB,GAAG,uBAAuBuB,EAAI8B,UAAS7B,EAAG,MAAM,CAACO,YAAY,SAAS,CAACR,EAAIwB,GAAGxB,EAAIyB,GAAGzB,EAAIvB,GAAG,oBAAoB,MAEv0CsD,EAAkB,G,gFCwCP,GACfjM,KAAA,gBACAkM,WAAA,CAAAC,mBACAC,MAAA,CACAC,SAAA,CACA5K,KAAA6K,OACAC,iBAGArG,OACA,OACAsG,YAAA,GAEAC,aAAAC,EACAC,cAAA,IAGAjD,SAAA,IACAC,eAAA,4CACAA,eAAA,6FACA8B,sBACA,OAAAmB,GACA,KAAAC,QACA,KAAAjB,IAAAkB,qBAAA7K,SAAA2K,EAAAjN,iBACA,KAAAiM,IAAAC,cACA,KAAAkB,iBACA,KAAA3J,aAAA4J,UAEArC,iBACA,MAAAsC,EAAA,KAAApL,OAAAsB,QAAA,kCACA,OAAA8J,EAAArC,QACAqC,EAAAhL,SAAA,KAAApC,cAAAN,aAAA,KAAAsC,OAAAqL,OAAA,yBACA,KAAAV,YAAAW,OAAAC,IAAAH,EAAAhL,SAAAmL,EAAA7N,cAEA,KAAAiN,cAGAtO,QAAA,CACAmP,kBACA,MAAAC,EAAA,CACA5H,SAAA,KAAA3D,UAAAwL,GACApN,OAAA,KAAAL,cAAAK,MACAD,WAAA,KAAAJ,cAAAI,YAGAkD,EAAA,KAAAvB,OAAAC,MAAA8B,SAAAR,aAMA,GALAA,EAAA4J,WACA,oBAAA5J,EAAAC,SAAAiK,EAAAnN,MAAAiD,EAAAI,gBACA,gBAAAJ,EAAAC,SAAAiK,EAAAnN,MAAAiD,EAAAjD,OACA,cAAAiD,EAAAC,SAAAiK,EAAAnN,MAAAiD,EAAAI,iBAEA,KAAA3B,OAAAsB,QAAA,uCACA,MAAAqK,EAAA,8JACA,IAAAC,EAAA,IAAAH,EAAAnN,MACAsN,EAAAD,EAAAvL,SAAA,KAAAnC,cAAA4F,UAAAgI,KAAAC,KAAAF,GACAA,EAAAG,QAAA,GACAN,EAAAnN,OAAAsN,EAIA,WAAAhM,EAAA,SAAAoM,GAAA,KAAA/N,cACA,IAAA2B,IAAA6L,EAAAnN,OAAA0N,GAEA,KAAAlP,SAAAC,OAEA,KAAAiD,OAAAqL,OAAA,yBAEAY,eAAAR,GACApO,KAAA,EAAAgH,OAAAC,OAAAC,cACA,IAAAD,GACA,KAAAqG,YAAA,KAAAuB,eAAA7H,GACApH,WAAA,KACA,kBAAAkP,GAAA,KAAAnM,OAAAC,MAAAmM,SACA,GAAAD,EAAA,CACA,MAAAE,EAAA,KAAAvD,eAAAwC,OAAAC,KAAA7N,aAAAyO,EAAAzO,YAAAyO,EAAArO,iBAAAyN,EAAAzN,gBACAuO,KAAAtD,QAAA,KAAA/I,OAAAqL,OAAA,4BAAAgB,EAAA,IAEA,MAAAC,EAAA,KAAAxD,eAAAwC,OAAAC,KAAA3N,eAAAuO,EAAAvO,cACA,GAAA0O,KAAAvD,OAAA,YAAA/I,OAAAqL,OAAA,4BAAAiB,EAAA,MAEA,GACA,KAAAlK,MAAA,+BAAAjD,OAAAoN,aAAA,KAAAvM,OAAAC,MAAAuM,OACArN,OAAAoN,YAAA,EACA,KAAAE,UAAA,KACAC,WAAAC,KAAA,iBAAAC,OAAA,EAAAC,SAAA,GAAAC,WAAA,eAKA,KAAA7F,OAAAP,IAAA,KAAAI,GAAA,wBAGAxJ,QAAA,SAAAR,SAAAS,SAEA2O,eAAAa,GACA,QAAA/M,OAAAC,MAAAwI,SAAAuE,KAAA,CACA,IAAAC,EAEAA,EADAC,OACA,2BACA/N,OAAAgO,SACA,0BAEA,6BAEAJ,IAAAzB,OAAAC,GACA,UAAAA,EAAA7N,YAAA,WAAA6N,EAAA7N,YACAuP,EAAA3M,QAAAiL,EAAA3N,aAAA2N,EAAAzN,iBAAA,GAIA,OAAAiP,EAAAK,IAAA7B,IACAA,EAAAtC,cAAA,GAAAsC,EAAA7N,eAAA6N,EAAA3N,eACA2N,KAGAnC,aAAA/I,GACA,KAAAyK,cAAA,EAEA,MAAAuC,EAAA,KAAAvE,eAAAzI,GACAiN,EAAA,KAAAtP,cACA,GAAAqP,EAAApE,gBAAAqE,EAAArE,cAAA,YAEA,KAAAjJ,OAAAqL,OAAA,iCAAAvC,eAAAzI,KAEAkN,kBACAC,iBACAnQ,KAAA+G,IACA,WAAAE,EAAA,KAAAD,GAAAD,EACA,OAAAE,EAAA,CAIA,GAHA,KAAAtE,OAAAqL,OAAA,+BAAAhH,GAGA,KAAAyG,aAAA,YACA,kBAAAqB,GAAA,KAAAnM,OAAAC,MAAAmM,SACA,GAAAD,EAAA,CACA,MAAAE,EAAA,KAAAvD,eAAAwC,OAAAC,KAAA7N,aAAAyO,EAAAzO,YAAAyO,EAAArO,iBAAAyN,EAAAzN,gBACA,GAAAuO,KAAAtD,OAAA,YAAA/I,OAAAqL,OAAA,4BAAAgB,EAAA,UAMAtE,UAIA,KAAAvD,MAAAwD,IAAA,wBAAAwD,mBAEA,KAAAhH,MAAAwD,IAAA,2BAAAwD,mBAEA,KAAAhH,MAAAwD,IAAA,WAAA/H,IAEA,IAAAA,GAAA,KAAAsN,qBAGAE,gBACA,KAAA7C,SAAA,KAAAA,YCtMsV,I,wBCQlV8C,EAAY,eACd,EACAtF,EACAgC,GACA,EACA,KACA,WACA,MAIa,OAAAsD,E,6CClBf,IAAIC,EAAgB,EAAQ,QACxBC,EAAc,EAAQ,QACtBC,EAAW,EAAQ,QACnBC,EAA0B,EAAQ,QAElCC,EAAmB3I,gBACnB4I,EAA2BD,EAAiBE,UAC5CC,EAASN,EAAYI,EAAyBE,QAC9CC,EAAOP,EAAYI,EAAyBI,KAC5ClQ,EAAS,IAAI6P,EAAiB,QAI9B7P,EAAOkQ,IAAI,IAAK,IAAOlQ,EAAOkQ,IAAI,SAAKvD,IACzC8C,EAAcK,EAA0B,OAAO,SAAa7P,GAC1D,IAAI4K,EAASsF,UAAUtF,OACnBuF,EAASvF,EAAS,OAAI8B,EAAYwD,UAAU,GAChD,GAAItF,QAAqB8B,IAAXyD,EAAsB,OAAOH,EAAKtR,KAAMsB,GACtD,IAAIoQ,EAASL,EAAOrR,KAAMsB,GAC1B2P,EAAwB/E,EAAQ,GAChC,IAAIQ,EAAQsE,EAASS,GACjBjO,EAAQ,EACZ,MAAOA,EAAQkO,EAAOxF,OACpB,GAAIwF,EAAOlO,OAAakJ,EAAO,OAAO,EACtC,OAAO,IACR,CAAEiF,YAAY,EAAMC,QAAQ,K,2DC1BjC,IAAIrG,EAAS,WAAkB,IAAIC,EAAIxL,KAAKyL,EAAGD,EAAIE,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACO,YAAY,sBAAsBL,MAAM,CAACH,EAAI9I,UAAW,CAAEoJ,IAAKN,EAAIrI,OAAOC,MAAMhB,mBAAoB,CAACqJ,EAAG,MAAM,CAACO,YAAY,SAAS,CAACR,EAAIqG,GAAG,SAAQ,WAAW,MAAO,CAACrG,EAAIwB,GAAGxB,EAAIyB,GAAGzB,EAAIsG,iBAAgB,GAAGrG,EAAG,MAAM,CAACO,YAAY,QAAQ,CAACR,EAAIqG,GAAG,YAAY,MAEvUtE,EAAkB,GCUP,GACfjM,KAAA,aACAoM,MAAA,CACAoE,UAAA,CACA/O,KAAAgP,OACAlE,QAAA,MCjBkW,I,wBCQ9VgD,EAAY,eACd,EACAtF,EACAgC,GACA,EACA,KACA,WACA,MAIa,OAAAsD,E,sECnBf,W,2DCAA,W,kCCCA,IAAImB,EAAc,EAAQ,QACtBjB,EAAc,EAAQ,QACtBkB,EAAwB,EAAQ,QAEhCd,EAA2B5I,gBAAgB6I,UAC3Cc,EAAUnB,EAAYI,EAAyBe,SAI/CF,KAAiB,SAAUb,IAC7Bc,EAAsBd,EAA0B,OAAQ,CACtD1I,IAAK,WACH,IAAI0J,EAAQ,EAEZ,OADAD,EAAQlS,MAAM,WAAcmS,OACrBA,GAETC,cAAc,EACdT,YAAY,K,oCClBhB,W,2DCAA,IAAIpG,EAAS,WAAkB,IAAIC,EAAIxL,KAAKyL,EAAGD,EAAIE,MAAMD,GAAG,OAAOA,EAAG,cAAc,CAACE,MAAM,CAAC,UAAWH,EAAI9I,WAAYqJ,MAAM,CAAC,GAAK,qBAAqB,aAAaP,EAAI+D,SAASjF,QAAS,GAAGkB,EAAIjK,IAAI,sBAAsB,CAAEiK,EAAI+D,SAASjF,QAASmB,EAAG,MAAM,CAACO,YAAY,uBAAuB,CAACP,EAAG,MAAM,CAACe,WAAW,CAAC,CAAClL,KAAK,OAAOmL,QAAQ,yBAAyBC,MAAOlB,EAAI+D,SAAS8C,KAAMzF,WAAW,gBAAgBC,IAAI,oBAAoBb,YAAY,gBAAgBP,EAAG,MAAM,CAACO,YAAY,aAAa,CAACP,EAAG,MAAM,CAACO,YAAY,SAAS,CAACP,EAAG,MAAM,CAACO,YAAY,QAAQ,CAACR,EAAIwB,GAAGxB,EAAIyB,GAAGzB,EAAI+D,SAASjO,SAAWkK,EAAIrI,OAAOC,MAAMkP,QAA8J9G,EAAI8B,KAAzJ7B,EAAG,MAAM,CAACO,YAAY,mBAAmBK,GAAG,CAAC,MAAQb,EAAI+G,SAAS,CAAoB,QAAlB/G,EAAI9I,UAAqB,CAAC8I,EAAIwB,GAAGxB,EAAIyB,GAAGzB,EAAIvB,GAAG,qBAAqBuB,EAAI8B,MAAM,KAAe9B,EAAIgH,OAAShH,EAAI0B,IAAIiB,OAAQ1C,EAAG,MAAM,CAACO,YAAY,WAAW,CAACP,EAAG,MAAM,CAACO,YAAY,SAAS,CAACR,EAAIwB,GAAG,OAAOxB,EAAIyB,GAAGzB,EAAI0B,IAAIuF,UAAUhH,EAAG,MAAM,CAACO,YAAY,YAAY,CAACP,EAAG,IAAI,CAACM,MAAM,CAAC,KAAOP,EAAIkH,oBAAoB,OAAS,WAAW,CAAClH,EAAIwB,GAAGxB,EAAIyB,GAAGzB,EAAIvB,GAAG,uBAAuBuB,EAAI8B,KAAK7B,EAAG,MAAM,CAACO,YAAY,SAAS,CAACP,EAAG,OAAO,CAACO,YAAY,SAAS,CAACR,EAAIwB,GAAGxB,EAAIyB,GAAGzB,EAAIvB,GAAG,aAAa,CAAC0I,EAAGnH,EAAI+D,SAASkD,YAAYhH,EAAG,OAAO,CAACO,YAAY,UAAU,CAACR,EAAIwB,GAAGxB,EAAIyB,GAAGzB,EAAIvB,GAAG,cAAc,CAAC0I,EAAGnH,EAAI+D,SAASqD,mBAAmB,CAACnH,EAAG,MAAM,CAACO,YAAY,0BAA0BL,MAAM,CAAC,CAAC,UAAaH,EAAIqH,kBAAkB,CAACpH,EAAG,MAAM,CAACO,YAAY,uBAAuB,CAACP,EAAG,QAAQ,CAACe,WAAW,CAAC,CAAClL,KAAK,QAAQmL,QAAQ,UAAUC,MAAOlB,EAAIsH,IAAKlG,WAAW,QAAQb,MAAM,CAAC,KAAO,SAAS,GAAK,WAAW,YAAcP,EAAIqH,eAAiB,GAAKrH,EAAIjK,IAAI,qBAAqBwR,SAAS,CAAC,MAASvH,EAAIsH,KAAMzG,GAAG,CAAC,MAAQ,SAASC,GAAWA,EAAO0G,OAAOC,YAAiBzH,EAAIsH,IAAIxG,EAAO0G,OAAOtG,WAAUjB,EAAG,IAAI,CAACY,GAAG,CAAC,MAAQ,SAASC,GAAQA,EAAO4G,kBAAkB1H,EAAI2H,eAAgB,GAAM,WAAa,SAAS7G,GAAQA,EAAO4G,kBAAkB1H,EAAI2H,eAAgB,MAAS1H,EAAG,MAAM,CAACO,YAAY,WAAWL,MAAM,CAAEyH,OAAQ5H,EAAI2H,eAAgBpH,MAAM,CAAC,IAAMP,EAAI6H,aAAa,WAAW,IAAM,IAAIhH,GAAG,CAAC,MAAQ,SAASC,GAAQA,EAAO4G,wBAAyBzH,EAAG,MAAM,CAACE,MAAM,CAAC,YAAa,YAAa,MAAM,CAAE,aAAgBH,EAAIsH,KAAM,CAAC,SAAYtH,EAAIsH,MAAMzG,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOd,EAAI8H,WAAW,CAAC9H,EAAIwB,GAAGxB,EAAIyB,GAAGzB,EAAIvB,GAAG,aAAcuB,EAAIgH,MAAO/G,EAAG,MAAM,CAACO,YAAY,oBAAoB,CAACP,EAAG,IAAI,CAACM,MAAM,CAAC,KAAOP,EAAIkH,oBAAoB,OAAS,WAAW,CAAClH,EAAIwB,GAAGxB,EAAIyB,GAAGzB,EAAIvB,GAAG,qBAAqBuB,EAAI8B,OAAQ9B,EAAIqH,eAAgBpH,EAAG,MAAM,CAACO,YAAY,wBAAwB,CAACR,EAAIwB,GAAGxB,EAAIyB,GAAGzB,EAAIjK,IAAI,wBAAwBiK,EAAI8B,KAAO9B,EAAIqH,eAAmGrH,EAAI8B,KAAvF7B,EAAG,MAAM,CAACO,YAAY,mBAAmB,CAACR,EAAIwB,GAAGxB,EAAIyB,GAAGzB,EAAIvB,GAAG,sBAA+B,IAEhqFsD,EAAkB,G,6FCAtB,MAAMgG,EAAUC,IAASC,IAAIC,KAAK1N,MAAM,4BAElC2N,EAAQJ,EAAQK,QAIf,SAASC,EAAaC,GACzB,OAAOC,mBAAmBP,IAASQ,IAAIC,QAAQT,IAASC,IAAIC,KAAK1N,MAAM8N,GAAUP,EAAS,CACtFW,GAAIP,EACJQ,KAAMX,IAASW,KAAKC,IACpBC,QAASb,IAASc,IAAIC,QACvBvD,YARP2C,EAAMa,SAAW,GACjBb,EAAMc,MAAMC,OAAO,GC+CJ,OACfpT,KAAA,cACAkM,WAAA,CAAAC,mBACAjG,OACA,OACAsL,IAAA,GACAK,eAAA,EAEAN,gBAAA,IAGA7H,SAAA,IACAC,eAAA,iBACAA,eAAA,0FACAA,eAAA,yCACAA,eAAA,aACAA,eAAA,oBACAyH,sBACA,OAAAiC,i8BAAA,oCAAAjS,WAAA,WAAAkS,MAAAC,SAGArV,QAAA,CAEA,YAAAsV,GACA,SAAAhC,MAAAgC,EAAA,OACA,MAAA1R,EAAA,KAAAD,OAAAC,MACA/B,EAAA,CACA0T,cAAA,EACAC,aAAA5R,EAAA6R,aAEAH,GAAA,kBAAAA,IAAAzT,EAAAyT,UACA,KAAAhC,MACAhP,aAAAgC,WAAA,UACAzE,EAAAyR,KAAA,KAAAA,IAEA,aAAA3P,OAAAC,MAAAwI,SAAAC,WACAxK,EAAA6T,OAAArB,EAAA,QAAAf,OAAA9D,KAAAmG,MAAAC,KAAAC,MAAA,QACAhU,EAAAiU,QAAAzB,EAAA,GAAA7E,KAAAC,KAAAmG,KAAAC,MAAA,aAAAvC,SAIA,KAAAA,KAAAyC,8BAAA,KAAAzC,KAEA,IACA,KAAA7S,SAAAC,OACA,SAAAsH,EAAA,QAAAC,SAAA+N,eAAAnU,GAEA,OADA,KAAApB,SAAAS,OACA+G,GACA,QACA,IACA,MAAAgO,EAAA,KAAAlQ,MAAA,iBACA,kBAAAiC,MAAA5E,KAAAoD,MAAA0P,eAAAlO,EAAAiO,KACA,MAAAE,GACAhW,QAAAC,MAAA,aAAAkT,KAGA,MAAA8C,EAAA,KAAAA,cAAA,GACA,GAAAA,EAAA1J,SAAA0J,EAAArS,SAAAiE,EAAAqO,aAEA,OADA,KAAA/C,IAAA,GACA,KAAA1I,OAAAP,IAAA,KAAAI,GAAA,kBAIA,MAAA6L,EAAA,KAAAA,aAAA,IACAD,YAAAE,EAAA,OAAAjB,GAAAtN,EACA,GAAAsO,EAAA5J,OACA,UAAA8J,KAAAF,EAAA,CACA,cAAAnU,EAAA,GAAAsU,GAAAD,EACA,GAAArU,EAAA4B,SAAAwS,GAGA,OAFAjS,aAAAgC,WAAA,eACAxD,OAAAT,SAAAoH,KAAA,GAAAgN,YAAAlC,mBAAAe,MAMA,MAAAoB,EAAA,KAAAA,cAAA,GACA,GAAAA,EAAAhK,QAAAgK,EAAA3S,SAAAwS,GAAA,YAAA3L,OAAAP,IAAA,KAAAI,GAAA,kBAUA,MAAAkM,EAAA,KAAAhT,OAAAC,MAAAhB,iBAAA,KAAAe,OAAAC,MAAAkP,QACA,QAAA8D,kBAAAD,EAAA,CACA,MAAAE,QAAA,KAAAC,aAAA9O,GACA,IAAA6O,EAAA,YAEA,KAAAlT,OAAAqL,OAAA,uBAAAhH,IAEA,KAAAgL,OAAA,KAAA+D,gBAAA,KAAAC,gBACA,KAAAC,WAGA,KAAAC,iBAEAC,kCAEA,KAAAhP,MAAAC,MAAA,gBACA,KAAAD,MAAAC,MAAA,cACA,KAAAkL,IAAA,GACA,KAAAvN,MAAA,8BACAnF,WACA,IAAAyP,WAAAC,KAAA,wBAAA8G,QAAA,EAAAC,UAAA,EAAA7G,SAAA,KACA,GAGA,MAEA,OACA,KAAA8G,MAAA5W,KAAA,KAAA+J,GAAA,qBACA,MAEA,wCACA,MAAA8M,EAAA,CACA,0BACA,oBACA,wBACA,4BAEA,KAAA3M,OAAAP,IAAA,KAAAI,GAAA8M,EAAAtP,KACA,OAAAA,GAAApG,EAAAyR,KAAA,KAAAP,SAEA,MAEA,UACA,KAAAnI,OAAAP,IAAA,KAAAI,GAAA,uBACA7J,WAAA,SAAA+C,OAAAqL,OAAA,wBACA,MAEA,QACA,KAAApE,OAAAP,IAAA,KAAAI,GAAA,wBAGA,MAAArK,GACAA,EAAA8H,QAAAnE,SAAA,oBAAAtD,SAAAS,SAGA6R,SACA,KAAApP,OAAAqL,OAAA,oBAEAiI,WACA,GAAAnU,OAAAT,SAAAyJ,SAAA/H,SAAA,mBACA,MAAAyT,EAAA1U,OAAAwB,aAAAC,QAAA,oBACAiT,IACA1U,OAAAwB,aAAAyC,QAAA,2BACA,KAAAoB,MAAAC,MAAA,0BAGA8O,iBACA,MAAAO,EAAA,KAAAtP,MAAAC,MAAAsP,KAAA,KAAAvP,OAEA,KAAA1H,SAAAC,OACAiX,iBACA3W,KAAA+G,IACA,WAAAE,EAAA,KAAAD,GAAAD,EACA,IAAAE,IACA,KAAAtE,OAAAqL,OAAA,mBAAA0B,KAAA1I,EAAA4P,eAAAH,SACA,KAAA9T,OAAAqL,OAAA,uBAAAhH,EAAA6P,iCACA,KAAAlU,OAAAqL,OAAA,+BAAAhH,EAAA8P,sBACA,KAAAnU,OAAAqL,OAAA,wBAAAhH,EAAA+P,sBACA,KAAApU,OAAAqL,OAAA,oCAAAhH,GACA,KAAAG,MAAAC,MAAA,uBAAAJ,EAAAgQ,uBAGA/W,QAAA,KACA,KAAAR,SAAAS,UAGA,mBAAA6O,GACA,IAAAjP,EAAA,GAKA,OAJAD,eAAAoX,IACAnX,EAAAmX,IAGA,IAAA5X,QAAA,CAAAC,EAAAC,KACA,KAAAE,SAAAC,OACAwX,eAAA,CAAA3V,aAAAzB,EAAAwU,OAAAvF,EAAAuF,SACAtU,KAAA+G,IACA,SAAAE,EAAA,KAAAD,GAAAD,EASA,OAPAC,MAAA,IAKAA,EAAAmQ,SAAApI,EAAAjO,KACAkG,EAAAsN,OAAAvF,EAAAuF,OACArN,GACA,iBACAD,EAAAoQ,UAAA,IAAA9X,GAAA,GACA0H,EAAAqQ,OAAA,IAAA/X,GAAA,GACA,KAAA6H,MAAAC,MAAA,iBAAAJ,GACA,MAEA,8BACA1H,GAAA,GACA,MAOA,QACAA,GAAA,GACA,KAAAsK,OAAAP,IAAA,KAAAI,GAAA,mCAIAxJ,QAAA,SAAAR,SAAAS,WAKAoX,iBACA,QAAA3U,OAAAC,MAAAhB,gBAAA,OACA,MAAA2V,EAAAC,OAAAC,gBAAA,oBACAC,EAAA,QAAA9C,MAAA+C,WAAA,UAAA/C,MAAAgD,YAEAF,IAAAH,IACA,KAAApQ,MAAAC,MAAA,4BACAoQ,OAAAK,gBAAA,mBAAAH,KAGA,sBACA,QAAA/U,OAAAC,MAAAoD,mBAAA,OACA,MAAAnF,EAAA,CAAAiX,GAAA,MAAAC,GAAA,GAAAC,GAAA,MACA,KAAAvY,SAAAC,OACA,IACA,WAAAuH,EAAA,KAAAD,SAAAiR,eAAApX,GAEA,IAAAoG,GAAA,KAAAtE,OAAAqL,OAAA,uBAAAhH,GACA,MAAA5H,IACA,KAAAK,SAAAS,OAGA,KAAAwM,IAAAwL,WAAA,KAAAZ,iBAEA,IAAAa,GAAA,EACA,KAAAhR,MAAAwD,IAAA,8BACAwN,GAAA,KAAAvO,OAAAwO,QAAA,KAAA3O,GAAA,oCACA0O,GAAA,IAEA,KAAAhR,MAAAwD,IAAA,qBAAAwN,GAAA,KAGAzN,UACA,MAAA4J,EAAAhR,aAAAC,QAAA,UACA+Q,EAAA,KAAAxB,MAAAwB,IAEA,KAAAnN,MAAAC,MAAA,cACA,KAAA4K,OAAA,KAAAsF,iBACA,KAAArB,YAEA,KAAA9O,MAAAwD,IAAA,iBACA,KAAAgI,eAAA,IAGA,KAAAxL,MAAAwD,IAAA,2BACA,KAAA0H,gBAAA,EACA,MAAAgG,EAAAC,SAAAC,cAAA,aAEA3Y,WAAA,KACAyY,EAAAG,SACA,KAEAH,EAAAI,iBAAA,aACA,KAAApG,gBAAA,QCrUoV,I,wBCQhVhC,EAAY,eACd,EACAtF,EACAgC,GACA,EACA,KACA,WACA,MAIa,OAAAsD,E,6CCnBf,W,kCCAA,W,uBCAAqI,EAAOC,QAAU,IAA0B,yD,2DCA3C,W,oCCAA,IAAI5N,EAAS,WAAkB,IAAIC,EAAIxL,KAAKyL,EAAGD,EAAIE,MAAMD,GAAG,OAAOA,EAAG,cAAc,CAACE,MAAM,CAAC,qBAAsBH,EAAI9I,WAAWqJ,MAAM,CAAC,aAAaP,EAAIvB,GAAG,UAAU,GAAK,uBAAuB,CAAEuB,EAAIrI,OAAOC,MAAMmM,SAASjF,QAASmB,EAAG,MAAM,CAAC2N,YAAY,CAAC,QAAU,gBAAgB/M,GAAG,CAAC,MAAQ,SAASC,GAAQd,EAAImC,SAAS0L,SAAU,KAAQ,CAAG7N,EAAImC,SAASU,eAAwS,CAA6B,IAA3B7C,EAAI8N,mBAA0B7N,EAAG,MAAM,CAACO,YAAY,8CAA8C,CAACR,EAAIwB,GAAG,IAAIxB,EAAIyB,GAAGzB,EAAIvB,GAAG,4BAA4B,OAAOuB,EAAI8B,KAAM9B,EAAI8N,mBAAqB,IAAM9N,EAAI9G,aAAa4J,SAAU7C,EAAG,MAAM,CAACO,YAAY,8CAA8C,CAACP,EAAG,OAAO,CAACD,EAAIwB,GAAGxB,EAAIyB,GAAGzB,EAAIvB,GAAG,cAAe,CAAC0I,EAAGnH,EAAI8N,yBAAyB9N,EAAIwB,GAAG,KAAKvB,EAAG,OAAOD,EAAI8B,KAAM9B,EAAI8N,mBAAqB,GAAK9N,EAAI9G,aAAa4J,SAAU7C,EAAG,MAAM,CAACO,YAAY,2CAA2C,CAACP,EAAG,MAAM,CAACO,YAAY,QAAQ,CAACP,EAAG,OAAO,CAACD,EAAIwB,GAAGxB,EAAIyB,GAAGzB,EAAIvB,GAAG,kCAAkCwB,EAAG,MAAM,CAACO,YAAY,SAAS,CAACP,EAAG,kBAAkB,CAACX,IAAIU,EAAI9G,aAAa4J,UAAU,CAAC7C,EAAG,OAAO,CAA4B,oBAA1BD,EAAI9G,aAAaC,OAA4B,CAAC6G,EAAIwB,GAAGxB,EAAIyB,GAAGzB,EAAIvB,GAAG,kBAAmB,CAAE0I,EAAGnH,EAAI9G,aAAa6U,yBAA0B/N,EAAI8B,KAAgC,gBAA1B9B,EAAI9G,aAAaC,OAAwB,CAAC8G,EAAG,OAAO,CAACE,MAAM,CAAC,aAAcH,EAAIgO,WAAW,CAAChO,EAAIwB,GAAGxB,EAAIyB,GAAGzB,EAAI9G,aAAaM,cAAc,IAAIwG,EAAIyB,GAAGzB,EAAIiO,iBAAiBjO,EAAIwB,GAAG,SAASxB,EAAI8B,KAAgC,kBAA1B9B,EAAI9G,aAAaC,OAA0B,CAAC8G,EAAG,OAAO,CAACD,EAAIwB,GAAGxB,EAAIyB,GAAGzB,EAAIvB,GAAG,eAAe,OAAOuB,EAAIwB,GAAGxB,EAAIyB,GAAGzB,EAAI9G,aAAagV,OAAOjO,EAAG,IAAI,CAACO,YAAY,kBAAkBR,EAAI8B,MAAM,KAAK7B,EAAG,MAAM,KAAKD,EAAI8B,MAAhhD7B,EAAG,MAAM,CAACO,YAAY,gDAAgD,CAACP,EAAG,MAAM,CAACO,YAAY,QAAQ,CAA6B,IAA3BR,EAAI8N,mBAA0B7N,EAAG,OAAO,CAACD,EAAIwB,GAAGxB,EAAIyB,GAAGzB,EAAIvB,GAAG,+BAA+BwB,EAAG,OAAO,CAACD,EAAIwB,GAAGxB,EAAIyB,GAAGzB,EAAIvB,GAAG,cAAe,CAAC0I,EAAGnH,EAAI8N,8BAA4xC,GAAG7N,EAAG,MAAM,CAACO,YAAY,6CAA6CK,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOd,EAAI7D,MAAMC,MAAM,0BAA0B,CAAC4D,EAAIwB,GAAGxB,EAAIyB,GAAGzB,EAAIvB,GAAG,gCAAiCuB,EAAImC,SAAS0L,QAAS5N,EAAG,oBAAoB,CAACM,MAAM,CAAC,oBAAoBP,EAAImC,SAASgM,gBAAgB,oBAAoBnO,EAAImC,SAASiM,gBAAgB,aAAapO,EAAImC,SAASkM,YAAY,uBAAuBrO,EAAImC,SAASU,gBAAgBhC,GAAG,CAAC,MAAQb,EAAIsO,kBAAkBtO,EAAI8B,MAAM,IAE/2EC,EAAkB,G,kGCFlBhC,EAAS,WAAkB,IAAIC,EAAIxL,KAAKyL,EAAGD,EAAIE,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,MAAM,CAAC,wBAAyBH,EAAI9I,WAAWqJ,MAAM,CAAC,GAAK,0BAA0B,CAACN,EAAG,MAAM,CAACO,YAAY,YAAY,CAACP,EAAG,MAAM,CAACO,YAAY,YAAYK,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOd,EAAI5D,MAAM,aAAa6D,EAAG,MAAM,CAACO,YAAY,aAAa,CAACP,EAAG,KAAK,CAACD,EAAIwB,GAAGxB,EAAIyB,GAAGzB,EAAIvB,GAAG,cAAcwB,EAAG,OAAO,CAACD,EAAIwB,GAAGxB,EAAIyB,GAAGzB,EAAIvB,GAAG,6BAA6BwB,EAAG,MAAM,CAACO,YAAY,YAAYP,EAAG,MAAM,CAACO,YAAY,mBAAmB,CAACP,EAAG,MAAM,CAACE,MAAM,CAAC,MAAM,CAAC,aAA6B,IAAfH,EAAIuO,WAAe1N,GAAG,CAAC,MAAQ,SAASC,GAAQd,EAAIuO,SAAS,KAAK,CAACvO,EAAIwB,GAAGxB,EAAIyB,GAAGzB,EAAIvB,GAAG,qBAAqBwB,EAAG,MAAM,CAACE,MAAM,CAAC,MAAM,CAAC,aAA6B,IAAfH,EAAIuO,WAAe1N,GAAG,CAAC,MAAQ,SAASC,GAAQd,EAAIuO,SAAS,KAAK,CAACvO,EAAIwB,GAAGxB,EAAIyB,GAAGzB,EAAIvB,GAAG,0BAA0BwB,EAAG,MAAM,CAACO,YAAY,kBAAkB,CAAiB,IAAfR,EAAIuO,SAAc,CAACtO,EAAG,qBAAqB,CAACX,IAAI,EAAEiB,MAAM,CAAC,cAAcP,EAAImO,gBAAgB,uBAAuBnO,EAAIwO,kBAAkB,OAAQ,EAAK,qBAAqBxO,EAAIyO,kBAAkB5N,GAAG,CAAC,0BAA0B,SAASC,GAAQd,EAAIyO,iBAAiB3N,GAAQ,4BAA4B,SAASA,GAAQd,EAAIyO,iBAAiB3N,MAAWb,EAAG,MAAM,CAACE,MAAM,CAAC,cAAe,YAAa,CAAC,4BAA6BH,EAAIwO,oBAAqB3N,GAAG,CAAC,MAAQb,EAAI0O,eAAe,CAAC1O,EAAIwB,GAAGxB,EAAIyB,GAAGzB,EAAIvB,GAAG,mBAAmBwB,EAAG,qBAAqB,CAACX,IAAI,EAAEiB,MAAM,CAAC,cAAcP,EAAIoO,gBAAgB,uBAAuBpO,EAAIwO,kBAAkB,OAAQ,MAAU,GAAGvO,EAAG,IAAI,CAACO,YAAY,sBAAsB,CAACR,EAAIwB,GAAG,KAAKxB,EAAIyB,GAAGzB,EAAIvB,GAAG,0BAA0B,KAAmC,OAA7BuB,EAAIrI,OAAOC,MAAM2D,QAAkB,CAACyE,EAAIwB,GAAG,uCAAuCxB,EAAI8B,MAAM,QAE7pDC,EAAkB,GCFlBhC,EAAS,WAAkB,IAAIC,EAAIxL,KAAKyL,EAAGD,EAAIE,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,MAAM,CAAC,iBAAkBH,EAAI9I,WAAWqJ,MAAM,CAAC,GAAK,mBAAmB,CAACN,EAAG,MAAM,CAACO,YAAY,eAAe,CAACR,EAAIW,GAAIX,EAAI2O,YAAY,SAASzL,EAAKlL,GAAO,MAAO,CAAgB,cAAdkL,EAAK/J,QAAsC,qBAAd+J,EAAK/J,WAAgC,eAAgB+J,IAAOA,EAAK0L,WAAa,GAAY3O,EAAG,MAAM,CAACX,IAAI4D,EAAK3J,UAAY2J,EAAK3L,KAAOS,EAAMmI,MAAM,CACvZ,OACA,CAAC,aAAgBH,EAAI6O,OAAS3L,EAAKJ,WAAa9C,EAAIyO,iBAAiB3L,UACrE,CAAC,mBAAqB9C,EAAIwO,oBAAsBxO,EAAI6O,OAA6B,IAApB3L,EAAK4L,aAAmBjO,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOd,EAAI+O,OAAO/W,EAAOkL,MAAS,CAACjD,EAAG,MAAM,CAACO,YAAY,QAAQ,CAAER,EAAIrI,OAAOC,MAAM8B,SAASsV,mBAAqB9L,EAAK+L,cAAehP,EAAG,gBAAgB,CAACM,MAAM,CAAC,cAAcP,EAAI6O,OAAS3L,EAAKJ,WAAa9C,EAAIyO,iBAAiB3L,SAAS,cAAcI,KAAQlD,EAAI8B,KAAK7B,EAAG,MAAM,CAACO,YAAY,SAAS,CAAgB,oBAAd0C,EAAK/J,OAA4B,CAAC6G,EAAIwB,GAAGxB,EAAIyB,GAAGyB,EAAKgL,MAAM,KAAKjO,EAAG,OAAO,CAACD,EAAIwB,GAAG,UAAyB,gBAAd0B,EAAK/J,OAAwB,CAAC8G,EAAG,OAAO,CAACE,MAAM,CAAC,aAAcH,EAAIgO,WAAW,CAAChO,EAAIwB,GAAGxB,EAAIyB,GAAGyB,EAAK1J,cAAc,IAAIwG,EAAIyB,GAAGzB,EAAIiO,iBAAiBjO,EAAIwB,GAAG,UAA0B,kBAAf0B,EAAK/J,OAA2B,CAAC8G,EAAG,OAAO,CAACD,EAAIwB,GAAGxB,EAAIyB,GAAGzB,EAAIvB,GAAG,eAAe,OAAOuB,EAAIwB,GAAGxB,EAAIyB,GAAGyB,EAAKgL,OAAOjO,EAAG,IAAI,CAACO,YAAY,kBAAkBR,EAAI8B,MAAM,IAAI,GAAG7B,EAAG,MAAM,CAACO,YAAY,SAAS,CAACP,EAAG,MAAM,CAACO,YAAY,QAAQ,CAACR,EAAIwB,GAAGxB,EAAIyB,GAAGzB,EAAIvB,GAAGyE,EAAKgM,QAAS,CAAC/H,EAAEjE,EAAKiM,OAAO,IAAInP,EAAIyB,GAAGyB,EAAKkM,cAAelM,EAAKmM,cAAepP,EAAG,MAAM,CAACO,YAAY,QAAQ,CAACR,EAAIwB,GAAGxB,EAAIyB,GAAGyB,EAAKmM,kBAAkBrP,EAAI8B,SAAS9B,EAAI8B,UAAS,GAAI9B,EAAIsP,UAAWrP,EAAG,OAAO,CAACO,YAAY,WAAW,CAACR,EAAIwB,GAAGxB,EAAIyB,GAAGzB,EAAIvB,GAAG,mBAAmBuB,EAAI8B,QAE5qCC,EAAkB,GCLlBhC,EAAS,WAAkB,IAAIC,EAAIxL,KAAKyL,EAAGD,EAAIE,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACO,YAAY,6BAA6BK,GAAG,CAAC,MAAQ,SAASC,GAAiC,OAAzBA,EAAO4G,kBAAyB1H,EAAIuP,kBAAkBC,MAAM,KAAMxJ,YAAY,UAAY,SAASlF,GAAQd,EAAIyP,KAAiC,oBAA1BzP,EAAI0P,WAAWvW,OAA+B6G,EAAIvB,GAAG,oBAAsBuB,EAAIvB,GAAG,qBAAqB,WAAa,SAASqC,GAAQd,EAAIyP,KAAO,MAAM,CAAEzP,EAAIyP,KAAMxP,EAAG,MAAM,CAACO,YAAY,QAAQ,CAACR,EAAIwB,GAAGxB,EAAIyB,GAAGzB,EAAIyP,SAASzP,EAAI8B,QAEreC,EAAkB,GCWP,GACfjM,KAAA,eACAoM,MAAA,4BACAlG,OACA,OACAyT,KAAA,KAGAzb,QAAA,CACAub,oBACA,KAAAE,KAAA,GACA,MAAAC,EAAA,KAAAA,WACA7Z,EAAA,CACA8Z,YAAA,oBAAAD,EAAAvW,OAAA,kBACAI,UAAA,KAAAmW,WAAAnW,WAGA,KAAA9E,SAAAC,OACAkb,eAAA/Z,GACAb,KAAA+G,IACA,WAAAE,GAAAF,EACA,IAAAE,GACA5B,eAAAU,QAAA,oBACA,KAAA8U,YAAAxV,eAAAU,QAAA,sBAAA2U,WAAAnW,WACA,KAAA4C,MAAAC,MAAA,wBAEA,KAAAwC,OAAAP,IAAA,KAAAI,GAAA,uBAGAL,MAAAC,IACAlK,QAAAC,MAAAiK,EAAAnC,WAEAjH,QAAA,SAAAR,SAAAS,UAGA4a,UACA,KAAAJ,WAAA5M,WAAAzI,eAAA9B,QAAA,cACA8B,eAAAC,WAAA,YACA,KAAAmV,KAAA,yBAAAC,WAAAvW,OAAA,KAAAsF,GAAA,yBAAAA,GAAA,oBACA,KAAAsR,IAAAC,eAAA,CAAAC,SAAA,cCpDoW,I,wBCQhW5K,EAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,MAIa,EAAAA,E,oBCyBA,GACfvP,KAAA,mBACAkM,WAAA,CAAAkO,gBACAhO,MAAA,8DACAlO,QAAA,CACA+a,OAAA/W,EAAAkL,GAEA,SAAAsL,oBAAA,KAAAK,OAAA,IAAA3L,EAAA4L,WAAA,YAEA,KAAAL,iBAAA3L,WAAAI,EAAAJ,SAAA,KAAA1G,MAAA,+BAEA,KAAAA,MAAA,0BAAA8G,GACA,KAAA/G,MAAAC,MAAA,4BAIAoD,SAAA,IACAC,eAAA,6BACA6P,YACA,YAAAX,WAAA1L,OAAAC,KAAA/J,OAAApB,SAAA,YAAA2I,SAGAhB,UACA,QAAA/H,OAAAC,MAAA8B,SAAAsV,mBAAA,KAAAH,OAAAsB,eAAA,iBACA,MAAAC,EAAA,KAAAzB,WAAAyB,UAAAlN,KAAA+L,eACA,KAAAN,WAAAyB,IAAA/V,eAAAU,QAAA,gBAAA4T,WAAAyB,GAAAtN,aCrEwW,ICQpW,G,UAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,OAIa,I,QC0BA,GACfhN,KAAA,kBACAkM,WAAA,CAAAqO,oBACAnO,MAAA,sEACAlG,OACA,OACAuS,SAAA,EAEAE,iBAAA,KAAA9W,OAAAC,MAAA8B,SAAAR,eAGAsG,SAAA,IACAC,eAAA,mCAEAzL,QAAA,CACA0a,eACA,SAAAF,kBAGA,YAEA,KAAAC,kBAAA,KAAA9W,OAAAqL,OAAA,gCAAAyL,kBACA,KAAArS,MAAA,SACA,KAAAD,MAAAC,MAAA,gBAEA,KAAAqS,iBAAA3L,UACA,KAAA3G,MAAAC,MAAA,kBAIA0T,UACA,KAAA/V,MAAA,+BACAsK,WAAAC,KAAA,oCAAAgM,IAAA,MAAA9L,SAAA,GAAA+L,KAAA,OAAA9L,YAAA,IACAJ,WAAAC,KAAA,0BAAAkM,gBAAA,mBAAAhM,SAAA,GAAAC,YAAA,OC9EuW,ICQnW,G,UAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,OAIa,I,gCCwCf,MAAAgM,EAAAvP,IAAA,OAAAA,IAAAwC,QAAA,GACAgN,EAAAxP,GAAA,KAAAA,EAAA,UAAAA,EAAA,IAAAwC,QAAA,GACAiN,EAAAzP,KAAAW,KAAAX,EAAA0P,WAEe,OACf9a,KAAA,eACAkM,WAAA,CAAA6O,qBAAA5O,kBAAA6O,mBACA9U,OACA,OACAmG,SAAA,CACAiM,gBAAA,GACAD,gBAAA,GACAE,aAAA,EACAR,SAAA,EAEAhL,gBAAA,EACAkO,kBAAAvO,EAEAwO,iBAAA,KAIAxR,SAAA,IACAC,eAAA,4CACAA,eAAA,oFACAwR,eAAA,gCACAnD,qBACA,MAAAoD,EAAA,KAAA/O,SAAAgM,gBACA,OAAA+C,KAAAxQ,OAAAwQ,EAAAjO,OAAAC,KAAA/J,OAAApB,SAAA,4BAAAmL,KAAA0L,WAAA,oBAAA1L,KAAAxC,OAAA,IAGA1M,QAAA,CACAmd,YAAAC,GAEA,mBAAAL,GAAA,KAAA5O,SACA4O,GAAAM,cAAAN,GACA,KAAA5O,SAAA,CACAiM,gBAAA,GACAD,gBAAA,GACAE,aAAA,EACAR,SAAA,EACAhL,gBAAA,EACAkO,kBAAAvO,GAEA,KAAA7K,OAAAqL,OAAA,4BAGA,MAAA/M,EAAA,KAAAL,cAAAK,MACA,KAAAkG,MAAAC,MAAA,0BACAnG,QACAqD,gBAAA,IAAArD,GAAAyN,QAAA,GACAvK,OAAA,wBACA2J,SAAA,0BACAiL,oBAAA,EACAxW,KAAA,oBAIA8C,eAAA9B,QAAA,iBAAA6Y,KACA/W,eAAAC,WAAA,gBAEA,KAAA8J,UAAA,KACA,KAAAjC,SAAA0L,SAAA,MAIAyD,WAAAC,GACA,MAAAC,EAAAD,EAgBA,OAfAC,EAAAC,KAAA,CAAAC,EAAAC,IACAD,EAAAE,mBAAAD,EAAAC,mBACA,EACAF,EAAAE,qBAAAD,EAAAC,mBACAC,WAAAH,EAAA3D,qBAAA8D,WAAAF,EAAA5D,qBACA,EACA8D,WAAAH,EAAA3D,uBAAA8D,WAAAF,EAAA5D,qBACA,GAEA,GAGA,GAGAyD,GAEAM,aAAAC,GAAA,GAEA,MAAAlc,EAAA,GACAA,EAAAI,MAAA,KAAAL,cAAAK,MACAJ,EAAAG,WAAA,KAAAJ,cAAAI,WAEA,WAAAuB,EAAA,UAAAC,GAAA,KAAA5B,cAGA,GAFA,IAAA2B,IAAA1B,EAAA4B,gBAAAD,IAEA3B,EAAAG,WAAA,YACA,KAAAL,gBACAE,EAAAR,WAAA,KAAAM,cAAAN,WACAQ,EAAAJ,eAAA,KAAAE,cAAAF,gBAIA,KAAAkC,OAAAC,MAAAhB,kBACAf,EAAAmc,aAAA,KAAApc,cAAAoc,cAIA,KAAAra,OAAAC,MAAAhB,kBAAA,KAAAe,OAAAC,MAAA2D,UACA1F,EAAA0F,QAAA,KACA1F,EAAA2F,SAAA,OAGA,KAAA/G,SAAAC,OACA,KAAAud,eAAA,EACAC,eAAArc,GACAb,KAAA+G,IACA,KAAAoV,cACA,WAAAlV,EAAA,KAAAD,EAAA,QAAAE,GAAAH,EAQA,GANAgW,IACA,KAAAI,kBAAAnW,EAAAoW,eAAA,GACApW,EAAAqW,aAAA,GACA,KAAA1a,OAAAqL,OAAA,0BAGA,IAAA/G,EAsNA,MAAAQ,MAAAP,GAtNA,CAEA,KAAAvE,OAAAsB,QAAA,uBACA+C,EAAAsW,UAAAtW,EAAAuW,OAAAvW,EAAAwW,OAAAxW,EAAAoW,eAAA,IAEA,KAAAza,OAAAqL,OAAA,yBACA,KAAArL,OAAAC,MAAAwI,SAAA4G,OAAA,KAAArP,OAAAqL,OAAA,+BAAAhH,EAAAyW,iBAAA,IAEA,oBAAAvb,WAAA,KAAAwb,oBAAA7c,EAAAmG,GACA,KAAA2W,kBAAA3W,GAEA,IAAA4W,EAAA5W,EAAAsW,WAAA,GACAM,IAAA7N,IAAA,CAAA7B,EAAAlL,KAAA,IACAkL,EACA/J,OAAA,YACA4U,oBAAA0C,EAAAvN,EAAA7J,UACA6U,KAAAuC,EAAAvN,EAAA7J,UAAA,IACAyJ,SAAA,aAAA9K,EACA6a,UAAAhd,EAAAG,cAGAgG,EAAAsW,WAAAtW,EAAAsW,UAAA5R,SAAA1E,EAAAsW,UAAA,GAAAjZ,WAAAuZ,EAAA,KAEA5W,EAAA8W,kBAAA,IAAApS,SACAkS,GAAA5W,EAAA8W,kBAAA,IAAA/N,IAAA,CAAA7B,EAAAlL,KAAA,IACAkL,EACA/J,OAAA,mBACA+U,KAAA,GAAAyC,EAAAzN,GACAJ,SAAA,oBAAA9K,EACA6a,UAAAhd,EAAAG,eAIA,IAAA+c,EAAA/W,EAAAuW,QAAA,GACAQ,IAAAhO,IAAA,CAAA7B,EAAAlL,KAAA,IACAkL,EACA/J,OAAA,kBACA4U,oBAAA0C,EAAAvN,EAAA7J,UACA6U,KAAAuC,EAAAvN,EAAA7J,UAAA,IACAyJ,SAAA,mBAAA9K,EACA6a,UAAAhd,EAAAG,cAEA,MAAAgd,EAAAD,EAAA9P,OAAAC,KAAA+P,aAAA/P,EAAA4L,YACAoE,EAAAH,EAAA9P,OAAAC,KAAA+P,cAAA/P,EAAA4L,YAEA,IAAAqE,EAAAnX,EAAAwW,QAAA,GACAW,IAAApO,IAAA,CAAA7B,EAAAlL,KAAA,IACAkL,EACA/J,OAAA,cACA2J,SAAA,eAAA9K,EACA6a,UAAAhd,EAAAG,cAEA,MAAAod,EAAAD,EAAAlQ,OAAAC,KAAA+P,aAAA/P,EAAA4L,YACAuE,EAAAF,EAAAlQ,OAAAC,KAAA+P,cAAA/P,EAAA4L,YAEA,IAAAwE,EAAAtX,EAAAuX,QAAA,GACAD,IAAAvO,IAAA,CAAA7B,EAAAlL,KAAA,IACAkL,EACA/J,OAAA,gBACA2J,SAAA,iBAAA9K,EACAkW,KAAAwC,EAAAxN,EAAA7J,UAAA,IACA0U,oBAAA2C,EAAAxN,EAAA7J,UACAwZ,UAAAhd,EAAAG,cAEAsd,EAAA,KAAAhC,WAAAgC,GACA,MAAAE,EAAAF,EAAArQ,OAAAC,KAAA+P,aAAA/P,EAAA4L,YACA2E,EAAAH,EAAArQ,OAAAC,KAAA+P,cAAA/P,EAAA4L,YAEA,IAAA4E,EAAA1X,EAAAoW,gBAAA,GACAsB,IAAA3O,IAAA,CAAA7B,EAAAlL,KAAA,IACAkL,EACA/J,OAAA,wBACA2J,SAAA,yBAAA9K,EACA+V,oBAAA0C,EAAAvN,EAAA7J,aAEAqa,EAAAhT,QAAA,KAAA/I,OAAAqL,OAAA,0BAAA0Q,EAAA,IAGA,IAAAC,EAAA3X,EAAAqW,cAAA,GACAsB,IAAA5O,IAAA,CAAA7B,EAAAlL,KAAA,IACAkL,EACA/J,OAAA,eACA4U,oBAAA2C,EAAAxN,EAAA7J,UACA6U,KAAAwC,EAAAxN,EAAA7J,UAAA,IACAyJ,SAAA,gBAAA9K,EACA6a,UAAAhd,EAAAG,cAEA,KAAA2B,OAAAqL,OAAA,0BAAA2Q,EAAAjT,OAAAiT,EAAA,OAGA,IAAAC,EAAA5X,EAAA6X,sBAAA,GACAD,IAAA7O,IAAA,CAAA7B,EAAAlL,KAAA,IACAkL,EACA/J,OAAA,uBACA4U,oBAAA2C,EAAAxN,EAAA7J,UACA6U,KAAAwC,EAAAxN,EAAA7J,UAAA,IACAyJ,SAAA,wBAAA9K,EACA6a,UAAAhd,EAAAG,cAEA,KAAA2B,OAAAqL,OAAA,kCACA8Q,OAAAF,EAAA,OACAG,IAAA/X,EAAAgY,4BAAA,KAIA,KAAAC,cAAA,IAAAlB,KAAAI,KAAAG,GAAArQ,OAAAC,KAAA+P,cAGA,MAAAiB,EAAA,IAAAtB,KAAAY,KAAAJ,KAAAJ,KAAAS,KAAAJ,KAAAH,GAKA,KAAA/Q,SAAAU,eAAA,IAAA+P,EAAAlS,OACA,KAAA/I,OAAAqL,OAAA,kCAAAb,SAAAU,gBACA,KAAAV,SAAAgM,gBAAA+F,EAGA,MAAAC,EAAA,IAAAX,KAAAJ,KAAAJ,GAKA,GAAAJ,EAAAlS,OACA,KAAA/I,OAAAqL,OAAA,2BAAAkR,EAAA,QACA,CAEA,MAAAE,EAAA,KAAAzc,OAAAC,MAAAoD,oBAAA,IAAA0Y,KAAAC,GAAAjT,OAAA,EACA,GAAAyT,EAAAzT,SAAA0T,EAAA,CACA,IAAApc,EAAA,EAGA,MAAAqc,EAAAha,eAAA9B,QAAA,kBACA8b,IACArc,EAAAkc,EAAA9D,UAAAlN,KAAA3J,aAAA8a,GACArc,EAAAwL,KAAA8Q,IAAA,EAAAtc,IAEA,KAAAL,OAAAqL,OAAA,2BAAAkR,EAAAlc,KAGA,KAAAuc,kBAAAJ,GAGA,KAAAhS,SAAAgM,gBAAA,KAAAhM,SAAAgM,gBAAApJ,IAAA7B,IACA,GAAAA,EAAAsR,eAAA,CACA,MAAAC,EAAA,KAAA9c,OAAAC,MAAAhB,gBAEA8d,EAAAD,GAAAvR,EAAA/J,OAAApB,SAAA,WAAAmL,EAAAyR,qBAAAC,MAAA,KAAA1R,EAAAsR,eAAAI,MAAA,KACApZ,EAAA,KAAA7D,OAAAC,MAAA4D,SAGA,YAAA7D,OAAAC,MAAAwI,SAAAC,UACA,mBACA,gBAAA6C,EAAA/J,SAAA+J,EAAAkM,UAAA,KAAA3Q,GAAA,4BAAAiW,EAAA,UAAA3e,IAAA,gBACA,oBAAAmN,EAAA/J,SAAA+J,EAAAkM,UAAA,KAAA3Q,GAAA,4BAAAiW,EAAA,UAAA3e,IAAA,gBACA,kBAAAmN,EAAA/J,SAAA+J,EAAAkM,UAAA,KAAA3Q,GAAA,4BAAAiW,EAAA,UAAA3e,IAAA,gBACA,MAEA,QAEA2e,EAAAhU,OAAA,EACA,MAAAgU,EAAA,GAAAxR,EAAAkM,UAAA,KAAA3Q,GAAA,4BAAAiW,EAAA,UAAA3e,IAAA,eACA,MAAA2e,EAAA,GAAAxR,EAAAkM,UAAA,KAAA3Q,GAAA,4BAAAiW,EAAA,UAAA3e,IAAA,eACAmN,EAAAkM,UAAA,KAAA3Q,GAAA,6BAAAiW,EAAA,KAAAA,EAAA,UAAA3e,IAAA,eAEAmN,EAAAkM,UAAA,KAAA3Q,GAAA,oBAAAiW,EAAA,UAAA3e,IAAA,eAMA,KAAA4B,OAAAC,MAAAhB,kBACA8d,EAAAhU,OAAA,EACA,MAAAgU,EAAA,GAAAxR,EAAAkM,UAAA,KAAA3Q,GAAA,4BAAAjD,EAAA,EAAAkZ,EAAA,KACA,MAAAA,EAAA,GAAAxR,EAAAkM,UAAA,KAAA3Q,GAAA,4BAAAjD,EAAA,EAAAkZ,EAAA,KACAxR,EAAAkM,UAAA,KAAA3Q,GAAA,4BAAAjD,EAAA,EAAAkZ,EAAA,KAAAA,EAAA,KAEAxR,EAAAkM,UAAA,KAAA3Q,GAAA,oBAAAjD,EAAA,EAAAkZ,EAAA,MAIA,OAAAxR,IAIA,MAAAkL,EAAA,IAAAkF,KAAAH,KAAAJ,GAAA9P,OAAAC,MAAA+P,aACA4B,EAAA,CACA,eACA,oBACA,qBAEAC,EAAA,CACA,gCACA,sCACA,uCAEA1G,EAAA1H,QAAAxD,IACAA,EAAA3L,KAAAQ,SAAA,gBAAAmL,EAAA7J,UAAAwb,EAAA,aAAArX,KAAA0F,GACAA,EAAA3L,KAAAQ,SAAA,oBAAAmL,EAAA7J,UAAAwb,EAAA,kBAAArX,KAAA0F,GACAA,EAAA3L,KAAAQ,SAAA,oBAAAmL,EAAA7J,UAAAwb,EAAA,kBAAArX,KAAA0F,KAEA,UAAA5D,EAAA4B,KAAAkB,OAAA2S,QAAAF,GACA3T,EAAAR,QAAA,KAAAyB,SAAAiM,gBAAA5Q,KACA,IACA0D,EAAA,GACAiO,IAAAjO,EAAAR,OACAwO,QAAA4F,EAAAxV,KAIA,KAAA6C,SAAAiM,gBAAA,KAAAjM,SAAAiM,gBAAAqD,KAAA,CAAAC,EAAAC,IAAAD,EAAArY,SAAAsY,EAAAtY,UAEA,KAAA8C,MAAAC,MAAA,sBAKAgC,MAAAC,IACA,KAAA8S,cACA,KAAAvS,OAAAP,IAAA,KAAAI,GAAA,gBACAtK,QAAAC,MAAA,YAAAiK,EAAAnC,WAEAjH,QAAA,KACA,KAAAgd,eAAA,EACA,KAAAxd,SAAAS,OACAmF,eAAAC,WAAA,qBAGA0a,kBACA,QAAA/C,cAAA,YACA,MAAAgD,EAAA,KAAA/b,aAAA4J,SAGAjN,EAAA,GACAA,EAAAI,MAAA,KAAAL,cAAAK,MACAJ,EAAAG,WAAA,KAAAJ,cAAAI,WAEA,WAAAuB,EAAA,UAAAC,GAAA,KAAA5B,cAGA,GAFA,IAAA2B,IAAA1B,EAAA4B,gBAAAD,IAEA3B,EAAAG,WAAA,YAEA,KAAAL,gBACAE,EAAAR,WAAA,KAAAM,cAAAN,WACAQ,EAAAJ,eAAA,KAAAE,cAAAF,gBAIA,KAAAkC,OAAAC,MAAAhB,kBACAf,EAAAmc,aAAA,KAAApc,cAAAoc,cAIA,KAAAra,OAAAC,MAAAhB,kBAAA,KAAAe,OAAAC,MAAA2D,UACA1F,EAAA0F,QAAA,KACA1F,EAAA2F,SAAA,OAIA0W,eAAArc,GACAb,KAAA+G,IAEA,KAAAoV,YAAA,KAAAhP,SAAA0L,SACA,WAAA5R,EAAA,KAAAD,EAAA,QAAAE,GAAAH,EACA,OAAAE,EAqMA,MAAAQ,MAAAP,GArMA,CACA,KAAAvE,OAAAsB,QAAA,uBACA+C,EAAAsW,UAAAtW,EAAAuW,OAAAvW,EAAAwW,OAAAxW,EAAAoW,eAAA,IAEA,KAAAza,OAAAqL,OAAA,yBACA,oBAAA9L,WAAA,KAAAwb,oBAAA7c,EAAAmG,GACA,KAAA2W,kBAAA3W,GAEA,IAAA4W,EAAA5W,EAAAsW,WAAA,GACAM,IAAA7N,IAAA,CAAA7B,EAAAlL,KAAA,IACAkL,EACA/J,OAAA,YACA4U,oBAAA0C,EAAAvN,EAAA7J,UACA6U,KAAAuC,EAAAvN,EAAA7J,UAAA,IACAyJ,SAAA,aAAA9K,EACA6a,UAAAhd,EAAAG,cAGAgG,EAAAsW,WAAAtW,EAAAsW,UAAA5R,SAAA1E,EAAAsW,UAAA,GAAAjZ,WAAAuZ,EAAA,KAEA5W,EAAA8W,kBAAA,IAAApS,SACAkS,GAAA5W,EAAA8W,kBAAA,IAAA/N,IAAA,CAAA7B,EAAAlL,KAAA,IACAkL,EACA/J,OAAA,mBACA+U,KAAA,GAAAyC,EAAAzN,GACAJ,SAAA,oBAAA9K,EACA6a,UAAAhd,EAAAG,eAIA,IAAA+c,EAAA/W,EAAAuW,QAAA,GACAQ,IAAAhO,IAAA,CAAA7B,EAAAlL,KAAA,IACAkL,EACA/J,OAAA,kBACA4U,oBAAA0C,EAAAvN,EAAA7J,UACA6U,KAAAuC,EAAAvN,EAAA7J,UAAA,IACAyJ,SAAA,mBAAA9K,EACA6a,UAAAhd,EAAAG,cAEA,MAAAgd,EAAAD,EAAA9P,OAAAC,KAAA+P,aAAA/P,EAAA4L,YACAoE,EAAAH,EAAA9P,OAAAC,KAAA+P,cAAA/P,EAAA4L,YAEA,IAAAqE,EAAAnX,EAAAwW,QAAA,GACAW,IAAApO,IAAA,CAAA7B,EAAAlL,KAAA,IACAkL,EACA/J,OAAA,cACA2J,SAAA,eAAA9K,EACA6a,UAAAhd,EAAAG,cAEA,MAAAod,EAAAD,EAAAlQ,OAAAC,KAAA+P,aAAA/P,EAAA4L,YACAuE,EAAAF,EAAAlQ,OAAAC,KAAA+P,cAAA/P,EAAA4L,YAEA,IAAAwE,EAAAtX,EAAAuX,QAAA,GACAD,IAAAvO,IAAA,CAAA7B,EAAAlL,KAAA,IACAkL,EACA/J,OAAA,gBACA2J,SAAA,iBAAA9K,EACAkW,KAAAwC,EAAAxN,EAAA7J,UAAA,IACA0U,oBAAA2C,EAAAxN,EAAA7J,UACAwZ,UAAAhd,EAAAG,cAEAsd,EAAA,KAAAhC,WAAAgC,GACA,MAAAE,EAAAF,EAAArQ,OAAAC,KAAA+P,aAAA/P,EAAA4L,YACA2E,EAAAH,EAAArQ,OAAAC,KAAA+P,cAAA/P,EAAA4L,YAEA,IAAA4E,EAAA1X,EAAAoW,gBAAA,GACAsB,IAAA3O,IAAA,CAAA7B,EAAAlL,KAAA,IACAkL,EACA/J,OAAA,wBACA2J,SAAA,yBAAA9K,EACA+V,oBAAA0C,EAAAvN,EAAA7J,aAEAqa,EAAAhT,QAAA,KAAA/I,OAAAqL,OAAA,0BAAA0Q,EAAA,IAGA,IAAAC,EAAA3X,EAAAqW,cAAA,GACAsB,IAAA5O,IAAA,CAAA7B,EAAAlL,KAAA,IACAkL,EACA/J,OAAA,eACA4U,oBAAA2C,EAAAxN,EAAA7J,UACA6U,KAAAwC,EAAAxN,EAAA7J,UAAA,IACAyJ,SAAA,gBAAA9K,EACA6a,UAAAhd,EAAAG,cAEA,KAAA2B,OAAAqL,OAAA,0BAAA2Q,EAAAjT,OAAAiT,EAAA,OAGA,IAAAC,EAAA5X,EAAA6X,sBAAA,GACAD,IAAA7O,IAAA,CAAA7B,EAAAlL,KAAA,IACAkL,EACA/J,OAAA,uBACA4U,oBAAA2C,EAAAxN,EAAA7J,UACA6U,KAAAwC,EAAAxN,EAAA7J,UAAA,IACAyJ,SAAA,wBAAA9K,EACA6a,UAAAhd,EAAAG,cAEA,KAAA2B,OAAAqL,OAAA,kCACA8Q,OAAAF,EAAA,OACAG,IAAA/X,EAAAgY,4BAAA,KAIA,KAAAC,cAAA,IAAAlB,KAAAI,KAAAG,GAAArQ,OAAAC,KAAA+P,cAGA,MAAAiB,EAAA,IAAAtB,KAAAY,KAAAJ,KAAAJ,KAAAS,KAAAJ,KAAAH,GAGA,KAAA/Q,SAAAU,eAAA,IAAA+P,EAAAlS,OACA,KAAA/I,OAAAqL,OAAA,kCAAAb,SAAAU,gBACA,KAAAV,SAAAgM,gBAAA+F,EAGA,MAAAC,EAAA,IAAAX,KAAAJ,KAAAJ,GAEA,GAAAiC,EACA,GAAArC,EAAAlS,OACAuU,IAAAf,EAAA,GAAApR,UAAA,KAAAnL,OAAAqL,OAAA,2BAAAkR,EAAA,SAEA,GAAAC,EAAAzT,OAAA,CACA,MAAAwU,EAAAhB,EAAA9D,UAAAlN,KAAAJ,WAAAmS,IAAA,EACA,KAAAtd,OAAAqL,OAAA,2BAAAkR,EAAAgB,IAIA,KAAAX,kBAAAJ,GAGA,KAAAhS,SAAAgM,gBAAA,KAAAhM,SAAAgM,gBAAApJ,IAAA7B,IACA,GAAAA,EAAAsR,eAAA,CACA,MAAAC,EAAA,KAAA9c,OAAAC,MAAAhB,gBAEA8d,EAAAD,GAAAvR,EAAA/J,OAAApB,SAAA,WAAAmL,EAAAyR,qBAAAC,MAAA,KAAA1R,EAAAsR,eAAAI,MAAA,KACApZ,EAAA,KAAA7D,OAAAC,MAAA4D,SAGA,YAAA7D,OAAAC,MAAAwI,SAAAC,UACA,mBACA,gBAAA6C,EAAA/J,SAAA+J,EAAAkM,UAAA,KAAA3Q,GAAA,4BAAAiW,EAAA,UAAA3e,IAAA,gBACA,oBAAAmN,EAAA/J,SAAA+J,EAAAkM,UAAA,KAAA3Q,GAAA,4BAAAiW,EAAA,UAAA3e,IAAA,gBACA,kBAAAmN,EAAA/J,SAAA+J,EAAAkM,UAAA,KAAA3Q,GAAA,4BAAAiW,EAAA,UAAA3e,IAAA,gBACA,MAEA,QAEA2e,EAAAhU,OAAA,EACA,MAAAgU,EAAA,GAAAxR,EAAAkM,UAAA,KAAA3Q,GAAA,4BAAAiW,EAAA,UAAA3e,IAAA,eACA,MAAA2e,EAAA,GAAAxR,EAAAkM,UAAA,KAAA3Q,GAAA,4BAAAiW,EAAA,UAAA3e,IAAA,eACAmN,EAAAkM,UAAA,KAAA3Q,GAAA,6BAAAiW,EAAA,KAAAA,EAAA,UAAA3e,IAAA,eAEAmN,EAAAkM,UAAA,KAAA3Q,GAAA,oBAAAiW,EAAA,UAAA3e,IAAA,eAMA,KAAA4B,OAAAC,MAAAhB,kBACA8d,EAAAhU,OAAA,EACA,MAAAgU,EAAA,GAAAxR,EAAAkM,UAAA,KAAA3Q,GAAA,4BAAAjD,EAAA,EAAAkZ,EAAA,KACA,MAAAA,EAAA,GAAAxR,EAAAkM,UAAA,KAAA3Q,GAAA,4BAAAjD,EAAA,EAAAkZ,EAAA,KACAxR,EAAAkM,UAAA,KAAA3Q,GAAA,4BAAAjD,EAAA,EAAAkZ,EAAA,KAAAA,EAAA,KAEAxR,EAAAkM,UAAA,KAAA3Q,GAAA,oBAAAjD,EAAA,EAAAkZ,EAAA,MAIA,OAAAxR,IAIA,MAAAkL,EAAA,IAAAkF,KAAAH,KAAAJ,GAAA9P,OAAAC,MAAA+P,aACA4B,EAAA,CACA,eACA,oBACA,qBAEAC,EAAA,CACA,gCACA,sCACA,uCAEA1G,EAAA1H,QAAAxD,IACAA,EAAA3L,KAAAQ,SAAA,gBAAAmL,EAAA7J,UAAAwb,EAAA,aAAArX,KAAA0F,GACAA,EAAA3L,KAAAQ,SAAA,oBAAAmL,EAAA7J,UAAAwb,EAAA,kBAAArX,KAAA0F,GACAA,EAAA3L,KAAAQ,SAAA,oBAAAmL,EAAA7J,UAAAwb,EAAA,kBAAArX,KAAA0F,KAEA,UAAA5D,EAAA4B,KAAAkB,OAAA2S,QAAAF,GACA3T,EAAAR,QAAA,KAAAyB,SAAAiM,gBAAA5Q,KACA,IACA0D,EAAA,GACAiO,IAAAjO,EAAAR,OACAwO,QAAA4F,EAAAxV,KAIA,KAAA6C,SAAAiM,gBAAA,KAAAjM,SAAAiM,gBAAAqD,KAAA,CAAAC,EAAAC,IAAAD,EAAArY,SAAAsY,EAAAtY,aAKA+E,MAAAC,IACA,KAAA8S,cACA,KAAAvS,OAAAP,IAAA,KAAAI,GAAA,gBACAtK,QAAAC,MAAA,YAAAiK,EAAAnC,YAIA+X,cAAAvP,GACA,MAAAyQ,EAAAC,KAAA,OAAA5R,KAAAmG,MAAAyL,GAAA5R,KAAAmG,MAAAyL,GACAC,EAAA1O,GAAA,GAAAnD,KAAAmG,MAAAhD,EAAA,aAAAwO,EAAAxO,EAAA,cAAAwO,EAAAxO,EAAA,YAAAwO,EAAAxO,EAAA,MACA2O,EAAA5Q,EAAAzB,OAAAC,KAAA0O,oBAAA1O,EAAA0O,mBAAA,GACA,UAAA1Q,KAAAkB,OAAA8D,OAAAoP,GAAA,CACA,MAAA3O,EAAAzF,EAAA0N,WAAA1N,EAAA0Q,mBACA1Q,EAAAmO,cAAAgG,EAAA1O,GAGA,KAAAxE,SAAA4O,aAAAwE,YAAA,KACA,UAAArU,KAAAkB,OAAA8D,OAAAoP,GAAA,CACA,MAAA3O,EAAAzF,EAAA0N,WAAA,EACAjI,GAAA,IACAzF,EAAA0N,aACA1N,EAAAmO,cAAAgG,EAAA1O,IAEA,IAAAA,GAEA,KAAAzN,aAAA4J,WAAA5B,EAAA4B,WAEA,KAAAnL,OAAAqL,OAAA,+BACA,KAAA7G,MAAAC,MAAA,mBAKA,MAEAkS,eAAApN,GACA,KAAAiB,SAAA0L,SAAA,EACA,KAAA1R,MAAAC,MAAA,mBAGAmY,kBAAA5F,GACA,KAAAhX,OAAAC,MAAAoD,oBAAA,KAAAmB,MAAAC,MAAA,uBAAAuS,IAEA+D,oBAAA8C,EAAAC,GAEA,MAAAC,EAAAD,EAAAE,qBACA,IAAAD,MAAAhV,OAAA,YAEA,MAAAkV,EAAAF,EAAA3Q,IAAA7B,IACAA,EAAAgL,KAAA,GAAAyC,EAAAzN,GACAA,EAAA/J,OAAA,mBACA+J,IAEA0S,EAAAlV,QACA,KAAA/I,OAAAqL,OAAA,iCACA6S,uBAAAD,KAGA,MAAAE,EAAAF,EAAA3S,OAAAC,KAAAlN,aAAAwf,EAAAxf,aAAA,GACA8f,EAAApV,SACAoV,EAAA,GAAA1c,SAAA,uBACAqc,EAAA3C,iBAAAgD,IAGAnD,kBAAA3W,GACA,KAAArE,OAAAC,MAAAoD,qBACAgB,EAAAsW,UAAAtW,EAAA+Z,iBACA/Z,EAAA8W,iBAAA9W,EAAAga,wBACAha,EAAAoW,eAAApW,EAAAia,sBACAja,EAAAqW,aAAArW,EAAAka,oBACAC,QAAAC,eAAApa,EAAA,oBACAma,QAAAC,eAAApa,EAAA,2BACAma,QAAAC,eAAApa,EAAA,yBACAma,QAAAC,eAAApa,EAAA,0BAIA0D,UACA,MAAA2W,EAAA,iDACA,KAAA9T,QAAA,KAAA+T,OAAAD,EAAAnV,MAAA,KAAA4Q,eAAA,CAAAyE,WAAA,IAGA,MAAAC,EAAA,mDACA,KAAAjU,QAAA,KAAA+T,OAAAE,EAAA,CAAAC,EAAAC,IAAAD,GAAAC,GAAA,KAAA5E,gBAEA,KAAA3V,MAAAI,MAAA,wBAAAuV,gBAEA,MAAAlR,EAAA,oDACA,KAAA2B,QAAA,KAAA+T,OAAA1V,EAAA6V,MAAA,KAAAzB,mBAGA,KAAA7Y,MAAAwD,IAAA,+BAAAmS,gBAEA,KAAA3V,MAAAwD,IAAA,0BAAAmS,gBAEA,KAAA3V,MAAAwD,IAAA,oBAAA6L,GAAA,KAAAsG,aAAAtG,KAEAsE,UACA6G,OAAAC,OAAA,KAAAN,OAAA,mBAAApV,GAAA,KAAA/E,MAAAC,MAAA,gBAAA8E,KAEAkE,gBACA,KAAA7C,SAAA,KAAAA,YC5uBoW,ICQhW,G,UAAY,eACd,EACAxC,EACAgC,GACA,EACA,KACA,WACA,OAIa,S,6CClBf,IAAIuD,EAAgB,EAAQ,QACxBC,EAAc,EAAQ,QACtBC,EAAW,EAAQ,QACnBC,EAA0B,EAAQ,QAElCC,EAAmB3I,gBACnB4I,EAA2BD,EAAiBE,UAC5CiR,EAAStR,EAAYI,EAAyBkR,QAC9CC,EAAUvR,EAAYI,EAAyB,WAC/Ce,EAAUnB,EAAYI,EAAyBe,SAC/ClJ,EAAO+H,EAAY,GAAG/H,MACtB3H,EAAS,IAAI6P,EAAiB,eAElC7P,EAAO,UAAU,IAAK,GAGtBA,EAAO,UAAU,SAAK2M,GAElB3M,EAAS,KAAO,OAClByP,EAAcK,EAA0B,UAAU,SAAU7P,GAC1D,IAAI4K,EAASsF,UAAUtF,OACnBuF,EAASvF,EAAS,OAAI8B,EAAYwD,UAAU,GAChD,GAAItF,QAAqB8B,IAAXyD,EAAsB,OAAO6Q,EAAQtiB,KAAMsB,GACzD,IAAIif,EAAU,GACdrO,EAAQlS,MAAM,SAAUuiB,EAAGC,GACzBxZ,EAAKuX,EAAS,CAAEzV,IAAK0X,EAAG9V,MAAO6V,OAEjCtR,EAAwB/E,EAAQ,GAChC,IAMIuW,EANA3X,EAAMkG,EAAS1P,GACfoL,EAAQsE,EAASS,GACjBjO,EAAQ,EACRkf,EAAS,EACTC,GAAQ,EACRC,EAAgBrC,EAAQrU,OAE5B,MAAO1I,EAAQof,EACbH,EAAQlC,EAAQ/c,KACZmf,GAASF,EAAM3X,MAAQA,GACzB6X,GAAQ,EACRL,EAAQtiB,KAAMyiB,EAAM3X,MACf4X,IAET,MAAOA,EAASE,EACdH,EAAQlC,EAAQmC,KACVD,EAAM3X,MAAQA,GAAO2X,EAAM/V,QAAUA,GAAQ2V,EAAOriB,KAAMyiB,EAAM3X,IAAK2X,EAAM/V,SAElF,CAAEiF,YAAY,EAAMC,QAAQ,K,oCC/CjC,IAAIrG,EAAS,WAAkB,IAAIC,EAAIxL,KAAKyL,EAAGD,EAAIE,MAAMD,GAAG,OAAOA,EAAG,cAAc,CAACoX,YAAYrX,EAAIsX,GAAG,CAAC,CAAChY,IAAI,QAAQiY,GAAG,WAAW,MAAO,CAACtX,EAAG,MAAM,CAACO,YAAY,iBAAiB,CAACP,EAAG,MAAM,CAACD,EAAIwB,GAAGxB,EAAIyB,GAAGzB,EAAIvB,GAAG,wBAAyBuB,EAAIjG,MAAM,uBAAuB,IAAUiG,EAAIrI,OAAOC,MAAM8B,SAAS8d,iBAAkBvX,EAAG,MAAM,CAACA,EAAG,wBAAwB,GAAGD,EAAI8B,SAAS2V,OAAM,MAAS,CAACxX,EAAG,MAAM,CAACO,YAAY,aAAaL,MAAM,CAACH,EAAI9I,WAAWqJ,MAAM,CAAC,GAAK,wBAAwB,CAACP,EAAIwB,GAAG,IAAIxB,EAAIyB,GAAGzB,EAAI0X,WAAW,UAEngB3V,EAAkB,G,wBCFlBhC,EAAS,WAAkB,IAAIC,EAAIxL,KAAKyL,EAAGD,EAAIE,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACO,YAAY,uBAAuBK,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOd,EAAI7D,MAAMC,MAAM,qBAAqB,MAAS,CAAC6D,EAAG,IAAI,CAACO,YAAY,cAAcL,MAAM,CAAEyH,OAAQ5H,EAAImS,qBAAsBlS,EAAG,OAAO,CAACO,YAAY,eAAe,CAACR,EAAIwB,GAAGxB,EAAIyB,GAAGzB,EAAIvB,GAAG,2BAA2B9H,QAAQ,MAAO,aAEjXoL,EAAkB,G,YCAP,GACfjM,KAAA,sBACA0J,SAAA,IACAC,eAAA,oCCL0V,I,wBCQtV4F,EAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,MAIa,EAAAA,E,oBCbA,GACfrD,WAAA,CAAAC,kBAAA0V,qBACA3b,OACA,OACA0b,UAAA,MAGA1jB,QAAA,CACA4jB,kBACA,MAAA/hB,EAAA,GAEA,QAAA8B,OAAAC,MAAAC,UAAA0C,GAAA,CACA,MAAAA,EAAAnD,KAAAoD,MAAA,KAAA7C,OAAAC,MAAAC,UAAA0C,IACA1E,EAAAG,WAAAuE,EAAAvE,WAEA,KAAA0hB,UAAAnd,EAAAK,aAEA,KAAAnG,SAAAC,OACAmjB,eAAAhiB,GACAb,KAAA+G,IACA,WAAAC,EAAA,KAAAC,GAAAF,EACA,OAAAE,EAAA,CACA,GAAAD,EAAA0E,QAAA,IAAA1E,EAAA0E,OAAA,YAAA9B,OAAAP,IAAA,KAAAI,GAAA,sBACA,KAAA9G,OAAAqL,OAAA,4BAAAhH,EAAA,OAGA/G,QAAA,SAAAR,SAAAS,SAEA4iB,uBACA,MAAAjiB,EAAA,CACAgF,cAAA,KAAAlD,OAAAC,MAAAC,UAAAiD,KAAA,IAEA,QAAAnD,OAAAC,MAAAC,UAAA0C,GAAA,CACA,MAAAA,EAAAnD,KAAAoD,MAAA,KAAA7C,OAAAC,MAAAC,UAAA0C,IACA1E,EAAAG,WAAAuE,EAAAvE,WACAuE,EAAAK,eACA,KAAA8c,UAAAnd,EAAAK,aACA,KAAAuB,MAAAC,MAAA,wBAAA7B,EAAAK,eAGA,IAAA/E,EAAAgF,cAAA,YAAA+D,OAAAP,IAAA,KAAAI,GAAA,qBAEA,KAAAhK,SAAAC,OACAmjB,eAAAhiB,GACAb,KAAA+G,IACA,WAAAC,EAAA,KAAAC,GAAAF,EACA,OAAAE,GACA,OACA,KAAAtE,OAAAqL,OAAA,4BAAAhH,GACAA,EAAA+b,UAAA,WAAAL,YACA,KAAAA,UAAA1b,EAAA+b,SACA,KAAApgB,OAAAqL,OAAA,mBAAApI,aAAAoB,EAAA+b,WACA,KAAA5b,MAAAC,MAAA,6BAAAsb,YAEA,WAAAA,WAAA,KAAAM,eAAAhc,GACA,MAEA,YACA,KAAA4C,OAAAP,IAAA,KAAAI,GAAA,qBACA,MAEA,QACA,KAAAG,OAAAP,IAAA,KAAAI,GAAA,kBAIAxJ,QAAA,SAAAR,SAAAS,SAEA+iB,iBACA3f,aAAAyC,QAAA,4BAEAmd,YACA,KAAA9T,UAAA,KACAkJ,SAAAC,cAAA,8BAAAD,SAAAC,cAAA,4BAAA4K,MAAAC,QAAA,QACA9K,SAAAC,cAAA,6BAAAD,SAAAC,cAAA,2BAAA4K,MAAAC,QAAA,WAGAJ,eAAAhc,GACA,MAAAqc,EAAA,KAAA5Z,GAAA,+BAAAzC,EAAAsc,aAAA,EAAAtc,EAAAsB,kBACA,KAAAoa,UAAAW,EAEA,KAAA1gB,OAAAqL,OAAA,mBAAApI,aAAAyd,IACA,KAAAlc,MAAAC,MAAA,wBAAAic,KAGA3Y,UACA,KAAAuY,iBACA,KAAA9b,MAAAwD,IAAA,WAAA4Y,IACA,IAAAA,IAAA,YAAArhB,UAAA,KAAA0gB,kBAAA,KAAAE,wBACA,IAAAS,GAAA,KAAAL,cAGA/jB,QAAAmK,IAAA,KAAAvE,MAAA,6BClGyW,ICQrW,G,UAAY,eACd,EACAgG,EACAgC,GACA,EACA,KACA,WACA,OAIa,S,6CCnBf,W,2DCAA,W,kCCAA,W,qBCAA,MAAMyW,EAAS,CAGXC,EAAQ,QACRA,EAAQ,SAGNC,EAAaC,GACVA,EACE,IAAItkB,QAAQ,CAACC,EAASC,KACzB,MAAMqkB,EAAOtL,SAASuL,cAAc,QACpCD,EAAKE,GAAK,QACVF,EAAKG,IAAM,UACXH,EAAKnb,KAAOkb,EACZrL,SAAS0L,KAAKC,YAAYL,GAE1BA,EAAKM,OAAS5kB,EACdskB,EAAKO,QAAU5kB,EACfK,WAAWL,EAAQ,OAVNF,QAAQC,UAcvB8kB,EAAaC,UACf,MAAOb,EAAO9X,OACV,UACUgY,EAAUF,EAAOc,SACzB,MAAOllB,GACLD,QAAQC,MAAMA,KAK1BC,QAAQ0f,IAAIwF,MAAMjV,KAAK,CAAE5D,OAAQ,GAAK0Y,K,yDChCtC,W,kCCCA,IAAII,EAAaC,UAEjB/L,EAAOC,QAAU,SAAU+L,EAAQC,GACjC,GAAID,EAASC,EAAU,MAAM,IAAIH,EAAW,wBAC5C,OAAOE,I,kCCLT,IAAI3Z,EAAS,WAAkB,IAAIC,EAAIxL,KAAKyL,EAAGD,EAAIE,MAAMD,GAAG,OAAOA,EAAG,OAAO,CAACzH,IAAI,QAAQgI,YAAY,SAAS,CAACP,EAAG,OAAO,CAACzH,IAAI,QAAQgI,YAAY,QAAQ2X,MAAO,oBAAoBnY,EAAI4Z,UAAW,CAAC5Z,EAAIqG,GAAG,YAAY,MAErNtE,EAAkB,GCOP,GACfjM,KAAA,gBACAkG,OACA,OACA4d,MAAA,IAGA5lB,QAAA,CACA6lB,OACA,MAAAC,EAAA,KAAAC,MAAAD,MAAAE,YACAC,EAAA,KAAAF,MAAAE,MAAAD,YACAC,EAAAH,IAAA,KAAAF,MAAAE,EAAAG,KAGAnK,UACA,KAAA+J,SCxBsV,I,wBCQlVxU,EAAY,eACd,EACAtF,EACAgC,GACA,EACA,KACA,WACA,MAIa,OAAAsD,E,2CCnBf,IAAItF,EAAS,WAAkB,IAAIC,EAAIxL,KAAKyL,EAAGD,EAAIE,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,MAAM,CAAC,0BAA2BH,EAAI9I,UAAW,CAAEoJ,IAAKN,EAAIpJ,kBAAmB2J,MAAM,CAAC,GAAK,2BAA2BM,GAAG,CAAC,MAAQ,SAASC,GAAQA,EAAO4G,qBAAsB,CAACzH,EAAG,aAAa,CAACM,MAAM,CAAC,KAAO,SAAS,CAAEP,EAAIka,WAAYja,EAAG,MAAM,CAACO,YAAY,eAAe,CAACP,EAAG,MAAM,CAACO,YAAY,aAAa,CAACR,EAAIwB,GAAGxB,EAAIyB,GAAGzB,EAAIvB,GAAG,mBAAmBwB,EAAG,MAAM,CAACO,YAAY,YAAYP,EAAG,MAAM,CAACO,YAAY,iBAAiB,CAACP,EAAG,MAAM,CAACO,YAAY,gBAAgB,CAACP,EAAG,OAAO,CAACD,EAAIwB,GAAGxB,EAAIyB,GAAGzB,EAAIvB,GAAG,eAAgBuB,EAAIpJ,gBAAiB,CAACoJ,EAAIwB,GAAG,MAAMxB,EAAI8B,MAAM,GAAG7B,EAAG,OAAO,CAACD,EAAIwB,GAAGxB,EAAIyB,GAAGzB,EAAIpK,cAAc+N,UAAY3D,EAAIpK,cAAcukB,sBAAsBna,EAAIyB,GAAGzB,EAAIiO,mBAAoBjO,EAAIhH,gBAAgBG,QAAU6G,EAAIoa,gBAAiBna,EAAG,MAAM,CAACO,YAAY,YAAY,CAACP,EAAG,OAAO,CAACD,EAAIwB,GAAGxB,EAAIyB,GAAGzB,EAAIvB,GAAG,iBAAiB,KAAMuB,EAAIpJ,gBAAiB,CAACoJ,EAAIwB,GAAG,MAAMxB,EAAI8B,MAAM,GAAG7B,EAAG,OAAO,CAAC,CAAE,CAAC,YAAa,mBAAmBlI,SAASiI,EAAI9G,aAAaC,QAAS,CAAC6G,EAAIwB,GAAG,MAAMxB,EAAIyB,GAAGzB,EAAI9G,aAAamhB,iBAAiB,IAAIra,EAAIyB,GAAGzB,EAAIiO,cAAc,KAAKjO,EAAIyB,GAAGzB,EAAI9G,aAAa6U,qBAAqB,YAAY/N,EAAI8B,KAAgC,gBAA1B9B,EAAI9G,aAAaC,OAAwB,CAAC6G,EAAIwB,GAAG,MAAMxB,EAAIyB,GAAGzB,EAAI9G,aAAamhB,iBAAiB,IAAIra,EAAIyB,GAAGzB,EAAIiO,cAAc,MAAMjO,EAAI8B,KAAmC,0BAA7B9B,EAAIhH,gBAAgBG,OAAkC,CAAC6G,EAAIwB,GAAG,MAAMxB,EAAIyB,GAAGzB,EAAIhH,gBAAgBshB,gBAAgB,IAAIta,EAAIyB,GAAGzB,EAAIiO,cAAc,MAAMjO,EAAI8B,OAAO,KAAK9B,EAAI8B,KAAM9B,EAAIua,QAASta,EAAG,MAAM,CAACO,YAAY,OAAO,CAACP,EAAG,OAAO,CAACD,EAAIwB,GAAGxB,EAAIyB,GAAGzB,EAAIvB,GAAG,aAAcuB,EAAIpJ,gBAAiB,CAACoJ,EAAIwB,GAAG,MAAMxB,EAAI8B,MAAM,GAAG7B,EAAG,OAAO,CAACD,EAAIwB,GAAGxB,EAAIyB,GAAGzB,EAAIua,SAASva,EAAIyB,GAAGzB,EAAIiO,mBAAmBjO,EAAI8B,KAAM9B,EAAIwa,UAAWva,EAAG,MAAM,CAACO,YAAY,OAAO,CAACP,EAAG,OAAO,CAACD,EAAIwB,GAAGxB,EAAIyB,GAAGzB,EAAIvB,GAAG,eAAgBuB,EAAIpJ,gBAAiB,CAACoJ,EAAIwB,GAAG,MAAMxB,EAAI8B,MAAM,GAAG7B,EAAG,OAAO,CAACD,EAAIwB,GAAGxB,EAAIyB,GAAGzB,EAAIwa,WAAWxa,EAAIyB,GAAGzB,EAAIiO,mBAAmBjO,EAAI8B,OAAO7B,EAAG,MAAM,CAACO,YAAY,cAAcR,EAAI8B,OAAO7B,EAAG,MAAM,CAACO,YAAY,eAAe,CAACP,EAAG,MAAM,CAACO,YAAY,eAAe,CAACP,EAAG,MAAM,CAACO,YAAY,SAAS,CAACP,EAAG,OAAO,CAACO,YAAY,YAAYL,MAAM,CAAC,aAAcH,EAAIgO,WAAW,CAAChO,EAAIwB,GAAGxB,EAAIyB,GAAGzB,EAAIhH,gBAAgByhB,kBAAmBza,EAAI0a,WAAYza,EAAG,OAAO,CAACO,YAAY,OAAOL,MAAM,CAACyH,OAAQ5H,EAAIka,YAAYrZ,GAAG,CAAC,MAAQ,SAASC,GAAQd,EAAIka,YAAcla,EAAIka,cAAc,CAACla,EAAIwB,GAAG,KAAKxB,EAAIyB,GAAGzB,EAAIvB,GAAG,aAAawB,EAAG,OAAOD,EAAI8B,OAAO7B,EAAG,MAAM,CAACO,YAAY,SAAS,CAAER,EAAIhH,gBAAgB2hB,iBAAkB1a,EAAG,OAAO,CAACE,MAAM,CAAC,eAAgB,CAAC,aAAcH,EAAIgO,YAAY,CAAChO,EAAIwB,GAAGxB,EAAIyB,GAAGzB,EAAIhH,gBAAgB2hB,qBAAqB3a,EAAI8B,KAAM9B,EAAIhH,gBAAgB4hB,aAAc3a,EAAG,OAAO,CAACO,YAAY,iBAAiBL,MAAM,CAAE,kBAAmBH,EAAIoa,iBAAkB7S,SAAS,CAAC,UAAYvH,EAAIyB,GAAGzB,EAAIhH,gBAAgB4hB,iBAAiB5a,EAAI8B,SAAS7B,EAAG,MAAM,CAACO,YAAY,gBAAgBL,MAAM,CAAC,CAAC0a,QAAS7a,EAAIpE,gBAAkBoE,EAAIrI,OAAOsB,QAAQ,+BAAgC+G,EAAIoJ,MAAMC,QAAQxI,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOd,EAAI5D,MAAM,oBAAoB,CAAC6D,EAAG,OAAO,CAACD,EAAIwB,GAAGxB,EAAIyB,GAAGzB,EAAIvB,GAAG,gBAAiBuB,EAAI0B,IAAIwL,UAAWjN,EAAG,KAAKD,EAAI8B,UAAU,IAEnjGC,EAAkB,G,YC4DP,GACfjM,KAAA,oBACAoM,MAAA,mBACAlG,OACA,OACAke,YAAA,IAGA1a,SAAA,IACAC,eAAA,8DACAA,eAAA,sEACAA,eAAA,8CACAA,eAAA,2BACAwR,eAAA,mEACAsJ,UACA,YAAArhB,aAAA4hB,UAAA,KAAA9hB,gBAAA8hB,UAAA,KAAAllB,cAAAklB,UAEAN,YACA,YAAAthB,aAAA6hB,kBAAA,KAAA/hB,gBAAA+hB,kBAAA,KAAAnlB,cAAAmlB,kBAEAL,aACA,YAAAH,SAAA,KAAAC,WAEAJ,kBACA,aAAAphB,gBAAAG,SACA,KAAAH,gBAAAG,OAAApB,SAAA,YAGAijB,MAAA,CACAN,WAAAjE,GACAA,IAAA,KAAAyD,YAAA,KAGApK,UACA,KAAA3T,MAAAwD,IAAA,iBACA,KAAAua,YAAA,MCjGyW,I,wBCQrW7U,EAAY,eACd,EACAtF,EACAgC,GACA,EACA,KACA,WACA,MAIa,OAAAsD,E,kECnBf,W,0CCAAqI,EAAOC,QAAU,kjC", "file": "js/pagePay~pageSdkV2.af8762f2.js", "sourcesContent": ["import { dealSmDeviceId, getBrowserInfo, getPlatform, getPWADisplayMode } from '@/utils/utils'\nimport UAParser from 'ua-parser-js'\nimport { placeOrderCard, placeOrderToken } from '@/server'\nimport { logForPayResult } from '@/utils/logHelper'\nimport { mapState } from 'vuex'\n\nexport default {\n  methods: {\n    customGetSmDeviceId (cb) {\n      if (typeof cb !== 'function') return console.error('customGetSmDeviceId cb 必须为函数！')\n\n      new Promise((resolve, reject) => {\n        this.$loading.show()\n\n        const timer = setTimeout(() => resolve(''), 2500)\n        dealSmDeviceId(function (deviceId) {\n          timer && clearTimeout(timer)\n          resolve(deviceId)\n        })\n      })\n        .then(deviceId => {\n          if (!deviceId) console.error('dealSmDeviceId 失败！')\n          cb(deviceId)\n        })\n        .finally(() => this.$loading.hide())\n    },\n    purchaseGoodsWithDeviceId (backup, deviceId) {\n      const {\n        channel_id: channelId,\n        channel_name: channelName,\n        sub_channel_id: subChannelId\n      } = this.chosenChannel\n\n      const chosenDiamond = this.chosenDiamond\n      const params = {\n        // group_id: this.urlParams.gid,\n        // sku_id: this.urlParams.sk,\n        name: this.$vt('tokenName'),\n        product_id: chosenDiamond.product_id,\n        price: +(chosenDiamond.nowPriceWithTaxAndExtra || chosenDiamond.price),\n        // sku_source: ['game', 'mall_center'][+this.urlParams.cms],\n        channel: channelName,\n        channel_id: channelId,\n        sub_channel_id: subChannelId,\n        // through_cargo: this.urlParams.tc,\n        return_url: location.origin, // payment 根据此参数来跳转\n        fp_device_id: deviceId\n      }\n      if (this.$router.options.base !== '/') {\n        params.return_url += `/${this.$router.options.base.replace(/\\//g, '')}`\n        if (this.IS_CHECKOUT_SDK) {\n          let returnUrl = location.origin + window.__ROUTERPATH\n          if (returnUrl.endsWith('/')) returnUrl = returnUrl.slice(0, -1)\n          params.return_url = returnUrl\n        }\n      }\n\n      if (this.$gameName === 'aof' || this.$gameName === 'rom') {\n        params.ext_info = JSON.stringify({\n          project_code: 'koa_aof_web'\n        })\n      }\n\n      const { type, chosenNum } = this.chosenDiamond\n      if (type === 2) params.custom_multiple = chosenNum\n\n      if (backup) params.backup = 1\n\n      let utmCampaign = this.$store.state.urlParams.utm_campaign\n      if (utmCampaign) {\n        if (utmCampaign.includes('?')) {\n          const index = utmCampaign.indexOf('?')\n          utmCampaign = utmCampaign.slice(0, index)\n        }\n        params.tracking_id = utmCampaign\n      }\n\n      if (getPWADisplayMode === 'standalone') params.tracking_id = `pwa_${getPlatform}`\n      params.browser_id = localStorage.getItem('browserMark') || ''\n\n      // 其他额外参数\n      const ref = new UAParser()\n      params.browser_info = {\n        terminalType: window.innerWidth > 1200 ? 'pc' : 'mobile',\n        osType: ref.getOS().name,\n        ...getBrowserInfo()\n      }\n\n      const FinalPriceState = this.$store.getters['formdata/FinalPriceState']\n      const chosenCoupon = this.chosenCoupon\n      switch (FinalPriceState.feType) {\n        case 'first_pay': case 'direct_first_pay': {\n          params.act_type = 'first_pay'\n          params.discount = chosenCoupon.discount\n          params.discount_price = chosenCoupon.discount_price\n          break\n        }\n        case 'first_pay_rebate': case 'direct_first_pay_rebate': {\n          params.act_type = 'first_pay_rebate'\n          params.discount = chosenCoupon.discount\n          params.discount_price = chosenCoupon.discount_price\n\n          if (chosenCoupon.act_type) params.act_type = chosenCoupon.act_type\n          break\n        }\n        case 'discount_coupon': {\n          params.act_type = 'coupon'\n          params.discount = chosenCoupon.discount\n          params.discount_price = chosenCoupon.discount_price\n          params.coupon_id = chosenCoupon.coupon_id\n          break\n        }\n        case 'cash_coupon': {\n          params.act_type = 'deduct'\n          params.discount = chosenCoupon.deduct_price\n          params.discount_price = chosenCoupon.price\n          params.coupon_id = chosenCoupon.coupon_id\n          break\n        }\n        case 'rebate_coupon': {\n          params.act_type = 'rebate'\n          params.discount = chosenCoupon.discount\n          params.discount_price = chosenCoupon.discount_price\n          params.coupon_id = chosenCoupon.coupon_id\n          break\n        }\n        /* 固定折扣 */\n        case 'fixed_discount_coupon': case 'direct_fixed_discount': {\n          const defaultDiscountInfo = this.$store.state.formdata.defaultDiscountInfo\n          params.act_type = 'fixed_discount'\n          params.discount = defaultDiscountInfo.discount\n          params.discount_price = defaultDiscountInfo.discount_price\n          break\n        }\n        case 'fixed_rebate': case 'direct_fixed_rebate': {\n          params.act_type = 'fixed_rebate'\n          break\n        }\n        case 'fixed_dynamic_rebate': {\n          params.act_type = 'product_fixed_rebate'\n          break\n        }\n        default: {\n          // 兜底折扣\n          const type = this.$store.state.functionSwitch.fixedDiscountType\n          if (type && !params.act_type) {\n            params.act_type = type\n          }\n\n          // 兼容ssv 第一个档位没有返钻、mycard 没有档位\n          const isMinimumDiamondId = this.$store.state.functionSwitch.smallDiamondDoubleDiscount && (chosenDiamond.product_id === this.$gcbk('ids.minimumDiamondId'))\n          const isNotRebate = isMinimumDiamondId || this.$store.getters['formdata/TWMyCard']\n          if (isNotRebate) delete params.act_type\n        }\n      }\n\n      /* Xsolla Partner network 活动 */\n      // const tck = this.urlParams.tck || ''\n      // if (tck.startsWith('x@')) {\n      //   params.tracking_id = tck.replace('x@', '')\n      //   params.source = 'pc_xsolla'\n      // }\n\n      // payermax 灰度\n      const chosenChannel = this.chosenChannel\n      const payermaxKey = `${chosenChannel.channel_id}_${chosenChannel.channel_name}_${chosenChannel.sub_channel_id}_dropin`\n      if (this.$store.state.vb.builtInCashier && payermaxKey === 'payermax_A34_A34_dropin') {\n        params.order_type = 'drop_in'\n      }\n\n      /* 支持直购礼包 */\n      sessionStorage.removeItem('goodsName')\n      if (this.IS_CHECKOUT_SDK) {\n        const urlParams = this.$store.state.urlParams\n        const tc = JSON.parse(urlParams.tc || '{}')\n        if (FinalPriceState.sdkType) params.act_type = FinalPriceState.sdkType\n        params.package_id = tc.package_id\n        params.name = params.package_name = tc.product_name\n        params.game_order_id = urlParams.oid\n        sessionStorage.setItem('goodsName', tc.product_name)\n      }\n      if (this.IS_CHECKOUT_SDK_V2) {\n        const calcSdk2Info = window._calState()\n        if (calcSdk2Info.type) params.act_type = calcSdk2Info.type\n        if (!params.name) {\n          params.name = window.defaultPackageName\n          sessionStorage.setItem('goodsName', window.defaultPackageName)\n        }\n      }\n\n      const state = this.$store.state\n      const orderInfo = {\n        method: `${channelName}|${channelId}|${subChannelId}`,\n        amount: '', // 定价金额\n        country: state.country, // 国家\n        currency: state.currency, // 当地货币\n        product_info: params.product_id, // 购买的商品id信息集\n        event: 'pay_completed', // 支付完成\n        revenue: params.price // 支付的当地货币金额\n      }\n\n      this.$loading.show()\n      this.requestLoading = true\n\n      new Promise(resolve => {\n        if (this.$store.getters['formdata/TWMyCard']) return resolve(placeOrderCard(params))\n        resolve(placeOrderToken(params))\n      })\n        .then(res => {\n          const { data, code, message } = res\n          switch (code) {\n            case 0: {\n              new Promise((resolve, reject) => {\n                if ('coin_debt' in data) {\n                  this.$root.$emit('showPop', 'ArrearsReminder', { debt: data.coin_debt || 0 })\n                  this.$root.$once('arrearsReminderResult', result => {\n                    if (result) resolve(1)\n                    else reject(Error('cancel pop!'))\n                  })\n                } else {\n                  resolve(2)\n                }\n              })\n                .then(res => {\n                  logForPayResult('success', data.order_id, '-', orderInfo)\n                  if (typeof data.pay_url === 'string') {\n                    const url = new URL(data.pay_url)\n                    const urlParams = new URLSearchParams(url.search)\n\n                    sessionStorage.setItem('3zRtY8vXwN', JSON.stringify(data))\n                    sessionStorage.removeItem('7x9FkL2pQm')\n                    if (data.pay_url.includes('pingpong') && urlParams.get('window_type') !== 'jump_url') {\n                      const ppParams = {\n                        ppToken: urlParams.get('token'),\n                        coinNums: data.coin_recv,\n                        currency: data.currency,\n                        currency_symbol: data.currency_symbol,\n                        amount: data.price\n                      }\n\n                      if (res === 1) ppParams.inDebt = true\n                      sessionStorage.setItem('ppParams', JSON.stringify(ppParams))\n                      this.$router.push('/pp')\n                    } else {\n                      if (urlParams.get('window_type') === 'jump_url') {\n                        location.href = data.pay_url.replace('&window_type=jump_url', '').replace('?window_type=jump_url', '')\n                        return null\n                      }\n                      if (data.open_with_new_window) return window.open(data.pay_url, '_blank')\n                      location.href = data.pay_url\n                    }\n                  } else {\n                    data.pay_url.coinNums = data.coin_recv\n                    data.pay_url.currency_symbol = data.currency_symbol\n                    data.pay_url.host = data.payment_host\n                    data.pay_url.order_id = data.payment_order_id\n                    data.pay_url.out_trade_no = data.order_id\n\n                    sessionStorage.setItem('id_sign', data.payment_order_id_sign) // 临时数据\n                    sessionStorage.setItem('url', data.payment_host) // 临时数据\n\n                    if (res === 1) data.pay_url.inDebt = true\n\n                    if (data.pay_url.channel === 'payermax') {\n                      data.pay_url.payment_order_id = data.payment_order_id\n                      data.pay_url.name = data.name\n                    }\n                    sessionStorage.setItem('params', JSON.stringify(data.pay_url))\n\n                    if (data.pay_url.channel === 'payermax') {\n                      this.$router.push('/pm')\n                    } else if (data.pay_url.client_secret) {\n                      this.$router.push('/aw')\n                    } else if (data.pay_url.store_card_url) {\n                      this.$router.push('/cko')\n                    } else if (data.pay_url.stripe_client_secret) {\n                      this.$router.push('/sp')\n                    } else {\n                      this.$router.push('/ad')\n                    }\n                  }\n                })\n                .catch(err => {\n                  console.log(err.message)\n                })\n              break\n            }\n            case 1003: {\n              const errTipsList = {\n                1: this.$t('illegalGift'),\n                2: this.$t('expiredPackage'),\n                3: this.$t('InventoryShortage')\n              }\n              this.$toast.err(errTipsList[data.check_status])\n\n              logForPayResult('failed', '-', errTipsList[data.check_status], orderInfo)\n              break\n            }\n            default: {\n              this.$toast.err(this.$t('shop_fail'))\n              throw Error(message)\n            }\n          }\n        })\n        .catch(err => {\n          this.requestLoading = false\n          logForPayResult('failed', '-', err, orderInfo)\n          console.error(`coinPlaceOrder下单失败：${err.message}`)\n        })\n        .finally(() => {\n          this.requestLoading = false\n          this.$loading.hide()\n        })\n    },\n    async purchaseGoods (backup) {\n      if (this.requestLoading || this.$store.getters['riskPolicy/forbiddenAccess']) return null\n      if (!this.isLogin) return this.$root.$emit('ClickPayButNotLogin')\n      // 未同意隐私协议\n      if (!this.$store.state.agreePrivacyPolicy) return this.$root.$emit('showPop', 'PrivacyPolicy')\n      // 德国区 需要同意隐私协议\n      if (window.__needDEPop) {\n        const result = await new Promise((resolve, reject) => {\n          const payload = {\n            ok: () => resolve(1),\n            no: () => resolve(0)\n          }\n          this.$root.$emit('showPop', 'privateConfirmPop', payload)\n        })\n        if (!result) return null\n      }\n      if (JSON.stringify(this.chosenChannel) === '{}') return this.$toast.err(this.$t('ModalTIpsShopBeforeChooseChannel'))\n      const me = this\n      this.customGetSmDeviceId(function (deviceId) {\n        me.purchaseGoodsWithDeviceId(backup, deviceId)\n      })\n    },\n    judgeRisk () {\n      const { channel_id: channelId } = this.chosenChannel\n      const index = this.$store.getters['riskPolicy/showTipsWhenSomeChannel'].indexOf(channelId)\n      if (index !== -1) {\n        const key = `use_${this.$store.getters['riskPolicy/showTipsWhenSomeChannel'][index]}`\n        return this.$root.$emit('showPop', 'RiskControlPolicy', { key, cb: this.purchaseGoods })\n      }\n\n      this.purchaseGoods()\n    }\n  },\n  computed: {\n    ...mapState('formdata', ['chosenChannel', 'chosenDiamond', 'chosenCoupon', 'vip']),\n    ...mapState(['isPc', 'isMobile', 'IS_CHECKOUT_SDK', 'IS_CHECKOUT_SDK_V2']),\n    ...mapState('userinfo', ['isLogin']),\n    ...mapState('gameinfo', ['gameCode', 'isKOA', 'isSS']),\n    ...mapState('functionSwitch', ['showMobilePolicy', 'boon'])\n  },\n  data () {\n    return {\n      requestLoading: false\n    }\n  },\n  created () {\n    this.$root.$on('adyenInitError', () => this.purchaseGoods(1))\n\n    location.search && history.pushState({}, '', location.pathname)\n  }\n}\n", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('common-part',{class:[_vm.$store.state.gameinfo.gameCode, 'channel-part-wrapper', _vm.$gameName, { sdk: _vm.$store.state.IS_CHECKOUT_SDK }],attrs:{\"label-font\":_vm.$t('channelChosen'),\"id\":\"channel-part-wrapper\"}},[_c('div',{staticClass:\"channel-list\"},[(_vm.calChannelList.length)?_vm._l((_vm.calChannelList),function(channel,index){return _c('div',{key:channel.FE_CHANNEL_ID,staticClass:\"channel-btn\",class:[{'channel-chosen__active': channel.FE_CHANNEL_ID === _vm.chosenChannel.FE_CHANNEL_ID}],on:{\"click\":function($event){return _vm.toggleStatus(index)}}},[_c('div',{directives:[{name:\"lazy\",rawName:\"v-lazy:backgroundImage\",value:(channel.icon_url),expression:\"channel.icon_url\",arg:\"backgroundImage\"}],staticClass:\"image common-fade-in\"}),(channel.subscript || _vm.whetherShowVipBonus(channel))?_c('div',{class:['recommendation', `recommendation-REC`]},[_c('span',{staticClass:\"blank\"}),(_vm.whetherShowVipBonus(channel))?_c('span',{staticClass:\"bonus-description\"},[_vm._v(\" VIP +\"+_vm._s(_vm.vip.channelBonus * (_vm.chosenDiamond.type === 2 ? _vm.chosenDiamond.totalDiamond : _vm.chosenDiamond.coin))+\" \"),_c('i')]):_c('span',{staticClass:\"txt\"},[_vm._v(_vm._s(_vm.$t('recommend-txt')))])]):_vm._e()])}):_c('div',{staticClass:\"empty\"},[_vm._v(_vm._s(_vm.$t('nothingHere')))])],2)])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <common-part :label-font=\"$t('channelChosen')\"  :class=\"[$store.state.gameinfo.gameCode, 'channel-part-wrapper', $gameName, { sdk: $store.state.IS_CHECKOUT_SDK }]\" id=\"channel-part-wrapper\">\n    <div class=\"channel-list\">\n      <template v-if=\"calChannelList.length\">\n        <div v-for=\"(channel, index) in calChannelList\"\n             @click=\"toggleStatus(index)\"\n             :key=\"channel.FE_CHANNEL_ID\"\n             :class=\"[{'channel-chosen__active': channel.FE_CHANNEL_ID === chosenChannel.FE_CHANNEL_ID}]\"\n             class=\" channel-btn\">\n          <div class=\"image common-fade-in\" v-lazy:backgroundImage='channel.icon_url'></div>\n\n          <!-- initVipInfo不调用就不会展示-->\n<!--          <transition name=\"bonus\">-->\n<!--            <div class=\"bonus\" v-if=\"whetherShowVipBonus(channel)\">-->\n<!--              <span class=\"bonus-description\">-->\n<!--                VIP {{ $t('bonus_tips') }} +{{ vip.channelBonus * (chosenDiamond.type === 2 ? chosenDiamond.totalDiamond : chosenDiamond.coin) }}-->\n<!--                <i></i>-->\n<!--              </span>-->\n<!--            </div>-->\n<!--          </transition>-->\n\n          <div v-if=\"channel.subscript || whetherShowVipBonus(channel)\" :class=\"['recommendation', `recommendation-REC`]\">\n            <span class=\"blank\"></span>\n            <span v-if=\"whetherShowVipBonus(channel)\" class=\"bonus-description\">\n                VIP +{{ vip.channelBonus * (chosenDiamond.type === 2 ? chosenDiamond.totalDiamond : chosenDiamond.coin) }}\n                <i></i>\n              </span>\n            <span v-else class=\"txt\">{{ $t('recommend-txt') }}</span>\n          </div>\n        </div>\n      </template>\n      <div v-else class=\"empty\">{{ $t('nothingHere') }}</div>\n    </div>\n  </common-part>\n</template>\n\n<script>\nimport CommonPart from '@/components/common/CommonPart.vue'\nimport { getTokenChannelList, getLastChosenChannel } from '@/server'\nimport { mapState } from 'vuex'\nimport { isWx } from '@/utils/utils'\n\nexport default {\n  name: 'ChannelChoose',\n  components: { CommonPart },\n  props: {\n    activity: {\n      type: Object,\n      default: () => {}\n    }\n  },\n  data () {\n    return {\n      channelList: [],\n\n      unwatch: undefined,\n      isUserChosen: false\n    }\n  },\n  computed: {\n    ...mapState(['urlParams', 'isArZone', 'currencyUnit']),\n    ...mapState('formdata', ['chosenChannel', 'chosenDiamond', 'chosenCoupon', 'vip', 'isInit', 'isFirstPayUsed']),\n    whetherShowVipBonus () {\n      return channelInfo =>\n        this.isInit &&\n        this.vip.discountSubChannelId.includes(channelInfo.sub_channel_id) &&\n        this.vip.channelBonus &&\n        this.isFirstPayUsed &&\n        !this.chosenCoupon.FE_INDEX\n    },\n    calChannelList () {\n      const someChannelNeedHide = this.$store.getters['riskPolicy/hideSomeChannel'] || []\n      if (someChannelNeedHide.length) {\n        if (someChannelNeedHide.includes(this.chosenChannel.channel_id)) this.$store.commit('formdata/resetChannel')\n        return this.channelList.filter(item => !someChannelNeedHide.includes(item.channel_id))\n      }\n      return this.channelList\n    }\n  },\n  methods: {\n    loadChannelList () {\n      const testParam = {\n        currency: this.urlParams.cr,\n        price: +this.chosenDiamond.price,\n        product_id: this.chosenDiamond.product_id\n      }\n\n      const chosenCoupon = this.$store.state.formdata.chosenCoupon\n      if (chosenCoupon.FE_INDEX) {\n        if (chosenCoupon.feType === 'discount_coupon') testParam.price = chosenCoupon.discount_price\n        if (chosenCoupon.feType === 'cash_coupon') testParam.price = chosenCoupon.price\n        if (chosenCoupon.feType === 'first_pay') testParam.price = chosenCoupon.discount_price\n      }\n      if (this.$store.getters['formdata/takeEffectDefaultDiscount']) {\n        const arr = ['BDT', 'CLP', 'COP', 'CRC', 'DZD', 'HUF', 'IDR', 'INR', 'IQD', 'JPY', 'KES', 'KRW', 'KZT', 'LBP', 'LKR', 'MMK', 'NGN', 'PHP', 'PKR', 'PYG', 'RSD', 'RUB', 'THB', 'TWD', 'TZS', 'VND']\n        let finalMoney = testParam.price * 0.95\n        if (arr.includes(this.chosenDiamond.currency)) finalMoney = Math.ceil(finalMoney)\n        else finalMoney = finalMoney.toFixed(2)\n        testParam.price = +finalMoney\n      }\n\n      // 兼容自定义的档位\n      const { type, nowPrice } = this.chosenDiamond\n      if (type === 2) testParam.price = +nowPrice\n\n      this.$loading.show()\n\n      this.$store.commit('formdata/resetChannel')\n\n      getTokenChannelList(testParam)\n        .then(({ data, code, message }) => {\n          if (code === 0) {\n            this.channelList = this.adapterChannel(data)\n            setTimeout(() => {\n              const { lastChannel } = this.$store.state.userinfo\n              if (lastChannel) {\n                const lastList = this.calChannelList.filter(item => (item.channel_id === lastChannel.channel_id) && (lastChannel.sub_channel_id === item.sub_channel_id))\n                if (lastList && lastList.length) this.$store.commit('formdata/setChosenChannel', lastList[0])\n\n                const lastList2 = this.calChannelList.filter(item => (item.channel_name === lastChannel.channel_name))\n                if (lastList2 && lastList2.length) return this.$store.commit('formdata/setChosenChannel', lastList2[0])\n              }\n            }, 0)\n            if (this.$gcbk('switch.enableAnimation', false) && !window.channelFlag && this.$store.state.isPc) {\n              window.channelFlag = 1\n              this.$nextTick(() => {\n                gsap && gsap.from('.channel-list', { height: 0, duration: .4, clearProps: 'height' })\n              })\n            }\n\n          } else {\n            this.$toast.err(this.$t('fetchChannelError'))\n          }\n        })\n        .finally(() => this.$loading.hide())\n    },\n    adapterChannel (list) {\n      if (this.$store.state.gameinfo.isCn) {\n        let delSubChannel\n        if (isWx) {\n          delSubChannel = ['WxpayJSAPI', 'Alipaywap']\n        } else if (window.isMobile) {\n          delSubChannel = ['WxpayMWEB', 'Alipaywap']\n        } else {\n          delSubChannel = ['WxpayPcNATIVE', 'Alipaypc']\n        }\n        list = list.filter(item => {\n          if (item.channel_id !== 'wxpay' && item.channel_id !== 'alipay') return true\n          return delSubChannel.indexOf(item.channel_name + item.sub_channel_id) > -1\n        })\n      }\n\n      return list.map(item => {\n        item.FE_CHANNEL_ID = `${item.channel_id}__${item.channel_name}`\n        return item\n      })\n    },\n    toggleStatus (index) {\n      this.isUserChosen = true\n\n      const newChosen = this.calChannelList[index]\n      const oldChosen = this.chosenChannel\n      if (newChosen.FE_CHANNEL_ID === oldChosen.FE_CHANNEL_ID) return null\n\n      this.$store.commit('formdata/setChosenChannel', this.calChannelList[index])\n    },\n    initLastChannel () {\n      getLastChosenChannel()\n        .then(res => {\n          const { code, data } = res\n          if (code === 0) {\n            this.$store.commit('userinfo/saveLastChannelInfo', data)\n\n            // 如果获取到的渠道信息比获取到的 上次选中渠道信息 早，且用户已经选中了新的渠道，就不需要使用上次的信息来初始化本次渠道选择。\n            if (this.isUserChosen) return null\n            const { lastChannel } = this.$store.state.userinfo\n            if (lastChannel) {\n              const lastList = this.calChannelList.filter(item => (item.channel_id === lastChannel.channel_id) && (lastChannel.sub_channel_id === item.sub_channel_id))\n              if (lastList && lastList.length) return this.$store.commit('formdata/setChosenChannel', lastList[0])\n            }\n          }\n        })\n    }\n  },\n  created () {\n    // const objUrl = '$store.state.formdata.chosenDiamond.product_id'\n    // this.unwatch = this.$watch(objUrl, value => value && this.loadChannelList(), { immediate: true })\n    // 选中优惠券价格变化也需要更新渠道\n    this.$root.$on('couponChoose', () => this.loadChannelList())\n    // 优惠券初始化完成后需要更新渠道\n    this.$root.$on('activityInitEnd', () => this.loadChannelList())\n\n    this.$root.$on('loginEnd', state => {\n      // 登录成功，初始化last channel\n      if (state === 1) this.initLastChannel()\n    })\n  },\n  beforeDestroy () {\n    this.unwatch && this.unwatch()\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n@use \"~@/utils/utils.scss\" as utils;\n.channel-list {\n  width: 100%;\n  direction: ltr;\n  display: flex;\n  flex-wrap: wrap;\n  @include utils.setMobileContent{\n    width: calc(100% + 18px);\n    position: relative;\n    left: -5px;\n    top: -5px;\n\n    .channel-btn{\n      margin: 5px;\n    }\n  }\n  @include utils.setPcContent{\n    width: calc(100% + 24PX);\n    position: relative;\n    //left: -12PX;\n  }\n\n  .channel-btn {\n    background: #FFFFFF;\n    position: relative;\n    cursor: pointer;\n    vertical-align: top;\n    width: 216px;\n    height: 80px;\n    border-radius: 8px;\n    border: 2px solid transparent;\n    box-sizing: border-box;\n\n    &.channel-chosen__active {\n      border: 2px solid #FF5E0F;\n\n      &:after {\n        display: inline-block;\n        content: ' ';\n        position: absolute;\n        right: -2px;\n        top: -2px;\n        @include utils.bgCenter('common/channel/choose-active.png', 36px, 36px);\n      }\n    }\n\n    .image {\n      width: 194px;\n      height: 64px;\n      position: absolute;\n      left: 50%;\n      top: 50%;\n      transform: translate(-50%, -50%);\n      background-repeat: no-repeat;\n      background-position: center center;\n      background-size: contain;\n      overflow: hidden;\n    }\n\n    .recommendation-REC{\n      position: absolute;\n      top: -17px;\n      left: -8px;\n      display: flex;\n      align-items: flex-end;\n      justify-content: space-between;\n      z-index: 2;\n\n      @include utils.bgCenter('common/channel/recommend-logo-m.png', 118px, 33px);\n\n      .blank{\n        width: 30px;\n        height: 100%;\n      }\n\n      .txt{\n        font-size: 15px;\n        font-family: SourceHanSansCN-Medium, SourceHanSansCN;\n        font-weight: 500;\n        color: #FFFFFF;\n        line-height: 21px;\n        text-shadow: 0px 1px 0px rgba(0,0,0,0.5);\n        flex-grow: 1;\n        height: 23px;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        white-space: nowrap;\n      }\n\n      .bonus-description{\n        font-size: 12px;\n        font-family: PingFangSC-Semibold, PingFang SC;\n        font-weight: 600;\n        color: #FFFFFF;\n        line-height: 21px;\n        flex-grow: 1;\n        height: 23px;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        white-space: nowrap;\n\n        i{\n          @include utils.bgCenter('koa/diamond/diamond.png', 10px, 8px);\n          display: inline-block;\n          margin:0 1px;\n        }\n      }\n    }\n\n    @include utils.setPcContent{\n      width: 134PX;\n      height: 51PX;\n      border-radius: 8PX;\n      border: 2PX solid transparent;\n\n      .image{\n        width: 120PX;\n        height: 40PX;\n      }\n\n      $size: 9.8PX;\n      &:nth-of-type(n+6){\n        margin-top: $size;\n      }\n      &:not(:nth-of-type(5n + 1)) {\n        margin-left: $size;\n      }\n\n      &.channel-chosen__active{\n        border-width: 2PX;\n\n        &:after{\n          display: inline-block;\n          content: ' ';\n          position: absolute;\n          right: -1PX;\n          top: -1PX;\n          @include utils.bgCenter('common/channel/choose-active.png', 20PX, 20PX);\n        }\n      }\n\n      .recommendation-REC{\n        top: -15px;\n        left: -9px;\n\n        @include utils.bgCenter('common/channel/recommend-logo.png', 86px, 25px);\n\n        .blank{\n          width: 22px;\n        }\n\n        .txt{\n          font-size: 12px;\n          line-height: 18px;\n          text-shadow: 0px 1px 0px rgba(0,0,0,0.5);\n          flex-grow: 1;\n          height: 18px;\n        }\n\n        .bonus-description{\n          font-size: 12px;\n          line-height: 18px;\n          height: 18px;\n        }\n      }\n    }\n  }\n\n  .empty {\n    font-size: 18px;\n    color: #8C8C8C;\n    margin-left: 10px;\n\n    @include utils.setPcContent{\n      font-size: 14PX;\n      margin-left: 12PX;\n      line-height: 30PX;\n    }\n  }\n}\n.KOA{\n  .channel-list{\n    .channel-btn{\n      border-radius: 0;\n      position: relative;\n      &.channel-chosen__active{\n        &:before {\n          height: 10px;\n          width: 10px;\n          display: inline-block;\n          content: ' ';\n          background-color: #FF5E0F;\n          right: -2px;\n          top: -2px;\n          position: absolute;\n          z-index: 0;\n        }\n      }\n    }\n  }\n  @include utils.setPcContent{\n    .channel-list{\n      .channel-btn{\n        border-radius: 0;\n        position: relative;\n        &.channel-chosen__active{\n          &:before {\n            height: 5PX;\n            width: 5PX;\n            display: inline-block;\n            content: ' ';\n            background-color: #FF5E0F;\n            z-index: 1;\n            right: -2PX;\n            top: -2PX;\n            position: absolute;\n          }\n        }\n      }\n    }\n  }\n}\n\n.channel-part-wrapper.dc{\n  .channel-btn {\n    background: #E7EDFF;\n    border-radius: 0;\n\n    &.channel-chosen__active{\n      border: none;\n\n      &:after{\n        @include utils.bgCenterForDC('channel/channel-chosen-bg-m.png', 100%, 100%);\n        top: 0;\n        left: 0;\n        z-index: 0;\n      }\n    }\n  }\n}\n.channel-part-wrapper.sdk{\n  @include utils.setPcContent{\n    .channel-list{\n      width: 100%;\n      .channel-btn{\n        zoom: 1.109;\n        /*大小放大1.11倍*/\n\n        margin-top: 0;\n        margin-left: 0;\n        $size: 7.8PX;\n        &:nth-of-type(n+7){\n          margin-top: $size;\n        }\n        &:not(:nth-of-type(6n + 1)) {\n          margin-left: $size;\n        }\n      }\n      .empty {\n        margin-left: 0;\n      }\n    }\n  }\n}\n</style>\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ChannelChoose.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ChannelChoose.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./ChannelChoose.vue?vue&type=template&id=97029186&scoped=true\"\nimport script from \"./ChannelChoose.vue?vue&type=script&lang=js\"\nexport * from \"./ChannelChoose.vue?vue&type=script&lang=js\"\nimport style0 from \"./ChannelChoose.vue?vue&type=style&index=0&id=97029186&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"97029186\",\n  null\n  \n)\n\nexport default component.exports", "'use strict';\nvar defineBuiltIn = require('../internals/define-built-in');\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar toString = require('../internals/to-string');\nvar validateArgumentsLength = require('../internals/validate-arguments-length');\n\nvar $URLSearchParams = URLSearchParams;\nvar URLSearchParamsPrototype = $URLSearchParams.prototype;\nvar getAll = uncurryThis(URLSearchParamsPrototype.getAll);\nvar $has = uncurryThis(URLSearchParamsPrototype.has);\nvar params = new $URLSearchParams('a=1');\n\n// `undefined` case is a Chromium 117 bug\n// https://bugs.chromium.org/p/v8/issues/detail?id=14222\nif (params.has('a', 2) || !params.has('a', undefined)) {\n  defineBuiltIn(URLSearchParamsPrototype, 'has', function has(name /* , value */) {\n    var length = arguments.length;\n    var $value = length < 2 ? undefined : arguments[1];\n    if (length && $value === undefined) return $has(this, name);\n    var values = getAll(this, name); // also validates `this`\n    validateArgumentsLength(length, 1);\n    var value = toString($value);\n    var index = 0;\n    while (index < values.length) {\n      if (values[index++] === value) return true;\n    } return false;\n  }, { enumerable: true, unsafe: true });\n}\n", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"common-part-wrapper\",class:[_vm.$gameName, { sdk: _vm.$store.state.IS_CHECKOUT_SDK }]},[_c('div',{staticClass:\"label\"},[_vm._t(\"label\",function(){return [_vm._v(_vm._s(_vm.labelFont))]})],2),_c('div',{staticClass:\"body\"},[_vm._t(\"default\")],2)])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <div class=\"common-part-wrapper\" :class=\"[$gameName, { sdk: $store.state.IS_CHECKOUT_SDK }]\">\n    <div class=\"label\">\n      <slot name=\"label\">{{ labelFont }}</slot>\n    </div>\n    <div class=\"body\">\n      <slot></slot>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'CommonPart',\n  props: {\n    labelFont: {\n      type: String,\n      default: ''\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n@use \"~@/utils/utils\" as utils;\n\n.common-part-wrapper{\n  display: flex;\n  width: 100%;\n  margin: 0 auto;\n\n  @include utils.setPropByBp(\n    $m: (flex-direction: column,width: calc(100% - 80px),margin-top: 24px),\n    $p: (margin-top: 35px)\n  );\n  .label{\n    font-family: PingFangSC-Semibold, PingFang SC;\n    font-weight: 600;\n    color: #CACACA;\n    flex-shrink: 0;\n\n    @include utils.setPropByBp(\n      $m: (font-size: 28px,line-height: 40px, margin-bottom: 8px,text-align: left),\n      $p: (width: 233px,font-size: 18px,padding-right: 28px,line-height: 30px,text-align: right)\n    );\n  }\n\n  .body{\n    flex: 1;\n  }\n}\n\n.common-part-wrapper.dc{\n  .label{\n    color: #F4FBFF;\n  }\n}\n.common-part-wrapper.sdk{\n  flex-direction: column;\n  max-width: 940PX;\n  margin-top: 36px;\n\n  .label{\n    text-align: left;\n    margin-bottom: 16px;\n    width: 100%;\n  }\n\n  @include utils.setPcContent{\n    margin-top: 24px;\n\n    .label{\n      margin-bottom: 8px;\n    }\n  }\n}\n</style>\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./CommonPart.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./CommonPart.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./CommonPart.vue?vue&type=template&id=c17bc992&scoped=true\"\nimport script from \"./CommonPart.vue?vue&type=script&lang=js\"\nexport * from \"./CommonPart.vue?vue&type=script&lang=js\"\nimport style0 from \"./CommonPart.vue?vue&type=style&index=0&id=c17bc992&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"c17bc992\",\n  null\n  \n)\n\nexport default component.exports", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./CouponChooseList.vue?vue&type=style&index=0&id=3d2a8ef0&prod&scoped=true&lang=scss\"", "export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ChannelChoose.vue?vue&type=style&index=0&id=97029186&prod&scoped=true&lang=scss\"", "'use strict';\nvar DESCRIPTORS = require('../internals/descriptors');\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar defineBuiltInAccessor = require('../internals/define-built-in-accessor');\n\nvar URLSearchParamsPrototype = URLSearchParams.prototype;\nvar forEach = uncurryThis(URLSearchParamsPrototype.forEach);\n\n// `URLSearchParams.prototype.size` getter\n// https://github.com/whatwg/url/pull/734\nif (DESCRIPTORS && !('size' in URLSearchParamsPrototype)) {\n  defineBuiltInAccessor(URLSearchParamsPrototype, 'size', {\n    get: function size() {\n      var count = 0;\n      forEach(this, function () { count++; });\n      return count;\n    },\n    configurable: true,\n    enumerable: true\n  });\n}\n", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./CouponChoosePop.vue?vue&type=style&index=0&id=26605e4f&prod&scoped=true&lang=scss\"", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('common-part',{class:['wrapper', _vm.$gameName ],attrs:{\"id\":\"login-part-wrapper\",\"label-font\":_vm.userinfo.isLogin? '':_vm.$vt('loginPlaceHolder')}},[(_vm.userinfo.isLogin)?_c('div',{staticClass:\"login-status__login\"},[_c('div',{directives:[{name:\"lazy\",rawName:\"v-lazy:backgroundImage\",value:(_vm.userinfo.icon),expression:\"userinfo.icon\",arg:\"backgroundImage\"}],staticClass:\"avatar lazy\"}),_c('div',{staticClass:\"user-info\"},[_c('div',{staticClass:\"row-1\"},[_c('div',{staticClass:\"name\"},[_vm._v(_vm._s(_vm.userinfo.name))]),(!_vm.$store.state.isPCSDK)?_c('div',{staticClass:\"toggle click-btn\",on:{\"click\":_vm.logout}},[(_vm.$gameName !== 'ssv')?[_vm._v(_vm._s(_vm.$t('switch_account')))]:_vm._e()],2):_vm._e()]),(_vm.isKOA && _vm.vip.isInit)?_c('div',{staticClass:\"row-koa\"},[_c('div',{staticClass:\"grade\"},[_vm._v(\"VIP \"+_vm._s(_vm.vip.level))]),_c('div',{staticClass:\"btn-jump\"},[_c('a',{attrs:{\"href\":_vm.vipIntroducePageUrl,\"target\":\"_blank\"}},[_vm._v(_vm._s(_vm.$t('title_s_vip')))])])]):_vm._e(),_c('div',{staticClass:\"row-2\"},[_c('span',{staticClass:\"leave\"},[_vm._v(_vm._s(_vm.$t('user_level',{0: _vm.userinfo.level})))]),_c('span',{staticClass:\"server\"},[_vm._v(_vm._s(_vm.$t('user_server',{0: _vm.userinfo.server})))])])])]):[_c('div',{staticClass:\"login-status__not-login\",class:[{'emphasize': _vm.focusEmphasize}]},[_c('div',{staticClass:\"login-input-wrapper\"},[_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.uid),expression:\"uid\"}],attrs:{\"type\":\"number\",\"id\":\"uidInput\",\"placeholder\":_vm.focusEmphasize ? '' : _vm.$vt('loginPlaceHolder')},domProps:{\"value\":(_vm.uid)},on:{\"input\":function($event){if($event.target.composing)return;_vm.uid=$event.target.value}}}),_c('i',{on:{\"click\":function($event){$event.stopPropagation();_vm.isShowTipsImg = true},\"mouseenter\":function($event){$event.stopPropagation();_vm.isShowTipsImg = true}}}),_c('img',{staticClass:\"tips-img\",class:{ active: _vm.isShowTipsImg },attrs:{\"src\":_vm.$imageLoader('uidTips'),\"alt\":\"\"},on:{\"click\":function($event){$event.stopPropagation();}}})]),_c('div',{class:['btn-login', 'click-btn', 'btn',{ 'btn_disable': !_vm.uid}, {'disable': !_vm.uid}],on:{\"click\":function($event){return _vm.query()}}},[_vm._v(_vm._s(_vm.$t('login')))]),(_vm.isKOA)?_c('div',{staticClass:\"vip-jump-wrapper\"},[_c('a',{attrs:{\"href\":_vm.vipIntroducePageUrl,\"target\":\"_blank\"}},[_vm._v(_vm._s(_vm.$t('title_s_vip')))])]):_vm._e()]),(_vm.focusEmphasize)?_c('div',{staticClass:\"tip tip-please-input\"},[_vm._v(_vm._s(_vm.$vt('loginPlaceHolder')))]):_vm._e(),(!_vm.focusEmphasize)?_c('div',{staticClass:\"tip tip-uid-get\"},[_vm._v(_vm._s(_vm.$t('where-uid-is')))]):_vm._e()]],2)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "import CryptoJS from 'crypto-js'\n\nconst userKey = CryptoJS.enc.Utf8.parse('84uyzdgah9m#m9x4qzu&ye53')\n\nconst ivHex = userKey.clone()\nivHex.sigBytes = 16\nivHex.words.splice(4)\n\nexport function GetAesResult(uidplus) {\n    return encodeURIComponent(CryptoJS.AES.encrypt(CryptoJS.enc.Utf8.parse(uidplus), userKey, {\n        iv: ivHex,\n        mode: CryptoJS.mode.CBC,\n        padding: CryptoJS.pad.Pkcs7,\n    }).toString())\n}", "<template>\n  <common-part :class=\"['wrapper', $gameName ]\" id=\"login-part-wrapper\" :label-font=\"userinfo.isLogin? '':$vt('loginPlaceHolder')\">\n    <div v-if=\"userinfo.isLogin\" class=\"login-status__login\">\n      <div class=\"avatar lazy\" v-lazy:backgroundImage=\"userinfo.icon\"></div>\n      <div class=\"user-info\">\n        <div class=\"row-1\">\n          <div class=\"name\">{{ userinfo.name }}</div>\n          <div v-if=\"!$store.state.isPCSDK\" class=\"toggle click-btn\" @click=\"logout\">\n            <template v-if=\"$gameName !== 'ssv'\">{{ $t('switch_account') }}</template>\n          </div>\n        </div>\n        <div v-if=\"isKOA && vip.isInit\" class=\"row-koa\">\n          <div class=\"grade\">VIP {{ vip.level }}</div>\n          <div class=\"btn-jump\">\n            <a :href=\"vipIntroducePageUrl\" target=\"_blank\">{{ $t('title_s_vip') }}</a>\n          </div>\n        </div>\n        <div class=\"row-2\">\n          <span class=\"leave\">{{ $t('user_level',{0: userinfo.level}) }}</span>\n          <span class=\"server\">{{ $t('user_server',{0: userinfo.server}) }}</span>\n        </div>\n      </div>\n    </div>\n    <template v-else>\n      <div class=\"login-status__not-login\" :class=\"[{'emphasize': focusEmphasize}]\">\n        <div class=\"login-input-wrapper\">\n          <input type=\"number\" v-model=\"uid\" id=\"uidInput\" :placeholder=\"focusEmphasize ? '' : $vt('loginPlaceHolder')\">\n          <i @click.stop=\"isShowTipsImg = true\" @mouseenter.stop=\"isShowTipsImg = true\"></i>\n          <img class=\"tips-img\" @click.stop :class=\"{ active: isShowTipsImg }\" :src=\"$imageLoader('uidTips')\" alt=\"\">\n        </div>\n        <div :class=\"['btn-login', 'click-btn', 'btn',{ 'btn_disable': !uid}, {'disable': !uid}]\" @click=\"query()\">{{ $t('login') }}</div>\n\n        <!--只有koa有vip -->\n        <div v-if=\"isKOA\" class=\"vip-jump-wrapper\">\n          <a :href=\"vipIntroducePageUrl\" target=\"_blank\">{{ $t('title_s_vip') }}</a>\n        </div>\n      </div>\n\n      <div v-if=\"focusEmphasize\" class=\"tip tip-please-input\">{{ $vt('loginPlaceHolder') }}</div>\n      <div v-if=\"!focusEmphasize\" class=\"tip tip-uid-get\">{{ $t('where-uid-is') }}</div>\n    </template>\n  </common-part>\n</template>\n\n<script>\nimport CommonPart from '@/components/common/CommonPart.vue'\nimport { getUserInfoForToken, getAmeDo, getCommonInfo, sendCode } from '@/server'\nimport { mapState } from 'vuex'\nimport { logForClickLogin, logForLoginSuccess } from '@/utils/logHelper'\nimport { StorageUtils } from '@/utils/storageUtils'\nimport { dealSmDeviceId, decryptAES } from '@/utils/utils'\nimport { GetAesResult } from '@/utils/uidEncrypto'\n\nexport default {\n  name: 'LoginModule',\n  components: { CommonPart },\n  data () {\n    return {\n      uid: '',\n      isShowTipsImg: false,\n\n      focusEmphasize: false\n    }\n  },\n  computed: {\n    ...mapState(['userinfo']),\n    ...mapState('gameinfo', ['whiteChannel', 'blackChannel', 'greyChannel', 'gameCode', 'isKOA', 'isROMCP']),\n    ...mapState('functionSwitch', ['loginValidation']),\n    ...mapState(['isPc']),\n    ...mapState('formdata', ['vip']),\n    vipIntroducePageUrl () {\n      return process.env[`VUE_APP_VipIntroducePageUrl_${this.$gameName}`] + '?l=' + this.$i18n.locale\n    }\n  },\n  methods: {\n    /* 通用 */\n    async query (openid) {\n      if (!(this.uid || openid)) return\n      const state = this.$store.state\n      const params = {\n        hideErrToast: true,\n        game_project: state.gameProject\n      }\n      if (openid && typeof openid === 'string') params.openid = openid\n      if (this.uid) {\n        localStorage.removeItem('openid')\n        params.uid = +this.uid\n\n        if (this.$store.state.gameinfo.gameCode === 'KOA') {\n          params.ticket = GetAesResult(`${this.uid}|${Math.floor(Date.now() / 1000)}`)\n          params.fopenid = GetAesResult(`${Math.ceil(Date.now() / 1000)}|${this.uid}`)\n        }\n      }\n\n      if (this.uid) logForClickLogin(this.uid)\n\n      try {\n        this.$loading.show()\n        let { data = {}, code } = await getUserInfoForToken(params)\n        this.$loading.hide()\n        switch (code) {\n          case 0: {\n            try {\n              const secretKey = this.$gcbk('ids.secretKey')\n              if (typeof data === 'string') data = JSON.parse(decryptAES(data, secretKey))\n            } catch (e) {\n              console.error(`解密失败！${this.uid}`)\n            }\n            /* 白名单 */\n            const whiteChannel = this.whiteChannel || []\n            if (whiteChannel.length && !whiteChannel.includes(data.pkg_channel)) {\n              this.uid = ''\n              return this.$toast.err(this.$t('pkg_not_allow')) // ss 直接提示\n              // return this.$root.$emit('showPop', 'IosForbidden', { openid: data.openid }) // koa cn跳走 todo 未兼容\n            }\n            /* 灰名单 跳转到其他商城 */\n            const greyChannel = this.greyChannel || []\n            const { pkg_channel: pkgChannel, openid } = data\n            if (greyChannel.length) {\n              for (const greyItem of greyChannel) {\n                const { channel, to } = greyItem\n                if (channel.includes(pkgChannel)) {\n                  localStorage.removeItem('openid')\n                  window.location.href = `${to}?openid=${encodeURIComponent(openid)}`\n                  return\n                }\n              }\n            }\n            // 黑名单 禁止登录\n            const blackChannel = this.blackChannel || []\n            if (blackChannel.length && blackChannel.includes(pkgChannel)) return this.$toast.err(this.$t('pkg_not_allow'))\n\n            // if (getPlatform === 'ios') return this.$root.$emit('showPop', 'IosForbidden')\n            // if (this.uid) {\n            //   localStorage.setItem('openid', data.openid)\n            //   window.location.reload()\n            //   return\n            // }\n\n            // 安全验证\n            const isSDK = this.$store.state.IS_CHECKOUT_SDK || this.$store.state.isPCSDK // pc/直购收银台不需要登录验证\n            if (this.loginValidation && !isSDK) {\n              const isValidation = await this.goValidation(data)\n              if (!isValidation) return null\n            }\n            this.$store.commit('userinfo/setUserInfo', data)\n\n            if (this.isKOA || this.isROMCP) await this.loadVipStatus()\n            this.judgePop()\n\n            // 初始化风控规则相关\n            this.initRiskPolicy()\n\n            logForLoginSuccess()\n\n            this.$root.$emit('loginSuccess') // 老代码在用\n            this.$root.$emit('loginEnd', 1)\n            this.uid = ''\n            if (this.$gcbk('switch.enableAnimation', false)) {\n              setTimeout(\n                () => gsap && gsap.from('.login-status__login', { opacity: 0, xPercent: -5, duration: .5 }),\n                0\n              )\n            }\n            break\n          }\n          case 2: {\n            this.$tips.show(this.$t('maintenance_tips'))\n            break\n          }\n          case 1000: case 1002: case 4001: case 1007: {\n            const msgMap = {\n              1000: 'toast_invalid_param',\n              1002: 'text_no_role',\n              4001: 'toast_black_user',\n              1007: 'toast_black_country'\n            }\n            this.$toast.err(this.$t(msgMap[code]))\n            if (code === 1002 && !params.uid) this.logout() // 如果使用uid登录，不需要刷新\n            // logOnRoleCheck(msgMap[code].replace(/toast_|text_/g, ''), +this.valueId)\n            break\n          }\n          case 2001: {\n            this.$toast.err(this.$t('account_login_fail'))\n            setTimeout(() => this.$store.commit('userinfo/logout'), 1000)\n            break\n          }\n          default: {\n            this.$toast.err(this.$t('account_login_fail'))\n          }\n        }\n      } catch (error) {\n        if (error.message.includes('timeout of')) this.$loading.hide()\n      }\n    },\n    logout () {\n      this.$store.commit('userinfo/logout')\n    },\n    judgePop () {\n      if (window.location.pathname.includes('/common/')) return\n      const flag = window.localStorage.getItem('isWhatDiamondPop')\n      if (!flag) {\n        window.localStorage.setItem('isWhatDiamondPop', 'true')\n        this.$root.$emit('showWhatIsDiamondPop')\n      }\n    },\n    initRiskPolicy () {\n      const emit = this.$root.$emit.bind(this.$root)\n\n      this.$loading.show()\n      getCommonInfo()\n        .then(res => {\n          const { code, data } = res\n          if (code === 0) {\n            this.$store.commit('riskPolicy/init', { list: data.user_risk_list, emit })\n            this.$store.commit('vb/savePrefixChannel', data.reserve_card_channel_gray_ratio)\n            this.$store.commit('vb/resetBuiltInCashierStatus', data.switch_global_config)\n            this.$store.commit('formdata/switchToggle', data.change_coupon_enable)\n            this.$store.commit('functionSwitch/updateFunctionInfo', data)\n            this.$root.$emit('updateSpecialDiamond', data.point_card_product)\n          }\n        })\n        .finally(() => {\n          this.$loading.hide()\n        })\n    },\n    async goValidation (userinfo) {\n      let deviceId = ''\n      dealSmDeviceId((id) => {\n        deviceId = id\n      })\n\n      return new Promise((resolve, reject) => {\n        this.$loading.show()\n        sendCode({ fp_device_id: deviceId, openid: userinfo.openid })\n          .then(res => {\n            let { code, data } = res\n\n            if (!data) data = {}\n            // todo 注释\n            // data.remaining_verification_attempts = 10\n            // code = 0\n\n            data.username = userinfo.name\n            data.openid = userinfo.openid\n            switch (code) {\n              case 0: case 5004: {\n                data.successCb = () => resolve(true)\n                data.failCb = () => resolve(false)\n                this.$root.$emit('saveValidation', data)\n                break\n              }\n              case 5011: case 5001: case 5002: {\n                resolve(true)\n                break\n              }\n              // case 5003: {\n              //   this.$toast.err('今天验证码已达上限')\n              //   resolve(false)\n              //   break\n              // }\n              default: {\n                resolve(false)\n                this.$toast.err(this.$t('login-validation-error-text'))\n              }\n            }\n          })\n          .finally(() => this.$loading.hide())\n      })\n    },\n\n    /* koa相关 */\n    judgeAvatarPop () {\n      if (this.$store.state.IS_CHECKOUT_SDK) return\n      const avatarFlag = StorageUtils.getLocalStorage('isAvatarBonusPop')\n      const popDate = `${(new Date().getMonth() + 1)}/${(new Date().getDate())}`\n      // 头像框弹框\n      if (popDate !== avatarFlag) {\n        this.$root.$emit('showPop', 'AvatarBonusPop')\n        StorageUtils.setLocalStorage('isAvatarBonusPop', popDate)\n      }\n    },\n    async loadVipStatus () {\n      if (this.$store.state.IS_CHECKOUT_SDK_V2) return\n      const params = { p0: 'web', p1: 11, p2: 1075 }\n      this.$loading.show()\n      try {\n        const { code, data } = await getAmeDo(params)\n        // data = { exp: 0, level: 1, level_up_exp: { level: 2, pay_bonus: 0.02, recharge: 60000, vip_bonus: 0.02 }, pay_bonus: 0.03, recharge: 0, vip_bonus: 0.02 }\n        if (code === 0) this.$store.commit('formdata/initVipInfo', data)\n      } catch (error) {}\n      this.$loading.hide()\n\n      // this.judgeDailyReward() // 判断是否需要弹每日弹窗\n      if (this.vip.isNewUser) this.judgeAvatarPop()\n\n      let isFirstToast = false\n      this.$root.$on('availableTicketChosen', () => {\n        !isFirstToast && this.$toast.success(this.$t('bonus_coupon_mutually_exclusive'))\n        isFirstToast = true\n      })\n      this.$root.$on('TicketPopClose', () => (isFirstToast = false))\n    }\n  },\n  created () {\n    const openid = localStorage.getItem('openid')\n    if (openid) this.query(openid)\n    else {\n      this.$root.$emit('loginEnd', 0)\n      if (this.isKOA) this.judgeAvatarPop()\n      this.judgePop()\n    }\n    this.$root.$on('BodyClick', () => {\n      this.isShowTipsImg = false\n    })\n\n    this.$root.$on('ClickPayButNotLogin', () => {\n      this.focusEmphasize = true\n      const input = document.querySelector('#uidInput')\n      // input.scrollIntoView({ behavior: 'smooth' })\n      setTimeout(() => {\n        input.focus()\n      }, 300)\n\n      input.addEventListener('input', () => {\n        this.focusEmphasize = false\n      })\n    })\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n@use \"~@/utils/utils.scss\" as utils;\n.wrapper{\n  margin-top: 0;\n  .login-status__not-login{\n    display: flex;\n    align-items: center;\n\n    .login-input-wrapper{\n      position: relative;\n      width: 100%;\n      @include utils.setMobileContent{\n        flex-grow: 1;\n        margin-right: 44px;\n        border: 1px solid white;\n      }\n\n      input{\n        background-color: transparent;\n        appearance: none;\n        -webkit-appearance:none;\n        border: none;\n        font-family: PingFangSC-Regular, PingFang SC;\n        font-weight: 400;\n        color: #8C8C8C;\n        width: 100%;\n        @include utils.setMobileContent{\n          background-color: rgba(0,0,0,.3);\n          font-size: 23px;\n          line-height: 62px;\n          height: 62px;\n          width: 100%;\n          padding-right: 66px;\n          text-indent: 6px;\n          padding-left: 22px;\n        }\n\n        &:active, &:focus{\n          appearance: none;\n          -webkit-appearance:none;\n          border: none!important;\n          outline: none;\n        }\n      }\n      input::-webkit-outer-spin-button,\n      input::-webkit-inner-spin-button {\n        -webkit-appearance: none !important;\n        margin: 0;\n      }\n      input[type=number]{-moz-appearance:textfield;}\n\n      i{\n        display: inline-block;\n        position: absolute;\n        top: 50%;\n        transform: translateY(-50%);\n        cursor: pointer;\n        z-index: 0;\n        transition: all .3s;\n        right: 16px;\n        @include utils.bgCenter('koa/login/login-tips-btn.png', 31px, 31px);\n      }\n\n      .tips-img{\n        position: absolute;\n        cursor: default;\n        top: 0;\n        opacity: 0;\n        transition: all .3s;\n        pointer-events: none;\n        z-index: 1000;\n        display: inline-block;\n        width: 420px;\n        right: 25px;\n        &.active{\n          right: 0;\n          opacity: 1;\n        }\n      }\n    }\n\n    .btn-login{\n      background: #FF5E0F;\n      font-family: PingFangSC-Semibold, PingFang SC;\n      font-weight: 600;\n      color: #FFFFFF;\n\n      font-size: 26px;\n      height: 59px;\n      line-height: 59px;\n      border-radius: 8px;\n      padding: 0 30px;\n      white-space: nowrap;\n      cursor: pointer;\n      //transition: all .3s;\n\n      &.btn_disable{\n        opacity: .3;\n        cursor: not-allowed;\n      }\n    }\n\n    &.emphasize{\n      .login-input-wrapper{\n        border-color: #F83939;\n\n        input{\n          color: #F83939;\n        }\n\n        i {\n          background-image: url(\"~@/assets/common/login/login-tips-btn-red.png\");\n        }\n\n        @include utils.setMobileContent{\n          border: 1px solid opacify(#F83939, .3);\n          box-sizing: border-box;\n        }\n      }\n    }\n  }\n  .login-status__login{\n    display: flex;\n    align-items: center;\n    .avatar {\n      background-color: #F7F7F7;\n      border-radius: 50%;\n      flex-shrink: 0;\n      background-size: cover;\n      background-position: center center;\n      width: 130px;\n      height: 130px;\n      margin-right: 31px;\n\n      &[lazy=error], &[lazy=loading]{\n        opacity: .4;\n      }\n      &[lazy=loaded]{\n        transition: all .5s;\n      }\n    }\n\n    .user-info {\n      width: 0;\n      flex-grow: 1;\n\n      .row-1 {\n        display: flex;\n        align-items: center;\n        justify-content: flex-start;\n\n        .name {\n          font-family: PingFangSC-Semibold, PingFang SC;\n          font-weight: 600;\n          color: #FFFFFF;\n          font-size: 32px\n        }\n\n        .toggle {\n          font-family: PingFangSC-Regular, PingFang SC;\n          font-weight: 400;\n          color: #FF813C;\n          text-decoration: underline;\n          cursor: pointer;\n          font-size: 22px;\n          margin-left: 30px;\n        }\n      }\n\n      .row-2 {\n        font-family: PingFangSC-Semibold, PingFang SC;\n        font-weight: 600;\n        color: #BFBFBF;\n        font-size: 26px;\n        margin-top: 16px;\n\n        .server {\n          margin-left: 57px\n        }\n      }\n    }\n  }\n  .tip-uid-get{\n    font-size: 16px;\n    font-family: PingFangSC-Regular, PingFang SC;\n    font-weight: 400;\n    color: #A8A8A8;\n    line-height: 22px;\n    margin-top: 4px;\n  }\n  .tip-please-input{\n    font-size: 16px;\n    font-family: PingFangSC-Regular, PingFang SC;\n    font-weight: 400;\n    color: #F83939;\n    line-height: 22px;\n    letter-spacing: 1px;\n    //margin-left: 19px;\n    margin-top: 4px;\n  }\n}\n@include utils.setPcContent{\n  ::v-deep{\n    .label{\n      margin-top: 12px;\n    }\n  }\n  .wrapper{\n    .login-status__not-login{\n      .login-input-wrapper{\n        border: 1PX solid white;\n        width: 428PX;\n        height: 50PX;\n        display: flex;\n        align-items: center;\n\n        input{\n          height: 25px;\n          font-size: 18px;\n          line-height: 1;\n          font-weight: 400;\n          text-indent: 10px;\n          color: #FFFFFF;\n        }\n\n        i{\n          right: 14PX;\n          @include utils.bgCenter('koa/login/login-tips-btn.png', 25PX, 25PX);\n        }\n\n        .tips-img{\n          width: 420PX;\n          right: 25PX;\n        }\n      }\n      .btn-login{\n        margin-left: 34PX;\n        padding: 0 38PX;\n        width: auto;\n        height: 53PX;\n        border-radius: 8PX;\n        font-size: 26PX;\n        line-height: 53PX;\n      }\n    }\n    .login-status__login{\n      .avatar{\n        width: 96px;\n        height: 96px;\n        margin-right: 14px\n      }\n\n      .user-info{\n        margin-left: 37px;\n        .row-1{\n          .name{\n            font-size: 24px\n          }\n          .toggle{\n            font-size: 16px;\n            margin-left: 72px\n          }\n        }\n        .row-2{\n          font-size: 20px;\n          margin-top: 20px;\n\n          .server{\n            margin-left: 24px\n          }\n        }\n      }\n    }\n    .tip-please-input{\n      font-size: 16PX;\n      font-family: PingFangSC-Regular, PingFang SC;\n      font-weight: 400;\n      color: #F83939;\n      line-height: 20PX;\n    }\n    .tip-uid-get{\n      font-size: 14PX;\n      font-family: PingFangSC-Regular, PingFang SC;\n      font-weight: 400;\n      color: #A8A8A8;\n      line-height: 20PX;\n    }\n  }\n}\n\n/* 各游戏主题 */\n.wrapper.koa{\n  .login-status__login{\n    .avatar{\n      &[lazy=error], &[lazy=loading]{\n        background-image: url(\"~@/assets/koa/login/home-default-image.png\") !important;\n        background-color: transparent;\n      }\n    }\n    .user-info{\n      .row-1{\n        .name{\n          font-size: 28px;\n          font-family: PingFangSC-Semibold, PingFang SC;\n          font-weight: 600;\n          color: #FFFFFF;\n          line-height: 30px;\n          flex-shrink: 1;\n          overflow: hidden;\n          text-overflow: ellipsis;\n        }\n\n        .toggle{\n          background: linear-gradient(180deg, #F6E190 0%, #CBA455 100%);\n          text-decoration: none;\n          padding: 6px 11px 6px 34px;\n          font-size: 20px;\n          font-family: PingFangSC-Regular, PingFang SC;\n          font-weight: 400;\n          color: #633B00;\n          line-height: 28px;\n          position: relative;\n          margin-left: 40px;\n          white-space: nowrap;\n          flex-shrink: 0;\n          &:after{\n            @include utils.bgCenterForKoa('koa/login/toggle-account.png', 16px, 14px);\n            content: ' ';\n            display: inline-block;\n            position: absolute;\n            left: 12px;\n            width: 14px;\n            height: 14px;\n            top: 50%;\n            transform: translateY(-50%);\n          }\n        }\n      }\n\n      .row-koa{\n        display: flex;\n        align-items: center;\n        margin-top: 8px;\n        .grade{\n          font-size: 12px;\n          font-family: PingFangSC-Semibold, PingFang SC;\n          font-weight: 600;\n          color: #543000;\n          line-height: 29px;\n          padding-left: 28px;\n          @include utils.bgCenter('koa/vip/vip-grade-bg.png', 67px, 26px);\n          display: none;\n        }\n\n        .btn-jump{\n          font-size: 20px;\n          font-family: PingFangSC-Regular, PingFang SC;\n          font-weight: 400;\n          line-height: 25px;\n          letter-spacing: 1px;\n          text-decoration: underline;\n          margin-left: 29px;\n          margin-left: 0!important;\n          cursor: pointer;\n          a{\n            color: #FEB522;\n          }\n        }\n\n        &+ .row-2{\n          margin-top: 13px;\n        }\n      }\n\n      .row-2{\n        font-size: 20px;\n        line-height: 22px;\n\n        .server{\n          margin-left: 30px;\n        }\n      }\n    }\n  }\n  .login-status__not-login{\n    .btn-login{\n      background: linear-gradient(180deg, #F6E190 0%, #CBA455 100%);\n      font-weight: 600;\n      color: #633B00;\n      border-radius: 0;\n    }\n\n    .vip-jump-wrapper{\n      font-size: 14PX;\n      font-family: PingFangSC-Regular, PingFang SC;\n      font-weight: 400;\n      line-height: 20PX;\n      margin-left: 52PX;\n      white-space: nowrap;\n      text-decoration: underline;\n      flex-shrink: 0;\n\n      a{\n        color: #FEB522;\n      }\n    }\n  }\n  @include utils.setMobileContent{\n    .login-status__not-login{\n      position: relative;\n      .vip-jump-wrapper{\n        position: absolute;\n        right: 0;\n        top: -30px;\n        transform: translateY(-50%);\n        font-size: 18px;\n        line-height: 25px;\n      }\n    }\n  }\n\n  @include utils.setPcContent(){\n    .login-status__not-login{\n      .login-input-wrapper{\n        max-width: 428PX;\n      }\n      .btn-login{\n        margin-left: 16PX;\n      }\n      .vip{\n        font-size: 14PX;\n        font-family: PingFangSC-Regular, PingFang SC;\n        font-weight: 400;\n        color: #FEB522;\n        line-height: 20PX;\n        margin-left: 52PX;\n        white-space: nowrap;\n        text-decoration: underline;\n        flex-shrink: 0;\n      }\n    }\n    .login-status__login{\n      .user-info{\n        .row-1{\n          .name{\n            font-size: 20PX;\n            font-family: PingFangSC-Semibold, PingFang SC;\n            font-weight: 600;\n            color: #FFFFFF;\n            line-height: 28PX;\n          }\n\n          .toggle{\n            background: linear-gradient(180deg, #F6E190 0%, #CBA455 100%);\n            text-decoration: none;\n            padding: 4PX 10PX 3PX 32PX;\n            font-size: 18PX;\n            font-family: PingFangSC-Regular, PingFang SC;\n            font-weight: 400;\n            color: #633B00;\n            line-height: 25PX;\n            position: relative;\n            margin-left: 60PX;\n            &:after{\n              @include utils.bgCenterForKoa('koa/login/toggle-account.png', 16PX, 16PX);\n              content: ' ';\n              display: inline-block;\n              width: 16PX;\n              height: 16PX;\n              position: absolute;\n              left: 10PX;\n              top: 50%;\n              transform: translateY(-50%);\n            }\n          }\n        }\n        .row-koa{\n          display: flex;\n          align-items: center;\n          margin-top: 10PX;\n          .grade{\n            font-size: 12PX;\n            font-family: PingFangSC-Semibold, PingFang SC;\n            font-weight: 600;\n            color: #543000;\n            line-height: 27PX;\n            padding-left: 28PX;\n            height: 26PX;\n            width: 67PX;\n            @include utils.bgCenterForKoa('koa/vip/vip-grade-bg.png', 67PX, 26PX);\n          }\n\n          .btn-jump{\n            font-size: 18PX;\n            font-family: PingFangSC-Regular, PingFang SC;\n            font-weight: 400;\n            color: #FEB522;\n            line-height: 25PX;\n            letter-spacing: 1PX;\n            text-decoration: underline;\n            margin-left: 29PX;\n            cursor: pointer;\n          }\n\n          &+ .row-2{\n            margin-top: 11PX!important;\n          }\n        }\n        .row-2{\n          font-size: 16PX;\n\n          .server{\n            margin-left: 30PX;\n          }\n        }\n      }\n    }\n  }\n}\n.wrapper.aof,\n.wrapper.rom{\n  @extend .koa;\n}\n.wrapper.dc{\n  $color: rgb(53,121,209);\n  .login-status__not-login{\n    .login-input-wrapper{\n      background: #34353D;\n      border: 1px solid #000000;\n      margin-right: 0;\n\n      input{\n        color: #7E8089;\n        font-family: PingFangSC, PingFang SC;\n        @extend .dc-stroke;\n      }\n\n      i{\n        @include utils.bgCenterForDC('/login/login-tips-btn.png', 39px, 39px);\n      }\n    }\n    .btn-login{\n      width: 216px;\n      margin-left: 12px;\n      border-radius: 0;\n      background: rgb(53,121,209);\n      text-align: center;\n      font-family: PingFangSC, PingFang SC;\n      @extend .dc-stroke;\n      @extend .dc-btn-decoration;\n      @include utils.flexCenter;\n    }\n  }\n  .login-status__login{\n    .avatar {\n      width: 100px;\n      height: 100px;\n      margin-right: 21px;\n\n      &[lazy=error], &[lazy=loading]{\n        background-image: url(\"~@/assets/dc/pc/logo.png\") !important;\n        background-color: transparent;\n        opacity: .4;\n      }\n    }\n    .user-info {\n      .row-1 {\n        .name {\n          font-size: 28px;\n          line-height: 40px;\n        }\n        .toggle {\n          //width: 140px;\n          width: auto!important;\n          height: 40px;\n          background-color: $color;\n          font-size: 20px;\n          color: #F4FBFF;\n          text-decoration: none;\n          margin-left: 20px;\n          @include utils.flexCenter;\n          @extend .dc-stroke;\n          @extend .dc-btn-decoration;\n          white-space: nowrap;\n          padding: 0 10px;\n\n          &:before{\n            content: ' ';\n            @include utils.bgCenterForDC('login/uid-btn-toggle.png', 16px, 16px);\n            display: inline-block;\n            margin-right: 4px;\n          }\n        }\n      }\n      .row-2 {\n        font-size: 20px;\n        line-height: 28px;\n        margin-top: 12px;\n        .server {\n          margin-left: 30px;\n        }\n      }\n    }\n  }\n\n  @include utils.setPcContent{\n    .login-status__not-login{\n      .login-input-wrapper{\n        background: #34353D;\n        border: 1px solid #000000;\n        margin-right: 0;\n\n        input{\n          color: #7E8089;\n        }\n\n        i{\n          @include utils.bgCenterForDC('login/login-tips-btn.png', 32px, 32px);\n        }\n      }\n      .btn-login{\n        width: 150px;\n        height: 50px;\n        margin-left: 12px;\n        border-radius: 0;\n        background: rgb(53,121,209);\n        text-align: center;\n        font-family: PingFangSC, PingFang SC;\n        font-size: 21px;\n      }\n    }\n    .login-status__login{\n      .avatar {\n        width: 70px;\n        height: 70px;\n        margin-right: 12px;\n      }\n      .user-info {\n        margin-left: 0;\n        .row-1 {\n          .name {\n            font-size: 20px;\n            line-height: 28px;\n          }\n          .toggle {\n            width: 100px;\n            height: 30px;\n            background-color: $color;\n            font-size: 15px;\n            color: #F4FBFF;\n            text-decoration: none;\n            margin-left: 40px;\n\n            &:before{\n              @include utils.bgCenterForDC('login/uid-btn-toggle.png', 12px, 12px);\n              margin-right: 4px;\n            }\n          }\n        }\n        .row-2 {\n          font-size: 16px;\n          line-height: 22px;\n          margin-top: 2px;\n          .server {\n            margin-left: 20px;\n          }\n        }\n      }\n    }\n  }\n}\n\n.wrapper.ssv{\n  .login-status__not-login{\n    .login-input-wrapper{\n      margin-right: 0;\n      height: 60px;\n      box-sizing: border-box;\n\n      input{\n        color: #8C8C8C;\n        font-family: PingFangSC, PingFang SC;\n        text-indent: 20px;\n        height: 40px;\n        font-size: 28px;\n        padding: 0;\n      }\n\n      i{\n        @include utils.bgCenterForSSV('login/login-tips-btn.png', 31px, 31px);\n      }\n    }\n    .btn-login{\n      width: 216px;\n      height: 60px;\n      margin-left: 12px;\n      text-align: center;\n      font-family: PingFangSC, PingFang SC;\n      @include utils.flexCenter;\n    }\n  }\n  .login-status__login{\n    .avatar {\n      width: 60px;\n      height: 60px;\n      margin-right: 12px;\n\n      &[lazy=error], &[lazy=loading]{\n        background-image: url(https://d15h26rducpaui.cloudfront.net/kg-face/prod/ss_ea/social/cf182b6cdb5f713e29860055aea76fad.png) !important;\n        opacity: .4;\n      }\n    }\n    .user-info {\n      .row-1 {\n        .name {\n          font-size: 28px;\n          line-height: 40px;\n        }\n        .toggle {\n          width: 48px;\n          height: 32px;\n          background-color: #FF5E0F;\n          border-radius: 16px;\n          margin-left: 10px;\n\n          font-size: 20px;\n          color: #F4FBFF;\n          text-decoration: none;\n          @include utils.flexCenter;\n\n          &:before{\n            content: ' ';\n            @include utils.bgCenterForSSV('login/toggle-btn.png', 22px, 22px);\n            display: inline-block;\n          }\n        }\n      }\n      .row-2 {\n        height: 33px;\n        font-size: 24px;\n        line-height: 33px;\n        margin-top: 0;\n        .server {\n          margin-left: 30px;\n        }\n      }\n    }\n  }\n\n  @include utils.setPcContent{\n    .login-status__not-login{\n      .login-input-wrapper{\n        padding-right: 40px + 10px; // 再留出10px\n        width: 428px;\n        height: 50px;\n\n        input{\n          text-indent: 10px;\n          flex-grow: 1;\n          font-size: 20px;\n          line-height: 28px;\n        }\n\n        i{\n          @include utils.bgCenterForSSV('login/login-tips-btn.png', 25px, 25px);\n        }\n      }\n      .btn-login{\n        padding: 0 38PX;\n        width: auto;\n        height: 52px;\n        border-radius: 4px;\n        font-size: 20PX;\n        line-height: 52PX;\n      }\n    }\n    .login-status__login{\n      .avatar {\n        width: 50px;\n        height: 50px;\n      }\n      .user-info {\n        margin-left: 0;\n        .row-1 {\n          .name {\n            font-size: 20px;\n            line-height: 28px;\n          }\n          .toggle {\n            width: 32px;\n            height: 24px;\n\n            &:before{\n              @include utils.bgCenterForSSV('login/toggle-btn.png', 16px, 16px);\n            }\n          }\n        }\n        .row-2 {\n          font-size: 16px;\n          line-height: 22px;\n          margin-top: 2px;\n          .server {\n            margin-left: 20px;\n          }\n        }\n      }\n    }\n  }\n}\n\n.standard-login{\n  @include utils.setMobileContent{\n    ::v-deep{\n      .label{\n        display: none;\n      }\n    }\n  }\n  .login-status__not-login{\n    .login-input-wrapper{\n      width: 444px;\n      height: 60px;\n      flex-grow: 0;\n      margin-right: 12px;\n\n      input{\n        padding-left: 20px;\n        text-indent: 0;\n        line-height: 58px;\n        height: 58px;\n      }\n    }\n    .btn-login{\n      width: 216px;\n      height: 60px;\n      text-align: center;\n    }\n  }\n  .login-status__login{\n    .avatar{\n      width: 100px;\n      height: 100px;\n      margin-right: 20px;\n    }\n    .user-info{\n      .row-1{\n        .name{\n          font-size: 28px;\n          line-height: 40px;\n        }\n        .toggle{\n\n        }\n      }\n      .row-2{\n        font-size: 20px;\n        line-height: 28px;\n        margin-top: 12px;\n        .server {\n          margin-left: 30px\n        }\n      }\n    }\n  }\n  .tip-uid-get,\n  .tip-please-input{\n    font-size: 18px;\n    margin-top: 8px;\n  }\n\n  @include utils.setPcContent{\n    .login-status__not-login{\n      .login-input-wrapper{\n        margin-right: 0;\n        height: 50px;\n        width: 428px;\n        input{\n          height: 35px;\n          padding-left: 12px;\n        }\n      }\n      .btn-login{\n        height: 50px;\n        width: auto;\n        margin-left: 12px;\n        padding: 0 10px;\n        font-size: 21px;\n        min-width: 150px;\n        line-height: 50px;\n      }\n    }\n    .login-status__login{\n      .avatar{\n        height: 50px;\n        width: 50px;\n        margin-right: 0;\n      }\n      .user-info{\n        margin-left: 12px;\n        .row-1{\n          .name{\n            font-size: 20px;\n            line-height: 28px;\n          }\n          .toggle{\n            font-size: 16px;\n            margin-left: 22px\n          }\n        }\n        .row-2{\n          font-size: 16px;\n          margin-top: 1px;\n\n          .server{\n            margin-left: 20px\n          }\n        }\n      }\n    }\n    .tip-uid-get,\n    .tip-please-input{\n      font-size: 13px;\n      margin-top: 4px;\n    }\n  }\n}\n.wrapper.ssv2{\n  @extend .standard-login;\n  .login-status__not-login{\n    .login-input-wrapper{\n      background: rgba(0,2,31,0.3);\n      border-radius: 4px;\n      border: 1px solid #FFFFFF;\n    }\n    .btn-login{\n      border-radius: 5px;\n    }\n  }\n}\n</style>\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./LoginModule.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./LoginModule.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./LoginModule.vue?vue&type=template&id=12983a50&scoped=true\"\nimport script from \"./LoginModule.vue?vue&type=script&lang=js\"\nexport * from \"./LoginModule.vue?vue&type=script&lang=js\"\nimport style0 from \"./LoginModule.vue?vue&type=style&index=0&id=12983a50&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"12983a50\",\n  null\n  \n)\n\nexport default component.exports", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./DirectGiftPackage.vue?vue&type=style&index=0&id=cf7c3d58&prod&scoped=true&lang=scss\"", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./CheckoutFooterTax.vue?vue&type=style&index=0&id=4ceefa9e&prod&scoped=true&lang=scss\"", "module.exports = __webpack_public_path__ + \"static/1751299200/img/coupon-item-chosen.fe63410a.png\";", "export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./LoginModule.vue?vue&type=style&index=0&id=12983a50&prod&scoped=true&lang=scss\"", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('common-part',{class:['coupon-bar-wrapper', _vm.$gameName],attrs:{\"label-font\":_vm.$t('coupon'),\"id\":\"coupon-bar-wrapper\"}},[(_vm.$store.state.userinfo.isLogin)?_c('div',{staticStyle:{\"display\":\"inline-block\"},on:{\"click\":function($event){_vm.activity.showPop = true}}},[(!_vm.activity.isFirstPayUsed)?_c('div',{staticClass:\"coupons-wrapper coupons-wrapper__unavailable\"},[_c('div',{staticClass:\"left\"},[(_vm.availableCouponNum === 0)?_c('span',[_vm._v(_vm._s(_vm.$t('coupon_desc_unavailable')))]):_c('span',[_vm._v(_vm._s(_vm.$t('coupon_nums', {0: _vm.availableCouponNum})))])])]):[(_vm.availableCouponNum === 0)?_c('div',{staticClass:\"coupons-wrapper coupons-wrapper__no_coupon\"},[_vm._v(\" \"+_vm._s(_vm.$t('coupon_desc_unavailable'))+\" \")]):_vm._e(),(_vm.availableCouponNum > 0 && !_vm.chosenCoupon.FE_INDEX)?_c('div',{staticClass:\"coupons-wrapper coupons-wrapper__available\"},[_c('span',[_vm._v(_vm._s(_vm.$t('coupon_nums', {0: _vm.availableCouponNum})))]),_vm._v(\" \"),_c('i')]):_vm._e(),(_vm.availableCouponNum > 0 && _vm.chosenCoupon.FE_INDEX)?_c('div',{staticClass:\"coupons-wrapper coupons-wrapper__chosen\"},[_c('div',{staticClass:\"left\"},[_c('span',[_vm._v(_vm._s(_vm.$t('coupon_desc_chosen_erver')))])]),_c('div',{staticClass:\"right\"},[_c('over-size-scale',{key:_vm.chosenCoupon.FE_INDEX},[_c('span',[(_vm.chosenCoupon.feType==='discount_coupon')?[_vm._v(_vm._s(_vm.$t('coupon_discount', { 0: _vm.chosenCoupon.rateWidthOutPercent })))]:_vm._e(),(_vm.chosenCoupon.feType==='cash_coupon')?[_c('span',{class:{'is-ar-zone': _vm.isArZone}},[_vm._v(_vm._s(_vm.chosenCoupon.deduct_price)+\" \"+_vm._s(_vm.currencyUnit))]),_vm._v(\" OFF\")]:_vm._e(),(_vm.chosenCoupon.feType==='rebate_coupon')?[_c('span',[_vm._v(_vm._s(_vm.$t('bonus_tips'))+\" \")]),_vm._v(_vm._s(_vm.chosenCoupon.rate)),_c('i',{staticClass:\"diamond-icon\"})]:_vm._e()],2)]),_c('i')],1)]):_vm._e()]],2):_c('div',{staticClass:\"coupons-wrapper coupons-wrapper__not-login\",on:{\"click\":function($event){return _vm.$root.$emit('ClickPayButNotLogin')}}},[_vm._v(_vm._s(_vm.$t('view_coupons_after_login')))]),(_vm.activity.showPop)?_c('coupon-choose-pop',{attrs:{\"had-obtained-list\":_vm.activity.hadObtainedList,\"not-obtained-list\":_vm.activity.notObtainedList,\"last-index\":_vm.activity.chosenIndex,\"is-first-charge-used\":_vm.activity.isFirstPayUsed},on:{\"close\":_vm.closeCouponPop}}):_vm._e()],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{class:['ticket-chosen-wrapper', _vm.$gameName],attrs:{\"id\":\"ticket-chosen-wrapper\"}},[_c('div',{staticClass:\"pop-main\"},[_c('div',{staticClass:\"pop-close\",on:{\"click\":function($event){return _vm.$emit('close')}}}),_c('div',{staticClass:\"pop-title\"},[_c('h3',[_vm._v(_vm._s(_vm.$t('coupon')))]),_c('span',[_vm._v(_vm._s(_vm.$t('discount_offer_tips')))])]),_c('div',{staticClass:\"divider\"}),_c('div',{staticClass:\"nav-btn-wrapper\"},[_c('div',{class:['nav',{'nav-active': _vm.navIndex===0}],on:{\"click\":function($event){_vm.navIndex=0}}},[_vm._v(_vm._s(_vm.$t('nav_my_coupon')))]),_c('div',{class:['nav',{'nav-active': _vm.navIndex===1}],on:{\"click\":function($event){_vm.navIndex=1}}},[_vm._v(_vm._s(_vm.$t('nav_other_coupon')))])]),_c('div',{staticClass:\"main-container\"},[(_vm.navIndex===0)?[_c('coupon-choose-list',{key:0,attrs:{\"coupon-list\":_vm.hadObtainedList,\"is-first-charge-used\":_vm.isFirstChargeUsed,\"reach\":true,\"temp-chosen-coupon\":_vm.tempChosenCoupon},on:{\"update:tempChosenCoupon\":function($event){_vm.tempChosenCoupon=$event},\"update:temp-chosen-coupon\":function($event){_vm.tempChosenCoupon=$event}}}),_c('div',{class:['btn-confirm', 'click-btn', {'btn-confirm__unavailable': !_vm.isFirstChargeUsed }],on:{\"click\":_vm.chooseCoupon}},[_vm._v(_vm._s(_vm.$t('modalBtnOk')))])]:_c('coupon-choose-list',{key:1,attrs:{\"coupon-list\":_vm.notObtainedList,\"is-first-charge-used\":_vm.isFirstChargeUsed,\"reach\":false}})],2),_c('p',{staticClass:\"coupon-repeat-tips\"},[_vm._v(\" *\"+_vm._s(_vm.$t('construction_faq_q5a1'))+\" \"),(_vm.$store.state.country === 'RU')?[_vm._v(\"Купоны не поддерживают Huawei Pay.\")]:_vm._e()],2)])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{class:['ticket-wrapper', _vm.$gameName],attrs:{\"id\":\"ticket-wrapper\"}},[_c('div',{staticClass:\"ticket-list\"},[_vm._l((_vm.couponList),function(item,index){return [(item.feType!=='first_pay' && item.feType!=='first_pay_rebate' && ('leaveCount' in item ? item.leaveCount > 0 : true ))?_c('div',{key:item.coupon_id + item.type + index,class:[\n        'item',\n        {'item__active': _vm.reach && item.FE_INDEX === _vm.tempChosenCoupon.FE_INDEX },\n        {'item__unavailable':!_vm.isFirstChargeUsed || !_vm.reach || item.is_invalid === 0}],on:{\"click\":function($event){return _vm.choose(index, item)}}},[_c('div',{staticClass:\"left\"},[(_vm.$store.state.formdata.switchToggleState && item.change_enable)?_c('coupon-toggle',{attrs:{\"temp-chosen\":_vm.reach && item.FE_INDEX === _vm.tempChosenCoupon.FE_INDEX,\"coupon-item\":item}}):_vm._e(),_c('div',{staticClass:\"title\"},[(item.feType==='discount_coupon')?[_vm._v(_vm._s(item.rate)+\" \"),_c('span',[_vm._v(\"OFF\")])]:(item.feType==='cash_coupon')?[_c('span',{class:{'is-ar-zone': _vm.isArZone}},[_vm._v(_vm._s(item.deduct_price)+\" \"+_vm._s(_vm.currencyUnit))]),_vm._v(\" OFF \")]:(item.feType ==='rebate_coupon')?[_c('span',[_vm._v(_vm._s(_vm.$t('bonus_tips'))+\" \")]),_vm._v(_vm._s(item.rate)),_c('i',{staticClass:\"diamond-icon\"})]:_vm._e()],2)],1),_c('div',{staticClass:\"right\"},[_c('div',{staticClass:\"desc\"},[_vm._v(_vm._s(_vm.$t(item.langKey, {0:item.num}))+\" \"+_vm._s(item.langValue))]),(item.showLeaveDate)?_c('div',{staticClass:\"time\"},[_vm._v(_vm._s(item.showLeaveDate))]):_vm._e()])]):_vm._e()]})],2),(_vm.showEmpty)?_c('span',{staticClass:\"no-data\"},[_vm._v(_vm._s(_vm.$t('nothingHere')))]):_vm._e()])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"coupon-type-toggle-wrapper\",on:{\"click\":function($event){$event.stopPropagation();return _vm.beginToggleCoupon.apply(null, arguments)},\"mouseover\":function($event){_vm.tips = _vm.couponItem.feType === 'discount_coupon' ? _vm.$t('toggle-to-rebate') : _vm.$t('toggle-to-common')},\"mouseleave\":function($event){_vm.tips = ''}}},[(_vm.tips)?_c('div',{staticClass:\"tips\"},[_vm._v(_vm._s(_vm.tips))]):_vm._e()])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <div class=\"coupon-type-toggle-wrapper\"\n       @click.stop=\"beginToggleCoupon\"\n       @mouseover=\"tips = couponItem.feType === 'discount_coupon' ? $t('toggle-to-rebate') : $t('toggle-to-common')\"\n       @mouseleave=\"tips = ''\">\n\n    <div class=\"tips\" v-if=\"tips\">{{ tips }}</div>\n  </div>\n</template>\n\n<script>\nimport { toggleCouponSingle } from '@/server'\n\nexport default {\n  name: 'couponToggle',\n  props: ['couponItem', 'tempChosen'],\n  data () {\n    return {\n      tips: ''\n    }\n  },\n  methods: {\n    beginToggleCoupon () {\n      this.tips = ''\n      const couponItem = this.couponItem\n      const params = {\n        change_type: couponItem.feType === 'discount_coupon' ? 'rebate' : 'coupon',\n        coupon_id: this.couponItem.coupon_id\n      }\n\n      this.$loading.show()\n      toggleCouponSingle(params)\n        .then(res => {\n          const { code } = res\n          if (code === 0) {\n            sessionStorage.setItem('reopenCoupon', '1') // 用户保持弹窗打开状态\n            if (this.tempChosen) sessionStorage.setItem('reChooseCoupon', this.couponItem.coupon_id) // 用于恢复本次选中的券\n            this.$root.$emit('couponToggleSuccess')\n          } else {\n            this.$toast.err(this.$t('toggle-fail-tips'))\n          }\n        })\n        .catch(err => {\n          console.error(err.message)\n        })\n        .finally(() => this.$loading.hide())\n    }\n  },\n  mounted () {\n    if (this.couponItem.FE_INDEX === sessionStorage.getItem('popIndex')) {\n      sessionStorage.removeItem('popIndex')\n      this.tips = this.couponItem.feType === 'discount_coupon' ? this.$t('toggle-to-rebate') : this.$t('toggle-to-common')\n      this.$el.scrollIntoView({ behavior: 'smooth' })\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n@use \"~@/utils/utils.scss\" as utils;\n\n.coupon-type-toggle-wrapper{\n  @include utils.bgCenter('koa/activity/coupon-toggle-btn-bg.png', 24px, 24px);\n  margin-left: -4px;\n  margin-right: 6px;\n  position: relative;\n  z-index: 1;\n\n  .tips{\n    font-family: SourceHanSansCN, SourceHanSansCN;\n    font-weight: 500;\n    font-size: 16px;\n    color: #E0E0E0;\n    line-height: 24px;\n    text-align: left;\n    font-style: normal;\n    position: absolute;\n    bottom: -10px;\n    left: 0;\n    transform: translate(-30px, 100%);\n    background: #606060;\n    padding: 7px 24px;\n    border-radius: 8px;\n    max-width: 550px;\n    white-space: nowrap;\n\n    &:after{\n      content: '';\n      display: inline-block;\n      @include utils.bgCenter('koa/activity/coupon-toggle-caret.png', 7px, 7px);\n      position: absolute;\n      top: 2px;\n      left: 37px;\n      transform: translate(0, -100%);\n      z-index: -1;\n    }\n  }\n}\n@include utils.setPcContent{\n  .coupon-type-toggle-wrapper{\n    @include utils.bgCenter('koa/activity/coupon-toggle-btn-bg.png', 20px, 20px);\n\n    .tips{\n      transform: translate(-15px, 100%);\n      background: #606060;\n      border-radius: 5px;\n      max-width: 750px;\n      font-size: 13px;\n      line-height: 18px;\n\n      &:after{\n        @include utils.bgCenter('koa/activity/coupon-toggle-caret.png', 6px, 6px);\n        top: 1px;\n        left: 21px;\n      }\n    }\n  }\n}\n</style>\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./couponToggle.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./couponToggle.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./couponToggle.vue?vue&type=template&id=153b91ac&scoped=true\"\nimport script from \"./couponToggle.vue?vue&type=script&lang=js\"\nexport * from \"./couponToggle.vue?vue&type=script&lang=js\"\nimport style0 from \"./couponToggle.vue?vue&type=style&index=0&id=153b91ac&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"153b91ac\",\n  null\n  \n)\n\nexport default component.exports", "<template>\n  <div :class=\"['ticket-wrapper', $gameName]\" id=\"ticket-wrapper\">\n    <div class=\"ticket-list\">\n      <template v-for=\"(item, index) in couponList\">\n        <div :class=\"[\n          'item',\n          {'item__active': reach && item.FE_INDEX === tempChosenCoupon.FE_INDEX },\n          {'item__unavailable':!isFirstChargeUsed || !reach || item.is_invalid === 0}]\"\n             :key=\"item.coupon_id + item.type + index\"\n             v-if=\"item.feType!=='first_pay' && item.feType!=='first_pay_rebate' && ('leaveCount' in item ? item.leaveCount > 0 : true )\"\n             @click=\"choose(index, item)\">\n          <div class=\"left\">\n            <coupon-toggle\n              v-if=\"$store.state.formdata.switchToggleState && item.change_enable\"\n              :temp-chosen=\"reach && item.FE_INDEX === tempChosenCoupon.FE_INDEX\"\n              :coupon-item=\"item\">\n            </coupon-toggle>\n            <div class=\"title\">\n              <template v-if=\"item.feType==='discount_coupon'\">{{ item.rate }} <span>OFF</span></template>\n              <template v-else-if=\"item.feType==='cash_coupon'\">\n                <span :class=\"{'is-ar-zone': isArZone}\">{{ item.deduct_price}} {{ currencyUnit }}</span> OFF\n              </template>\n              <template v-else-if=\"item.feType ==='rebate_coupon'\">\n                <span>{{ $t('bonus_tips') }} </span>{{ item.rate }}<i class=\"diamond-icon\"></i>\n              </template>\n            </div>\n            <!--<div class=\"desc\">满xxx</div>-->\n          </div>\n          <div class=\"right\">\n            <div class=\"desc\">{{ $t(item.langKey, {0:item.num}) }} {{ item.langValue }}</div>\n            <div class=\"time\" v-if=\"item.showLeaveDate\">{{ item.showLeaveDate }}</div>\n          </div>\n        </div>\n      </template>\n    </div>\n    <span class=\"no-data\" v-if=\"showEmpty\">{{ $t('nothingHere') }}</span>\n  </div>\n</template>\n\n<script>\nimport { mapState } from 'vuex'\nimport CouponToggle from '@/components/coupon/couponToggle'\nimport { onceADay } from '@/utils/utils'\n\nexport default {\n  name: 'CouponChooseList',\n  components: { CouponToggle },\n  props: ['couponList', 'isFirstChargeUsed', 'reach', 'tempChosenCoupon'], // reach 未获取\n  methods: {\n    choose (index, item) {\n      /* 券不可使用 */\n      if (!this.isFirstChargeUsed || !this.reach || item.is_invalid === 0) return null\n\n      if (this.tempChosenCoupon.FE_INDEX === item.FE_INDEX) this.$emit('update:tempChosenCoupon', {})\n      else {\n        this.$emit('update:tempChosenCoupon', item)\n        this.$root.$emit('availableTicketChosen')\n      }\n    }\n  },\n  computed: {\n    ...mapState(['isArZone', 'currencyUnit']),\n    showEmpty () {\n      return !this.couponList.filter(item => item.feType.includes('_coupon')).length\n    }\n  },\n  created () {\n    if (this.$store.state.formdata.switchToggleState && this.reach && onceADay('toggleCoupon')) {\n      const findIndex = this.couponList.findIndex(item => item.change_enable)\n      if (this.couponList[findIndex]) sessionStorage.setItem('popIndex', this.couponList[findIndex].FE_INDEX)\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n@use \"~@/utils/utils.scss\" as utils;\n\n.is-ar-zone{\n  display: inline-block;\n}\n\n.ticket-wrapper {\n  margin-top: 27px;\n  padding: 3px 20px;\n  text-align: center;\n\n  @include utils.setPropByBp(\n    $m: (margin-top: 22px, padding: 3 24px),\n    $p: (margin-top: 37, padding: 3px 20px)\n  );\n\n  .ticket-list {\n    overflow-y: scroll;\n    display: flex;\n    flex-wrap: wrap;\n    box-sizing: border-box;\n    padding-bottom: 20px;\n\n    @include utils.setPropByBp(\n      $m: (max-height: 600px),\n      $p: (max-height: 270px),\n    );\n\n    .item {\n      cursor: pointer;\n      box-sizing: border-box;\n      font-weight: 400;\n      display: flex;\n      transition: all .1s;\n      background-size: 100% 100%;\n      margin: 0 auto;\n\n      @include utils.setPropByBp(\n        $m: (\n          height: 90px,\n          width: calc(100% - 28px),\n          background-image: url(~@/assets/koa/coupon/shopping-modal-ticket-bg-m.png)),\n        $p: (\n          height: 64px,\n          width: 619px,\n          background-image: url(~@/assets/koa/coupon/shopping-modal-ticket-bg.png)),\n      );\n\n      .left {\n        height: 100%;\n        flex-grow: 0;\n        display: flex;\n        align-items: center;\n        //flex-direction: column;\n        justify-content: center;\n        @include utils.setPropByBp(\n          $m: (width: 29%),\n          $p: (width: 157px),\n        );\n\n        .title {\n          font-family: PingFang SC;\n          font-weight: bold;\n          color: #373737;\n          //white-space: nowrap;\n          max-width: 70%;\n\n          @include utils.setPropByBp(\n            $m: (line-height: 20px, font-size: 18px),\n            $p: (line-height: 16px, font-size: 16px),\n          );\n        }\n\n        .desc {\n          font-family: PingFang SC;\n          font-weight: bold;\n          color: #545454;\n\n          @include utils.setPropByBp(\n            $m: (line-height: 22px, font-size: 16px, margin-top: 6px),\n            $p: (line-height: 12px, font-size: 12px, margin-top: 6px)\n          );\n        }\n\n        i.diamond-icon {\n          @include utils.bgCenterForKoaIcon('koa/diamond/diamond.png', 15px, 12px);\n          display: inline-block;\n          margin-left: 2px;\n          position: relative;\n        }\n      }\n\n      .right {\n        position: relative;\n        width: 0;\n        flex-grow: 1;\n\n        .desc {\n          position: absolute;;\n          left: 50%;\n          top: 50%;\n          transform: translate(-50%, -50%);\n          font-family: PingFang SC;\n          font-weight: bold;\n          color: #373737;\n          @include utils.setPropByBp(\n            $m: (line-height: 22px,width: 96%,font-size: 16px),\n            $p: (line-height: 16px,width: 414px,font-size: 14px)\n          );\n        }\n\n        .time {\n          position: absolute;\n          font-family: PingFang SC;\n          font-weight: bold;\n          color: #545454;\n          @include utils.setPropByBp(\n            $m: (right: 15px, bottom: 9px, font-size: 16px,),\n            $p: (right: 8px, bottom: 10px, font-size: 12px,),\n          );\n        }\n      }\n\n      &.item__active {\n        position: relative;\n        background-size: 100% 100%;\n\n        @include utils.setPropByBp(\n          $m: (background-image: url(~@/assets/koa/coupon/shopping-modal-ticket-bg-m_active.png)),\n          $p: (background-image: url(~@/assets/koa/coupon/shopping-modal-ticket-bg_active.png)),\n        );\n      }\n\n      &.item__unavailable{\n        cursor: not-allowed;\n        opacity: .5;\n      }\n\n      &:nth-of-type(n+2) {\n        @include utils.setPropByBp(\n          $m: (margin-top: 12px),\n          $p: (margin-top: 10px)\n        );\n      }\n    }\n  }\n\n  .no-data {\n    text-align: center;\n    margin: 0 auto;\n    font-weight: bold;\n    color: #666666;\n    width: 100%;\n    position: relative;\n\n    @include utils.setPropByBp(\n      $m: (font-size: 25px, line-height: 220px, top: -40px),\n      $p: (font-size: 18px, line-height: 220px, top: -10px)\n    );\n  }\n}\n\n.ticket-wrapper.dc{\n  .ticket-list {\n    min-height: 200px;\n    .item {\n      @include utils.bgCenterForDC('coupon/coupon-pop-list-item.png', 614px, 88px);\n\n      &.item__active {\n        @include utils.bgCenterForDC('coupon/coupon-pop-list-item-active.png', 614px, 88px);\n      }\n\n      .left {\n        .title{\n          color: white;\n          @extend .dc-stroke;\n        }\n        i.diamond-icon {\n          @include utils.bgCenterForDC('diamond/diamond-icon.png', 18px, 18px);\n        }\n      }\n\n      .right{\n        .desc{\n          color: #4A402C;\n        }\n        .time {\n          color: #7E7055;\n        }\n      }\n    }\n  }\n\n  @include utils.setPcContent{\n    .ticket-list {\n      .item {\n        @include utils.bgCenterForDC('coupon/coupon-pop-list-item-pc.png', 564px, 58px);\n        .left{\n          i.diamond-icon {\n            @include utils.bgCenterForDC('diamond/diamond-icon.png', 17px, 17px);\n          }\n        }\n        &.item__active {\n          @include utils.bgCenterForDC('coupon/coupon-pop-list-item-active-pc.png', 564px, 58px);\n        }\n      }\n    }\n  }\n}\n</style>\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./CouponChooseList.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./CouponChooseList.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./CouponChooseList.vue?vue&type=template&id=3d2a8ef0&scoped=true\"\nimport script from \"./CouponChooseList.vue?vue&type=script&lang=js\"\nexport * from \"./CouponChooseList.vue?vue&type=script&lang=js\"\nimport style0 from \"./CouponChooseList.vue?vue&type=style&index=0&id=3d2a8ef0&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"3d2a8ef0\",\n  null\n  \n)\n\nexport default component.exports", "<template>\n  <div :class=\"['ticket-chosen-wrapper', $gameName]\" id=\"ticket-chosen-wrapper\">\n    <div class=\"pop-main\">\n      <div class=\"pop-close\" @click=\"$emit('close')\"></div>\n      <div class=\"pop-title\">\n        <h3>{{ $t('coupon') }}</h3>\n        <span>{{ $t('discount_offer_tips') }}</span>\n      </div>\n      <div class=\"divider\"></div>\n      <div class=\"nav-btn-wrapper\">\n        <div :class=\"['nav',{'nav-active': navIndex===0}]\" @click=\"navIndex=0\">{{ $t('nav_my_coupon') }}</div>\n        <div :class=\"['nav',{'nav-active': navIndex===1}]\" @click=\"navIndex=1\">{{ $t('nav_other_coupon') }}</div>\n      </div>\n      <div class=\"main-container\">\n        <template v-if=\"navIndex===0\">\n          <coupon-choose-list\n            :key=\"0\"\n            :coupon-list=\"hadObtainedList\"\n            :is-first-charge-used=\"isFirstChargeUsed\"\n            :reach=\"true\"\n            :temp-chosen-coupon.sync=\"tempChosenCoupon\">\n          </coupon-choose-list>\n          <div :class=\"['btn-confirm', 'click-btn', {'btn-confirm__unavailable': !isFirstChargeUsed }]\" @click=\"chooseCoupon\">{{ $t('modalBtnOk') }}</div>\n        </template>\n        <coupon-choose-list\n          v-else\n          :key=\"1\"\n          :coupon-list=\"notObtainedList\"\n          :is-first-charge-used=\"isFirstChargeUsed\"\n          :reach=\"false\">\n        </coupon-choose-list>\n      </div>\n\n      <p class=\"coupon-repeat-tips\">\n        *{{ $t('construction_faq_q5a1') }}\n        <template v-if=\"$store.state.country === 'RU'\">Купоны не поддерживают Huawei Pay.</template>\n      </p>\n    </div>\n  </div>\n</template>\n\n<script>\nimport CouponChooseList from '@/components/coupon/CouponChooseList'\nimport { mapState } from 'vuex'\n\nexport default {\n  name: 'CouponChoosePop',\n  components: { CouponChooseList },\n  props: ['hadObtainedList', 'notObtainedList', 'isFirstChargeUsed', 'lastIndex'],\n  data () {\n    return {\n      navIndex: 0,\n\n      tempChosenCoupon: this.$store.state.formdata.chosenCoupon\n    }\n  },\n  computed: {\n    ...mapState('formdata', ['switchToggleState'])\n  },\n  methods: {\n    chooseCoupon () {\n      if (!this.isFirstChargeUsed) {\n        // this.$tips.show('首冲活动与此活动互斥！')\n        // this.$emit('close')\n        return null\n      }\n      if (this.tempChosenCoupon) this.$store.commit('formdata/setChosenCoupon', this.tempChosenCoupon)\n      this.$emit('close')\n      this.$root.$emit('couponChoose')\n\n      if (this.tempChosenCoupon.FE_INDEX) {\n        this.$root.$emit('couponChosen')\n      }\n    }\n  },\n  mounted () {\n    if (this.$gcbk('switch.enableAnimation', false)) {\n      gsap && gsap.from('.ticket-chosen-wrapper .pop-main', { top: '45%', duration: .4, ease: 'back', clearProps: true })\n      gsap && gsap.from('.ticket-chosen-wrapper', { backgroundColor: 'rgba(0, 0, 0, 0)', duration: .4, clearProps: true })\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n@use \"~@/utils/utils.scss\" as utils;\n.is-ar-zone{\n  display: inline-block;\n}\n\n.ticket-chosen-wrapper {\n  user-select: none;\n  height: 100%;\n  width: 100%;\n  background-color: rgba(0, 0, 0, 0.5);\n  position: fixed;\n  z-index: 100;\n  left: 0;\n  top: 0;\n  * {\n    ::-webkit-scrollbar {\n      display: none;\n    }\n  }\n\n  .pop-main {\n    background-color: #383838;\n    position: absolute;\n    left: 50%;\n    top: 50%;\n    transform: translate(-50%, -50%);\n    @include utils.setPropByBp(\n      $m: (width: calc(100% - 86px), border-radius: 15px,max-width: 750px)\n    );\n\n    .pop-close {\n      position: absolute;\n      cursor: pointer;\n      @include utils.setMobileContent{\n        @include utils.bgCenter('koa/coupon/shopping-modal-close.png', 24px, 22px);\n        right: 40px;\n        top: 27px;\n      }\n    }\n\n    .pop-title {\n      color: #000000;\n      text-align: center;\n      margin-top: 14px;\n      display: flex;\n      flex-direction: column;\n\n      h3 {\n        white-space: nowrap;\n        font-family: PingFang SC;\n        font-weight: bold;\n        line-height: 40px;\n        font-size: 28px;\n        color: #FFFFFF;\n      }\n\n      span {\n        overflow: hidden;\n        white-space: nowrap;\n        text-overflow: ellipsis;\n        font-family: PingFang SC;\n        font-weight: bold;\n        color: #8C8C8C;\n        line-height: 22px;\n        font-size: 16px;\n      }\n    }\n\n    .divider {\n      width: calc(100% - 60px);\n      height: 1px;\n      background: #D8D8D8;\n      margin: 15px auto 0;\n      opacity: .4;\n    }\n\n    .nav-btn-wrapper {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin-top: 10px;\n\n      .nav {\n        cursor: pointer;\n        font-family: PingFang SC;\n        font-weight: bold;\n        line-height: 33px;\n        font-size: 24px;\n        color: #8C8C8C;\n        &.nav-active {\n          position: relative;\n          color: #FFFFFF;\n\n          &:after {\n            content: '';\n            width: 80%;\n            background: #F18648;\n            position: absolute;\n            bottom: -4px;\n            height: 2px;\n            left: 50%;\n            transform: translateX(-50%);\n          }\n        }\n\n        &:last-of-type {\n          margin-left: 84px;\n        }\n      }\n    }\n\n    .main-container{\n      text-align: center;\n      .btn-confirm {\n        text-align: center;\n        background: #FF5A00;\n        font-family: PingFang SC;\n        font-weight: 400;\n        color: #F1F1F1;\n        cursor: pointer;\n\n        margin: 30px auto 0;\n        font-size: 30px;\n        padding: 0 80px;\n        line-height: 72px;\n        border-radius: 8px;\n        display: inline-block;\n        white-space: nowrap;\n\n        &.btn-confirm__unavailable{\n          opacity: .3;\n          cursor: auto;\n        }\n      }\n    }\n\n    .coupon-repeat-tips{\n      width: 90%;\n      font-size: 16px;\n      line-height: 22px;\n      margin: 20px auto 20px;\n      text-align: center;\n      color: #8C8C8C;\n    }\n  }\n\n  @include utils.setPcContent{\n    .pop-main{\n      border-radius: 15PX;\n      width: 665PX;\n\n      .pop-close{\n        right: 22PX;\n        top: 19PX;\n        @include utils.bgCenter('koa/coupon/shopping-modal-close.png', 23PX, 23PX);\n      }\n\n      .pop-title{\n        margin-top: 14PX;\n\n        h3{\n          line-height: 33PX;\n          font-size: 24PX;\n        }\n\n        span{\n          font-size: 13PX;\n          line-height: 18PX;\n        }\n      }\n      .divider{\n        width: 618PX;\n        height: 1PX;\n        margin: 17PX auto 0;\n      }\n      .nav-btn-wrapper {\n        margin-top: 15PX;\n\n        .nav {\n          line-height: 25PX;\n          font-size: 18PX;\n          color: #8C8C8C;\n\n          &.nav-active {\n            &:after {\n              bottom: -4PX;\n              height: 2PX;\n            }\n          }\n\n          &:last-of-type {\n            margin-left: 81PX;\n          }\n        }\n      }\n\n      .main-container{\n        .btn-confirm {\n          margin: 17PX auto 0;\n          font-size: 24PX;\n          line-height: 53PX;\n          border-radius: 4PX;\n          padding: 0 60PX;\n          display: inline-block;\n        }\n      }\n\n      .coupon-repeat-tips{\n        font-size: 13PX;\n        margin: 20PX auto 15PX;\n        line-height: 17PX;\n      }\n    }\n  }\n}\n\n.ticket-chosen-wrapper.dc{\n  .pop-main{\n    background: #C3CBE1;\n    border-radius: 0;\n    border: 1px solid #979797;\n\n    .pop-close {\n      @include utils.bgCenterForDC('coupon/pop-close.png', 32px, 32px);\n    }\n\n    .divider{\n      background: #ABB4CD;\n    }\n\n    .pop-title{\n      h3{\n        color: #F4FBFF;\n        @extend .dc-stroke;\n      }\n      span{\n        color: #525280;\n      }\n    }\n\n    .nav-btn-wrapper {\n      .nav {\n        color: #7C83A6;\n\n        &.nav-active {\n          color: #525280;\n\n          &:after {\n            background: #D9AD00;\n          }\n        }\n      }\n    }\n\n    .main-container{\n      .btn-confirm {\n        border-radius: 0;\n        color: #F4FBFF;\n        margin-top: 0;\n        @extend .dc-stroke;\n        @extend .dc-btn-decoration;\n        width: 220px;\n        height: 72px;\n        @include utils.flexCenter\n      }\n    }\n\n    .coupon-repeat-tips{\n      color: #525280;\n    }\n  }\n\n  @include utils.setPcContent{\n    .pop-main{\n      .pop-close {\n        width: 32px;\n        height: 32px;\n      }\n\n      .main-container{\n        .btn-confirm {\n          width: 220px;\n          height: 60px;\n        }\n      }\n    }\n  }\n}\n</style>\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./CouponChoosePop.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./CouponChoosePop.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./CouponChoosePop.vue?vue&type=template&id=26605e4f&scoped=true\"\nimport script from \"./CouponChoosePop.vue?vue&type=script&lang=js\"\nexport * from \"./CouponChoosePop.vue?vue&type=script&lang=js\"\nimport style0 from \"./CouponChoosePop.vue?vue&type=style&index=0&id=26605e4f&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"26605e4f\",\n  null\n  \n)\n\nexport default component.exports", "<template>\n  <common-part :class=\"['coupon-bar-wrapper', $gameName]\" :label-font=\"$t('coupon')\" id=\"coupon-bar-wrapper\">\n    <!--登录-->\n    <div v-if=\"$store.state.userinfo.isLogin\" @click=\"activity.showPop = true\" style=\"display: inline-block\">\n      <div v-if=\"!activity.isFirstPayUsed\" class=\"coupons-wrapper coupons-wrapper__unavailable\">\n        <div class=\"left\">\n          <span v-if=\"availableCouponNum === 0\">{{ $t('coupon_desc_unavailable')  }}</span>\n          <span v-else>{{ $t('coupon_nums', {0: availableCouponNum}) }}</span>\n        </div>\n      </div>\n      <template v-else>\n        <!--没有优惠券 -->\n        <div v-if=\"availableCouponNum === 0\" class=\"coupons-wrapper coupons-wrapper__no_coupon\">\n          {{ $t('coupon_desc_unavailable')  }}\n        </div>\n        <!--有优惠券 (首冲用了&&没有选中其他优惠券 || 没有使用首冲) -->\n        <div v-if=\"availableCouponNum > 0 && !chosenCoupon.FE_INDEX\"  class=\"coupons-wrapper coupons-wrapper__available\">\n          <span>{{ $t('coupon_nums', {0: availableCouponNum})  }}</span> <i></i>\n        </div>\n        <!--选中了优惠券 -->\n        <div v-if=\"availableCouponNum > 0 && chosenCoupon.FE_INDEX\" class=\"coupons-wrapper coupons-wrapper__chosen\">\n          <div class=\"left\">\n            <span>{{ $t('coupon_desc_chosen_erver')  }}</span>\n          </div>\n          <div class=\"right\">\n            <over-size-scale :key=\"chosenCoupon.FE_INDEX\">\n              <span>\n            <template v-if=\"chosenCoupon.feType==='discount_coupon'\">{{ $t('coupon_discount', { 0: chosenCoupon.rateWidthOutPercent }) }}</template>\n            <template v-if=\"chosenCoupon.feType==='cash_coupon'\"><span :class=\"{'is-ar-zone': isArZone}\">{{ chosenCoupon.deduct_price}} {{ currencyUnit }}</span> OFF</template>\n            <template v-if=\"chosenCoupon.feType==='rebate_coupon'\"> <span>{{ $t('bonus_tips') }} </span>{{ chosenCoupon.rate }}<i class=\"diamond-icon\"></i></template>\n          </span>\n            </over-size-scale>\n            <i></i>\n          </div>\n        </div>\n      </template>\n    </div>\n    <!--未登录-->\n    <div v-else class=\"coupons-wrapper coupons-wrapper__not-login\" @click=\"$root.$emit('ClickPayButNotLogin')\">{{ $t('view_coupons_after_login') }}</div>\n\n    <coupon-choose-pop\n      v-if=\"activity.showPop\"\n      :had-obtained-list=\"activity.hadObtainedList\"\n      :not-obtained-list=\"activity.notObtainedList\"\n      :last-index=\"activity.chosenIndex\"\n      :is-first-charge-used=\"activity.isFirstPayUsed\"\n      @close=\"closeCouponPop\">\n    </coupon-choose-pop>\n  </common-part>\n</template>\n\n<script>\nimport CommonPart from '@/components/common/CommonPart.vue'\nimport { getActivityListForToken } from '@/server'\nimport { mapGetters, mapState } from 'vuex'\nimport CouponChoosePop from '@/components/coupon/CouponChoosePop'\nimport OverSizeScale from '@/components/OverSizeScale.vue'\nimport device from 'current-device'\n// import { logForBtnClick, logForClickTokenChanel } from '@/lib/logHelper'\nconst getRate = value => ((1 - value) * 100).toFixed(0)\nconst getDiamondRate = value => ((value - 1) * 100) && ((value - 1) * 100).toFixed(0)\nconst getDiamondSend = value => value.coin - value.level_coin\n\nexport default {\n  name: 'CouponChoose',\n  components: { OverSizeScale, CommonPart, CouponChoosePop },\n  data () {\n    return {\n      activity: {\n        notObtainedList: [],\n        hadObtainedList: [], // 包括首冲的券（如果有的话第一个是）、优惠券\n        chosenIndex: -1,\n        showPop: false,\n\n        isFirstPayUsed: true,\n        timeInterval: undefined,\n\n        isLoadingCoupon: false\n      }\n    }\n  },\n  computed: {\n    ...mapState(['urlParams', 'isArZone', 'currencyUnit']),\n    ...mapState('formdata', ['chosenChannel', 'chosenCoupon', 'chosenDiamond', 'isFixedRebateWork']),\n    ...mapGetters('formdata', ['FinalPriceState']),\n    availableCouponNum () {\n      const l = this.activity.hadObtainedList\n      return (l && l.length) ? l.filter(item => item.feType.includes('_coupon') && (('leaveCount' in item && item.leaveCount > 0) || !('leaveCount' in item))).length : 0\n    }\n  },\n  methods: {\n    initActInfo (keepPop) {\n      // 置空上一次加载的数据\n      const { timeInterval } = this.activity\n      if (timeInterval) clearInterval(timeInterval)\n      this.activity = {\n        notObtainedList: [],\n        hadObtainedList: [], // 包括首冲的券（如果有的话第一个是）、优惠券\n        chosenIndex: -1,\n        showPop: false,\n        isFirstPayUsed: true,\n        timeInterval: undefined\n      }\n      this.$store.commit('formdata/resetCouponInfo')\n\n      // 默认券\n      const price = this.chosenDiamond.price\n      this.$root.$emit('setDefaultDiscountInfo', {\n        price,\n        discount_price: (price * 0.95).toFixed(2),\n        feType: 'fixed_discount_coupon',\n        FE_INDEX: 'fixed_discount_coupon_1',\n        rateWidthOutPercent: 5,\n        type: 'fixed_discount'\n      })\n\n      // 切换券后保持弹窗打开\n      if (sessionStorage.getItem('reopenCoupon') || keepPop) {\n        sessionStorage.removeItem('reopenCoupon')\n\n        this.$nextTick(() => {\n          this.activity.showPop = true\n        })\n      }\n    },\n    couponSort (coupons) {\n      const items = coupons\n      items.sort((a, b) => {\n        if (a.coupon_expire_time > b.coupon_expire_time) {\n          return 1\n        } else if (a.coupon_expire_time === b.coupon_expire_time) {\n          if (parseFloat(a.rateWidthOutPercent) < parseFloat(b.rateWidthOutPercent)) {\n            return 1\n          } else if (parseFloat(a.rateWidthOutPercent) === parseFloat(b.rateWidthOutPercent)) {\n            return 0\n          } else {\n            return -1\n          }\n        } else {\n          return -1\n        }\n      })\n      return items\n    },\n    loadActivity (isToggleRebate = false) {\n      // 兼容活动只在ss、koa上，其他游戏不请求链接。测试环境暂时还没兼容。\n      const params = {}\n      params.price = this.chosenDiamond.price\n      params.product_id = this.chosenDiamond.product_id\n\n      const { type, chosenNum } = this.chosenDiamond\n      if (type === 2) params.custom_multiple = chosenNum\n\n      if (!params.product_id) return null\n      if (this.chosenChannel) {\n        params.channel_id = this.chosenChannel.channel_id\n        params.sub_channel_id = this.chosenChannel.sub_channel_id\n      }\n\n      // sdk 添加 package_type 参数，来自 api/product 接口\n      if (this.$store.state.IS_CHECKOUT_SDK) {\n        params.package_type = this.chosenDiamond.package_type\n      }\n\n      // sdk为了优先展示，可能ip接口还没回来\n      if (this.$store.state.IS_CHECKOUT_SDK && !this.$store.state.country) {\n        params.country = 'US'\n        params.currency = 'USD'\n      }\n\n      this.$loading.show()\n      this.couponLoading = true\n      getActivityListForToken(params)\n        .then(res => {\n          this.initActInfo()\n          const { code, data, message } = res\n\n          if (isToggleRebate) {\n            if (this.isFixedRebateWork) data.fixed_discount = []\n            else data.fixed_rebate = []\n            this.$store.commit('formdata/toggleCoupon')\n          }\n\n          if (code === 0) {\n            // 特别档位 台湾 mycard 不适用任何优惠券\n            if (this.$store.getters['formdata/TWMyCard']) {\n              data.first_pay = data.coupon = data.deduct = data.fixed_discount = []\n            }\n            this.$store.commit('formdata/setIsInit', true)\n            if (this.$store.state.gameinfo.isKOA) this.$store.commit('formdata/setFirstPayProducts', data.range_first_pay || [])\n\n            if (this.$gameName === 'foundation') this.fixFoundationCoupon(params, data)\n            this.adapterCouponType(data)\n            // 首冲券\n            let firstPayOrigin = data.first_pay || []\n            firstPayOrigin = firstPayOrigin.map((item, index) => ({\n              ...item,\n              feType: 'first_pay',\n              rateWidthOutPercent: getRate(item.discount),\n              rate: `${getRate(item.discount)}%`,\n              FE_INDEX: `first_pay_${index}`,\n              productId: params.product_id\n            }))\n            // 兼容后端，返回首冲discount为0代表没有首冲\n            if (data.first_pay && data.first_pay.length && !data.first_pay[0].discount) firstPayOrigin = []\n            // 首冲返钻\n            if ((data.first_pay_rebate || []).length) {\n              firstPayOrigin = (data.first_pay_rebate || []).map((item, index) => ({\n                ...item,\n                feType: 'first_pay_rebate',\n                rate: `${getDiamondSend(item)}`,\n                FE_INDEX: `first_pay_rebate_${index}`,\n                productId: params.product_id\n              }))\n            }\n            // 折扣券 打折\n            let couponOrigin = data.coupon || []\n            couponOrigin = couponOrigin.map((item, index) => ({\n              ...item,\n              feType: 'discount_coupon',\n              rateWidthOutPercent: getRate(item.discount),\n              rate: `${getRate(item.discount)}%`,\n              FE_INDEX: `discount_coupon_${index}`,\n              productId: params.product_id\n            }))\n            const couponOriginCanUse = couponOrigin.filter(item => item.is_received && item.is_invalid) // 已获取，可用\n            const couponOriginCanNotUse = couponOrigin.filter(item => item.is_received && !item.is_invalid) // 已获取，不可用\n            // 抵扣券 抵钱\n            let deductOrigin = data.deduct || []\n            deductOrigin = deductOrigin.map((item, index) => ({\n              ...item,\n              feType: 'cash_coupon',\n              FE_INDEX: `cash_coupon_${index}`,\n              productId: params.product_id\n            }))\n            const deductOriginCanUse = deductOrigin.filter(item => item.is_received && item.is_invalid) // 已获取，可用\n            const deductOriginCanNotUse = deductOrigin.filter(item => item.is_received && !item.is_invalid) // 已获取，不可用\n            // 钻石券\n            let rebateOrigin = data.rebate || []\n            rebateOrigin = rebateOrigin.map((item, index) => ({\n              ...item,\n              feType: 'rebate_coupon',\n              FE_INDEX: `rebate_coupon_${index}`,\n              rate: `${getDiamondRate(item.discount)}%`,\n              rateWidthOutPercent: getDiamondRate(item.discount),\n              productId: params.product_id\n            }))\n            rebateOrigin = this.couponSort(rebateOrigin)\n            const rebateOriginCanUse = rebateOrigin.filter(item => item.is_received && item.is_invalid) // 已获取，可用\n            const rebateOriginCanNotUse = rebateOrigin.filter(item => item.is_received && !item.is_invalid) // 已获取，不可用\n            // 长期XX折活动\n            let defaultDiscountOrigin = data.fixed_discount || []\n            defaultDiscountOrigin = defaultDiscountOrigin.map((item, index) => ({\n              ...item,\n              feType: 'fixed_discount_coupon',\n              FE_INDEX: `fixed_discount_coupon_${index}`,\n              rateWidthOutPercent: getRate(item.discount)\n            }))\n            if (defaultDiscountOrigin.length) this.$store.commit('formdata/setFixedCoupon', defaultDiscountOrigin[0])\n\n            // 默认返钻\n            let fixedRebateOrigin = data.fixed_rebate || []\n            fixedRebateOrigin = fixedRebateOrigin.map((item, index) => ({\n              ...item,\n              feType: 'fixed_rebate',\n              rateWidthOutPercent: getDiamondRate(item.discount),\n              rate: `${getDiamondRate(item.discount)}%`,\n              FE_INDEX: `fixed_rebate_${index}`,\n              productId: params.product_id\n            }))\n            this.$store.commit('formdata/setFixedRebate', fixedRebateOrigin.length ? fixedRebateOrigin[0] : {})\n\n            // 默认动态返钻\n            let fixedDynamicRebateOrigin = data.product_fixed_rebate || []\n            fixedDynamicRebateOrigin = fixedDynamicRebateOrigin.map((item, index) => ({\n              ...item,\n              feType: 'fixed_dynamic_rebate',\n              rateWidthOutPercent: getDiamondRate(item.discount),\n              rate: `${getDiamondRate(item.discount)}%`,\n              FE_INDEX: `fixed_dynamic_rebate_${index}`,\n              productId: params.product_id\n            }))\n            this.$store.commit('formdata/setFixedDynamicRebate', {\n              chosen: fixedDynamicRebateOrigin[0] || {},\n              all: data.range_product_fixed_rebate || []\n            })\n\n            /* 倒计时 */\n            this.calcLeaveTime([...couponOrigin, ...deductOrigin, ...rebateOrigin].filter(item => item.is_received))\n\n            /* 左边一行列表 备注：如果有firstPayOrigin 作为第一个券，选中不可改 */\n            const leftObtainedList = [...firstPayOrigin, ...rebateOriginCanUse, ...deductOriginCanUse, ...couponOriginCanUse, ...rebateOriginCanNotUse, ...deductOriginCanNotUse, ...couponOriginCanNotUse]\n            // if (isSS) leftObtainedList = [...firstPayOrigin, ...couponOriginCanUse, ...deductOriginCanUse, ...couponOriginCanNotUse, ...deductOriginCanNotUse]\n            // if (isKOA) leftObtainedList = [...firstPayOrigin, ...couponOrigin]\n\n            /* 拥有的优惠券 */\n            this.activity.isFirstPayUsed = firstPayOrigin.length === 0\n            this.$store.commit('formdata/setFirstPayStatus', this.activity.isFirstPayUsed)\n            this.activity.hadObtainedList = leftObtainedList\n\n            // 拥有且可用券（任何类型：目前有首冲、优惠、满减）,如果有改成选中第一个\n            const availableList = [...rebateOriginCanUse, ...deductOriginCanUse, ...couponOriginCanUse]\n            // if (isSS) availableList = [...firstPayOrigin, ...deductOriginCanUse, ...couponOriginCanUse]\n            // if (isKOA) availableList = [...firstPayOrigin, ...couponOrigin].filter(item => item.is_received)\n            // !__HaveHigherPriorityActivity：如果发现上次返钻活动有可用的档位，直接不初始化chosenIndex。（返钻活动已下线）\n            // firstPayOrigin 如果有也会将chosenIndex 置为0\n            if (firstPayOrigin.length) {\n              this.$store.commit('formdata/setChosenCoupon', leftObtainedList[0])\n            } else {\n              // sdk2 固定折扣/返钻的优先级比优惠券高,所以不能选中优惠券\n              const canNotChooseCouponByDefault = this.$store.state.IS_CHECKOUT_SDK_V2 && [...defaultDiscountOrigin, ...fixedRebateOrigin].length > 0\n              if (availableList.length && !canNotChooseCouponByDefault) {\n                let index = 0\n\n                // 恢复切换前选中的券\n                const reChooseCouponId = sessionStorage.getItem('reChooseCoupon')\n                if (reChooseCouponId) {\n                  index = leftObtainedList.findIndex(item => item.coupon_id === +reChooseCouponId)\n                  index = Math.max(0, index)\n                }\n                this.$store.commit('formdata/setChosenCoupon', leftObtainedList[index])\n              }\n            }\n            this.parsingSdk2Coupon(availableList)\n\n            // 抵扣券适配ss周年庆活动\n            this.activity.hadObtainedList = this.activity.hadObtainedList.map(item => {\n              if (item.discount_range) {\n                const isCheckoutSdk = this.$store.state.IS_CHECKOUT_SDK\n                // 直购 && 优惠券\n                const diamondNumArr = (isCheckoutSdk && item.feType.includes('_coupon')) ? item.discount_price_range.split('-') : item.discount_range.split('-')\n                const currency = this.$store.state.currency\n                /* isCheckoutSdk 只设置了default的最大最小，其他都没设置 */\n\n                switch (this.$store.state.gameinfo.gameCode) {\n                  case 'KOA': case 'MO': {\n                    if (item.feType === 'cash_coupon') item.langValue = this.$t('min_cash_available_num', { 0: diamondNumArr[0], 1: this.$vt('tokenName') })\n                    if (item.feType === 'discount_coupon') item.langValue = this.$t('max_cash_available_num', { 0: diamondNumArr[1], 1: this.$vt('tokenName') })\n                    if (item.feType === 'rebate_coupon') item.langValue = this.$t('max_cash_available_num', { 0: diamondNumArr[1], 1: this.$vt('tokenName') })\n                    break\n                  }\n                  default: {\n                    // 复用gog的逻辑\n                    if (diamondNumArr.length > 1) {\n                      if (diamondNumArr[0] === '0') item.langValue = this.$t('max_cash_available_num', { 0: diamondNumArr[1], 1: this.$vt('tokenName') })\n                      else if (diamondNumArr[1] === '0') item.langValue = this.$t('min_cash_available_num', { 0: diamondNumArr[0], 1: this.$vt('tokenName') })\n                      else item.langValue = this.$t('btw_cash_available_num2', { 1: diamondNumArr[0], 2: diamondNumArr[1], 0: this.$vt('tokenName') })\n                    } else {\n                      item.langValue = this.$t('cash-num-eq-to', { 0: diamondNumArr[0], 1: this.$vt('tokenName') })\n                    }\n                  }\n                }\n\n                // 直购收银台单独的逻辑\n                if (this.$store.state.IS_CHECKOUT_SDK) {\n                  if (diamondNumArr.length > 1) {\n                    if (diamondNumArr[0] === '0') item.langValue = this.$t('max_cash_available_num', { 0: currency, 1: diamondNumArr[1] })\n                    else if (diamondNumArr[1] === '0') item.langValue = this.$t('min_cash_available_num', { 0: currency, 1: diamondNumArr[0] })\n                    else item.langValue = this.$t('btw_cash_available_num', { 0: currency, 1: diamondNumArr[0], 2: diamondNumArr[1] })\n                  } else {\n                    item.langValue = this.$t('cash-num-eq-to', { 0: currency, 1: diamondNumArr[0] })\n                  }\n                }\n              }\n              return item\n            })\n\n            /* 不可领优惠券 */\n            const notObtainedList = [...rebateOrigin, ...deductOrigin, ...couponOrigin].filter(item => !item.is_received)\n            const kind = {\n              'login_0.9': [],\n              'comm_third_0.8': [],\n              'comm_third_0.9': []\n            }\n            const langKeyMap = {\n              'login_0.9': 'login_gain_coupon',\n              'comm_third_0.9': 'invite_gain_coupon',\n              'comm_third_0.8': 'invite_gain_coupon'\n            }\n            notObtainedList.forEach(item => {\n              if (item.type.includes('login_') && item.discount === 0.9) kind['login_0.9'].push(item)\n              if (item.type.includes('comm_third') && item.discount === 0.8) kind['comm_third_0.8'].push(item)\n              if (item.type.includes('comm_third') && item.discount === 0.9) kind['comm_third_0.9'].push(item)\n            })\n            for (const [key, value] of Object.entries(kind)) {\n              value.length && this.activity.notObtainedList.push(\n                {\n                  ...value[0],\n                  num: value.length,\n                  langKey: langKeyMap[key]\n                }\n              )\n            }\n            this.activity.notObtainedList = this.activity.notObtainedList.sort((a, b) => a.discount - b.discount)\n\n            this.$root.$emit('activityInitEnd')\n          } else {\n            throw Error(message)\n          }\n        })\n        .catch(err => {\n          this.initActInfo()\n          this.$toast.err(this.$t('network_err'))\n          console.error(`优惠券初始化失败：${err.message}`)\n        })\n        .finally(() => {\n          this.couponLoading = false\n          this.$loading.hide()\n          sessionStorage.removeItem('reChooseCoupon')\n        })\n    },\n    fixActivityInfo () {\n      if (this.couponLoading) return null\n      const couponFeIndex = this.chosenCoupon.FE_INDEX\n\n      // 兼容活动只在ss、koa上，其他游戏不请求链接。测试环境暂时还没兼容。\n      const params = {}\n      params.price = this.chosenDiamond.price\n      params.product_id = this.chosenDiamond.product_id\n\n      const { type, chosenNum } = this.chosenDiamond\n      if (type === 2) params.custom_multiple = chosenNum\n\n      if (!params.product_id) return null\n\n      if (this.chosenChannel) {\n        params.channel_id = this.chosenChannel.channel_id\n        params.sub_channel_id = this.chosenChannel.sub_channel_id\n      }\n\n      // sdk 添加 package_type 参数，来自 api/product 接口\n      if (this.$store.state.IS_CHECKOUT_SDK) {\n        params.package_type = this.chosenDiamond.package_type\n      }\n\n      // sdk为了优先展示，可能ip接口还没回来\n      if (this.$store.state.IS_CHECKOUT_SDK && !this.$store.state.country) {\n        params.country = 'US'\n        params.currency = 'USD'\n      }\n\n      // this.$loading.show()\n      getActivityListForToken(params)\n        .then(res => {\n          // 如果修复之前是开启 一样开启\n          this.initActInfo(this.activity.showPop)\n          const { code, data, message } = res\n          if (code === 0) {\n            if (this.$store.getters['formdata/TWMyCard']) {\n              data.first_pay = data.coupon = data.deduct = data.fixed_discount = []\n            }\n            this.$store.commit('formdata/setIsInit', true)\n            if (this.$gameName === 'foundation') this.fixFoundationCoupon(params, data)\n            this.adapterCouponType(data)\n            // 首冲券\n            let firstPayOrigin = data.first_pay || []\n            firstPayOrigin = firstPayOrigin.map((item, index) => ({\n              ...item,\n              feType: 'first_pay',\n              rateWidthOutPercent: getRate(item.discount),\n              rate: `${getRate(item.discount)}%`,\n              FE_INDEX: `first_pay_${index}`,\n              productId: params.product_id\n            }))\n            // 兼容后端，返回首冲discount为0代表没有首冲\n            if (data.first_pay && data.first_pay.length && !data.first_pay[0].discount) firstPayOrigin = []\n            // 首冲返钻\n            if ((data.first_pay_rebate || []).length) {\n              firstPayOrigin = (data.first_pay_rebate || []).map((item, index) => ({\n                ...item,\n                feType: 'first_pay_rebate',\n                rate: `${getDiamondSend(item)}`,\n                FE_INDEX: `first_pay_rebate_${index}`,\n                productId: params.product_id\n              }))\n            }\n            // 折扣券 打折\n            let couponOrigin = data.coupon || []\n            couponOrigin = couponOrigin.map((item, index) => ({\n              ...item,\n              feType: 'discount_coupon',\n              rateWidthOutPercent: getRate(item.discount),\n              rate: `${getRate(item.discount)}%`,\n              FE_INDEX: `discount_coupon_${index}`,\n              productId: params.product_id\n            }))\n            const couponOriginCanUse = couponOrigin.filter(item => item.is_received && item.is_invalid) // 已获取，可用\n            const couponOriginCanNotUse = couponOrigin.filter(item => item.is_received && !item.is_invalid) // 已获取，不可用\n            // 抵扣券 抵钱\n            let deductOrigin = data.deduct || []\n            deductOrigin = deductOrigin.map((item, index) => ({\n              ...item,\n              feType: 'cash_coupon',\n              FE_INDEX: `cash_coupon_${index}`,\n              productId: params.product_id\n            }))\n            const deductOriginCanUse = deductOrigin.filter(item => item.is_received && item.is_invalid) // 已获取，可用\n            const deductOriginCanNotUse = deductOrigin.filter(item => item.is_received && !item.is_invalid) // 已获取，不可用\n            // 钻石券\n            let rebateOrigin = data.rebate || []\n            rebateOrigin = rebateOrigin.map((item, index) => ({\n              ...item,\n              feType: 'rebate_coupon',\n              FE_INDEX: `rebate_coupon_${index}`,\n              rate: `${getDiamondRate(item.discount)}%`,\n              rateWidthOutPercent: getDiamondRate(item.discount),\n              productId: params.product_id\n            }))\n            rebateOrigin = this.couponSort(rebateOrigin)\n            const rebateOriginCanUse = rebateOrigin.filter(item => item.is_received && item.is_invalid) // 已获取，可用\n            const rebateOriginCanNotUse = rebateOrigin.filter(item => item.is_received && !item.is_invalid) // 已获取，不可用\n            // 长期XX折活动\n            let defaultDiscountOrigin = data.fixed_discount || []\n            defaultDiscountOrigin = defaultDiscountOrigin.map((item, index) => ({\n              ...item,\n              feType: 'fixed_discount_coupon',\n              FE_INDEX: `fixed_discount_coupon_${index}`,\n              rateWidthOutPercent: getRate(item.discount)\n            }))\n            if (defaultDiscountOrigin.length) this.$store.commit('formdata/setFixedCoupon', defaultDiscountOrigin[0])\n\n            // 默认返钻\n            let fixedRebateOrigin = data.fixed_rebate || []\n            fixedRebateOrigin = fixedRebateOrigin.map((item, index) => ({\n              ...item,\n              feType: 'fixed_rebate',\n              rateWidthOutPercent: getDiamondRate(item.discount),\n              rate: `${getDiamondRate(item.discount)}%`,\n              FE_INDEX: `fixed_rebate_${index}`,\n              productId: params.product_id\n            }))\n            this.$store.commit('formdata/setFixedRebate', fixedRebateOrigin.length ? fixedRebateOrigin[0] : {})\n\n            // 默认动态返钻\n            let fixedDynamicRebateOrigin = data.product_fixed_rebate || []\n            fixedDynamicRebateOrigin = fixedDynamicRebateOrigin.map((item, index) => ({\n              ...item,\n              feType: 'fixed_dynamic_rebate',\n              rateWidthOutPercent: getDiamondRate(item.discount),\n              rate: `${getDiamondRate(item.discount)}%`,\n              FE_INDEX: `fixed_dynamic_rebate_${index}`,\n              productId: params.product_id\n            }))\n            this.$store.commit('formdata/setFixedDynamicRebate', {\n              chosen: fixedDynamicRebateOrigin[0] || {},\n              all: data.range_product_fixed_rebate || []\n            })\n\n            /* 倒计时 */\n            this.calcLeaveTime([...couponOrigin, ...deductOrigin, ...rebateOrigin].filter(item => item.is_received))\n\n            /* 左边一行列表 备注：如果有firstPayOrigin 作为第一个券，选中不可改 */\n            const leftObtainedList = [...firstPayOrigin, ...rebateOriginCanUse, ...deductOriginCanUse, ...couponOriginCanUse, ...rebateOriginCanNotUse, ...deductOriginCanNotUse, ...couponOriginCanNotUse]\n\n            /* 拥有的优惠券 */\n            this.activity.isFirstPayUsed = firstPayOrigin.length === 0\n            this.$store.commit('formdata/setFirstPayStatus', this.activity.isFirstPayUsed)\n            this.activity.hadObtainedList = leftObtainedList\n\n            // 拥有且可用券（任何类型：目前有首冲、优惠、满减）,如果有改成选中第一个\n            const availableList = [...rebateOriginCanUse, ...deductOriginCanUse, ...couponOriginCanUse]\n\n            if (couponFeIndex) {\n              if (firstPayOrigin.length) {\n                if (couponFeIndex === leftObtainedList[0].FE_INDEX) this.$store.commit('formdata/setChosenCoupon', leftObtainedList[0])\n              } else {\n                if (availableList.length) {\n                  const findLastIndex = leftObtainedList.findIndex(item => item.FE_INDEX === couponFeIndex) || 0\n                  this.$store.commit('formdata/setChosenCoupon', leftObtainedList[findLastIndex])\n                }\n              }\n            }\n            this.parsingSdk2Coupon(availableList)\n\n            // 抵扣券适配ss周年庆活动\n            this.activity.hadObtainedList = this.activity.hadObtainedList.map(item => {\n              if (item.discount_range) {\n                const isCheckoutSdk = this.$store.state.IS_CHECKOUT_SDK\n                // 直购 && 优惠券\n                const diamondNumArr = (isCheckoutSdk && item.feType.includes('_coupon')) ? item.discount_price_range.split('-') : item.discount_range.split('-')\n                const currency = this.$store.state.currency\n                /* isCheckoutSdk 只设置了default的最大最小，其他都没设置 */\n\n                switch (this.$store.state.gameinfo.gameCode) {\n                  case 'KOA': case 'MO': {\n                    if (item.feType === 'cash_coupon') item.langValue = this.$t('min_cash_available_num', { 0: diamondNumArr[0], 1: this.$vt('tokenName') })\n                    if (item.feType === 'discount_coupon') item.langValue = this.$t('max_cash_available_num', { 0: diamondNumArr[1], 1: this.$vt('tokenName') })\n                    if (item.feType === 'rebate_coupon') item.langValue = this.$t('max_cash_available_num', { 0: diamondNumArr[1], 1: this.$vt('tokenName') })\n                    break\n                  }\n                  default: {\n                    // 复用gog的逻辑\n                    if (diamondNumArr.length > 1) {\n                      if (diamondNumArr[0] === '0') item.langValue = this.$t('max_cash_available_num', { 0: diamondNumArr[1], 1: this.$vt('tokenName') })\n                      else if (diamondNumArr[1] === '0') item.langValue = this.$t('min_cash_available_num', { 0: diamondNumArr[0], 1: this.$vt('tokenName') })\n                      else item.langValue = this.$t('btw_cash_available_num2', { 1: diamondNumArr[0], 2: diamondNumArr[1], 0: this.$vt('tokenName') })\n                    } else {\n                      item.langValue = this.$t('cash-num-eq-to', { 0: diamondNumArr[0], 1: this.$vt('tokenName') })\n                    }\n                  }\n                }\n\n                // 直购收银台单独的逻辑\n                if (this.$store.state.IS_CHECKOUT_SDK) {\n                  if (diamondNumArr.length > 1) {\n                    if (diamondNumArr[0] === '0') item.langValue = this.$t('max_cash_available_num', { 0: currency, 1: diamondNumArr[1] })\n                    else if (diamondNumArr[1] === '0') item.langValue = this.$t('min_cash_available_num', { 0: currency, 1: diamondNumArr[0] })\n                    else item.langValue = this.$t('btw_cash_available_num', { 0: currency, 1: diamondNumArr[0], 2: diamondNumArr[1] })\n                  } else {\n                    item.langValue = this.$t('cash-num-eq-to', { 0: currency, 1: diamondNumArr[0] })\n                  }\n                }\n              }\n              return item\n            })\n\n            /* 不可领优惠券 */\n            const notObtainedList = [...rebateOrigin, ...deductOrigin, ...couponOrigin].filter(item => !item.is_received)\n            const kind = {\n              'login_0.9': [],\n              'comm_third_0.8': [],\n              'comm_third_0.9': []\n            }\n            const langKeyMap = {\n              'login_0.9': 'login_gain_coupon',\n              'comm_third_0.9': 'invite_gain_coupon',\n              'comm_third_0.8': 'invite_gain_coupon'\n            }\n            notObtainedList.forEach(item => {\n              if (item.type.includes('login_') && item.discount === 0.9) kind['login_0.9'].push(item)\n              if (item.type.includes('comm_third') && item.discount === 0.8) kind['comm_third_0.8'].push(item)\n              if (item.type.includes('comm_third') && item.discount === 0.9) kind['comm_third_0.9'].push(item)\n            })\n            for (const [key, value] of Object.entries(kind)) {\n              value.length && this.activity.notObtainedList.push(\n                {\n                  ...value[0],\n                  num: value.length,\n                  langKey: langKeyMap[key]\n                }\n              )\n            }\n            this.activity.notObtainedList = this.activity.notObtainedList.sort((a, b) => a.discount - b.discount)\n          } else {\n            throw Error(message)\n          }\n        })\n        .catch(err => {\n          this.initActInfo()\n          this.$toast.err(this.$t('network_err'))\n          console.error(`优惠券初始化失败：${err.message}`)\n        })\n        // .finally(() => this.$loading.hide())\n    },\n    calcLeaveTime (list) {\n      const fixDate = p => p < 10 ? `0${Math.floor(p)}` : Math.floor(p)\n      const getLeaveDateTxt = count => `${Math.floor(count / 3600 / 24)}d ${fixDate(count / 3600 % 24)} : ${fixDate((count / 60) % 60)} : ${fixDate(count % 60)}`\n      const newList = list.filter(item => item.coupon_expire_time && (item.coupon_expire_time > 0))\n      for (const value of Object.values(newList)) {\n        const count = value.leaveCount = value.coupon_expire_time\n        value.showLeaveDate = getLeaveDateTxt(count)\n      }\n\n      this.activity.timeInterval = setInterval(() => {\n        for (const value of Object.values(newList)) {\n          const count = value.leaveCount - 1\n          if (count >= 0) {\n            value.leaveCount--\n            value.showLeaveDate = getLeaveDateTxt(count)\n          }\n          if (count === 0) {\n            // 如果倒计时这个为0 且该券选中了 去掉选中状态\n            if (this.chosenCoupon.FE_INDEX === value.FE_INDEX) {\n              // this.activity.chosenIndex = -1\n              this.$store.commit('formdata/setChosenCoupon', {})\n              this.$root.$emit('couponChoose')\n              // this.$root.$emit('chosenTicketTimeOut')\n            }\n          }\n        }\n      }, 1000)\n    },\n    closeCouponPop (value) {\n      this.activity.showPop = false\n      this.$root.$emit('TicketPopClose')\n    },\n\n    parsingSdk2Coupon (couponList) {\n      if (this.$store.state.IS_CHECKOUT_SDK_V2) this.$root.$emit('updateSdk2CouponList', couponList)\n    },\n    fixFoundationCoupon (requestParams, returnData) {\n      /* foundation 每个档位都有单独的首冲 */\n      const firstCouponList = returnData.coin_level_first_pay\n      if (!firstCouponList || !firstCouponList.length) return null\n\n      const rangeList = firstCouponList.map(item => {\n        item.rate = `${getDiamondSend(item)}`\n        item.feType = 'first_pay_rebate'\n        return item\n      })\n      if (rangeList.length) {\n        this.$store.commit('formdata/setFirstPayProducts', [{\n          product_discount_range: rangeList\n        }])\n      }\n      const filterFirstPayList = rangeList.filter(item => item.product_id === requestParams.product_id) || []\n      if (filterFirstPayList.length) {\n        filterFirstPayList[0].act_type = 'coin_level_first_pay'\n        returnData.first_pay_rebate = filterFirstPayList\n      }\n    },\n    adapterCouponType (data) {\n      if (this.$store.state.IS_CHECKOUT_SDK_V2) {\n        data.first_pay = data.direct_first_pay\n        data.first_pay_rebate = data.direct_first_pay_rebate\n        data.fixed_discount = data.direct_fixed_discount\n        data.fixed_rebate = data.direct_fixed_rebate\n        Reflect.deleteProperty(data, 'direct_first_pay')\n        Reflect.deleteProperty(data, 'direct_first_pay_rebate')\n        Reflect.deleteProperty(data, 'direct_fixed_discount')\n        Reflect.deleteProperty(data, 'direct_fixed_rebate')\n      }\n    }\n  },\n  created () {\n    const objUrl = '$store.state.formdata.chosenDiamond.product_id'\n    this.unwatch = this.$watch(objUrl, value => value && this.loadActivity(), { immediate: true })\n\n    // 如果新旧值都有，说明是切换的当前的数量，避免请求两次。\n    const objDiamondUrl = '$store.state.formdata.chosenDiamond.totalDiamond'\n    this.unwatch = this.$watch(objDiamondUrl, (newValue, oldValue) => newValue && oldValue && this.loadActivity())\n\n    this.$root.$once('loginSuccess', () => this.loadActivity())\n\n    const FE_CHANNEL_ID = '$store.state.formdata.chosenChannel.FE_CHANNEL_ID'\n    this.unwatch = this.$watch(FE_CHANNEL_ID, newValue => newValue && this.fixActivityInfo())\n\n    // 切换优惠券类型 koa\n    this.$root.$on('couponToggleSuccess', () => this.loadActivity())\n    // ss 小档位重置\n    this.$root.$on('reloadActivity', () => this.loadActivity())\n    // 切换优惠券类型 gog\n    this.$root.$on('toggleFixedCoupon', (flag) => this.loadActivity(flag))\n  },\n  mounted () {\n    if (device.ios()) this.$watch('activity.showPop', (value) => this.$root.$emit('showCouponPop', value))\n  },\n  beforeDestroy () {\n    this.unwatch && this.unwatch()\n  }\n}\n</script>\n<!--500000350-->\n\n<style scoped lang=\"scss\">\n@use \"~@/utils/utils.scss\" as utils;\n\n::v-deep{\n  .body{\n    display: flex;\n  }\n}\n.coupons-wrapper {\n  transition: background .5s;\n  border-radius: 4px;\n  cursor: pointer;\n}\n.coupons-wrapper__not-login {\n  @include utils.bgCenter('koa/coupon/pay-coupon-bg_mobile_login.png', 672px, 68px);\n  position: relative;\n  font-size: 22px;\n  font-family: SourceHanSansSC;\n  font-weight: 400;\n  color: #808080;\n  padding-left: 48px;\n  line-height: 68px;\n\n  &:after {\n    content: '';\n    @include utils.bgCenter('koa/coupon/pay-coupon-bg_mobile_login_tanhao.png', 23px, 23px);\n    position: absolute;\n    left: 13px;\n    top: 50%;\n    transform: translateY(-50%);\n    z-index: 1;\n  }\n}\n.coupons-wrapper__available {\n  @include utils.bgCenter('koa/coupon/pay-coupon-bg_mobile-available.png', 672px, 68px);\n  font-size: 24px;\n  font-family: SourceHanSansSC;\n  font-weight: 600;\n  color: #633B00;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding-left: 21px;\n  padding-right: 21px;\n\n  i {\n    @include utils.bgCenter('koa/coupon/pay-coupon-bg_mobile-available-arrow.png', 24px, 24px);\n    display: inline-block;\n  }\n}\n.coupons-wrapper__chosen {\n  @include utils.bgCenter('koa/coupon/pay-coupon-bg_mobile-chosen.png', 672px, 68px);\n  padding-left: 21px;\n  padding-right: 20px;\n  display: flex;\n\n  .left {\n    width: 76.6%;\n    display: flex;\n    align-items: center;\n    justify-content: flex-start;\n\n    span {\n      font-size: 24px;\n      font-family: PingFangSC-Semibold, PingFang SC;\n      font-weight: 600;\n      color: #633B00;\n      display: inline-block;\n      white-space: nowrap;\n      position: relative;\n      margin-right: 30px;\n\n      &:after{\n        content: '';\n        @include utils.bgCenter('koa/coupon/coupon_chosen_gou.png', 24px, 24px);\n        position: absolute;\n        right: -34px;\n        top: 50%;\n        transform: translateY(-50%);\n      }\n    }\n  }\n\n  .right {\n    flex: 1;\n    padding-left: 10px;\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    font-size: 24px;\n    font-family: PingFang;\n    font-weight: 600;\n    color: #633B00;\n    flex-wrap: nowrap;\n    white-space: nowrap;\n\n    i {\n      @include utils.bgCenter('koa/coupon/pay-coupon-bg_mobile-chosen-arrow.png', 24px, 24px);\n    }\n    span {\n      ::v-deep{\n        i.diamond-icon {\n          @include utils.bgCenterForKoaIcon('koa/diamond/diamond.png', 22px, 18px);\n          display: inline-block;\n          margin: 0 2px;\n        }\n      }\n    }\n  }\n}\n.coupons-wrapper__unavailable {\n  //@include utils.bgCenter('pay-coupon-bg_mobile_login.png', 672px, 68px);\n  //font-size: 22px;\n  //font-family: SourceHanSansSC;\n  //font-weight: 400;\n  //color: #808080;\n  //padding-left: 13px;\n  //line-height: 68px;\n  @extend .coupons-wrapper__chosen;\n  .left span:after{\n    display: none;\n  }\n}\n.coupons-wrapper__no_coupon{\n  @include utils.bgCenter('koa/coupon/pay-coupon-bg_mobile_login.png', 672px, 68px);\n  font-size: 24px;\n  font-family: PingFangSC-Semibold, PingFang SC;\n  font-weight: 600;\n  color: #818080;\n  padding-left: 20px;\n  display: flex;\n  align-items: center;\n}\n@include utils.setPcContent{\n  .coupons-wrapper {\n    border-radius: 4PX;\n  }\n\n  .coupons-wrapper__not-login {\n    @include utils.bgCenter('koa/coupon/pay-coupon-bg_pc_login.png', 425PX, 44PX);\n    font-size: 14PX;\n    font-family: PingFangSC-Regular, PingFang SC;\n    padding-left: 41PX;\n    line-height: 44PX;\n    color: #818080;\n\n    &:after {\n      content: '';\n      @include utils.bgCenter('koa/coupon/pay-coupon-bg_mobile_login_tanhao.png', 20PX, 20PX);\n      left: 12PX;\n    }\n  }\n\n  .coupons-wrapper__available {\n    @include utils.bgCenter('koa/coupon/pay-coupon-bg_pc-available.png', 425PX, 43PX);\n    font-size: 14PX;\n    padding-left: 12PX;\n    padding-right: 12PX;\n\n    i {\n      @include utils.bgCenter('koa/coupon/pay-coupon-bg_pc-available-arrow.png', 18PX, 18PX);\n    }\n  }\n\n  .coupons-wrapper__chosen,\n  .coupons-wrapper__unavailable {\n    @include utils.bgCenter('koa/coupon/pay-coupon-bg_pc-chosen.png', 420PX, 43PX);\n    padding-left: 12PX;\n    padding-right: 12PX;\n\n    .left {\n      span {\n        font-size: 14PX;\n        font-family: PingFangSC-Regular, PingFang SC;\n        font-weight: 400;\n        color: #633B00;\n        margin-right: 20PX;\n\n        &:after{\n          content: '';\n          @include utils.bgCenter('koa/coupon/coupon_chosen_gou.png', 14PX, 14PX);\n          position: absolute;\n          right: -20PX;\n          top: 50%;\n          transform: translateY(-50%);\n        }\n      }\n    }\n\n    .right {\n      padding-left: 10PX;\n      font-size: 14PX;\n      font-family: PingFang;\n\n      i {\n        @include utils.bgCenter('koa/coupon/pay-coupon-bg_mobile-chosen-arrow.png', 18PX, 18PX);\n      }\n      span {\n        ::v-deep{\n          i.diamond-icon {\n            @include utils.bgCenterForKoaIcon('koa/diamond/diamond.png', 14px, 11px);\n            display: inline-block;\n            margin: 0 2px;\n          }\n        }\n      }\n    }\n  }\n\n  .coupons-wrapper__no_coupon{\n    @include utils.bgCenter('koa/coupon/pay-coupon-bg_pc_login.png', 425PX, 44PX);\n    padding-left: 12PX;\n    font-size: 14PX;\n  }\n}\n@include utils.setMobileContent{\n  ::v-deep{\n    .label{\n      display: none;\n    }\n  }\n}\n\n/* dc */\n.coupon-bar-wrapper.dc{\n  .coupons-wrapper {\n    border-radius: 0;\n  }\n  .coupons-wrapper__not-login {\n    @include utils.bgCenterForDC('coupon/coupon-bar__not-login-m.png', 672px, 82px);\n    color: #8E9CC7;\n    line-height: 82px;\n\n    &:after {\n      @include utils.bgCenterForDC('coupon/coupon-bar-icon-exclamation-mark.png', 30px, 30px);\n    }\n  }\n  .coupons-wrapper__available {\n    @include utils.bgCenterForDC('coupon/pay-coupon-bg_mobile-available.png', 675px, 82px);\n    font-size: 24px;\n    line-height: 82px;\n    color: #F4FBFF;\n    @extend .dc-stroke;\n\n    i {\n      @include utils.bgCenterForDC('coupon/pay-coupon-bg_mobile-available-arrow.png', 24px, 24px);\n    }\n  }\n  .coupons-wrapper__chosen, .coupons-wrapper__unavailable{\n    @include utils.bgCenterForDC('coupon/pay-coupon-bg_mobile-chosen.png', 675px, 82px);\n    .left{\n      span{\n        color: #4A402C;\n        line-height: 1;\n\n        &:after{\n          @include utils.bgCenterForDC('coupon/coupon_chosen_gou.png', 24px, 24px);\n        }\n      }\n    }\n\n    .right{\n      color: #4A402C;\n      //i {\n      //  @include utils.bgCenterForDC('coupon/pay-coupon-bg_mobile-chosen-arrow.png', 24px, 24px);\n      //}\n      ::v-deep{\n        .diamond-icon {\n          @include utils.bgCenterForDC('diamond/diamond-icon.png', 22px, 22px);\n        }\n      }\n    }\n  }\n  .coupons-wrapper__no_coupon{\n    @include utils.bgCenterForDC('coupon/pay-coupon-bg_mobile_login.png', 675px, 82px);\n    color: #8E9CC7;\n    line-height: 1;\n  }\n\n  @include utils.setPcContent{\n    .coupons-wrapper__not-login{\n      @include utils.bgCenterForDC('coupon/coupon-bar__not-login-m.png', 428px, 47px);\n      line-height: 47px;\n      padding-left: 37px;\n      &:after {\n        @include utils.bgCenterForDC('coupon/coupon-bar-icon-exclamation-mark.png', 14px, 14px);\n      }\n    }\n    .coupons-wrapper__available{\n      @include utils.bgCenterForDC('coupon/pay-coupon-bg_mobile-available-pc.png', 428px, 47px);\n      font-size: 14px;\n      line-height: 47px;\n\n      i {\n        @include utils.bgCenterForDC('coupon/pay-coupon-bg_mobile-available-arrow.png', 18px, 18px);\n      }\n    }\n    .coupons-wrapper__chosen, .coupons-wrapper__unavailable{\n      @include utils.bgCenterForDC('coupon/pay-coupon-bg_mobile-chosen-pc.png', 428px, 47px);\n      .left{\n        span{\n          &:after{\n            @include utils.bgCenterForDC('coupon/coupon_chosen_gou.png', 14px, 14px);\n          }\n        }\n      }\n      .right{\n        ::v-deep{\n          .diamond-icon {\n            @include utils.bgCenterForDC('diamond/diamond-icon.png', 16px, 16px);\n          }\n        }\n      }\n    }\n    .coupons-wrapper__no_coupon{\n      @include utils.bgCenterForDC('coupon/pay-coupon-bg_mobile_login-pc.png', 428px, 47px);\n    }\n  }\n}\n/* ssv */\n.coupon-bar-wrapper.ssv{\n  .coupons-wrapper__available {\n    @include utils.bgCenterForSSV('coupon/pay-coupon-bg_mobile-available.png', 672px, 68px);\n    color: #FF5A00;\n\n    i {\n      @include utils.bgCenterForSSV('coupon/pay-coupon-bg_mobile-available-arrow.png', 23px, 20px);\n    }\n  }\n\n  @include utils.setPcContent{\n    .coupons-wrapper__available{\n      @include utils.bgCenterForSSV('coupon/pay-coupon-bg_pc-available.png', 425PX, 43PX);\n      font-size: 16PX;\n      padding-left: 14PX;\n      padding-right: 10PX;\n\n      i {\n        @include utils.bgCenterForSSV('coupon/pay-coupon-bg_pc-available-arrow.png', 19PX, 17PX);\n      }\n    }\n  }\n}\n\n/* ssv2 */\n.coupon-bar-wrapper.ssv2{\n  .coupons-wrapper__not-login {\n    @include utils.bgCenterForSS('coupon/pay-coupon-bg_mobile_login.png', 672px, 68px);\n    position: relative;\n    font-size: 22px;\n    font-weight: 400;\n    color: #808080;\n    padding-left: 48px;\n    line-height: 68px;\n\n    &:after {\n      content: '';\n      @include utils.bgCenterForSS('coupon/pay-coupon-bg_mobile_login_tanhao.png', 23px, 23px);\n      position: absolute;\n      left: 13px;\n      top: 50%;\n      transform: translateY(-50%);\n      z-index: 1;\n    }\n  }\n  .coupons-wrapper__available {\n    @include utils.bgCenterForSS('coupon/pay-coupon-bg_mobile-available.png', 672px, 68px);\n    font-size: 22px;\n    font-family: SourceHanSansSC;\n    font-weight: 400;\n    color: #FF5A00;\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    padding-left: 20px;\n    padding-right: 18px;\n\n    i {\n      @include utils.bgCenterForSS('coupon/pay-coupon-bg_mobile-available-arrow.png', 23px, 20px);\n      display: inline-block;\n    }\n  }\n  .coupons-wrapper__unavailable,\n  .coupons-wrapper__chosen {\n    @include utils.bgCenterForSS('coupon/pay-coupon-bg_mobile-chosen.png', 672px, 68px);\n    padding-left: 10px;\n    padding-right: 18px;\n    display: flex;\n\n    .left {\n      width: 76.6%;\n      display: flex;\n      align-items: center;\n      justify-content: flex-start;\n\n      span {\n        font-size: 18px;\n        font-family: SourceHanSansSC;\n        font-weight: 400;\n        color: #FF5400;\n        line-height: 44px;\n        border: 1px solid #FF5E0F;\n        border-radius: 4px;\n        display: inline-block;\n        padding: 0 52px;\n        white-space: nowrap;\n\n        &:after{\n          display: none;\n        }\n      }\n    }\n\n    .right {\n      flex: 1;\n      padding-left: 10px;\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      font-size: 24px;\n      font-family: PingFang;\n      font-weight: 400;\n      color: #FF5E0F;\n      flex-wrap: nowrap;\n      white-space: nowrap;\n\n      i {\n        @include utils.bgCenterForSS('coupon/pay-coupon-bg_mobile-chosen-arrow.png', 10px, 14px);\n        margin-left: 15px;\n      }\n    }\n  }\n  .coupons-wrapper__no_coupon{\n    @include utils.bgCenterForSS('coupon/pay-coupon-bg_mobile_login.png', 672px, 68px);\n    font-size: 24px;\n    font-family: PingFangSC-Semibold, PingFang SC;\n    font-weight: 600;\n    color: #818080;\n    padding-left: 20px;\n    display: flex;\n    align-items: center;\n  }\n\n  @include utils.setPcContent{\n    .coupons-wrapper {\n      border-radius: 4PX;\n    }\n\n    .coupons-wrapper__not-login {\n      @include utils.bgCenterForSS('coupon/pay-coupon-bg_pc_login.png', 425PX, 43PX);\n      font-size: 16PX;\n      font-family: SourceHanSansSC;\n      padding-left: 41PX;\n      line-height: 43PX;\n\n      &:after {\n        content: '';\n        @include utils.bgCenterForSS('coupon/pay-coupon-bg_mobile_login_tanhao.png', 21PX, 21PX);\n        left: 12PX;\n      }\n    }\n\n    .coupons-wrapper__available {\n      @include utils.bgCenterForSS('coupon/pay-coupon-bg_pc-available.png', 425PX, 43PX);\n      font-size: 16PX;\n      padding-left: 14PX;\n      padding-right: 10PX;\n\n      i {\n        @include utils.bgCenterForSS('coupon/pay-coupon-bg_pc-available-arrow.png', 19PX, 17PX);\n      }\n    }\n\n    .coupons-wrapper__unavailable,\n    .coupons-wrapper__chosen {\n      @include utils.bgCenterForSS('coupon/pay-coupon-bg_pc-chosen.png', 425PX, 43PX);\n      padding-left: 10PX;\n      padding-right: 7PX;\n\n      .left {\n\n        span {\n          font-size: 12PX;\n          color: #FF5400;\n          line-height: 26PX;\n          border: 1PX solid #FF5E0F;\n          border-radius: 3PX;\n          padding: 0 13PX;\n        }\n      }\n\n      .right {\n        padding-left: 10PX;\n        font-size: 15PX;\n        font-family: PingFang;\n\n        i {\n          @include utils.bgCenterForSS('coupon/pay-coupon-bg_mobile-chosen-arrow.png', 10PX, 13PX);\n          margin-left: 6PX;\n        }\n      }\n    }\n    .coupons-wrapper__no_coupon{\n      @include utils.bgCenterForSS('coupon/pay-coupon-bg_pc_login.png', 425PX, 44PX);\n      padding-left: 12PX;\n      font-size: 14PX;\n    }\n  }\n}\n</style>\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./CouponChoose.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./CouponChoose.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./CouponChoose.vue?vue&type=template&id=066665a8&scoped=true\"\nimport script from \"./CouponChoose.vue?vue&type=script&lang=js\"\nexport * from \"./CouponChoose.vue?vue&type=script&lang=js\"\nimport style0 from \"./CouponChoose.vue?vue&type=style&index=0&id=066665a8&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"066665a8\",\n  null\n  \n)\n\nexport default component.exports", "'use strict';\nvar defineBuiltIn = require('../internals/define-built-in');\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar toString = require('../internals/to-string');\nvar validateArgumentsLength = require('../internals/validate-arguments-length');\n\nvar $URLSearchParams = URLSearchParams;\nvar URLSearchParamsPrototype = $URLSearchParams.prototype;\nvar append = uncurryThis(URLSearchParamsPrototype.append);\nvar $delete = uncurryThis(URLSearchParamsPrototype['delete']);\nvar forEach = uncurryThis(URLSearchParamsPrototype.forEach);\nvar push = uncurryThis([].push);\nvar params = new $URLSearchParams('a=1&a=2&b=3');\n\nparams['delete']('a', 1);\n// `undefined` case is a Chromium 117 bug\n// https://bugs.chromium.org/p/v8/issues/detail?id=14222\nparams['delete']('b', undefined);\n\nif (params + '' !== 'a=2') {\n  defineBuiltIn(URLSearchParamsPrototype, 'delete', function (name /* , value */) {\n    var length = arguments.length;\n    var $value = length < 2 ? undefined : arguments[1];\n    if (length && $value === undefined) return $delete(this, name);\n    var entries = [];\n    forEach(this, function (v, k) { // also validates `this`\n      push(entries, { key: k, value: v });\n    });\n    validateArgumentsLength(length, 1);\n    var key = toString(name);\n    var value = toString($value);\n    var index = 0;\n    var dindex = 0;\n    var found = false;\n    var entriesLength = entries.length;\n    var entry;\n    while (index < entriesLength) {\n      entry = entries[index++];\n      if (found || entry.key === key) {\n        found = true;\n        $delete(this, entry.key);\n      } else dindex++;\n    }\n    while (dindex < entriesLength) {\n      entry = entries[dindex++];\n      if (!(entry.key === key && entry.value === value)) append(this, entry.key, entry.value);\n    }\n  }, { enumerable: true, unsafe: true });\n}\n", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('common-part',{scopedSlots:_vm._u([{key:\"label\",fn:function(){return [_c('div',{staticClass:\"package-label\"},[_c('div',[_vm._v(_vm._s(_vm.$t('adyen-order-info')))]),(_vm.$gcbk('switch.switchRebate', false) && _vm.$store.state.formdata.isFixedEventOpen)?_c('div',[_c('fixed-coupon-switch')],1):_vm._e()])]},proxy:true}])},[_c('div',{staticClass:\"goods-name\",class:[_vm.$gameName],attrs:{\"id\":\"direct-package-name\"}},[_vm._v(\" \"+_vm._s(_vm.goodsName)+\" \")])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"coupon-switch-toggle\",on:{\"click\":function($event){return _vm.$root.$emit('toggleFixedCoupon', true)}}},[_c('i',{staticClass:\"toggle-icon\",class:{ active: _vm.isFixedRebateWork }}),_c('span',{staticClass:\"toggle-lang\"},[_vm._v(_vm._s(_vm.$t('gog-fixed-coupon-switch').replace('5 %', '5%')))])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<script>\nimport { mapState } from 'vuex'\nexport default {\n  name: 'fixed-coupon-switch',\n  computed: {\n    ...mapState('formdata', ['isFixedRebateWork'])\n  }\n}\n</script>\n\n<template>\n  <div class=\"coupon-switch-toggle\" @click=\"$root.$emit('toggleFixedCoupon', true)\">\n    <!-- <span class=\"toggle-lang\">{{ $t('gog-fixed-coupon-switch') }}</span> -->\n    <i class=\"toggle-icon\" :class=\"{ active: isFixedRebateWork }\"></i>\n    <span class=\"toggle-lang\">{{ $t('gog-fixed-coupon-switch').replace('5 %', '5%') }}</span>\n  </div>\n</template>\n\n<style scoped lang=\"scss\">\n@use \"~@/utils/utils.scss\" as utils;\n\n.coupon-switch-toggle{\n  display: flex;\n  align-items: center;\n  .toggle-icon{\n    display: inline-block;\n    margin: 0 10px;\n    flex-shrink: 0;\n    background-color: #9D9D9D;\n    width: 50px;\n    height: 30px;\n    border-radius: 15px;\n    position: relative;\n    transition: all .3s;\n    &:after{\n      content: '';\n      height: 25px;\n      width: 25px;\n      position: absolute;\n      left: 2px;\n      top: 50%;\n      transform: translateY(-50%);\n      background-color: white;\n      border-radius: 50%;\n      transition: all .3s;\n    }\n\n    &.active{\n      background-color: #34CA5A;\n      &:after{\n        content: '';\n        left: calc(100% - 25px - 4px);\n      }\n    }\n  }\n  .toggle-lang{\n    font-family: PingFangSC, PingFang SC;\n    font-weight: 600;\n    font-size: 24px;\n    // color: #FFFFFF;\n    color: #ff5e00;\n    line-height: 33px;\n  }\n}\n\n@include utils.setPcContent{\n  .coupon-switch-toggle{\n    // position: absolute;\n    // left: 440px;\n    // top: 50%;\n    // transform: translateY(-50%);\n    cursor: pointer;\n    .toggle-icon{\n      width: 40px;\n      height: 24px;\n      border-radius: 12px;\n      &:after{\n        width: 20px;\n        height: 20px;\n      }\n\n      &.active{\n        background-color: #34CA5A;\n        &:after{\n          content: '';\n          left: calc(100% - 20px - 2px);\n        }\n      }\n    }\n    .toggle-lang{\n      font-size: 18px;\n      line-height: 28px;\n    }\n  }\n}\n</style>\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./FixedCouponSwitch.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./FixedCouponSwitch.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./FixedCouponSwitch.vue?vue&type=template&id=2b400ddb&scoped=true\"\nimport script from \"./FixedCouponSwitch.vue?vue&type=script&lang=js\"\nexport * from \"./FixedCouponSwitch.vue?vue&type=script&lang=js\"\nimport style0 from \"./FixedCouponSwitch.vue?vue&type=style&index=0&id=2b400ddb&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2b400ddb\",\n  null\n  \n)\n\nexport default component.exports", "<script>\nimport './Preload'\nimport CommonPart from '../common/CommonPart.vue'\nimport FixedCouponSwitch from '../FixedCouponSwitch.vue'\nimport { getRedirectProductList } from '../../server'\n\nexport default {\n  components: { CommonPart, FixedCouponSwitch },\n  data () {\n    return {\n      goodsName: '-'\n    }\n  },\n  methods: {\n    loadDiamondList () {\n      const params = {}\n\n      if (this.$store.state.urlParams.tc) {\n        const tc = JSON.parse(this.$store.state.urlParams.tc)\n        params.product_id = tc.product_id\n\n        this.goodsName = tc.product_name\n      }\n      this.$loading.show()\n      getRedirectProductList(params)\n        .then(res => {\n          const { data, code } = res\n          if (code === 0) {\n            if (data.length && data.length !== 1) return this.$toast.err(this.$t('cb_page_title_err'))\n            this.$store.commit('formdata/setChosenDiamond', data[0])\n          }\n        })\n        .finally(() => this.$loading.hide())\n    },\n    loadOtherDiamondList () {\n      const params = {\n        game_order_id: this.$store.state.urlParams.oid || ''\n      }\n      if (this.$store.state.urlParams.tc) {\n        const tc = JSON.parse(this.$store.state.urlParams.tc)\n        params.product_id = tc.product_id\n        if (tc.product_name) {\n          this.goodsName = tc.product_name\n          this.$root.$emit('updateSdk2PackageName', tc.product_name)\n        }\n      }\n      if (!params.game_order_id) return this.$toast.err(this.$t('sdk2_error_order'))\n\n      this.$loading.show()\n      getRedirectProductList(params)\n        .then(res => {\n          const { data, code } = res\n          switch (code) {\n            case 0: {\n              this.$store.commit('formdata/setChosenDiamond', data)\n              if (data.sku_name && this.goodsName === '-') {\n                this.goodsName = data.sku_name\n                this.$store.commit('updateUrlParams', { product_name: data.sku_name })\n                this.$root.$emit('updateSdk2PackageName', this.goodsName)\n              }\n              if (this.goodsName === '-') this.setDefaultName(data) // sdk2 有些游戏没有礼包名称\n              break\n            }\n            case 106041: {\n              this.$toast.err(this.$t('cb_view_err_tips'))\n              break\n            }\n            default: {\n              this.$toast.err(this.$t('RU_refused'))\n            }\n          }\n        })\n        .finally(() => this.$loading.hide())\n    },\n    resetSdkStatus () {\n      localStorage.setItem('isWhatDiamondPop', 'true') // sdk不需要弹什么事钻石\n    },\n    loginFail () {\n      this.$nextTick(() => {\n        if (document.querySelector('.checkout-footer-wrapper')) document.querySelector('.checkout-footer-wrapper').style.display = 'none'\n        if (document.querySelector('.checkout-counter-sdk-b')) document.querySelector('.checkout-counter-sdk-b').style.display = 'none'\n      })\n    },\n    setDefaultName (data) {\n      const defaultName = this.$t('sdk2_default_package_name', { 1: data.no_tax_price, 0: data.currency_symbol })\n      this.goodsName = defaultName\n\n      this.$store.commit('updateUrlParams', { product_name: defaultName })\n      this.$root.$emit('updateSdk2PackageName', defaultName)\n    }\n  },\n  created () {\n    this.resetSdkStatus()\n    this.$root.$on('loginEnd', (status) => {\n      if (status === 1) this.$gameName === 'dc' ? this.loadDiamondList() : this.loadOtherDiamondList()\n      if (status === 0) this.loginFail()\n    })\n\n    console.log(this.$gcbk('switch.switchRebate', false))\n  }\n}\n</script>\n\n<template>\n  <!-- <common-part :labelFont=\"$t('adyen-order-info')\"> -->\n  <common-part>\n    <template #label>\n      <div class=\"package-label\">\n        <div>{{ $t('adyen-order-info') }}</div>\n        <div v-if=\"$gcbk('switch.switchRebate', false) && $store.state.formdata.isFixedEventOpen\">\n          <fixed-coupon-switch></fixed-coupon-switch>\n        </div>\n      </div>\n    </template>\n    <div class=\"goods-name\" id=\"direct-package-name\" :class=\"[$gameName]\">\n      {{ goodsName }}\n    </div>\n  </common-part>\n</template>\n\n<style scoped lang=\"scss\">\n@use \"~@/utils/utils.scss\" as utils;\n.package-label {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.goods-name{\n  width: 674px;\n  height: 80px;\n  background: #34353D;\n  border: 1px solid #000000;\n\n  font-family: PingFangSC, PingFang SC;\n  font-weight: 500;\n  font-size: 24px;\n  color: #FFFFFF;\n  line-height: 33px;\n  text-align: left;\n  font-style: normal;\n\n  @include utils.flexCenter;\n  justify-content: flex-start;\n  padding-left: 21px;\n}\n\n@include utils.setPcContent{\n  .goods-name{\n    width: auto;\n    height: 48px;\n    font-size: 18px;\n    padding-left: 10px;\n  }\n}\n</style>\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./DirectGiftPackage.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./DirectGiftPackage.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./DirectGiftPackage.vue?vue&type=template&id=cf7c3d58&scoped=true\"\nimport script from \"./DirectGiftPackage.vue?vue&type=script&lang=js\"\nexport * from \"./DirectGiftPackage.vue?vue&type=script&lang=js\"\nimport style0 from \"./DirectGiftPackage.vue?vue&type=style&index=0&id=cf7c3d58&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"cf7c3d58\",\n  null\n  \n)\n\nexport default component.exports", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./couponToggle.vue?vue&type=style&index=0&id=153b91ac&prod&scoped=true&lang=scss\"", "export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./OverSizeScale.vue?vue&type=style&index=0&id=62189d20&prod&scoped=true&lang=scss\"", "export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./FixedCouponSwitch.vue?vue&type=style&index=0&id=2b400ddb&prod&scoped=true&lang=scss\"", "const images = [\n    // 'https://kg-web-cdn.akamaized.net/prod/versionAdmin/source/web-pay-unique/dev/coupon-item.svg',\n    // 'https://kg-web-cdn.akamaized.net/prod/versionAdmin/source/web-pay-unique/dev/coupon-item-chosen.svg'\n    require('@/assets/common/coupon/coupon-item.png'),\n    require('@/assets/common/coupon/coupon-item-chosen.png')\n]\n\nconst loadImage = (src) => {\n    if (!src) return Promise.resolve()\n    return new Promise((resolve, reject) => {\n        const link = document.createElement('link')\n        link.as = 'image'\n        link.rel = 'preload'\n        link.href = src\n        document.head.appendChild(link)\n        \n        link.onload = resolve\n        link.onerror = reject\n        setTimeout(reject, 5000)\n    })\n}\n\nconst loadImages = async () => {\n    while (images.length) {\n        try {\n            await loadImage(images.shift())\n        } catch (error) {\n            console.error(error)\n        }\n    }\n}\n\nPromise.all(Array.from({ length: 1 }, loadImages))\n", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./CommonPart.vue?vue&type=style&index=0&id=c17bc992&prod&scoped=true&lang=scss\"", "'use strict';\nvar $TypeError = TypeError;\n\nmodule.exports = function (passed, required) {\n  if (passed < required) throw new $TypeError('Not enough arguments');\n  return passed;\n};\n", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('span',{ref:\"outer\",staticClass:\"outer\"},[_c('span',{ref:\"inner\",staticClass:\"inner\",style:(`transform: scale(${_vm.scale})`)},[_vm._t(\"default\")],2)])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n<span class=\"outer\" ref=\"outer\">\n  <span class=\"inner\" :style=\"`transform: scale(${scale})`\" ref=\"inner\">\n    <slot></slot>\n  </span>\n</span>\n</template>\n\n<script>\nexport default {\n  name: 'OverSizeScale',\n  data () {\n    return {\n      scale: 1\n    }\n  },\n  methods: {\n    calc () {\n      const outer = this.$refs.outer.offsetWidth\n      const inner = this.$refs.inner.offsetWidth\n      if (inner > outer) this.scale = outer / inner\n    }\n  },\n  mounted () {\n    this.calc()\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n.outer{\n  width: 100%;\n  overflow: hidden;\n  display: inline-block;\n\n  .inner{\n    transform-origin: left center;\n    display: inline-block;\n    white-space: nowrap;\n    line-height: 1;\n  }\n}\n</style>\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./OverSizeScale.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./OverSizeScale.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./OverSizeScale.vue?vue&type=template&id=62189d20&scoped=true\"\nimport script from \"./OverSizeScale.vue?vue&type=script&lang=js\"\nexport * from \"./OverSizeScale.vue?vue&type=script&lang=js\"\nimport style0 from \"./OverSizeScale.vue?vue&type=style&index=0&id=62189d20&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"62189d20\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{class:['checkout-footer-wrapper', _vm.$gameName, { sdk: _vm.IS_CHECKOUT_SDK }],attrs:{\"id\":\"checkout-footer-wrapper\"},on:{\"click\":function($event){$event.stopPropagation();}}},[_c('transition',{attrs:{\"name\":\"fade\"}},[(_vm.expandMode)?_c('div',{staticClass:\"expand-part\"},[_c('div',{staticClass:\"pop-title\"},[_vm._v(_vm._s(_vm.$t('tax-details')))]),_c('div',{staticClass:\"divider\"}),_c('div',{staticClass:\"value-wrapper\"},[_c('div',{staticClass:\"origin-price\"},[_c('span',[_vm._v(_vm._s(_vm.$t('tax-price'))),(_vm.IS_CHECKOUT_SDK)?[_vm._v(\"：\")]:_vm._e()],2),_c('span',[_vm._v(_vm._s(_vm.chosenDiamond.nowPrice || _vm.chosenDiamond.level_currency_price)+_vm._s(_vm.currencyUnit))])]),(_vm.FinalPriceState.feType && _vm.hideDiscountRow)?_c('div',{staticClass:\"discount\"},[_c('span',[_vm._v(_vm._s(_vm.$t('tax-discount'))+\" \"),(_vm.IS_CHECKOUT_SDK)?[_vm._v(\"：\")]:_vm._e()],2),_c('span',[[(['first_pay', 'discount_coupon'].includes(_vm.chosenCoupon.feType))?[_vm._v(\" - \"+_vm._s(_vm.chosenCoupon.discount_amount)+\" \"+_vm._s(_vm.currencyUnit)+\" (\"+_vm._s(_vm.chosenCoupon.rateWidthOutPercent)+\"% OFF) \")]:_vm._e(),(_vm.chosenCoupon.feType==='cash_coupon')?[_vm._v(\" - \"+_vm._s(_vm.chosenCoupon.discount_amount)+\" \"+_vm._s(_vm.currencyUnit)+\" \")]:_vm._e(),(_vm.FinalPriceState.feType==='fixed_discount_coupon')?[_vm._v(\" - \"+_vm._s(_vm.FinalPriceState.offCountAmount)+\" \"+_vm._s(_vm.currencyUnit)+\" \")]:_vm._e()]],2)]):_vm._e(),(_vm.taxCost)?_c('div',{staticClass:\"tax\"},[_c('span',[_vm._v(_vm._s(_vm.$t('tax-txt'))),(_vm.IS_CHECKOUT_SDK)?[_vm._v(\"：\")]:_vm._e()],2),_c('span',[_vm._v(_vm._s(_vm.taxCost)+_vm._s(_vm.currencyUnit))])]):_vm._e(),(_vm.extraCost)?_c('div',{staticClass:\"tax\"},[_c('span',[_vm._v(_vm._s(_vm.$t('extra-txt'))),(_vm.IS_CHECKOUT_SDK)?[_vm._v(\"：\")]:_vm._e()],2),_c('span',[_vm._v(_vm._s(_vm.extraCost)+_vm._s(_vm.currencyUnit))])]):_vm._e()]),_c('div',{staticClass:\"divider\"})]):_vm._e()]),_c('div',{staticClass:\"common-part\"},[_c('div',{staticClass:\"total-price\"},[_c('div',{staticClass:\"row-1\"},[_c('span',{staticClass:\"now-price\",class:{'is-ar-zone': _vm.isArZone}},[_vm._v(_vm._s(_vm.FinalPriceState.finalNowPrice))]),(_vm.showTaxBtn)?_c('span',{staticClass:\"rate\",class:{active: _vm.expandMode},on:{\"click\":function($event){_vm.expandMode = !_vm.expandMode}}},[_vm._v(\"+ \"+_vm._s(_vm.$t('tax-txt'))),_c('i')]):_vm._e()]),_c('div',{staticClass:\"row-2\"},[(_vm.FinalPriceState.finalOriginPrice)?_c('span',{class:['origin-price', {'is-ar-zone': _vm.isArZone}]},[_vm._v(_vm._s(_vm.FinalPriceState.finalOriginPrice))]):_vm._e(),(_vm.FinalPriceState.offCountTips)?_c('span',{staticClass:\"off-count-tips\",class:{ 'off-count-left': !_vm.hideDiscountRow },domProps:{\"innerHTML\":_vm._s(_vm.FinalPriceState.offCountTips)}}):_vm._e()])]),_c('div',{staticClass:\"btn click-btn\",class:[{disable: _vm.requestLoading || _vm.$store.getters['riskPolicy/forbiddenAccess']}, _vm.$i18n.locale],on:{\"click\":function($event){return _vm.$emit('purchaseGoods')}}},[_c('span',[_vm._v(_vm._s(_vm.$t('shop_now')))]),(_vm.vip.isNewUser)?_c('i'):_vm._e()])])],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <div :class=\"['checkout-footer-wrapper', $gameName, { sdk: IS_CHECKOUT_SDK }]\" id=\"checkout-footer-wrapper\" @click.stop>\n    <transition name=\"fade\">\n      <div class=\"expand-part\" v-if=\"expandMode\">\n        <div class=\"pop-title\">{{ $t('tax-details') }}</div>\n        <div class=\"divider\"></div>\n        <div class=\"value-wrapper\">\n          <div class=\"origin-price\">\n            <span>{{ $t('tax-price') }}<template v-if=\"IS_CHECKOUT_SDK\">：</template></span>\n            <span>{{ chosenDiamond.nowPrice || chosenDiamond.level_currency_price }}{{ currencyUnit }}</span>\n          </div>\n          <div v-if=\"FinalPriceState.feType && hideDiscountRow\" class=\"discount\">\n            <span>{{ $t('tax-discount')}} <template v-if=\"IS_CHECKOUT_SDK\">：</template></span>\n            <span>\n            <template>\n              <template v-if=\"['first_pay', 'discount_coupon'].includes(chosenCoupon.feType)\">\n                 - {{chosenCoupon.discount_amount }} {{ currencyUnit }}\n              ({{ chosenCoupon.rateWidthOutPercent }}% OFF)\n              </template>\n              <template v-if=\"chosenCoupon.feType==='cash_coupon'\">\n                 - {{chosenCoupon.discount_amount }} {{ currencyUnit }}\n              </template>\n              <template v-if=\"FinalPriceState.feType==='fixed_discount_coupon'\">\n                 - {{FinalPriceState.offCountAmount }} {{ currencyUnit }}\n              </template>\n            </template>\n          </span>\n          </div>\n          <div class=\"tax\" v-if=\"taxCost\">\n            <span>{{ $t('tax-txt') }}<template v-if=\"IS_CHECKOUT_SDK\">：</template></span>\n            <span>{{ taxCost }}{{ currencyUnit }}</span>\n          </div>\n          <div class=\"tax\" v-if=\"extraCost\">\n            <span>{{ $t('extra-txt') }}<template v-if=\"IS_CHECKOUT_SDK\">：</template></span>\n            <span>{{ extraCost }}{{ currencyUnit }}</span>\n          </div>\n        </div>\n        <div class=\"divider\"></div>\n      </div>\n    </transition>\n    <div class=\"common-part\">\n      <div class=\"total-price\">\n        <div class=\"row-1\">\n          <span class=\"now-price\" :class=\"{'is-ar-zone': isArZone}\">{{ FinalPriceState.finalNowPrice }}</span>\n          <span v-if=\"showTaxBtn\" @click=\"expandMode = !expandMode\" class=\"rate\" :class=\"{active: expandMode}\">+ {{ $t('tax-txt') }}<i></i></span>\n        </div>\n        <div class=\"row-2\">\n          <span v-if=\"FinalPriceState.finalOriginPrice\" :class=\"['origin-price', {'is-ar-zone': isArZone}]\">{{ FinalPriceState.finalOriginPrice }}</span>\n          <span v-html=\"FinalPriceState.offCountTips\" v-if=\"FinalPriceState.offCountTips\" class=\"off-count-tips\" :class=\"{ 'off-count-left': !hideDiscountRow }\"></span>\n        </div>\n      </div>\n      <div class=\"btn click-btn\" :class=\"[{disable: requestLoading || $store.getters['riskPolicy/forbiddenAccess']}, $i18n.locale]\"  @click=\"$emit('purchaseGoods')\">\n        <span>{{ $t('shop_now') }}</span>\n        <i v-if=\"vip.isNewUser\"></i>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport {mapGetters, mapState} from 'vuex'\n\nexport default {\n  name: 'CheckoutFooterTax',\n  props: ['requestLoading'],\n  data () {\n    return {\n      expandMode: false\n    }\n  },\n  computed: {\n    ...mapState(['urlParams', 'isArZone', 'currencyUnit', 'IS_CHECKOUT_SDK']),\n    ...mapState('formdata', ['chosenChannel', 'chosenDiamond', 'chosenCoupon', 'vip']),\n    ...mapState('gameinfo', ['defaultDiscount', 'gameCode']),\n    ...mapState('userinfo', ['isLogin']),\n    ...mapGetters('formdata', ['FinalPriceState', 'getRebateCoin', 'getSDKRebateCoin']),\n    taxCost () {\n      return this.chosenCoupon.taxation || this.FinalPriceState.taxation || this.chosenDiamond.taxation\n    },\n    extraCost () {\n      return this.chosenCoupon.extra_fee_amount || this.FinalPriceState.extra_fee_amount || this.chosenDiamond.extra_fee_amount\n    },\n    showTaxBtn () {\n      return this.taxCost || this.extraCost\n    },\n    hideDiscountRow(){\n      if (!this.FinalPriceState.feType) return false\n      return !this.FinalPriceState.feType.includes('rebate')\n    }\n  },\n  watch: {\n    showTaxBtn (newValue) {\n      if (!newValue) this.expandMode = false\n    }\n  },\n  mounted () {\n    this.$root.$on('BodyClick', () => {\n      this.expandMode = false\n    })\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n@use \"~@/utils/utils.scss\" as utils;\n.fade-enter-active, .fade-leave-active{\n  transition: all .3s;\n}\n.fade-enter{\n  transform: translateY(-80%)!important;\n  opacity: 0;\n}\n.fade-leave-to{\n  transform: translateY(-80%)!important;\n  opacity: 0;\n}\n\n.is-ar-zone {\n  display: inline-block;\n}\n.checkout-footer-wrapper{\n  background: #192136;\n  position: relative;\n\n  .expand-part{\n    padding: 0 42px 0;\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    transform: translateY(calc(-100% + 2px));\n    border-top-right-radius: 20px;\n    border-top-left-radius: 20px;\n    background: #192136;\n    z-index: 10;\n\n    .pop-title{\n      font-size: 28px;\n      font-family: PingFangSC-Semibold, PingFang SC;\n      font-weight: 600;\n      color: #FFFFFF;\n      line-height: 40px;\n      padding: 20px 0;\n    }\n\n    .value-wrapper{\n      padding-top: 5px;\n      padding-bottom: 20px;\n\n      div{\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n        margin-top: 15px;\n        span:first-of-type{\n          font-size: 20px;\n          font-family: PingFangSC-Semibold, PingFang SC;\n          font-weight: 600;\n          color: #FFFFFF;\n          line-height: 28px;\n        }\n\n        span:last-of-type{\n          font-size: 24px;\n          font-family: PingFangSC-Medium, PingFang SC;\n          font-weight: 500;\n          color: #FEB522;\n          line-height: 33px;\n        }\n      }\n    }\n\n    .divider{\n      height: 1px;\n      opacity: 0.3;\n      border: 1px solid #979797;\n    }\n  }\n\n  .common-part{\n    display: flex;\n    align-items: center;\n    height: 120px;\n    flex-shrink: 0;\n    padding: 0 42px 0;\n    position: relative;\n    z-index: 2;\n    background: #192136;\n\n    .total-price{\n      display: flex;\n      white-space: nowrap;\n      align-items: flex-start;\n      justify-content: center;\n      flex-direction: column;\n\n      .row-1{\n        display: flex;\n        align-items: center;\n        .now-price{\n          font-size: 36px;\n          font-family: PingFangSC-Semibold, PingFang SC;\n          font-weight: 600;\n          color: #FEB522;\n          line-height: 50px;\n        }\n        .rate{\n          font-size: 18px;\n          font-family: PingFangSC-Medium, PingFang SC;\n          font-weight: 500;\n          color: #FEB522;\n          line-height: 25px;\n          margin-left: 20px;\n          display: flex;\n          align-items: center;\n\n          i{\n            @include utils.bgCenter('common/icon/tax-arrow.png', 16px, 16px);\n            display: inline-block;\n            margin-left: 4px;\n            transition: all .3s;\n          }\n\n          &.active i{\n            transform: rotate(180deg);\n          }\n        }\n      }\n\n      .row-2{\n        display: flex;\n        align-items: center;\n\n        .origin-price {\n          text-decoration: line-through;\n          font-weight: 400;\n          color: #BCBCBC;\n          font-size: 18px;\n          font-family: PingFangSC-Regular, PingFang SC;\n          line-height: 25px;\n        }\n\n        .off-count-tips{\n          height: 20px;\n          background: #FEB522;\n          border-radius: 4px;\n          font-size: 14px;\n          font-family: PingFangSC-Semibold, PingFang SC;\n          font-weight: 600;\n          line-height: 20px;\n          padding: 0 8px;\n          margin-left: 10px;\n          color: #633B00;\n\n          ::v-deep{\n            .diamond-icon {\n              @include utils.bgCenter('koa/diamond/diamond.png', 13px, 11px);\n              display: inline-block;\n              margin-left: 2px;\n              position: relative;\n            }\n          }\n        }\n\n        .off-count-left {\n          margin-left: 2px;\n        }\n      }\n    }\n\n    .btn{\n      width: 227px;\n      height: 73px;\n      font-size: 30px;\n      font-family: PingFangSC-Medium, PingFang SC;\n      font-weight: 500;\n      text-align: center;\n      line-height: 73px;\n      margin-left: auto;\n\n      background: linear-gradient(180deg, #F6E190 0%, #CBA455 100%);\n      border-radius: 0;\n      color: #633B00;\n      position: relative;\n\n      &.de, &.id, &.vi, &.my{\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        font-size: 25px;\n        line-height: 30px;\n        padding: 0 3px;\n      }\n\n      &.disable {\n        opacity: .4;\n        cursor: not-allowed;\n      }\n\n      i{\n        position: absolute;\n        right: 0;\n        top: 0;\n        @include utils.bgCenter('koa/pay-btn-gift.png', 44px, 46px);\n        display: inline-block;\n        transform: translate(50%, -50%);\n      }\n    }\n  }\n}\n\n.checkout-footer-wrapper.dc{\n  background-color: rgb(50, 50, 72);\n\n  .common-part{\n    background: rgb(50, 50, 72);\n\n    .row-1{\n      .now-price{\n        color: #FFE14D;\n        @extend .dc-stroke;\n      }\n    }\n\n    .row-2{\n      .off-count-tips{\n        background: #FFE14D;\n        color: #393A3E;\n        ::v-deep{\n          .diamond-icon {\n            @include utils.bgCenterForDC('diamond/diamond-icon.png', 14px, 14px);\n            margin-left: 0;\n            display: inline-block;\n            top: 1px;\n          }\n        }\n      }\n    }\n\n    .btn{\n      @include utils.bgCenterForDC('checkout/checkout-foooter-btn.png', 227px, 73px);\n      color: #393A3E;\n      i{\n        display: none;\n      }\n    }\n  }\n  .expand-part{\n    background: rgb(50, 50, 72);\n  }\n}\n.checkout-footer-wrapper.ssv{\n  background: #404040;\n  .expand-part{\n    background: #404040;\n\n    .value-wrapper{\n      div{\n        span:last-of-type{\n          color: #FF5E0F;\n        }\n      }\n    }\n  }\n  .common-part{\n    background: #404040;\n\n    .total-price{\n      .row-1{\n        .now-price{\n          color: #FF5E0F;\n        }\n        .rate{\n          color: #FF5E0F;\n          i{\n            @include utils.bgCenterForSSV('checkout/tax-arrow.png', 16px, 16px);\n          }\n        }\n      }\n      .row-2{\n        .off-count-tips{\n          background: #FF5E0F;\n          border-radius: 4px;\n          font-weight: 600;\n          color: #FFFFFF;\n        }\n      }\n    }\n\n    .btn{\n      background: #FF5E0F;\n      color: #FFFFFF;\n      border-radius: 8px;\n      i{\n        display: none;\n      }\n    }\n  }\n}\n.checkout-footer-wrapper.sdk{\n  @include utils.setPcContent{\n    display: none;\n  }\n}\n.checkout-footer-wrapper.ssv2{\n  background: #404040;\n  position: relative;\n\n  .expand-part{\n    padding: 0 42px 0;\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    transform: translateY(calc(-100% + 2px));\n    border-top-right-radius: 20px;\n    border-top-left-radius: 20px;\n    background: #404040;\n    z-index: 10;\n\n    .pop-title{\n      font-size: 28px;\n      font-family: PingFangSC-Semibold, PingFang SC;\n      font-weight: 600;\n      color: #FFFFFF;\n      line-height: 40px;\n      padding: 20px 0;\n    }\n\n    .value-wrapper{\n      padding-top: 5px;\n      padding-bottom: 20px;\n\n      div{\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n        margin-top: 15px;\n        span:first-of-type{\n          font-size: 20px;\n          font-family: PingFangSC-Semibold, PingFang SC;\n          font-weight: 600;\n          color: #FFFFFF;\n          line-height: 28px;\n        }\n\n        span:last-of-type{\n          font-size: 24px;\n          font-family: PingFangSC-Medium, PingFang SC;\n          font-weight: 500;\n          color: #FF5E0F;\n          line-height: 33px;\n        }\n      }\n    }\n\n    .divider{\n      height: 1px;\n      opacity: 0.3;\n      border: 1px solid #979797;\n    }\n  }\n\n  .common-part{\n    display: flex;\n    align-items: center;\n    height: 120px;\n    flex-shrink: 0;\n    padding: 0 42px 0;\n    position: relative;\n    z-index: 2;\n    background: #404040;\n\n    .total-price{\n      display: flex;\n      white-space: nowrap;\n      align-items: flex-start;\n      justify-content: center;\n      flex-direction: column;\n\n      .row-1{\n        display: flex;\n        align-items: center;\n        .now-price{\n          font-size: 36px;\n          font-family: PingFangSC-Semibold, PingFang SC;\n          font-weight: 600;\n          color: #FF5E0F;\n          line-height: 50px;\n        }\n        .rate{\n          font-size: 18px;\n          font-family: PingFangSC-Medium, PingFang SC;\n          font-weight: 500;\n          color: #FF5E0F;\n          line-height: 25px;\n          margin-left: 20px;\n          display: flex;\n          align-items: center;\n\n          i{\n            @include utils.bgCenterForSS('checkout/tax-arrow.png', 16px, 16px);\n            display: inline-block;\n            margin-left: 4px;\n            transition: all .3s;\n          }\n\n          &.active i{\n            transform: rotate(180deg);\n          }\n        }\n      }\n\n      .row-2{\n        display: flex;\n        align-items: center;\n\n        .origin-price {\n          text-decoration: line-through;\n          font-weight: 400;\n          color: #BCBCBC;\n          font-size: 18px;\n          font-family: PingFangSC-Regular, PingFang SC;\n          line-height: 25px;\n        }\n\n        .off-count-tips{\n          height: 20px;\n          background: #FF5E0F;\n          border-radius: 4px;\n          font-size: 14px;\n          font-family: PingFangSC-Semibold, PingFang SC;\n          font-weight: 600;\n          color: #FFFFFF;\n          line-height: 20px;\n          padding: 0 8px;\n          margin-left: 10px;\n        }\n      }\n    }\n\n    .btn{\n      width: 227px;\n      height: 73px;\n      background: #FF5E0F;\n      border-radius: 8px;\n      font-size: 30px;\n      font-family: PingFangSC-Medium, PingFang SC;\n      font-weight: 500;\n      color: #FFFFFF;\n      text-align: center;\n      line-height: 73px;\n      margin-left: auto;\n\n      &.de{\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        font-size: 25px;\n        line-height: 30px;\n        padding: 0 3px;\n      }\n\n      &.disable {\n        opacity: .4;\n        cursor: not-allowed;\n      }\n\n      i{\n        display: none;\n      }\n    }\n  }\n}\n</style>\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./CheckoutFooterTax.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./CheckoutFooterTax.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./CheckoutFooterTax.vue?vue&type=template&id=4ceefa9e&scoped=true\"\nimport script from \"./CheckoutFooterTax.vue?vue&type=script&lang=js\"\nexport * from \"./CheckoutFooterTax.vue?vue&type=script&lang=js\"\nimport style0 from \"./CheckoutFooterTax.vue?vue&type=style&index=0&id=4ceefa9e&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4ceefa9e\",\n  null\n  \n)\n\nexport default component.exports", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./CouponChoose.vue?vue&type=style&index=0&id=066665a8&prod&scoped=true&lang=scss\"", "module.exports = \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAABDwAAACGBAMAAADDWofQAAAAD1BMVEUAAAD/66//67L/6bH/6rHH7gOuAAAABHRSTlMAQL+ATezaDQAAAq1JREFUeNrs3FFu01AURVEXGICDGICFOoBUMIAmefMfEx8tGJA4QpXqd2+01gTys+34WTlZ4D+cjvTHB1PPX218HYdafjMo6HZefvk8Drbun/0wKOlpefVhvKucx8dBTefX6/dxHG2TRwMvF/GXcbjnPY9Pg6Ius778b3se3wdVrfuTx7EcXDrYZl2+2/5YTFnXWZfvzXdLB+usk8P2840LhW3TTg5np9r6nufd3b+dTu4dxV18+ZOeTR8H/MPVewfkwdvIA3kgD+RBIA8CeRDIg0AeBPIgkAeBPAjkQSAPAnkQ3FEet/XBL9Wqm5bHdV0WP1Wrbloel5d5L6VNy+NsI9fAtDxW//3SwLQ8DLA7mHf3kEcD8/IwomxgWh6bPBqYd7D19w0NLGOWs5tHfRNfqj+dvDStztkBeSAP5EEgDwJ5EMiDQB4E8iCQB4E8CORBIA8CeRDIg0AeBPIgkAfBHeVhod+AhT4V87DQ78BCn4p5WOh3YKFPxTws9Duw0KdiHhb6HVjoUzEPC/0OLPQpmQcNyAN5IA/kQSAPAnkQyINAHgTyIJAHgTwI5EEgDwJ5EMiDQB4E8iC4ozws9Buw0KdiHhb6HVjoUzEPC/0OLPSpmIeFfgcW+lTMw0K/Awt9KuZhod+BhT4l86ABeSAP5IE8CORBIA8CeRDI40c7d3CDMAwEUXQkKCB0AKnAEg2EdfqvCWQf4MIecsjO4b8WPLIsa3eQIB5IEA8kiAcSxAMJ4oEE8cCZWFTDf0E8QDxwSC9bc42bdCGb3l667iVi7rkwqm5tq5oWbxKLLvaatFfo+mDTxV3VATWJ68Ner+rY0ERBkLVWdEBdAwVB1kJFFT2bBgqCrN2rnoeNePjrZc/DRQP9UcZi0VDweyni4S6avp7rqfRjhaHHvDveAy+ZKkukVxYAAAAASUVORK5CYII=\""], "sourceRoot": ""}