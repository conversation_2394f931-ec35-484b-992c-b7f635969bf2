{"version": 3, "sources": ["webpack:///./node_modules/vue-awesome-swiper/dist/vue-awesome-swiper.js", "webpack:///./node_modules/swiper/js/swiper.esm.bundle.js"], "names": ["g", "f", "exports", "this", "SwiperClass", "<PERSON><PERSON>", "CoreNames", "Object", "prototype", "hasOwnProperty", "call", "ComponentEvents", "ComponentPropNames", "DEFAULT_CLASSES", "freeze", "containerClass", "wrapperClass", "slideClass", "SWIPER_EVENTS", "__spreadA<PERSON>ys", "s", "i", "il", "arguments", "length", "r", "Array", "k", "a", "j", "jl", "SlotNames", "kebabcase", "string", "replace", "toLowerCase", "handleClickSlideEvent", "swiper", "event", "emit", "_a", "_b", "_c", "eventPath", "<PERSON><PERSON><PERSON>", "path", "target", "slides_1", "from", "slides", "paths", "includes", "some", "item", "clickedIndex", "reallyIndex", "Number", "clickedSlide", "dataset", "swiperSlideIndex", "reallyIndexValue", "isInteger", "ClickSlide", "bindSwiperEvents", "for<PERSON>ach", "eventName", "on", "arguments$1", "args", "_i", "apply", "kebabcaseName", "INSTANCE_NAME_KEY", "getDirective", "globalOptions", "getStandardisedOptionByAttrs", "vnode", "key", "_d", "value", "data", "attrs", "undefined", "getSwiperInstanceName", "element", "binding", "arg", "id", "SwiperInstance", "getSwiperInstance", "instanceName", "context", "getSwipeOptions", "getBooleanValueByInput", "input", "getEventEmiter", "handlers", "componentOptions", "listeners", "name", "handle", "fns", "bind", "className", "indexOf", "addEventListener", "emitEvent", "inserted", "swiperOptions", "vueContext", "destroyed", "Ready", "componentUpdated", "_e", "_f", "_g", "_h", "_j", "_k", "_l", "_m", "autoUpdate", "AutoUpdate", "isLoop", "loop", "loop<PERSON><PERSON><PERSON>", "update", "navigation", "pagination", "render", "loopCreate", "unbind", "autoDestroy", "AutoDestroy", "initialized", "destroy", "DeleteInstanceOnDestroy", "CleanupStylesOnDestroy", "getSwiperComponent", "extend", "SwiperComponent", "props", "defaultOptions", "type", "required", "default", "options", "Boolean", "computed", "swiperInstance", "cache", "set", "get", "methods", "handleSwiperClick", "$emit", "autoReLoopSwiper", "updateSwiper", "destroySwiper", "initSwiper", "$el", "mounted", "activated", "updated", "<PERSON><PERSON><PERSON><PERSON>", "$nextTick", "createElement", "staticClass", "click", "$slots", "ParallaxBg", "class", "Pagination", "PrevButton", "NextButton", "Sc<PERSON><PERSON>", "SwiperSlideComponent", "$parent", "parent", "getInstaller", "install", "installed", "component", "directive", "SwiperDirective", "exporter", "version", "VueAwesomeSwiper", "Swiper", "SwiperSlide", "defineProperty", "Methods", "addClass", "removeClass", "hasClass", "toggleClass", "attr", "removeAttr", "transform", "transition", "off", "trigger", "transitionEnd", "outerWidth", "outerHeight", "offset", "css", "each", "html", "text", "is", "index", "eq", "append", "prepend", "next", "nextAll", "prev", "prevAll", "parents", "closest", "find", "children", "filter", "remove", "add", "styles", "keys", "methodName", "fn", "Utils", "obj", "object", "e", "callback", "delay", "setTimeout", "Date", "now", "el", "axis", "matrix", "curTransform", "transformMatrix", "curStyle", "getComputedStyle", "WebKitCSSMatrix", "webkitTransform", "split", "map", "join", "MozTransform", "OTransform", "MsTransform", "msTransform", "getPropertyValue", "toString", "m41", "parseFloat", "m42", "url", "query", "params", "param", "urlToParse", "location", "href", "paramsPart", "decodeURIComponent", "o", "constructor", "to", "nextSource", "keysArray", "nextIndex", "len", "<PERSON><PERSON><PERSON>", "desc", "getOwnPropertyDescriptor", "enumerable", "isObject", "Support", "touch", "DocumentTouch", "pointerEvents", "PointerEvent", "navigator", "maxTouchPoints", "observer", "passiveListener", "supportsPassive", "opts", "gestures", "self", "eventsListeners", "events", "handler", "priority", "method", "once<PERSON><PERSON><PERSON>", "f7proxy", "<PERSON><PERSON><PERSON><PERSON>", "splice", "isArray", "slice", "eventsArray", "push", "instanceParams", "instance", "modules", "moduleName", "module", "modulesParams", "moduleParams", "modulePropName", "moduleProp", "moduleEventName", "create", "components", "Class", "use", "proto", "static", "m", "installModule", "updateSize", "width", "height", "clientWidth", "clientHeight", "isHorizontal", "isVertical", "parseInt", "size", "updateSlides", "$wrapperEl", "swiperSize", "rtlTranslate", "rtl", "wrongRTL", "isVirtual", "virtual", "enabled", "previousSlidesLength", "<PERSON><PERSON><PERSON><PERSON>", "snapGrid", "slidesGrid", "slidesSizesGrid", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "slideIndex", "cssMode", "offsetBefore", "slidesOffsetBefore", "offsetAfter", "slidesOffsetAfter", "previousSnapGridLength", "previousSlidesGridLength", "slidesNumberEvenToRows", "slideSize", "spaceBetween", "slidePosition", "prevSlideSize", "virtualSize", "marginLeft", "marginTop", "marginRight", "marginBottom", "slidesPerColumn", "Math", "floor", "ceil", "<PERSON><PERSON><PERSON><PERSON>iew", "slidesPerColumnFill", "max", "slidesPerRow", "numFullColumns", "slide", "newSlideOrderIndex", "column", "row", "slidesPerGroup", "groupIndex", "slideIndexInGroup", "columnsInGroup", "min", "order", "slideStyles", "currentTransform", "style", "currentWebKitTransform", "roundLengths", "paddingLeft", "paddingRight", "boxSizing", "paddingTop", "paddingBottom", "swiperSlideSize", "centeredSlides", "abs", "slidesPerGroupSkip", "newSlidesGrid", "effect", "setWrapperSize", "slidesGridItem", "centeredSlidesBounds", "allSlidesSize", "slideSizeValue", "maxSnap", "snap", "centerInsufficientSlides", "allSlidesOffset", "snapIndex", "watchOverflow", "checkOverflow", "watchSlidesProgress", "watchSlidesVisibility", "updateSlidesOffset", "updateAutoHeight", "speed", "activeSlides", "newHeight", "setTransition", "visibleSlides", "activeIndex", "offsetHeight", "swiperSlideOffset", "offsetLeft", "offsetTop", "updateSlidesProgress", "translate", "offsetCenter", "slideVisibleClass", "visibleSlidesIndexes", "slideProgress", "minTranslate", "autoHeight", "slideBefore", "slideAfter", "isVisible", "progress", "updateProgress", "multiplier", "translatesDiff", "maxTranslate", "isBeginning", "isEnd", "wasBeginning", "wasEnd", "updateSlidesClasses", "realIndex", "activeSlide", "slideActiveClass", "slideNextClass", "slidePrevClass", "slideDuplicateActiveClass", "slideDuplicateNextClass", "slideDuplicatePrevClass", "slideDuplicateClass", "nextSlide", "prevSlide", "updateActiveIndex", "newActiveIndex", "previousIndex", "previousRealIndex", "previousSnapIndex", "normalizeSlideIndex", "skip", "runCallbacksOnInit", "updateClickedSlide", "slideFound", "slideToClickedSlide", "getTranslate", "virtualTranslate", "currentTranslate", "setTranslate", "byController", "wrapperEl", "x", "y", "z", "newProgress", "previousTranslate", "translateTo", "runCallbacks", "translateBounds", "internal", "animating", "preventInteractionOnTransition", "newTranslate", "isH", "scrollTo", "behavior", "onTranslateToWrapperTransitionEnd", "removeEventListener", "duration", "transitionStart", "direction", "dir", "slideTo", "initialSlide", "allowSlideNext", "allowSlidePrev", "t", "scrollWidth", "offsetWidth", "onSlideToWrapperTransitionEnd", "slideToLoop", "newIndex", "loopedSlides", "slideNext", "increment", "loopFix", "_clientLeft", "clientLeft", "slidePrev", "normalize", "val", "normalizedTranslate", "normalizedSnapGrid", "prevIndex", "prevSnap", "slideReset", "slideToClosest", "threshold", "currentSnap", "nextSnap", "slidesPerViewDynamic", "slideToIndex", "nextTick", "loopFillGroupWithBlank", "blankSlidesNum", "blankNode", "slideBlankClass", "loopAdditionalSlides", "prependSlides", "appendSlides", "cloneNode", "snapTranslate", "diff", "slideChanged", "setGrabCursor", "moving", "simulate<PERSON>ouch", "isLocked", "cursor", "unsetGrabCursor", "grabCursor", "appendSlide", "prependSlide", "addSlide", "activeIndexBuffer", "baseLength", "slidesBuffer", "currentSlide", "unshift", "removeSlide", "slidesIndexes", "indexToRemove", "removeAllSlides", "manipulation", "<PERSON><PERSON>", "platform", "ua", "userAgent", "device", "ios", "android", "androidChrome", "desktop", "iphone", "ipod", "ipad", "edge", "ie", "firefox", "macos", "windows", "<PERSON><PERSON>", "phonegap", "electron", "screenWidth", "screen", "screenHeight", "match", "os", "osVersion", "webView", "standalone", "matchMedia", "matches", "webview", "pixelRatio", "devicePixelRatio", "onTouchStart", "touchEventsData", "touches", "originalEvent", "$targetEl", "touchEventsTarget", "isTouchEvent", "which", "button", "isTouched", "isMoved", "noSwiping", "noSwipingSelector", "noSwipingClass", "allowClick", "swi<PERSON><PERSON><PERSON><PERSON>", "currentX", "targetTouches", "pageX", "currentY", "pageY", "startX", "startY", "edgeSwipeDetection", "iOSEdgeSwipeDetection", "edgeSwipeThreshold", "iOSEdgeSwipeThreshold", "allowTouchCallbacks", "isScrolling", "startMoving", "touchStartTime", "swipeDirection", "allowThresholdMove", "preventDefault", "formElements", "activeElement", "blur", "shouldPreventDefault", "allowTouchMove", "touchStartPreventDefault", "touchStartForcePreventDefault", "onTouchMove", "targetTouch", "changedTouches", "preventedByNestedSwiper", "touchReleaseOnEdges", "diffX", "diffY", "sqrt", "touchAngle", "atan2", "PI", "cancelable", "touchMoveStopPropagation", "nested", "stopPropagation", "startTranslate", "allowMomentumBounce", "touchRatio", "disableParentSwiper", "resistanceRatio", "resistance", "follow<PERSON><PERSON>", "freeMode", "velocities", "position", "time", "onTouchEnd", "touchEndTime", "timeDiff", "lastClickTime", "currentPos", "freeModeMomentum", "lastMoveEvent", "pop", "velocityEvent", "distance", "velocity", "freeModeMinimumVelocity", "freeModeMomentumVelocityRatio", "momentumDuration", "freeModeMomentumRatio", "momentumDistance", "newPosition", "afterBouncePosition", "doBounce", "bounceAmount", "freeModeMomentumBounceRatio", "needsLoopFix", "freeModeMomentumBounce", "freeModeSticky", "once", "moveDistance", "currentSlideSize", "longSwipesMs", "stopIndex", "groupSize", "ratio", "longSwipes", "longSwipesRatio", "shortSwipes", "isNavButtonTarget", "nextEl", "prevEl", "onResize", "breakpoints", "setBreakpoint", "autoplay", "running", "paused", "run", "onClick", "preventClicks", "preventClicksPropagation", "stopImmediatePropagation", "onScroll", "scrollLeft", "scrollTop", "dummy<PERSON><PERSON><PERSON>ttached", "dummyEventListener", "attachEvents", "touchEvents", "capture", "start", "move", "end", "passiveListeners", "passive", "cancel", "updateOnWindowResize", "detachEvents", "breakpoint", "getBreakpoint", "currentBreakpoint", "breakpoint<PERSON>nly<PERSON><PERSON><PERSON>", "paramValue", "breakpointP<PERSON>ms", "originalParams", "wasMultiRow", "isMultiRow", "containerModifierClass", "directionChanged", "needsReLoop", "changeDirection", "points", "point", "minRatio", "substr", "innerHeight", "sort", "b", "innerWidth", "addClasses", "classNames", "suffixes", "suffix", "removeClasses", "classes", "loadImage", "imageEl", "src", "srcset", "sizes", "checkForComplete", "image", "onReady", "isPicture", "complete", "Image", "onload", "onerror", "preloadImages", "imagesLoaded", "imagesToLoad", "updateOnImagesReady", "currentSrc", "getAttribute", "images", "wasLocked", "lastSlidePosition", "checkOverflow$1", "defaults", "init", "uniqueNavElements", "prototypes", "extendedDefaults", "super", "prototypeGroup", "protoMethod", "moduleParamName", "swiperParams", "useModulesParams", "passedParams", "$", "swipers", "containerEl", "newParams", "shadowRoot", "querySelector", "touchEventsTouch", "touchEventsDesktop", "clickTimeout", "useModules", "spv", "breakLoop", "translateValue", "translated", "newDirection", "needUpdate", "currentDirection", "slideEl", "deleteInstance", "cleanStyles", "deleteProps", "newDefaults", "Device$1", "Support$1", "support", "Browser", "<PERSON><PERSON><PERSON><PERSON>", "isEdge", "isUiWebView", "test", "Browser$1", "browser", "Resize", "resize", "resize<PERSON><PERSON>ler", "orientationChangeHandler", "Observer", "func", "MutationObserver", "WebkitMutationObserver", "ObserverFunc", "mutations", "observerUpdate", "requestAnimationFrame", "observe", "attributes", "childList", "characterData", "observers", "observeParents", "containerParents", "attach", "observeSlideChildren", "disconnect", "Observer$1", "Virtual", "force", "addSlidesBefore", "addSlidesAfter", "previousFrom", "previousTo", "previousSlidesGrid", "renderSlide", "previousOffset", "offsetProp", "slidesAfter", "slidesBefore", "onRendered", "lazy", "load", "renderExternal", "slidesToRender", "prependIndexes", "appendIndexes", "$slideEl", "numberOfNewSlides", "newCache", "cachedIndex", "$cachedEl", "cachedElIndex", "Virtual$1", "overwriteParams", "Keyboard", "kc", "keyCode", "charCode", "shift<PERSON>ey", "altKey", "ctrl<PERSON>ey", "metaKey", "nodeName", "keyboard", "onlyInViewport", "inView", "windowWidth", "windowHeight", "swiperOffset", "left", "swiperCoord", "top", "returnValue", "Keyboard$1", "enable", "disable", "isEventSupported", "isSupported", "setAttribute", "implementation", "hasFeature", "Mousewheel", "lastScrollTime", "lastEventBeforeSnap", "recentWheelEvents", "PIXEL_STEP", "LINE_HEIGHT", "PAGE_HEIGHT", "sX", "sY", "pX", "pY", "detail", "wheelDelta", "wheelDeltaY", "wheelDeltaX", "HORIZONTAL_AXIS", "deltaY", "deltaX", "deltaMode", "spinX", "spinY", "pixelX", "pixelY", "mouseEntered", "mousewheel", "eventsTarged", "contains", "releaseOnEdges", "delta", "rtlFactor", "forceToAxis", "invert", "newEvent", "sign", "ignoreWheelEvents", "sensitivity", "clearTimeout", "timeout", "shift", "prevEvent", "firstEvent", "snapToThreshold", "autoplayDisableOnInteraction", "stop", "raw", "animateSlider", "releaseScroll", "getTime", "handleMouseEnter", "handleMouseLeave", "Mousewheel$1", "Navigation", "$nextEl", "$prevEl", "disabledClass", "lockClass", "onNextClick", "onPrevClick", "Navigation$1", "hideOnClick", "hiddenClass", "isHidden", "current", "total", "paginationType", "bullets", "firstIndex", "lastIndex", "midIndex", "dynamicBullets", "bulletSize", "dynamicMainBullets", "dynamicBulletIndex", "bulletActiveClass", "bullet", "$bullet", "bulletIndex", "$firstDisplayedBullet", "$lastDisplayedBullet", "dynamicBulletsLength", "bulletsOffset", "currentClass", "formatFractionCurrent", "totalClass", "formatFractionTotal", "progressbarDirection", "progressbarOpposite", "scale", "scaleX", "scaleY", "progressbarFillClass", "renderCustom", "paginationHTML", "numberOfBullets", "renderBullet", "bulletClass", "bulletElement", "renderFraction", "renderProgressbar", "clickable", "clickableClass", "modifierClass", "progressbarOppositeClass", "Pagination$1", "number", "scrollbar", "dragSize", "trackSize", "$dragEl", "newSize", "newPos", "hide", "opacity", "divider", "moveDivider", "display", "clientX", "clientY", "dragStartPos", "positionRatio", "getPointerPosition", "getBoundingClientRect", "setDragPosition", "dragTimeout", "snapOnRelease", "activeListener", "onDragStart", "onDragMove", "onDragEnd", "$swiperEl", "dragClass", "dragEl", "draggable", "enableDraggable", "disableDraggable", "Scrollbar$1", "Parallax", "p", "currentOpacity", "currentScale", "parallax", "setTransform", "parallaxEl", "$parallaxEl", "parallaxDuration", "Parallax$1", "Zoom", "x1", "y1", "x2", "y2", "zoom", "gesture", "fakeGestureTouched", "fakeGestureMoved", "scaleStart", "getDistanceBetweenTouches", "$imageEl", "$imageWrapEl", "maxRatio", "isScaling", "scaleMove", "touchesStart", "slideWidth", "slideHeight", "scaledWidth", "scaledHeight", "minX", "maxX", "minY", "maxY", "touchesCurrent", "prevPositionX", "prevPositionY", "prevTime", "momentumDurationX", "momentumDurationY", "momentumDistanceX", "newPositionX", "momentumDistanceY", "newPositionY", "out", "in", "touchX", "touchY", "offsetX", "offsetY", "translateX", "translateY", "imageWidth", "imageHeight", "translateMinX", "translateMinY", "translateMaxX", "translateMaxY", "zoomedSlideClass", "activeListenerWithCapture", "slideSelector", "onGestureStart", "onGestureChange", "onGestureEnd", "Zoom$1", "toggle", "onTransitionEnd", "Lazy", "loadInDuplicate", "$images", "elementClass", "loadedClass", "loadingClass", "imageIndex", "background", "$pictureEl", "sourceIndex", "sourceEl", "$source", "preloaderClass", "slideOriginalIndex", "originalSlide", "loadInSlide", "duplicatedSlide", "slideExist", "initialImageLoaded", "elIndex", "loadPrevNext", "loadPrevNextAmount", "amount", "maxIndex", "minIndex", "Lazy$1", "loadOnTransitionStart", "Controller", "LinearSpline", "binarySearch", "guess", "array", "i1", "i3", "interpolate", "c", "controller", "spline", "controlled", "control", "controlledTranslate", "setControlledTranslate", "by", "getInterpolateFunction", "inverse", "setControlledTransition", "Controller$1", "a11y", "role", "label", "notify", "lastSlideMessage", "nextSlideMessage", "firstSlideMessage", "prevSlideMessage", "message", "notification", "liveRegion", "disableEl", "makeElNotFocusable", "enableEl", "makeElFocusable", "bulletEl", "$bulletEl", "addElRole", "addElLabel", "paginationBulletMessage", "onEnterKey", "A11y", "notificationClass", "updateNavigation", "updatePagination", "History", "history", "pushState", "hashNavigation", "get<PERSON>ath<PERSON><PERSON><PERSON>", "scrollToSlide", "replaceState", "setHistoryPopState", "pathArray", "pathname", "part", "slugify", "currentState", "state", "slideHistory", "History$1", "setHistory", "HashNavigation", "newHash", "hash", "activeSlideHash", "slideHash", "watchState", "onHashCange", "HashNavigation$1", "setHash", "Autoplay", "$activeSlideEl", "reverseDirection", "stopOnLastSlide", "waitForTransition", "Autoplay$1", "disableOnInteraction", "pause", "document", "visibilityState", "onVisibilityChange", "Fade", "tx", "ty", "slideOpacity", "fadeEffect", "crossFade", "eventTriggered", "triggerEvents", "EffectFade", "C<PERSON>", "swiper<PERSON><PERSON><PERSON>", "swiperHeight", "cubeEffect", "$cubeShadowEl", "wrapperRotate", "shadow", "slideAngle", "round", "tz", "slideShadows", "shadowBefore", "shadowAfter", "shadowOffset", "shadowScale", "shadowAngle", "sin", "cos", "scale1", "scale2", "zFactor", "EffectCube", "Flip", "flipEffect", "limitRotation", "rotate", "rotateY", "rotateX", "zIndex", "EffectFlip", "Coverflow", "coverflowEffect", "center", "depth", "slideOffset", "offsetMultiplier", "modifier", "translateZ", "stretch", "slideTransform", "$shadowBeforeEl", "$shadowAfterEl", "prefixedPointerEvents", "ws", "<PERSON><PERSON><PERSON><PERSON>", "EffectCoverflow", "Thumbs", "thumbs", "thumbsParams", "swiperCreated", "thumbsContainerClass", "onThumbClick", "thumbsSwiper", "slideThumbActiveClass", "currentIndex", "initial", "autoScrollOffset", "useOffset", "newThumbsIndex", "currentThumbsIndex", "prevThumbsIndex", "nextThumbsIndex", "thumbsToActivate", "thumbActiveClass", "multipleActiveThumbs", "Thumbs$1"], "mappings": ";;;;;;;CAQC,SAASA,EAAEC,GAA0DA,EAAEC,EAAQ,EAAQ,QAAU,EAAQ,UAA1G,CAAoPC,GAAK,SAAUD,EAASE,EAAaC,GAAK,aAK9R,IAAIC,EALuSF,EAAYA,GAAaG,OAAOC,UAAUC,eAAeC,KAAKN,EAAY,WAAWA,EAAY,WAAWA,EAAYC,EAAIA,GAAKE,OAAOC,UAAUC,eAAeC,KAAKL,EAAI,WAAWA,EAAI,WAAWA,EAM/e,SAAWC,GACPA,EAAU,mBAAqB,SAC/BA,EAAU,wBAA0B,cACpCA,EAAU,mBAAqB,SAC/BA,EAAU,kBAAoB,UAJlC,CAKGA,IAAcA,EAAY,KAC7B,IAKIK,EAKAC,EAVAC,EAAkBN,OAAOO,OAAO,CAChCC,eAAgB,mBAChBC,aAAc,iBACdC,WAAY,kBAGhB,SAAWN,GACPA,EAAgB,SAAW,QAC3BA,EAAgB,cAAgB,cAFpC,CAGGA,IAAoBA,EAAkB,KAEzC,SAAWC,GACPA,EAAmB,cAAgB,aACnCA,EAAmB,eAAiB,cACpCA,EAAmB,2BAA6B,0BAChDA,EAAmB,0BAA4B,yBAJnD,CAKGA,IAAuBA,EAAqB,KAE/C,IAAIM,EAAgB,CAChB,OACA,gBACA,cACA,6BACA,2BACA,2BACA,yBACA,2BACA,yBACA,kBACA,gBACA,aACA,YACA,oBACA,aACA,WACA,QACA,MACA,YACA,cACA,WACA,iBACA,WACA,WACA,eACA,gBACA,SACA,iBACA,gBACA;;;;;;;;;;;;;;gFAgBJ,SAASC,IACL,IAAK,IAAIC,EAAI,EAAGC,EAAI,EAAGC,EAAKC,UAAUC,OAAQH,EAAIC,EAAID,IAAKD,GAAKG,UAAUF,GAAGG,OACxE,IAAIC,EAAIC,MAAMN,GAAIO,EAAI,EAA3B,IAA8BN,EAAI,EAAGA,EAAIC,EAAID,IACzC,IAAK,IAAIO,EAAIL,UAAUF,GAAIQ,EAAI,EAAGC,EAAKF,EAAEJ,OAAQK,EAAIC,EAAID,IAAKF,IAC1DF,EAAEE,GAAKC,EAAEC,GACjB,OAAOJ,EAMX,IAqKIM,EArKAC,EAAY,SAAUC,GACtB,OAAOA,EACFC,QAAQ,kBAAmB,SAC3BA,QAAQ,OAAQ,KAChBC,eAMLC,EAAwB,SAAUC,EAAQC,EAAOC,GACjD,IAAIC,EAAIC,EAAIC,EACZ,GAAIL,IAAYA,EAAgB,UAAG,CAC/B,IAAIM,GAA2C,QAA7BH,EAAKF,EAAMM,oBAAiC,IAAPJ,OAAgB,EAASA,EAAG9B,KAAK4B,KAAWA,EAAMO,KACzG,IAAe,OAAVP,QAA4B,IAAVA,OAAmB,EAASA,EAAMQ,SAAWH,EAAW,CAC3E,IAAII,EAAWrB,MAAMsB,KAAKX,EAAOY,QAC7BC,EAAQxB,MAAMsB,KAAKL,GAEvB,GAAII,EAASI,SAASb,EAAMQ,SAAWI,EAAME,MAAK,SAAUC,GAAQ,OAAON,EAASI,SAASE,MAAW,CACpG,IAAIC,EAAejB,EAAOiB,aACtBC,EAAcC,OAA6F,QAArFd,EAAoC,QAA9BD,EAAKJ,EAAOoB,oBAAiC,IAAPhB,OAAgB,EAASA,EAAGiB,eAA4B,IAAPhB,OAAgB,EAASA,EAAGiB,kBAC/IC,EAAmBJ,OAAOK,UAAUN,GAAeA,EAAc,KACrEhB,EAAK5B,EAAgBmD,WAAYR,EAAcM,GAC/CrB,EAAKP,EAAUrB,EAAgBmD,YAAaR,EAAcM,OAKtEG,EAAmB,SAAU1B,EAAQE,GACrCrB,EAAc8C,SAAQ,SAAUC,GAC5B5B,EAAO6B,GAAGD,GAAW,WAIjB,IAHA,IAAIE,EAAc5C,UAEd6C,EAAO,GACFC,EAAK,EAAGA,EAAK9C,UAAUC,OAAQ6C,IACpCD,EAAKC,GAAMF,EAAYE,GAE3B9B,EAAK+B,WAAM,EAAQnD,EAAe,CAAC8C,GAAYG,IAC/C,IAAIG,EAAgBvC,EAAUiC,GAC1BM,IAAkBN,GAClB1B,EAAK+B,WAAM,EAAQnD,EAAe,CAACoD,GAAgBH,WAS/DI,EAAoB,eACxB,SAASC,EAAarE,EAAasE,GAC/B,IAAIC,EAA+B,SAAUC,EAAOC,GAChD,IAAIrC,EAAIC,EAAIC,EAAIoC,EACZC,EAAmF,QAA1EtC,EAA2B,QAArBD,EAAKoC,EAAMI,YAAyB,IAAPxC,OAAgB,EAASA,EAAGyC,aAA0B,IAAPxC,OAAgB,EAASA,EAAGoC,GAC3H,YAAiBK,IAAVH,EACDA,EAC2E,QAA1ED,EAA2B,QAArBpC,EAAKkC,EAAMI,YAAyB,IAAPtC,OAAgB,EAASA,EAAGuC,aAA0B,IAAPH,OAAgB,EAASA,EAAG9C,EAAU6C,KAG/HM,EAAwB,SAAUC,EAASC,EAAST,GACpD,OAAQS,EAAQC,KACZX,EAA6BC,EAAOJ,IACpCY,EAAQG,IACRjF,EAAUkF,gBAEdC,EAAoB,SAAUL,EAASC,EAAST,GAChD,IAAIc,EAAeP,EAAsBC,EAASC,EAAST,GAC3D,OAAOA,EAAMe,QAAQD,IAAiB,MAEtCE,EAAkB,SAAUP,GAC5B,OAAOA,EAAQN,OAASL,GAExBmB,EAAyB,SAAUC,GACnC,MAAO,EAAC,OAAMZ,EAAW,KAAM,IAAI/B,SAAS2C,IAG5CC,EAAiB,SAAUnB,GAC3B,IAAIpC,EAAIC,EACJuD,GAAkC,QAArBxD,EAAKoC,EAAMI,YAAyB,IAAPxC,OAAgB,EAASA,EAAG0B,MAA0C,QAAjCzB,EAAKmC,EAAMqB,wBAAqC,IAAPxD,OAAgB,EAASA,EAAGyD,WACxJ,OAAO,SAAUC,GAIb,IAHA,IAMI3D,EANA2B,EAAc5C,UAEd6C,EAAO,GACFC,EAAK,EAAGA,EAAK9C,UAAUC,OAAQ6C,IACpCD,EAAKC,EAAK,GAAKF,EAAYE,GAG/B,IAAI+B,EAA6B,QAAnB5D,EAAKwD,SAA6B,IAAPxD,OAAgB,EAASA,EAAG2D,GACjEC,GACAA,EAAOC,IAAI/B,MAAM8B,EAAQhC,KAIrC,MAAO,CAEHkC,KAAM,SAAUlB,EAASC,EAAST,IAEqC,IAA/DQ,EAAQmB,UAAUC,QAAQ3F,EAAgBE,kBAC1CqE,EAAQmB,YAAenB,EAAQmB,UAAY,IAAM,IAAM1F,EAAgBE,gBAG3EqE,EAAQqB,iBAAiB,SAAS,SAAUnE,GACxC,IAAIoE,EAAYX,EAAenB,GAC3BvC,EAASoD,EAAkBL,EAASC,EAAST,GACjDxC,EAAsBC,EAAQC,EAAOoE,OAI7CC,SAAU,SAAUvB,EAASC,EAAST,GAClC,IAAIe,EAAUf,EAAMe,QAChBiB,EAAgBhB,EAAgBP,GAChCK,EAAeP,EAAsBC,EAASC,EAAST,GACvD8B,EAAYX,EAAenB,GAC3BiC,EAAalB,EACbtD,EAAwB,OAAfwE,QAAsC,IAAfA,OAAwB,EAASA,EAAWnB,GAE3ErD,IAAUA,EAAOyE,YAClBzE,EAAS,IAAIjC,EAAYgF,EAASwB,GAClCC,EAAWnB,GAAgBrD,EAC3B0B,EAAiB1B,EAAQqE,GACzBA,EAAU/F,EAAgBoG,MAAO1E,KAMzC2E,iBAAkB,SAAU5B,EAASC,EAAST,GAC1C,IAAIpC,EAAIC,EAAIC,EAAIoC,EAAImC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAC5CC,EAAa9C,EAA6BC,EAAOhE,EAAmB8G,YACxE,GAAI7B,EAAuB4B,GAAa,CACpC,IAAIpF,EAASoD,EAAkBL,EAASC,EAAST,GACjD,GAAIvC,EAAQ,CACR,IAAIuE,EAAgBhB,EAAgBP,GAChCsC,EAASf,EAAcgB,KACvBD,IAC6E,QAA5ElF,EAAuB,QAAjBD,EAAKH,SAA2B,IAAPG,OAAgB,EAASA,EAAGqF,mBAAgC,IAAPpF,GAAyBA,EAAG/B,KAAK8B,IAEjD,QAAxEE,EAAgB,OAAXL,QAA8B,IAAXA,OAAoB,EAASA,EAAOyF,cAA2B,IAAPpF,GAAyBA,EAAGhC,KAAK2B,GAC/B,QAAlF4E,EAAkC,QAA5BnC,EAAKzC,EAAO0F,kBAA+B,IAAPjD,OAAgB,EAASA,EAAGgD,cAA2B,IAAPb,GAAyBA,EAAGvG,KAAKoE,GACzC,QAAlFqC,EAAkC,QAA5BD,EAAK7E,EAAO2F,kBAA+B,IAAPd,OAAgB,EAASA,EAAGe,cAA2B,IAAPd,GAAyBA,EAAGzG,KAAKwG,GACzC,QAAlFG,EAAkC,QAA5BD,EAAK/E,EAAO2F,kBAA+B,IAAPZ,OAAgB,EAASA,EAAGU,cAA2B,IAAPT,GAAyBA,EAAG3G,KAAK0G,GACxHO,IAC4E,QAA3EJ,EAAuB,QAAjBD,EAAKjF,SAA2B,IAAPiF,OAAgB,EAASA,EAAGY,kBAA+B,IAAPX,GAAyBA,EAAG7G,KAAK4G,GAC5C,QAAxEE,EAAgB,OAAXnF,QAA8B,IAAXA,OAAoB,EAASA,EAAOyF,cAA2B,IAAPN,GAAyBA,EAAG9G,KAAK2B,OAMlI8F,OAAQ,SAAU/C,EAASC,EAAST,GAChC,IAAIpC,EACA4F,EAAczD,EAA6BC,EAAOhE,EAAmByH,aACzE,GAAIxC,EAAuBuC,GAAc,CACrC,IAAI/F,EAASoD,EAAkBL,EAASC,EAAST,GAC7CvC,GAAUA,EAAOiG,cACyD,QAAzE9F,EAAgB,OAAXH,QAA8B,IAAXA,OAAoB,EAASA,EAAOkG,eAA4B,IAAP/F,GAAyBA,EAAG9B,KAAK2B,EAAQwD,EAAuBlB,EAA6BC,EAAOhE,EAAmB4H,0BAA2B3C,EAAuBlB,EAA6BC,EAAOhE,EAAmB6H,8BAkBtU,SAASC,EAAmBtI,GACxB,IAAIoC,EACJ,OAAOnC,EAAIsI,OAAO,CACdxC,KAAM7F,EAAUsI,gBAChBC,OAAQrG,EAAK,CACLsG,eAAgB,CACZC,KAAMxI,OACNyI,UAAU,EACVC,QAAS,WAAc,MAAO,KAGlCC,QAAS,CACLH,KAAMxI,OACNyI,UAAU,IAGlBxG,EAAG5B,EAAmB8G,YAAc,CAChCqB,KAAMI,QACNF,SAAS,GAGbzG,EAAG5B,EAAmByH,aAAe,CACjCU,KAAMI,QACNF,SAAS,GAGbzG,EAAG5B,EAAmB4H,yBAA2B,CAC7CO,KAAMI,QACNH,UAAU,EACVC,SAAS,GAEbzG,EAAG5B,EAAmB6H,wBAA0B,CAC5CM,KAAMI,QACNH,UAAU,EACVC,SAAS,GAEbzG,GACJwC,KAAM,WACF,IAAIxC,EACJ,OAAOA,EAAK,GACRA,EAAGlC,EAAUkF,gBAAkB,KAC/BhD,GAER4G,SAAU,CACNC,eAAgB,CACZC,OAAO,EACPC,IAAK,SAAUlH,GACXlC,KAAKG,EAAUkF,gBAAkBnD,GAErCmH,IAAK,WACD,OAAOrJ,KAAKG,EAAUkF,kBAG9BoB,cAAe,WACX,OAAOzG,KAAK+I,SAAW/I,KAAK2I,gBAEhC9H,aAAc,WACV,OAAOb,KAAKyG,cAAc5F,cAAgBH,EAAgBG,eAGlEyI,QAAS,CAELC,kBAAmB,SAAUpH,GACzBF,EAAsBjC,KAAKkJ,eAAgB/G,EAAOnC,KAAKwJ,MAAMrD,KAAKnG,QAEtEyJ,iBAAkB,WACd,IAAIpH,EAAIC,EACR,GAAItC,KAAKkJ,gBAAkBlJ,KAAKyG,cAAcgB,KAAM,CAIhD,IAAIvF,EAASlC,KAAKkJ,eAC4D,QAA7E7G,EAAgB,OAAXH,QAA8B,IAAXA,OAAoB,EAASA,EAAOwF,mBAAgC,IAAPrF,GAAyBA,EAAG9B,KAAK2B,GAC1C,QAA5EI,EAAgB,OAAXJ,QAA8B,IAAXA,OAAoB,EAASA,EAAO6F,kBAA+B,IAAPzF,GAAyBA,EAAG/B,KAAK2B,KAG9HwH,aAAc,WACV,IAAIrH,EAAIC,EAAIC,EAAIoC,EAAImC,EAAIC,EAAIC,EAAIC,EAC5BjH,KAAKS,EAAmB8G,aAAevH,KAAKkJ,iBAC5ClJ,KAAKyJ,mBACgF,QAApFnH,EAAoC,QAA9BD,EAAKrC,KAAKkJ,sBAAmC,IAAP7G,OAAgB,EAASA,EAAGsF,cAA2B,IAAPrF,GAAyBA,EAAG/B,KAAK8B,GAC9B,QAA/FsC,EAA+C,QAAzCpC,EAAKvC,KAAKkJ,eAAetB,kBAA+B,IAAPrF,OAAgB,EAASA,EAAGoF,cAA2B,IAAPhD,GAAyBA,EAAGpE,KAAKgC,GACzC,QAA/FwE,EAA+C,QAAzCD,EAAK9G,KAAKkJ,eAAerB,kBAA+B,IAAPf,OAAgB,EAASA,EAAGgB,cAA2B,IAAPf,GAAyBA,EAAGxG,KAAKuG,GACzC,QAA/FG,EAA+C,QAAzCD,EAAKhH,KAAKkJ,eAAerB,kBAA+B,IAAPb,OAAgB,EAASA,EAAGW,cAA2B,IAAPV,GAAyBA,EAAG1G,KAAKyG,KAGjJ2C,cAAe,WACX,IAAItH,EAAIC,EACJtC,KAAKS,EAAmByH,cAAgBlI,KAAKkJ,gBAGzClJ,KAAKkJ,eAAef,cACkE,QAArF7F,EAAoC,QAA9BD,EAAKrC,KAAKkJ,sBAAmC,IAAP7G,OAAgB,EAASA,EAAG+F,eAA4B,IAAP9F,GAAyBA,EAAG/B,KAAK8B,EAAIrC,KAAKS,EAAmB4H,yBAA0BrI,KAAKS,EAAmB6H,2BAIzNsB,WAAY,WACR5J,KAAKkJ,eAAiB,IAAIjJ,EAAYD,KAAK6J,IAAK7J,KAAKyG,eACrD7C,EAAiB5D,KAAKkJ,eAAgBlJ,KAAKwJ,MAAMrD,KAAKnG,OACtDA,KAAKwJ,MAAMhJ,EAAgBoG,MAAO5G,KAAKkJ,kBAG/CY,QAAS,WACA9J,KAAKkJ,gBACNlJ,KAAK4J,cAIbG,UAAW,WACP/J,KAAK0J,gBAETM,QAAS,WACLhK,KAAK0J,gBAETO,cAAe,WAEXjK,KAAKkK,UAAUlK,KAAK2J,gBAExB7B,OAAQ,SAAUqC,GACd,OAAOA,EAAc,MAAO,CACxBC,YAAa1J,EAAgBE,eAC7BmD,GAAI,CACAsG,MAAOrK,KAAKuJ,oBAEjB,CACCvJ,KAAKsK,OAAO1I,EAAU2I,YACtBJ,EAAc,MAAO,CACjBK,MAAOxK,KAAKa,cACbb,KAAKsK,OAAOxB,SACf9I,KAAKsK,OAAO1I,EAAU6I,YACtBzK,KAAKsK,OAAO1I,EAAU8I,YACtB1K,KAAKsK,OAAO1I,EAAU+I,YACtB3K,KAAKsK,OAAO1I,EAAUgJ,iBA3ItC,SAAWhJ,GACPA,EAAU,cAAgB,cAC1BA,EAAU,cAAgB,aAC1BA,EAAU,aAAe,YACzBA,EAAU,cAAgB,cAC1BA,EAAU,cAAgB,eAL9B,CAMGA,IAAcA,EAAY,KA8I7B,IAAIiJ,EAAuB3K,EAAIsI,OAAO,CAClCxC,KAAM7F,EAAU0K,qBAChB5B,SAAU,CACNnI,WAAY,WACR,IAAIuB,EAAIC,EACR,OAA6F,QAApFA,EAA6B,QAAvBD,EAAKrC,KAAK8K,eAA4B,IAAPzI,OAAgB,EAASA,EAAGoE,qBAAkC,IAAPnE,OAAgB,EAASA,EAAGxB,aAAeJ,EAAgBI,aAGxKwI,QAAS,CACL3B,OAAQ,WACJ,IAAItF,EACA0I,EAAS/K,KAAK8K,QAEdC,EAAOtK,EAAmB8G,cACuD,QAAhFlF,EAAgB,OAAX0I,QAA8B,IAAXA,OAAoB,EAASA,EAAO7B,sBAAmC,IAAP7G,GAAyBA,EAAGsF,YAIjImC,QAAS,WACL9J,KAAK2H,UAETqC,QAAS,WACLhK,KAAK2H,UAETG,OAAQ,SAAUqC,GACd,OAAOA,EAAc,MAAO,CACxBK,MAAOxK,KAAKc,YACbd,KAAKsK,OAAOxB,YAOnBkC,EAAe,SAAU/K,GACzB,IAAIgL,EAAU,SAAU/K,EAAKqE,GACzB,IAAI0G,EAAQC,UAAZ,CAEA,IAAIzC,EAAkBF,EAAmBtI,GACrCsE,IACAkE,EAAgBM,QAAQL,MAAMC,eAAeG,QAAU,WAAc,OAAOvE,IAEhFrE,EAAIiL,UAAUhL,EAAUsI,gBAAiBA,GACzCvI,EAAIiL,UAAUhL,EAAU0K,qBAAsBA,GAC9C3K,EAAIkL,UAAUjL,EAAUkL,gBAAiB/G,EAAarE,EAAasE,IACnE0G,EAAQC,WAAY,IAExB,OAAOD,GAEX,SAASK,EAASrL,GACd,IAAIoC,EACJ,OAAOA,EAAK,CACJkJ,QAAS,QACTN,QAASD,EAAa/K,GACtBmL,UAAW9G,EAAarE,IAE5BoC,EAAGlC,EAAUsI,iBAAmBF,EAAmBtI,GACnDoC,EAAGlC,EAAU0K,sBAAwBA,EACrCxI,EAMR,IAAImJ,EAAmBF,EAASrL,GAC5BsL,EAAUC,EAAiBD,QAC3BN,EAAUO,EAAiBP,QAC3BG,EAAYI,EAAiBJ,UAC7BK,EAASD,EAAiBC,OAC1BC,EAAcF,EAAiBE,YAAY3L,EAAQ0L,OAAOA,EAAO1L,EAAQ2L,YAAYA,EAAY3L,EAAQ+I,QAAQ0C,EAAiBzL,EAAQqL,UAAUA,EAAUrL,EAAQkL,QAAQA,EAAQlL,EAAQwL,QAAQA,EAAQnL,OAAOuL,eAAe5L,EAAQ,aAAa,CAAC6E,OAAM,Q,kCC9dpQ,mCAeA,MAAMgH,EAAU,CACdC,SAAA,OACAC,YAAA,OACAC,SAAA,OACAC,YAAA,OACAC,KAAA,OACAC,WAAA,OACArH,KAAA,OACAsH,UAAA,OACAC,WAAY,OACZrI,GAAA,OACAsI,IAAA,OACAC,QAAA,OACAC,cAAe,OACfC,WAAA,OACAC,YAAA,OACAC,OAAA,OACAC,IAAA,OACAC,KAAA,OACAC,KAAA,OACAC,KAAA,OACAC,GAAA,OACAC,MAAA,OACAC,GAAA,OACAC,OAAA,OACAC,QAAA,OACAC,KAAA,OACAC,QAAA,OACAC,KAAA,OACAC,QAAA,OACAxC,OAAA,OACAyC,QAAA,OACAC,QAAA,OACAC,KAAA,OACAC,SAAA,OACAC,OAAA,OACAC,OAAA,OACAC,IAAA,OACAC,OAAA,QAGF3N,OAAO4N,KAAKpC,GAAS/H,QAASoK,IAC5B,OAAEC,GAAGD,GAAc,OAAEC,GAAGD,IAAerC,EAAQqC,KAGjD,MAAME,EAAQ,CACZ,YAAYC,GACV,MAAMC,EAASD,EACfhO,OAAO4N,KAAKK,GAAQxK,QAASa,IAC3B,IACE2J,EAAO3J,GAAO,KACd,MAAO4J,IAGT,WACSD,EAAO3J,GACd,MAAO4J,QAKb,SAASC,EAAUC,EAAQ,GACzB,OAAOC,WAAWF,EAAUC,IAE9B,MACE,OAAOE,KAAKC,OAEd,aAAaC,EAAIC,EAAO,KACtB,IAAIC,EACAC,EACAC,EAEJ,MAAMC,EAAW,OAAOC,iBAAiBN,EAAI,MA+B7C,OA7BI,OAAOO,iBACTJ,EAAeE,EAAS9C,WAAa8C,EAASG,gBAC1CL,EAAaM,MAAM,KAAKhO,OAAS,IACnC0N,EAAeA,EAAaM,MAAM,MAAMC,IAAK7N,GAAMA,EAAEM,QAAQ,IAAK,MAAMwN,KAAK,OAI/EP,EAAkB,IAAI,OAAOG,gBAAiC,SAAjBJ,EAA0B,GAAKA,KAE5EC,EAAkBC,EAASO,cAAgBP,EAASQ,YAAcR,EAASS,aAAeT,EAASU,aAAeV,EAAS9C,WAAa8C,EAASW,iBAAiB,aAAa7N,QAAQ,aAAc,sBACrM+M,EAASE,EAAgBa,WAAWR,MAAM,MAG/B,MAATR,IAE0BE,EAAxB,OAAOI,gBAAgCH,EAAgBc,IAEhC,KAAlBhB,EAAOzN,OAA8B0O,WAAWjB,EAAO,KAE5CiB,WAAWjB,EAAO,KAE3B,MAATD,IAE0BE,EAAxB,OAAOI,gBAAgCH,EAAgBgB,IAEhC,KAAlBlB,EAAOzN,OAA8B0O,WAAWjB,EAAO,KAE5CiB,WAAWjB,EAAO,KAEjCC,GAAgB,GAEzB,cAAckB,GACZ,MAAMC,EAAQ,GACd,IACIhP,EACAiP,EACAC,EACA/O,EAJAgP,EAAaJ,GAAO,OAAOK,SAASC,KAKxC,GAA0B,kBAAfF,GAA2BA,EAAWhP,OAK/C,IAJAgP,EAAaA,EAAWhK,QAAQ,MAAQ,EAAIgK,EAAWtO,QAAQ,QAAS,IAAM,GAC9EoO,EAASE,EAAWhB,MAAM,KAAKzB,OAAQ4C,GAA8B,KAAfA,GACtDnP,EAAS8O,EAAO9O,OAEXH,EAAI,EAAGA,EAAIG,EAAQH,GAAK,EAC3BkP,EAAQD,EAAOjP,GAAGa,QAAQ,QAAS,IAAIsN,MAAM,KAC7Ca,EAAMO,mBAAmBL,EAAM,KAA2B,qBAAbA,EAAM,QAAqBrL,EAAY0L,mBAAmBL,EAAM,KAAO,GAGxH,OAAOF,GAET,SAASQ,GACP,MAAoB,kBAANA,GAAwB,OAANA,GAAcA,EAAEC,aAAeD,EAAEC,cAAgBvQ,QAEnF,UAAU6D,GACR,MAAM2M,EAAKxQ,OAAO6D,EAAK,IACvB,IAAK,IAAI/C,EAAI,EAAGA,EAAI+C,EAAK5C,OAAQH,GAAK,EAAG,CACvC,MAAM2P,EAAa5M,EAAK/C,GACxB,QAAmB6D,IAAf8L,GAA2C,OAAfA,EAAqB,CACnD,MAAMC,EAAY1Q,OAAO4N,KAAK5N,OAAOyQ,IACrC,IAAK,IAAIE,EAAY,EAAGC,EAAMF,EAAUzP,OAAQ0P,EAAYC,EAAKD,GAAa,EAAG,CAC/E,MAAME,EAAUH,EAAUC,GACpBG,EAAO9Q,OAAO+Q,yBAAyBN,EAAYI,QAC5ClM,IAATmM,GAAsBA,EAAKE,aACzBjD,EAAMkD,SAAST,EAAGK,KAAa9C,EAAMkD,SAASR,EAAWI,IAC3D9C,EAAM3F,OAAOoI,EAAGK,GAAUJ,EAAWI,KAC3B9C,EAAMkD,SAAST,EAAGK,KAAa9C,EAAMkD,SAASR,EAAWI,KACnEL,EAAGK,GAAW,GACd9C,EAAM3F,OAAOoI,EAAGK,GAAUJ,EAAWI,KAErCL,EAAGK,GAAWJ,EAAWI,MAMnC,OAAOL,IAILU,EAAW,WACf,MAAO,CACLC,SAAW,iBAAkB,QAAY,OAAOC,eAAiB,iBAAsB,OAAOA,eAE9FC,gBAAiB,OAAOC,cAAiB,mBAAoB,OAAOC,WAAc,OAAOA,UAAUC,gBAAkB,EAErHC,SAAW,WACT,MAAQ,qBAAsB,QAAU,2BAA4B,OAD5D,GAIVC,gBAAkB,WAChB,IAAIC,GAAkB,EACtB,IACE,MAAMC,EAAO5R,OAAOuL,eAAe,GAAI,UAAW,CAEhD,MACEoG,GAAkB,KAGtB,OAAOzL,iBAAiB,sBAAuB,KAAM0L,GACrD,MAAO1D,IAGT,OAAOyD,EAbQ,GAgBjBE,SAAW,WACT,MAAO,mBAAoB,OADnB,IA1BE,GAgChB,MAAMhS,EACJ,YAAYkQ,EAAS,IACnB,MAAM+B,EAAOlS,KACbkS,EAAK/B,OAASA,EAGd+B,EAAKC,gBAAkB,GAEnBD,EAAK/B,QAAU+B,EAAK/B,OAAOpM,IAC7B3D,OAAO4N,KAAKkE,EAAK/B,OAAOpM,IAAIF,QAASC,IACnCoO,EAAKnO,GAAGD,EAAWoO,EAAK/B,OAAOpM,GAAGD,MAKxC,GAAGsO,EAAQC,EAASC,GAClB,MAAMJ,EAAOlS,KACb,GAAuB,oBAAZqS,EAAwB,OAAOH,EAC1C,MAAMK,EAASD,EAAW,UAAY,OAKtC,OAJAF,EAAO/C,MAAM,KAAKxL,QAAS1B,IACpB+P,EAAKC,gBAAgBhQ,KAAQ+P,EAAKC,gBAAgBhQ,GAAS,IAChE+P,EAAKC,gBAAgBhQ,GAAOoQ,GAAQF,KAE/BH,EAGT,KAAKE,EAAQC,EAASC,GACpB,MAAMJ,EAAOlS,KACb,GAAuB,oBAAZqS,EAAwB,OAAOH,EAC1C,SAASM,KAAevO,GACtBiO,EAAK7F,IAAI+F,EAAQI,GACbA,EAAYC,gBACPD,EAAYC,QAErBJ,EAAQlO,MAAM+N,EAAMjO,GAGtB,OADAuO,EAAYC,QAAUJ,EACfH,EAAKnO,GAAGqO,EAAQI,EAAaF,GAGtC,IAAIF,EAAQC,GACV,MAAMH,EAAOlS,KACb,OAAKkS,EAAKC,iBACVC,EAAO/C,MAAM,KAAKxL,QAAS1B,IACF,qBAAZkQ,EACTH,EAAKC,gBAAgBhQ,GAAS,GACrB+P,EAAKC,gBAAgBhQ,IAAU+P,EAAKC,gBAAgBhQ,GAAOd,QACpE6Q,EAAKC,gBAAgBhQ,GAAO0B,QAAQ,CAAC6O,EAAc1F,MAC7C0F,IAAiBL,GAAYK,EAAaD,SAAWC,EAAaD,UAAYJ,IAChFH,EAAKC,gBAAgBhQ,GAAOwQ,OAAO3F,EAAO,OAK3CkF,GAZ2BA,EAepC,QAAQjO,GACN,MAAMiO,EAAOlS,KACb,IAAKkS,EAAKC,gBAAiB,OAAOD,EAClC,IAAIE,EACAvN,EACAW,EACmB,kBAAZvB,EAAK,IAAmB1C,MAAMqR,QAAQ3O,EAAK,KACpDmO,EAASnO,EAAK,GACdY,EAAOZ,EAAK4O,MAAM,EAAG5O,EAAK5C,QAC1BmE,EAAU0M,IAEVE,EAASnO,EAAK,GAAGmO,OACjBvN,EAAOZ,EAAK,GAAGY,KACfW,EAAUvB,EAAK,GAAGuB,SAAW0M,GAE/B,MAAMY,EAAcvR,MAAMqR,QAAQR,GAAUA,EAASA,EAAO/C,MAAM,KAYlE,OAXAyD,EAAYjP,QAAS1B,IACnB,GAAI+P,EAAKC,iBAAmBD,EAAKC,gBAAgBhQ,GAAQ,CACvD,MAAM0D,EAAW,GACjBqM,EAAKC,gBAAgBhQ,GAAO0B,QAAS6O,IACnC7M,EAASkN,KAAKL,KAEhB7M,EAAShC,QAAS6O,IAChBA,EAAavO,MAAMqB,EAASX,QAI3BqN,EAGT,iBAAiBc,GACf,MAAMC,EAAWjT,KACZiT,EAASC,SACd9S,OAAO4N,KAAKiF,EAASC,SAASrP,QAASsP,IACrC,MAAMC,EAASH,EAASC,QAAQC,GAE5BC,EAAOjD,QACThC,EAAM3F,OAAOwK,EAAgBI,EAAOjD,UAK1C,WAAWkD,EAAgB,IACzB,MAAMJ,EAAWjT,KACZiT,EAASC,SACd9S,OAAO4N,KAAKiF,EAASC,SAASrP,QAASsP,IACrC,MAAMC,EAASH,EAASC,QAAQC,GAC1BG,EAAeD,EAAcF,IAAe,GAE9CC,EAAOH,UACT7S,OAAO4N,KAAKoF,EAAOH,UAAUpP,QAAS0P,IACpC,MAAMC,EAAaJ,EAAOH,SAASM,GAEjCN,EAASM,GADe,oBAAfC,EACkBA,EAAWrN,KAAK8M,GAEhBO,IAK7BJ,EAAOrP,IAAMkP,EAASlP,IACxB3D,OAAO4N,KAAKoF,EAAOrP,IAAIF,QAAS4P,IAC9BR,EAASlP,GAAG0P,EAAiBL,EAAOrP,GAAG0P,MAKvCL,EAAOM,QACTN,EAAOM,OAAOvN,KAAK8M,EAAnBG,CAA6BE,KAKnC,sBAAsBK,GACpB,MAAMC,EAAQ5T,KACT4T,EAAMC,KACXD,EAAMC,IAAIF,GAGZ,qBAAqBP,KAAWjD,GAC9B,MAAMyD,EAAQ5T,KACT4T,EAAMvT,UAAU6S,UAASU,EAAMvT,UAAU6S,QAAU,IACxD,MAAMlN,EAAOoN,EAAOpN,MAAQ,GAAI5F,OAAO4N,KAAK4F,EAAMvT,UAAU6S,SAAS7R,UAAU8M,EAAMQ,QAkBrF,OAjBAiF,EAAMvT,UAAU6S,QAAQlN,GAAQoN,EAE5BA,EAAOU,OACT1T,OAAO4N,KAAKoF,EAAOU,OAAOjQ,QAASa,IACjCkP,EAAMvT,UAAUqE,GAAO0O,EAAOU,MAAMpP,KAIpC0O,EAAOW,QACT3T,OAAO4N,KAAKoF,EAAOW,QAAQlQ,QAASa,IAClCkP,EAAMlP,GAAO0O,EAAOW,OAAOrP,KAI3B0O,EAAOnI,SACTmI,EAAOnI,QAAQ9G,MAAMyP,EAAOzD,GAEvByD,EAGT,WAAWR,KAAWjD,GACpB,MAAMyD,EAAQ5T,KACd,OAAIuB,MAAMqR,QAAQQ,IAChBA,EAAOvP,QAASmQ,GAAMJ,EAAMK,cAAcD,IACnCJ,GAEFA,EAAMK,cAAcb,KAAWjD,IAI1C,SAAS+D,IACP,MAAMhS,EAASlC,KACf,IAAImU,EACAC,EACJ,MAAMvK,EAAM3H,EAAO2H,IAEjBsK,EADiC,qBAAxBjS,EAAOiO,OAAOgE,MACfjS,EAAOiO,OAAOgE,MAEdtK,EAAI,GAAGwK,YAGfD,EADkC,qBAAzBlS,EAAOiO,OAAOiE,OACdlS,EAAOiO,OAAOiE,OAEdvK,EAAI,GAAGyK,aAEH,IAAVH,GAAejS,EAAOqS,gBAA+B,IAAXH,GAAgBlS,EAAOsS,eAKtEL,EAAQA,EAAQM,SAAS5K,EAAI8C,IAAI,gBAAiB,IAAM8H,SAAS5K,EAAI8C,IAAI,iBAAkB,IAC3FyH,EAASA,EAASK,SAAS5K,EAAI8C,IAAI,eAAgB,IAAM8H,SAAS5K,EAAI8C,IAAI,kBAAmB,IAE7FwB,EAAM3F,OAAOtG,EAAQ,CACnBiS,QACAC,SACAM,KAAMxS,EAAOqS,eAAiBJ,EAAQC,KAI1C,SAASO,IACP,MAAMzS,EAASlC,KACTmQ,EAASjO,EAAOiO,QAEhB,WACJyE,EAAYF,KAAMG,EAAYC,aAAcC,EAAG,SAAEC,GAC/C9S,EACE+S,EAAY/S,EAAOgT,SAAW/E,EAAO+E,QAAQC,QAC7CC,EAAuBH,EAAY/S,EAAOgT,QAAQpS,OAAOzB,OAASa,EAAOY,OAAOzB,OAChFyB,EAAS8R,EAAWjH,SAAS,IAAIzL,EAAOiO,OAAOrP,YAC/CuU,EAAeJ,EAAY/S,EAAOgT,QAAQpS,OAAOzB,OAASyB,EAAOzB,OACvE,IAAIiU,EAAW,GACf,MAAMC,EAAa,GACbC,EAAkB,GAExB,SAASC,EAAgBC,GACvB,OAAKvF,EAAOwF,SACRD,IAAe5S,EAAOzB,OAAS,EAMrC,IAAIuU,EAAezF,EAAO0F,mBACE,oBAAjBD,IACTA,EAAezF,EAAO0F,mBAAmBtV,KAAK2B,IAGhD,IAAI4T,EAAc3F,EAAO4F,kBACE,oBAAhBD,IACTA,EAAc3F,EAAO4F,kBAAkBxV,KAAK2B,IAG9C,MAAM8T,EAAyB9T,EAAOoT,SAASjU,OACzC4U,EAA2B/T,EAAOoT,SAASjU,OAEjD,IAiBI6U,EAaAC,EA9BAC,EAAejG,EAAOiG,aACtBC,GAAiBT,EACjBU,EAAgB,EAChBtJ,EAAQ,EACZ,GAA0B,qBAAf6H,EACT,OAE0B,kBAAjBuB,GAA6BA,EAAa/P,QAAQ,MAAQ,IACnE+P,EAAgBrG,WAAWqG,EAAarU,QAAQ,IAAK,KAAO,IAAO8S,GAGrE3S,EAAOqU,aAAeH,EAGlBrB,EAAKjS,EAAO6J,IAAI,CAAE6J,WAAY,GAAIC,UAAW,KAC5C3T,EAAO6J,IAAI,CAAE+J,YAAa,GAAIC,aAAc,KAG7CxG,EAAOyG,gBAAkB,IAEzBV,EADEW,KAAKC,MAAMzB,EAAelF,EAAOyG,mBAAqBvB,EAAenT,EAAOiO,OAAOyG,gBAC5DvB,EAEAwB,KAAKE,KAAK1B,EAAelF,EAAOyG,iBAAmBzG,EAAOyG,gBAExD,SAAzBzG,EAAO6G,eAA2D,QAA/B7G,EAAO8G,sBAC5Cf,EAAyBW,KAAKK,IAAIhB,EAAwB/F,EAAO6G,cAAgB7G,EAAOyG,mBAM5F,MAAMA,EAAkBzG,EAAOyG,gBACzBO,EAAejB,EAAyBU,EACxCQ,EAAiBP,KAAKC,MAAMzB,EAAelF,EAAOyG,iBACxD,IAAK,IAAI1V,EAAI,EAAGA,EAAImU,EAAcnU,GAAK,EAAG,CACxCiV,EAAY,EACZ,MAAMkB,EAAQvU,EAAOmK,GAAG/L,GACxB,GAAIiP,EAAOyG,gBAAkB,EAAG,CAE9B,IAAIU,EACAC,EACAC,EACJ,GAAmC,QAA/BrH,EAAO8G,qBAAiC9G,EAAOsH,eAAiB,EAAG,CACrE,MAAMC,EAAab,KAAKC,MAAM5V,GAAKiP,EAAOsH,eAAiBtH,EAAOyG,kBAC5De,EAAoBzW,EAAIiP,EAAOyG,gBAAkBzG,EAAOsH,eAAiBC,EACzEE,EAAgC,IAAfF,EACnBvH,EAAOsH,eACPZ,KAAKgB,IAAIhB,KAAKE,MAAM1B,EAAeqC,EAAad,EAAkBzG,EAAOsH,gBAAkBb,GAAkBzG,EAAOsH,gBACxHD,EAAMX,KAAKC,MAAMa,EAAoBC,GACrCL,EAAUI,EAAoBH,EAAMI,EAAkBF,EAAavH,EAAOsH,eAE1EH,EAAqBC,EAAWC,EAAMtB,EAA0BU,EAChES,EACG1K,IAAI,CACH,4BAA6B2K,EAC7B,yBAA0BA,EAC1B,iBAAkBA,EAClB,gBAAiBA,EACjBQ,MAAOR,QAE6B,WAA/BnH,EAAO8G,qBAChBM,EAASV,KAAKC,MAAM5V,EAAI0V,GACxBY,EAAMtW,EAAKqW,EAASX,GAChBW,EAASH,GAAmBG,IAAWH,GAAkBI,IAAQZ,EAAkB,KACrFY,GAAO,EACHA,GAAOZ,IACTY,EAAM,EACND,GAAU,MAIdC,EAAMX,KAAKC,MAAM5V,EAAIiW,GACrBI,EAASrW,EAAKsW,EAAML,GAEtBE,EAAM1K,IACJ,WAAUzK,EAAOqS,eAAiB,MAAQ,QACjC,IAARiD,GAAarH,EAAOiG,cAAqBjG,EAAOiG,aAAX,MAG1C,GAA6B,SAAzBiB,EAAM1K,IAAI,WAAd,CAEA,GAA6B,SAAzBwD,EAAO6G,cAA0B,CACnC,MAAMe,EAAc,OAAO7I,iBAAiBmI,EAAM,GAAI,MAChDW,EAAmBX,EAAM,GAAGY,MAAM9L,UAClC+L,EAAyBb,EAAM,GAAGY,MAAM7I,gBAO9C,GANI4I,IACFX,EAAM,GAAGY,MAAM9L,UAAY,QAEzB+L,IACFb,EAAM,GAAGY,MAAM7I,gBAAkB,QAE/Be,EAAOgI,aACThC,EAAYjU,EAAOqS,eACf8C,EAAM7K,YAAW,GACjB6K,EAAM5K,aAAY,QAGtB,GAAIvK,EAAOqS,eAAgB,CACzB,MAAMJ,EAAQpE,WAAWgI,EAAYnI,iBAAiB,UAChDwI,EAAcrI,WAAWgI,EAAYnI,iBAAiB,iBACtDyI,EAAetI,WAAWgI,EAAYnI,iBAAiB,kBACvD4G,EAAazG,WAAWgI,EAAYnI,iBAAiB,gBACrD8G,EAAc3G,WAAWgI,EAAYnI,iBAAiB,iBACtD0I,EAAYP,EAAYnI,iBAAiB,cAE7CuG,EADEmC,GAA2B,eAAdA,EACHnE,EAAQqC,EAAaE,EAErBvC,EAAQiE,EAAcC,EAAe7B,EAAaE,MAE3D,CACL,MAAMtC,EAASrE,WAAWgI,EAAYnI,iBAAiB,WACjD2I,EAAaxI,WAAWgI,EAAYnI,iBAAiB,gBACrD4I,EAAgBzI,WAAWgI,EAAYnI,iBAAiB,mBACxD6G,EAAY1G,WAAWgI,EAAYnI,iBAAiB,eACpD+G,EAAe5G,WAAWgI,EAAYnI,iBAAiB,kBACvD0I,EAAYP,EAAYnI,iBAAiB,cAE7CuG,EADEmC,GAA2B,eAAdA,EACHlE,EAASqC,EAAYE,EAErBvC,EAASmE,EAAaC,EAAgB/B,EAAYE,EAIhEqB,IACFX,EAAM,GAAGY,MAAM9L,UAAY6L,GAEzBE,IACFb,EAAM,GAAGY,MAAM7I,gBAAkB8I,GAE/B/H,EAAOgI,eAAchC,EAAYU,KAAKC,MAAMX,SAEhDA,GAAatB,GAAe1E,EAAO6G,cAAgB,GAAKZ,GAAiBjG,EAAO6G,cAC5E7G,EAAOgI,eAAchC,EAAYU,KAAKC,MAAMX,IAE5CrT,EAAO5B,KACLgB,EAAOqS,eACTzR,EAAO5B,GAAG+W,MAAM9D,MAAWgC,EAAH,KAExBrT,EAAO5B,GAAG+W,MAAM7D,OAAY+B,EAAH,MAI3BrT,EAAO5B,KACT4B,EAAO5B,GAAGuX,gBAAkBtC,GAE9BX,EAAgBzC,KAAKoD,GAGjBhG,EAAOuI,gBACTrC,EAAgBA,EAAiBF,EAAY,EAAMG,EAAgB,EAAKF,EAClD,IAAlBE,GAA6B,IAANpV,IAASmV,EAAgBA,EAAiBxB,EAAa,EAAKuB,GAC7E,IAANlV,IAASmV,EAAgBA,EAAiBxB,EAAa,EAAKuB,GAC5DS,KAAK8B,IAAItC,GAAiB,OAAUA,EAAgB,GACpDlG,EAAOgI,eAAc9B,EAAgBQ,KAAKC,MAAMT,IAChD,EAAUlG,EAAOsH,iBAAmB,GAAGnC,EAASvC,KAAKsD,GACzDd,EAAWxC,KAAKsD,KAEZlG,EAAOgI,eAAc9B,EAAgBQ,KAAKC,MAAMT,KAC/CrJ,EAAQ6J,KAAKgB,IAAI3V,EAAOiO,OAAOyI,mBAAoB5L,IAAU9K,EAAOiO,OAAOsH,iBAAmB,GAAGnC,EAASvC,KAAKsD,GACpHd,EAAWxC,KAAKsD,GAChBA,EAAgBA,EAAgBF,EAAYC,GAG9ClU,EAAOqU,aAAeJ,EAAYC,EAElCE,EAAgBH,EAEhBnJ,GAAS,GAGX,IAAI6L,EAWJ,GAZA3W,EAAOqU,YAAcM,KAAKK,IAAIhV,EAAOqU,YAAa1B,GAAciB,EAI9Df,GAAOC,IAA+B,UAAlB7E,EAAO2I,QAAwC,cAAlB3I,EAAO2I,SACxDlE,EAAWjI,IAAI,CAAEwH,MAAUjS,EAAOqU,YAAcpG,EAAOiG,aAA/B,OAEtBjG,EAAO4I,iBACL7W,EAAOqS,eAAgBK,EAAWjI,IAAI,CAAEwH,MAAUjS,EAAOqU,YAAcpG,EAAOiG,aAA/B,OAC9CxB,EAAWjI,IAAI,CAAEyH,OAAWlS,EAAOqU,YAAcpG,EAAOiG,aAA/B,QAG5BjG,EAAOyG,gBAAkB,IAC3B1U,EAAOqU,aAAeJ,EAAYhG,EAAOiG,cAAgBF,EACzDhU,EAAOqU,YAAcM,KAAKE,KAAK7U,EAAOqU,YAAcpG,EAAOyG,iBAAmBzG,EAAOiG,aACjFlU,EAAOqS,eAAgBK,EAAWjI,IAAI,CAAEwH,MAAUjS,EAAOqU,YAAcpG,EAAOiG,aAA/B,OAC9CxB,EAAWjI,IAAI,CAAEyH,OAAWlS,EAAOqU,YAAcpG,EAAOiG,aAA/B,OAC1BjG,EAAOuI,gBAAgB,CACzBG,EAAgB,GAChB,IAAK,IAAI3X,EAAI,EAAGA,EAAIoU,EAASjU,OAAQH,GAAK,EAAG,CAC3C,IAAI8X,EAAiB1D,EAASpU,GAC1BiP,EAAOgI,eAAca,EAAiBnC,KAAKC,MAAMkC,IACjD1D,EAASpU,GAAKgB,EAAOqU,YAAcjB,EAAS,IAAIuD,EAAc9F,KAAKiG,GAEzE1D,EAAWuD,EAKf,IAAK1I,EAAOuI,eAAgB,CAC1BG,EAAgB,GAChB,IAAK,IAAI3X,EAAI,EAAGA,EAAIoU,EAASjU,OAAQH,GAAK,EAAG,CAC3C,IAAI8X,EAAiB1D,EAASpU,GAC1BiP,EAAOgI,eAAca,EAAiBnC,KAAKC,MAAMkC,IACjD1D,EAASpU,IAAMgB,EAAOqU,YAAc1B,GACtCgE,EAAc9F,KAAKiG,GAGvB1D,EAAWuD,EACPhC,KAAKC,MAAM5U,EAAOqU,YAAc1B,GAAcgC,KAAKC,MAAMxB,EAASA,EAASjU,OAAS,IAAM,GAC5FiU,EAASvC,KAAK7Q,EAAOqU,YAAc1B,GAYvC,GATwB,IAApBS,EAASjU,SAAciU,EAAW,CAAC,IAEX,IAAxBnF,EAAOiG,eACLlU,EAAOqS,eACLQ,EAAKjS,EAAO8K,OAAO6H,GAAiB9I,IAAI,CAAE6J,WAAeJ,EAAH,OACrDtT,EAAO8K,OAAO6H,GAAiB9I,IAAI,CAAE+J,YAAgBN,EAAH,OAClDtT,EAAO8K,OAAO6H,GAAiB9I,IAAI,CAAEgK,aAAiBP,EAAH,QAGxDjG,EAAOuI,gBAAkBvI,EAAO8I,qBAAsB,CACxD,IAAIC,EAAgB,EACpB1D,EAAgB3R,QAASsV,IACvBD,GAAiBC,GAAkBhJ,EAAOiG,aAAejG,EAAOiG,aAAe,KAEjF8C,GAAiB/I,EAAOiG,aACxB,MAAMgD,EAAUF,EAAgBrE,EAChCS,EAAWA,EAAShG,IAAK+J,GACnBA,EAAO,GAAWzD,EAClByD,EAAOD,EAAgBA,EAAUtD,EAC9BuD,GAIX,GAAIlJ,EAAOmJ,yBAA0B,CACnC,IAAIJ,EAAgB,EAKpB,GAJA1D,EAAgB3R,QAASsV,IACvBD,GAAiBC,GAAkBhJ,EAAOiG,aAAejG,EAAOiG,aAAe,KAEjF8C,GAAiB/I,EAAOiG,aACpB8C,EAAgBrE,EAAY,CAC9B,MAAM0E,GAAmB1E,EAAaqE,GAAiB,EACvD5D,EAASzR,QAAQ,CAACwV,EAAMG,KACtBlE,EAASkE,GAAaH,EAAOE,IAE/BhE,EAAW1R,QAAQ,CAACwV,EAAMG,KACxBjE,EAAWiE,GAAaH,EAAOE,KAKrCpL,EAAM3F,OAAOtG,EAAQ,CACnBY,SACAwS,WACAC,aACAC,oBAGEH,IAAiBD,GACnBlT,EAAOE,KAAK,sBAEVkT,EAASjU,SAAW2U,IAClB9T,EAAOiO,OAAOsJ,eAAevX,EAAOwX,gBACxCxX,EAAOE,KAAK,yBAEVmT,EAAWlU,SAAW4U,GACxB/T,EAAOE,KAAK,2BAGV+N,EAAOwJ,qBAAuBxJ,EAAOyJ,wBACvC1X,EAAO2X,qBAIX,SAASC,EAAkBC,GACzB,MAAM7X,EAASlC,KACTga,EAAe,GACrB,IACI9Y,EADA+Y,EAAY,EAQhB,GANqB,kBAAVF,EACT7X,EAAOgY,cAAcH,IACF,IAAVA,GACT7X,EAAOgY,cAAchY,EAAOiO,OAAO4J,OAGD,SAAhC7X,EAAOiO,OAAO6G,eAA4B9U,EAAOiO,OAAO6G,cAAgB,EAC1E,GAAI9U,EAAOiO,OAAOuI,eAChBxW,EAAOiY,cAAcvN,KAAK,CAACI,EAAOqK,KAChC2C,EAAajH,KAAKsE,UAGpB,IAAKnW,EAAI,EAAGA,EAAI2V,KAAKE,KAAK7U,EAAOiO,OAAO6G,eAAgB9V,GAAK,EAAG,CAC9D,MAAM8L,EAAQ9K,EAAOkY,YAAclZ,EACnC,GAAI8L,EAAQ9K,EAAOY,OAAOzB,OAAQ,MAClC2Y,EAAajH,KAAK7Q,EAAOY,OAAOmK,GAAGD,GAAO,SAI9CgN,EAAajH,KAAK7Q,EAAOY,OAAOmK,GAAG/K,EAAOkY,aAAa,IAIzD,IAAKlZ,EAAI,EAAGA,EAAI8Y,EAAa3Y,OAAQH,GAAK,EACxC,GAA+B,qBAApB8Y,EAAa9Y,GAAoB,CAC1C,MAAMkT,EAAS4F,EAAa9Y,GAAGmZ,aAC/BJ,EAAY7F,EAAS6F,EAAY7F,EAAS6F,EAK1CA,GAAW/X,EAAO0S,WAAWjI,IAAI,SAAasN,EAAH,MAGjD,SAASJ,IACP,MAAM3X,EAASlC,KACT8C,EAASZ,EAAOY,OACtB,IAAK,IAAI5B,EAAI,EAAGA,EAAI4B,EAAOzB,OAAQH,GAAK,EACtC4B,EAAO5B,GAAGoZ,kBAAoBpY,EAAOqS,eAAiBzR,EAAO5B,GAAGqZ,WAAazX,EAAO5B,GAAGsZ,UAI3F,SAASC,EAAsBC,EAAa1a,MAAQA,KAAK0a,WAAc,GACrE,MAAMxY,EAASlC,KACTmQ,EAASjO,EAAOiO,QAEhB,OAAErN,EAAQgS,aAAcC,GAAQ7S,EAEtC,GAAsB,IAAlBY,EAAOzB,OAAc,OACkB,qBAAhCyB,EAAO,GAAGwX,mBAAmCpY,EAAO2X,qBAE/D,IAAIc,GAAgBD,EAChB3F,IAAK4F,EAAeD,GAGxB5X,EAAOgJ,YAAYqE,EAAOyK,mBAE1B1Y,EAAO2Y,qBAAuB,GAC9B3Y,EAAOiY,cAAgB,GAEvB,IAAK,IAAIjZ,EAAI,EAAGA,EAAI4B,EAAOzB,OAAQH,GAAK,EAAG,CACzC,MAAMmW,EAAQvU,EAAO5B,GACf4Z,GACHH,GAAgBxK,EAAOuI,eAAiBxW,EAAO6Y,eAAiB,GAAM1D,EAAMiD,oBAC1EjD,EAAMoB,gBAAkBtI,EAAOiG,cACpC,GAAIjG,EAAOyJ,uBAA0BzJ,EAAOuI,gBAAkBvI,EAAO6K,WAAa,CAChF,MAAMC,IAAgBN,EAAetD,EAAMiD,mBACrCY,EAAaD,EAAc/Y,EAAOsT,gBAAgBtU,GAClDia,EAAaF,GAAe,GAAKA,EAAc/Y,EAAOwS,KAAO,GACrDwG,EAAa,GAAKA,GAAchZ,EAAOwS,MACvCuG,GAAe,GAAKC,GAAchZ,EAAOwS,KACnDyG,IACFjZ,EAAOiY,cAAcpH,KAAKsE,GAC1BnV,EAAO2Y,qBAAqB9H,KAAK7R,GACjC4B,EAAOmK,GAAG/L,GAAG2K,SAASsE,EAAOyK,oBAGjCvD,EAAM+D,SAAWrG,GAAO+F,EAAgBA,EAE1C5Y,EAAOiY,cAAgB,eAAEjY,EAAOiY,eAGlC,SAASkB,EAAgBX,GACvB,MAAMxY,EAASlC,KACf,GAAyB,qBAAd0a,EAA2B,CACpC,MAAMY,EAAapZ,EAAO4S,cAAgB,EAAI,EAE9C4F,EAAaxY,GAAUA,EAAOwY,WAAcxY,EAAOwY,UAAYY,GAAgB,EAEjF,MAAMnL,EAASjO,EAAOiO,OAChBoL,EAAiBrZ,EAAOsZ,eAAiBtZ,EAAO6Y,eACtD,IAAI,SAAEK,EAAQ,YAAEK,EAAW,MAAEC,GAAUxZ,EACvC,MAAMyZ,EAAeF,EACfG,EAASF,EACQ,IAAnBH,GACFH,EAAW,EACXK,GAAc,EACdC,GAAQ,IAERN,GAAYV,EAAYxY,EAAO6Y,gBAAkB,EACjDU,EAAcL,GAAY,EAC1BM,EAAQN,GAAY,GAEtBjN,EAAM3F,OAAOtG,EAAQ,CACnBkZ,WACAK,cACAC,WAGEvL,EAAOwJ,qBAAuBxJ,EAAOyJ,uBAA0BzJ,EAAOuI,gBAAkBvI,EAAO6K,aAAa9Y,EAAOuY,qBAAqBC,GAExIe,IAAgBE,GAClBzZ,EAAOE,KAAK,yBAEVsZ,IAAUE,GACZ1Z,EAAOE,KAAK,oBAETuZ,IAAiBF,GAAiBG,IAAWF,IAChDxZ,EAAOE,KAAK,YAGdF,EAAOE,KAAK,WAAYgZ,GAG1B,SAASS,IACP,MAAM3Z,EAASlC,MAET,OACJ8C,EAAM,OAAEqN,EAAM,WAAEyE,EAAU,YAAEwF,EAAW,UAAE0B,GACvC5Z,EACE+S,EAAY/S,EAAOgT,SAAW/E,EAAO+E,QAAQC,QAInD,IAAI4G,EAFJjZ,EAAOgJ,YAAY,GAAGqE,EAAO6L,oBAAoB7L,EAAO8L,kBAAkB9L,EAAO+L,kBAAkB/L,EAAOgM,6BAA6BhM,EAAOiM,2BAA2BjM,EAAOkM,2BAI9KN,EADE9G,EACY/S,EAAO0S,WAAWlH,KAAK,IAAIyC,EAAOrP,uCAAuCsZ,OAEzEtX,EAAOmK,GAAGmN,GAI1B2B,EAAYlQ,SAASsE,EAAO6L,kBAExB7L,EAAO1I,OAELsU,EAAYhQ,SAASoE,EAAOmM,qBAC9B1H,EACGjH,SAAS,IAAIwC,EAAOrP,mBAAmBqP,EAAOmM,iDAAiDR,OAC/FjQ,SAASsE,EAAOgM,2BAEnBvH,EACGjH,SAAS,IAAIwC,EAAOrP,cAAcqP,EAAOmM,gDAAgDR,OACzFjQ,SAASsE,EAAOgM,4BAIvB,IAAII,EAAYR,EAAY1O,QAAQ,IAAI8C,EAAOrP,YAAcmM,GAAG,GAAGpB,SAASsE,EAAO8L,gBAC/E9L,EAAO1I,MAA6B,IAArB8U,EAAUlb,SAC3Bkb,EAAYzZ,EAAOmK,GAAG,GACtBsP,EAAU1Q,SAASsE,EAAO8L,iBAG5B,IAAIO,EAAYT,EAAYxO,QAAQ,IAAI4C,EAAOrP,YAAcmM,GAAG,GAAGpB,SAASsE,EAAO+L,gBAC/E/L,EAAO1I,MAA6B,IAArB+U,EAAUnb,SAC3Bmb,EAAY1Z,EAAOmK,IAAI,GACvBuP,EAAU3Q,SAASsE,EAAO+L,iBAExB/L,EAAO1I,OAEL8U,EAAUxQ,SAASoE,EAAOmM,qBAC5B1H,EACGjH,SAAS,IAAIwC,EAAOrP,mBAAmBqP,EAAOmM,iDAAiDC,EAAUtQ,KAAK,gCAC9GJ,SAASsE,EAAOiM,yBAEnBxH,EACGjH,SAAS,IAAIwC,EAAOrP,cAAcqP,EAAOmM,gDAAgDC,EAAUtQ,KAAK,gCACxGJ,SAASsE,EAAOiM,yBAEjBI,EAAUzQ,SAASoE,EAAOmM,qBAC5B1H,EACGjH,SAAS,IAAIwC,EAAOrP,mBAAmBqP,EAAOmM,iDAAiDE,EAAUvQ,KAAK,gCAC9GJ,SAASsE,EAAOkM,yBAEnBzH,EACGjH,SAAS,IAAIwC,EAAOrP,cAAcqP,EAAOmM,gDAAgDE,EAAUvQ,KAAK,gCACxGJ,SAASsE,EAAOkM,0BAKzB,SAASI,EAAmBC,GAC1B,MAAMxa,EAASlC,KACT0a,EAAYxY,EAAO4S,aAAe5S,EAAOwY,WAAaxY,EAAOwY,WAC7D,WACJnF,EAAU,SAAED,EAAQ,OAAEnF,EAAQiK,YAAauC,EAAeb,UAAWc,EAAmBpD,UAAWqD,GACjG3a,EACJ,IACIsX,EADAY,EAAcsC,EAElB,GAA2B,qBAAhBtC,EAA6B,CACtC,IAAK,IAAIlZ,EAAI,EAAGA,EAAIqU,EAAWlU,OAAQH,GAAK,EACT,qBAAtBqU,EAAWrU,EAAI,GACpBwZ,GAAanF,EAAWrU,IAAMwZ,EAAYnF,EAAWrU,EAAI,IAAOqU,EAAWrU,EAAI,GAAKqU,EAAWrU,IAAM,EACvGkZ,EAAclZ,EACLwZ,GAAanF,EAAWrU,IAAMwZ,EAAYnF,EAAWrU,EAAI,KAClEkZ,EAAclZ,EAAI,GAEXwZ,GAAanF,EAAWrU,KACjCkZ,EAAclZ,GAIdiP,EAAO2M,sBACL1C,EAAc,GAA4B,qBAAhBA,KAA6BA,EAAc,GAG7E,GAAI9E,EAASjP,QAAQqU,IAAc,EACjClB,EAAYlE,EAASjP,QAAQqU,OACxB,CACL,MAAMqC,EAAOlG,KAAKgB,IAAI1H,EAAOyI,mBAAoBwB,GACjDZ,EAAYuD,EAAOlG,KAAKC,OAAOsD,EAAc2C,GAAQ5M,EAAOsH,gBAG9D,GADI+B,GAAalE,EAASjU,SAAQmY,EAAYlE,EAASjU,OAAS,GAC5D+Y,IAAgBuC,EAKlB,YAJInD,IAAcqD,IAChB3a,EAAOsX,UAAYA,EACnBtX,EAAOE,KAAK,qBAMhB,MAAM0Z,EAAYrH,SAASvS,EAAOY,OAAOmK,GAAGmN,GAAanO,KAAK,4BAA8BmO,EAAa,IAEzGjM,EAAM3F,OAAOtG,EAAQ,CACnBsX,YACAsC,YACAa,gBACAvC,gBAEFlY,EAAOE,KAAK,qBACZF,EAAOE,KAAK,mBACRwa,IAAsBd,GACxB5Z,EAAOE,KAAK,oBAEVF,EAAOiG,aAAejG,EAAOiO,OAAO6M,qBACtC9a,EAAOE,KAAK,eAIhB,SAAS6a,EAAoB3O,GAC3B,MAAMpM,EAASlC,KACTmQ,EAASjO,EAAOiO,OAChBkH,EAAQ,eAAE/I,EAAE3L,QAAQ8K,QAAQ,IAAI0C,EAAOrP,YAAc,GAC3D,IAAIoc,GAAa,EACjB,GAAI7F,EACF,IAAK,IAAInW,EAAI,EAAGA,EAAIgB,EAAOY,OAAOzB,OAAQH,GAAK,EACzCgB,EAAOY,OAAO5B,KAAOmW,IAAO6F,GAAa,GAIjD,IAAI7F,IAAS6F,EAUX,OAFAhb,EAAOoB,kBAAeyB,OACtB7C,EAAOiB,kBAAe4B,GARtB7C,EAAOoB,aAAe+T,EAClBnV,EAAOgT,SAAWhT,EAAOiO,OAAO+E,QAAQC,QAC1CjT,EAAOiB,aAAesR,SAAS,eAAE4C,GAAOpL,KAAK,2BAA4B,IAEzE/J,EAAOiB,aAAe,eAAEkU,GAAOrK,QAO/BmD,EAAOgN,0BAA+CpY,IAAxB7C,EAAOiB,cAA8BjB,EAAOiB,eAAiBjB,EAAOkY,aACpGlY,EAAOib,sBAIX,IAAIxV,EAAS,CACXuM,aACAS,eACAmF,mBACAD,qBACAY,uBACAY,iBACAQ,sBACAY,oBACAQ,sBAGF,SAASG,EAAcvO,GAAO7O,KAAKuU,eAAiB,IAAM,MACxD,MAAMrS,EAASlC,MAET,OACJmQ,EAAQ2E,aAAcC,EAAG,UAAE2F,EAAS,WAAE9F,GACpC1S,EAEJ,GAAIiO,EAAOkN,iBACT,OAAOtI,GAAO2F,EAAYA,EAE5B,GAAIvK,EAAOwF,QACT,OAAO+E,EAGT,IAAI4C,EAAmBnP,EAAMiP,aAAaxI,EAAW,GAAI/F,GAGzD,OAFIkG,IAAKuI,GAAoBA,GAEtBA,GAAoB,EAG7B,SAASC,EAAc7C,EAAW8C,GAChC,MAAMtb,EAASlC,MAEb8U,aAAcC,EAAG,OAAE5E,EAAM,WAAEyE,EAAU,UAAE6I,EAAS,SAAErC,GAChDlZ,EACJ,IAAIwb,EAAI,EACJC,EAAI,EACR,MAAMC,EAAI,EAsBV,IAAIC,EApBA3b,EAAOqS,eACTmJ,EAAI3I,GAAO2F,EAAYA,EAEvBiD,EAAIjD,EAGFvK,EAAOgI,eACTuF,EAAI7G,KAAKC,MAAM4G,GACfC,EAAI9G,KAAKC,MAAM6G,IAGbxN,EAAOwF,QACT8H,EAAUvb,EAAOqS,eAAiB,aAAe,aAAerS,EAAOqS,gBAAkBmJ,GAAKC,EACpFxN,EAAOkN,kBACjBzI,EAAWzI,UAAU,eAAeuR,QAAQC,QAAQC,QAEtD1b,EAAO4b,kBAAoB5b,EAAOwY,UAClCxY,EAAOwY,UAAYxY,EAAOqS,eAAiBmJ,EAAIC,EAI/C,MAAMpC,EAAiBrZ,EAAOsZ,eAAiBtZ,EAAO6Y,eAEpD8C,EADqB,IAAnBtC,EACY,GAECb,EAAYxY,EAAO6Y,gBAAkB,EAElD8C,IAAgBzC,GAClBlZ,EAAOmZ,eAAeX,GAGxBxY,EAAOE,KAAK,eAAgBF,EAAOwY,UAAW8C,GAGhD,SAASzC,IACP,OAAS/a,KAAKsV,SAAS,GAGzB,SAASkG,IACP,OAASxb,KAAKsV,SAAStV,KAAKsV,SAASjU,OAAS,GAGhD,SAAS0c,EAAarD,EAAY,EAAGX,EAAQ/Z,KAAKmQ,OAAO4J,MAAOiE,GAAe,EAAMC,GAAkB,EAAMC,GAC3G,MAAMhc,EAASlC,MAET,OACJmQ,EAAM,UACNsN,GACEvb,EAEJ,GAAIA,EAAOic,WAAahO,EAAOiO,+BAC7B,OAAO,EAGT,MAAMrD,EAAe7Y,EAAO6Y,eACtBS,EAAetZ,EAAOsZ,eAC5B,IAAI6C,EAQJ,GAPiDA,EAA7CJ,GAAmBvD,EAAYK,EAA6BA,EACvDkD,GAAmBvD,EAAYc,EAA6BA,EACjDd,EAGpBxY,EAAOmZ,eAAegD,GAElBlO,EAAOwF,QAAS,CAClB,MAAM2I,EAAMpc,EAAOqS,eAcnB,OAbc,IAAVwF,EACF0D,EAAUa,EAAM,aAAe,cAAgBD,EAG3CZ,EAAUc,SACZd,EAAUc,SAAS,CACjB,CAACD,EAAM,OAAS,QAASD,EACzBG,SAAU,WAGZf,EAAUa,EAAM,aAAe,cAAgBD,GAG5C,EAqCT,OAlCc,IAAVtE,GACF7X,EAAOgY,cAAc,GACrBhY,EAAOqb,aAAac,GAChBL,IACF9b,EAAOE,KAAK,wBAAyB2X,EAAOmE,GAC5Chc,EAAOE,KAAK,oBAGdF,EAAOgY,cAAcH,GACrB7X,EAAOqb,aAAac,GAChBL,IACF9b,EAAOE,KAAK,wBAAyB2X,EAAOmE,GAC5Chc,EAAOE,KAAK,oBAETF,EAAOic,YACVjc,EAAOic,WAAY,EACdjc,EAAOuc,oCACVvc,EAAOuc,kCAAoC,SAAuBnQ,GAC3DpM,IAAUA,EAAOyE,WAClB2H,EAAE3L,SAAW3C,OACjBkC,EAAO0S,WAAW,GAAG8J,oBAAoB,gBAAiBxc,EAAOuc,mCACjEvc,EAAO0S,WAAW,GAAG8J,oBAAoB,sBAAuBxc,EAAOuc,mCACvEvc,EAAOuc,kCAAoC,YACpCvc,EAAOuc,kCACVT,GACF9b,EAAOE,KAAK,oBAIlBF,EAAO0S,WAAW,GAAGtO,iBAAiB,gBAAiBpE,EAAOuc,mCAC9Dvc,EAAO0S,WAAW,GAAGtO,iBAAiB,sBAAuBpE,EAAOuc,sCAIjE,EAGT,IAAI/D,EAAY,CACd0C,eACAG,eACAxC,eACAS,eACAuC,eAGF,SAAS7D,EAAeyE,EAAUnB,GAChC,MAAMtb,EAASlC,KAEVkC,EAAOiO,OAAOwF,SACjBzT,EAAO0S,WAAWxI,WAAWuS,GAG/Bzc,EAAOE,KAAK,gBAAiBuc,EAAUnB,GAGzC,SAASoB,EAAiBZ,GAAe,EAAMa,GAC7C,MAAM3c,EAASlC,MACT,YAAEoa,EAAW,OAAEjK,EAAM,cAAEwM,GAAkBza,EAC/C,GAAIiO,EAAOwF,QAAS,OAChBxF,EAAO6K,YACT9Y,EAAO4X,mBAGT,IAAIgF,EAAMD,EASV,GARKC,IAC8BA,EAA7B1E,EAAcuC,EAAqB,OAC9BvC,EAAcuC,EAAqB,OACjC,SAGbza,EAAOE,KAAK,mBAER4b,GAAgB5D,IAAgBuC,EAAe,CACjD,GAAY,UAARmC,EAEF,YADA5c,EAAOE,KAAK,6BAGdF,EAAOE,KAAK,8BACA,SAAR0c,EACF5c,EAAOE,KAAK,4BAEZF,EAAOE,KAAK,6BAKlB,SAASmK,EAAeyR,GAAe,EAAMa,GAC3C,MAAM3c,EAASlC,MACT,YAAEoa,EAAW,cAAEuC,EAAa,OAAExM,GAAWjO,EAE/C,GADAA,EAAOic,WAAY,EACfhO,EAAOwF,QAAS,OACpBzT,EAAOgY,cAAc,GAErB,IAAI4E,EAAMD,EASV,GARKC,IAC8BA,EAA7B1E,EAAcuC,EAAqB,OAC9BvC,EAAcuC,EAAqB,OACjC,SAGbza,EAAOE,KAAK,iBAER4b,GAAgB5D,IAAgBuC,EAAe,CACjD,GAAY,UAARmC,EAEF,YADA5c,EAAOE,KAAK,2BAGdF,EAAOE,KAAK,4BACA,SAAR0c,EACF5c,EAAOE,KAAK,0BAEZF,EAAOE,KAAK,2BAKlB,IAAIgK,EAAa,CACf8N,gBACA0E,kBACArS,iBAGF,SAASwS,EAAS/R,EAAQ,EAAG+M,EAAQ/Z,KAAKmQ,OAAO4J,MAAOiE,GAAe,EAAME,GAC3E,MAAMhc,EAASlC,KACf,IAAI0V,EAAa1I,EACb0I,EAAa,IAAGA,EAAa,GAEjC,MAAM,OACJvF,EAAM,SAAEmF,EAAQ,WAAEC,EAAU,cAAEoH,EAAa,YAAEvC,EAAatF,aAAcC,EAAG,UAAE0I,GAC3Evb,EACJ,GAAIA,EAAOic,WAAahO,EAAOiO,+BAC7B,OAAO,EAGT,MAAMrB,EAAOlG,KAAKgB,IAAI3V,EAAOiO,OAAOyI,mBAAoBlD,GACxD,IAAI8D,EAAYuD,EAAOlG,KAAKC,OAAOpB,EAAaqH,GAAQ7a,EAAOiO,OAAOsH,gBAClE+B,GAAalE,EAASjU,SAAQmY,EAAYlE,EAASjU,OAAS,IAE3D+Y,GAAejK,EAAO6O,cAAgB,MAAQrC,GAAiB,IAAMqB,GACxE9b,EAAOE,KAAK,0BAGd,MAAMsY,GAAapF,EAASkE,GAM5B,GAHAtX,EAAOmZ,eAAeX,GAGlBvK,EAAO2M,oBACT,IAAK,IAAI5b,EAAI,EAAGA,EAAIqU,EAAWlU,OAAQH,GAAK,GACrC2V,KAAKC,MAAkB,IAAZ4D,IAAoB7D,KAAKC,MAAsB,IAAhBvB,EAAWrU,MACxDwU,EAAaxU,GAKnB,GAAIgB,EAAOiG,aAAeuN,IAAe0E,EAAa,CACpD,IAAKlY,EAAO+c,gBAAkBvE,EAAYxY,EAAOwY,WAAaA,EAAYxY,EAAO6Y,eAC/E,OAAO,EAET,IAAK7Y,EAAOgd,gBAAkBxE,EAAYxY,EAAOwY,WAAaA,EAAYxY,EAAOsZ,iBAC1EpB,GAAe,KAAO1E,EAAY,OAAO,EAIlD,IAAImJ,EAOJ,GAN8BA,EAA1BnJ,EAAa0E,EAAyB,OACjC1E,EAAa0E,EAAyB,OAC9B,QAIZrF,IAAQ2F,IAAcxY,EAAOwY,YAAgB3F,GAAO2F,IAAcxY,EAAOwY,UAc5E,OAbAxY,EAAOua,kBAAkB/G,GAErBvF,EAAO6K,YACT9Y,EAAO4X,mBAET5X,EAAO2Z,sBACe,UAAlB1L,EAAO2I,QACT5W,EAAOqb,aAAa7C,GAEJ,UAAdmE,IACF3c,EAAO0c,gBAAgBZ,EAAca,GACrC3c,EAAOqK,cAAcyR,EAAca,KAE9B,EAET,GAAI1O,EAAOwF,QAAS,CAClB,MAAM2I,EAAMpc,EAAOqS,eACnB,IAAI4K,GAAKzE,EAiBT,OAhBI3F,IACFoK,EAAI1B,EAAU2B,YAAc3B,EAAU4B,YAAcF,GAExC,IAAVpF,EACF0D,EAAUa,EAAM,aAAe,aAAea,EAG1C1B,EAAUc,SACZd,EAAUc,SAAS,CACjB,CAACD,EAAM,OAAS,OAAQa,EACxBX,SAAU,WAGZf,EAAUa,EAAM,aAAe,aAAea,GAG3C,EAoCT,OAjCc,IAAVpF,GACF7X,EAAOgY,cAAc,GACrBhY,EAAOqb,aAAa7C,GACpBxY,EAAOua,kBAAkB/G,GACzBxT,EAAO2Z,sBACP3Z,EAAOE,KAAK,wBAAyB2X,EAAOmE,GAC5Chc,EAAO0c,gBAAgBZ,EAAca,GACrC3c,EAAOqK,cAAcyR,EAAca,KAEnC3c,EAAOgY,cAAcH,GACrB7X,EAAOqb,aAAa7C,GACpBxY,EAAOua,kBAAkB/G,GACzBxT,EAAO2Z,sBACP3Z,EAAOE,KAAK,wBAAyB2X,EAAOmE,GAC5Chc,EAAO0c,gBAAgBZ,EAAca,GAChC3c,EAAOic,YACVjc,EAAOic,WAAY,EACdjc,EAAOod,gCACVpd,EAAOod,8BAAgC,SAAuBhR,GACvDpM,IAAUA,EAAOyE,WAClB2H,EAAE3L,SAAW3C,OACjBkC,EAAO0S,WAAW,GAAG8J,oBAAoB,gBAAiBxc,EAAOod,+BACjEpd,EAAO0S,WAAW,GAAG8J,oBAAoB,sBAAuBxc,EAAOod,+BACvEpd,EAAOod,8BAAgC,YAChCpd,EAAOod,8BACdpd,EAAOqK,cAAcyR,EAAca,MAGvC3c,EAAO0S,WAAW,GAAGtO,iBAAiB,gBAAiBpE,EAAOod,+BAC9Dpd,EAAO0S,WAAW,GAAGtO,iBAAiB,sBAAuBpE,EAAOod,kCAIjE,EAGT,SAASC,EAAavS,EAAQ,EAAG+M,EAAQ/Z,KAAKmQ,OAAO4J,MAAOiE,GAAe,EAAME,GAC/E,MAAMhc,EAASlC,KACf,IAAIwf,EAAWxS,EAKf,OAJI9K,EAAOiO,OAAO1I,OAChB+X,GAAYtd,EAAOud,cAGdvd,EAAO6c,QAAQS,EAAUzF,EAAOiE,EAAcE,GAIvD,SAASwB,EAAW3F,EAAQ/Z,KAAKmQ,OAAO4J,MAAOiE,GAAe,EAAME,GAClE,MAAMhc,EAASlC,MACT,OAAEmQ,EAAM,UAAEgO,GAAcjc,EACxByd,EAAYzd,EAAOkY,YAAcjK,EAAOyI,mBAAqB,EAAIzI,EAAOsH,eAC9E,GAAItH,EAAO1I,KAAM,CACf,GAAI0W,EAAW,OAAO,EACtBjc,EAAO0d,UAEP1d,EAAO2d,YAAc3d,EAAO0S,WAAW,GAAGkL,WAE5C,OAAO5d,EAAO6c,QAAQ7c,EAAOkY,YAAcuF,EAAW5F,EAAOiE,EAAcE,GAI7E,SAAS6B,EAAWhG,EAAQ/Z,KAAKmQ,OAAO4J,MAAOiE,GAAe,EAAME,GAClE,MAAMhc,EAASlC,MACT,OACJmQ,EAAM,UAAEgO,EAAS,SAAE7I,EAAQ,WAAEC,EAAU,aAAET,GACvC5S,EAEJ,GAAIiO,EAAO1I,KAAM,CACf,GAAI0W,EAAW,OAAO,EACtBjc,EAAO0d,UAEP1d,EAAO2d,YAAc3d,EAAO0S,WAAW,GAAGkL,WAE5C,MAAMpF,EAAY5F,EAAe5S,EAAOwY,WAAaxY,EAAOwY,UAC5D,SAASsF,EAAUC,GACjB,OAAIA,EAAM,GAAWpJ,KAAKC,MAAMD,KAAK8B,IAAIsH,IAClCpJ,KAAKC,MAAMmJ,GAEpB,MAAMC,EAAsBF,EAAUtF,GAChCyF,EAAqB7K,EAAShG,IAAK2Q,GAAQD,EAAUC,IAC9B1K,EAAWjG,IAAK2Q,GAAQD,EAAUC,IAE3C3K,EAAS6K,EAAmB9Z,QAAQ6Z,IACxD,IAMIE,EANAC,EAAW/K,EAAS6K,EAAmB9Z,QAAQ6Z,GAAuB,GAW1E,MAVwB,qBAAbG,GAA4BlQ,EAAOwF,SAC5CL,EAASzR,QAASwV,KACXgH,GAAYH,GAAuB7G,IAAMgH,EAAWhH,KAIrC,qBAAbgH,IACTD,EAAY7K,EAAWlP,QAAQga,GAC3BD,EAAY,IAAGA,EAAYle,EAAOkY,YAAc,IAE/ClY,EAAO6c,QAAQqB,EAAWrG,EAAOiE,EAAcE,GAIxD,SAASoC,EAAYvG,EAAQ/Z,KAAKmQ,OAAO4J,MAAOiE,GAAe,EAAME,GACnE,MAAMhc,EAASlC,KACf,OAAOkC,EAAO6c,QAAQ7c,EAAOkY,YAAaL,EAAOiE,EAAcE,GAIjE,SAASqC,EAAgBxG,EAAQ/Z,KAAKmQ,OAAO4J,MAAOiE,GAAe,EAAME,EAAUsC,EAAY,IAC7F,MAAMte,EAASlC,KACf,IAAIgN,EAAQ9K,EAAOkY,YACnB,MAAM2C,EAAOlG,KAAKgB,IAAI3V,EAAOiO,OAAOyI,mBAAoB5L,GAClDwM,EAAYuD,EAAOlG,KAAKC,OAAO9J,EAAQ+P,GAAQ7a,EAAOiO,OAAOsH,gBAE7DiD,EAAYxY,EAAO4S,aAAe5S,EAAOwY,WAAaxY,EAAOwY,UAEnE,GAAIA,GAAaxY,EAAOoT,SAASkE,GAAY,CAG3C,MAAMiH,EAAcve,EAAOoT,SAASkE,GAC9BkH,EAAWxe,EAAOoT,SAASkE,EAAY,GACxCkB,EAAY+F,GAAgBC,EAAWD,GAAeD,IACzDxT,GAAS9K,EAAOiO,OAAOsH,oBAEpB,CAGL,MAAM4I,EAAWne,EAAOoT,SAASkE,EAAY,GACvCiH,EAAcve,EAAOoT,SAASkE,GAC/BkB,EAAY2F,IAAcI,EAAcJ,GAAYG,IACvDxT,GAAS9K,EAAOiO,OAAOsH,gBAM3B,OAHAzK,EAAQ6J,KAAKK,IAAIlK,EAAO,GACxBA,EAAQ6J,KAAKgB,IAAI7K,EAAO9K,EAAOqT,WAAWlU,OAAS,GAE5Ca,EAAO6c,QAAQ/R,EAAO+M,EAAOiE,EAAcE,GAGpD,SAASf,IACP,MAAMjb,EAASlC,MACT,OAAEmQ,EAAM,WAAEyE,GAAe1S,EAEzB8U,EAAyC,SAAzB7G,EAAO6G,cAA2B9U,EAAOye,uBAAyBxQ,EAAO6G,cAC/F,IACI8E,EADA8E,EAAe1e,EAAOiB,aAE1B,GAAIgN,EAAO1I,KAAM,CACf,GAAIvF,EAAOic,UAAW,OACtBrC,EAAYrH,SAAS,eAAEvS,EAAOoB,cAAc2I,KAAK,2BAA4B,IACzEkE,EAAOuI,eAENkI,EAAe1e,EAAOud,aAAgBzI,EAAgB,GACnD4J,EAAgB1e,EAAOY,OAAOzB,OAASa,EAAOud,aAAiBzI,EAAgB,GAEnF9U,EAAO0d,UACPgB,EAAehM,EACZjH,SAAS,IAAIwC,EAAOrP,uCAAuCgb,YAAoB3L,EAAOmM,wBACtFrP,GAAG,GACHD,QAEHmB,EAAM0S,SAAS,KACb3e,EAAO6c,QAAQ6B,MAGjB1e,EAAO6c,QAAQ6B,GAERA,EAAe1e,EAAOY,OAAOzB,OAAS2V,GAC/C9U,EAAO0d,UACPgB,EAAehM,EACZjH,SAAS,IAAIwC,EAAOrP,uCAAuCgb,YAAoB3L,EAAOmM,wBACtFrP,GAAG,GACHD,QAEHmB,EAAM0S,SAAS,KACb3e,EAAO6c,QAAQ6B,MAGjB1e,EAAO6c,QAAQ6B,QAGjB1e,EAAO6c,QAAQ6B,GAInB,IAAIvJ,EAAQ,CACV0H,UACAQ,cACAG,YACAK,YACAO,aACAC,iBACApD,uBAGF,SAASpV,IACP,MAAM7F,EAASlC,MACT,OAAEmQ,EAAM,WAAEyE,GAAe1S,EAE/B0S,EAAWjH,SAAS,IAAIwC,EAAOrP,cAAcqP,EAAOmM,uBAAuBzO,SAE3E,IAAI/K,EAAS8R,EAAWjH,SAAS,IAAIwC,EAAOrP,YAE5C,GAAIqP,EAAO2Q,uBAAwB,CACjC,MAAMC,EAAiB5Q,EAAOsH,eAAkB3U,EAAOzB,OAAS8O,EAAOsH,eACvE,GAAIsJ,IAAmB5Q,EAAOsH,eAAgB,CAC5C,IAAK,IAAIvW,EAAI,EAAGA,EAAI6f,EAAgB7f,GAAK,EAAG,CAC1C,MAAM8f,EAAY,eAAE,OAAW7W,cAAc,QAAQ0B,SAAS,GAAGsE,EAAOrP,cAAcqP,EAAO8Q,mBAC7FrM,EAAW1H,OAAO8T,GAEpBle,EAAS8R,EAAWjH,SAAS,IAAIwC,EAAOrP,aAIf,SAAzBqP,EAAO6G,eAA6B7G,EAAOsP,eAActP,EAAOsP,aAAe3c,EAAOzB,QAE1Fa,EAAOud,aAAe5I,KAAKE,KAAKhH,WAAWI,EAAOsP,cAAgBtP,EAAO6G,cAAe,KACxF9U,EAAOud,cAAgBtP,EAAO+Q,qBAC1Bhf,EAAOud,aAAe3c,EAAOzB,SAC/Ba,EAAOud,aAAe3c,EAAOzB,QAG/B,MAAM8f,EAAgB,GAChBC,EAAe,GACrBte,EAAO8J,KAAK,CAACI,EAAO4B,KAClB,MAAMyI,EAAQ,eAAEzI,GACZ5B,EAAQ9K,EAAOud,cAAc2B,EAAarO,KAAKnE,GAC/C5B,EAAQlK,EAAOzB,QAAU2L,GAASlK,EAAOzB,OAASa,EAAOud,cAAc0B,EAAcpO,KAAKnE,GAC9FyI,EAAMpL,KAAK,0BAA2Be,KAExC,IAAK,IAAI9L,EAAI,EAAGA,EAAIkgB,EAAa/f,OAAQH,GAAK,EAC5C0T,EAAW1H,OAAO,eAAEkU,EAAalgB,GAAGmgB,WAAU,IAAOxV,SAASsE,EAAOmM,sBAEvE,IAAK,IAAIpb,EAAIigB,EAAc9f,OAAS,EAAGH,GAAK,EAAGA,GAAK,EAClD0T,EAAWzH,QAAQ,eAAEgU,EAAcjgB,GAAGmgB,WAAU,IAAOxV,SAASsE,EAAOmM,sBAI3E,SAASsD,IACP,MAAM1d,EAASlC,KAEfkC,EAAOE,KAAK,iBAEZ,MAAM,YACJgY,EAAW,OAAEtX,EAAM,aAAE2c,EAAY,eAAEP,EAAc,eAAED,EAAc,SAAE3J,EAAUR,aAAcC,GACzF7S,EACJ,IAAIsd,EACJtd,EAAOgd,gBAAiB,EACxBhd,EAAO+c,gBAAiB,EAExB,MAAMqC,GAAiBhM,EAAS8E,GAC1BmH,EAAOD,EAAgBpf,EAAOkb,eAGpC,GAAIhD,EAAcqF,EAAc,CAC9BD,EAAY1c,EAAOzB,OAAyB,EAAfoe,EAAqBrF,EAClDoF,GAAYC,EACZ,MAAM+B,EAAetf,EAAO6c,QAAQS,EAAU,GAAG,GAAO,GACpDgC,GAAyB,IAATD,GAClBrf,EAAOqb,cAAcxI,GAAO7S,EAAOwY,UAAYxY,EAAOwY,WAAa6G,QAEhE,GAAInH,GAAetX,EAAOzB,OAASoe,EAAc,CAEtDD,GAAY1c,EAAOzB,OAAS+Y,EAAcqF,EAC1CD,GAAYC,EACZ,MAAM+B,EAAetf,EAAO6c,QAAQS,EAAU,GAAG,GAAO,GACpDgC,GAAyB,IAATD,GAClBrf,EAAOqb,cAAcxI,GAAO7S,EAAOwY,UAAYxY,EAAOwY,WAAa6G,GAGvErf,EAAOgd,eAAiBA,EACxBhd,EAAO+c,eAAiBA,EAExB/c,EAAOE,KAAK,WAGd,SAASsF,IACP,MAAMxF,EAASlC,MACT,WAAE4U,EAAU,OAAEzE,EAAM,OAAErN,GAAWZ,EACvC0S,EAAWjH,SAAS,IAAIwC,EAAOrP,cAAcqP,EAAOmM,wBAAwBnM,EAAOrP,cAAcqP,EAAO8Q,mBAAmBpT,SAC3H/K,EAAOoJ,WAAW,2BAGpB,IAAIzE,EAAO,CACTM,aACA6X,UACAlY,eAGF,SAAS+Z,EAAeC,GACtB,MAAMxf,EAASlC,KACf,GAAIsR,EAAQC,QAAUrP,EAAOiO,OAAOwR,eAAkBzf,EAAOiO,OAAOsJ,eAAiBvX,EAAO0f,UAAa1f,EAAOiO,OAAOwF,QAAS,OAChI,MAAM/G,EAAK1M,EAAO0M,GAClBA,EAAGqJ,MAAM4J,OAAS,OAClBjT,EAAGqJ,MAAM4J,OAASH,EAAS,mBAAqB,eAChD9S,EAAGqJ,MAAM4J,OAASH,EAAS,eAAiB,YAC5C9S,EAAGqJ,MAAM4J,OAASH,EAAS,WAAa,OAG1C,SAASI,IACP,MAAM5f,EAASlC,KACXsR,EAAQC,OAAUrP,EAAOiO,OAAOsJ,eAAiBvX,EAAO0f,UAAa1f,EAAOiO,OAAOwF,UACvFzT,EAAO0M,GAAGqJ,MAAM4J,OAAS,IAG3B,IAAIE,EAAa,CACfN,gBACAK,mBAGF,SAASE,EAAalf,GACpB,MAAMZ,EAASlC,MACT,WAAE4U,EAAU,OAAEzE,GAAWjO,EAI/B,GAHIiO,EAAO1I,MACTvF,EAAOwF,cAEa,kBAAX5E,GAAuB,WAAYA,EAC5C,IAAK,IAAI5B,EAAI,EAAGA,EAAI4B,EAAOzB,OAAQH,GAAK,EAClC4B,EAAO5B,IAAI0T,EAAW1H,OAAOpK,EAAO5B,SAG1C0T,EAAW1H,OAAOpK,GAEhBqN,EAAO1I,MACTvF,EAAO6F,aAEHoI,EAAO0B,UAAYP,EAAQO,UAC/B3P,EAAOyF,SAIX,SAASsa,EAAcnf,GACrB,MAAMZ,EAASlC,MACT,OAAEmQ,EAAM,WAAEyE,EAAU,YAAEwF,GAAgBlY,EAExCiO,EAAO1I,MACTvF,EAAOwF,cAET,IAAIgV,EAAiBtC,EAAc,EACnC,GAAsB,kBAAXtX,GAAuB,WAAYA,EAAQ,CACpD,IAAK,IAAI5B,EAAI,EAAGA,EAAI4B,EAAOzB,OAAQH,GAAK,EAClC4B,EAAO5B,IAAI0T,EAAWzH,QAAQrK,EAAO5B,IAE3Cwb,EAAiBtC,EAActX,EAAOzB,YAEtCuT,EAAWzH,QAAQrK,GAEjBqN,EAAO1I,MACTvF,EAAO6F,aAEHoI,EAAO0B,UAAYP,EAAQO,UAC/B3P,EAAOyF,SAETzF,EAAO6c,QAAQrC,EAAgB,GAAG,GAGpC,SAASwF,EAAUlV,EAAOlK,GACxB,MAAMZ,EAASlC,MACT,WAAE4U,EAAU,OAAEzE,EAAM,YAAEiK,GAAgBlY,EAC5C,IAAIigB,EAAoB/H,EACpBjK,EAAO1I,OACT0a,GAAqBjgB,EAAOud,aAC5Bvd,EAAOwF,cACPxF,EAAOY,OAAS8R,EAAWjH,SAAS,IAAIwC,EAAOrP,aAEjD,MAAMshB,EAAalgB,EAAOY,OAAOzB,OACjC,GAAI2L,GAAS,EAEX,YADA9K,EAAO+f,aAAanf,GAGtB,GAAIkK,GAASoV,EAEX,YADAlgB,EAAO8f,YAAYlf,GAGrB,IAAI4Z,EAAiByF,EAAoBnV,EAAQmV,EAAoB,EAAIA,EAEzE,MAAME,EAAe,GACrB,IAAK,IAAInhB,EAAIkhB,EAAa,EAAGlhB,GAAK8L,EAAO9L,GAAK,EAAG,CAC/C,MAAMohB,EAAepgB,EAAOY,OAAOmK,GAAG/L,GACtCohB,EAAazU,SACbwU,EAAaE,QAAQD,GAGvB,GAAsB,kBAAXxf,GAAuB,WAAYA,EAAQ,CACpD,IAAK,IAAI5B,EAAI,EAAGA,EAAI4B,EAAOzB,OAAQH,GAAK,EAClC4B,EAAO5B,IAAI0T,EAAW1H,OAAOpK,EAAO5B,IAE1Cwb,EAAiByF,EAAoBnV,EAAQmV,EAAoBrf,EAAOzB,OAAS8gB,OAEjFvN,EAAW1H,OAAOpK,GAGpB,IAAK,IAAI5B,EAAI,EAAGA,EAAImhB,EAAahhB,OAAQH,GAAK,EAC5C0T,EAAW1H,OAAOmV,EAAanhB,IAG7BiP,EAAO1I,MACTvF,EAAO6F,aAEHoI,EAAO0B,UAAYP,EAAQO,UAC/B3P,EAAOyF,SAELwI,EAAO1I,KACTvF,EAAO6c,QAAQrC,EAAiBxa,EAAOud,aAAc,GAAG,GAExDvd,EAAO6c,QAAQrC,EAAgB,GAAG,GAItC,SAAS8F,EAAaC,GACpB,MAAMvgB,EAASlC,MACT,OAAEmQ,EAAM,WAAEyE,EAAU,YAAEwF,GAAgBlY,EAE5C,IAAIigB,EAAoB/H,EACpBjK,EAAO1I,OACT0a,GAAqBjgB,EAAOud,aAC5Bvd,EAAOwF,cACPxF,EAAOY,OAAS8R,EAAWjH,SAAS,IAAIwC,EAAOrP,aAEjD,IACI4hB,EADAhG,EAAiByF,EAGrB,GAA6B,kBAAlBM,GAA8B,WAAYA,EAAe,CAClE,IAAK,IAAIvhB,EAAI,EAAGA,EAAIuhB,EAAcphB,OAAQH,GAAK,EAC7CwhB,EAAgBD,EAAcvhB,GAC1BgB,EAAOY,OAAO4f,IAAgBxgB,EAAOY,OAAOmK,GAAGyV,GAAe7U,SAC9D6U,EAAgBhG,IAAgBA,GAAkB,GAExDA,EAAiB7F,KAAKK,IAAIwF,EAAgB,QAE1CgG,EAAgBD,EACZvgB,EAAOY,OAAO4f,IAAgBxgB,EAAOY,OAAOmK,GAAGyV,GAAe7U,SAC9D6U,EAAgBhG,IAAgBA,GAAkB,GACtDA,EAAiB7F,KAAKK,IAAIwF,EAAgB,GAGxCvM,EAAO1I,MACTvF,EAAO6F,aAGHoI,EAAO0B,UAAYP,EAAQO,UAC/B3P,EAAOyF,SAELwI,EAAO1I,KACTvF,EAAO6c,QAAQrC,EAAiBxa,EAAOud,aAAc,GAAG,GAExDvd,EAAO6c,QAAQrC,EAAgB,GAAG,GAItC,SAASiG,IACP,MAAMzgB,EAASlC,KAETyiB,EAAgB,GACtB,IAAK,IAAIvhB,EAAI,EAAGA,EAAIgB,EAAOY,OAAOzB,OAAQH,GAAK,EAC7CuhB,EAAc1P,KAAK7R,GAErBgB,EAAOsgB,YAAYC,GAGrB,IAAIG,EAAe,CACjBZ,cACAC,eACAC,WACAM,cACAG,mBAGF,MAAME,EAAU,WACd,MAAMC,EAAW,OAAOnR,UAAUmR,SAC5BC,EAAK,OAAOpR,UAAUqR,UAEtBC,EAAS,CACbC,KAAK,EACLC,SAAS,EACTC,eAAe,EACfC,SAAS,EACTC,QAAQ,EACRC,MAAM,EACNC,MAAM,EACNC,MAAM,EACNC,IAAI,EACJC,SAAS,EACTC,OAAO,EACPC,SAAS,EACTC,WAAY,OAAOA,UAAW,OAAOC,UACrCA,YAAa,OAAOD,UAAW,OAAOC,UACtCC,UAAU,GAGNC,EAAc,OAAOC,OAAO/P,MAC5BgQ,EAAe,OAAOD,OAAO9P,OAE7B+O,EAAUJ,EAAGqB,MAAM,+BACzB,IAAIZ,EAAOT,EAAGqB,MAAM,wBACpB,MAAMb,EAAOR,EAAGqB,MAAM,2BAChBd,GAAUE,GAAQT,EAAGqB,MAAM,8BAC3BV,EAAKX,EAAG1c,QAAQ,UAAY,GAAK0c,EAAG1c,QAAQ,aAAe,EAC3Dod,EAAOV,EAAG1c,QAAQ,UAAY,EAC9Bsd,EAAUZ,EAAG1c,QAAQ,WAAa,GAAK0c,EAAG1c,QAAQ,aAAe,EACjEwd,EAAuB,UAAbf,EACVkB,EAAWjB,EAAG/gB,cAAcqE,QAAQ,aAAe,EACzD,IAAIud,EAAqB,aAAbd,EA4EZ,OAzEKU,GACAI,GACAtS,EAAQC,QAEQ,OAAhB0S,GAAyC,OAAjBE,GACL,MAAhBF,GAAwC,OAAjBE,GACP,MAAhBF,GAAwC,OAAjBE,GACP,MAAhBF,GAAwC,OAAjBE,KAG7BX,EAAOT,EAAGqB,MAAM,uBAChBR,GAAQ,GAGVX,EAAOS,GAAKA,EACZT,EAAOQ,KAAOA,EACdR,EAAOU,QAAUA,EAGbR,IAAYU,IACdZ,EAAOoB,GAAK,UACZpB,EAAOqB,UAAYnB,EAAQ,GAC3BF,EAAOE,SAAU,EACjBF,EAAOG,cAAgBL,EAAG/gB,cAAcqE,QAAQ,WAAa,IAE3Dmd,GAAQF,GAAUC,KACpBN,EAAOoB,GAAK,MACZpB,EAAOC,KAAM,GAGXI,IAAWC,IACbN,EAAOqB,UAAYhB,EAAO,GAAGvhB,QAAQ,KAAM,KAC3CkhB,EAAOK,QAAS,GAEdE,IACFP,EAAOqB,UAAYd,EAAK,GAAGzhB,QAAQ,KAAM,KACzCkhB,EAAOO,MAAO,GAEZD,IACFN,EAAOqB,UAAYf,EAAK,GAAKA,EAAK,GAAGxhB,QAAQ,KAAM,KAAO,KAC1DkhB,EAAOM,MAAO,GAGZN,EAAOC,KAAOD,EAAOqB,WAAavB,EAAG1c,QAAQ,aAAe,GACvB,OAAnC4c,EAAOqB,UAAUjV,MAAM,KAAK,KAC9B4T,EAAOqB,UAAYvB,EAAG/gB,cAAcqN,MAAM,YAAY,GAAGA,MAAM,KAAK,IAKxE4T,EAAOsB,YAAcjB,GAAUE,GAAQD,KAAUR,EAAGqB,MAAM,gCAAiC,OAAOzS,UAAU6S,aACtG,OAAOC,YAAc,OAAOA,WAAW,8BAA8BC,QAC3EzB,EAAO0B,QAAU1B,EAAOsB,QACxBtB,EAAOuB,WAAavB,EAAOsB,QAG3BtB,EAAOI,UAAYJ,EAAOC,KAAOD,EAAOE,UAAYa,EAChDf,EAAOI,UACTJ,EAAOe,SAAWA,EAClBf,EAAOW,MAAQA,EACfX,EAAOY,QAAUA,EACbZ,EAAOW,QACTX,EAAOoB,GAAK,SAEVpB,EAAOY,UACTZ,EAAOoB,GAAK,YAKhBpB,EAAO2B,WAAa,OAAOC,kBAAoB,EAGxC5B,EA9GM,GAiHf,SAAS6B,EAAc3iB,GACrB,MAAMD,EAASlC,KACT6E,EAAO3C,EAAO6iB,iBACd,OAAE5U,EAAM,QAAE6U,GAAY9iB,EAE5B,GAAIA,EAAOic,WAAahO,EAAOiO,+BAC7B,OAEF,IAAI9P,EAAInM,EACJmM,EAAE2W,gBAAe3W,EAAIA,EAAE2W,eAC3B,MAAMC,EAAY,eAAE5W,EAAE3L,QAEtB,GAAiC,YAA7BwN,EAAOgV,oBACJD,EAAUzX,QAAQvL,EAAOub,WAAWpc,OAAQ,OAGnD,GADAwD,EAAKugB,aAA0B,eAAX9W,EAAE1F,MACjB/D,EAAKugB,cAAgB,UAAW9W,GAAiB,IAAZA,EAAE+W,MAAa,OACzD,IAAKxgB,EAAKugB,cAAgB,WAAY9W,GAAKA,EAAEgX,OAAS,EAAG,OACzD,GAAIzgB,EAAK0gB,WAAa1gB,EAAK2gB,QAAS,OACpC,GAAIrV,EAAOsV,WAAaP,EAAUzX,QAAQ0C,EAAOuV,kBAAoBvV,EAAOuV,kBAAoB,IAAIvV,EAAOwV,gBAAkB,GAE3H,YADAzjB,EAAO0jB,YAAa,GAGtB,GAAIzV,EAAO0V,eACJX,EAAUzX,QAAQ0C,EAAO0V,cAAc,GAAI,OAGlDb,EAAQc,SAAsB,eAAXxX,EAAE1F,KAAwB0F,EAAEyX,cAAc,GAAGC,MAAQ1X,EAAE0X,MAC1EhB,EAAQiB,SAAsB,eAAX3X,EAAE1F,KAAwB0F,EAAEyX,cAAc,GAAGG,MAAQ5X,EAAE4X,MAC1E,MAAMC,EAASnB,EAAQc,SACjBM,EAASpB,EAAQiB,SAIjBI,EAAqBlW,EAAOkW,oBAAsBlW,EAAOmW,sBACzDC,EAAqBpW,EAAOoW,oBAAsBpW,EAAOqW,sBAC/D,IACEH,KACKF,GAAUI,GACXJ,GAAU,OAAOjC,OAAO/P,MAAQoS,GAHtC,CAuBA,GAfApY,EAAM3F,OAAO3D,EAAM,CACjB0gB,WAAW,EACXC,SAAS,EACTiB,qBAAqB,EACrBC,iBAAa3hB,EACb4hB,iBAAa5hB,IAGfigB,EAAQmB,OAASA,EACjBnB,EAAQoB,OAASA,EACjBvhB,EAAK+hB,eAAiBzY,EAAMQ,MAC5BzM,EAAO0jB,YAAa,EACpB1jB,EAAOgS,aACPhS,EAAO2kB,oBAAiB9hB,EACpBoL,EAAOqQ,UAAY,IAAG3b,EAAKiiB,oBAAqB,GACrC,eAAXxY,EAAE1F,KAAuB,CAC3B,IAAIme,GAAiB,EACjB7B,EAAUnY,GAAGlI,EAAKmiB,gBAAeD,GAAiB,GAEpD,OAAWE,eACR,eAAE,OAAWA,eAAela,GAAGlI,EAAKmiB,eACpC,OAAWC,gBAAkB/B,EAAU,IAE1C,OAAW+B,cAAcC,OAG3B,MAAMC,EAAuBJ,GAAkB7kB,EAAOklB,gBAAkBjX,EAAOkX,0BAC3ElX,EAAOmX,+BAAiCH,IAC1C7Y,EAAEyY,iBAGN7kB,EAAOE,KAAK,aAAckM,IAG5B,SAASiZ,EAAaplB,GACpB,MAAMD,EAASlC,KACT6E,EAAO3C,EAAO6iB,iBACd,OAAE5U,EAAM,QAAE6U,EAASlQ,aAAcC,GAAQ7S,EAC/C,IAAIoM,EAAInM,EAER,GADImM,EAAE2W,gBAAe3W,EAAIA,EAAE2W,gBACtBpgB,EAAK0gB,UAIR,YAHI1gB,EAAK8hB,aAAe9hB,EAAK6hB,aAC3BxkB,EAAOE,KAAK,oBAAqBkM,IAIrC,GAAIzJ,EAAKugB,cAA2B,cAAX9W,EAAE1F,KAAsB,OACjD,MAAM4e,EAAyB,cAAXlZ,EAAE1F,MAAwB0F,EAAEyX,gBAAkBzX,EAAEyX,cAAc,IAAMzX,EAAEmZ,eAAe,IACnGzB,EAAmB,cAAX1X,EAAE1F,KAAuB4e,EAAYxB,MAAQ1X,EAAE0X,MACvDE,EAAmB,cAAX5X,EAAE1F,KAAuB4e,EAAYtB,MAAQ5X,EAAE4X,MAC7D,GAAI5X,EAAEoZ,wBAGJ,OAFA1C,EAAQmB,OAASH,OACjBhB,EAAQoB,OAASF,GAGnB,IAAKhkB,EAAOklB,eAYV,OAVAllB,EAAO0jB,YAAa,OAChB/gB,EAAK0gB,YACPpX,EAAM3F,OAAOwc,EAAS,CACpBmB,OAAQH,EACRI,OAAQF,EACRJ,SAAUE,EACVC,SAAUC,IAEZrhB,EAAK+hB,eAAiBzY,EAAMQ,QAIhC,GAAI9J,EAAKugB,cAAgBjV,EAAOwX,sBAAwBxX,EAAO1I,KAC7D,GAAIvF,EAAOsS,cAET,GACG0R,EAAQlB,EAAQoB,QAAUlkB,EAAOwY,WAAaxY,EAAOsZ,gBAClD0K,EAAQlB,EAAQoB,QAAUlkB,EAAOwY,WAAaxY,EAAO6Y,eAIzD,OAFAlW,EAAK0gB,WAAY,OACjB1gB,EAAK2gB,SAAU,QAGZ,GACJQ,EAAQhB,EAAQmB,QAAUjkB,EAAOwY,WAAaxY,EAAOsZ,gBAClDwK,EAAQhB,EAAQmB,QAAUjkB,EAAOwY,WAAaxY,EAAO6Y,eAEzD,OAGJ,GAAIlW,EAAKugB,cAAgB,OAAW6B,eAC9B3Y,EAAE3L,SAAW,OAAWskB,eAAiB,eAAE3Y,EAAE3L,QAAQoK,GAAGlI,EAAKmiB,cAG/D,OAFAniB,EAAK2gB,SAAU,OACftjB,EAAO0jB,YAAa,GAOxB,GAHI/gB,EAAK4hB,qBACPvkB,EAAOE,KAAK,YAAakM,GAEvBA,EAAEyX,eAAiBzX,EAAEyX,cAAc1kB,OAAS,EAAG,OAEnD2jB,EAAQc,SAAWE,EACnBhB,EAAQiB,SAAWC,EAEnB,MAAM0B,EAAQ5C,EAAQc,SAAWd,EAAQmB,OACnC0B,EAAQ7C,EAAQiB,SAAWjB,EAAQoB,OACzC,GAAIlkB,EAAOiO,OAAOqQ,WAAa3J,KAAKiR,KAAMF,GAAS,EAAMC,GAAS,GAAM3lB,EAAOiO,OAAOqQ,UAAW,OAEjG,GAAgC,qBAArB3b,EAAK6hB,YAA6B,CAC3C,IAAIqB,EACC7lB,EAAOqS,gBAAkByQ,EAAQiB,WAAajB,EAAQoB,QAAYlkB,EAAOsS,cAAgBwQ,EAAQc,WAAad,EAAQmB,OACzHthB,EAAK6hB,aAAc,EAGdkB,EAAQA,EAAUC,EAAQA,GAAU,KACvCE,EAA6D,IAA/ClR,KAAKmR,MAAMnR,KAAK8B,IAAIkP,GAAQhR,KAAK8B,IAAIiP,IAAiB/Q,KAAKoR,GACzEpjB,EAAK6hB,YAAcxkB,EAAOqS,eAAiBwT,EAAa5X,EAAO4X,WAAc,GAAKA,EAAa5X,EAAO4X,YAY5G,GARIljB,EAAK6hB,aACPxkB,EAAOE,KAAK,oBAAqBkM,GAEH,qBAArBzJ,EAAK8hB,cACV3B,EAAQc,WAAad,EAAQmB,QAAUnB,EAAQiB,WAAajB,EAAQoB,SACtEvhB,EAAK8hB,aAAc,IAGnB9hB,EAAK6hB,YAEP,YADA7hB,EAAK0gB,WAAY,GAGnB,IAAK1gB,EAAK8hB,YACR,OAEFzkB,EAAO0jB,YAAa,GACfzV,EAAOwF,SAAWrH,EAAE4Z,YACvB5Z,EAAEyY,iBAEA5W,EAAOgY,2BAA6BhY,EAAOiY,QAC7C9Z,EAAE+Z,kBAGCxjB,EAAK2gB,UACJrV,EAAO1I,MACTvF,EAAO0d,UAET/a,EAAKyjB,eAAiBpmB,EAAOkb,eAC7Blb,EAAOgY,cAAc,GACjBhY,EAAOic,WACTjc,EAAO0S,WAAWtI,QAAQ,qCAE5BzH,EAAK0jB,qBAAsB,GAEvBpY,EAAO4R,aAAyC,IAA1B7f,EAAO+c,iBAAqD,IAA1B/c,EAAOgd,gBACjEhd,EAAOuf,eAAc,GAEvBvf,EAAOE,KAAK,kBAAmBkM,IAEjCpM,EAAOE,KAAK,aAAckM,GAC1BzJ,EAAK2gB,SAAU,EAEf,IAAIjE,EAAOrf,EAAOqS,eAAiBqT,EAAQC,EAC3C7C,EAAQzD,KAAOA,EAEfA,GAAQpR,EAAOqY,WACXzT,IAAKwM,GAAQA,GAEjBrf,EAAO2kB,eAAiBtF,EAAO,EAAI,OAAS,OAC5C1c,EAAKyY,iBAAmBiE,EAAO1c,EAAKyjB,eAEpC,IAAIG,GAAsB,EACtBC,EAAkBvY,EAAOuY,gBA0B7B,GAzBIvY,EAAOwX,sBACTe,EAAkB,GAEfnH,EAAO,GAAK1c,EAAKyY,iBAAmBpb,EAAO6Y,gBAC9C0N,GAAsB,EAClBtY,EAAOwY,aAAY9jB,EAAKyY,iBAAoBpb,EAAO6Y,eAAiB,IAAQ7Y,EAAO6Y,eAAiBlW,EAAKyjB,eAAiB/G,IAASmH,IAC9HnH,EAAO,GAAK1c,EAAKyY,iBAAmBpb,EAAOsZ,iBACpDiN,GAAsB,EAClBtY,EAAOwY,aAAY9jB,EAAKyY,iBAAoBpb,EAAOsZ,eAAiB,GAAOtZ,EAAOsZ,eAAiB3W,EAAKyjB,eAAiB/G,IAASmH,IAGpID,IACFna,EAAEoZ,yBAA0B,IAIzBxlB,EAAO+c,gBAA4C,SAA1B/c,EAAO2kB,gBAA6BhiB,EAAKyY,iBAAmBzY,EAAKyjB,iBAC7FzjB,EAAKyY,iBAAmBzY,EAAKyjB,iBAE1BpmB,EAAOgd,gBAA4C,SAA1Bhd,EAAO2kB,gBAA6BhiB,EAAKyY,iBAAmBzY,EAAKyjB,iBAC7FzjB,EAAKyY,iBAAmBzY,EAAKyjB,gBAK3BnY,EAAOqQ,UAAY,EAAG,CACxB,KAAI3J,KAAK8B,IAAI4I,GAAQpR,EAAOqQ,WAAa3b,EAAKiiB,oBAW5C,YADAjiB,EAAKyY,iBAAmBzY,EAAKyjB,gBAT7B,IAAKzjB,EAAKiiB,mBAMR,OALAjiB,EAAKiiB,oBAAqB,EAC1B9B,EAAQmB,OAASnB,EAAQc,SACzBd,EAAQoB,OAASpB,EAAQiB,SACzBphB,EAAKyY,iBAAmBzY,EAAKyjB,oBAC7BtD,EAAQzD,KAAOrf,EAAOqS,eAAiByQ,EAAQc,SAAWd,EAAQmB,OAASnB,EAAQiB,SAAWjB,EAAQoB,QASvGjW,EAAOyY,eAAgBzY,EAAOwF,WAG/BxF,EAAO0Y,UAAY1Y,EAAOwJ,qBAAuBxJ,EAAOyJ,yBAC1D1X,EAAOua,oBACPva,EAAO2Z,uBAEL1L,EAAO0Y,WAEsB,IAA3BhkB,EAAKikB,WAAWznB,QAClBwD,EAAKikB,WAAW/V,KAAK,CACnBgW,SAAU/D,EAAQ9iB,EAAOqS,eAAiB,SAAW,UACrDyU,KAAMnkB,EAAK+hB,iBAGf/hB,EAAKikB,WAAW/V,KAAK,CACnBgW,SAAU/D,EAAQ9iB,EAAOqS,eAAiB,WAAa,YACvDyU,KAAM7a,EAAMQ,SAIhBzM,EAAOmZ,eAAexW,EAAKyY,kBAE3Bpb,EAAOqb,aAAa1Y,EAAKyY,mBAG3B,SAAS2L,EAAY9mB,GACnB,MAAMD,EAASlC,KACT6E,EAAO3C,EAAO6iB,iBAEd,OACJ5U,EAAM,QAAE6U,EAASlQ,aAAcC,EAAG,WAAEH,EAAU,WAAEW,EAAU,SAAED,GAC1DpT,EACJ,IAAIoM,EAAInM,EAMR,GALImM,EAAE2W,gBAAe3W,EAAIA,EAAE2W,eACvBpgB,EAAK4hB,qBACPvkB,EAAOE,KAAK,WAAYkM,GAE1BzJ,EAAK4hB,qBAAsB,GACtB5hB,EAAK0gB,UAMR,OALI1gB,EAAK2gB,SAAWrV,EAAO4R,YACzB7f,EAAOuf,eAAc,GAEvB5c,EAAK2gB,SAAU,OACf3gB,EAAK8hB,aAAc,GAIjBxW,EAAO4R,YAAcld,EAAK2gB,SAAW3gB,EAAK0gB,aAAwC,IAA1BrjB,EAAO+c,iBAAqD,IAA1B/c,EAAOgd,iBACnGhd,EAAOuf,eAAc,GAIvB,MAAMyH,EAAe/a,EAAMQ,MACrBwa,EAAWD,EAAerkB,EAAK+hB,eAgBrC,GAbI1kB,EAAO0jB,aACT1jB,EAAO+a,mBAAmB3O,GAC1BpM,EAAOE,KAAK,YAAakM,GACrB6a,EAAW,KAAQD,EAAerkB,EAAKukB,cAAiB,KAC1DlnB,EAAOE,KAAK,wBAAyBkM,IAIzCzJ,EAAKukB,cAAgBjb,EAAMQ,MAC3BR,EAAM0S,SAAS,KACR3e,EAAOyE,YAAWzE,EAAO0jB,YAAa,MAGxC/gB,EAAK0gB,YAAc1gB,EAAK2gB,UAAYtjB,EAAO2kB,gBAAmC,IAAjB7B,EAAQzD,MAAc1c,EAAKyY,mBAAqBzY,EAAKyjB,eAIrH,OAHAzjB,EAAK0gB,WAAY,EACjB1gB,EAAK2gB,SAAU,OACf3gB,EAAK8hB,aAAc,GAOrB,IAAI0C,EAOJ,GAXAxkB,EAAK0gB,WAAY,EACjB1gB,EAAK2gB,SAAU,EACf3gB,EAAK8hB,aAAc,EAIjB0C,EADElZ,EAAOyY,aACI7T,EAAM7S,EAAOwY,WAAaxY,EAAOwY,WAEhC7V,EAAKyY,iBAGjBnN,EAAOwF,QACT,OAGF,GAAIxF,EAAO0Y,SAAU,CACnB,GAAIQ,GAAcnnB,EAAO6Y,eAEvB,YADA7Y,EAAO6c,QAAQ7c,EAAOkY,aAGxB,GAAIiP,GAAcnnB,EAAOsZ,eAMvB,YALItZ,EAAOY,OAAOzB,OAASiU,EAASjU,OAClCa,EAAO6c,QAAQzJ,EAASjU,OAAS,GAEjCa,EAAO6c,QAAQ7c,EAAOY,OAAOzB,OAAS,IAK1C,GAAI8O,EAAOmZ,iBAAkB,CAC3B,GAAIzkB,EAAKikB,WAAWznB,OAAS,EAAG,CAC9B,MAAMkoB,EAAgB1kB,EAAKikB,WAAWU,MAChCC,EAAgB5kB,EAAKikB,WAAWU,MAEhCE,EAAWH,EAAcR,SAAWU,EAAcV,SAClDC,EAAOO,EAAcP,KAAOS,EAAcT,KAChD9mB,EAAOynB,SAAWD,EAAWV,EAC7B9mB,EAAOynB,UAAY,EACf9S,KAAK8B,IAAIzW,EAAOynB,UAAYxZ,EAAOyZ,0BACrC1nB,EAAOynB,SAAW,IAIhBX,EAAO,KAAQ7a,EAAMQ,MAAQ4a,EAAcP,KAAQ,OACrD9mB,EAAOynB,SAAW,QAGpBznB,EAAOynB,SAAW,EAEpBznB,EAAOynB,UAAYxZ,EAAO0Z,8BAE1BhlB,EAAKikB,WAAWznB,OAAS,EACzB,IAAIyoB,EAAmB,IAAO3Z,EAAO4Z,sBACrC,MAAMC,EAAmB9nB,EAAOynB,SAAWG,EAE3C,IAAIG,EAAc/nB,EAAOwY,UAAYsP,EACjCjV,IAAKkV,GAAeA,GAExB,IACIC,EADAC,GAAW,EAEf,MAAMC,EAA2C,GAA5BvT,KAAK8B,IAAIzW,EAAOynB,UAAiBxZ,EAAOka,4BAC7D,IAAIC,EACJ,GAAIL,EAAc/nB,EAAOsZ,eACnBrL,EAAOoa,wBACLN,EAAc/nB,EAAOsZ,gBAAkB4O,IACzCH,EAAc/nB,EAAOsZ,eAAiB4O,GAExCF,EAAsBhoB,EAAOsZ,eAC7B2O,GAAW,EACXtlB,EAAK0jB,qBAAsB,GAE3B0B,EAAc/nB,EAAOsZ,eAEnBrL,EAAO1I,MAAQ0I,EAAOuI,iBAAgB4R,GAAe,QACpD,GAAIL,EAAc/nB,EAAO6Y,eAC1B5K,EAAOoa,wBACLN,EAAc/nB,EAAO6Y,eAAiBqP,IACxCH,EAAc/nB,EAAO6Y,eAAiBqP,GAExCF,EAAsBhoB,EAAO6Y,eAC7BoP,GAAW,EACXtlB,EAAK0jB,qBAAsB,GAE3B0B,EAAc/nB,EAAO6Y,eAEnB5K,EAAO1I,MAAQ0I,EAAOuI,iBAAgB4R,GAAe,QACpD,GAAIna,EAAOqa,eAAgB,CAChC,IAAIjO,EACJ,IAAK,IAAI7a,EAAI,EAAGA,EAAI4T,EAASjU,OAAQK,GAAK,EACxC,GAAI4T,EAAS5T,IAAMuoB,EAAa,CAC9B1N,EAAY7a,EACZ,MAKFuoB,EADEpT,KAAK8B,IAAIrD,EAASiH,GAAa0N,GAAepT,KAAK8B,IAAIrD,EAASiH,EAAY,GAAK0N,IAA0C,SAA1B/nB,EAAO2kB,eAC5FvR,EAASiH,GAETjH,EAASiH,EAAY,GAErC0N,GAAeA,EAQjB,GANIK,GACFpoB,EAAOuoB,KAAK,gBAAiB,KAC3BvoB,EAAO0d,YAIa,IAApB1d,EAAOynB,UAMT,GAJEG,EADE/U,EACiB8B,KAAK8B,MAAMsR,EAAc/nB,EAAOwY,WAAaxY,EAAOynB,UAEpD9S,KAAK8B,KAAKsR,EAAc/nB,EAAOwY,WAAaxY,EAAOynB,UAEpExZ,EAAOqa,eAAgB,CAQzB,MAAME,EAAe7T,KAAK8B,KAAK5D,GAAOkV,EAAcA,GAAe/nB,EAAOwY,WACpEiQ,EAAmBzoB,EAAOsT,gBAAgBtT,EAAOkY,aAErD0P,EADEY,EAAeC,EACExa,EAAO4J,MACjB2Q,EAAe,EAAIC,EACM,IAAfxa,EAAO4J,MAEQ,IAAf5J,EAAO4J,YAGzB,GAAI5J,EAAOqa,eAEhB,YADAtoB,EAAOqe,iBAILpQ,EAAOoa,wBAA0BJ,GACnCjoB,EAAOmZ,eAAe6O,GACtBhoB,EAAOgY,cAAc4P,GACrB5nB,EAAOqb,aAAa0M,GACpB/nB,EAAO0c,iBAAgB,EAAM1c,EAAO2kB,gBACpC3kB,EAAOic,WAAY,EACnBvJ,EAAWrI,cAAc,KAClBrK,IAAUA,EAAOyE,WAAc9B,EAAK0jB,sBACzCrmB,EAAOE,KAAK,kBACZF,EAAOgY,cAAc/J,EAAO4J,OAC5BtL,WAAW,KACTvM,EAAOqb,aAAa2M,GACpBtV,EAAWrI,cAAc,KAClBrK,IAAUA,EAAOyE,WACtBzE,EAAOqK,mBAER,OAEIrK,EAAOynB,UAChBznB,EAAOmZ,eAAe4O,GACtB/nB,EAAOgY,cAAc4P,GACrB5nB,EAAOqb,aAAa0M,GACpB/nB,EAAO0c,iBAAgB,EAAM1c,EAAO2kB,gBAC/B3kB,EAAOic,YACVjc,EAAOic,WAAY,EACnBvJ,EAAWrI,cAAc,KAClBrK,IAAUA,EAAOyE,WACtBzE,EAAOqK,oBAIXrK,EAAOmZ,eAAe4O,GAGxB/nB,EAAOua,oBACPva,EAAO2Z,2BACF,GAAI1L,EAAOqa,eAEhB,YADAtoB,EAAOqe,iBAST,cALKpQ,EAAOmZ,kBAAoBH,GAAYhZ,EAAOya,gBACjD1oB,EAAOmZ,iBACPnZ,EAAOua,oBACPva,EAAO2Z,wBAMX,IAAIgP,EAAY,EACZC,EAAY5oB,EAAOsT,gBAAgB,GACvC,IAAK,IAAItU,EAAI,EAAGA,EAAIqU,EAAWlU,OAAQH,GAAMA,EAAIiP,EAAOyI,mBAAqB,EAAIzI,EAAOsH,eAAiB,CACvG,MAAMkI,EAAaze,EAAIiP,EAAOyI,mBAAqB,EAAI,EAAIzI,EAAOsH,eACzB,qBAA9BlC,EAAWrU,EAAIye,GACpB0J,GAAc9T,EAAWrU,IAAMmoB,EAAa9T,EAAWrU,EAAIye,KAC7DkL,EAAY3pB,EACZ4pB,EAAYvV,EAAWrU,EAAIye,GAAapK,EAAWrU,IAE5CmoB,GAAc9T,EAAWrU,KAClC2pB,EAAY3pB,EACZ4pB,EAAYvV,EAAWA,EAAWlU,OAAS,GAAKkU,EAAWA,EAAWlU,OAAS,IAKnF,MAAM0pB,GAAS1B,EAAa9T,EAAWsV,IAAcC,EAC/CnL,EAAakL,EAAY1a,EAAOyI,mBAAqB,EAAI,EAAIzI,EAAOsH,eAE1E,GAAI0R,EAAWhZ,EAAOya,aAAc,CAElC,IAAKza,EAAO6a,WAEV,YADA9oB,EAAO6c,QAAQ7c,EAAOkY,aAGM,SAA1BlY,EAAO2kB,iBACLkE,GAAS5a,EAAO8a,gBAAiB/oB,EAAO6c,QAAQ8L,EAAYlL,GAC3Dzd,EAAO6c,QAAQ8L,IAEQ,SAA1B3oB,EAAO2kB,iBACLkE,EAAS,EAAI5a,EAAO8a,gBAAkB/oB,EAAO6c,QAAQ8L,EAAYlL,GAChEzd,EAAO6c,QAAQ8L,QAEjB,CAEL,IAAK1a,EAAO+a,YAEV,YADAhpB,EAAO6c,QAAQ7c,EAAOkY,aAGxB,MAAM+Q,EAAoBjpB,EAAO0F,aAAe0G,EAAE3L,SAAWT,EAAO0F,WAAWwjB,QAAU9c,EAAE3L,SAAWT,EAAO0F,WAAWyjB,QACnHF,EAOM7c,EAAE3L,SAAWT,EAAO0F,WAAWwjB,OACxClpB,EAAO6c,QAAQ8L,EAAYlL,GAE3Bzd,EAAO6c,QAAQ8L,IATe,SAA1B3oB,EAAO2kB,gBACT3kB,EAAO6c,QAAQ8L,EAAYlL,GAEC,SAA1Bzd,EAAO2kB,gBACT3kB,EAAO6c,QAAQ8L,KAUvB,SAASS,KACP,MAAMppB,EAASlC,MAET,OAAEmQ,EAAM,GAAEvB,GAAO1M,EAEvB,GAAI0M,GAAyB,IAAnBA,EAAGyQ,YAAmB,OAG5BlP,EAAOob,aACTrpB,EAAOspB,gBAIT,MAAM,eAAEvM,EAAc,eAAEC,EAAc,SAAE5J,GAAapT,EAGrDA,EAAO+c,gBAAiB,EACxB/c,EAAOgd,gBAAiB,EAExBhd,EAAOgS,aACPhS,EAAOyS,eAEPzS,EAAO2Z,uBACuB,SAAzB1L,EAAO6G,eAA4B7G,EAAO6G,cAAgB,IAAM9U,EAAOwZ,QAAUxZ,EAAOiO,OAAOuI,eAClGxW,EAAO6c,QAAQ7c,EAAOY,OAAOzB,OAAS,EAAG,GAAG,GAAO,GAEnDa,EAAO6c,QAAQ7c,EAAOkY,YAAa,GAAG,GAAO,GAG3ClY,EAAOupB,UAAYvpB,EAAOupB,SAASC,SAAWxpB,EAAOupB,SAASE,QAChEzpB,EAAOupB,SAASG,MAGlB1pB,EAAOgd,eAAiBA,EACxBhd,EAAO+c,eAAiBA,EAEpB/c,EAAOiO,OAAOsJ,eAAiBnE,IAAapT,EAAOoT,UACrDpT,EAAOwX,gBAIX,SAASmS,GAASvd,GAChB,MAAMpM,EAASlC,KACVkC,EAAO0jB,aACN1jB,EAAOiO,OAAO2b,eAAexd,EAAEyY,iBAC/B7kB,EAAOiO,OAAO4b,0BAA4B7pB,EAAOic,YACnD7P,EAAE+Z,kBACF/Z,EAAE0d,6BAKR,SAASC,KACP,MAAM/pB,EAASlC,MACT,UAAEyd,EAAS,aAAE3I,GAAiB5S,EAiBpC,IAAI2b,EAhBJ3b,EAAO4b,kBAAoB5b,EAAOwY,UAC9BxY,EAAOqS,eAEPrS,EAAOwY,UADL5F,EACmB2I,EAAU2B,YAAc3B,EAAU4B,YAAe5B,EAAUyO,YAE5DzO,EAAUyO,WAGhChqB,EAAOwY,WAAa+C,EAAU0O,WAGN,IAAtBjqB,EAAOwY,YAAkBxY,EAAOwY,UAAY,GAEhDxY,EAAOua,oBACPva,EAAO2Z,sBAGP,MAAMN,EAAiBrZ,EAAOsZ,eAAiBtZ,EAAO6Y,eAEpD8C,EADqB,IAAnBtC,EACY,GAECrZ,EAAOwY,UAAYxY,EAAO6Y,gBAAkB,EAEzD8C,IAAgB3b,EAAOkZ,UACzBlZ,EAAOmZ,eAAevG,GAAgB5S,EAAOwY,UAAYxY,EAAOwY,WAGlExY,EAAOE,KAAK,eAAgBF,EAAOwY,WAAW,GAGhD,IAAI0R,IAAqB,EACzB,SAASC,MAET,SAASC,KACP,MAAMpqB,EAASlC,MACT,OACJmQ,EAAM,YAAEoc,EAAW,GAAE3d,EAAE,UAAE6O,GACvBvb,EAEJA,EAAO4iB,aAAeA,EAAa3e,KAAKjE,GACxCA,EAAOqlB,YAAcA,EAAYphB,KAAKjE,GACtCA,EAAO+mB,WAAaA,EAAW9iB,KAAKjE,GAChCiO,EAAOwF,UACTzT,EAAO+pB,SAAWA,GAAS9lB,KAAKjE,IAGlCA,EAAO2pB,QAAUA,GAAQ1lB,KAAKjE,GAE9B,MAAMsqB,IAAYrc,EAAOiY,OAGzB,IAAK9W,EAAQC,OAASD,EAAQG,cAC5B7C,EAAGtI,iBAAiBimB,EAAYE,MAAOvqB,EAAO4iB,cAAc,GAC5D,OAAWxe,iBAAiBimB,EAAYG,KAAMxqB,EAAOqlB,YAAaiF,GAClE,OAAWlmB,iBAAiBimB,EAAYI,IAAKzqB,EAAO+mB,YAAY,OAC3D,CACL,GAAI3X,EAAQC,MAAO,CACjB,MAAMO,IAAwC,eAAtBya,EAAYE,QAA0Bnb,EAAQQ,kBAAmB3B,EAAOyc,mBAAmB,CAAEC,SAAS,EAAML,SAAS,GAC7I5d,EAAGtI,iBAAiBimB,EAAYE,MAAOvqB,EAAO4iB,aAAchT,GAC5DlD,EAAGtI,iBAAiBimB,EAAYG,KAAMxqB,EAAOqlB,YAAajW,EAAQQ,gBAAkB,CAAE+a,SAAS,EAAOL,WAAYA,GAClH5d,EAAGtI,iBAAiBimB,EAAYI,IAAKzqB,EAAO+mB,WAAYnX,GACpDya,EAAYO,QACdle,EAAGtI,iBAAiBimB,EAAYO,OAAQ5qB,EAAO+mB,WAAYnX,GAExDsa,KACH,OAAW9lB,iBAAiB,aAAc+lB,IAC1CD,IAAqB,IAGpBjc,EAAOwR,gBAAkBkB,EAAOK,MAAQL,EAAOM,SAAahT,EAAOwR,gBAAkBrQ,EAAQC,OAASsR,EAAOK,OAChHtU,EAAGtI,iBAAiB,YAAapE,EAAO4iB,cAAc,GACtD,OAAWxe,iBAAiB,YAAapE,EAAOqlB,YAAaiF,GAC7D,OAAWlmB,iBAAiB,UAAWpE,EAAO+mB,YAAY,KAI1D9Y,EAAO2b,eAAiB3b,EAAO4b,2BACjCnd,EAAGtI,iBAAiB,QAASpE,EAAO2pB,SAAS,GAE3C1b,EAAOwF,SACT8H,EAAUnX,iBAAiB,SAAUpE,EAAO+pB,UAI1C9b,EAAO4c,qBACT7qB,EAAO6B,GAAI8e,EAAOK,KAAOL,EAAOM,QAAU,0CAA4C,wBAA0BmI,IAAU,GAE1HppB,EAAO6B,GAAG,iBAAkBunB,IAAU,GAI1C,SAAS0B,KACP,MAAM9qB,EAASlC,MAET,OACJmQ,EAAM,YAAEoc,EAAW,GAAE3d,EAAE,UAAE6O,GACvBvb,EAEEsqB,IAAYrc,EAAOiY,OAGzB,IAAK9W,EAAQC,OAASD,EAAQG,cAC5B7C,EAAG8P,oBAAoB6N,EAAYE,MAAOvqB,EAAO4iB,cAAc,GAC/D,OAAWpG,oBAAoB6N,EAAYG,KAAMxqB,EAAOqlB,YAAaiF,GACrE,OAAW9N,oBAAoB6N,EAAYI,IAAKzqB,EAAO+mB,YAAY,OAC9D,CACL,GAAI3X,EAAQC,MAAO,CACjB,MAAMO,IAAwC,iBAAtBya,EAAYE,QAA4Bnb,EAAQQ,kBAAmB3B,EAAOyc,mBAAmB,CAAEC,SAAS,EAAML,SAAS,GAC/I5d,EAAG8P,oBAAoB6N,EAAYE,MAAOvqB,EAAO4iB,aAAchT,GAC/DlD,EAAG8P,oBAAoB6N,EAAYG,KAAMxqB,EAAOqlB,YAAaiF,GAC7D5d,EAAG8P,oBAAoB6N,EAAYI,IAAKzqB,EAAO+mB,WAAYnX,GACvDya,EAAYO,QACdle,EAAG8P,oBAAoB6N,EAAYO,OAAQ5qB,EAAO+mB,WAAYnX,IAG7D3B,EAAOwR,gBAAkBkB,EAAOK,MAAQL,EAAOM,SAAahT,EAAOwR,gBAAkBrQ,EAAQC,OAASsR,EAAOK,OAChHtU,EAAG8P,oBAAoB,YAAaxc,EAAO4iB,cAAc,GACzD,OAAWpG,oBAAoB,YAAaxc,EAAOqlB,YAAaiF,GAChE,OAAW9N,oBAAoB,UAAWxc,EAAO+mB,YAAY,KAI7D9Y,EAAO2b,eAAiB3b,EAAO4b,2BACjCnd,EAAG8P,oBAAoB,QAASxc,EAAO2pB,SAAS,GAG9C1b,EAAOwF,SACT8H,EAAUiB,oBAAoB,SAAUxc,EAAO+pB,UAIjD/pB,EAAOmK,IAAKwW,EAAOK,KAAOL,EAAOM,QAAU,0CAA4C,wBAA0BmI,IAGnH,IAAIlZ,GAAS,CACXka,gBACAU,iBAGF,SAASxB,KACP,MAAMtpB,EAASlC,MACT,YACJoa,EAAW,YAAEjS,EAAW,aAAEsX,EAAe,EAAC,OAAEtP,EAAM,IAAEtG,GAClD3H,EACEqpB,EAAcpb,EAAOob,YAC3B,IAAKA,GAAgBA,GAAmD,IAApCnrB,OAAO4N,KAAKud,GAAalqB,OAAe,OAG5E,MAAM4rB,EAAa/qB,EAAOgrB,cAAc3B,GAExC,GAAI0B,GAAc/qB,EAAOirB,oBAAsBF,EAAY,CACzD,MAAMG,EAAuBH,KAAc1B,EAAcA,EAAY0B,QAAcloB,EAC/EqoB,GACF,CAAC,gBAAiB,eAAgB,iBAAkB,qBAAsB,mBAAmBvpB,QAASuM,IACpG,MAAMid,EAAaD,EAAqBhd,GACd,qBAAfid,IAITD,EAAqBhd,GAHT,kBAAVA,GAA6C,SAAfid,GAAwC,SAAfA,EAEtC,kBAAVjd,EACqBL,WAAWsd,GAEX5Y,SAAS4Y,EAAY,IAJrB,UASpC,MAAMC,EAAmBF,GAAwBlrB,EAAOqrB,eAClDC,EAAcrd,EAAOyG,gBAAkB,EACvC6W,EAAaH,EAAiB1W,gBAAkB,EAClD4W,IAAgBC,EAClB5jB,EAAIiC,YAAY,GAAGqE,EAAOud,kCAAkCvd,EAAOud,0CACzDF,GAAeC,IACzB5jB,EAAIgC,SAAYsE,EAAOud,uBAAV,YACgC,WAAzCJ,EAAiBrW,qBACnBpN,EAAIgC,SAAYsE,EAAOud,uBAAV,oBAIjB,MAAMC,EAAmBL,EAAiBzO,WAAayO,EAAiBzO,YAAc1O,EAAO0O,UACvF+O,EAAczd,EAAO1I,OAAS6lB,EAAiBtW,gBAAkB7G,EAAO6G,eAAiB2W,GAE3FA,GAAoBxlB,GACtBjG,EAAO2rB,kBAGT1f,EAAM3F,OAAOtG,EAAOiO,OAAQmd,GAE5Bnf,EAAM3F,OAAOtG,EAAQ,CACnBklB,eAAgBllB,EAAOiO,OAAOiX,eAC9BnI,eAAgB/c,EAAOiO,OAAO8O,eAC9BC,eAAgBhd,EAAOiO,OAAO+O,iBAGhChd,EAAOirB,kBAAoBF,EAEvBW,GAAezlB,IACjBjG,EAAOwF,cACPxF,EAAO6F,aACP7F,EAAOyS,eACPzS,EAAO6c,QAAS3E,EAAcqF,EAAgBvd,EAAOud,aAAc,GAAG,IAGxEvd,EAAOE,KAAK,aAAckrB,IAI9B,SAASJ,GAAe3B,GAEtB,IAAKA,EAAa,OAClB,IAAI0B,GAAa,EAEjB,MAAMa,EAAS1tB,OAAO4N,KAAKud,GAAajc,IAAKye,IAC3C,GAAqB,kBAAVA,GAA6C,IAAvBA,EAAM1nB,QAAQ,KAAY,CACzD,MAAM2nB,EAAWje,WAAWge,EAAME,OAAO,IACnCrpB,EAAQ,OAAOspB,YAAcF,EACnC,MAAO,CAAEppB,QAAOmpB,SAElB,MAAO,CAAEnpB,MAAOmpB,EAAOA,WAGzBD,EAAOK,KAAK,CAAC1sB,EAAG2sB,IAAM3Z,SAAShT,EAAEmD,MAAO,IAAM6P,SAAS2Z,EAAExpB,MAAO,KAChE,IAAK,IAAI1D,EAAI,EAAGA,EAAI4sB,EAAOzsB,OAAQH,GAAK,EAAG,CACzC,MAAM,MAAE6sB,EAAK,MAAEnpB,GAAUkpB,EAAO5sB,GAC5B0D,GAAS,OAAOypB,aAClBpB,EAAac,GAGjB,OAAOd,GAAc,MAGvB,IAAI1B,GAAc,CAAEC,iBAAe0B,kBAEnC,SAASoB,KACP,MAAMpsB,EAASlC,MACT,WACJuuB,EAAU,OAAEpe,EAAM,IAAE4E,EAAG,IAAElL,GACvB3H,EACEssB,EAAW,GAEjBA,EAASzb,KAAK,eACdyb,EAASzb,KAAK5C,EAAO0O,WAEjB1O,EAAO0Y,UACT2F,EAASzb,KAAK,aAEZ5C,EAAO6K,YACTwT,EAASzb,KAAK,cAEZgC,GACFyZ,EAASzb,KAAK,OAEZ5C,EAAOyG,gBAAkB,IAC3B4X,EAASzb,KAAK,YACqB,WAA/B5C,EAAO8G,qBACTuX,EAASzb,KAAK,oBAGd8P,EAAOM,SACTqL,EAASzb,KAAK,WAEZ8P,EAAOK,KACTsL,EAASzb,KAAK,OAGZ5C,EAAOwF,SACT6Y,EAASzb,KAAK,YAGhByb,EAAS3qB,QAAS4qB,IAChBF,EAAWxb,KAAK5C,EAAOud,uBAAyBe,KAGlD5kB,EAAIgC,SAAS0iB,EAAWhf,KAAK,MAG/B,SAASmf,KACP,MAAMxsB,EAASlC,MACT,IAAE6J,EAAG,WAAE0kB,GAAersB,EAE5B2H,EAAIiC,YAAYyiB,EAAWhf,KAAK,MAGlC,IAAIof,GAAU,CAAEL,cAAYI,kBAE5B,SAASE,GAAWC,EAASC,EAAKC,EAAQC,EAAOC,EAAkB1gB,GACjE,IAAI2gB,EACJ,SAASC,IACH5gB,GAAUA,IAEhB,MAAM6gB,EAAY,eAAEP,GAAS9jB,OAAO,WAAW,GAE1CqkB,GAAeP,EAAQQ,UAAaJ,EAmBvCE,IAlBIL,GACFI,EAAQ,IAAI,OAAOI,MACnBJ,EAAMK,OAASJ,EACfD,EAAMM,QAAUL,EACZH,IACFE,EAAMF,MAAQA,GAEZD,IACFG,EAAMH,OAASA,GAEbD,IACFI,EAAMJ,IAAMA,IAGdK,IAQN,SAASM,KACP,MAAMvtB,EAASlC,KAEf,SAASmvB,IACe,qBAAXjtB,GAAqC,OAAXA,GAAoBA,IAAUA,EAAOyE,iBAC9C5B,IAAxB7C,EAAOwtB,eAA4BxtB,EAAOwtB,cAAgB,GAC1DxtB,EAAOwtB,eAAiBxtB,EAAOytB,aAAatuB,SAC1Ca,EAAOiO,OAAOyf,qBAAqB1tB,EAAOyF,SAC9CzF,EAAOE,KAAK,iBANhBF,EAAOytB,aAAeztB,EAAO2H,IAAI6D,KAAK,OAStC,IAAK,IAAIxM,EAAI,EAAGA,EAAIgB,EAAOytB,aAAatuB,OAAQH,GAAK,EAAG,CACtD,MAAM2tB,EAAU3sB,EAAOytB,aAAazuB,GACpCgB,EAAO0sB,UACLC,EACAA,EAAQgB,YAAchB,EAAQiB,aAAa,OAC3CjB,EAAQE,QAAUF,EAAQiB,aAAa,UACvCjB,EAAQG,OAASH,EAAQiB,aAAa,UACtC,EACAX,IAKN,IAAIY,GAAS,CACXnB,aACAa,kBAGF,SAAS/V,KACP,MAAMxX,EAASlC,KACTmQ,EAASjO,EAAOiO,OAChB6f,EAAY9tB,EAAO0f,SACnBqO,EAAoB/tB,EAAOY,OAAOzB,OAAS,GAAM8O,EAAO0F,mBAAsB1F,EAAOiG,cAAgBlU,EAAOY,OAAOzB,OAAS,GAAQa,EAAOY,OAAO,GAAe,YAAIZ,EAAOY,OAAOzB,OAErL8O,EAAO0F,oBAAsB1F,EAAO4F,mBAAqBka,EAC3D/tB,EAAO0f,SAAWqO,GAAqB/tB,EAAOwS,KAE9CxS,EAAO0f,SAAsC,IAA3B1f,EAAOoT,SAASjU,OAGpCa,EAAO+c,gBAAkB/c,EAAO0f,SAChC1f,EAAOgd,gBAAkBhd,EAAO0f,SAG5BoO,IAAc9tB,EAAO0f,UAAU1f,EAAOE,KAAKF,EAAO0f,SAAW,OAAS,UAEtEoO,GAAaA,IAAc9tB,EAAO0f,WACpC1f,EAAOwZ,OAAQ,EACfxZ,EAAO0F,WAAWD,UAItB,IAAIuoB,GAAkB,CAAExW,kBAEpByW,GAAW,CACbC,MAAM,EACNvR,UAAW,aACXsG,kBAAmB,YACnBnG,aAAc,EACdjF,MAAO,IACPpE,SAAS,EACToX,sBAAsB,EAEtB3O,gCAAgC,EAGhCiI,oBAAoB,EACpBE,mBAAoB,GAGpBsC,UAAU,EACVS,kBAAkB,EAClBS,sBAAuB,EACvBQ,wBAAwB,EACxBF,4BAA6B,EAC7BR,8BAA+B,EAC/BW,gBAAgB,EAChBZ,wBAAyB,IAGzB5O,YAAY,EAGZjC,gBAAgB,EAGhBsE,kBAAkB,EAGlBvE,OAAQ,QAGRyS,iBAAaxmB,EAGbqR,aAAc,EACdY,cAAe,EACfJ,gBAAiB,EACjBK,oBAAqB,SACrBQ,eAAgB,EAChBmB,mBAAoB,EACpBF,gBAAgB,EAChBO,sBAAsB,EACtBpD,mBAAoB,EACpBE,kBAAmB,EACnB+G,qBAAqB,EACrBxD,0BAA0B,EAG1BG,eAAe,EAGftB,cAAc,EAGdqQ,WAAY,EACZT,WAAY,GACZpG,eAAe,EACfuJ,aAAa,EACbF,YAAY,EACZC,gBAAiB,GACjBL,aAAc,IACdhC,cAAc,EACdxB,gBAAgB,EAChB5G,UAAW,EACX2H,0BAA0B,EAC1Bd,0BAA0B,EAC1BC,+BAA+B,EAC/BK,qBAAqB,EAGrB0I,mBAAmB,EAGnB1H,YAAY,EACZD,gBAAiB,IAGjB/O,qBAAqB,EACrBC,uBAAuB,EAGvBmI,YAAY,EAGZ+J,eAAe,EACfC,0BAA0B,EAC1B5O,qBAAqB,EAGrBsS,eAAe,EACfG,qBAAqB,EAGrBnoB,MAAM,EACNyZ,qBAAsB,EACtBzB,aAAc,KACdqB,wBAAwB,EAGxB5B,gBAAgB,EAChBD,gBAAgB,EAChB4G,aAAc,KACdJ,WAAW,EACXE,eAAgB,oBAChBD,kBAAmB,KAGnBkH,kBAAkB,EAGlBc,uBAAwB,oBACxB5sB,WAAY,eACZmgB,gBAAiB,+BACjBjF,iBAAkB,sBAClBG,0BAA2B,gCAC3BvB,kBAAmB,uBACnB0B,oBAAqB,yBACrBL,eAAgB,oBAChBG,wBAAyB,8BACzBF,eAAgB,oBAChBG,wBAAyB,8BACzBxb,aAAc,iBAGdmc,oBAAoB,GAKtB,MAAMsT,GAAa,CACjB3oB,SACA+S,YACAtO,aACAiL,QACA5P,OACAsa,aACAa,eACAxQ,UACAmZ,eACA7R,cAAewW,GACfvB,WACAoB,WAGIQ,GAAmB,GAEzB,MAAM9kB,WAAexL,EACnB,eAAegE,GACb,IAAI2K,EACAuB,EACgB,IAAhBlM,EAAK5C,QAAgB4C,EAAK,GAAG0M,aAAe1M,EAAK,GAAG0M,cAAgBvQ,OACtE+P,EAASlM,EAAK,IAEb2K,EAAIuB,GAAUlM,EAEZkM,IAAQA,EAAS,IAEtBA,EAAShC,EAAM3F,OAAO,GAAI2H,GACtBvB,IAAOuB,EAAOvB,KAAIuB,EAAOvB,GAAKA,GAElC4hB,MAAMrgB,GAEN/P,OAAO4N,KAAKsiB,IAAYzsB,QAAS4sB,IAC/BrwB,OAAO4N,KAAKsiB,GAAWG,IAAiB5sB,QAAS6sB,IAC1CjlB,GAAOpL,UAAUqwB,KACpBjlB,GAAOpL,UAAUqwB,GAAeJ,GAAWG,GAAgBC,QAMjE,MAAMxuB,EAASlC,KACe,qBAAnBkC,EAAOgR,UAChBhR,EAAOgR,QAAU,IAEnB9S,OAAO4N,KAAK9L,EAAOgR,SAASrP,QAASsP,IACnC,MAAMC,EAASlR,EAAOgR,QAAQC,GAC9B,GAAIC,EAAOjD,OAAQ,CACjB,MAAMwgB,EAAkBvwB,OAAO4N,KAAKoF,EAAOjD,QAAQ,GAC7CmD,EAAeF,EAAOjD,OAAOwgB,GACnC,GAA4B,kBAAjBrd,GAA8C,OAAjBA,EAAuB,OAC/D,KAAMqd,KAAmBxgB,MAAU,YAAamD,GAAe,QAC/B,IAA5BnD,EAAOwgB,KACTxgB,EAAOwgB,GAAmB,CAAExb,SAAS,IAGF,kBAA5BhF,EAAOwgB,IACT,YAAaxgB,EAAOwgB,KAEzBxgB,EAAOwgB,GAAiBxb,SAAU,GAE/BhF,EAAOwgB,KAAkBxgB,EAAOwgB,GAAmB,CAAExb,SAAS,OAKvE,MAAMyb,EAAeziB,EAAM3F,OAAO,GAAI2nB,IACtCjuB,EAAO2uB,iBAAiBD,GAGxB1uB,EAAOiO,OAAShC,EAAM3F,OAAO,GAAIooB,EAAcL,GAAkBpgB,GACjEjO,EAAOqrB,eAAiBpf,EAAM3F,OAAO,GAAItG,EAAOiO,QAChDjO,EAAO4uB,aAAe3iB,EAAM3F,OAAO,GAAI2H,GAGvCjO,EAAO6uB,EAAI,OAGX,MAAMlnB,EAAM,eAAE3H,EAAOiO,OAAOvB,IAG5B,GAFAA,EAAK/E,EAAI,IAEJ+E,EACH,OAGF,GAAI/E,EAAIxI,OAAS,EAAG,CAClB,MAAM2vB,EAAU,GAKhB,OAJAnnB,EAAI+C,KAAK,CAACI,EAAOikB,KACf,MAAMC,EAAY/iB,EAAM3F,OAAO,GAAI2H,EAAQ,CAAEvB,GAAIqiB,IACjDD,EAAQje,KAAK,IAAItH,GAAOylB,MAEnBF,EAOT,IAAIpc,EA6HJ,OAjIAhG,EAAG1M,OAASA,EACZ2H,EAAIhF,KAAK,SAAU3C,GAIf0M,GAAMA,EAAGuiB,YAAcviB,EAAGuiB,WAAWC,eACvCxc,EAAa,eAAEhG,EAAGuiB,WAAWC,cAAc,IAAIlvB,EAAOiO,OAAOtP,eAE7D+T,EAAWjH,SAAY5E,GAAYc,EAAI8D,SAAS5E,IAEhD6L,EAAa/K,EAAI8D,SAAS,IAAIzL,EAAOiO,OAAOtP,cAG9CsN,EAAM3F,OAAOtG,EAAQ,CACnB2H,MACA+E,KACAgG,aACA6I,UAAW7I,EAAW,GAGtB2Z,WAAY,GAGZzrB,OAAQ,iBACRyS,WAAY,GACZD,SAAU,GACVE,gBAAiB,GAGjB,eACE,MAAmC,eAA5BtT,EAAOiO,OAAO0O,WAEvB,aACE,MAAmC,aAA5B3c,EAAOiO,OAAO0O,WAGvB9J,IAA+B,QAAzBnG,EAAGkQ,IAAI9c,eAAoD,QAAzB6H,EAAI8C,IAAI,aAChDmI,aAA0C,eAA5B5S,EAAOiO,OAAO0O,YAAwD,QAAzBjQ,EAAGkQ,IAAI9c,eAAoD,QAAzB6H,EAAI8C,IAAI,cACrGqI,SAAwC,gBAA9BJ,EAAWjI,IAAI,WAGzByN,YAAa,EACb0B,UAAW,EAGXL,aAAa,EACbC,OAAO,EAGPhB,UAAW,EACXoD,kBAAmB,EACnB1C,SAAU,EACVuO,SAAU,EACVxL,WAAW,EAGXc,eAAgB/c,EAAOiO,OAAO8O,eAC9BC,eAAgBhd,EAAOiO,OAAO+O,eAG9BqN,YAAc,WACZ,MAAMhb,EAAQ,CAAC,aAAc,YAAa,WAAY,eACtD,IAAI8R,EAAU,CAAC,YAAa,YAAa,WAezC,OAdI/R,EAAQG,gBACV4R,EAAU,CAAC,cAAe,cAAe,cAE3CnhB,EAAOmvB,iBAAmB,CACxB5E,MAAOlb,EAAM,GACbmb,KAAMnb,EAAM,GACZob,IAAKpb,EAAM,GACXub,OAAQvb,EAAM,IAEhBrP,EAAOovB,mBAAqB,CAC1B7E,MAAOpJ,EAAQ,GACfqJ,KAAMrJ,EAAQ,GACdsJ,IAAKtJ,EAAQ,IAER/R,EAAQC,QAAUrP,EAAOiO,OAAOwR,cAAgBzf,EAAOmvB,iBAAmBnvB,EAAOovB,mBAjB7E,GAmBbvM,gBAAiB,CACfQ,eAAWxgB,EACXygB,aAASzgB,EACT0hB,yBAAqB1hB,EACrB6hB,oBAAgB7hB,EAChB2hB,iBAAa3hB,EACbuY,sBAAkBvY,EAClBujB,oBAAgBvjB,EAChB+hB,wBAAoB/hB,EAEpBiiB,aAAc,wDAEdoC,cAAejb,EAAMQ,MACrB4iB,kBAAcxsB,EAEd+jB,WAAY,GACZP,yBAAqBxjB,EACrBqgB,kBAAcrgB,EACd4hB,iBAAa5hB,GAIf6gB,YAAY,EAGZwB,eAAgBllB,EAAOiO,OAAOiX,eAE9BpC,QAAS,CACPmB,OAAQ,EACRC,OAAQ,EACRN,SAAU,EACVG,SAAU,EACV1E,KAAM,GAIRoO,aAAc,GACdD,aAAc,IAKhBxtB,EAAOsvB,aAGHtvB,EAAOiO,OAAOigB,MAChBluB,EAAOkuB,OAIFluB,EAGT,uBACE,MAAMA,EAASlC,MACT,OACJmQ,EAAM,OAAErN,EAAM,WAAEyS,EAAYb,KAAMG,EAAU,YAAEuF,GAC5ClY,EACJ,IAAIuvB,EAAM,EACV,GAAIthB,EAAOuI,eAAgB,CACzB,IACIgZ,EADAvb,EAAYrT,EAAOsX,GAAa3B,gBAEpC,IAAK,IAAIvX,EAAIkZ,EAAc,EAAGlZ,EAAI4B,EAAOzB,OAAQH,GAAK,EAChD4B,EAAO5B,KAAOwwB,IAChBvb,GAAarT,EAAO5B,GAAGuX,gBACvBgZ,GAAO,EACHtb,EAAYtB,IAAY6c,GAAY,IAG5C,IAAK,IAAIxwB,EAAIkZ,EAAc,EAAGlZ,GAAK,EAAGA,GAAK,EACrC4B,EAAO5B,KAAOwwB,IAChBvb,GAAarT,EAAO5B,GAAGuX,gBACvBgZ,GAAO,EACHtb,EAAYtB,IAAY6c,GAAY,SAI5C,IAAK,IAAIxwB,EAAIkZ,EAAc,EAAGlZ,EAAI4B,EAAOzB,OAAQH,GAAK,EAChDqU,EAAWrU,GAAKqU,EAAW6E,GAAevF,IAC5C4c,GAAO,GAIb,OAAOA,EAGT,SACE,MAAMvvB,EAASlC,KACf,IAAKkC,GAAUA,EAAOyE,UAAW,OACjC,MAAM,SAAE2O,EAAQ,OAAEnF,GAAWjO,EAU7B,SAASqb,IACP,MAAMoU,EAAiBzvB,EAAO4S,cAAmC,EAApB5S,EAAOwY,UAAiBxY,EAAOwY,UACtE2D,EAAexH,KAAKgB,IAAIhB,KAAKK,IAAIya,EAAgBzvB,EAAOsZ,gBAAiBtZ,EAAO6Y,gBACtF7Y,EAAOqb,aAAac,GACpBnc,EAAOua,oBACPva,EAAO2Z,sBAET,IAAI+V,EAfAzhB,EAAOob,aACTrpB,EAAOspB,gBAETtpB,EAAOgS,aACPhS,EAAOyS,eACPzS,EAAOmZ,iBACPnZ,EAAO2Z,sBAUH3Z,EAAOiO,OAAO0Y,UAChBtL,IACIrb,EAAOiO,OAAO6K,YAChB9Y,EAAO4X,qBAIP8X,GADmC,SAAhC1vB,EAAOiO,OAAO6G,eAA4B9U,EAAOiO,OAAO6G,cAAgB,IAAM9U,EAAOwZ,QAAUxZ,EAAOiO,OAAOuI,eACnGxW,EAAO6c,QAAQ7c,EAAOY,OAAOzB,OAAS,EAAG,GAAG,GAAO,GAEnDa,EAAO6c,QAAQ7c,EAAOkY,YAAa,GAAG,GAAO,GAEvDwX,GACHrU,KAGApN,EAAOsJ,eAAiBnE,IAAapT,EAAOoT,UAC9CpT,EAAOwX,gBAETxX,EAAOE,KAAK,UAGd,gBAAgByvB,EAAcC,GAAa,GACzC,MAAM5vB,EAASlC,KACT+xB,EAAmB7vB,EAAOiO,OAAO0O,UAKvC,OAJKgT,IAEHA,EAAoC,eAArBE,EAAoC,WAAa,cAE7DF,IAAiBE,GAAuC,eAAjBF,GAAkD,aAAjBA,IAI7E3vB,EAAO2H,IACJiC,YAAY,GAAG5J,EAAOiO,OAAOud,yBAAyBqE,KACtDlmB,SAAS,GAAG3J,EAAOiO,OAAOud,yBAAyBmE,KAEtD3vB,EAAOiO,OAAO0O,UAAYgT,EAE1B3vB,EAAOY,OAAO8J,KAAK,CAAC8I,EAAYsc,KACT,aAAjBH,EACFG,EAAQ/Z,MAAM9D,MAAQ,GAEtB6d,EAAQ/Z,MAAM7D,OAAS,KAI3BlS,EAAOE,KAAK,mBACR0vB,GAAY5vB,EAAOyF,UAlBdzF,EAuBX,OACE,MAAMA,EAASlC,KACXkC,EAAOiG,cAEXjG,EAAOE,KAAK,cAGRF,EAAOiO,OAAOob,aAChBrpB,EAAOspB,gBAITtpB,EAAOosB,aAGHpsB,EAAOiO,OAAO1I,MAChBvF,EAAO6F,aAIT7F,EAAOgS,aAGPhS,EAAOyS,eAEHzS,EAAOiO,OAAOsJ,eAChBvX,EAAOwX,gBAILxX,EAAOiO,OAAO4R,YAChB7f,EAAOuf,gBAGLvf,EAAOiO,OAAOsf,eAChBvtB,EAAOutB,gBAILvtB,EAAOiO,OAAO1I,KAChBvF,EAAO6c,QAAQ7c,EAAOiO,OAAO6O,aAAe9c,EAAOud,aAAc,EAAGvd,EAAOiO,OAAO6M,oBAElF9a,EAAO6c,QAAQ7c,EAAOiO,OAAO6O,aAAc,EAAG9c,EAAOiO,OAAO6M,oBAI9D9a,EAAOoqB,eAGPpqB,EAAOiG,aAAc,EAGrBjG,EAAOE,KAAK,SAGd,QAAQ6vB,GAAiB,EAAMC,GAAc,GAC3C,MAAMhwB,EAASlC,MACT,OACJmQ,EAAM,IAAEtG,EAAG,WAAE+K,EAAU,OAAE9R,GACvBZ,EAEJ,MAA6B,qBAAlBA,EAAOiO,QAA0BjO,EAAOyE,YAInDzE,EAAOE,KAAK,iBAGZF,EAAOiG,aAAc,EAGrBjG,EAAO8qB,eAGH7c,EAAO1I,MACTvF,EAAOwF,cAILwqB,IACFhwB,EAAOwsB,gBACP7kB,EAAIqC,WAAW,SACf0I,EAAW1I,WAAW,SAClBpJ,GAAUA,EAAOzB,QACnByB,EACGgJ,YAAY,CACXqE,EAAOyK,kBACPzK,EAAO6L,iBACP7L,EAAO8L,eACP9L,EAAO+L,gBACP3M,KAAK,MACNrD,WAAW,SACXA,WAAW,4BAIlBhK,EAAOE,KAAK,WAGZhC,OAAO4N,KAAK9L,EAAOiQ,iBAAiBtO,QAASC,IAC3C5B,EAAOmK,IAAIvI,MAGU,IAAnBmuB,IACF/vB,EAAO2H,IAAI,GAAG3H,OAAS,KACvBA,EAAO2H,IAAIhF,KAAK,SAAU,MAC1BsJ,EAAMgkB,YAAYjwB,IAEpBA,EAAOyE,WAAY,GA9CV,KAmDX,sBAAsByrB,GACpBjkB,EAAM3F,OAAO+nB,GAAkB6B,GAGjC,8BACE,OAAO7B,GAGT,sBACE,OAAOJ,GAGT,mBACE,OAAOlwB,EAGT,eACE,OAAO,QAIX,IAAIoyB,GAAW,CACbrsB,KAAM,SACN8N,MAAO,CACLmP,OAAQJ,GAEV9O,OAAQ,CACNkP,OAAQJ,IAIRyP,GAAY,CACdtsB,KAAM,UACN8N,MAAO,CACLye,QAASjhB,GAEXyC,OAAQ,CACNwe,QAASjhB,IAIb,MAAMkhB,GAAW,WACf,SAASC,IACP,MAAM1P,EAAK,OAAOpR,UAAUqR,UAAUhhB,cACtC,OAAQ+gB,EAAG1c,QAAQ,WAAa,GAAK0c,EAAG1c,QAAQ,UAAY,GAAK0c,EAAG1c,QAAQ,WAAa,EAE3F,MAAO,CACLqsB,SAAU,OAAO/gB,UAAUqR,UAAUoB,MAAM,SAC3CqO,SAAUA,IACVE,YAAa,+CAA+CC,KAAK,OAAOjhB,UAAUqR,YARtE,GAYhB,IAAI6P,GAAY,CACd7sB,KAAM,UACN8N,MAAO,CACLgf,QAASN,IAEXze,OAAQ,CACN+e,QAASN,KAITO,GAAS,CACX/sB,KAAM,SACN,SACE,MAAM9D,EAASlC,KACfmO,EAAM3F,OAAOtG,EAAQ,CACnB8wB,OAAQ,CACN,gBACO9wB,IAAUA,EAAOyE,WAAczE,EAAOiG,cAC3CjG,EAAOE,KAAK,gBACZF,EAAOE,KAAK,YAEd,2BACOF,IAAUA,EAAOyE,WAAczE,EAAOiG,aAC3CjG,EAAOE,KAAK,0BAKpB2B,GAAI,CACF,OACE,MAAM7B,EAASlC,KAEf,OAAOsG,iBAAiB,SAAUpE,EAAO8wB,OAAOC,eAGhD,OAAO3sB,iBAAiB,oBAAqBpE,EAAO8wB,OAAOE,2BAE7D,UACE,MAAMhxB,EAASlC,KACf,OAAO0e,oBAAoB,SAAUxc,EAAO8wB,OAAOC,eACnD,OAAOvU,oBAAoB,oBAAqBxc,EAAO8wB,OAAOE,6BAKpE,MAAMC,GAAW,CACfC,KAAM,OAAOC,kBAAoB,OAAOC,uBACxC,OAAO3wB,EAAQoG,EAAU,IACvB,MAAM7G,EAASlC,KAETuzB,EAAeJ,GAASC,KACxBvhB,EAAW,IAAI0hB,EAAcC,IAIjC,GAAyB,IAArBA,EAAUnyB,OAEZ,YADAa,EAAOE,KAAK,iBAAkBoxB,EAAU,IAG1C,MAAMC,EAAiB,WACrBvxB,EAAOE,KAAK,iBAAkBoxB,EAAU,KAGtC,OAAOE,sBACT,OAAOA,sBAAsBD,GAE7B,OAAOhlB,WAAWglB,EAAgB,KAItC5hB,EAAS8hB,QAAQhxB,EAAQ,CACvBixB,WAA0C,qBAAvB7qB,EAAQ6qB,YAAoC7qB,EAAQ6qB,WACvEC,UAAwC,qBAAtB9qB,EAAQ8qB,WAAmC9qB,EAAQ8qB,UACrEC,cAAgD,qBAA1B/qB,EAAQ+qB,eAAuC/qB,EAAQ+qB,gBAG/E5xB,EAAO2P,SAASkiB,UAAUhhB,KAAKlB,IAEjC,OACE,MAAM3P,EAASlC,KACf,GAAKsR,EAAQO,UAAa3P,EAAOiO,OAAO0B,SAAxC,CACA,GAAI3P,EAAOiO,OAAO6jB,eAAgB,CAChC,MAAMC,EAAmB/xB,EAAO2H,IAAI2D,UACpC,IAAK,IAAItM,EAAI,EAAGA,EAAI+yB,EAAiB5yB,OAAQH,GAAK,EAChDgB,EAAO2P,SAASqiB,OAAOD,EAAiB/yB,IAI5CgB,EAAO2P,SAASqiB,OAAOhyB,EAAO2H,IAAI,GAAI,CAAEgqB,UAAW3xB,EAAOiO,OAAOgkB,uBAGjEjyB,EAAO2P,SAASqiB,OAAOhyB,EAAO0S,WAAW,GAAI,CAAEgf,YAAY,MAE7D,UACE,MAAM1xB,EAASlC,KACfkC,EAAO2P,SAASkiB,UAAUlwB,QAASgO,IACjCA,EAASuiB,eAEXlyB,EAAO2P,SAASkiB,UAAY,KAIhC,IAAIM,GAAa,CACfruB,KAAM,WACNmK,OAAQ,CACN0B,UAAU,EACVmiB,gBAAgB,EAChBG,sBAAsB,GAExB,SACE,MAAMjyB,EAASlC,KACfmO,EAAM3F,OAAOtG,EAAQ,CACnB2P,SAAU,CACRue,KAAM+C,GAAS/C,KAAKjqB,KAAKjE,GACzBgyB,OAAQf,GAASe,OAAO/tB,KAAKjE,GAC7BkG,QAAS+qB,GAAS/qB,QAAQjC,KAAKjE,GAC/B6xB,UAAW,OAIjBhwB,GAAI,CACF,OACE,MAAM7B,EAASlC,KACfkC,EAAO2P,SAASue,QAElB,UACE,MAAMluB,EAASlC,KACfkC,EAAO2P,SAASzJ,aAKtB,MAAMksB,GAAU,CACd,OAAOC,GACL,MAAMryB,EAASlC,MACT,cAAEgX,EAAa,eAAES,EAAc,eAAEiB,GAAmBxW,EAAOiO,QAC3D,gBAAEqkB,EAAe,eAAEC,GAAmBvyB,EAAOiO,OAAO+E,SAExDrS,KAAM6xB,EACN9jB,GAAI+jB,EAAU,OACd7xB,EACAyS,WAAYqf,EAAkB,YAC9BC,EACAnoB,OAAQooB,GACN5yB,EAAOgT,QACXhT,EAAOua,oBACP,MAAMrC,EAAclY,EAAOkY,aAAe,EAE1C,IAAI2a,EAIAC,EACAC,EAJqBF,EAArB7yB,EAAO4S,aAA2B,QACpB5S,EAAOqS,eAAiB,OAAS,MAI/CmE,GACFsc,EAAcne,KAAKC,MAAME,EAAgB,GAAKS,EAAiB+c,EAC/DS,EAAepe,KAAKC,MAAME,EAAgB,GAAKS,EAAiBgd,IAEhEO,EAAche,GAAiBS,EAAiB,GAAK+c,EACrDS,EAAexd,EAAiBgd,GAElC,MAAM5xB,EAAOgU,KAAKK,KAAKkD,GAAe,GAAK6a,EAAc,GACnDrkB,EAAKiG,KAAKgB,KAAKuC,GAAe,GAAK4a,EAAalyB,EAAOzB,OAAS,GAChEqL,GAAUxK,EAAOqT,WAAW1S,IAAS,IAAMX,EAAOqT,WAAW,IAAM,GASzE,SAAS2f,IACPhzB,EAAOyS,eACPzS,EAAOmZ,iBACPnZ,EAAO2Z,sBACH3Z,EAAOizB,MAAQjzB,EAAOiO,OAAOglB,KAAKhgB,SACpCjT,EAAOizB,KAAKC,OAIhB,GAhBAjnB,EAAM3F,OAAOtG,EAAOgT,QAAS,CAC3BrS,OACA+N,KACAlE,SACA6I,WAAYrT,EAAOqT,aAYjBmf,IAAiB7xB,GAAQ8xB,IAAe/jB,IAAO2jB,EAKjD,OAJIryB,EAAOqT,aAAeqf,GAAsBloB,IAAWooB,GACzD5yB,EAAOY,OAAO6J,IAAIooB,EAAeroB,EAAH,WAEhCxK,EAAOmZ,iBAGT,GAAInZ,EAAOiO,OAAO+E,QAAQmgB,eAcxB,OAbAnzB,EAAOiO,OAAO+E,QAAQmgB,eAAe90B,KAAK2B,EAAQ,CAChDwK,SACA7J,OACA+N,KACA9N,OAAS,WACP,MAAMwyB,EAAiB,GACvB,IAAK,IAAIp0B,EAAI2B,EAAM3B,GAAK0P,EAAI1P,GAAK,EAC/Bo0B,EAAeviB,KAAKjQ,EAAO5B,IAE7B,OAAOo0B,EALD,UAQVJ,IAGF,MAAMK,EAAiB,GACjBC,EAAgB,GACtB,GAAIjB,EACFryB,EAAO0S,WAAWlH,KAAK,IAAIxL,EAAOiO,OAAOrP,YAAc+M,cAEvD,IAAK,IAAI3M,EAAIwzB,EAAcxzB,GAAKyzB,EAAYzzB,GAAK,GAC3CA,EAAI2B,GAAQ3B,EAAI0P,IAClB1O,EAAO0S,WAAWlH,KAAK,IAAIxL,EAAOiO,OAAOrP,uCAAuCI,OAAO2M,SAI7F,IAAK,IAAI3M,EAAI,EAAGA,EAAI4B,EAAOzB,OAAQH,GAAK,EAClCA,GAAK2B,GAAQ3B,GAAK0P,IACM,qBAAf+jB,GAA8BJ,EACvCiB,EAAcziB,KAAK7R,IAEfA,EAAIyzB,GAAYa,EAAcziB,KAAK7R,GACnCA,EAAIwzB,GAAca,EAAexiB,KAAK7R,KAIhDs0B,EAAc3xB,QAASmJ,IACrB9K,EAAO0S,WAAW1H,OAAO2nB,EAAY/xB,EAAOkK,GAAQA,MAEtDuoB,EAAepH,KAAK,CAAC1sB,EAAG2sB,IAAMA,EAAI3sB,GAAGoC,QAASmJ,IAC5C9K,EAAO0S,WAAWzH,QAAQ0nB,EAAY/xB,EAAOkK,GAAQA,MAEvD9K,EAAO0S,WAAWjH,SAAS,iBAAiBhB,IAAIooB,EAAeroB,EAAH,MAC5DwoB,KAEF,YAAY7d,EAAOrK,GACjB,MAAM9K,EAASlC,KACTmQ,EAASjO,EAAOiO,OAAO+E,QAC7B,GAAI/E,EAAOhH,OAASjH,EAAOgT,QAAQ/L,MAAM6D,GACvC,OAAO9K,EAAOgT,QAAQ/L,MAAM6D,GAE9B,MAAMyoB,EAAWtlB,EAAO0kB,YACpB,eAAE1kB,EAAO0kB,YAAYt0B,KAAK2B,EAAQmV,EAAOrK,IACzC,eAAE,eAAe9K,EAAOiO,OAAOrP,wCAAwCkM,MAAUqK,WAGrF,OAFKoe,EAASxpB,KAAK,4BAA4BwpB,EAASxpB,KAAK,0BAA2Be,GACpFmD,EAAOhH,QAAOjH,EAAOgT,QAAQ/L,MAAM6D,GAASyoB,GACzCA,GAET,YAAY3yB,GACV,MAAMZ,EAASlC,KACf,GAAsB,kBAAX8C,GAAuB,WAAYA,EAC5C,IAAK,IAAI5B,EAAI,EAAGA,EAAI4B,EAAOzB,OAAQH,GAAK,EAClC4B,EAAO5B,IAAIgB,EAAOgT,QAAQpS,OAAOiQ,KAAKjQ,EAAO5B,SAGnDgB,EAAOgT,QAAQpS,OAAOiQ,KAAKjQ,GAE7BZ,EAAOgT,QAAQvN,QAAO,IAExB,aAAa7E,GACX,MAAMZ,EAASlC,KACToa,EAAclY,EAAOkY,YAC3B,IAAIsC,EAAiBtC,EAAc,EAC/Bsb,EAAoB,EAExB,GAAIn0B,MAAMqR,QAAQ9P,GAAS,CACzB,IAAK,IAAI5B,EAAI,EAAGA,EAAI4B,EAAOzB,OAAQH,GAAK,EAClC4B,EAAO5B,IAAIgB,EAAOgT,QAAQpS,OAAOyf,QAAQzf,EAAO5B,IAEtDwb,EAAiBtC,EAActX,EAAOzB,OACtCq0B,EAAoB5yB,EAAOzB,YAE3Ba,EAAOgT,QAAQpS,OAAOyf,QAAQzf,GAEhC,GAAIZ,EAAOiO,OAAO+E,QAAQ/L,MAAO,CAC/B,MAAMA,EAAQjH,EAAOgT,QAAQ/L,MACvBwsB,EAAW,GACjBv1B,OAAO4N,KAAK7E,GAAOtF,QAAS+xB,IAC1B,MAAMC,EAAY1sB,EAAMysB,GAClBE,EAAgBD,EAAU5pB,KAAK,2BACjC6pB,GACFD,EAAU5pB,KAAK,0BAA2BwI,SAASqhB,EAAe,IAAM,GAE1EH,EAASlhB,SAASmhB,EAAa,IAAMF,GAAqBG,IAE5D3zB,EAAOgT,QAAQ/L,MAAQwsB,EAEzBzzB,EAAOgT,QAAQvN,QAAO,GACtBzF,EAAO6c,QAAQrC,EAAgB,IAEjC,YAAY+F,GACV,MAAMvgB,EAASlC,KACf,GAA6B,qBAAlByiB,GAAmD,OAAlBA,EAAwB,OACpE,IAAIrI,EAAclY,EAAOkY,YACzB,GAAI7Y,MAAMqR,QAAQ6P,GAChB,IAAK,IAAIvhB,EAAIuhB,EAAcphB,OAAS,EAAGH,GAAK,EAAGA,GAAK,EAClDgB,EAAOgT,QAAQpS,OAAO6P,OAAO8P,EAAcvhB,GAAI,GAC3CgB,EAAOiO,OAAO+E,QAAQ/L,cACjBjH,EAAOgT,QAAQ/L,MAAMsZ,EAAcvhB,IAExCuhB,EAAcvhB,GAAKkZ,IAAaA,GAAe,GACnDA,EAAcvD,KAAKK,IAAIkD,EAAa,QAGtClY,EAAOgT,QAAQpS,OAAO6P,OAAO8P,EAAe,GACxCvgB,EAAOiO,OAAO+E,QAAQ/L,cACjBjH,EAAOgT,QAAQ/L,MAAMsZ,GAE1BA,EAAgBrI,IAAaA,GAAe,GAChDA,EAAcvD,KAAKK,IAAIkD,EAAa,GAEtClY,EAAOgT,QAAQvN,QAAO,GACtBzF,EAAO6c,QAAQ3E,EAAa,IAE9B,kBACE,MAAMlY,EAASlC,KACfkC,EAAOgT,QAAQpS,OAAS,GACpBZ,EAAOiO,OAAO+E,QAAQ/L,QACxBjH,EAAOgT,QAAQ/L,MAAQ,IAEzBjH,EAAOgT,QAAQvN,QAAO,GACtBzF,EAAO6c,QAAQ,EAAG,KAItB,IAAIgX,GAAY,CACd/vB,KAAM,UACNmK,OAAQ,CACN+E,QAAS,CACPC,SAAS,EACTrS,OAAQ,GACRqG,OAAO,EACP0rB,YAAa,KACbQ,eAAgB,KAChBb,gBAAiB,EACjBC,eAAgB,IAGpB,SACE,MAAMvyB,EAASlC,KACfmO,EAAM3F,OAAOtG,EAAQ,CACnBgT,QAAS,CACPvN,OAAQ2sB,GAAQ3sB,OAAOxB,KAAKjE,GAC5B8f,YAAasS,GAAQtS,YAAY7b,KAAKjE,GACtC+f,aAAcqS,GAAQrS,aAAa9b,KAAKjE,GACxCsgB,YAAa8R,GAAQ9R,YAAYrc,KAAKjE,GACtCygB,gBAAiB2R,GAAQ3R,gBAAgBxc,KAAKjE,GAC9C2yB,YAAaP,GAAQO,YAAY1uB,KAAKjE,GACtCY,OAAQZ,EAAOiO,OAAO+E,QAAQpS,OAC9BqG,MAAO,OAIbpF,GAAI,CACF,aACE,MAAM7B,EAASlC,KACf,IAAKkC,EAAOiO,OAAO+E,QAAQC,QAAS,OACpCjT,EAAOqsB,WAAWxb,KAAQ7Q,EAAOiO,OAAOud,uBAAjB,WACvB,MAAMsI,EAAkB,CACtBrc,qBAAqB,GAEvBxL,EAAM3F,OAAOtG,EAAOiO,OAAQ6lB,GAC5B7nB,EAAM3F,OAAOtG,EAAOqrB,eAAgByI,GAE/B9zB,EAAOiO,OAAO6O,cACjB9c,EAAOgT,QAAQvN,UAGnB,eACE,MAAMzF,EAASlC,KACVkC,EAAOiO,OAAO+E,QAAQC,SAC3BjT,EAAOgT,QAAQvN,YAKrB,MAAMsuB,GAAW,CACf,OAAO9zB,GACL,MAAMD,EAASlC,MACP8U,aAAcC,GAAQ7S,EAC9B,IAAIoM,EAAInM,EACJmM,EAAE2W,gBAAe3W,EAAIA,EAAE2W,eAC3B,MAAMiR,EAAK5nB,EAAE6nB,SAAW7nB,EAAE8nB,SAE1B,IAAKl0B,EAAO+c,iBAAoB/c,EAAOqS,gBAAyB,KAAP2hB,GAAeh0B,EAAOsS,cAAuB,KAAP0hB,GAAqB,KAAPA,GAC3G,OAAO,EAET,IAAKh0B,EAAOgd,iBAAoBhd,EAAOqS,gBAAyB,KAAP2hB,GAAeh0B,EAAOsS,cAAuB,KAAP0hB,GAAqB,KAAPA,GAC3G,OAAO,EAET,KAAI5nB,EAAE+nB,UAAY/nB,EAAEgoB,QAAUhoB,EAAEioB,SAAWjoB,EAAEkoB,YAGzC,OAAWvP,gBAAiB,OAAWA,cAAcwP,UAAiE,UAApD,OAAWxP,cAAcwP,SAASz0B,eAAiF,aAApD,OAAWilB,cAAcwP,SAASz0B,eAAvK,CAGA,GAAIE,EAAOiO,OAAOumB,SAASC,iBAA0B,KAAPT,GAAoB,KAAPA,GAAoB,KAAPA,GAAoB,KAAPA,GAAoB,KAAPA,GAAoB,KAAPA,GAAY,CACzH,IAAIU,GAAS,EAEb,GAAI10B,EAAO2H,IAAI2D,QAAQ,IAAItL,EAAOiO,OAAOrP,YAAcO,OAAS,GAAyE,IAApEa,EAAO2H,IAAI2D,QAAQ,IAAItL,EAAOiO,OAAO6L,kBAAoB3a,OAC5H,OAEF,MAAMw1B,EAAc,OAAOxI,WACrByI,EAAe,OAAO5I,YACtB6I,EAAe70B,EAAO2H,IAAI6C,SAC5BqI,IAAKgiB,EAAaC,MAAQ90B,EAAO2H,IAAI,GAAGqiB,YAC5C,MAAM+K,EAAc,CAClB,CAACF,EAAaC,KAAMD,EAAaG,KACjC,CAACH,EAAaC,KAAO90B,EAAOiS,MAAO4iB,EAAaG,KAChD,CAACH,EAAaC,KAAMD,EAAaG,IAAMh1B,EAAOkS,QAC9C,CAAC2iB,EAAaC,KAAO90B,EAAOiS,MAAO4iB,EAAaG,IAAMh1B,EAAOkS,SAE/D,IAAK,IAAIlT,EAAI,EAAGA,EAAI+1B,EAAY51B,OAAQH,GAAK,EAAG,CAC9C,MAAM6sB,EAAQkJ,EAAY/1B,GAExB6sB,EAAM,IAAM,GAAKA,EAAM,IAAM8I,GAC1B9I,EAAM,IAAM,GAAKA,EAAM,IAAM+I,IAEhCF,GAAS,GAGb,IAAKA,EAAQ,OAEX10B,EAAOqS,gBACE,KAAP2hB,GAAoB,KAAPA,GAAoB,KAAPA,GAAoB,KAAPA,IACrC5nB,EAAEyY,eAAgBzY,EAAEyY,iBACnBzY,EAAE6oB,aAAc,IAEV,KAAPjB,GAAoB,KAAPA,GAAenhB,KAAiB,KAAPmhB,GAAoB,KAAPA,IAAcnhB,IAAM7S,EAAOwd,aACvE,KAAPwW,GAAoB,KAAPA,GAAenhB,KAAiB,KAAPmhB,GAAoB,KAAPA,IAAcnhB,IAAM7S,EAAO6d,cAEzE,KAAPmW,GAAoB,KAAPA,GAAoB,KAAPA,GAAoB,KAAPA,IACrC5nB,EAAEyY,eAAgBzY,EAAEyY,iBACnBzY,EAAE6oB,aAAc,GAEZ,KAAPjB,GAAoB,KAAPA,GAAWh0B,EAAOwd,YACxB,KAAPwW,GAAoB,KAAPA,GAAWh0B,EAAO6d,aAErC7d,EAAOE,KAAK,WAAY8zB,KAG1B,SACE,MAAMh0B,EAASlC,KACXkC,EAAOw0B,SAASvhB,UACpB,eAAE,QAAYpR,GAAG,UAAW7B,EAAOw0B,SAASzwB,QAC5C/D,EAAOw0B,SAASvhB,SAAU,IAE5B,UACE,MAAMjT,EAASlC,KACVkC,EAAOw0B,SAASvhB,UACrB,eAAE,QAAY9I,IAAI,UAAWnK,EAAOw0B,SAASzwB,QAC7C/D,EAAOw0B,SAASvhB,SAAU,KAI9B,IAAIiiB,GAAa,CACfpxB,KAAM,WACNmK,OAAQ,CACNumB,SAAU,CACRvhB,SAAS,EACTwhB,gBAAgB,IAGpB,SACE,MAAMz0B,EAASlC,KACfmO,EAAM3F,OAAOtG,EAAQ,CACnBw0B,SAAU,CACRvhB,SAAS,EACTkiB,OAAQpB,GAASoB,OAAOlxB,KAAKjE,GAC7Bo1B,QAASrB,GAASqB,QAAQnxB,KAAKjE,GAC/B+D,OAAQgwB,GAAShwB,OAAOE,KAAKjE,OAInC6B,GAAI,CACF,OACE,MAAM7B,EAASlC,KACXkC,EAAOiO,OAAOumB,SAASvhB,SACzBjT,EAAOw0B,SAASW,UAGpB,UACE,MAAMn1B,EAASlC,KACXkC,EAAOw0B,SAASvhB,SAClBjT,EAAOw0B,SAASY,aAMxB,SAASC,KACP,MAAMzzB,EAAY,UAClB,IAAI0zB,EAAc1zB,KAAa,OAE/B,IAAK0zB,EAAa,CAChB,MAAMvyB,EAAU,OAAWkF,cAAc,OACzClF,EAAQwyB,aAAa3zB,EAAW,WAChC0zB,EAA4C,oBAAvBvyB,EAAQnB,GAc/B,OAXK0zB,GACA,OAAWE,gBACX,OAAWA,eAAeC,aAGuB,IAAjD,OAAWD,eAAeC,WAAW,GAAI,MAG5CH,EAAc,OAAWE,eAAeC,WAAW,eAAgB,QAG9DH,EAET,MAAMI,GAAa,CACjBC,eAAgB1pB,EAAMQ,MACtBmpB,yBAAqB/yB,EACrBgzB,kBAAmB,GACnB,QACE,OAAI,OAAOpmB,UAAUqR,UAAU3c,QAAQ,YAAc,EAAU,iBACxDkxB,KAAqB,QAAU,cAExC,UAAUjpB,GAER,MAAM0pB,EAAa,GACbC,EAAc,GACdC,EAAc,IAEpB,IAAIC,EAAK,EACLC,EAAK,EACLC,EAAK,EACLC,EAAK,EAuDT,MApDI,WAAYhqB,IACd8pB,EAAK9pB,EAAEiqB,QAEL,eAAgBjqB,IAClB8pB,GAAM9pB,EAAEkqB,WAAa,KAEnB,gBAAiBlqB,IACnB8pB,GAAM9pB,EAAEmqB,YAAc,KAEpB,gBAAiBnqB,IACnB6pB,GAAM7pB,EAAEoqB,YAAc,KAIpB,SAAUpqB,GAAKA,EAAEO,OAASP,EAAEqqB,kBAC9BR,EAAKC,EACLA,EAAK,GAGPC,EAAKF,EAAKH,EACVM,EAAKF,EAAKJ,EAEN,WAAY1pB,IACdgqB,EAAKhqB,EAAEsqB,QAEL,WAAYtqB,IACd+pB,EAAK/pB,EAAEuqB,QAGLvqB,EAAE+nB,WAAagC,IACjBA,EAAKC,EACLA,EAAK,IAGFD,GAAMC,IAAOhqB,EAAEwqB,YACE,IAAhBxqB,EAAEwqB,WACJT,GAAMJ,EACNK,GAAML,IAENI,GAAMH,EACNI,GAAMJ,IAKNG,IAAOF,IACTA,EAAME,EAAK,GAAM,EAAI,GAEnBC,IAAOF,IACTA,EAAME,EAAK,GAAM,EAAI,GAGhB,CACLS,MAAOZ,EACPa,MAAOZ,EACPa,OAAQZ,EACRa,OAAQZ,IAGZ,mBACE,MAAMp2B,EAASlC,KACfkC,EAAOi3B,cAAe,GAExB,mBACE,MAAMj3B,EAASlC,KACfkC,EAAOi3B,cAAe,GAExB,OAAOh3B,GACL,IAAImM,EAAInM,EACR,MAAMD,EAASlC,KACTmQ,EAASjO,EAAOiO,OAAOipB,WAEzBl3B,EAAOiO,OAAOwF,SAChBrH,EAAEyY,iBAGJ,IAAIpkB,EAAST,EAAO2H,IAIpB,GAH8C,cAA1C3H,EAAOiO,OAAOipB,WAAWC,eAC3B12B,EAAS,eAAET,EAAOiO,OAAOipB,WAAWC,gBAEjCn3B,EAAOi3B,eAAiBx2B,EAAO,GAAG22B,SAAShrB,EAAE3L,UAAYwN,EAAOopB,eAAgB,OAAO,EAExFjrB,EAAE2W,gBAAe3W,EAAIA,EAAE2W,eAC3B,IAAIuU,EAAQ,EACZ,MAAMC,EAAYv3B,EAAO4S,cAAgB,EAAI,EAEvCjQ,EAAO+yB,GAAW5X,UAAU1R,GAElC,GAAI6B,EAAOupB,YACT,GAAIx3B,EAAOqS,eAAgB,CACzB,KAAIsC,KAAK8B,IAAI9T,EAAKo0B,QAAUpiB,KAAK8B,IAAI9T,EAAKq0B,SACrC,OAAO,EADuCM,EAAQ30B,EAAKo0B,OAASQ,MAEpE,MAAI5iB,KAAK8B,IAAI9T,EAAKq0B,QAAUriB,KAAK8B,IAAI9T,EAAKo0B,SAC5C,OAAO,EAD8CO,EAAQ30B,EAAKq0B,YAGvEM,EAAQ3iB,KAAK8B,IAAI9T,EAAKo0B,QAAUpiB,KAAK8B,IAAI9T,EAAKq0B,SAAWr0B,EAAKo0B,OAASQ,GAAa50B,EAAKq0B,OAG3F,GAAc,IAAVM,EAAa,OAAO,EAIxB,GAFIrpB,EAAOwpB,SAAQH,GAASA,GAEvBt3B,EAAOiO,OAAO0Y,SAoCZ,CAOL,MAAM+Q,EAAW,CAAE5Q,KAAM7a,EAAMQ,MAAO6qB,MAAO3iB,KAAK8B,IAAI6gB,GAAQ3a,UAAWhI,KAAKgjB,KAAKL,KAC7E,oBAAE1B,GAAwB51B,EAAOk3B,WACjCU,EAAoBhC,GACrB8B,EAAS5Q,KAAO8O,EAAoB9O,KAAO,KAC3C4Q,EAASJ,OAAS1B,EAAoB0B,OACtCI,EAAS/a,YAAciZ,EAAoBjZ,UAChD,IAAKib,EAAmB,CACtB53B,EAAOk3B,WAAWtB,yBAAsB/yB,EAEpC7C,EAAOiO,OAAO1I,MAChBvF,EAAO0d,UAET,IAAImJ,EAAW7mB,EAAOkb,eAAkBoc,EAAQrpB,EAAO4pB,YACvD,MAAMpe,EAAezZ,EAAOuZ,YACtBG,EAAS1Z,EAAOwZ,MAetB,GAbIqN,GAAY7mB,EAAO6Y,iBAAgBgO,EAAW7mB,EAAO6Y,gBACrDgO,GAAY7mB,EAAOsZ,iBAAgBuN,EAAW7mB,EAAOsZ,gBAEzDtZ,EAAOgY,cAAc,GACrBhY,EAAOqb,aAAawL,GACpB7mB,EAAOmZ,iBACPnZ,EAAOua,oBACPva,EAAO2Z,wBAEDF,GAAgBzZ,EAAOuZ,cAAkBG,GAAU1Z,EAAOwZ,QAC9DxZ,EAAO2Z,sBAGL3Z,EAAOiO,OAAOqa,eAAgB,CAYhCwP,aAAa93B,EAAOk3B,WAAWa,SAC/B/3B,EAAOk3B,WAAWa,aAAUl1B,EAC5B,MAAMgzB,EAAoB71B,EAAOk3B,WAAWrB,kBACxCA,EAAkB12B,QAAU,IAC9B02B,EAAkBmC,QAEpB,MAAMC,EAAYpC,EAAkB12B,OAAS02B,EAAkBA,EAAkB12B,OAAS,QAAK0D,EACzFq1B,EAAarC,EAAkB,GAErC,GADAA,EAAkBhlB,KAAK6mB,GACnBO,IAAcP,EAASJ,MAAQW,EAAUX,OAASI,EAAS/a,YAAcsb,EAAUtb,WAErFkZ,EAAkBplB,OAAO,QACpB,GAAIolB,EAAkB12B,QAAU,IAChCu4B,EAAS5Q,KAAOoR,EAAWpR,KAAO,KAClCoR,EAAWZ,MAAQI,EAASJ,OAAS,GACrCI,EAASJ,OAAS,EACvB,CAOA,MAAMa,EAAkBb,EAAQ,EAAI,GAAM,GAC1Ct3B,EAAOk3B,WAAWtB,oBAAsB8B,EACxC7B,EAAkBplB,OAAO,GACzBzQ,EAAOk3B,WAAWa,QAAU9rB,EAAM0S,SAAS,KACzC3e,EAAOqe,eAAere,EAAOiO,OAAO4J,OAAO,OAAMhV,EAAWs1B,IAC3D,GAEAn4B,EAAOk3B,WAAWa,UAIrB/3B,EAAOk3B,WAAWa,QAAU9rB,EAAM0S,SAAS,KACzC,MAAMwZ,EAAkB,GACxBn4B,EAAOk3B,WAAWtB,oBAAsB8B,EACxC7B,EAAkBplB,OAAO,GACzBzQ,EAAOqe,eAAere,EAAOiO,OAAO4J,OAAO,OAAMhV,EAAWs1B,IAC3D,MAUP,GALKP,GAAmB53B,EAAOE,KAAK,SAAUkM,GAG1CpM,EAAOiO,OAAOsb,UAAYvpB,EAAOiO,OAAOmqB,8BAA8Bp4B,EAAOupB,SAAS8O,OAEtFxR,IAAa7mB,EAAO6Y,gBAAkBgO,IAAa7mB,EAAOsZ,eAAgB,OAAO,OArI5D,CAE3B,MAAMoe,EAAW,CACf5Q,KAAM7a,EAAMQ,MACZ6qB,MAAO3iB,KAAK8B,IAAI6gB,GAChB3a,UAAWhI,KAAKgjB,KAAKL,GACrBgB,IAAKr4B,GAID41B,EAAoB71B,EAAOk3B,WAAWrB,kBACxCA,EAAkB12B,QAAU,GAC9B02B,EAAkBmC,QAEpB,MAAMC,EAAYpC,EAAkB12B,OAAS02B,EAAkBA,EAAkB12B,OAAS,QAAK0D,EAmB/F,GAlBAgzB,EAAkBhlB,KAAK6mB,GAQnBO,GACEP,EAAS/a,YAAcsb,EAAUtb,WAAa+a,EAASJ,MAAQW,EAAUX,OAASI,EAAS5Q,KAAOmR,EAAUnR,KAAO,MACrH9mB,EAAOk3B,WAAWqB,cAAcb,GAGlC13B,EAAOk3B,WAAWqB,cAAcb,GAK9B13B,EAAOk3B,WAAWsB,cAAcd,GAClC,OAAO,EAyGX,OAFItrB,EAAEyY,eAAgBzY,EAAEyY,iBACnBzY,EAAE6oB,aAAc,GACd,GAET,cAAcyC,GACZ,MAAM13B,EAASlC,KAIf,OAAI45B,EAASJ,OAAS,GAAKrrB,EAAMQ,MAAQzM,EAAOk3B,WAAWvB,eAAiB,KAgBxE+B,EAAS/a,UAAY,EACjB3c,EAAOwZ,QAASxZ,EAAOiO,OAAO1I,MAAUvF,EAAOic,YACnDjc,EAAOwd,YACPxd,EAAOE,KAAK,SAAUw3B,EAASY,MAEtBt4B,EAAOuZ,cAAevZ,EAAOiO,OAAO1I,MAAUvF,EAAOic,YAChEjc,EAAO6d,YACP7d,EAAOE,KAAK,SAAUw3B,EAASY,MAGjCt4B,EAAOk3B,WAAWvB,gBAAiB,IAAK,OAAOnpB,MAAQisB,WAEhD,IAET,cAAcf,GACZ,MAAM13B,EAASlC,KACTmQ,EAASjO,EAAOiO,OAAOipB,WAC7B,GAAIQ,EAAS/a,UAAY,GACvB,GAAI3c,EAAOwZ,QAAUxZ,EAAOiO,OAAO1I,MAAQ0I,EAAOopB,eAEhD,OAAO,OAEJ,GAAIr3B,EAAOuZ,cAAgBvZ,EAAOiO,OAAO1I,MAAQ0I,EAAOopB,eAE7D,OAAO,EAET,OAAO,GAET,SACE,MAAMr3B,EAASlC,KACTmC,EAAQy1B,GAAWz1B,QACzB,GAAID,EAAOiO,OAAOwF,QAEhB,OADAzT,EAAOub,UAAUiB,oBAAoBvc,EAAOD,EAAOk3B,WAAWnzB,SACvD,EAET,IAAK9D,EAAO,OAAO,EACnB,GAAID,EAAOk3B,WAAWjkB,QAAS,OAAO,EACtC,IAAIxS,EAAST,EAAO2H,IAQpB,MAP8C,cAA1C3H,EAAOiO,OAAOipB,WAAWC,eAC3B12B,EAAS,eAAET,EAAOiO,OAAOipB,WAAWC,eAEtC12B,EAAOoB,GAAG,aAAc7B,EAAOk3B,WAAWwB,kBAC1Cj4B,EAAOoB,GAAG,aAAc7B,EAAOk3B,WAAWyB,kBAC1Cl4B,EAAOoB,GAAG5B,EAAOD,EAAOk3B,WAAWnzB,QACnC/D,EAAOk3B,WAAWjkB,SAAU,GACrB,GAET,UACE,MAAMjT,EAASlC,KACTmC,EAAQy1B,GAAWz1B,QACzB,GAAID,EAAOiO,OAAOwF,QAEhB,OADAzT,EAAOub,UAAUnX,iBAAiBnE,EAAOD,EAAOk3B,WAAWnzB,SACpD,EAET,IAAK9D,EAAO,OAAO,EACnB,IAAKD,EAAOk3B,WAAWjkB,QAAS,OAAO,EACvC,IAAIxS,EAAST,EAAO2H,IAMpB,MAL8C,cAA1C3H,EAAOiO,OAAOipB,WAAWC,eAC3B12B,EAAS,eAAET,EAAOiO,OAAOipB,WAAWC,eAEtC12B,EAAO0J,IAAIlK,EAAOD,EAAOk3B,WAAWnzB,QACpC/D,EAAOk3B,WAAWjkB,SAAU,GACrB,IAIX,IAAI2lB,GAAe,CACjB90B,KAAM,aACNmK,OAAQ,CACNipB,WAAY,CACVjkB,SAAS,EACTokB,gBAAgB,EAChBI,QAAQ,EACRD,aAAa,EACbK,YAAa,EACbV,aAAc,cAGlB,SACE,MAAMn3B,EAASlC,KACfmO,EAAM3F,OAAOtG,EAAQ,CACnBk3B,WAAY,CACVjkB,SAAS,EACTkiB,OAAQO,GAAWP,OAAOlxB,KAAKjE,GAC/Bo1B,QAASM,GAAWN,QAAQnxB,KAAKjE,GACjC+D,OAAQ2xB,GAAW3xB,OAAOE,KAAKjE,GAC/B04B,iBAAkBhD,GAAWgD,iBAAiBz0B,KAAKjE,GACnD24B,iBAAkBjD,GAAWiD,iBAAiB10B,KAAKjE,GACnDu4B,cAAe7C,GAAW6C,cAAct0B,KAAKjE,GAC7Cw4B,cAAe9C,GAAW8C,cAAcv0B,KAAKjE,GAC7C21B,eAAgB1pB,EAAMQ,MACtBmpB,yBAAqB/yB,EACrBgzB,kBAAmB,OAIzBh0B,GAAI,CACF,OACE,MAAM7B,EAASlC,MACVkC,EAAOiO,OAAOipB,WAAWjkB,SAAWjT,EAAOiO,OAAOwF,SACrDzT,EAAOk3B,WAAW9B,UAEhBp1B,EAAOiO,OAAOipB,WAAWjkB,SAASjT,EAAOk3B,WAAW/B,UAE1D,UACE,MAAMn1B,EAASlC,KACXkC,EAAOiO,OAAOwF,SAChBzT,EAAOk3B,WAAW/B,SAEhBn1B,EAAOk3B,WAAWjkB,SAASjT,EAAOk3B,WAAW9B,aAKvD,MAAMyD,GAAa,CACjB,SAEE,MAAM74B,EAASlC,KACTmQ,EAASjO,EAAOiO,OAAOvI,WAE7B,GAAI1F,EAAOiO,OAAO1I,KAAM,OACxB,MAAM,QAAEuzB,EAAO,QAAEC,GAAY/4B,EAAO0F,WAEhCqzB,GAAWA,EAAQ55B,OAAS,IAC1Ba,EAAOuZ,YACTwf,EAAQpvB,SAASsE,EAAO+qB,eAExBD,EAAQnvB,YAAYqE,EAAO+qB,eAE7BD,EAAQ/4B,EAAOiO,OAAOsJ,eAAiBvX,EAAO0f,SAAW,WAAa,eAAezR,EAAOgrB,YAE1FH,GAAWA,EAAQ35B,OAAS,IAC1Ba,EAAOwZ,MACTsf,EAAQnvB,SAASsE,EAAO+qB,eAExBF,EAAQlvB,YAAYqE,EAAO+qB,eAE7BF,EAAQ94B,EAAOiO,OAAOsJ,eAAiBvX,EAAO0f,SAAW,WAAa,eAAezR,EAAOgrB,aAGhG,YAAY7sB,GACV,MAAMpM,EAASlC,KACfsO,EAAEyY,iBACE7kB,EAAOuZ,cAAgBvZ,EAAOiO,OAAO1I,MACzCvF,EAAO6d,aAET,YAAYzR,GACV,MAAMpM,EAASlC,KACfsO,EAAEyY,iBACE7kB,EAAOwZ,QAAUxZ,EAAOiO,OAAO1I,MACnCvF,EAAOwd,aAET,OACE,MAAMxd,EAASlC,KACTmQ,EAASjO,EAAOiO,OAAOvI,WAC7B,IAAMuI,EAAOib,SAAUjb,EAAOkb,OAAS,OAEvC,IAAI2P,EACAC,EACA9qB,EAAOib,SACT4P,EAAU,eAAE7qB,EAAOib,QAEjBlpB,EAAOiO,OAAOkgB,mBACc,kBAAlBlgB,EAAOib,QACd4P,EAAQ35B,OAAS,GACyB,IAA1Ca,EAAO2H,IAAI6D,KAAKyC,EAAOib,QAAQ/pB,SAElC25B,EAAU94B,EAAO2H,IAAI6D,KAAKyC,EAAOib,UAGjCjb,EAAOkb,SACT4P,EAAU,eAAE9qB,EAAOkb,QAEjBnpB,EAAOiO,OAAOkgB,mBACc,kBAAlBlgB,EAAOkb,QACd4P,EAAQ55B,OAAS,GACyB,IAA1Ca,EAAO2H,IAAI6D,KAAKyC,EAAOkb,QAAQhqB,SAElC45B,EAAU/4B,EAAO2H,IAAI6D,KAAKyC,EAAOkb,UAIjC2P,GAAWA,EAAQ35B,OAAS,GAC9B25B,EAAQj3B,GAAG,QAAS7B,EAAO0F,WAAWwzB,aAEpCH,GAAWA,EAAQ55B,OAAS,GAC9B45B,EAAQl3B,GAAG,QAAS7B,EAAO0F,WAAWyzB,aAGxCltB,EAAM3F,OAAOtG,EAAO0F,WAAY,CAC9BozB,UACA5P,OAAQ4P,GAAWA,EAAQ,GAC3BC,UACA5P,OAAQ4P,GAAWA,EAAQ,MAG/B,UACE,MAAM/4B,EAASlC,MACT,QAAEg7B,EAAO,QAAEC,GAAY/4B,EAAO0F,WAChCozB,GAAWA,EAAQ35B,SACrB25B,EAAQ3uB,IAAI,QAASnK,EAAO0F,WAAWwzB,aACvCJ,EAAQlvB,YAAY5J,EAAOiO,OAAOvI,WAAWszB,gBAE3CD,GAAWA,EAAQ55B,SACrB45B,EAAQ5uB,IAAI,QAASnK,EAAO0F,WAAWyzB,aACvCJ,EAAQnvB,YAAY5J,EAAOiO,OAAOvI,WAAWszB,kBAKnD,IAAII,GAAe,CACjBt1B,KAAM,aACNmK,OAAQ,CACNvI,WAAY,CACVwjB,OAAQ,KACRC,OAAQ,KAERkQ,aAAa,EACbL,cAAe,yBACfM,YAAa,uBACbL,UAAW,uBAGf,SACE,MAAMj5B,EAASlC,KACfmO,EAAM3F,OAAOtG,EAAQ,CACnB0F,WAAY,CACVwoB,KAAM2K,GAAW3K,KAAKjqB,KAAKjE,GAC3ByF,OAAQozB,GAAWpzB,OAAOxB,KAAKjE,GAC/BkG,QAAS2yB,GAAW3yB,QAAQjC,KAAKjE,GACjCk5B,YAAaL,GAAWK,YAAYj1B,KAAKjE,GACzCm5B,YAAaN,GAAWM,YAAYl1B,KAAKjE,OAI/C6B,GAAI,CACF,OACE,MAAM7B,EAASlC,KACfkC,EAAO0F,WAAWwoB,OAClBluB,EAAO0F,WAAWD,UAEpB,SACE,MAAMzF,EAASlC,KACfkC,EAAO0F,WAAWD,UAEpB,WACE,MAAMzF,EAASlC,KACfkC,EAAO0F,WAAWD,UAEpB,UACE,MAAMzF,EAASlC,KACfkC,EAAO0F,WAAWQ,WAEpB,MAAMkG,GACJ,MAAMpM,EAASlC,MACT,QAAEg7B,EAAO,QAAEC,GAAY/4B,EAAO0F,WACpC,GACE1F,EAAOiO,OAAOvI,WAAW2zB,cACrB,eAAEjtB,EAAE3L,QAAQoK,GAAGkuB,KACf,eAAE3sB,EAAE3L,QAAQoK,GAAGiuB,GACnB,CACA,IAAIS,EACAT,EACFS,EAAWT,EAAQjvB,SAAS7J,EAAOiO,OAAOvI,WAAW4zB,aAC5CP,IACTQ,EAAWR,EAAQlvB,SAAS7J,EAAOiO,OAAOvI,WAAW4zB,eAEtC,IAAbC,EACFv5B,EAAOE,KAAK,iBAAkBF,GAE9BA,EAAOE,KAAK,iBAAkBF,GAE5B84B,GACFA,EAAQhvB,YAAY9J,EAAOiO,OAAOvI,WAAW4zB,aAE3CP,GACFA,EAAQjvB,YAAY9J,EAAOiO,OAAOvI,WAAW4zB,iBAOvD,MAAM/wB,GAAa,CACjB,SAEE,MAAMvI,EAASlC,KACT+U,EAAM7S,EAAO6S,IACb5E,EAASjO,EAAOiO,OAAOtI,WAC7B,IAAKsI,EAAOvB,KAAO1M,EAAO2F,WAAW+G,KAAO1M,EAAO2F,WAAWgC,KAAwC,IAAjC3H,EAAO2F,WAAWgC,IAAIxI,OAAc,OACzG,MAAMgU,EAAenT,EAAOgT,SAAWhT,EAAOiO,OAAO+E,QAAQC,QAAUjT,EAAOgT,QAAQpS,OAAOzB,OAASa,EAAOY,OAAOzB,OAC9GwI,EAAM3H,EAAO2F,WAAWgC,IAE9B,IAAI6xB,EACJ,MAAMC,EAAQz5B,EAAOiO,OAAO1I,KAAOoP,KAAKE,MAAM1B,EAAsC,EAAtBnT,EAAOud,cAAqBvd,EAAOiO,OAAOsH,gBAAkBvV,EAAOoT,SAASjU,OAc1I,GAbIa,EAAOiO,OAAO1I,MAChBi0B,EAAU7kB,KAAKE,MAAM7U,EAAOkY,YAAclY,EAAOud,cAAgBvd,EAAOiO,OAAOsH,gBAC3EikB,EAAUrmB,EAAe,EAA2B,EAAtBnT,EAAOud,eACvCic,GAAYrmB,EAAsC,EAAtBnT,EAAOud,cAEjCic,EAAUC,EAAQ,IAAGD,GAAWC,GAChCD,EAAU,GAAsC,YAAjCx5B,EAAOiO,OAAOyrB,iBAA8BF,EAAUC,EAAQD,IAEjFA,EADqC,qBAArBx5B,EAAOsX,UACbtX,EAAOsX,UAEPtX,EAAOkY,aAAe,EAGd,YAAhBjK,EAAOvH,MAAsB1G,EAAO2F,WAAWg0B,SAAW35B,EAAO2F,WAAWg0B,QAAQx6B,OAAS,EAAG,CAClG,MAAMw6B,EAAU35B,EAAO2F,WAAWg0B,QAClC,IAAIC,EACAC,EACAC,EAiBJ,GAhBI7rB,EAAO8rB,iBACT/5B,EAAO2F,WAAWq0B,WAAaL,EAAQ5uB,GAAG,GAAG/K,EAAOqS,eAAiB,aAAe,gBAAe,GACnG1K,EAAI8C,IAAIzK,EAAOqS,eAAiB,QAAU,SAAarS,EAAO2F,WAAWq0B,YAAc/rB,EAAOgsB,mBAAqB,GAA/D,MAChDhsB,EAAOgsB,mBAAqB,QAA8Bp3B,IAAzB7C,EAAOya,gBAC1Cza,EAAO2F,WAAWu0B,oBAAuBV,EAAUx5B,EAAOya,cACtDza,EAAO2F,WAAWu0B,mBAAsBjsB,EAAOgsB,mBAAqB,EACtEj6B,EAAO2F,WAAWu0B,mBAAqBjsB,EAAOgsB,mBAAqB,EAC1Dj6B,EAAO2F,WAAWu0B,mBAAqB,IAChDl6B,EAAO2F,WAAWu0B,mBAAqB,IAG3CN,EAAaJ,EAAUx5B,EAAO2F,WAAWu0B,mBACzCL,EAAYD,GAAcjlB,KAAKgB,IAAIgkB,EAAQx6B,OAAQ8O,EAAOgsB,oBAAsB,GAChFH,GAAYD,EAAYD,GAAc,GAExCD,EAAQ/vB,YAAY,GAAGqE,EAAOksB,qBAAqBlsB,EAAOksB,0BAA0BlsB,EAAOksB,+BAA+BlsB,EAAOksB,0BAA0BlsB,EAAOksB,+BAA+BlsB,EAAOksB,0BACpMxyB,EAAIxI,OAAS,EACfw6B,EAAQjvB,KAAK,CAACI,EAAOsvB,KACnB,MAAMC,EAAU,eAAED,GACZE,EAAcD,EAAQvvB,QACxBwvB,IAAgBd,GAClBa,EAAQ1wB,SAASsE,EAAOksB,mBAEtBlsB,EAAO8rB,iBACLO,GAAeV,GAAcU,GAAeT,GAC9CQ,EAAQ1wB,SAAYsE,EAAOksB,kBAAV,SAEfG,IAAgBV,GAClBS,EACGjvB,OACAzB,SAAYsE,EAAOksB,kBAAV,SACT/uB,OACAzB,SAAYsE,EAAOksB,kBAAV,cAEVG,IAAgBT,GAClBQ,EACGnvB,OACAvB,SAAYsE,EAAOksB,kBAAV,SACTjvB,OACAvB,SAAYsE,EAAOksB,kBAAV,qBAIb,CACL,MAAME,EAAUV,EAAQ5uB,GAAGyuB,GACrBc,EAAcD,EAAQvvB,QAE5B,GADAuvB,EAAQ1wB,SAASsE,EAAOksB,mBACpBlsB,EAAO8rB,eAAgB,CACzB,MAAMQ,EAAwBZ,EAAQ5uB,GAAG6uB,GACnCY,EAAuBb,EAAQ5uB,GAAG8uB,GACxC,IAAK,IAAI76B,EAAI46B,EAAY56B,GAAK66B,EAAW76B,GAAK,EAC5C26B,EAAQ5uB,GAAG/L,GAAG2K,SAAYsE,EAAOksB,kBAAV,SAEzB,GAAIn6B,EAAOiO,OAAO1I,KAChB,GAAI+0B,GAAeX,EAAQx6B,OAAS8O,EAAOgsB,mBAAoB,CAC7D,IAAK,IAAIj7B,EAAIiP,EAAOgsB,mBAAoBj7B,GAAK,EAAGA,GAAK,EACnD26B,EAAQ5uB,GAAG4uB,EAAQx6B,OAASH,GAAG2K,SAAYsE,EAAOksB,kBAAV,SAE1CR,EAAQ5uB,GAAG4uB,EAAQx6B,OAAS8O,EAAOgsB,mBAAqB,GAAGtwB,SAAYsE,EAAOksB,kBAAV,cAEpEI,EACGnvB,OACAzB,SAAYsE,EAAOksB,kBAAV,SACT/uB,OACAzB,SAAYsE,EAAOksB,kBAAV,cACZK,EACGtvB,OACAvB,SAAYsE,EAAOksB,kBAAV,SACTjvB,OACAvB,SAAYsE,EAAOksB,kBAAV,mBAGdI,EACGnvB,OACAzB,SAAYsE,EAAOksB,kBAAV,SACT/uB,OACAzB,SAAYsE,EAAOksB,kBAAV,cACZK,EACGtvB,OACAvB,SAAYsE,EAAOksB,kBAAV,SACTjvB,OACAvB,SAAYsE,EAAOksB,kBAAV,eAIlB,GAAIlsB,EAAO8rB,eAAgB,CACzB,MAAMU,EAAuB9lB,KAAKgB,IAAIgkB,EAAQx6B,OAAQ8O,EAAOgsB,mBAAqB,GAC5ES,GAAmB16B,EAAO2F,WAAWq0B,WAAaS,EAAyBz6B,EAAO2F,WAAqB,YAAK,EAAMm0B,EAAW95B,EAAO2F,WAAWq0B,WAC/InH,EAAahgB,EAAM,QAAU,OACnC8mB,EAAQlvB,IAAIzK,EAAOqS,eAAiBwgB,EAAa,MAAU6H,EAAH,OAO5D,GAJoB,aAAhBzsB,EAAOvH,OACTiB,EAAI6D,KAAK,IAAIyC,EAAO0sB,cAAgB/vB,KAAKqD,EAAO2sB,sBAAsBpB,EAAU,IAChF7xB,EAAI6D,KAAK,IAAIyC,EAAO4sB,YAAcjwB,KAAKqD,EAAO6sB,oBAAoBrB,KAEhD,gBAAhBxrB,EAAOvH,KAAwB,CACjC,IAAIq0B,EAEFA,EADE9sB,EAAO+sB,oBACch7B,EAAOqS,eAAiB,WAAa,aAErCrS,EAAOqS,eAAiB,aAAe,WAEhE,MAAM4oB,GAASzB,EAAU,GAAKC,EAC9B,IAAIyB,EAAS,EACTC,EAAS,EACgB,eAAzBJ,EACFG,EAASD,EAETE,EAASF,EAEXtzB,EAAI6D,KAAK,IAAIyC,EAAOmtB,sBAAwBnxB,UAAU,6BAA6BixB,aAAkBC,MAAWjxB,WAAWlK,EAAOiO,OAAO4J,OAEvH,WAAhB5J,EAAOvH,MAAqBuH,EAAOotB,cACrC1zB,EAAIgD,KAAKsD,EAAOotB,aAAar7B,EAAQw5B,EAAU,EAAGC,IAClDz5B,EAAOE,KAAK,mBAAoBF,EAAQ2H,EAAI,KAE5C3H,EAAOE,KAAK,mBAAoBF,EAAQ2H,EAAI,IAE9CA,EAAI3H,EAAOiO,OAAOsJ,eAAiBvX,EAAO0f,SAAW,WAAa,eAAezR,EAAOgrB,YAE1F,SAEE,MAAMj5B,EAASlC,KACTmQ,EAASjO,EAAOiO,OAAOtI,WAC7B,IAAKsI,EAAOvB,KAAO1M,EAAO2F,WAAW+G,KAAO1M,EAAO2F,WAAWgC,KAAwC,IAAjC3H,EAAO2F,WAAWgC,IAAIxI,OAAc,OACzG,MAAMgU,EAAenT,EAAOgT,SAAWhT,EAAOiO,OAAO+E,QAAQC,QAAUjT,EAAOgT,QAAQpS,OAAOzB,OAASa,EAAOY,OAAOzB,OAE9GwI,EAAM3H,EAAO2F,WAAWgC,IAC9B,IAAI2zB,EAAiB,GACrB,GAAoB,YAAhBrtB,EAAOvH,KAAoB,CAC7B,MAAM60B,EAAkBv7B,EAAOiO,OAAO1I,KAAOoP,KAAKE,MAAM1B,EAAsC,EAAtBnT,EAAOud,cAAqBvd,EAAOiO,OAAOsH,gBAAkBvV,EAAOoT,SAASjU,OACpJ,IAAK,IAAIH,EAAI,EAAGA,EAAIu8B,EAAiBv8B,GAAK,EACpCiP,EAAOutB,aACTF,GAAkBrtB,EAAOutB,aAAan9B,KAAK2B,EAAQhB,EAAGiP,EAAOwtB,aAE7DH,GAAkB,IAAIrtB,EAAOytB,wBAAwBztB,EAAOwtB,kBAAkBxtB,EAAOytB,iBAGzF/zB,EAAIgD,KAAK2wB,GACTt7B,EAAO2F,WAAWg0B,QAAUhyB,EAAI6D,KAAK,IAAIyC,EAAOwtB,aAE9B,aAAhBxtB,EAAOvH,OAEP40B,EADErtB,EAAO0tB,eACQ1tB,EAAO0tB,eAAet9B,KAAK2B,EAAQiO,EAAO0sB,aAAc1sB,EAAO4sB,YAE/D,gBAAgB5sB,EAAO0sB,wCAEtB1sB,EAAO4sB,sBAE3BlzB,EAAIgD,KAAK2wB,IAES,gBAAhBrtB,EAAOvH,OAEP40B,EADErtB,EAAO2tB,kBACQ3tB,EAAO2tB,kBAAkBv9B,KAAK2B,EAAQiO,EAAOmtB,sBAE7C,gBAAgBntB,EAAOmtB,gCAE1CzzB,EAAIgD,KAAK2wB,IAES,WAAhBrtB,EAAOvH,MACT1G,EAAOE,KAAK,mBAAoBF,EAAO2F,WAAWgC,IAAI,KAG1D,OACE,MAAM3H,EAASlC,KACTmQ,EAASjO,EAAOiO,OAAOtI,WAC7B,IAAKsI,EAAOvB,GAAI,OAEhB,IAAI/E,EAAM,eAAEsG,EAAOvB,IACA,IAAf/E,EAAIxI,SAGNa,EAAOiO,OAAOkgB,mBACU,kBAAdlgB,EAAOvB,IACd/E,EAAIxI,OAAS,GACyB,IAAtCa,EAAO2H,IAAI6D,KAAKyC,EAAOvB,IAAIvN,SAE9BwI,EAAM3H,EAAO2H,IAAI6D,KAAKyC,EAAOvB,KAGX,YAAhBuB,EAAOvH,MAAsBuH,EAAO4tB,WACtCl0B,EAAIgC,SAASsE,EAAO6tB,gBAGtBn0B,EAAIgC,SAASsE,EAAO8tB,cAAgB9tB,EAAOvH,MAEvB,YAAhBuH,EAAOvH,MAAsBuH,EAAO8rB,iBACtCpyB,EAAIgC,SAAS,GAAGsE,EAAO8tB,gBAAgB9tB,EAAOvH,gBAC9C1G,EAAO2F,WAAWu0B,mBAAqB,EACnCjsB,EAAOgsB,mBAAqB,IAC9BhsB,EAAOgsB,mBAAqB,IAGZ,gBAAhBhsB,EAAOvH,MAA0BuH,EAAO+sB,qBAC1CrzB,EAAIgC,SAASsE,EAAO+tB,0BAGlB/tB,EAAO4tB,WACTl0B,EAAI9F,GAAG,QAAS,IAAIoM,EAAOwtB,aAAe,SAAiBrvB,GACzDA,EAAEyY,iBACF,IAAI/Z,EAAQ,eAAEhN,MAAMgN,QAAU9K,EAAOiO,OAAOsH,eACxCvV,EAAOiO,OAAO1I,OAAMuF,GAAS9K,EAAOud,cACxCvd,EAAO6c,QAAQ/R,MAInBmB,EAAM3F,OAAOtG,EAAO2F,WAAY,CAC9BgC,MACA+E,GAAI/E,EAAI,OAGZ,UACE,MAAM3H,EAASlC,KACTmQ,EAASjO,EAAOiO,OAAOtI,WAC7B,IAAKsI,EAAOvB,KAAO1M,EAAO2F,WAAW+G,KAAO1M,EAAO2F,WAAWgC,KAAwC,IAAjC3H,EAAO2F,WAAWgC,IAAIxI,OAAc,OACzG,MAAMwI,EAAM3H,EAAO2F,WAAWgC,IAE9BA,EAAIiC,YAAYqE,EAAOqrB,aACvB3xB,EAAIiC,YAAYqE,EAAO8tB,cAAgB9tB,EAAOvH,MAC1C1G,EAAO2F,WAAWg0B,SAAS35B,EAAO2F,WAAWg0B,QAAQ/vB,YAAYqE,EAAOksB,mBACxElsB,EAAO4tB,WACTl0B,EAAIwC,IAAI,QAAS,IAAI8D,EAAOwtB,eAKlC,IAAIQ,GAAe,CACjBn4B,KAAM,aACNmK,OAAQ,CACNtI,WAAY,CACV+G,GAAI,KACJgvB,cAAe,OACfG,WAAW,EACXxC,aAAa,EACbmC,aAAc,KACdI,kBAAmB,KACnBD,eAAgB,KAChBN,aAAc,KACdL,qBAAqB,EACrBt0B,KAAM,UACNqzB,gBAAgB,EAChBE,mBAAoB,EACpBW,sBAAwBsB,GAAWA,EACnCpB,oBAAsBoB,GAAWA,EACjCT,YAAa,2BACbtB,kBAAmB,kCACnB4B,cAAe,qBACfpB,aAAc,4BACdE,WAAY,0BACZvB,YAAa,2BACb8B,qBAAsB,qCACtBY,yBAA0B,yCAC1BF,eAAgB,8BAChB7C,UAAW,2BAGf,SACE,MAAMj5B,EAASlC,KACfmO,EAAM3F,OAAOtG,EAAQ,CACnB2F,WAAY,CACVuoB,KAAM3lB,GAAW2lB,KAAKjqB,KAAKjE,GAC3B4F,OAAQ2C,GAAW3C,OAAO3B,KAAKjE,GAC/ByF,OAAQ8C,GAAW9C,OAAOxB,KAAKjE,GAC/BkG,QAASqC,GAAWrC,QAAQjC,KAAKjE,GACjCk6B,mBAAoB,MAI1Br4B,GAAI,CACF,OACE,MAAM7B,EAASlC,KACfkC,EAAO2F,WAAWuoB,OAClBluB,EAAO2F,WAAWC,SAClB5F,EAAO2F,WAAWF,UAEpB,oBACE,MAAMzF,EAASlC,MACXkC,EAAOiO,OAAO1I,MAEqB,qBAArBvF,EAAOsX,YADvBtX,EAAO2F,WAAWF,UAKtB,kBACE,MAAMzF,EAASlC,KACVkC,EAAOiO,OAAO1I,MACjBvF,EAAO2F,WAAWF,UAGtB,qBACE,MAAMzF,EAASlC,KACXkC,EAAOiO,OAAO1I,OAChBvF,EAAO2F,WAAWC,SAClB5F,EAAO2F,WAAWF,WAGtB,uBACE,MAAMzF,EAASlC,KACVkC,EAAOiO,OAAO1I,OACjBvF,EAAO2F,WAAWC,SAClB5F,EAAO2F,WAAWF,WAGtB,UACE,MAAMzF,EAASlC,KACfkC,EAAO2F,WAAWO,WAEpB,MAAMkG,GACJ,MAAMpM,EAASlC,KACf,GACEkC,EAAOiO,OAAOtI,WAAW+G,IACtB1M,EAAOiO,OAAOtI,WAAW0zB,aACzBr5B,EAAO2F,WAAWgC,IAAIxI,OAAS,IAC9B,eAAEiN,EAAE3L,QAAQoJ,SAAS7J,EAAOiO,OAAOtI,WAAW81B,aAClD,CACA,MAAMlC,EAAWv5B,EAAO2F,WAAWgC,IAAIkC,SAAS7J,EAAOiO,OAAOtI,WAAW2zB,cACxD,IAAbC,EACFv5B,EAAOE,KAAK,iBAAkBF,GAE9BA,EAAOE,KAAK,iBAAkBF,GAEhCA,EAAO2F,WAAWgC,IAAImC,YAAY9J,EAAOiO,OAAOtI,WAAW2zB,iBAMnE,MAAM5wB,GAAY,CAChB,eACE,MAAM1I,EAASlC,KACf,IAAKkC,EAAOiO,OAAOkuB,UAAUzvB,KAAO1M,EAAOm8B,UAAUzvB,GAAI,OACzD,MAAM,UAAEyvB,EAAWvpB,aAAcC,EAAG,SAAEqG,GAAalZ,GAC7C,SACJo8B,EAAQ,UAAEC,EAAS,QAAEC,EAAO,IAAE30B,GAC5Bw0B,EACEluB,EAASjO,EAAOiO,OAAOkuB,UAE7B,IAAII,EAAUH,EACVI,GAAUH,EAAYD,GAAYljB,EAClCrG,GACF2pB,GAAUA,EACNA,EAAS,GACXD,EAAUH,EAAWI,EACrBA,EAAS,IACCA,EAASJ,EAAWC,IAC9BE,EAAUF,EAAYG,IAEfA,EAAS,GAClBD,EAAUH,EAAWI,EACrBA,EAAS,GACAA,EAASJ,EAAWC,IAC7BE,EAAUF,EAAYG,GAEpBx8B,EAAOqS,gBACTiqB,EAAQryB,UAAU,eAAeuyB,cACjCF,EAAQ,GAAGvmB,MAAM9D,MAAWsqB,EAAH,OAEzBD,EAAQryB,UAAU,oBAAoBuyB,WACtCF,EAAQ,GAAGvmB,MAAM7D,OAAYqqB,EAAH,MAExBtuB,EAAOwuB,OACT3E,aAAa93B,EAAOm8B,UAAUpE,SAC9BpwB,EAAI,GAAGoO,MAAM2mB,QAAU,EACvB18B,EAAOm8B,UAAUpE,QAAUxrB,WAAW,KACpC5E,EAAI,GAAGoO,MAAM2mB,QAAU,EACvB/0B,EAAIuC,WAAW,MACd,OAGP,cAAcuS,GACZ,MAAMzc,EAASlC,KACVkC,EAAOiO,OAAOkuB,UAAUzvB,IAAO1M,EAAOm8B,UAAUzvB,IACrD1M,EAAOm8B,UAAUG,QAAQpyB,WAAWuS,IAEtC,aACE,MAAMzc,EAASlC,KACf,IAAKkC,EAAOiO,OAAOkuB,UAAUzvB,KAAO1M,EAAOm8B,UAAUzvB,GAAI,OAEzD,MAAM,UAAEyvB,GAAcn8B,GAChB,QAAEs8B,EAAO,IAAE30B,GAAQw0B,EAEzBG,EAAQ,GAAGvmB,MAAM9D,MAAQ,GACzBqqB,EAAQ,GAAGvmB,MAAM7D,OAAS,GAC1B,MAAMmqB,EAAYr8B,EAAOqS,eAAiB1K,EAAI,GAAGwV,YAAcxV,EAAI,GAAGwQ,aAEhEwkB,EAAU38B,EAAOwS,KAAOxS,EAAOqU,YAC/BuoB,EAAcD,GAAWN,EAAYr8B,EAAOwS,MAClD,IAAI4pB,EAEFA,EADuC,SAArCp8B,EAAOiO,OAAOkuB,UAAUC,SACfC,EAAYM,EAEZpqB,SAASvS,EAAOiO,OAAOkuB,UAAUC,SAAU,IAGpDp8B,EAAOqS,eACTiqB,EAAQ,GAAGvmB,MAAM9D,MAAWmqB,EAAH,KAEzBE,EAAQ,GAAGvmB,MAAM7D,OAAYkqB,EAAH,KAI1Bz0B,EAAI,GAAGoO,MAAM8mB,QADXF,GAAW,EACU,OAEA,GAErB38B,EAAOiO,OAAOkuB,UAAUM,OAC1B90B,EAAI,GAAGoO,MAAM2mB,QAAU,GAEzBzwB,EAAM3F,OAAO61B,EAAW,CACtBE,YACAM,UACAC,cACAR,aAEFD,EAAUx0B,IAAI3H,EAAOiO,OAAOsJ,eAAiBvX,EAAO0f,SAAW,WAAa,eAAe1f,EAAOiO,OAAOkuB,UAAUlD,YAErH,mBAAmB7sB,GACjB,MAAMpM,EAASlC,KACf,OAAIkC,EAAOqS,eACW,eAAXjG,EAAE1F,MAAoC,cAAX0F,EAAE1F,KAAwB0F,EAAEyX,cAAc,GAAGiZ,QAAU1wB,EAAE0wB,QAE3E,eAAX1wB,EAAE1F,MAAoC,cAAX0F,EAAE1F,KAAwB0F,EAAEyX,cAAc,GAAGkZ,QAAU3wB,EAAE2wB,SAE/F,gBAAgB3wB,GACd,MAAMpM,EAASlC,MACT,UAAEq+B,EAAWvpB,aAAcC,GAAQ7S,GACnC,IACJ2H,EAAG,SACHy0B,EAAQ,UACRC,EAAS,aACTW,GACEb,EAEJ,IAAIc,EACJA,GAAkBd,EAAUe,mBAAmB9wB,GAAMzE,EAAI6C,SAASxK,EAAOqS,eAAiB,OAAS,QAC7E,OAAjB2qB,EAAwBA,EAAeZ,EAAW,KAAOC,EAAYD,GAC1Ea,EAAgBtoB,KAAKK,IAAIL,KAAKgB,IAAIsnB,EAAe,GAAI,GACjDpqB,IACFoqB,EAAgB,EAAIA,GAGtB,MAAMpW,EAAW7mB,EAAO6Y,gBAAmB7Y,EAAOsZ,eAAiBtZ,EAAO6Y,gBAAkBokB,EAE5Fj9B,EAAOmZ,eAAe0N,GACtB7mB,EAAOqb,aAAawL,GACpB7mB,EAAOua,oBACPva,EAAO2Z,uBAET,YAAYvN,GACV,MAAMpM,EAASlC,KACTmQ,EAASjO,EAAOiO,OAAOkuB,WACvB,UAAEA,EAAS,WAAEzpB,GAAe1S,GAC5B,IAAE2H,EAAG,QAAE20B,GAAYH,EACzBn8B,EAAOm8B,UAAU9Y,WAAY,EAC7BrjB,EAAOm8B,UAAUa,aAAgB5wB,EAAE3L,SAAW67B,EAAQ,IAAMlwB,EAAE3L,SAAW67B,EACrEH,EAAUe,mBAAmB9wB,GAAKA,EAAE3L,OAAO08B,wBAAwBn9B,EAAOqS,eAAiB,OAAS,OAAS,KACjHjG,EAAEyY,iBACFzY,EAAE+Z,kBAEFzT,EAAWxI,WAAW,KACtBoyB,EAAQpyB,WAAW,KACnBiyB,EAAUiB,gBAAgBhxB,GAE1B0rB,aAAa93B,EAAOm8B,UAAUkB,aAE9B11B,EAAIuC,WAAW,GACX+D,EAAOwuB,MACT90B,EAAI8C,IAAI,UAAW,GAEjBzK,EAAOiO,OAAOwF,SAChBzT,EAAO0S,WAAWjI,IAAI,mBAAoB,QAE5CzK,EAAOE,KAAK,qBAAsBkM,IAEpC,WAAWA,GACT,MAAMpM,EAASlC,MACT,UAAEq+B,EAAS,WAAEzpB,GAAe1S,GAC5B,IAAE2H,EAAG,QAAE20B,GAAYH,EAEpBn8B,EAAOm8B,UAAU9Y,YAClBjX,EAAEyY,eAAgBzY,EAAEyY,iBACnBzY,EAAE6oB,aAAc,EACrBkH,EAAUiB,gBAAgBhxB,GAC1BsG,EAAWxI,WAAW,GACtBvC,EAAIuC,WAAW,GACfoyB,EAAQpyB,WAAW,GACnBlK,EAAOE,KAAK,oBAAqBkM,KAEnC,UAAUA,GACR,MAAMpM,EAASlC,KAETmQ,EAASjO,EAAOiO,OAAOkuB,WACvB,UAAEA,EAAS,WAAEzpB,GAAe1S,GAC5B,IAAE2H,GAAQw0B,EAEXn8B,EAAOm8B,UAAU9Y,YACtBrjB,EAAOm8B,UAAU9Y,WAAY,EACzBrjB,EAAOiO,OAAOwF,UAChBzT,EAAO0S,WAAWjI,IAAI,mBAAoB,IAC1CiI,EAAWxI,WAAW,KAEpB+D,EAAOwuB,OACT3E,aAAa93B,EAAOm8B,UAAUkB,aAC9Br9B,EAAOm8B,UAAUkB,YAAcpxB,EAAM0S,SAAS,KAC5ChX,EAAI8C,IAAI,UAAW,GACnB9C,EAAIuC,WAAW,MACd,MAELlK,EAAOE,KAAK,mBAAoBkM,GAC5B6B,EAAOqvB,eACTt9B,EAAOqe,mBAGX,kBACE,MAAMre,EAASlC,KACf,IAAKkC,EAAOiO,OAAOkuB,UAAUzvB,GAAI,OACjC,MAAM,UACJyvB,EAAS,iBAAEhN,EAAgB,mBAAEC,EAAkB,OAAEnhB,GAC/CjO,EACE2H,EAAMw0B,EAAUx0B,IAChBlH,EAASkH,EAAI,GACb41B,KAAiBnuB,EAAQQ,kBAAmB3B,EAAOyc,mBAAmB,CAAEC,SAAS,EAAOL,SAAS,GACjG1a,KAAkBR,EAAQQ,kBAAmB3B,EAAOyc,mBAAmB,CAAEC,SAAS,EAAML,SAAS,GAClGlb,EAAQC,OAKX5O,EAAO2D,iBAAiB+qB,EAAiB5E,MAAOvqB,EAAOm8B,UAAUqB,YAAaD,GAC9E98B,EAAO2D,iBAAiB+qB,EAAiB3E,KAAMxqB,EAAOm8B,UAAUsB,WAAYF,GAC5E98B,EAAO2D,iBAAiB+qB,EAAiB1E,IAAKzqB,EAAOm8B,UAAUuB,UAAW9tB,KAN1EnP,EAAO2D,iBAAiBgrB,EAAmB7E,MAAOvqB,EAAOm8B,UAAUqB,YAAaD,GAChF,OAAWn5B,iBAAiBgrB,EAAmB5E,KAAMxqB,EAAOm8B,UAAUsB,WAAYF,GAClF,OAAWn5B,iBAAiBgrB,EAAmB3E,IAAKzqB,EAAOm8B,UAAUuB,UAAW9tB,KAOpF,mBACE,MAAM5P,EAASlC,KACf,IAAKkC,EAAOiO,OAAOkuB,UAAUzvB,GAAI,OACjC,MAAM,UACJyvB,EAAS,iBAAEhN,EAAgB,mBAAEC,EAAkB,OAAEnhB,GAC/CjO,EACE2H,EAAMw0B,EAAUx0B,IAChBlH,EAASkH,EAAI,GACb41B,KAAiBnuB,EAAQQ,kBAAmB3B,EAAOyc,mBAAmB,CAAEC,SAAS,EAAOL,SAAS,GACjG1a,KAAkBR,EAAQQ,kBAAmB3B,EAAOyc,mBAAmB,CAAEC,SAAS,EAAML,SAAS,GAClGlb,EAAQC,OAKX5O,EAAO+b,oBAAoB2S,EAAiB5E,MAAOvqB,EAAOm8B,UAAUqB,YAAaD,GACjF98B,EAAO+b,oBAAoB2S,EAAiB3E,KAAMxqB,EAAOm8B,UAAUsB,WAAYF,GAC/E98B,EAAO+b,oBAAoB2S,EAAiB1E,IAAKzqB,EAAOm8B,UAAUuB,UAAW9tB,KAN7EnP,EAAO+b,oBAAoB4S,EAAmB7E,MAAOvqB,EAAOm8B,UAAUqB,YAAaD,GACnF,OAAW/gB,oBAAoB4S,EAAmB5E,KAAMxqB,EAAOm8B,UAAUsB,WAAYF,GACrF,OAAW/gB,oBAAoB4S,EAAmB3E,IAAKzqB,EAAOm8B,UAAUuB,UAAW9tB,KAOvF,OACE,MAAM5P,EAASlC,KACf,IAAKkC,EAAOiO,OAAOkuB,UAAUzvB,GAAI,OACjC,MAAM,UAAEyvB,EAAWx0B,IAAKg2B,GAAc39B,EAChCiO,EAASjO,EAAOiO,OAAOkuB,UAE7B,IAAIx0B,EAAM,eAAEsG,EAAOvB,IACf1M,EAAOiO,OAAOkgB,mBAA0C,kBAAdlgB,EAAOvB,IAAmB/E,EAAIxI,OAAS,GAA0C,IAArCw+B,EAAUnyB,KAAKyC,EAAOvB,IAAIvN,SAClHwI,EAAMg2B,EAAUnyB,KAAKyC,EAAOvB,KAG9B,IAAI4vB,EAAU30B,EAAI6D,KAAK,IAAIxL,EAAOiO,OAAOkuB,UAAUyB,WAC5B,IAAnBtB,EAAQn9B,SACVm9B,EAAU,eAAE,eAAet8B,EAAOiO,OAAOkuB,UAAUyB,qBACnDj2B,EAAIqD,OAAOsxB,IAGbrwB,EAAM3F,OAAO61B,EAAW,CACtBx0B,MACA+E,GAAI/E,EAAI,GACR20B,UACAuB,OAAQvB,EAAQ,KAGdruB,EAAO6vB,WACT3B,EAAU4B,mBAGd,UACE,MAAM/9B,EAASlC,KACfkC,EAAOm8B,UAAU6B,qBAIrB,IAAIC,GAAc,CAChBn6B,KAAM,YACNmK,OAAQ,CACNkuB,UAAW,CACTzvB,GAAI,KACJ0vB,SAAU,OACVK,MAAM,EACNqB,WAAW,EACXR,eAAe,EACfrE,UAAW,wBACX2E,UAAW,0BAGf,SACE,MAAM59B,EAASlC,KACfmO,EAAM3F,OAAOtG,EAAQ,CACnBm8B,UAAW,CACTjO,KAAMxlB,GAAUwlB,KAAKjqB,KAAKjE,GAC1BkG,QAASwC,GAAUxC,QAAQjC,KAAKjE,GAChCgS,WAAYtJ,GAAUsJ,WAAW/N,KAAKjE,GACtCqb,aAAc3S,GAAU2S,aAAapX,KAAKjE,GAC1CgY,cAAetP,GAAUsP,cAAc/T,KAAKjE,GAC5C+9B,gBAAiBr1B,GAAUq1B,gBAAgB95B,KAAKjE,GAChDg+B,iBAAkBt1B,GAAUs1B,iBAAiB/5B,KAAKjE,GAClDo9B,gBAAiB10B,GAAU00B,gBAAgBn5B,KAAKjE,GAChDk9B,mBAAoBx0B,GAAUw0B,mBAAmBj5B,KAAKjE,GACtDw9B,YAAa90B,GAAU80B,YAAYv5B,KAAKjE,GACxCy9B,WAAY/0B,GAAU+0B,WAAWx5B,KAAKjE,GACtC09B,UAAWh1B,GAAUg1B,UAAUz5B,KAAKjE,GACpCqjB,WAAW,EACX0U,QAAS,KACTsF,YAAa,SAInBx7B,GAAI,CACF,OACE,MAAM7B,EAASlC,KACfkC,EAAOm8B,UAAUjO,OACjBluB,EAAOm8B,UAAUnqB,aACjBhS,EAAOm8B,UAAU9gB,gBAEnB,SACE,MAAMrb,EAASlC,KACfkC,EAAOm8B,UAAUnqB,cAEnB,SACE,MAAMhS,EAASlC,KACfkC,EAAOm8B,UAAUnqB,cAEnB,iBACE,MAAMhS,EAASlC,KACfkC,EAAOm8B,UAAUnqB,cAEnB,eACE,MAAMhS,EAASlC,KACfkC,EAAOm8B,UAAU9gB,gBAEnB,cAAcoB,GACZ,MAAMzc,EAASlC,KACfkC,EAAOm8B,UAAUnkB,cAAcyE,IAEjC,UACE,MAAMzc,EAASlC,KACfkC,EAAOm8B,UAAUj2B,aAKvB,MAAMg4B,GAAW,CACf,aAAaxxB,EAAIwM,GACf,MAAMlZ,EAASlC,MACT,IAAE+U,GAAQ7S,EAEV2H,EAAM,eAAE+E,GACR6qB,EAAY1kB,GAAO,EAAI,EAEvBsrB,EAAIx2B,EAAIoC,KAAK,yBAA2B,IAC9C,IAAIyR,EAAI7T,EAAIoC,KAAK,0BACb0R,EAAI9T,EAAIoC,KAAK,0BACjB,MAAMkxB,EAAQtzB,EAAIoC,KAAK,8BACjB2yB,EAAU/0B,EAAIoC,KAAK,gCAwBzB,GAtBIyR,GAAKC,GACPD,EAAIA,GAAK,IACTC,EAAIA,GAAK,KACAzb,EAAOqS,gBAChBmJ,EAAI2iB,EACJ1iB,EAAI,MAEJA,EAAI0iB,EACJ3iB,EAAI,KAIJA,EADE,EAAIrX,QAAQ,MAAQ,EACfoO,SAASiJ,EAAG,IAAMtC,EAAWqe,EAAhC,IAEG/b,EAAItC,EAAWqe,EAAlB,KAGJ9b,EADE,EAAItX,QAAQ,MAAQ,EACfoO,SAASkJ,EAAG,IAAMvC,EAArB,IAEGuC,EAAIvC,EAAP,KAGiB,qBAAZwjB,GAAuC,OAAZA,EAAkB,CACtD,MAAM0B,EAAiB1B,GAAYA,EAAU,IAAM,EAAI/nB,KAAK8B,IAAIyC,IAChEvR,EAAI,GAAGoO,MAAM2mB,QAAU0B,EAEzB,GAAqB,qBAAVnD,GAAmC,OAAVA,EAClCtzB,EAAIsC,UAAU,eAAeuR,MAAMC,eAC9B,CACL,MAAM4iB,EAAepD,GAAUA,EAAQ,IAAM,EAAItmB,KAAK8B,IAAIyC,IAC1DvR,EAAIsC,UAAU,eAAeuR,MAAMC,iBAAiB4iB,QAGxD,eACE,MAAMr+B,EAASlC,MACT,IACJ6J,EAAG,OAAE/G,EAAM,SAAEsY,EAAQ,SAAE9F,GACrBpT,EACJ2H,EAAI8D,SAAS,4IACVf,KAAK,CAACI,EAAO4B,KACZ1M,EAAOs+B,SAASC,aAAa7xB,EAAIwM,KAErCtY,EAAO8J,KAAK,CAAC8I,EAAYsc,KACvB,IAAIlX,EAAgBkX,EAAQ5W,SACxBlZ,EAAOiO,OAAOsH,eAAiB,GAAqC,SAAhCvV,EAAOiO,OAAO6G,gBACpD8D,GAAiBjE,KAAKE,KAAKrB,EAAa,GAAM0F,GAAY9F,EAASjU,OAAS,IAE9EyZ,EAAgBjE,KAAKgB,IAAIhB,KAAKK,IAAI4D,GAAgB,GAAI,GACtD,eAAEkX,GAAStkB,KAAK,4IACbd,KAAK,CAACI,EAAO4B,KACZ1M,EAAOs+B,SAASC,aAAa7xB,EAAIkM,QAIzC,cAAc6D,EAAW3e,KAAKmQ,OAAO4J,OACnC,MAAM7X,EAASlC,MACT,IAAE6J,GAAQ3H,EAChB2H,EAAI6D,KAAK,4IACNd,KAAK,CAACI,EAAO0zB,KACZ,MAAMC,EAAc,eAAED,GACtB,IAAIE,EAAmBnsB,SAASksB,EAAY10B,KAAK,iCAAkC,KAAO0S,EACzE,IAAbA,IAAgBiiB,EAAmB,GACvCD,EAAYv0B,WAAWw0B,OAK/B,IAAIC,GAAa,CACf76B,KAAM,WACNmK,OAAQ,CACNqwB,SAAU,CACRrrB,SAAS,IAGb,SACE,MAAMjT,EAASlC,KACfmO,EAAM3F,OAAOtG,EAAQ,CACnBs+B,SAAU,CACRC,aAAcL,GAASK,aAAat6B,KAAKjE,GACzCqb,aAAc6iB,GAAS7iB,aAAapX,KAAKjE,GACzCgY,cAAekmB,GAASlmB,cAAc/T,KAAKjE,OAIjD6B,GAAI,CACF,aACE,MAAM7B,EAASlC,KACVkC,EAAOiO,OAAOqwB,SAASrrB,UAC5BjT,EAAOiO,OAAOwJ,qBAAsB,EACpCzX,EAAOqrB,eAAe5T,qBAAsB,IAE9C,OACE,MAAMzX,EAASlC,KACVkC,EAAOiO,OAAOqwB,SAASrrB,SAC5BjT,EAAOs+B,SAASjjB,gBAElB,eACE,MAAMrb,EAASlC,KACVkC,EAAOiO,OAAOqwB,SAASrrB,SAC5BjT,EAAOs+B,SAASjjB,gBAElB,cAAcoB,GACZ,MAAMzc,EAASlC,KACVkC,EAAOiO,OAAOqwB,SAASrrB,SAC5BjT,EAAOs+B,SAAStmB,cAAcyE,MAKpC,MAAMmiB,GAAO,CAEX,0BAA0BxyB,GACxB,GAAIA,EAAEyX,cAAc1kB,OAAS,EAAG,OAAO,EACvC,MAAM0/B,EAAKzyB,EAAEyX,cAAc,GAAGC,MACxBgb,EAAK1yB,EAAEyX,cAAc,GAAGG,MACxB+a,EAAK3yB,EAAEyX,cAAc,GAAGC,MACxBkb,EAAK5yB,EAAEyX,cAAc,GAAGG,MACxBwD,EAAW7S,KAAKiR,MAAOmZ,EAAKF,IAAO,GAAOG,EAAKF,IAAO,GAC5D,OAAOtX,GAGT,eAAepb,GACb,MAAMpM,EAASlC,KACTmQ,EAASjO,EAAOiO,OAAOgxB,KACvBA,EAAOj/B,EAAOi/B,MACd,QAAEC,GAAYD,EAGpB,GAFAA,EAAKE,oBAAqB,EAC1BF,EAAKG,kBAAmB,GACnBhwB,EAAQW,SAAU,CACrB,GAAe,eAAX3D,EAAE1F,MAAqC,eAAX0F,EAAE1F,MAAyB0F,EAAEyX,cAAc1kB,OAAS,EAClF,OAEF8/B,EAAKE,oBAAqB,EAC1BD,EAAQG,WAAaT,GAAKU,0BAA0BlzB,GAEjD8yB,EAAQ3L,UAAa2L,EAAQ3L,SAASp0B,SACzC+/B,EAAQ3L,SAAW,eAAEnnB,EAAE3L,QAAQ8K,QAAQ,IAAIvL,EAAOiO,OAAOrP,YACzB,IAA5BsgC,EAAQ3L,SAASp0B,SAAc+/B,EAAQ3L,SAAWvzB,EAAOY,OAAOmK,GAAG/K,EAAOkY,cAC9EgnB,EAAQK,SAAWL,EAAQ3L,SAAS/nB,KAAK,kDACzC0zB,EAAQM,aAAeN,EAAQK,SAAS12B,OAAO,IAAIoF,EAAOvP,gBAC1DwgC,EAAQO,SAAWP,EAAQM,aAAaz1B,KAAK,qBAAuBkE,EAAOwxB,SACvC,IAAhCP,EAAQM,aAAargC,SAKvB+/B,EAAQK,UACVL,EAAQK,SAASr1B,WAAW,GAE9BlK,EAAOi/B,KAAKS,WAAY,GAPpBR,EAAQK,cAAW18B,GASzB,gBAAgBuJ,GACd,MAAMpM,EAASlC,KACTmQ,EAASjO,EAAOiO,OAAOgxB,KACvBA,EAAOj/B,EAAOi/B,MACd,QAAEC,GAAYD,EACpB,IAAK7vB,EAAQW,SAAU,CACrB,GAAe,cAAX3D,EAAE1F,MAAoC,cAAX0F,EAAE1F,MAAwB0F,EAAEyX,cAAc1kB,OAAS,EAChF,OAEF8/B,EAAKG,kBAAmB,EACxBF,EAAQS,UAAYf,GAAKU,0BAA0BlzB,GAEhD8yB,EAAQK,UAAwC,IAA5BL,EAAQK,SAASpgC,SACtCiQ,EAAQW,SACVkvB,EAAKhE,MAAQ7uB,EAAE6uB,MAAQgE,EAAKZ,aAE5BY,EAAKhE,MAASiE,EAAQS,UAAYT,EAAQG,WAAcJ,EAAKZ,aAE3DY,EAAKhE,MAAQiE,EAAQO,WACvBR,EAAKhE,MAASiE,EAAQO,SAAW,GAAQR,EAAKhE,MAAQiE,EAAQO,SAAY,IAAM,IAE9ER,EAAKhE,MAAQhtB,EAAO6d,WACtBmT,EAAKhE,MAAShtB,EAAO6d,SAAW,GAAQ7d,EAAO6d,SAAWmT,EAAKhE,MAAS,IAAM,IAEhFiE,EAAQK,SAASt1B,UAAU,4BAA4Bg1B,EAAKhE,YAE9D,aAAa7uB,GACX,MAAMpM,EAASlC,KACTmQ,EAASjO,EAAOiO,OAAOgxB,KACvBA,EAAOj/B,EAAOi/B,MACd,QAAEC,GAAYD,EACpB,IAAK7vB,EAAQW,SAAU,CACrB,IAAKkvB,EAAKE,qBAAuBF,EAAKG,iBACpC,OAEF,GAAe,aAAXhzB,EAAE1F,MAAmC,aAAX0F,EAAE1F,MAAuB0F,EAAEmZ,eAAepmB,OAAS,IAAMwhB,EAAOM,QAC5F,OAEFge,EAAKE,oBAAqB,EAC1BF,EAAKG,kBAAmB,EAErBF,EAAQK,UAAwC,IAA5BL,EAAQK,SAASpgC,SAC1C8/B,EAAKhE,MAAQtmB,KAAKK,IAAIL,KAAKgB,IAAIspB,EAAKhE,MAAOiE,EAAQO,UAAWxxB,EAAO6d,UACrEoT,EAAQK,SAASr1B,WAAWlK,EAAOiO,OAAO4J,OAAO5N,UAAU,4BAA4Bg1B,EAAKhE,UAC5FgE,EAAKZ,aAAeY,EAAKhE,MACzBgE,EAAKS,WAAY,EACE,IAAfT,EAAKhE,QAAaiE,EAAQ3L,cAAW1wB,KAE3C,aAAauJ,GACX,MAAMpM,EAASlC,KACTmhC,EAAOj/B,EAAOi/B,MACd,QAAEC,EAAO,MAAElS,GAAUiS,EACtBC,EAAQK,UAAwC,IAA5BL,EAAQK,SAASpgC,SACtC6tB,EAAM3J,YACN1C,EAAOM,SAAW7U,EAAE4Z,YAAY5Z,EAAEyY,iBACtCmI,EAAM3J,WAAY,EAClB2J,EAAM4S,aAAapkB,EAAe,eAAXpP,EAAE1F,KAAwB0F,EAAEyX,cAAc,GAAGC,MAAQ1X,EAAE0X,MAC9EkJ,EAAM4S,aAAankB,EAAe,eAAXrP,EAAE1F,KAAwB0F,EAAEyX,cAAc,GAAGG,MAAQ5X,EAAE4X,SAEhF,YAAY5X,GACV,MAAMpM,EAASlC,KACTmhC,EAAOj/B,EAAOi/B,MACd,QAAEC,EAAO,MAAElS,EAAK,SAAEvF,GAAawX,EACrC,IAAKC,EAAQK,UAAwC,IAA5BL,EAAQK,SAASpgC,OAAc,OAExD,GADAa,EAAO0jB,YAAa,GACfsJ,EAAM3J,YAAc6b,EAAQ3L,SAAU,OAEtCvG,EAAM1J,UACT0J,EAAM/a,MAAQitB,EAAQK,SAAS,GAAGpiB,YAClC6P,EAAM9a,OAASgtB,EAAQK,SAAS,GAAGpnB,aACnC6U,EAAM/I,OAAShY,EAAMiP,aAAagkB,EAAQM,aAAa,GAAI,MAAQ,EACnExS,EAAM9I,OAASjY,EAAMiP,aAAagkB,EAAQM,aAAa,GAAI,MAAQ,EACnEN,EAAQW,WAAaX,EAAQ3L,SAAS,GAAGpW,YACzC+hB,EAAQY,YAAcZ,EAAQ3L,SAAS,GAAGpb,aAC1C+mB,EAAQM,aAAat1B,WAAW,GAC5BlK,EAAO6S,MACTma,EAAM/I,QAAU+I,EAAM/I,OACtB+I,EAAM9I,QAAU8I,EAAM9I,SAI1B,MAAM6b,EAAc/S,EAAM/a,MAAQgtB,EAAKhE,MACjC+E,EAAehT,EAAM9a,OAAS+sB,EAAKhE,MAEzC,KAAI8E,EAAcb,EAAQW,YAAcG,EAAed,EAAQY,aAA/D,CAUA,GARA9S,EAAMiT,KAAOtrB,KAAKgB,IAAMupB,EAAQW,WAAa,EAAME,EAAc,EAAK,GACtE/S,EAAMkT,MAAQlT,EAAMiT,KACpBjT,EAAMmT,KAAOxrB,KAAKgB,IAAMupB,EAAQY,YAAc,EAAME,EAAe,EAAK,GACxEhT,EAAMoT,MAAQpT,EAAMmT,KAEpBnT,EAAMqT,eAAe7kB,EAAe,cAAXpP,EAAE1F,KAAuB0F,EAAEyX,cAAc,GAAGC,MAAQ1X,EAAE0X,MAC/EkJ,EAAMqT,eAAe5kB,EAAe,cAAXrP,EAAE1F,KAAuB0F,EAAEyX,cAAc,GAAGG,MAAQ5X,EAAE4X,OAE1EgJ,EAAM1J,UAAY2b,EAAKS,UAAW,CACrC,GACE1/B,EAAOqS,iBAEJsC,KAAKC,MAAMoY,EAAMiT,QAAUtrB,KAAKC,MAAMoY,EAAM/I,SAAW+I,EAAMqT,eAAe7kB,EAAIwR,EAAM4S,aAAapkB,GAChG7G,KAAKC,MAAMoY,EAAMkT,QAAUvrB,KAAKC,MAAMoY,EAAM/I,SAAW+I,EAAMqT,eAAe7kB,EAAIwR,EAAM4S,aAAapkB,GAIzG,YADAwR,EAAM3J,WAAY,GAElB,IACCrjB,EAAOqS,iBAELsC,KAAKC,MAAMoY,EAAMmT,QAAUxrB,KAAKC,MAAMoY,EAAM9I,SAAW8I,EAAMqT,eAAe5kB,EAAIuR,EAAM4S,aAAankB,GAChG9G,KAAKC,MAAMoY,EAAMoT,QAAUzrB,KAAKC,MAAMoY,EAAM9I,SAAW8I,EAAMqT,eAAe5kB,EAAIuR,EAAM4S,aAAankB,GAIzG,YADAuR,EAAM3J,WAAY,GAIlBjX,EAAE4Z,YACJ5Z,EAAEyY,iBAEJzY,EAAE+Z,kBAEF6G,EAAM1J,SAAU,EAChB0J,EAAMpJ,SAAYoJ,EAAMqT,eAAe7kB,EAAIwR,EAAM4S,aAAapkB,EAAKwR,EAAM/I,OACzE+I,EAAMjJ,SAAYiJ,EAAMqT,eAAe5kB,EAAIuR,EAAM4S,aAAankB,EAAKuR,EAAM9I,OAErE8I,EAAMpJ,SAAWoJ,EAAMiT,OACzBjT,EAAMpJ,SAAYoJ,EAAMiT,KAAO,GAAQjT,EAAMiT,KAAOjT,EAAMpJ,SAAY,IAAM,IAE1EoJ,EAAMpJ,SAAWoJ,EAAMkT,OACzBlT,EAAMpJ,SAAYoJ,EAAMkT,KAAO,GAAQlT,EAAMpJ,SAAWoJ,EAAMkT,KAAQ,IAAM,IAG1ElT,EAAMjJ,SAAWiJ,EAAMmT,OACzBnT,EAAMjJ,SAAYiJ,EAAMmT,KAAO,GAAQnT,EAAMmT,KAAOnT,EAAMjJ,SAAY,IAAM,IAE1EiJ,EAAMjJ,SAAWiJ,EAAMoT,OACzBpT,EAAMjJ,SAAYiJ,EAAMoT,KAAO,GAAQpT,EAAMjJ,SAAWiJ,EAAMoT,KAAQ,IAAM,IAIzE3Y,EAAS6Y,gBAAe7Y,EAAS6Y,cAAgBtT,EAAMqT,eAAe7kB,GACtEiM,EAAS8Y,gBAAe9Y,EAAS8Y,cAAgBvT,EAAMqT,eAAe5kB,GACtEgM,EAAS+Y,WAAU/Y,EAAS+Y,SAAWh0B,KAAKC,OACjDgb,EAASjM,GAAKwR,EAAMqT,eAAe7kB,EAAIiM,EAAS6Y,gBAAkB9zB,KAAKC,MAAQgb,EAAS+Y,UAAY,EACpG/Y,EAAShM,GAAKuR,EAAMqT,eAAe5kB,EAAIgM,EAAS8Y,gBAAkB/zB,KAAKC,MAAQgb,EAAS+Y,UAAY,EAChG7rB,KAAK8B,IAAIuW,EAAMqT,eAAe7kB,EAAIiM,EAAS6Y,eAAiB,IAAG7Y,EAASjM,EAAI,GAC5E7G,KAAK8B,IAAIuW,EAAMqT,eAAe5kB,EAAIgM,EAAS8Y,eAAiB,IAAG9Y,EAAShM,EAAI,GAChFgM,EAAS6Y,cAAgBtT,EAAMqT,eAAe7kB,EAC9CiM,EAAS8Y,cAAgBvT,EAAMqT,eAAe5kB,EAC9CgM,EAAS+Y,SAAWh0B,KAAKC,MAEzByyB,EAAQM,aAAav1B,UAAU,eAAe+iB,EAAMpJ,eAAeoJ,EAAMjJ,mBAE3E,aACE,MAAM/jB,EAASlC,KACTmhC,EAAOj/B,EAAOi/B,MACd,QAAEC,EAAO,MAAElS,EAAK,SAAEvF,GAAawX,EACrC,IAAKC,EAAQK,UAAwC,IAA5BL,EAAQK,SAASpgC,OAAc,OACxD,IAAK6tB,EAAM3J,YAAc2J,EAAM1J,QAG7B,OAFA0J,EAAM3J,WAAY,OAClB2J,EAAM1J,SAAU,GAGlB0J,EAAM3J,WAAY,EAClB2J,EAAM1J,SAAU,EAChB,IAAImd,EAAoB,IACpBC,EAAoB,IACxB,MAAMC,EAAoBlZ,EAASjM,EAAIilB,EACjCG,EAAe5T,EAAMpJ,SAAW+c,EAChCE,EAAoBpZ,EAAShM,EAAIilB,EACjCI,EAAe9T,EAAMjJ,SAAW8c,EAGnB,IAAfpZ,EAASjM,IAASilB,EAAoB9rB,KAAK8B,KAAKmqB,EAAe5T,EAAMpJ,UAAY6D,EAASjM,IAC3E,IAAfiM,EAAShM,IAASilB,EAAoB/rB,KAAK8B,KAAKqqB,EAAe9T,EAAMjJ,UAAY0D,EAAShM,IAC9F,MAAMmM,EAAmBjT,KAAKK,IAAIyrB,EAAmBC,GAErD1T,EAAMpJ,SAAWgd,EACjB5T,EAAMjJ,SAAW+c,EAGjB,MAAMf,EAAc/S,EAAM/a,MAAQgtB,EAAKhE,MACjC+E,EAAehT,EAAM9a,OAAS+sB,EAAKhE,MACzCjO,EAAMiT,KAAOtrB,KAAKgB,IAAMupB,EAAQW,WAAa,EAAME,EAAc,EAAK,GACtE/S,EAAMkT,MAAQlT,EAAMiT,KACpBjT,EAAMmT,KAAOxrB,KAAKgB,IAAMupB,EAAQY,YAAc,EAAME,EAAe,EAAK,GACxEhT,EAAMoT,MAAQpT,EAAMmT,KACpBnT,EAAMpJ,SAAWjP,KAAKK,IAAIL,KAAKgB,IAAIqX,EAAMpJ,SAAUoJ,EAAMkT,MAAOlT,EAAMiT,MACtEjT,EAAMjJ,SAAWpP,KAAKK,IAAIL,KAAKgB,IAAIqX,EAAMjJ,SAAUiJ,EAAMoT,MAAOpT,EAAMmT,MAEtEjB,EAAQM,aAAat1B,WAAW0d,GAAkB3d,UAAU,eAAe+iB,EAAMpJ,eAAeoJ,EAAMjJ,kBAExG,kBACE,MAAM/jB,EAASlC,KACTmhC,EAAOj/B,EAAOi/B,MACd,QAAEC,GAAYD,EAChBC,EAAQ3L,UAAYvzB,EAAOya,gBAAkBza,EAAOkY,cAClDgnB,EAAQK,UACVL,EAAQK,SAASt1B,UAAU,+BAEzBi1B,EAAQM,cACVN,EAAQM,aAAav1B,UAAU,sBAGjCg1B,EAAKhE,MAAQ,EACbgE,EAAKZ,aAAe,EAEpBa,EAAQ3L,cAAW1wB,EACnBq8B,EAAQK,cAAW18B,EACnBq8B,EAAQM,kBAAe38B,IAI3B,OAAOuJ,GACL,MAAMpM,EAASlC,KACTmhC,EAAOj/B,EAAOi/B,KAEhBA,EAAKhE,OAAwB,IAAfgE,EAAKhE,MAErBgE,EAAK8B,MAGL9B,EAAK+B,GAAG50B,IAGZ,GAAGA,GACD,MAAMpM,EAASlC,KAETmhC,EAAOj/B,EAAOi/B,KACdhxB,EAASjO,EAAOiO,OAAOgxB,MACvB,QAAEC,EAAO,MAAElS,GAAUiS,EAW3B,GATKC,EAAQ3L,WACPvzB,EAAOiO,OAAO+E,SAAWhT,EAAOiO,OAAO+E,QAAQC,SAAWjT,EAAOgT,QACnEksB,EAAQ3L,SAAWvzB,EAAO0S,WAAWjH,SAAS,IAAIzL,EAAOiO,OAAO6L,kBAEhEolB,EAAQ3L,SAAWvzB,EAAOY,OAAOmK,GAAG/K,EAAOkY,aAE7CgnB,EAAQK,SAAWL,EAAQ3L,SAAS/nB,KAAK,kDACzC0zB,EAAQM,aAAeN,EAAQK,SAAS12B,OAAO,IAAIoF,EAAOvP,kBAEvDwgC,EAAQK,UAAwC,IAA5BL,EAAQK,SAASpgC,OAAc,OAIxD,IAAI8hC,EACAC,EACAC,EACAC,EACA1b,EACAC,EACA0b,EACAC,EACAC,EACAC,EACAzB,EACAC,EACAyB,EACAC,EACAC,EACAC,EACA/B,EACAC,EAnBJZ,EAAQ3L,SAAS5pB,SAAS,GAAGsE,EAAO4zB,kBAqBA,qBAAzB7U,EAAM4S,aAAapkB,GAAqBpP,GACjD60B,EAAoB,aAAX70B,EAAE1F,KAAsB0F,EAAEmZ,eAAe,GAAGzB,MAAQ1X,EAAE0X,MAC/Dod,EAAoB,aAAX90B,EAAE1F,KAAsB0F,EAAEmZ,eAAe,GAAGvB,MAAQ5X,EAAE4X,QAE/Did,EAASjU,EAAM4S,aAAapkB,EAC5B0lB,EAASlU,EAAM4S,aAAankB,GAG9BwjB,EAAKhE,MAAQiE,EAAQM,aAAaz1B,KAAK,qBAAuBkE,EAAOwxB,SACrER,EAAKZ,aAAea,EAAQM,aAAaz1B,KAAK,qBAAuBkE,EAAOwxB,SACxErzB,GACFyzB,EAAaX,EAAQ3L,SAAS,GAAGpW,YACjC2iB,EAAcZ,EAAQ3L,SAAS,GAAGpb,aAClCgpB,EAAUjC,EAAQ3L,SAAS/oB,SAASsqB,KACpCsM,EAAUlC,EAAQ3L,SAAS/oB,SAASwqB,IACpCtP,EAASyb,EAAWtB,EAAa,EAAMoB,EACvCtb,EAASyb,EAAWtB,EAAc,EAAMoB,EAExCK,EAAarC,EAAQK,SAAS,GAAGpiB,YACjCqkB,EAActC,EAAQK,SAAS,GAAGpnB,aAClC4nB,EAAcwB,EAAatC,EAAKhE,MAChC+E,EAAewB,EAAcvC,EAAKhE,MAElCwG,EAAgB9sB,KAAKgB,IAAMkqB,EAAa,EAAME,EAAc,EAAK,GACjE2B,EAAgB/sB,KAAKgB,IAAMmqB,EAAc,EAAME,EAAe,EAAK,GACnE2B,GAAiBF,EACjBG,GAAiBF,EAEjBL,EAAa3b,EAAQuZ,EAAKhE,MAC1BqG,EAAa3b,EAAQsZ,EAAKhE,MAEtBoG,EAAaI,IACfJ,EAAaI,GAEXJ,EAAaM,IACfN,EAAaM,GAGXL,EAAaI,IACfJ,EAAaI,GAEXJ,EAAaM,IACfN,EAAaM,KAGfP,EAAa,EACbC,EAAa,GAEfpC,EAAQM,aAAat1B,WAAW,KAAKD,UAAU,eAAeo3B,QAAiBC,UAC/EpC,EAAQK,SAASr1B,WAAW,KAAKD,UAAU,4BAA4Bg1B,EAAKhE,WAE9E,MACE,MAAMj7B,EAASlC,KAETmhC,EAAOj/B,EAAOi/B,KACdhxB,EAASjO,EAAOiO,OAAOgxB,MACvB,QAAEC,GAAYD,EAEfC,EAAQ3L,WACPvzB,EAAOiO,OAAO+E,SAAWhT,EAAOiO,OAAO+E,QAAQC,SAAWjT,EAAOgT,QACnEksB,EAAQ3L,SAAWvzB,EAAO0S,WAAWjH,SAAS,IAAIzL,EAAOiO,OAAO6L,kBAEhEolB,EAAQ3L,SAAWvzB,EAAOY,OAAOmK,GAAG/K,EAAOkY,aAE7CgnB,EAAQK,SAAWL,EAAQ3L,SAAS/nB,KAAK,kDACzC0zB,EAAQM,aAAeN,EAAQK,SAAS12B,OAAO,IAAIoF,EAAOvP,iBAEvDwgC,EAAQK,UAAwC,IAA5BL,EAAQK,SAASpgC,SAE1C8/B,EAAKhE,MAAQ,EACbgE,EAAKZ,aAAe,EACpBa,EAAQM,aAAat1B,WAAW,KAAKD,UAAU,sBAC/Ci1B,EAAQK,SAASr1B,WAAW,KAAKD,UAAU,+BAC3Ci1B,EAAQ3L,SAAS3pB,YAAY,GAAGqE,EAAO4zB,kBACvC3C,EAAQ3L,cAAW1wB,IAGrB,SACE,MAAM7C,EAASlC,KACTmhC,EAAOj/B,EAAOi/B,KACpB,GAAIA,EAAKhsB,QAAS,OAClBgsB,EAAKhsB,SAAU,EAEf,MAAMrD,IAA+C,eAA7B5P,EAAOqqB,YAAYE,QAA0Bnb,EAAQQ,kBAAmB5P,EAAOiO,OAAOyc,mBAAmB,CAAEC,SAAS,EAAML,SAAS,GACrJwX,GAA4B1yB,EAAQQ,iBAAkB,CAAE+a,SAAS,EAAOL,SAAS,GAEjFyX,EAAgB,IAAI/hC,EAAOiO,OAAOrP,WAGpCwQ,EAAQW,UACV/P,EAAO0S,WAAW7Q,GAAG,eAAgBkgC,EAAe9C,EAAK+C,eAAgBpyB,GACzE5P,EAAO0S,WAAW7Q,GAAG,gBAAiBkgC,EAAe9C,EAAKgD,gBAAiBryB,GAC3E5P,EAAO0S,WAAW7Q,GAAG,aAAckgC,EAAe9C,EAAKiD,aAActyB,IAC/B,eAA7B5P,EAAOqqB,YAAYE,QAC5BvqB,EAAO0S,WAAW7Q,GAAG7B,EAAOqqB,YAAYE,MAAOwX,EAAe9C,EAAK+C,eAAgBpyB,GACnF5P,EAAO0S,WAAW7Q,GAAG7B,EAAOqqB,YAAYG,KAAMuX,EAAe9C,EAAKgD,gBAAiBH,GACnF9hC,EAAO0S,WAAW7Q,GAAG7B,EAAOqqB,YAAYI,IAAKsX,EAAe9C,EAAKiD,aAActyB,GAC3E5P,EAAOqqB,YAAYO,QACrB5qB,EAAO0S,WAAW7Q,GAAG7B,EAAOqqB,YAAYO,OAAQmX,EAAe9C,EAAKiD,aAActyB,IAKtF5P,EAAO0S,WAAW7Q,GAAG7B,EAAOqqB,YAAYG,KAAM,IAAIxqB,EAAOiO,OAAOgxB,KAAKvgC,eAAkBugC,EAAK5Z,YAAayc,IAE3G,UACE,MAAM9hC,EAASlC,KACTmhC,EAAOj/B,EAAOi/B,KACpB,IAAKA,EAAKhsB,QAAS,OAEnBjT,EAAOi/B,KAAKhsB,SAAU,EAEtB,MAAMrD,IAA+C,eAA7B5P,EAAOqqB,YAAYE,QAA0Bnb,EAAQQ,kBAAmB5P,EAAOiO,OAAOyc,mBAAmB,CAAEC,SAAS,EAAML,SAAS,GACrJwX,GAA4B1yB,EAAQQ,iBAAkB,CAAE+a,SAAS,EAAOL,SAAS,GAEjFyX,EAAgB,IAAI/hC,EAAOiO,OAAOrP,WAGpCwQ,EAAQW,UACV/P,EAAO0S,WAAWvI,IAAI,eAAgB43B,EAAe9C,EAAK+C,eAAgBpyB,GAC1E5P,EAAO0S,WAAWvI,IAAI,gBAAiB43B,EAAe9C,EAAKgD,gBAAiBryB,GAC5E5P,EAAO0S,WAAWvI,IAAI,aAAc43B,EAAe9C,EAAKiD,aAActyB,IAChC,eAA7B5P,EAAOqqB,YAAYE,QAC5BvqB,EAAO0S,WAAWvI,IAAInK,EAAOqqB,YAAYE,MAAOwX,EAAe9C,EAAK+C,eAAgBpyB,GACpF5P,EAAO0S,WAAWvI,IAAInK,EAAOqqB,YAAYG,KAAMuX,EAAe9C,EAAKgD,gBAAiBH,GACpF9hC,EAAO0S,WAAWvI,IAAInK,EAAOqqB,YAAYI,IAAKsX,EAAe9C,EAAKiD,aAActyB,GAC5E5P,EAAOqqB,YAAYO,QACrB5qB,EAAO0S,WAAWvI,IAAInK,EAAOqqB,YAAYO,OAAQmX,EAAe9C,EAAKiD,aAActyB,IAKvF5P,EAAO0S,WAAWvI,IAAInK,EAAOqqB,YAAYG,KAAM,IAAIxqB,EAAOiO,OAAOgxB,KAAKvgC,eAAkBugC,EAAK5Z,YAAayc,KAI9G,IAAIK,GAAS,CACXr+B,KAAM,OACNmK,OAAQ,CACNgxB,KAAM,CACJhsB,SAAS,EACTwsB,SAAU,EACV3T,SAAU,EACVsW,QAAQ,EACR1jC,eAAgB,wBAChBmjC,iBAAkB,wBAGtB,SACE,MAAM7hC,EAASlC,KACTmhC,EAAO,CACXhsB,SAAS,EACTgoB,MAAO,EACPoD,aAAc,EACdqB,WAAW,EACXR,QAAS,CACP3L,cAAU1wB,EACVg9B,gBAAYh9B,EACZi9B,iBAAaj9B,EACb08B,cAAU18B,EACV28B,kBAAc38B,EACd48B,SAAU,GAEZzS,MAAO,CACL3J,eAAWxgB,EACXygB,aAASzgB,EACT+gB,cAAU/gB,EACVkhB,cAAUlhB,EACVo9B,UAAMp9B,EACNs9B,UAAMt9B,EACNq9B,UAAMr9B,EACNu9B,UAAMv9B,EACNoP,WAAOpP,EACPqP,YAAQrP,EACRohB,YAAQphB,EACRqhB,YAAQrhB,EACR+8B,aAAc,GACdS,eAAgB,IAElB5Y,SAAU,CACRjM,OAAG3Y,EACH4Y,OAAG5Y,EACHy9B,mBAAez9B,EACf09B,mBAAe19B,EACf29B,cAAU39B,IAId,+HAAiIsK,MAAM,KAAKxL,QAASoK,IACnJkzB,EAAKlzB,GAAc6yB,GAAK7yB,GAAY9H,KAAKjE,KAE3CiM,EAAM3F,OAAOtG,EAAQ,CACnBi/B,SAGF,IAAIhE,EAAQ,EACZ/8B,OAAOuL,eAAezJ,EAAOi/B,KAAM,QAAS,CAC1C,MACE,OAAOhE,GAET,IAAIv4B,GACF,GAAIu4B,IAAUv4B,EAAO,CACnB,MAAMiqB,EAAU3sB,EAAOi/B,KAAKC,QAAQK,SAAWv/B,EAAOi/B,KAAKC,QAAQK,SAAS,QAAK18B,EAC3EitB,EAAU9vB,EAAOi/B,KAAKC,QAAQ3L,SAAWvzB,EAAOi/B,KAAKC,QAAQ3L,SAAS,QAAK1wB,EACjF7C,EAAOE,KAAK,aAAcwC,EAAOiqB,EAASmD,GAE5CmL,EAAQv4B,MAIdb,GAAI,CACF,OACE,MAAM7B,EAASlC,KACXkC,EAAOiO,OAAOgxB,KAAKhsB,SACrBjT,EAAOi/B,KAAK9J,UAGhB,UACE,MAAMn1B,EAASlC,KACfkC,EAAOi/B,KAAK7J,WAEd,WAAWhpB,GACT,MAAMpM,EAASlC,KACVkC,EAAOi/B,KAAKhsB,SACjBjT,EAAOi/B,KAAKrc,aAAaxW,IAE3B,SAASA,GACP,MAAMpM,EAASlC,KACVkC,EAAOi/B,KAAKhsB,SACjBjT,EAAOi/B,KAAKlY,WAAW3a,IAEzB,UAAUA,GACR,MAAMpM,EAASlC,KACXkC,EAAOiO,OAAOgxB,KAAKhsB,SAAWjT,EAAOi/B,KAAKhsB,SAAWjT,EAAOiO,OAAOgxB,KAAKmD,QAC1EpiC,EAAOi/B,KAAKmD,OAAOh2B,IAGvB,gBACE,MAAMpM,EAASlC,KACXkC,EAAOi/B,KAAKhsB,SAAWjT,EAAOiO,OAAOgxB,KAAKhsB,SAC5CjT,EAAOi/B,KAAKoD,mBAGhB,cACE,MAAMriC,EAASlC,KACXkC,EAAOi/B,KAAKhsB,SAAWjT,EAAOiO,OAAOgxB,KAAKhsB,SAAWjT,EAAOiO,OAAOwF,SACrEzT,EAAOi/B,KAAKoD,qBAMpB,MAAMC,GAAO,CACX,YAAYx3B,EAAOy3B,GAAkB,GACnC,MAAMviC,EAASlC,KACTmQ,EAASjO,EAAOiO,OAAOglB,KAC7B,GAAqB,qBAAVnoB,EAAuB,OAClC,GAA6B,IAAzB9K,EAAOY,OAAOzB,OAAc,OAChC,MAAM4T,EAAY/S,EAAOgT,SAAWhT,EAAOiO,OAAO+E,QAAQC,QAEpDsgB,EAAWxgB,EACb/S,EAAO0S,WAAWjH,SAAS,IAAIzL,EAAOiO,OAAOrP,uCAAuCkM,OACpF9K,EAAOY,OAAOmK,GAAGD,GAErB,IAAI03B,EAAUjP,EAAS/nB,KAAK,IAAIyC,EAAOw0B,qBAAqBx0B,EAAOy0B,qBAAqBz0B,EAAO00B,kBAC3FpP,EAAS1pB,SAASoE,EAAOw0B,eAAkBlP,EAAS1pB,SAASoE,EAAOy0B,cAAiBnP,EAAS1pB,SAASoE,EAAO00B,gBAChHH,EAAUA,EAAQ52B,IAAI2nB,EAAS,KAEV,IAAnBiP,EAAQrjC,QAEZqjC,EAAQ93B,KAAK,CAACk4B,EAAYjW,KACxB,MAAM4S,EAAW,eAAE5S,GACnB4S,EAAS51B,SAASsE,EAAO00B,cAEzB,MAAME,EAAatD,EAASx1B,KAAK,mBAC3B6iB,EAAM2S,EAASx1B,KAAK,YACpB8iB,EAAS0S,EAASx1B,KAAK,eACvB+iB,EAAQyS,EAASx1B,KAAK,cACtB+4B,EAAavD,EAAS12B,OAAO,WAEnC7I,EAAO0sB,UAAU6S,EAAS,GAAK3S,GAAOiW,EAAahW,EAAQC,GAAO,EAAO,KACvE,GAAsB,qBAAX9sB,GAAqC,OAAXA,GAAoBA,KAAWA,GAAWA,EAAOiO,UAAWjO,EAAOyE,UAAxG,CA+BA,GA9BIo+B,GACFtD,EAAS90B,IAAI,mBAAoB,QAAQo4B,OACzCtD,EAASv1B,WAAW,qBAEhB6iB,IACF0S,EAASx1B,KAAK,SAAU8iB,GACxB0S,EAASv1B,WAAW,gBAElB8iB,IACFyS,EAASx1B,KAAK,QAAS+iB,GACvByS,EAASv1B,WAAW,eAElB84B,EAAW3jC,QACb2jC,EAAWr3B,SAAS,UAAUf,KAAK,CAACq4B,EAAaC,KAC/C,MAAMC,EAAU,eAAED,GAEdC,EAAQl5B,KAAK,iBACfk5B,EAAQl5B,KAAK,SAAUk5B,EAAQl5B,KAAK,gBACpCk5B,EAAQj5B,WAAW,kBAIrB4iB,IACF2S,EAASx1B,KAAK,MAAO6iB,GACrB2S,EAASv1B,WAAW,cAIxBu1B,EAAS51B,SAASsE,EAAOy0B,aAAa94B,YAAYqE,EAAO00B,cACzDpP,EAAS/nB,KAAK,IAAIyC,EAAOi1B,gBAAkBv3B,SACvC3L,EAAOiO,OAAO1I,MAAQg9B,EAAiB,CACzC,MAAMY,EAAqB5P,EAASxpB,KAAK,2BACzC,GAAIwpB,EAAS1pB,SAAS7J,EAAOiO,OAAOmM,qBAAsB,CACxD,MAAMgpB,EAAgBpjC,EAAO0S,WAAWjH,SAAS,6BAA6B03B,YAA6BnjC,EAAOiO,OAAOmM,wBACzHpa,EAAOizB,KAAKoQ,YAAYD,EAAct4B,SAAS,OAC1C,CACL,MAAMw4B,EAAkBtjC,EAAO0S,WAAWjH,SAAS,IAAIzL,EAAOiO,OAAOmM,gDAAgD+oB,OACrHnjC,EAAOizB,KAAKoQ,YAAYC,EAAgBx4B,SAAS,IAGrD9K,EAAOE,KAAK,iBAAkBqzB,EAAS,GAAIgM,EAAS,IAChDv/B,EAAOiO,OAAO6K,YAChB9Y,EAAO4X,sBAIX5X,EAAOE,KAAK,gBAAiBqzB,EAAS,GAAIgM,EAAS,OAGvD,OACE,MAAMv/B,EAASlC,MACT,WACJ4U,EAAYzE,OAAQygB,EAAY,OAAE9tB,EAAM,YAAEsX,GACxClY,EACE+S,EAAY/S,EAAOgT,SAAW0b,EAAa1b,QAAQC,QACnDhF,EAASygB,EAAauE,KAE5B,IAAIne,EAAgB4Z,EAAa5Z,cAKjC,SAASyuB,EAAWz4B,GAClB,GAAIiI,GACF,GAAIL,EAAWjH,SAAS,IAAIijB,EAAa9vB,uCAAuCkM,OAAW3L,OACzF,OAAO,OAEJ,GAAIyB,EAAOkK,GAAQ,OAAO,EACjC,OAAO,EAGT,SAAS0I,EAAWsc,GAClB,OAAI/c,EACK,eAAE+c,GAAS/lB,KAAK,2BAElB,eAAE+lB,GAAShlB,QAIpB,GArBsB,SAAlBgK,IACFA,EAAgB,GAmBb9U,EAAOizB,KAAKuQ,qBAAoBxjC,EAAOizB,KAAKuQ,oBAAqB,GAClExjC,EAAOiO,OAAOyJ,sBAChBhF,EAAWjH,SAAS,IAAIijB,EAAahW,mBAAqBhO,KAAK,CAAC+4B,EAAS3T,KACvE,MAAMhlB,EAAQiI,EAAY,eAAE+c,GAAS/lB,KAAK,2BAA6B,eAAE+lB,GAAShlB,QAClF9K,EAAOizB,KAAKoQ,YAAYv4B,UAErB,GAAIgK,EAAgB,EACzB,IAAK,IAAI9V,EAAIkZ,EAAalZ,EAAIkZ,EAAcpD,EAAe9V,GAAK,EAC1DukC,EAAWvkC,IAAIgB,EAAOizB,KAAKoQ,YAAYrkC,QAG7CgB,EAAOizB,KAAKoQ,YAAYnrB,GAE1B,GAAIjK,EAAOy1B,aACT,GAAI5uB,EAAgB,GAAM7G,EAAO01B,oBAAsB11B,EAAO01B,mBAAqB,EAAI,CACrF,MAAMC,EAAS31B,EAAO01B,mBAChBpU,EAAMza,EACN+uB,EAAWlvB,KAAKgB,IAAIuC,EAAcqX,EAAM5a,KAAKK,IAAI4uB,EAAQrU,GAAM3uB,EAAOzB,QACtE2kC,EAAWnvB,KAAKK,IAAIkD,EAAcvD,KAAKK,IAAIua,EAAKqU,GAAS,GAE/D,IAAK,IAAI5kC,EAAIkZ,EAAcpD,EAAe9V,EAAI6kC,EAAU7kC,GAAK,EACvDukC,EAAWvkC,IAAIgB,EAAOizB,KAAKoQ,YAAYrkC,GAG7C,IAAK,IAAIA,EAAI8kC,EAAU9kC,EAAIkZ,EAAalZ,GAAK,EACvCukC,EAAWvkC,IAAIgB,EAAOizB,KAAKoQ,YAAYrkC,OAExC,CACL,MAAMqb,EAAY3H,EAAWjH,SAAS,IAAIijB,EAAa3U,gBACnDM,EAAUlb,OAAS,GAAGa,EAAOizB,KAAKoQ,YAAY7vB,EAAW6G,IAE7D,MAAMC,EAAY5H,EAAWjH,SAAS,IAAIijB,EAAa1U,gBACnDM,EAAUnb,OAAS,GAAGa,EAAOizB,KAAKoQ,YAAY7vB,EAAW8G,OAMrE,IAAIypB,GAAS,CACXjgC,KAAM,OACNmK,OAAQ,CACNglB,KAAM,CACJhgB,SAAS,EACTywB,cAAc,EACdC,mBAAoB,EACpBK,uBAAuB,EAEvBvB,aAAc,cACdE,aAAc,sBACdD,YAAa,qBACbQ,eAAgB,0BAGpB,SACE,MAAMljC,EAASlC,KACfmO,EAAM3F,OAAOtG,EAAQ,CACnBizB,KAAM,CACJuQ,oBAAoB,EACpBtQ,KAAMoP,GAAKpP,KAAKjvB,KAAKjE,GACrBqjC,YAAaf,GAAKe,YAAYp/B,KAAKjE,OAIzC6B,GAAI,CACF,aACE,MAAM7B,EAASlC,KACXkC,EAAOiO,OAAOglB,KAAKhgB,SAAWjT,EAAOiO,OAAOsf,gBAC9CvtB,EAAOiO,OAAOsf,eAAgB,IAGlC,OACE,MAAMvtB,EAASlC,KACXkC,EAAOiO,OAAOglB,KAAKhgB,UAAYjT,EAAOiO,OAAO1I,MAAuC,IAA/BvF,EAAOiO,OAAO6O,cACrE9c,EAAOizB,KAAKC,QAGhB,SACE,MAAMlzB,EAASlC,KACXkC,EAAOiO,OAAO0Y,WAAa3mB,EAAOiO,OAAOqa,gBAC3CtoB,EAAOizB,KAAKC,QAGhB,SACE,MAAMlzB,EAASlC,KACXkC,EAAOiO,OAAOglB,KAAKhgB,SACrBjT,EAAOizB,KAAKC,QAGhB,oBACE,MAAMlzB,EAASlC,KACXkC,EAAOiO,OAAOglB,KAAKhgB,SACrBjT,EAAOizB,KAAKC,QAGhB,kBACE,MAAMlzB,EAASlC,KACXkC,EAAOiO,OAAOglB,KAAKhgB,UACjBjT,EAAOiO,OAAOglB,KAAK+Q,wBAA2BhkC,EAAOiO,OAAOglB,KAAK+Q,wBAA0BhkC,EAAOizB,KAAKuQ,qBACzGxjC,EAAOizB,KAAKC,QAIlB,gBACE,MAAMlzB,EAASlC,KACXkC,EAAOiO,OAAOglB,KAAKhgB,UAAYjT,EAAOiO,OAAOglB,KAAK+Q,uBACpDhkC,EAAOizB,KAAKC,QAGhB,cACE,MAAMlzB,EAASlC,KACXkC,EAAOiO,OAAOglB,KAAKhgB,SAAWjT,EAAOiO,OAAOwF,SAC9CzT,EAAOizB,KAAKC,UAQpB,MAAM+Q,GAAa,CACjBC,aAAc,SAAsB1oB,EAAGC,GACrC,MAAM0oB,EAAgB,WACpB,IAAIN,EACAC,EACAM,EACJ,MAAO,CAACC,EAAOtmB,KACb+lB,GAAY,EACZD,EAAWQ,EAAMllC,OACjB,MAAO0kC,EAAWC,EAAW,EAC3BM,EAAQP,EAAWC,GAAY,EAC3BO,EAAMD,IAAUrmB,EAClB+lB,EAAWM,EAEXP,EAAWO,EAGf,OAAOP,GAfU,GAwBrB,IAAIS,EACAC,EAaJ,OApBAzmC,KAAK0d,EAAIA,EACT1d,KAAK2d,EAAIA,EACT3d,KAAK+7B,UAAYre,EAAErc,OAAS,EAO5BrB,KAAK0mC,YAAc,SAAqBzF,GACtC,OAAKA,GAGLwF,EAAKJ,EAAarmC,KAAK0d,EAAGujB,GAC1BuF,EAAKC,EAAK,GAIAxF,EAAKjhC,KAAK0d,EAAE8oB,KAAQxmC,KAAK2d,EAAE8oB,GAAMzmC,KAAK2d,EAAE6oB,KAASxmC,KAAK0d,EAAE+oB,GAAMzmC,KAAK0d,EAAE8oB,IAAQxmC,KAAK2d,EAAE6oB,IAR9E,GAUXxmC,MAGT,uBAAuB2mC,GACrB,MAAMzkC,EAASlC,KACVkC,EAAO0kC,WAAWC,SACrB3kC,EAAO0kC,WAAWC,OAAS3kC,EAAOiO,OAAO1I,KACrC,IAAI0+B,GAAWC,aAAalkC,EAAOqT,WAAYoxB,EAAEpxB,YACjD,IAAI4wB,GAAWC,aAAalkC,EAAOoT,SAAUqxB,EAAErxB,YAGvD,aAAaiI,EAAcC,GACzB,MAAMtb,EAASlC,KACT8mC,EAAa5kC,EAAO0kC,WAAWG,QACrC,IAAIzrB,EACA0rB,EACJ,SAASC,EAAuBN,GAK9B,MAAMjsB,EAAYxY,EAAO4S,cAAgB5S,EAAOwY,UAAYxY,EAAOwY,UAC/B,UAAhCxY,EAAOiO,OAAOy2B,WAAWM,KAC3BhlC,EAAO0kC,WAAWO,uBAAuBR,GAGzCK,GAAuB9kC,EAAO0kC,WAAWC,OAAOH,aAAahsB,IAG1DssB,GAAuD,cAAhC9kC,EAAOiO,OAAOy2B,WAAWM,KACnD5rB,GAAcqrB,EAAEnrB,eAAiBmrB,EAAE5rB,iBAAmB7Y,EAAOsZ,eAAiBtZ,EAAO6Y,gBACrFisB,GAAwBtsB,EAAYxY,EAAO6Y,gBAAkBO,EAAcqrB,EAAE5rB,gBAG3E7Y,EAAOiO,OAAOy2B,WAAWQ,UAC3BJ,EAAsBL,EAAEnrB,eAAiBwrB,GAE3CL,EAAEtrB,eAAe2rB,GACjBL,EAAEppB,aAAaypB,EAAqB9kC,GACpCykC,EAAElqB,oBACFkqB,EAAE9qB,sBAEJ,GAAIta,MAAMqR,QAAQk0B,GAChB,IAAK,IAAI5lC,EAAI,EAAGA,EAAI4lC,EAAWzlC,OAAQH,GAAK,EACtC4lC,EAAW5lC,KAAOsc,GAAgBspB,EAAW5lC,aAAcuK,IAC7Dw7B,EAAuBH,EAAW5lC,SAG7B4lC,aAAsBr7B,IAAU+R,IAAiBspB,GAC1DG,EAAuBH,IAG3B,cAAcnoB,EAAUnB,GACtB,MAAMtb,EAASlC,KACT8mC,EAAa5kC,EAAO0kC,WAAWG,QACrC,IAAI7lC,EACJ,SAASmmC,EAAwBV,GAC/BA,EAAEzsB,cAAcyE,EAAUzc,GACT,IAAbyc,IACFgoB,EAAE/nB,kBACE+nB,EAAEx2B,OAAO6K,YACX7M,EAAM0S,SAAS,KACb8lB,EAAE7sB,qBAGN6sB,EAAE/xB,WAAWrI,cAAc,KACpBu6B,IACDH,EAAEx2B,OAAO1I,MAAwC,UAAhCvF,EAAOiO,OAAOy2B,WAAWM,IAC5CP,EAAE/mB,UAEJ+mB,EAAEp6B,oBAIR,GAAIhL,MAAMqR,QAAQk0B,GAChB,IAAK5lC,EAAI,EAAGA,EAAI4lC,EAAWzlC,OAAQH,GAAK,EAClC4lC,EAAW5lC,KAAOsc,GAAgBspB,EAAW5lC,aAAcuK,IAC7D47B,EAAwBP,EAAW5lC,SAG9B4lC,aAAsBr7B,IAAU+R,IAAiBspB,GAC1DO,EAAwBP,KAI9B,IAAIQ,GAAe,CACjBthC,KAAM,aACNmK,OAAQ,CACNy2B,WAAY,CACVG,aAAShiC,EACTqiC,SAAS,EACTF,GAAI,UAGR,SACE,MAAMhlC,EAASlC,KACfmO,EAAM3F,OAAOtG,EAAQ,CACnB0kC,WAAY,CACVG,QAAS7kC,EAAOiO,OAAOy2B,WAAWG,QAClCI,uBAAwBhB,GAAWgB,uBAAuBhhC,KAAKjE,GAC/Dqb,aAAc4oB,GAAW5oB,aAAapX,KAAKjE,GAC3CgY,cAAeisB,GAAWjsB,cAAc/T,KAAKjE,OAInD6B,GAAI,CACF,SACE,MAAM7B,EAASlC,KACVkC,EAAO0kC,WAAWG,SACnB7kC,EAAO0kC,WAAWC,SACpB3kC,EAAO0kC,WAAWC,YAAS9hC,SACpB7C,EAAO0kC,WAAWC,SAG7B,SACE,MAAM3kC,EAASlC,KACVkC,EAAO0kC,WAAWG,SACnB7kC,EAAO0kC,WAAWC,SACpB3kC,EAAO0kC,WAAWC,YAAS9hC,SACpB7C,EAAO0kC,WAAWC,SAG7B,iBACE,MAAM3kC,EAASlC,KACVkC,EAAO0kC,WAAWG,SACnB7kC,EAAO0kC,WAAWC,SACpB3kC,EAAO0kC,WAAWC,YAAS9hC,SACpB7C,EAAO0kC,WAAWC,SAG7B,aAAansB,EAAW8C,GACtB,MAAMtb,EAASlC,KACVkC,EAAO0kC,WAAWG,SACvB7kC,EAAO0kC,WAAWrpB,aAAa7C,EAAW8C,IAE5C,cAAcmB,EAAUnB,GACtB,MAAMtb,EAASlC,KACVkC,EAAO0kC,WAAWG,SACvB7kC,EAAO0kC,WAAW1sB,cAAcyE,EAAUnB,MAKhD,MAAM+pB,GAAO,CACX,gBAAgB19B,GAEd,OADAA,EAAIoC,KAAK,WAAY,KACdpC,GAET,mBAAmBA,GAEjB,OADAA,EAAIoC,KAAK,WAAY,MACdpC,GAET,UAAUA,EAAK29B,GAEb,OADA39B,EAAIoC,KAAK,OAAQu7B,GACV39B,GAET,WAAWA,EAAK49B,GAEd,OADA59B,EAAIoC,KAAK,aAAcw7B,GAChB59B,GAET,UAAUA,GAER,OADAA,EAAIoC,KAAK,iBAAiB,GACnBpC,GAET,SAASA,GAEP,OADAA,EAAIoC,KAAK,iBAAiB,GACnBpC,GAET,WAAWyE,GACT,MAAMpM,EAASlC,KACTmQ,EAASjO,EAAOiO,OAAOo3B,KAC7B,GAAkB,KAAdj5B,EAAE6nB,QAAgB,OACtB,MAAMjR,EAAY,eAAE5W,EAAE3L,QAClBT,EAAO0F,YAAc1F,EAAO0F,WAAWozB,SAAW9V,EAAUnY,GAAG7K,EAAO0F,WAAWozB,WAC7E94B,EAAOwZ,QAAUxZ,EAAOiO,OAAO1I,MACnCvF,EAAOwd,YAELxd,EAAOwZ,MACTxZ,EAAOqlC,KAAKG,OAAOv3B,EAAOw3B,kBAE1BzlC,EAAOqlC,KAAKG,OAAOv3B,EAAOy3B,mBAG1B1lC,EAAO0F,YAAc1F,EAAO0F,WAAWqzB,SAAW/V,EAAUnY,GAAG7K,EAAO0F,WAAWqzB,WAC7E/4B,EAAOuZ,cAAgBvZ,EAAOiO,OAAO1I,MACzCvF,EAAO6d,YAEL7d,EAAOuZ,YACTvZ,EAAOqlC,KAAKG,OAAOv3B,EAAO03B,mBAE1B3lC,EAAOqlC,KAAKG,OAAOv3B,EAAO23B,mBAG1B5lC,EAAO2F,YAAcqd,EAAUnY,GAAG,IAAI7K,EAAOiO,OAAOtI,WAAW81B,cACjEzY,EAAU,GAAG7a,SAGjB,OAAO09B,GACL,MAAM7lC,EAASlC,KACTgoC,EAAe9lC,EAAOqlC,KAAKU,WACL,IAAxBD,EAAa3mC,SACjB2mC,EAAan7B,KAAK,IAClBm7B,EAAan7B,KAAKk7B,KAEpB,mBACE,MAAM7lC,EAASlC,KAEf,GAAIkC,EAAOiO,OAAO1I,OAASvF,EAAO0F,WAAY,OAC9C,MAAM,QAAEozB,EAAO,QAAEC,GAAY/4B,EAAO0F,WAEhCqzB,GAAWA,EAAQ55B,OAAS,IAC1Ba,EAAOuZ,aACTvZ,EAAOqlC,KAAKW,UAAUjN,GACtB/4B,EAAOqlC,KAAKY,mBAAmBlN,KAE/B/4B,EAAOqlC,KAAKa,SAASnN,GACrB/4B,EAAOqlC,KAAKc,gBAAgBpN,KAG5BD,GAAWA,EAAQ35B,OAAS,IAC1Ba,EAAOwZ,OACTxZ,EAAOqlC,KAAKW,UAAUlN,GACtB94B,EAAOqlC,KAAKY,mBAAmBnN,KAE/B94B,EAAOqlC,KAAKa,SAASpN,GACrB94B,EAAOqlC,KAAKc,gBAAgBrN,MAIlC,mBACE,MAAM94B,EAASlC,KACTmQ,EAASjO,EAAOiO,OAAOo3B,KACzBrlC,EAAO2F,YAAc3F,EAAOiO,OAAOtI,WAAWk2B,WAAa77B,EAAO2F,WAAWg0B,SAAW35B,EAAO2F,WAAWg0B,QAAQx6B,QACpHa,EAAO2F,WAAWg0B,QAAQjvB,KAAK,CAAC4vB,EAAa8L,KAC3C,MAAMC,EAAY,eAAED,GACpBpmC,EAAOqlC,KAAKc,gBAAgBE,GAC5BrmC,EAAOqlC,KAAKiB,UAAUD,EAAW,UACjCrmC,EAAOqlC,KAAKkB,WAAWF,EAAWp4B,EAAOu4B,wBAAwB3mC,QAAQ,gBAAiBwmC,EAAUv7B,QAAU,OAIpH,OACE,MAAM9K,EAASlC,KAEfkC,EAAO2H,IAAIqD,OAAOhL,EAAOqlC,KAAKU,YAG9B,MAAM93B,EAASjO,EAAOiO,OAAOo3B,KAC7B,IAAIvM,EACAC,EACA/4B,EAAO0F,YAAc1F,EAAO0F,WAAWozB,UACzCA,EAAU94B,EAAO0F,WAAWozB,SAE1B94B,EAAO0F,YAAc1F,EAAO0F,WAAWqzB,UACzCA,EAAU/4B,EAAO0F,WAAWqzB,SAE1BD,IACF94B,EAAOqlC,KAAKc,gBAAgBrN,GAC5B94B,EAAOqlC,KAAKiB,UAAUxN,EAAS,UAC/B94B,EAAOqlC,KAAKkB,WAAWzN,EAAS7qB,EAAOy3B,kBACvC5M,EAAQj3B,GAAG,UAAW7B,EAAOqlC,KAAKoB,aAEhC1N,IACF/4B,EAAOqlC,KAAKc,gBAAgBpN,GAC5B/4B,EAAOqlC,KAAKiB,UAAUvN,EAAS,UAC/B/4B,EAAOqlC,KAAKkB,WAAWxN,EAAS9qB,EAAO23B,kBACvC7M,EAAQl3B,GAAG,UAAW7B,EAAOqlC,KAAKoB,aAIhCzmC,EAAO2F,YAAc3F,EAAOiO,OAAOtI,WAAWk2B,WAAa77B,EAAO2F,WAAWg0B,SAAW35B,EAAO2F,WAAWg0B,QAAQx6B,QACpHa,EAAO2F,WAAWgC,IAAI9F,GAAG,UAAW,IAAI7B,EAAOiO,OAAOtI,WAAW81B,YAAez7B,EAAOqlC,KAAKoB,aAGhG,UACE,MAAMzmC,EAASlC,KAGf,IAAIg7B,EACAC,EAHA/4B,EAAOqlC,KAAKU,YAAc/lC,EAAOqlC,KAAKU,WAAW5mC,OAAS,GAAGa,EAAOqlC,KAAKU,WAAWp6B,SAIpF3L,EAAO0F,YAAc1F,EAAO0F,WAAWozB,UACzCA,EAAU94B,EAAO0F,WAAWozB,SAE1B94B,EAAO0F,YAAc1F,EAAO0F,WAAWqzB,UACzCA,EAAU/4B,EAAO0F,WAAWqzB,SAE1BD,GACFA,EAAQ3uB,IAAI,UAAWnK,EAAOqlC,KAAKoB,YAEjC1N,GACFA,EAAQ5uB,IAAI,UAAWnK,EAAOqlC,KAAKoB,YAIjCzmC,EAAO2F,YAAc3F,EAAOiO,OAAOtI,WAAWk2B,WAAa77B,EAAO2F,WAAWg0B,SAAW35B,EAAO2F,WAAWg0B,QAAQx6B,QACpHa,EAAO2F,WAAWgC,IAAIwC,IAAI,UAAW,IAAInK,EAAOiO,OAAOtI,WAAW81B,YAAez7B,EAAOqlC,KAAKoB,cAInG,IAAIC,GAAO,CACT5iC,KAAM,OACNmK,OAAQ,CACNo3B,KAAM,CACJpyB,SAAS,EACT0zB,kBAAmB,sBACnBf,iBAAkB,iBAClBF,iBAAkB,aAClBC,kBAAmB,0BACnBF,iBAAkB,yBAClBe,wBAAyB,0BAG7B,SACE,MAAMxmC,EAASlC,KACfmO,EAAM3F,OAAOtG,EAAQ,CACnBqlC,KAAM,CACJU,WAAY,eAAE,gBAAgB/lC,EAAOiO,OAAOo3B,KAAKsB,0EAGrDzoC,OAAO4N,KAAKu5B,IAAM1jC,QAASoK,IACzB/L,EAAOqlC,KAAKt5B,GAAcs5B,GAAKt5B,GAAY9H,KAAKjE,MAGpD6B,GAAI,CACF,OACE,MAAM7B,EAASlC,KACVkC,EAAOiO,OAAOo3B,KAAKpyB,UACxBjT,EAAOqlC,KAAKnX,OACZluB,EAAOqlC,KAAKuB,qBAEd,SACE,MAAM5mC,EAASlC,KACVkC,EAAOiO,OAAOo3B,KAAKpyB,SACxBjT,EAAOqlC,KAAKuB,oBAEd,WACE,MAAM5mC,EAASlC,KACVkC,EAAOiO,OAAOo3B,KAAKpyB,SACxBjT,EAAOqlC,KAAKuB,oBAEd,mBACE,MAAM5mC,EAASlC,KACVkC,EAAOiO,OAAOo3B,KAAKpyB,SACxBjT,EAAOqlC,KAAKwB,oBAEd,UACE,MAAM7mC,EAASlC,KACVkC,EAAOiO,OAAOo3B,KAAKpyB,SACxBjT,EAAOqlC,KAAKn/B,aAKlB,MAAM4gC,GAAU,CACd,OACE,MAAM9mC,EAASlC,KACf,IAAKkC,EAAOiO,OAAO84B,QAAS,OAC5B,IAAK,OAAOA,UAAY,OAAOA,QAAQC,UAGrC,OAFAhnC,EAAOiO,OAAO84B,QAAQ9zB,SAAU,OAChCjT,EAAOiO,OAAOg5B,eAAeh0B,SAAU,GAGzC,MAAM8zB,EAAU/mC,EAAO+mC,QACvBA,EAAQ9gC,aAAc,EACtB8gC,EAAQlmC,MAAQimC,GAAQI,iBACnBH,EAAQlmC,MAAM2B,KAAQukC,EAAQlmC,MAAM6B,SACzCqkC,EAAQI,cAAc,EAAGJ,EAAQlmC,MAAM6B,MAAO1C,EAAOiO,OAAO6M,oBACvD9a,EAAOiO,OAAO84B,QAAQK,cACzB,OAAOhjC,iBAAiB,WAAYpE,EAAO+mC,QAAQM,sBAGvD,UACE,MAAMrnC,EAASlC,KACVkC,EAAOiO,OAAO84B,QAAQK,cACzB,OAAO5qB,oBAAoB,WAAYxc,EAAO+mC,QAAQM,qBAG1D,qBACE,MAAMrnC,EAASlC,KACfkC,EAAO+mC,QAAQlmC,MAAQimC,GAAQI,gBAC/BlnC,EAAO+mC,QAAQI,cAAcnnC,EAAOiO,OAAO4J,MAAO7X,EAAO+mC,QAAQlmC,MAAM6B,OAAO,IAEhF,gBACE,MAAM4kC,EAAY,OAAOl5B,SAASm5B,SAAS52B,MAAM,GAAGxD,MAAM,KAAKzB,OAAQ87B,GAAkB,KAATA,GAC1E/N,EAAQ6N,EAAUnoC,OAClBqD,EAAM8kC,EAAU7N,EAAQ,GACxB/2B,EAAQ4kC,EAAU7N,EAAQ,GAChC,MAAO,CAAEj3B,MAAKE,UAEhB,WAAWF,EAAKsI,GACd,MAAM9K,EAASlC,KACf,IAAKkC,EAAO+mC,QAAQ9gC,cAAgBjG,EAAOiO,OAAO84B,QAAQ9zB,QAAS,OACnE,MAAMkC,EAAQnV,EAAOY,OAAOmK,GAAGD,GAC/B,IAAIpI,EAAQokC,GAAQW,QAAQtyB,EAAMpL,KAAK,iBAClC,OAAOqE,SAASm5B,SAASzmC,SAAS0B,KACrCE,EAAQ,GAAGF,KAAOE,KAEpB,MAAMglC,EAAe,OAAOX,QAAQY,MAChCD,GAAgBA,EAAahlC,QAAUA,IAGvC1C,EAAOiO,OAAO84B,QAAQK,aACxB,OAAOL,QAAQK,aAAa,CAAE1kC,SAAS,KAAMA,GAE7C,OAAOqkC,QAAQC,UAAU,CAAEtkC,SAAS,KAAMA,KAG9C,QAAQkI,GACN,OAAOA,EAAK+C,WACT9N,QAAQ,OAAQ,KAChBA,QAAQ,WAAY,IACpBA,QAAQ,OAAQ,KAChBA,QAAQ,MAAO,IACfA,QAAQ,MAAO,KAEpB,cAAcgY,EAAOnV,EAAOoZ,GAC1B,MAAM9b,EAASlC,KACf,GAAI4E,EACF,IAAK,IAAI1D,EAAI,EAAGG,EAASa,EAAOY,OAAOzB,OAAQH,EAAIG,EAAQH,GAAK,EAAG,CACjE,MAAMmW,EAAQnV,EAAOY,OAAOmK,GAAG/L,GACzB4oC,EAAed,GAAQW,QAAQtyB,EAAMpL,KAAK,iBAChD,GAAI69B,IAAiBllC,IAAUyS,EAAMtL,SAAS7J,EAAOiO,OAAOmM,qBAAsB,CAChF,MAAMtP,EAAQqK,EAAMrK,QACpB9K,EAAO6c,QAAQ/R,EAAO+M,EAAOiE,SAIjC9b,EAAO6c,QAAQ,EAAGhF,EAAOiE,KAK/B,IAAI+rB,GAAY,CACd/jC,KAAM,UACNmK,OAAQ,CACN84B,QAAS,CACP9zB,SAAS,EACTm0B,cAAc,EACd5kC,IAAK,WAGT,SACE,MAAMxC,EAASlC,KACfmO,EAAM3F,OAAOtG,EAAQ,CACnB+mC,QAAS,CACP7Y,KAAM4Y,GAAQ5Y,KAAKjqB,KAAKjE,GACxB8nC,WAAYhB,GAAQgB,WAAW7jC,KAAKjE,GACpCqnC,mBAAoBP,GAAQO,mBAAmBpjC,KAAKjE,GACpDmnC,cAAeL,GAAQK,cAAcljC,KAAKjE,GAC1CkG,QAAS4gC,GAAQ5gC,QAAQjC,KAAKjE,OAIpC6B,GAAI,CACF,OACE,MAAM7B,EAASlC,KACXkC,EAAOiO,OAAO84B,QAAQ9zB,SACxBjT,EAAO+mC,QAAQ7Y,QAGnB,UACE,MAAMluB,EAASlC,KACXkC,EAAOiO,OAAO84B,QAAQ9zB,SACxBjT,EAAO+mC,QAAQ7gC,WAGnB,gBACE,MAAMlG,EAASlC,KACXkC,EAAO+mC,QAAQ9gC,aACjBjG,EAAO+mC,QAAQe,WAAW9nC,EAAOiO,OAAO84B,QAAQvkC,IAAKxC,EAAOkY,cAGhE,cACE,MAAMlY,EAASlC,KACXkC,EAAO+mC,QAAQ9gC,aAAejG,EAAOiO,OAAOwF,SAC9CzT,EAAO+mC,QAAQe,WAAW9nC,EAAOiO,OAAO84B,QAAQvkC,IAAKxC,EAAOkY,gBAMpE,MAAM6vB,GAAiB,CACrB,cACE,MAAM/nC,EAASlC,KACfkC,EAAOE,KAAK,cACZ,MAAM8nC,EAAU,OAAW55B,SAAS65B,KAAKpoC,QAAQ,IAAK,IAChDqoC,EAAkBloC,EAAOY,OAAOmK,GAAG/K,EAAOkY,aAAanO,KAAK,aAClE,GAAIi+B,IAAYE,EAAiB,CAC/B,MAAM5qB,EAAWtd,EAAO0S,WAAWjH,SAAS,IAAIzL,EAAOiO,OAAOrP,yBAAyBopC,OAAal9B,QACpG,GAAwB,qBAAbwS,EAA0B,OACrCtd,EAAO6c,QAAQS,KAGnB,UACE,MAAMtd,EAASlC,KACf,GAAKkC,EAAOinC,eAAehhC,aAAgBjG,EAAOiO,OAAOg5B,eAAeh0B,QACxE,GAAIjT,EAAOiO,OAAOg5B,eAAeG,cAAgB,OAAOL,SAAW,OAAOA,QAAQK,aAChF,OAAOL,QAAQK,aAAa,KAAM,KAAO,IAAIpnC,EAAOY,OAAOmK,GAAG/K,EAAOkY,aAAanO,KAAK,cAAkB,IACzG/J,EAAOE,KAAK,eACP,CACL,MAAMiV,EAAQnV,EAAOY,OAAOmK,GAAG/K,EAAOkY,aAChC+vB,EAAO9yB,EAAMpL,KAAK,cAAgBoL,EAAMpL,KAAK,gBACnD,OAAWqE,SAAS65B,KAAOA,GAAQ,GACnCjoC,EAAOE,KAAK,aAGhB,OACE,MAAMF,EAASlC,KACf,IAAKkC,EAAOiO,OAAOg5B,eAAeh0B,SAAYjT,EAAOiO,OAAO84B,SAAW/mC,EAAOiO,OAAO84B,QAAQ9zB,QAAU,OACvGjT,EAAOinC,eAAehhC,aAAc,EACpC,MAAMgiC,EAAO,OAAW75B,SAAS65B,KAAKpoC,QAAQ,IAAK,IACnD,GAAIooC,EAAM,CACR,MAAMpwB,EAAQ,EACd,IAAK,IAAI7Y,EAAI,EAAGG,EAASa,EAAOY,OAAOzB,OAAQH,EAAIG,EAAQH,GAAK,EAAG,CACjE,MAAMmW,EAAQnV,EAAOY,OAAOmK,GAAG/L,GACzBmpC,EAAYhzB,EAAMpL,KAAK,cAAgBoL,EAAMpL,KAAK,gBACxD,GAAIo+B,IAAcF,IAAS9yB,EAAMtL,SAAS7J,EAAOiO,OAAOmM,qBAAsB,CAC5E,MAAMtP,EAAQqK,EAAMrK,QACpB9K,EAAO6c,QAAQ/R,EAAO+M,EAAO7X,EAAOiO,OAAO6M,oBAAoB,KAIjE9a,EAAOiO,OAAOg5B,eAAemB,YAC/B,eAAE,QAAQvmC,GAAG,aAAc7B,EAAOinC,eAAeoB,cAGrD,UACE,MAAMroC,EAASlC,KACXkC,EAAOiO,OAAOg5B,eAAemB,YAC/B,eAAE,QAAQj+B,IAAI,aAAcnK,EAAOinC,eAAeoB,eAIxD,IAAIC,GAAmB,CACrBxkC,KAAM,kBACNmK,OAAQ,CACNg5B,eAAgB,CACdh0B,SAAS,EACTm0B,cAAc,EACdgB,YAAY,IAGhB,SACE,MAAMpoC,EAASlC,KACfmO,EAAM3F,OAAOtG,EAAQ,CACnBinC,eAAgB,CACdhhC,aAAa,EACbioB,KAAM6Z,GAAe7Z,KAAKjqB,KAAKjE,GAC/BkG,QAAS6hC,GAAe7hC,QAAQjC,KAAKjE,GACrCuoC,QAASR,GAAeQ,QAAQtkC,KAAKjE,GACrCqoC,YAAaN,GAAeM,YAAYpkC,KAAKjE,OAInD6B,GAAI,CACF,OACE,MAAM7B,EAASlC,KACXkC,EAAOiO,OAAOg5B,eAAeh0B,SAC/BjT,EAAOinC,eAAe/Y,QAG1B,UACE,MAAMluB,EAASlC,KACXkC,EAAOiO,OAAOg5B,eAAeh0B,SAC/BjT,EAAOinC,eAAe/gC,WAG1B,gBACE,MAAMlG,EAASlC,KACXkC,EAAOinC,eAAehhC,aACxBjG,EAAOinC,eAAesB,WAG1B,cACE,MAAMvoC,EAASlC,KACXkC,EAAOinC,eAAehhC,aAAejG,EAAOiO,OAAOwF,SACrDzT,EAAOinC,eAAesB,aAQ9B,MAAMC,GAAW,CACf,MACE,MAAMxoC,EAASlC,KACT2qC,EAAiBzoC,EAAOY,OAAOmK,GAAG/K,EAAOkY,aAC/C,IAAI5L,EAAQtM,EAAOiO,OAAOsb,SAASjd,MAC/Bm8B,EAAe1+B,KAAK,0BACtBuC,EAAQm8B,EAAe1+B,KAAK,yBAA2B/J,EAAOiO,OAAOsb,SAASjd,OAEhFwrB,aAAa93B,EAAOupB,SAASwO,SAC7B/3B,EAAOupB,SAASwO,QAAU9rB,EAAM0S,SAAS,KACnC3e,EAAOiO,OAAOsb,SAASmf,iBACrB1oC,EAAOiO,OAAO1I,MAChBvF,EAAO0d,UACP1d,EAAO6d,UAAU7d,EAAOiO,OAAO4J,OAAO,GAAM,GAC5C7X,EAAOE,KAAK,aACFF,EAAOuZ,YAGPvZ,EAAOiO,OAAOsb,SAASof,gBAIjC3oC,EAAOupB,SAAS8O,QAHhBr4B,EAAO6c,QAAQ7c,EAAOY,OAAOzB,OAAS,EAAGa,EAAOiO,OAAO4J,OAAO,GAAM,GACpE7X,EAAOE,KAAK,cAJZF,EAAO6d,UAAU7d,EAAOiO,OAAO4J,OAAO,GAAM,GAC5C7X,EAAOE,KAAK,aAOLF,EAAOiO,OAAO1I,MACvBvF,EAAO0d,UACP1d,EAAOwd,UAAUxd,EAAOiO,OAAO4J,OAAO,GAAM,GAC5C7X,EAAOE,KAAK,aACFF,EAAOwZ,MAGPxZ,EAAOiO,OAAOsb,SAASof,gBAIjC3oC,EAAOupB,SAAS8O,QAHhBr4B,EAAO6c,QAAQ,EAAG7c,EAAOiO,OAAO4J,OAAO,GAAM,GAC7C7X,EAAOE,KAAK,cAJZF,EAAOwd,UAAUxd,EAAOiO,OAAO4J,OAAO,GAAM,GAC5C7X,EAAOE,KAAK,aAOVF,EAAOiO,OAAOwF,SAAWzT,EAAOupB,SAASC,SAASxpB,EAAOupB,SAASG,OACrEpd,IAEL,QACE,MAAMtM,EAASlC,KACf,MAAuC,qBAA5BkC,EAAOupB,SAASwO,WACvB/3B,EAAOupB,SAASC,UACpBxpB,EAAOupB,SAASC,SAAU,EAC1BxpB,EAAOE,KAAK,iBACZF,EAAOupB,SAASG,OACT,KAET,OACE,MAAM1pB,EAASlC,KACf,QAAKkC,EAAOupB,SAASC,UACkB,qBAA5BxpB,EAAOupB,SAASwO,UAEvB/3B,EAAOupB,SAASwO,UAClBD,aAAa93B,EAAOupB,SAASwO,SAC7B/3B,EAAOupB,SAASwO,aAAUl1B,GAE5B7C,EAAOupB,SAASC,SAAU,EAC1BxpB,EAAOE,KAAK,iBACL,KAET,MAAM2X,GACJ,MAAM7X,EAASlC,KACVkC,EAAOupB,SAASC,UACjBxpB,EAAOupB,SAASE,SAChBzpB,EAAOupB,SAASwO,SAASD,aAAa93B,EAAOupB,SAASwO,SAC1D/3B,EAAOupB,SAASE,QAAS,EACX,IAAV5R,GAAgB7X,EAAOiO,OAAOsb,SAASqf,mBAIzC5oC,EAAO0S,WAAW,GAAGtO,iBAAiB,gBAAiBpE,EAAOupB,SAAS8Y,iBACvEriC,EAAO0S,WAAW,GAAGtO,iBAAiB,sBAAuBpE,EAAOupB,SAAS8Y,mBAJ7EriC,EAAOupB,SAASE,QAAS,EACzBzpB,EAAOupB,SAASG,WAQtB,IAAImf,GAAa,CACf/kC,KAAM,WACNmK,OAAQ,CACNsb,SAAU,CACRtW,SAAS,EACT3G,MAAO,IACPs8B,mBAAmB,EACnBE,sBAAsB,EACtBH,iBAAiB,EACjBD,kBAAkB,IAGtB,SACE,MAAM1oC,EAASlC,KACfmO,EAAM3F,OAAOtG,EAAQ,CACnBupB,SAAU,CACRC,SAAS,EACTC,QAAQ,EACRC,IAAK8e,GAAS9e,IAAIzlB,KAAKjE,GACvBuqB,MAAOie,GAASje,MAAMtmB,KAAKjE,GAC3Bq4B,KAAMmQ,GAASnQ,KAAKp0B,KAAKjE,GACzB+oC,MAAOP,GAASO,MAAM9kC,KAAKjE,GAC3B,qBACmC,WAA7BgpC,SAASC,iBAAgCjpC,EAAOupB,SAASC,SAC3DxpB,EAAOupB,SAASwf,QAEe,YAA7BC,SAASC,iBAAiCjpC,EAAOupB,SAASE,SAC5DzpB,EAAOupB,SAASG,MAChB1pB,EAAOupB,SAASE,QAAS,IAG7B,gBAAgBrd,GACTpM,IAAUA,EAAOyE,WAAczE,EAAO0S,YACvCtG,EAAE3L,SAAW3C,OACjBkC,EAAO0S,WAAW,GAAG8J,oBAAoB,gBAAiBxc,EAAOupB,SAAS8Y,iBAC1EriC,EAAO0S,WAAW,GAAG8J,oBAAoB,sBAAuBxc,EAAOupB,SAAS8Y,iBAChFriC,EAAOupB,SAASE,QAAS,EACpBzpB,EAAOupB,SAASC,QAGnBxpB,EAAOupB,SAASG,MAFhB1pB,EAAOupB,SAAS8O,aAQ1Bx2B,GAAI,CACF,OACE,MAAM7B,EAASlC,KACXkC,EAAOiO,OAAOsb,SAAStW,UACzBjT,EAAOupB,SAASgB,QAChBye,SAAS5kC,iBAAiB,mBAAoBpE,EAAOupB,SAAS2f,sBAGlE,sBAAsBrxB,EAAOmE,GAC3B,MAAMhc,EAASlC,KACXkC,EAAOupB,SAASC,UACdxN,IAAahc,EAAOiO,OAAOsb,SAASuf,qBACtC9oC,EAAOupB,SAASwf,MAAMlxB,GAEtB7X,EAAOupB,SAAS8O,SAItB,kBACE,MAAMr4B,EAASlC,KACXkC,EAAOupB,SAASC,UACdxpB,EAAOiO,OAAOsb,SAASuf,qBACzB9oC,EAAOupB,SAAS8O,OAEhBr4B,EAAOupB,SAASwf,UAItB,WACE,MAAM/oC,EAASlC,KACXkC,EAAOiO,OAAOwF,SAAWzT,EAAOupB,SAASE,SAAWzpB,EAAOiO,OAAOsb,SAASuf,sBAC7E9oC,EAAOupB,SAASG,OAGpB,UACE,MAAM1pB,EAASlC,KACXkC,EAAOupB,SAASC,SAClBxpB,EAAOupB,SAAS8O,OAElB2Q,SAASxsB,oBAAoB,mBAAoBxc,EAAOupB,SAAS2f,uBAKvE,MAAMC,GAAO,CACX,eACE,MAAMnpC,EAASlC,MACT,OAAE8C,GAAWZ,EACnB,IAAK,IAAIhB,EAAI,EAAGA,EAAI4B,EAAOzB,OAAQH,GAAK,EAAG,CACzC,MAAMu0B,EAAWvzB,EAAOY,OAAOmK,GAAG/L,GAC5BwL,EAAS+oB,EAAS,GAAGnb,kBAC3B,IAAIgxB,GAAM5+B,EACLxK,EAAOiO,OAAOkN,mBAAkBiuB,GAAMppC,EAAOwY,WAClD,IAAI6wB,EAAK,EACJrpC,EAAOqS,iBACVg3B,EAAKD,EACLA,EAAK,GAEP,MAAME,EAAetpC,EAAOiO,OAAOs7B,WAAWC,UAC1C70B,KAAKK,IAAI,EAAIL,KAAK8B,IAAI8c,EAAS,GAAGra,UAAW,GAC7C,EAAIvE,KAAKgB,IAAIhB,KAAKK,IAAIue,EAAS,GAAGra,UAAW,GAAI,GACrDqa,EACG9oB,IAAI,CACHiyB,QAAS4M,IAEVr/B,UAAU,eAAem/B,QAASC,eAGzC,cAAc5sB,GACZ,MAAMzc,EAASlC,MACT,OAAE8C,EAAM,WAAE8R,GAAe1S,EAE/B,GADAY,EAAOsJ,WAAWuS,GACdzc,EAAOiO,OAAOkN,kBAAiC,IAAbsB,EAAgB,CACpD,IAAIgtB,GAAiB,EACrB7oC,EAAOyJ,cAAc,KACnB,GAAIo/B,EAAgB,OACpB,IAAKzpC,GAAUA,EAAOyE,UAAW,OACjCglC,GAAiB,EACjBzpC,EAAOic,WAAY,EACnB,MAAMytB,EAAgB,CAAC,sBAAuB,iBAC9C,IAAK,IAAI1qC,EAAI,EAAGA,EAAI0qC,EAAcvqC,OAAQH,GAAK,EAC7C0T,EAAWtI,QAAQs/B,EAAc1qC,SAO3C,IAAI2qC,GAAa,CACf7lC,KAAM,cACNmK,OAAQ,CACNs7B,WAAY,CACVC,WAAW,IAGf,SACE,MAAMxpC,EAASlC,KACfmO,EAAM3F,OAAOtG,EAAQ,CACnBupC,WAAY,CACVluB,aAAc8tB,GAAK9tB,aAAapX,KAAKjE,GACrCgY,cAAemxB,GAAKnxB,cAAc/T,KAAKjE,OAI7C6B,GAAI,CACF,aACE,MAAM7B,EAASlC,KACf,GAA6B,SAAzBkC,EAAOiO,OAAO2I,OAAmB,OACrC5W,EAAOqsB,WAAWxb,KAAQ7Q,EAAOiO,OAAOud,uBAAjB,QACvB,MAAMsI,EAAkB,CACtBhf,cAAe,EACfJ,gBAAiB,EACjBa,eAAgB,EAChBkC,qBAAqB,EACrBvD,aAAc,EACdiH,kBAAkB,GAEpBlP,EAAM3F,OAAOtG,EAAOiO,OAAQ6lB,GAC5B7nB,EAAM3F,OAAOtG,EAAOqrB,eAAgByI,IAEtC,eACE,MAAM9zB,EAASlC,KACc,SAAzBkC,EAAOiO,OAAO2I,QAClB5W,EAAOupC,WAAWluB,gBAEpB,cAAcoB,GACZ,MAAMzc,EAASlC,KACc,SAAzBkC,EAAOiO,OAAO2I,QAClB5W,EAAOupC,WAAWvxB,cAAcyE,MAKtC,MAAMmtB,GAAO,CACX,eACE,MAAM5pC,EAASlC,MACT,IACJ6J,EAAG,WAAE+K,EAAU,OAAE9R,EAAQqR,MAAO43B,EAAa33B,OAAQ43B,EAAcl3B,aAAcC,EAAKL,KAAMG,GAC1F3S,EACEiO,EAASjO,EAAOiO,OAAO87B,WACvB13B,EAAerS,EAAOqS,eACtBU,EAAY/S,EAAOgT,SAAWhT,EAAOiO,OAAO+E,QAAQC,QAC1D,IACI+2B,EADAC,EAAgB,EAEhBh8B,EAAOi8B,SACL73B,GACF23B,EAAgBt3B,EAAWlH,KAAK,uBACH,IAAzBw+B,EAAc7qC,SAChB6qC,EAAgB,eAAE,0CAClBt3B,EAAW1H,OAAOg/B,IAEpBA,EAAcv/B,IAAI,CAAEyH,OAAW23B,EAAH,SAE5BG,EAAgBriC,EAAI6D,KAAK,uBACI,IAAzBw+B,EAAc7qC,SAChB6qC,EAAgB,eAAE,0CAClBriC,EAAIqD,OAAOg/B,MAIjB,IAAK,IAAIhrC,EAAI,EAAGA,EAAI4B,EAAOzB,OAAQH,GAAK,EAAG,CACzC,MAAMu0B,EAAW3yB,EAAOmK,GAAG/L,GAC3B,IAAIwU,EAAaxU,EACb+T,IACFS,EAAajB,SAASghB,EAASxpB,KAAK,2BAA4B,KAElE,IAAIogC,EAA0B,GAAb32B,EACb42B,EAAQz1B,KAAKC,MAAMu1B,EAAa,KAChCt3B,IACFs3B,GAAcA,EACdC,EAAQz1B,KAAKC,OAAOu1B,EAAa,MAEnC,MAAMjxB,EAAWvE,KAAKK,IAAIL,KAAKgB,IAAI4d,EAAS,GAAGra,SAAU,IAAK,GAC9D,IAAIkwB,EAAK,EACLC,EAAK,EACLgB,EAAK,EACL72B,EAAa,IAAM,GACrB41B,EAAc,GAARgB,EAAYz3B,EAClB03B,EAAK,IACK72B,EAAa,GAAK,IAAM,GAClC41B,EAAK,EACLiB,EAAc,GAARD,EAAYz3B,IACRa,EAAa,GAAK,IAAM,GAClC41B,EAAKz2B,EAAsB,EAARy3B,EAAYz3B,EAC/B03B,EAAK13B,IACKa,EAAa,GAAK,IAAM,IAClC41B,GAAMz2B,EACN03B,EAAM,EAAI13B,EAA4B,EAAbA,EAAiBy3B,GAExCv3B,IACFu2B,GAAMA,GAGH/2B,IACHg3B,EAAKD,EACLA,EAAK,GAGP,MAAMn/B,EAAY,WAAWoI,EAAe,GAAK83B,iBAA0B93B,EAAe83B,EAAa,qBAAqBf,QAASC,QAASgB,OAM9I,GALInxB,GAAY,GAAKA,GAAY,IAC/B+wB,EAA8B,GAAbz2B,EAA+B,GAAX0F,EACjCrG,IAAKo3B,EAA+B,IAAbz2B,EAA+B,GAAX0F,IAEjDqa,EAAStpB,UAAUA,GACfgE,EAAOq8B,aAAc,CAEvB,IAAIC,EAAel4B,EAAekhB,EAAS/nB,KAAK,6BAA+B+nB,EAAS/nB,KAAK,4BACzFg/B,EAAcn4B,EAAekhB,EAAS/nB,KAAK,8BAAgC+nB,EAAS/nB,KAAK,+BACjE,IAAxB++B,EAAaprC,SACforC,EAAe,eAAE,mCAAmCl4B,EAAe,OAAS,iBAC5EkhB,EAASvoB,OAAOu/B,IAES,IAAvBC,EAAYrrC,SACdqrC,EAAc,eAAE,mCAAmCn4B,EAAe,QAAU,oBAC5EkhB,EAASvoB,OAAOw/B,IAEdD,EAAaprC,SAAQorC,EAAa,GAAGx0B,MAAM2mB,QAAU/nB,KAAKK,KAAKkE,EAAU,IACzEsxB,EAAYrrC,SAAQqrC,EAAY,GAAGz0B,MAAM2mB,QAAU/nB,KAAKK,IAAIkE,EAAU,KAU9E,GAPAxG,EAAWjI,IAAI,CACb,2BAA4B,YAAYkI,EAAa,MACrD,wBAAyB,YAAYA,EAAa,MAClD,uBAAwB,YAAYA,EAAa,MACjD,mBAAoB,YAAYA,EAAa,QAG3C1E,EAAOi8B,OACT,GAAI73B,EACF23B,EAAc//B,UAAU,oBAAqB4/B,EAAc,EAAK57B,EAAOw8B,oBAAoBZ,EAAc,2CAA2C57B,EAAOy8B,oBACtJ,CACL,MAAMC,EAAch2B,KAAK8B,IAAIwzB,GAA6D,GAA3Ct1B,KAAKC,MAAMD,KAAK8B,IAAIwzB,GAAiB,IAC9E7wB,EAAa,KAChBzE,KAAKi2B,IAAmB,EAAdD,EAAkBh2B,KAAKoR,GAAM,KAAO,EAC5CpR,KAAKk2B,IAAmB,EAAdF,EAAkBh2B,KAAKoR,GAAM,KAAO,GAE7C+kB,EAAS78B,EAAOy8B,YAChBK,EAAS98B,EAAOy8B,YAActxB,EAC9B5O,EAASyD,EAAOw8B,aACtBT,EAAc//B,UAAU,WAAW6gC,SAAcC,uBAA6BjB,EAAe,EAAKt/B,SAAcs/B,EAAe,EAAIiB,wBAGvI,MAAMC,EAAW1a,GAAQC,UAAYD,GAAQG,aAAiB9d,EAAa,EAAK,EAChFD,EACGzI,UAAU,qBAAqB+gC,gBAAsBhrC,EAAOqS,eAAiB,EAAI43B,iBAA6BjqC,EAAOqS,gBAAkB43B,EAAgB,UAE5J,cAAcxtB,GACZ,MAAMzc,EAASlC,MACT,IAAE6J,EAAG,OAAE/G,GAAWZ,EACxBY,EACGsJ,WAAWuS,GACXjR,KAAK,gHACLtB,WAAWuS,GACVzc,EAAOiO,OAAO87B,WAAWG,SAAWlqC,EAAOqS,gBAC7C1K,EAAI6D,KAAK,uBAAuBtB,WAAWuS,KAKjD,IAAIwuB,GAAa,CACfnnC,KAAM,cACNmK,OAAQ,CACN87B,WAAY,CACVO,cAAc,EACdJ,QAAQ,EACRO,aAAc,GACdC,YAAa,MAGjB,SACE,MAAM1qC,EAASlC,KACfmO,EAAM3F,OAAOtG,EAAQ,CACnB+pC,WAAY,CACV1uB,aAAcuuB,GAAKvuB,aAAapX,KAAKjE,GACrCgY,cAAe4xB,GAAK5xB,cAAc/T,KAAKjE,OAI7C6B,GAAI,CACF,aACE,MAAM7B,EAASlC,KACf,GAA6B,SAAzBkC,EAAOiO,OAAO2I,OAAmB,OACrC5W,EAAOqsB,WAAWxb,KAAQ7Q,EAAOiO,OAAOud,uBAAjB,QACvBxrB,EAAOqsB,WAAWxb,KAAQ7Q,EAAOiO,OAAOud,uBAAjB,MACvB,MAAMsI,EAAkB,CACtBhf,cAAe,EACfJ,gBAAiB,EACjBa,eAAgB,EAChBkC,qBAAqB,EACrB+O,gBAAiB,EACjBtS,aAAc,EACdsC,gBAAgB,EAChB2E,kBAAkB,GAEpBlP,EAAM3F,OAAOtG,EAAOiO,OAAQ6lB,GAC5B7nB,EAAM3F,OAAOtG,EAAOqrB,eAAgByI,IAEtC,eACE,MAAM9zB,EAASlC,KACc,SAAzBkC,EAAOiO,OAAO2I,QAClB5W,EAAO+pC,WAAW1uB,gBAEpB,cAAcoB,GACZ,MAAMzc,EAASlC,KACc,SAAzBkC,EAAOiO,OAAO2I,QAClB5W,EAAO+pC,WAAW/xB,cAAcyE,MAKtC,MAAMyuB,GAAO,CACX,eACE,MAAMlrC,EAASlC,MACT,OAAE8C,EAAQgS,aAAcC,GAAQ7S,EACtC,IAAK,IAAIhB,EAAI,EAAGA,EAAI4B,EAAOzB,OAAQH,GAAK,EAAG,CACzC,MAAMu0B,EAAW3yB,EAAOmK,GAAG/L,GAC3B,IAAIka,EAAWqa,EAAS,GAAGra,SACvBlZ,EAAOiO,OAAOk9B,WAAWC,gBAC3BlyB,EAAWvE,KAAKK,IAAIL,KAAKgB,IAAI4d,EAAS,GAAGra,SAAU,IAAK,IAE1D,MAAM1O,EAAS+oB,EAAS,GAAGnb,kBACrBizB,GAAU,IAAMnyB,EACtB,IAAIoyB,EAAUD,EACVE,EAAU,EACVnC,GAAM5+B,EACN6+B,EAAK,EAYT,GAXKrpC,EAAOqS,eAKDQ,IACTy4B,GAAWA,IALXjC,EAAKD,EACLA,EAAK,EACLmC,GAAWD,EACXA,EAAU,GAKZ/X,EAAS,GAAGxd,MAAMy1B,QAAU72B,KAAK8B,IAAI9B,KAAKy1B,MAAMlxB,IAAatY,EAAOzB,OAEhEa,EAAOiO,OAAOk9B,WAAWb,aAAc,CAEzC,IAAIC,EAAevqC,EAAOqS,eAAiBkhB,EAAS/nB,KAAK,6BAA+B+nB,EAAS/nB,KAAK,4BAClGg/B,EAAcxqC,EAAOqS,eAAiBkhB,EAAS/nB,KAAK,8BAAgC+nB,EAAS/nB,KAAK,+BAC1E,IAAxB++B,EAAaprC,SACforC,EAAe,eAAE,mCAAmCvqC,EAAOqS,eAAiB,OAAS,iBACrFkhB,EAASvoB,OAAOu/B,IAES,IAAvBC,EAAYrrC,SACdqrC,EAAc,eAAE,mCAAmCxqC,EAAOqS,eAAiB,QAAU,oBACrFkhB,EAASvoB,OAAOw/B,IAEdD,EAAaprC,SAAQorC,EAAa,GAAGx0B,MAAM2mB,QAAU/nB,KAAKK,KAAKkE,EAAU,IACzEsxB,EAAYrrC,SAAQqrC,EAAY,GAAGz0B,MAAM2mB,QAAU/nB,KAAKK,IAAIkE,EAAU,IAE5Eqa,EACGtpB,UAAU,eAAem/B,QAASC,qBAAsBkC,iBAAuBD,WAGtF,cAAc7uB,GACZ,MAAMzc,EAASlC,MACT,OAAE8C,EAAM,YAAEsX,EAAW,WAAExF,GAAe1S,EAK5C,GAJAY,EACGsJ,WAAWuS,GACXjR,KAAK,gHACLtB,WAAWuS,GACVzc,EAAOiO,OAAOkN,kBAAiC,IAAbsB,EAAgB,CACpD,IAAIgtB,GAAiB,EAErB7oC,EAAOmK,GAAGmN,GAAa7N,eAAc,WACnC,GAAIo/B,EAAgB,OACpB,IAAKzpC,GAAUA,EAAOyE,UAAW,OAEjCglC,GAAiB,EACjBzpC,EAAOic,WAAY,EACnB,MAAMytB,EAAgB,CAAC,sBAAuB,iBAC9C,IAAK,IAAI1qC,EAAI,EAAGA,EAAI0qC,EAAcvqC,OAAQH,GAAK,EAC7C0T,EAAWtI,QAAQs/B,EAAc1qC,UAO3C,IAAIysC,GAAa,CACf3nC,KAAM,cACNmK,OAAQ,CACNk9B,WAAY,CACVb,cAAc,EACdc,eAAe,IAGnB,SACE,MAAMprC,EAASlC,KACfmO,EAAM3F,OAAOtG,EAAQ,CACnBmrC,WAAY,CACV9vB,aAAc6vB,GAAK7vB,aAAapX,KAAKjE,GACrCgY,cAAekzB,GAAKlzB,cAAc/T,KAAKjE,OAI7C6B,GAAI,CACF,aACE,MAAM7B,EAASlC,KACf,GAA6B,SAAzBkC,EAAOiO,OAAO2I,OAAmB,OACrC5W,EAAOqsB,WAAWxb,KAAQ7Q,EAAOiO,OAAOud,uBAAjB,QACvBxrB,EAAOqsB,WAAWxb,KAAQ7Q,EAAOiO,OAAOud,uBAAjB,MACvB,MAAMsI,EAAkB,CACtBhf,cAAe,EACfJ,gBAAiB,EACjBa,eAAgB,EAChBkC,qBAAqB,EACrBvD,aAAc,EACdiH,kBAAkB,GAEpBlP,EAAM3F,OAAOtG,EAAOiO,OAAQ6lB,GAC5B7nB,EAAM3F,OAAOtG,EAAOqrB,eAAgByI,IAEtC,eACE,MAAM9zB,EAASlC,KACc,SAAzBkC,EAAOiO,OAAO2I,QAClB5W,EAAOmrC,WAAW9vB,gBAEpB,cAAcoB,GACZ,MAAMzc,EAASlC,KACc,SAAzBkC,EAAOiO,OAAO2I,QAClB5W,EAAOmrC,WAAWnzB,cAAcyE,MAKtC,MAAMivB,GAAY,CAChB,eACE,MAAM1rC,EAASlC,MAEbmU,MAAO43B,EAAa33B,OAAQ43B,EAAY,OAAElpC,EAAM,WAAE8R,EAAU,gBAAEY,GAC5DtT,EACEiO,EAASjO,EAAOiO,OAAO09B,gBACvBt5B,EAAerS,EAAOqS,eACtBpI,EAAYjK,EAAOwY,UACnBozB,EAASv5B,EAA6Bw3B,EAAc,EAA3B5/B,EAA8C6/B,EAAe,EAA5B7/B,EAC1DohC,EAASh5B,EAAepE,EAAOo9B,QAAUp9B,EAAOo9B,OAChD7yB,EAAYvK,EAAO49B,MAEzB,IAAK,IAAI7sC,EAAI,EAAGG,EAASyB,EAAOzB,OAAQH,EAAIG,EAAQH,GAAK,EAAG,CAC1D,MAAMu0B,EAAW3yB,EAAOmK,GAAG/L,GACrBiV,EAAYX,EAAgBtU,GAC5B8sC,EAAcvY,EAAS,GAAGnb,kBAC1B2zB,GAAqBH,EAASE,EAAe73B,EAAY,GAAMA,EAAahG,EAAO+9B,SAEzF,IAAIV,EAAUj5B,EAAeg5B,EAASU,EAAmB,EACrDR,EAAUl5B,EAAe,EAAIg5B,EAASU,EAEtCE,GAAczzB,EAAY7D,KAAK8B,IAAIs1B,GAEnCG,EAAUj+B,EAAOi+B,QAEE,kBAAZA,IAAkD,IAA1BA,EAAQ/nC,QAAQ,OACjD+nC,EAAYr+B,WAAWI,EAAOi+B,SAAW,IAAOj4B,GAElD,IAAIqtB,EAAajvB,EAAe,EAAI65B,EAAU,EAC1C7K,EAAahvB,EAAe65B,EAAU,EAAqB,EAG3Dv3B,KAAK8B,IAAI4qB,GAAc,OAAOA,EAAa,GAC3C1sB,KAAK8B,IAAI6qB,GAAc,OAAOA,EAAa,GAC3C3sB,KAAK8B,IAAIw1B,GAAc,OAAOA,EAAa,GAC3Ct3B,KAAK8B,IAAI60B,GAAW,OAAOA,EAAU,GACrC32B,KAAK8B,IAAI80B,GAAW,OAAOA,EAAU,GAEzC,MAAMY,EAAiB,eAAe9K,OAAgBC,OAAgB2K,iBAA0BV,iBAAuBD,QAIvH,GAFA/X,EAAStpB,UAAUkiC,GACnB5Y,EAAS,GAAGxd,MAAMy1B,OAAmD,EAAzC72B,KAAK8B,IAAI9B,KAAKy1B,MAAM2B,IAC5C99B,EAAOq8B,aAAc,CAEvB,IAAI8B,EAAkB/5B,EAAekhB,EAAS/nB,KAAK,6BAA+B+nB,EAAS/nB,KAAK,4BAC5F6gC,EAAiBh6B,EAAekhB,EAAS/nB,KAAK,8BAAgC+nB,EAAS/nB,KAAK,+BACjE,IAA3B4gC,EAAgBjtC,SAClBitC,EAAkB,eAAE,mCAAmC/5B,EAAe,OAAS,iBAC/EkhB,EAASvoB,OAAOohC,IAEY,IAA1BC,EAAeltC,SACjBktC,EAAiB,eAAE,mCAAmCh6B,EAAe,QAAU,oBAC/EkhB,EAASvoB,OAAOqhC,IAEdD,EAAgBjtC,SAAQitC,EAAgB,GAAGr2B,MAAM2mB,QAAUqP,EAAmB,EAAIA,EAAmB,GACrGM,EAAeltC,SAAQktC,EAAe,GAAGt2B,MAAM2mB,SAAYqP,EAAoB,GAAKA,EAAmB,IAK/G,GAAI38B,EAAQG,eAAiBH,EAAQk9B,sBAAuB,CAC1D,MAAMC,EAAK75B,EAAW,GAAGqD,MACzBw2B,EAAGC,kBAAuBZ,EAAH,WAG3B,cAAcnvB,GACZ,MAAMzc,EAASlC,KACfkC,EAAOY,OACJsJ,WAAWuS,GACXjR,KAAK,gHACLtB,WAAWuS,KAIlB,IAAIgwB,GAAkB,CACpB3oC,KAAM,mBACNmK,OAAQ,CACN09B,gBAAiB,CACfN,OAAQ,GACRa,QAAS,EACTL,MAAO,IACPG,SAAU,EACV1B,cAAc,IAGlB,SACE,MAAMtqC,EAASlC,KACfmO,EAAM3F,OAAOtG,EAAQ,CACnB2rC,gBAAiB,CACftwB,aAAcqwB,GAAUrwB,aAAapX,KAAKjE,GAC1CgY,cAAe0zB,GAAU1zB,cAAc/T,KAAKjE,OAIlD6B,GAAI,CACF,aACE,MAAM7B,EAASlC,KACc,cAAzBkC,EAAOiO,OAAO2I,SAElB5W,EAAOqsB,WAAWxb,KAAQ7Q,EAAOiO,OAAOud,uBAAjB,aACvBxrB,EAAOqsB,WAAWxb,KAAQ7Q,EAAOiO,OAAOud,uBAAjB,MAEvBxrB,EAAOiO,OAAOwJ,qBAAsB,EACpCzX,EAAOqrB,eAAe5T,qBAAsB,IAE9C,eACE,MAAMzX,EAASlC,KACc,cAAzBkC,EAAOiO,OAAO2I,QAClB5W,EAAO2rC,gBAAgBtwB,gBAEzB,cAAcoB,GACZ,MAAMzc,EAASlC,KACc,cAAzBkC,EAAOiO,OAAO2I,QAClB5W,EAAO2rC,gBAAgB3zB,cAAcyE,MAK3C,MAAMiwB,GAAS,CACb,OACE,MAAM1sC,EAASlC,MACP6uC,OAAQC,GAAiB5sC,EAAOiO,OAClClQ,EAAciC,EAAOyO,YACvBm+B,EAAa5sC,kBAAkBjC,GACjCiC,EAAO2sC,OAAO3sC,OAAS4sC,EAAa5sC,OACpCiM,EAAM3F,OAAOtG,EAAO2sC,OAAO3sC,OAAOqrB,eAAgB,CAChD5T,qBAAqB,EACrBwD,qBAAqB,IAEvBhP,EAAM3F,OAAOtG,EAAO2sC,OAAO3sC,OAAOiO,OAAQ,CACxCwJ,qBAAqB,EACrBwD,qBAAqB,KAEdhP,EAAMkD,SAASy9B,EAAa5sC,UACrCA,EAAO2sC,OAAO3sC,OAAS,IAAIjC,EAAYkO,EAAM3F,OAAO,GAAIsmC,EAAa5sC,OAAQ,CAC3E0X,uBAAuB,EACvBD,qBAAqB,EACrBwD,qBAAqB,KAEvBjb,EAAO2sC,OAAOE,eAAgB,GAEhC7sC,EAAO2sC,OAAO3sC,OAAO2H,IAAIgC,SAAS3J,EAAOiO,OAAO0+B,OAAOG,sBACvD9sC,EAAO2sC,OAAO3sC,OAAO6B,GAAG,MAAO7B,EAAO2sC,OAAOI,eAE/C,eACE,MAAM/sC,EAASlC,KACTkvC,EAAehtC,EAAO2sC,OAAO3sC,OACnC,IAAKgtC,EAAc,OACnB,MAAM/rC,EAAe+rC,EAAa/rC,aAC5BG,EAAe4rC,EAAa5rC,aAClC,GAAIA,GAAgB,eAAEA,GAAcyI,SAAS7J,EAAOiO,OAAO0+B,OAAOM,uBAAwB,OAC1F,GAA4B,qBAAjBhsC,GAAiD,OAAjBA,EAAuB,OAClE,IAAIyd,EAMJ,GAJEA,EADEsuB,EAAa/+B,OAAO1I,KACPgN,SAAS,eAAEy6B,EAAa5rC,cAAc2I,KAAK,2BAA4B,IAEvE9I,EAEbjB,EAAOiO,OAAO1I,KAAM,CACtB,IAAI2nC,EAAeltC,EAAOkY,YACtBlY,EAAOY,OAAOmK,GAAGmiC,GAAcrjC,SAAS7J,EAAOiO,OAAOmM,uBACxDpa,EAAO0d,UAEP1d,EAAO2d,YAAc3d,EAAO0S,WAAW,GAAGkL,WAC1CsvB,EAAeltC,EAAOkY,aAExB,MAAMgG,EAAYle,EAAOY,OAAOmK,GAAGmiC,GAAc7hC,QAAQ,6BAA6BqT,OAAkB3T,GAAG,GAAGD,QACxG+D,EAAY7O,EAAOY,OAAOmK,GAAGmiC,GAAc/hC,QAAQ,6BAA6BuT,OAAkB3T,GAAG,GAAGD,QACxE4T,EAAb,qBAAdR,EAA0CrP,EACvB,qBAAdA,EAA0CqP,EACjDrP,EAAYq+B,EAAeA,EAAehvB,EAA0BrP,EACzDqP,EAEtBle,EAAO6c,QAAQ6B,IAEjB,OAAOyuB,GACL,MAAMntC,EAASlC,KACTkvC,EAAehtC,EAAO2sC,OAAO3sC,OACnC,IAAKgtC,EAAc,OAEnB,MAAMl4B,EAAsD,SAAtCk4B,EAAa/+B,OAAO6G,cACtCk4B,EAAavuB,uBACbuuB,EAAa/+B,OAAO6G,cAElBs4B,EAAmBptC,EAAOiO,OAAO0+B,OAAOS,iBACxCC,EAAYD,IAAqBJ,EAAa/+B,OAAO1I,KAC3D,GAAIvF,EAAO4Z,YAAcozB,EAAapzB,WAAayzB,EAAW,CAC5D,IACIC,EACA3wB,EAFA4wB,EAAqBP,EAAa90B,YAGtC,GAAI80B,EAAa/+B,OAAO1I,KAAM,CACxBynC,EAAapsC,OAAOmK,GAAGwiC,GAAoB1jC,SAASmjC,EAAa/+B,OAAOmM,uBAC1E4yB,EAAatvB,UAEbsvB,EAAarvB,YAAcqvB,EAAat6B,WAAW,GAAGkL,WACtD2vB,EAAqBP,EAAa90B,aAGpC,MAAMs1B,EAAkBR,EAAapsC,OAClCmK,GAAGwiC,GACHliC,QAAQ,6BAA6BrL,EAAO4Z,eAAe7O,GAAG,GAC9DD,QACG2iC,EAAkBT,EAAapsC,OAClCmK,GAAGwiC,GACHpiC,QAAQ,6BAA6BnL,EAAO4Z,eAAe7O,GAAG,GAC9DD,QACyCwiC,EAAb,qBAApBE,EAAkDC,EACzB,qBAApBA,EAAkDD,EACzDC,EAAkBF,IAAuBA,EAAqBC,EAAkCD,EAChGE,EAAkBF,EAAqBA,EAAqBC,EAAkCC,EACjFD,EACtB7wB,EAAY3c,EAAOkY,YAAclY,EAAOya,cAAgB,OAAS,YAEjE6yB,EAAiBttC,EAAO4Z,UACxB+C,EAAY2wB,EAAiBttC,EAAOya,cAAgB,OAAS,OAE3D4yB,IACFC,GAAgC,SAAd3wB,EAAuBywB,GAAoB,EAAIA,GAG/DJ,EAAar0B,sBAAwBq0B,EAAar0B,qBAAqBxU,QAAQmpC,GAAkB,IAC/FN,EAAa/+B,OAAOuI,eAEpB82B,EADEA,EAAiBC,EACFD,EAAiB34B,KAAKC,MAAME,EAAgB,GAAK,EAEjDw4B,EAAiB34B,KAAKC,MAAME,EAAgB,GAAK,EAE3Dw4B,EAAiBC,IAC1BD,EAAiBA,EAAiBx4B,EAAgB,GAEpDk4B,EAAanwB,QAAQywB,EAAgBH,EAAU,OAAItqC,IAKvD,IAAI6qC,EAAmB,EACvB,MAAMC,EAAmB3tC,EAAOiO,OAAO0+B,OAAOM,sBAa9C,GAXIjtC,EAAOiO,OAAO6G,cAAgB,IAAM9U,EAAOiO,OAAOuI,iBACpDk3B,EAAmB1tC,EAAOiO,OAAO6G,eAG9B9U,EAAOiO,OAAO0+B,OAAOiB,uBACxBF,EAAmB,GAGrBA,EAAmB/4B,KAAKC,MAAM84B,GAE9BV,EAAapsC,OAAOgJ,YAAY+jC,GAC5BX,EAAa/+B,OAAO1I,MAASynC,EAAa/+B,OAAO+E,SAAWg6B,EAAa/+B,OAAO+E,QAAQC,QAC1F,IAAK,IAAIjU,EAAI,EAAGA,EAAI0uC,EAAkB1uC,GAAK,EACzCguC,EAAat6B,WAAWjH,SAAS,6BAA6BzL,EAAO4Z,UAAY5a,OAAO2K,SAASgkC,QAGnG,IAAK,IAAI3uC,EAAI,EAAGA,EAAI0uC,EAAkB1uC,GAAK,EACzCguC,EAAapsC,OAAOmK,GAAG/K,EAAO4Z,UAAY5a,GAAG2K,SAASgkC,KAK9D,IAAIE,GAAW,CACb/pC,KAAM,SACNmK,OAAQ,CACN0+B,OAAQ,CACN3sC,OAAQ,KACR4tC,sBAAsB,EACtBR,iBAAkB,EAClBH,sBAAuB,4BACvBH,qBAAsB,4BAG1B,SACE,MAAM9sC,EAASlC,KACfmO,EAAM3F,OAAOtG,EAAQ,CACnB2sC,OAAQ,CACN3sC,OAAQ,KACRkuB,KAAMwe,GAAOxe,KAAKjqB,KAAKjE,GACvByF,OAAQinC,GAAOjnC,OAAOxB,KAAKjE,GAC3B+sC,aAAcL,GAAOK,aAAa9oC,KAAKjE,OAI7C6B,GAAI,CACF,aACE,MAAM7B,EAASlC,MACT,OAAE6uC,GAAW3sC,EAAOiO,OACrB0+B,GAAWA,EAAO3sC,SACvBA,EAAO2sC,OAAOze,OACdluB,EAAO2sC,OAAOlnC,QAAO,KAEvB,cACE,MAAMzF,EAASlC,KACVkC,EAAO2sC,OAAO3sC,QACnBA,EAAO2sC,OAAOlnC,UAEhB,SACE,MAAMzF,EAASlC,KACVkC,EAAO2sC,OAAO3sC,QACnBA,EAAO2sC,OAAOlnC,UAEhB,SACE,MAAMzF,EAASlC,KACVkC,EAAO2sC,OAAO3sC,QACnBA,EAAO2sC,OAAOlnC,UAEhB,iBACE,MAAMzF,EAASlC,KACVkC,EAAO2sC,OAAO3sC,QACnBA,EAAO2sC,OAAOlnC,UAEhB,cAAcgX,GACZ,MAAMzc,EAASlC,KACTkvC,EAAehtC,EAAO2sC,OAAO3sC,OAC9BgtC,GACLA,EAAah1B,cAAcyE,IAE7B,gBACE,MAAMzc,EAASlC,KACTkvC,EAAehtC,EAAO2sC,OAAO3sC,OAC9BgtC,GACDhtC,EAAO2sC,OAAOE,eAAiBG,GACjCA,EAAa9mC,aAQrB,MAAMuL,GAAa,CACjB0e,GACAC,GACAO,GACAE,GACAsB,GACA0B,GACAqB,GACA0D,GACAQ,GACA6C,GACAgC,GACAU,GACAwD,GACA4B,GACAqB,GACAsB,GACAmB,GACAS,GACAO,GACAc,GACAsB,GACAQ,GACAgB,GACAoB,IAGwB,qBAAftkC,GAAOoI,MAChBpI,GAAOoI,IAAMpI,GAAOmI,MAAMC,IAC1BpI,GAAOwI,cAAgBxI,GAAOmI,MAAMK,eAGtCxI,GAAOoI,IAAIF,IAEI,iB", "file": "js/chunk-tool.0f3d1c53.js", "sourcesContent": ["\n/*!\n * vue-awesome-swiper v4.1.1\n * Copyright (c) Surmon. All rights reserved.\n * Released under the MIT License.\n * Surmon <https://github.com/surmon-china>\n */\n\n(function(g,f){typeof exports==='object'&&typeof module!=='undefined'?f(exports,require('swiper'),require('vue')):typeof define==='function'&&define.amd?define(['exports','swiper','vue'],f):(g=g||self,f(g.VueAwesomeSwiper={},g.Swiper,g.Vue));}(this,(function(exports, SwiperClass, Vue){'use strict';SwiperClass=SwiperClass&&Object.prototype.hasOwnProperty.call(SwiperClass,'default')?SwiperClass['default']:SwiperClass;Vue=Vue&&Object.prototype.hasOwnProperty.call(Vue,'default')?Vue['default']:Vue;/**\r\n * @file vue-awesome-swiper\r\n * @module constants\r\n * <AUTHOR> <https://github.com/surmon-china>\r\n */\r\nvar CoreNames;\r\n(function (CoreNames) {\r\n    CoreNames[\"SwiperComponent\"] = \"Swiper\";\r\n    CoreNames[\"SwiperSlideComponent\"] = \"SwiperSlide\";\r\n    CoreNames[\"SwiperDirective\"] = \"swiper\";\r\n    CoreNames[\"SwiperInstance\"] = \"$swiper\";\r\n})(CoreNames || (CoreNames = {}));\r\nvar DEFAULT_CLASSES = Object.freeze({\r\n    containerClass: 'swiper-container',\r\n    wrapperClass: 'swiper-wrapper',\r\n    slideClass: 'swiper-slide'\r\n});\r\nvar ComponentEvents;\r\n(function (ComponentEvents) {\r\n    ComponentEvents[\"Ready\"] = \"ready\";\r\n    ComponentEvents[\"ClickSlide\"] = \"clickSlide\";\r\n})(ComponentEvents || (ComponentEvents = {}));\r\nvar ComponentPropNames;\r\n(function (ComponentPropNames) {\r\n    ComponentPropNames[\"AutoUpdate\"] = \"autoUpdate\";\r\n    ComponentPropNames[\"AutoDestroy\"] = \"autoDestroy\";\r\n    ComponentPropNames[\"DeleteInstanceOnDestroy\"] = \"deleteInstanceOnDestroy\";\r\n    ComponentPropNames[\"CleanupStylesOnDestroy\"] = \"cleanupStylesOnDestroy\";\r\n})(ComponentPropNames || (ComponentPropNames = {}));\r\n// https://swiperjs.com/api/#events\r\nvar SWIPER_EVENTS = [\r\n    'init',\r\n    'beforeDestroy',\r\n    'slideChange',\r\n    'slideChangeTransitionStart',\r\n    'slideChangeTransitionEnd',\r\n    'slideNextTransitionStart',\r\n    'slideNextTransitionEnd',\r\n    'slidePrevTransitionStart',\r\n    'slidePrevTransitionEnd',\r\n    'transitionStart',\r\n    'transitionEnd',\r\n    'touchStart',\r\n    'touchMove',\r\n    'touchMoveOpposite',\r\n    'sliderMove',\r\n    'touchEnd',\r\n    'click',\r\n    'tap',\r\n    'doubleTap',\r\n    'imagesReady',\r\n    'progress',\r\n    'reachBeginning',\r\n    'reachEnd',\r\n    'fromEdge',\r\n    'setTranslate',\r\n    'setTransition',\r\n    'resize',\r\n    'observerUpdate',\r\n    'beforeLoopFix',\r\n    'loopFix'\r\n];/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation. All rights reserved.\r\nLicensed under the Apache License, Version 2.0 (the \"License\"); you may not use\r\nthis file except in compliance with the License. You may obtain a copy of the\r\nLicense at http://www.apache.org/licenses/LICENSE-2.0\r\n\r\nTHIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\nKIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED\r\nWARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,\r\nMERCHANTABLITY OR NON-INFRINGEMENT.\r\n\r\nSee the Apache Version 2.0 License for specific language governing permissions\r\nand limitations under the License.\r\n***************************************************************************** */\r\n\r\nfunction __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n}/**\r\n * @file vue-awesome-swiper\r\n * @module utils\r\n * <AUTHOR> <https://github.com/surmon-china>\r\n */\r\nvar kebabcase = function (string) {\r\n    return string\r\n        .replace(/([a-z])([A-Z])/g, '$1-$2')\r\n        .replace(/\\s+/g, '-')\r\n        .toLowerCase();\r\n};/**\r\n * @file vue-awesome-swiper\r\n * @module event\r\n * <AUTHOR> <https://github.com/surmon-china>\r\n */\r\nvar handleClickSlideEvent = function (swiper, event, emit) {\r\n    var _a, _b, _c;\r\n    if (swiper && !(swiper.destroyed)) {\r\n        var eventPath = ((_a = event.composedPath) === null || _a === void 0 ? void 0 : _a.call(event)) || event.path;\r\n        if ((event === null || event === void 0 ? void 0 : event.target) && eventPath) {\r\n            var slides_1 = Array.from(swiper.slides);\r\n            var paths = Array.from(eventPath);\r\n            // Click slide || slide[children]\r\n            if (slides_1.includes(event.target) || paths.some(function (item) { return slides_1.includes(item); })) {\r\n                var clickedIndex = swiper.clickedIndex;\r\n                var reallyIndex = Number((_c = (_b = swiper.clickedSlide) === null || _b === void 0 ? void 0 : _b.dataset) === null || _c === void 0 ? void 0 : _c.swiperSlideIndex);\r\n                var reallyIndexValue = Number.isInteger(reallyIndex) ? reallyIndex : null;\r\n                emit(ComponentEvents.ClickSlide, clickedIndex, reallyIndexValue);\r\n                emit(kebabcase(ComponentEvents.ClickSlide), clickedIndex, reallyIndexValue);\r\n            }\r\n        }\r\n    }\r\n};\r\nvar bindSwiperEvents = function (swiper, emit) {\r\n    SWIPER_EVENTS.forEach(function (eventName) {\r\n        swiper.on(eventName, function () {\n            var arguments$1 = arguments;\n\r\n            var args = [];\r\n            for (var _i = 0; _i < arguments.length; _i++) {\r\n                args[_i] = arguments$1[_i];\r\n            }\r\n            emit.apply(void 0, __spreadArrays([eventName], args));\r\n            var kebabcaseName = kebabcase(eventName);\r\n            if (kebabcaseName !== eventName) {\r\n                emit.apply(void 0, __spreadArrays([kebabcaseName], args));\r\n            }\r\n        });\r\n    });\r\n};/**\r\n * @file vue-awesome-swiper\r\n * @module directive\r\n * <AUTHOR> <https://github.com/surmon-china>\r\n */\r\nvar INSTANCE_NAME_KEY = 'instanceName';\r\nfunction getDirective(SwiperClass, globalOptions) {\r\n    var getStandardisedOptionByAttrs = function (vnode, key) {\r\n        var _a, _b, _c, _d;\r\n        var value = (_b = (_a = vnode.data) === null || _a === void 0 ? void 0 : _a.attrs) === null || _b === void 0 ? void 0 : _b[key];\r\n        return value !== undefined\r\n            ? value\r\n            : (_d = (_c = vnode.data) === null || _c === void 0 ? void 0 : _c.attrs) === null || _d === void 0 ? void 0 : _d[kebabcase(key)];\r\n    };\r\n    // Get swiper instace name in directive\r\n    var getSwiperInstanceName = function (element, binding, vnode) {\r\n        return (binding.arg ||\r\n            getStandardisedOptionByAttrs(vnode, INSTANCE_NAME_KEY) ||\r\n            element.id ||\r\n            CoreNames.SwiperInstance);\r\n    };\r\n    var getSwiperInstance = function (element, binding, vnode) {\r\n        var instanceName = getSwiperInstanceName(element, binding, vnode);\r\n        return vnode.context[instanceName] || null;\r\n    };\r\n    var getSwipeOptions = function (binding) {\r\n        return binding.value || globalOptions;\r\n    };\r\n    var getBooleanValueByInput = function (input) {\r\n        return [true, undefined, null, ''].includes(input);\r\n    };\r\n    // Emit event in Vue directive\r\n    var getEventEmiter = function (vnode) {\r\n        var _a, _b;\r\n        var handlers = ((_a = vnode.data) === null || _a === void 0 ? void 0 : _a.on) || ((_b = vnode.componentOptions) === null || _b === void 0 ? void 0 : _b.listeners);\r\n        return function (name) {\n            var arguments$1 = arguments;\n\r\n            var args = [];\r\n            for (var _i = 1; _i < arguments.length; _i++) {\r\n                args[_i - 1] = arguments$1[_i];\r\n            }\r\n            var _a;\r\n            var handle = (_a = handlers) === null || _a === void 0 ? void 0 : _a[name];\r\n            if (handle) {\r\n                handle.fns.apply(handle, args);\r\n            }\r\n        };\r\n    };\r\n    return {\r\n        // Init\r\n        bind: function (element, binding, vnode) {\r\n            // auto class name\r\n            if (element.className.indexOf(DEFAULT_CLASSES.containerClass) === -1) {\r\n                element.className += ((element.className ? ' ' : '') + DEFAULT_CLASSES.containerClass);\r\n            }\r\n            // bind click event\r\n            element.addEventListener('click', function (event) {\r\n                var emitEvent = getEventEmiter(vnode);\r\n                var swiper = getSwiperInstance(element, binding, vnode);\r\n                handleClickSlideEvent(swiper, event, emitEvent);\r\n            });\r\n        },\r\n        // DOM inserted\r\n        inserted: function (element, binding, vnode) {\r\n            var context = vnode.context;\r\n            var swiperOptions = getSwipeOptions(binding);\r\n            var instanceName = getSwiperInstanceName(element, binding, vnode);\r\n            var emitEvent = getEventEmiter(vnode);\r\n            var vueContext = context;\r\n            var swiper = vueContext === null || vueContext === void 0 ? void 0 : vueContext[instanceName];\r\n            // Swiper will destroy but not delete instance, when used <keep-alive>\r\n            if (!swiper || swiper.destroyed) {\r\n                swiper = new SwiperClass(element, swiperOptions);\r\n                vueContext[instanceName] = swiper;\r\n                bindSwiperEvents(swiper, emitEvent);\r\n                emitEvent(ComponentEvents.Ready, swiper);\r\n                // MARK: Reinstance when the nexttick with <keep-alive>\r\n                // Vue.nextTick(instancing) | setTimeout(instancing)\r\n            }\r\n        },\r\n        // On options changed or DOM updated\r\n        componentUpdated: function (element, binding, vnode) {\r\n            var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m;\r\n            var autoUpdate = getStandardisedOptionByAttrs(vnode, ComponentPropNames.AutoUpdate);\r\n            if (getBooleanValueByInput(autoUpdate)) {\r\n                var swiper = getSwiperInstance(element, binding, vnode);\r\n                if (swiper) {\r\n                    var swiperOptions = getSwipeOptions(binding);\r\n                    var isLoop = swiperOptions.loop;\r\n                    if (isLoop) {\r\n                        (_b = (_a = swiper) === null || _a === void 0 ? void 0 : _a.loopDestroy) === null || _b === void 0 ? void 0 : _b.call(_a);\r\n                    }\r\n                    (_c = swiper === null || swiper === void 0 ? void 0 : swiper.update) === null || _c === void 0 ? void 0 : _c.call(swiper);\r\n                    (_e = (_d = swiper.navigation) === null || _d === void 0 ? void 0 : _d.update) === null || _e === void 0 ? void 0 : _e.call(_d);\r\n                    (_g = (_f = swiper.pagination) === null || _f === void 0 ? void 0 : _f.render) === null || _g === void 0 ? void 0 : _g.call(_f);\r\n                    (_j = (_h = swiper.pagination) === null || _h === void 0 ? void 0 : _h.update) === null || _j === void 0 ? void 0 : _j.call(_h);\r\n                    if (isLoop) {\r\n                        (_l = (_k = swiper) === null || _k === void 0 ? void 0 : _k.loopCreate) === null || _l === void 0 ? void 0 : _l.call(_k);\r\n                        (_m = swiper === null || swiper === void 0 ? void 0 : swiper.update) === null || _m === void 0 ? void 0 : _m.call(swiper);\r\n                    }\r\n                }\r\n            }\r\n        },\r\n        // Destroy this directive\r\n        unbind: function (element, binding, vnode) {\r\n            var _a;\r\n            var autoDestroy = getStandardisedOptionByAttrs(vnode, ComponentPropNames.AutoDestroy);\r\n            if (getBooleanValueByInput(autoDestroy)) {\r\n                var swiper = getSwiperInstance(element, binding, vnode);\r\n                if (swiper && swiper.initialized) {\r\n                    (_a = swiper === null || swiper === void 0 ? void 0 : swiper.destroy) === null || _a === void 0 ? void 0 : _a.call(swiper, getBooleanValueByInput(getStandardisedOptionByAttrs(vnode, ComponentPropNames.DeleteInstanceOnDestroy)), getBooleanValueByInput(getStandardisedOptionByAttrs(vnode, ComponentPropNames.CleanupStylesOnDestroy)));\r\n                }\r\n            }\r\n        }\r\n    };\r\n}/**\r\n * @file vue-awesome-swiper\r\n * @module SwiperComponent\r\n * <AUTHOR> <https://github.com/surmon-china>\r\n */\r\nvar SlotNames;\r\n(function (SlotNames) {\r\n    SlotNames[\"ParallaxBg\"] = \"parallax-bg\";\r\n    SlotNames[\"Pagination\"] = \"pagination\";\r\n    SlotNames[\"Scrollbar\"] = \"scrollbar\";\r\n    SlotNames[\"PrevButton\"] = \"button-prev\";\r\n    SlotNames[\"NextButton\"] = \"button-next\";\r\n})(SlotNames || (SlotNames = {}));\r\nfunction getSwiperComponent(SwiperClass) {\r\n    var _a;\r\n    return Vue.extend({\r\n        name: CoreNames.SwiperComponent,\r\n        props: (_a = {\r\n                defaultOptions: {\r\n                    type: Object,\r\n                    required: false,\r\n                    default: function () { return ({}); }\r\n                },\r\n                // eslint-disable-next-line vue/require-default-prop\r\n                options: {\r\n                    type: Object,\r\n                    required: false\r\n                }\r\n            },\r\n            _a[ComponentPropNames.AutoUpdate] = {\r\n                type: Boolean,\r\n                default: true\r\n            },\r\n            // https://github.com/surmon-china/vue-awesome-swiper/pull/550/files\r\n            _a[ComponentPropNames.AutoDestroy] = {\r\n                type: Boolean,\r\n                default: true\r\n            },\r\n            // https://github.com/surmon-china/vue-awesome-swiper/pull/388\r\n            _a[ComponentPropNames.DeleteInstanceOnDestroy] = {\r\n                type: Boolean,\r\n                required: false,\r\n                default: true\r\n            },\r\n            _a[ComponentPropNames.CleanupStylesOnDestroy] = {\r\n                type: Boolean,\r\n                required: false,\r\n                default: true\r\n            },\r\n            _a),\r\n        data: function () {\r\n            var _a;\r\n            return _a = {},\r\n                _a[CoreNames.SwiperInstance] = null,\r\n                _a;\r\n        },\r\n        computed: {\r\n            swiperInstance: {\r\n                cache: false,\r\n                set: function (swiper) {\r\n                    this[CoreNames.SwiperInstance] = swiper;\r\n                },\r\n                get: function () {\r\n                    return this[CoreNames.SwiperInstance];\r\n                }\r\n            },\r\n            swiperOptions: function () {\r\n                return this.options || this.defaultOptions;\r\n            },\r\n            wrapperClass: function () {\r\n                return this.swiperOptions.wrapperClass || DEFAULT_CLASSES.wrapperClass;\r\n            }\r\n        },\r\n        methods: {\r\n            // Feature: click event\r\n            handleSwiperClick: function (event) {\r\n                handleClickSlideEvent(this.swiperInstance, event, this.$emit.bind(this));\r\n            },\r\n            autoReLoopSwiper: function () {\r\n                var _a, _b;\r\n                if (this.swiperInstance && this.swiperOptions.loop) {\r\n                    // https://github.com/surmon-china/vue-awesome-swiper/issues/593\r\n                    // https://github.com/surmon-china/vue-awesome-swiper/issues/544\r\n                    // https://github.com/surmon-china/vue-awesome-swiper/pull/545/files\r\n                    var swiper = this.swiperInstance;\r\n                    (_a = swiper === null || swiper === void 0 ? void 0 : swiper.loopDestroy) === null || _a === void 0 ? void 0 : _a.call(swiper);\r\n                    (_b = swiper === null || swiper === void 0 ? void 0 : swiper.loopCreate) === null || _b === void 0 ? void 0 : _b.call(swiper);\r\n                }\r\n            },\r\n            updateSwiper: function () {\r\n                var _a, _b, _c, _d, _e, _f, _g, _h;\r\n                if (this[ComponentPropNames.AutoUpdate] && this.swiperInstance) {\r\n                    this.autoReLoopSwiper();\r\n                    (_b = (_a = this.swiperInstance) === null || _a === void 0 ? void 0 : _a.update) === null || _b === void 0 ? void 0 : _b.call(_a);\r\n                    (_d = (_c = this.swiperInstance.navigation) === null || _c === void 0 ? void 0 : _c.update) === null || _d === void 0 ? void 0 : _d.call(_c);\r\n                    (_f = (_e = this.swiperInstance.pagination) === null || _e === void 0 ? void 0 : _e.render) === null || _f === void 0 ? void 0 : _f.call(_e);\r\n                    (_h = (_g = this.swiperInstance.pagination) === null || _g === void 0 ? void 0 : _g.update) === null || _h === void 0 ? void 0 : _h.call(_g);\r\n                }\r\n            },\r\n            destroySwiper: function () {\r\n                var _a, _b;\r\n                if (this[ComponentPropNames.AutoDestroy] && this.swiperInstance) {\r\n                    // https://github.com/surmon-china/vue-awesome-swiper/pull/341\r\n                    // https://github.com/surmon-china/vue-awesome-swiper/issues/340\r\n                    if (this.swiperInstance.initialized) {\r\n                        (_b = (_a = this.swiperInstance) === null || _a === void 0 ? void 0 : _a.destroy) === null || _b === void 0 ? void 0 : _b.call(_a, this[ComponentPropNames.DeleteInstanceOnDestroy], this[ComponentPropNames.CleanupStylesOnDestroy]);\r\n                    }\r\n                }\r\n            },\r\n            initSwiper: function () {\r\n                this.swiperInstance = new SwiperClass(this.$el, this.swiperOptions);\r\n                bindSwiperEvents(this.swiperInstance, this.$emit.bind(this));\r\n                this.$emit(ComponentEvents.Ready, this.swiperInstance);\r\n            }\r\n        },\r\n        mounted: function () {\r\n            if (!this.swiperInstance) {\r\n                this.initSwiper();\r\n            }\r\n        },\r\n        // Update swiper when the parent component activated with `keep-alive`.\r\n        activated: function () {\r\n            this.updateSwiper();\r\n        },\r\n        updated: function () {\r\n            this.updateSwiper();\r\n        },\r\n        beforeDestroy: function () {\r\n            // https://github.com/surmon-china/vue-awesome-swiper/commit/2924a9d4d3d1cf51c0d46076410b1f804b2b8a43#diff-7f4e0261ac562c0f354cb91a1ca8864f\r\n            this.$nextTick(this.destroySwiper);\r\n        },\r\n        render: function (createElement) {\r\n            return createElement('div', {\r\n                staticClass: DEFAULT_CLASSES.containerClass,\r\n                on: {\r\n                    click: this.handleSwiperClick\r\n                }\r\n            }, [\r\n                this.$slots[SlotNames.ParallaxBg],\r\n                createElement('div', {\r\n                    class: this.wrapperClass\r\n                }, this.$slots.default),\r\n                this.$slots[SlotNames.Pagination],\r\n                this.$slots[SlotNames.PrevButton],\r\n                this.$slots[SlotNames.NextButton],\r\n                this.$slots[SlotNames.Scrollbar]\r\n            ]);\r\n        }\r\n    });\r\n}/**\r\n * @file vue-awesome-swiper\r\n * @module SwiperSlideComponent\r\n * <AUTHOR> <https://github.com/surmon-china>\r\n */\r\nvar SwiperSlideComponent = Vue.extend({\r\n    name: CoreNames.SwiperSlideComponent,\r\n    computed: {\r\n        slideClass: function () {\r\n            var _a, _b;\r\n            return ((_b = (_a = this.$parent) === null || _a === void 0 ? void 0 : _a.swiperOptions) === null || _b === void 0 ? void 0 : _b.slideClass) || DEFAULT_CLASSES.slideClass;\r\n        }\r\n    },\r\n    methods: {\r\n        update: function () {\r\n            var _a;\r\n            var parent = this.$parent;\r\n            // https://github.com/surmon-china/vue-awesome-swiper/issues/632\r\n            if (parent[ComponentPropNames.AutoUpdate]) {\r\n                (_a = parent === null || parent === void 0 ? void 0 : parent.swiperInstance) === null || _a === void 0 ? void 0 : _a.update();\r\n            }\r\n        }\r\n    },\r\n    mounted: function () {\r\n        this.update();\r\n    },\r\n    updated: function () {\r\n        this.update();\r\n    },\r\n    render: function (createElement) {\r\n        return createElement('div', {\r\n            class: this.slideClass\r\n        }, this.$slots.default);\r\n    }\r\n});/**\r\n * @file vue-awesome-swiper\r\n * @module exporter\r\n * <AUTHOR> <https://github.com/surmon-china>\r\n */\r\nvar getInstaller = function (SwiperClass) {\r\n    var install = function (Vue, globalOptions) {\r\n        if (install.installed)\r\n            { return; }\r\n        var SwiperComponent = getSwiperComponent(SwiperClass);\r\n        if (globalOptions) {\r\n            SwiperComponent.options.props.defaultOptions.default = function () { return globalOptions; };\r\n        }\r\n        Vue.component(CoreNames.SwiperComponent, SwiperComponent);\r\n        Vue.component(CoreNames.SwiperSlideComponent, SwiperSlideComponent);\r\n        Vue.directive(CoreNames.SwiperDirective, getDirective(SwiperClass, globalOptions));\r\n        install.installed = true;\r\n    };\r\n    return install;\r\n};\r\nfunction exporter(SwiperClass) {\r\n    var _a;\r\n    return _a = {\r\n            version: '4.1.1',\r\n            install: getInstaller(SwiperClass),\r\n            directive: getDirective(SwiperClass)\r\n        },\r\n        _a[CoreNames.SwiperComponent] = getSwiperComponent(SwiperClass),\r\n        _a[CoreNames.SwiperSlideComponent] = SwiperSlideComponent,\r\n        _a;\r\n}/**\r\n * @file vue-awesome-swiper\r\n * @module default-export\r\n * <AUTHOR> <https://github.com/surmon-china>\r\n */\r\nvar VueAwesomeSwiper = exporter(SwiperClass);\r\nvar version = VueAwesomeSwiper.version;\r\nvar install = VueAwesomeSwiper.install;\r\nvar directive = VueAwesomeSwiper.directive;\r\nvar Swiper = VueAwesomeSwiper.Swiper;\r\nvar SwiperSlide = VueAwesomeSwiper.SwiperSlide;exports.Swiper=Swiper;exports.SwiperSlide=SwiperSlide;exports.default=VueAwesomeSwiper;exports.directive=directive;exports.install=install;exports.version=version;Object.defineProperty(exports,'__esModule',{value:true});})));", "/**\n * Swiper 5.4.1\n * Most modern mobile touch slider and framework with hardware accelerated transitions\n * http://swiperjs.com\n *\n * Copyright 2014-2020 <PERSON>\n *\n * Released under the MIT License\n *\n * Released on: May 20, 2020\n */\n\nimport { $, addClass, removeClass, hasClass, toggleClass, attr, removeAttr, data, transform, transition as transition$1, on, off, trigger, transitionEnd as transitionEnd$1, outerWidth, outerHeight, offset, css, each, html, text, is, index, eq, append, prepend, next, nextAll, prev, prevAll, parent, parents, closest, find, children, filter, remove, add, styles } from 'dom7/dist/dom7.modular';\nimport { window, document as document$1 } from 'ssr-window';\n\nconst Methods = {\n  addClass,\n  removeClass,\n  hasClass,\n  toggleClass,\n  attr,\n  removeAttr,\n  data,\n  transform,\n  transition: transition$1,\n  on,\n  off,\n  trigger,\n  transitionEnd: transitionEnd$1,\n  outerWidth,\n  outerHeight,\n  offset,\n  css,\n  each,\n  html,\n  text,\n  is,\n  index,\n  eq,\n  append,\n  prepend,\n  next,\n  nextAll,\n  prev,\n  prevAll,\n  parent,\n  parents,\n  closest,\n  find,\n  children,\n  filter,\n  remove,\n  add,\n  styles,\n};\n\nObject.keys(Methods).forEach((methodName) => {\n  $.fn[methodName] = $.fn[methodName] || Methods[methodName];\n});\n\nconst Utils = {\n  deleteProps(obj) {\n    const object = obj;\n    Object.keys(object).forEach((key) => {\n      try {\n        object[key] = null;\n      } catch (e) {\n        // no getter for object\n      }\n      try {\n        delete object[key];\n      } catch (e) {\n        // something got wrong\n      }\n    });\n  },\n  nextTick(callback, delay = 0) {\n    return setTimeout(callback, delay);\n  },\n  now() {\n    return Date.now();\n  },\n  getTranslate(el, axis = 'x') {\n    let matrix;\n    let curTransform;\n    let transformMatrix;\n\n    const curStyle = window.getComputedStyle(el, null);\n\n    if (window.WebKitCSSMatrix) {\n      curTransform = curStyle.transform || curStyle.webkitTransform;\n      if (curTransform.split(',').length > 6) {\n        curTransform = curTransform.split(', ').map((a) => a.replace(',', '.')).join(', ');\n      }\n      // Some old versions of Webkit choke when 'none' is passed; pass\n      // empty string instead in this case\n      transformMatrix = new window.WebKitCSSMatrix(curTransform === 'none' ? '' : curTransform);\n    } else {\n      transformMatrix = curStyle.MozTransform || curStyle.OTransform || curStyle.MsTransform || curStyle.msTransform || curStyle.transform || curStyle.getPropertyValue('transform').replace('translate(', 'matrix(1, 0, 0, 1,');\n      matrix = transformMatrix.toString().split(',');\n    }\n\n    if (axis === 'x') {\n      // Latest Chrome and webkits Fix\n      if (window.WebKitCSSMatrix) curTransform = transformMatrix.m41;\n      // Crazy IE10 Matrix\n      else if (matrix.length === 16) curTransform = parseFloat(matrix[12]);\n      // Normal Browsers\n      else curTransform = parseFloat(matrix[4]);\n    }\n    if (axis === 'y') {\n      // Latest Chrome and webkits Fix\n      if (window.WebKitCSSMatrix) curTransform = transformMatrix.m42;\n      // Crazy IE10 Matrix\n      else if (matrix.length === 16) curTransform = parseFloat(matrix[13]);\n      // Normal Browsers\n      else curTransform = parseFloat(matrix[5]);\n    }\n    return curTransform || 0;\n  },\n  parseUrlQuery(url) {\n    const query = {};\n    let urlToParse = url || window.location.href;\n    let i;\n    let params;\n    let param;\n    let length;\n    if (typeof urlToParse === 'string' && urlToParse.length) {\n      urlToParse = urlToParse.indexOf('?') > -1 ? urlToParse.replace(/\\S*\\?/, '') : '';\n      params = urlToParse.split('&').filter((paramsPart) => paramsPart !== '');\n      length = params.length;\n\n      for (i = 0; i < length; i += 1) {\n        param = params[i].replace(/#\\S+/g, '').split('=');\n        query[decodeURIComponent(param[0])] = typeof param[1] === 'undefined' ? undefined : decodeURIComponent(param[1]) || '';\n      }\n    }\n    return query;\n  },\n  isObject(o) {\n    return typeof o === 'object' && o !== null && o.constructor && o.constructor === Object;\n  },\n  extend(...args) {\n    const to = Object(args[0]);\n    for (let i = 1; i < args.length; i += 1) {\n      const nextSource = args[i];\n      if (nextSource !== undefined && nextSource !== null) {\n        const keysArray = Object.keys(Object(nextSource));\n        for (let nextIndex = 0, len = keysArray.length; nextIndex < len; nextIndex += 1) {\n          const nextKey = keysArray[nextIndex];\n          const desc = Object.getOwnPropertyDescriptor(nextSource, nextKey);\n          if (desc !== undefined && desc.enumerable) {\n            if (Utils.isObject(to[nextKey]) && Utils.isObject(nextSource[nextKey])) {\n              Utils.extend(to[nextKey], nextSource[nextKey]);\n            } else if (!Utils.isObject(to[nextKey]) && Utils.isObject(nextSource[nextKey])) {\n              to[nextKey] = {};\n              Utils.extend(to[nextKey], nextSource[nextKey]);\n            } else {\n              to[nextKey] = nextSource[nextKey];\n            }\n          }\n        }\n      }\n    }\n    return to;\n  },\n};\n\nconst Support = (function Support() {\n  return {\n    touch: !!(('ontouchstart' in window) || (window.DocumentTouch && document$1 instanceof window.DocumentTouch)),\n\n    pointerEvents: !!window.PointerEvent && ('maxTouchPoints' in window.navigator) && window.navigator.maxTouchPoints >= 0,\n\n    observer: (function checkObserver() {\n      return ('MutationObserver' in window || 'WebkitMutationObserver' in window);\n    }()),\n\n    passiveListener: (function checkPassiveListener() {\n      let supportsPassive = false;\n      try {\n        const opts = Object.defineProperty({}, 'passive', {\n          // eslint-disable-next-line\n          get() {\n            supportsPassive = true;\n          },\n        });\n        window.addEventListener('testPassiveListener', null, opts);\n      } catch (e) {\n        // No support\n      }\n      return supportsPassive;\n    }()),\n\n    gestures: (function checkGestures() {\n      return 'ongesturestart' in window;\n    }()),\n  };\n}());\n\nclass SwiperClass {\n  constructor(params = {}) {\n    const self = this;\n    self.params = params;\n\n    // Events\n    self.eventsListeners = {};\n\n    if (self.params && self.params.on) {\n      Object.keys(self.params.on).forEach((eventName) => {\n        self.on(eventName, self.params.on[eventName]);\n      });\n    }\n  }\n\n  on(events, handler, priority) {\n    const self = this;\n    if (typeof handler !== 'function') return self;\n    const method = priority ? 'unshift' : 'push';\n    events.split(' ').forEach((event) => {\n      if (!self.eventsListeners[event]) self.eventsListeners[event] = [];\n      self.eventsListeners[event][method](handler);\n    });\n    return self;\n  }\n\n  once(events, handler, priority) {\n    const self = this;\n    if (typeof handler !== 'function') return self;\n    function onceHandler(...args) {\n      self.off(events, onceHandler);\n      if (onceHandler.f7proxy) {\n        delete onceHandler.f7proxy;\n      }\n      handler.apply(self, args);\n    }\n    onceHandler.f7proxy = handler;\n    return self.on(events, onceHandler, priority);\n  }\n\n  off(events, handler) {\n    const self = this;\n    if (!self.eventsListeners) return self;\n    events.split(' ').forEach((event) => {\n      if (typeof handler === 'undefined') {\n        self.eventsListeners[event] = [];\n      } else if (self.eventsListeners[event] && self.eventsListeners[event].length) {\n        self.eventsListeners[event].forEach((eventHandler, index) => {\n          if (eventHandler === handler || (eventHandler.f7proxy && eventHandler.f7proxy === handler)) {\n            self.eventsListeners[event].splice(index, 1);\n          }\n        });\n      }\n    });\n    return self;\n  }\n\n  emit(...args) {\n    const self = this;\n    if (!self.eventsListeners) return self;\n    let events;\n    let data;\n    let context;\n    if (typeof args[0] === 'string' || Array.isArray(args[0])) {\n      events = args[0];\n      data = args.slice(1, args.length);\n      context = self;\n    } else {\n      events = args[0].events;\n      data = args[0].data;\n      context = args[0].context || self;\n    }\n    const eventsArray = Array.isArray(events) ? events : events.split(' ');\n    eventsArray.forEach((event) => {\n      if (self.eventsListeners && self.eventsListeners[event]) {\n        const handlers = [];\n        self.eventsListeners[event].forEach((eventHandler) => {\n          handlers.push(eventHandler);\n        });\n        handlers.forEach((eventHandler) => {\n          eventHandler.apply(context, data);\n        });\n      }\n    });\n    return self;\n  }\n\n  useModulesParams(instanceParams) {\n    const instance = this;\n    if (!instance.modules) return;\n    Object.keys(instance.modules).forEach((moduleName) => {\n      const module = instance.modules[moduleName];\n      // Extend params\n      if (module.params) {\n        Utils.extend(instanceParams, module.params);\n      }\n    });\n  }\n\n  useModules(modulesParams = {}) {\n    const instance = this;\n    if (!instance.modules) return;\n    Object.keys(instance.modules).forEach((moduleName) => {\n      const module = instance.modules[moduleName];\n      const moduleParams = modulesParams[moduleName] || {};\n      // Extend instance methods and props\n      if (module.instance) {\n        Object.keys(module.instance).forEach((modulePropName) => {\n          const moduleProp = module.instance[modulePropName];\n          if (typeof moduleProp === 'function') {\n            instance[modulePropName] = moduleProp.bind(instance);\n          } else {\n            instance[modulePropName] = moduleProp;\n          }\n        });\n      }\n      // Add event listeners\n      if (module.on && instance.on) {\n        Object.keys(module.on).forEach((moduleEventName) => {\n          instance.on(moduleEventName, module.on[moduleEventName]);\n        });\n      }\n\n      // Module create callback\n      if (module.create) {\n        module.create.bind(instance)(moduleParams);\n      }\n    });\n  }\n\n  static set components(components) {\n    const Class = this;\n    if (!Class.use) return;\n    Class.use(components);\n  }\n\n  static installModule(module, ...params) {\n    const Class = this;\n    if (!Class.prototype.modules) Class.prototype.modules = {};\n    const name = module.name || (`${Object.keys(Class.prototype.modules).length}_${Utils.now()}`);\n    Class.prototype.modules[name] = module;\n    // Prototype\n    if (module.proto) {\n      Object.keys(module.proto).forEach((key) => {\n        Class.prototype[key] = module.proto[key];\n      });\n    }\n    // Class\n    if (module.static) {\n      Object.keys(module.static).forEach((key) => {\n        Class[key] = module.static[key];\n      });\n    }\n    // Callback\n    if (module.install) {\n      module.install.apply(Class, params);\n    }\n    return Class;\n  }\n\n  static use(module, ...params) {\n    const Class = this;\n    if (Array.isArray(module)) {\n      module.forEach((m) => Class.installModule(m));\n      return Class;\n    }\n    return Class.installModule(module, ...params);\n  }\n}\n\nfunction updateSize () {\n  const swiper = this;\n  let width;\n  let height;\n  const $el = swiper.$el;\n  if (typeof swiper.params.width !== 'undefined') {\n    width = swiper.params.width;\n  } else {\n    width = $el[0].clientWidth;\n  }\n  if (typeof swiper.params.height !== 'undefined') {\n    height = swiper.params.height;\n  } else {\n    height = $el[0].clientHeight;\n  }\n  if ((width === 0 && swiper.isHorizontal()) || (height === 0 && swiper.isVertical())) {\n    return;\n  }\n\n  // Subtract paddings\n  width = width - parseInt($el.css('padding-left'), 10) - parseInt($el.css('padding-right'), 10);\n  height = height - parseInt($el.css('padding-top'), 10) - parseInt($el.css('padding-bottom'), 10);\n\n  Utils.extend(swiper, {\n    width,\n    height,\n    size: swiper.isHorizontal() ? width : height,\n  });\n}\n\nfunction updateSlides () {\n  const swiper = this;\n  const params = swiper.params;\n\n  const {\n    $wrapperEl, size: swiperSize, rtlTranslate: rtl, wrongRTL,\n  } = swiper;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  const previousSlidesLength = isVirtual ? swiper.virtual.slides.length : swiper.slides.length;\n  const slides = $wrapperEl.children(`.${swiper.params.slideClass}`);\n  const slidesLength = isVirtual ? swiper.virtual.slides.length : slides.length;\n  let snapGrid = [];\n  const slidesGrid = [];\n  const slidesSizesGrid = [];\n\n  function slidesForMargin(slideIndex) {\n    if (!params.cssMode) return true;\n    if (slideIndex === slides.length - 1) {\n      return false;\n    }\n    return true;\n  }\n\n  let offsetBefore = params.slidesOffsetBefore;\n  if (typeof offsetBefore === 'function') {\n    offsetBefore = params.slidesOffsetBefore.call(swiper);\n  }\n\n  let offsetAfter = params.slidesOffsetAfter;\n  if (typeof offsetAfter === 'function') {\n    offsetAfter = params.slidesOffsetAfter.call(swiper);\n  }\n\n  const previousSnapGridLength = swiper.snapGrid.length;\n  const previousSlidesGridLength = swiper.snapGrid.length;\n\n  let spaceBetween = params.spaceBetween;\n  let slidePosition = -offsetBefore;\n  let prevSlideSize = 0;\n  let index = 0;\n  if (typeof swiperSize === 'undefined') {\n    return;\n  }\n  if (typeof spaceBetween === 'string' && spaceBetween.indexOf('%') >= 0) {\n    spaceBetween = (parseFloat(spaceBetween.replace('%', '')) / 100) * swiperSize;\n  }\n\n  swiper.virtualSize = -spaceBetween;\n\n  // reset margins\n  if (rtl) slides.css({ marginLeft: '', marginTop: '' });\n  else slides.css({ marginRight: '', marginBottom: '' });\n\n  let slidesNumberEvenToRows;\n  if (params.slidesPerColumn > 1) {\n    if (Math.floor(slidesLength / params.slidesPerColumn) === slidesLength / swiper.params.slidesPerColumn) {\n      slidesNumberEvenToRows = slidesLength;\n    } else {\n      slidesNumberEvenToRows = Math.ceil(slidesLength / params.slidesPerColumn) * params.slidesPerColumn;\n    }\n    if (params.slidesPerView !== 'auto' && params.slidesPerColumnFill === 'row') {\n      slidesNumberEvenToRows = Math.max(slidesNumberEvenToRows, params.slidesPerView * params.slidesPerColumn);\n    }\n  }\n\n  // Calc slides\n  let slideSize;\n  const slidesPerColumn = params.slidesPerColumn;\n  const slidesPerRow = slidesNumberEvenToRows / slidesPerColumn;\n  const numFullColumns = Math.floor(slidesLength / params.slidesPerColumn);\n  for (let i = 0; i < slidesLength; i += 1) {\n    slideSize = 0;\n    const slide = slides.eq(i);\n    if (params.slidesPerColumn > 1) {\n      // Set slides order\n      let newSlideOrderIndex;\n      let column;\n      let row;\n      if (params.slidesPerColumnFill === 'row' && params.slidesPerGroup > 1) {\n        const groupIndex = Math.floor(i / (params.slidesPerGroup * params.slidesPerColumn));\n        const slideIndexInGroup = i - params.slidesPerColumn * params.slidesPerGroup * groupIndex;\n        const columnsInGroup = groupIndex === 0\n          ? params.slidesPerGroup\n          : Math.min(Math.ceil((slidesLength - groupIndex * slidesPerColumn * params.slidesPerGroup) / slidesPerColumn), params.slidesPerGroup);\n        row = Math.floor(slideIndexInGroup / columnsInGroup);\n        column = (slideIndexInGroup - row * columnsInGroup) + groupIndex * params.slidesPerGroup;\n\n        newSlideOrderIndex = column + ((row * slidesNumberEvenToRows) / slidesPerColumn);\n        slide\n          .css({\n            '-webkit-box-ordinal-group': newSlideOrderIndex,\n            '-moz-box-ordinal-group': newSlideOrderIndex,\n            '-ms-flex-order': newSlideOrderIndex,\n            '-webkit-order': newSlideOrderIndex,\n            order: newSlideOrderIndex,\n          });\n      } else if (params.slidesPerColumnFill === 'column') {\n        column = Math.floor(i / slidesPerColumn);\n        row = i - (column * slidesPerColumn);\n        if (column > numFullColumns || (column === numFullColumns && row === slidesPerColumn - 1)) {\n          row += 1;\n          if (row >= slidesPerColumn) {\n            row = 0;\n            column += 1;\n          }\n        }\n      } else {\n        row = Math.floor(i / slidesPerRow);\n        column = i - (row * slidesPerRow);\n      }\n      slide.css(\n        `margin-${swiper.isHorizontal() ? 'top' : 'left'}`,\n        (row !== 0 && params.spaceBetween) && (`${params.spaceBetween}px`)\n      );\n    }\n    if (slide.css('display') === 'none') continue; // eslint-disable-line\n\n    if (params.slidesPerView === 'auto') {\n      const slideStyles = window.getComputedStyle(slide[0], null);\n      const currentTransform = slide[0].style.transform;\n      const currentWebKitTransform = slide[0].style.webkitTransform;\n      if (currentTransform) {\n        slide[0].style.transform = 'none';\n      }\n      if (currentWebKitTransform) {\n        slide[0].style.webkitTransform = 'none';\n      }\n      if (params.roundLengths) {\n        slideSize = swiper.isHorizontal()\n          ? slide.outerWidth(true)\n          : slide.outerHeight(true);\n      } else {\n        // eslint-disable-next-line\n        if (swiper.isHorizontal()) {\n          const width = parseFloat(slideStyles.getPropertyValue('width'));\n          const paddingLeft = parseFloat(slideStyles.getPropertyValue('padding-left'));\n          const paddingRight = parseFloat(slideStyles.getPropertyValue('padding-right'));\n          const marginLeft = parseFloat(slideStyles.getPropertyValue('margin-left'));\n          const marginRight = parseFloat(slideStyles.getPropertyValue('margin-right'));\n          const boxSizing = slideStyles.getPropertyValue('box-sizing');\n          if (boxSizing && boxSizing === 'border-box') {\n            slideSize = width + marginLeft + marginRight;\n          } else {\n            slideSize = width + paddingLeft + paddingRight + marginLeft + marginRight;\n          }\n        } else {\n          const height = parseFloat(slideStyles.getPropertyValue('height'));\n          const paddingTop = parseFloat(slideStyles.getPropertyValue('padding-top'));\n          const paddingBottom = parseFloat(slideStyles.getPropertyValue('padding-bottom'));\n          const marginTop = parseFloat(slideStyles.getPropertyValue('margin-top'));\n          const marginBottom = parseFloat(slideStyles.getPropertyValue('margin-bottom'));\n          const boxSizing = slideStyles.getPropertyValue('box-sizing');\n          if (boxSizing && boxSizing === 'border-box') {\n            slideSize = height + marginTop + marginBottom;\n          } else {\n            slideSize = height + paddingTop + paddingBottom + marginTop + marginBottom;\n          }\n        }\n      }\n      if (currentTransform) {\n        slide[0].style.transform = currentTransform;\n      }\n      if (currentWebKitTransform) {\n        slide[0].style.webkitTransform = currentWebKitTransform;\n      }\n      if (params.roundLengths) slideSize = Math.floor(slideSize);\n    } else {\n      slideSize = (swiperSize - ((params.slidesPerView - 1) * spaceBetween)) / params.slidesPerView;\n      if (params.roundLengths) slideSize = Math.floor(slideSize);\n\n      if (slides[i]) {\n        if (swiper.isHorizontal()) {\n          slides[i].style.width = `${slideSize}px`;\n        } else {\n          slides[i].style.height = `${slideSize}px`;\n        }\n      }\n    }\n    if (slides[i]) {\n      slides[i].swiperSlideSize = slideSize;\n    }\n    slidesSizesGrid.push(slideSize);\n\n\n    if (params.centeredSlides) {\n      slidePosition = slidePosition + (slideSize / 2) + (prevSlideSize / 2) + spaceBetween;\n      if (prevSlideSize === 0 && i !== 0) slidePosition = slidePosition - (swiperSize / 2) - spaceBetween;\n      if (i === 0) slidePosition = slidePosition - (swiperSize / 2) - spaceBetween;\n      if (Math.abs(slidePosition) < 1 / 1000) slidePosition = 0;\n      if (params.roundLengths) slidePosition = Math.floor(slidePosition);\n      if ((index) % params.slidesPerGroup === 0) snapGrid.push(slidePosition);\n      slidesGrid.push(slidePosition);\n    } else {\n      if (params.roundLengths) slidePosition = Math.floor(slidePosition);\n      if ((index - Math.min(swiper.params.slidesPerGroupSkip, index)) % swiper.params.slidesPerGroup === 0) snapGrid.push(slidePosition);\n      slidesGrid.push(slidePosition);\n      slidePosition = slidePosition + slideSize + spaceBetween;\n    }\n\n    swiper.virtualSize += slideSize + spaceBetween;\n\n    prevSlideSize = slideSize;\n\n    index += 1;\n  }\n  swiper.virtualSize = Math.max(swiper.virtualSize, swiperSize) + offsetAfter;\n  let newSlidesGrid;\n\n  if (\n    rtl && wrongRTL && (params.effect === 'slide' || params.effect === 'coverflow')) {\n    $wrapperEl.css({ width: `${swiper.virtualSize + params.spaceBetween}px` });\n  }\n  if (params.setWrapperSize) {\n    if (swiper.isHorizontal()) $wrapperEl.css({ width: `${swiper.virtualSize + params.spaceBetween}px` });\n    else $wrapperEl.css({ height: `${swiper.virtualSize + params.spaceBetween}px` });\n  }\n\n  if (params.slidesPerColumn > 1) {\n    swiper.virtualSize = (slideSize + params.spaceBetween) * slidesNumberEvenToRows;\n    swiper.virtualSize = Math.ceil(swiper.virtualSize / params.slidesPerColumn) - params.spaceBetween;\n    if (swiper.isHorizontal()) $wrapperEl.css({ width: `${swiper.virtualSize + params.spaceBetween}px` });\n    else $wrapperEl.css({ height: `${swiper.virtualSize + params.spaceBetween}px` });\n    if (params.centeredSlides) {\n      newSlidesGrid = [];\n      for (let i = 0; i < snapGrid.length; i += 1) {\n        let slidesGridItem = snapGrid[i];\n        if (params.roundLengths) slidesGridItem = Math.floor(slidesGridItem);\n        if (snapGrid[i] < swiper.virtualSize + snapGrid[0]) newSlidesGrid.push(slidesGridItem);\n      }\n      snapGrid = newSlidesGrid;\n    }\n  }\n\n  // Remove last grid elements depending on width\n  if (!params.centeredSlides) {\n    newSlidesGrid = [];\n    for (let i = 0; i < snapGrid.length; i += 1) {\n      let slidesGridItem = snapGrid[i];\n      if (params.roundLengths) slidesGridItem = Math.floor(slidesGridItem);\n      if (snapGrid[i] <= swiper.virtualSize - swiperSize) {\n        newSlidesGrid.push(slidesGridItem);\n      }\n    }\n    snapGrid = newSlidesGrid;\n    if (Math.floor(swiper.virtualSize - swiperSize) - Math.floor(snapGrid[snapGrid.length - 1]) > 1) {\n      snapGrid.push(swiper.virtualSize - swiperSize);\n    }\n  }\n  if (snapGrid.length === 0) snapGrid = [0];\n\n  if (params.spaceBetween !== 0) {\n    if (swiper.isHorizontal()) {\n      if (rtl) slides.filter(slidesForMargin).css({ marginLeft: `${spaceBetween}px` });\n      else slides.filter(slidesForMargin).css({ marginRight: `${spaceBetween}px` });\n    } else slides.filter(slidesForMargin).css({ marginBottom: `${spaceBetween}px` });\n  }\n\n  if (params.centeredSlides && params.centeredSlidesBounds) {\n    let allSlidesSize = 0;\n    slidesSizesGrid.forEach((slideSizeValue) => {\n      allSlidesSize += slideSizeValue + (params.spaceBetween ? params.spaceBetween : 0);\n    });\n    allSlidesSize -= params.spaceBetween;\n    const maxSnap = allSlidesSize - swiperSize;\n    snapGrid = snapGrid.map((snap) => {\n      if (snap < 0) return -offsetBefore;\n      if (snap > maxSnap) return maxSnap + offsetAfter;\n      return snap;\n    });\n  }\n\n  if (params.centerInsufficientSlides) {\n    let allSlidesSize = 0;\n    slidesSizesGrid.forEach((slideSizeValue) => {\n      allSlidesSize += slideSizeValue + (params.spaceBetween ? params.spaceBetween : 0);\n    });\n    allSlidesSize -= params.spaceBetween;\n    if (allSlidesSize < swiperSize) {\n      const allSlidesOffset = (swiperSize - allSlidesSize) / 2;\n      snapGrid.forEach((snap, snapIndex) => {\n        snapGrid[snapIndex] = snap - allSlidesOffset;\n      });\n      slidesGrid.forEach((snap, snapIndex) => {\n        slidesGrid[snapIndex] = snap + allSlidesOffset;\n      });\n    }\n  }\n\n  Utils.extend(swiper, {\n    slides,\n    snapGrid,\n    slidesGrid,\n    slidesSizesGrid,\n  });\n\n  if (slidesLength !== previousSlidesLength) {\n    swiper.emit('slidesLengthChange');\n  }\n  if (snapGrid.length !== previousSnapGridLength) {\n    if (swiper.params.watchOverflow) swiper.checkOverflow();\n    swiper.emit('snapGridLengthChange');\n  }\n  if (slidesGrid.length !== previousSlidesGridLength) {\n    swiper.emit('slidesGridLengthChange');\n  }\n\n  if (params.watchSlidesProgress || params.watchSlidesVisibility) {\n    swiper.updateSlidesOffset();\n  }\n}\n\nfunction updateAutoHeight (speed) {\n  const swiper = this;\n  const activeSlides = [];\n  let newHeight = 0;\n  let i;\n  if (typeof speed === 'number') {\n    swiper.setTransition(speed);\n  } else if (speed === true) {\n    swiper.setTransition(swiper.params.speed);\n  }\n  // Find slides currently in view\n  if (swiper.params.slidesPerView !== 'auto' && swiper.params.slidesPerView > 1) {\n    if (swiper.params.centeredSlides) {\n      swiper.visibleSlides.each((index, slide) => {\n        activeSlides.push(slide);\n      });\n    } else {\n      for (i = 0; i < Math.ceil(swiper.params.slidesPerView); i += 1) {\n        const index = swiper.activeIndex + i;\n        if (index > swiper.slides.length) break;\n        activeSlides.push(swiper.slides.eq(index)[0]);\n      }\n    }\n  } else {\n    activeSlides.push(swiper.slides.eq(swiper.activeIndex)[0]);\n  }\n\n  // Find new height from highest slide in view\n  for (i = 0; i < activeSlides.length; i += 1) {\n    if (typeof activeSlides[i] !== 'undefined') {\n      const height = activeSlides[i].offsetHeight;\n      newHeight = height > newHeight ? height : newHeight;\n    }\n  }\n\n  // Update Height\n  if (newHeight) swiper.$wrapperEl.css('height', `${newHeight}px`);\n}\n\nfunction updateSlidesOffset () {\n  const swiper = this;\n  const slides = swiper.slides;\n  for (let i = 0; i < slides.length; i += 1) {\n    slides[i].swiperSlideOffset = swiper.isHorizontal() ? slides[i].offsetLeft : slides[i].offsetTop;\n  }\n}\n\nfunction updateSlidesProgress (translate = (this && this.translate) || 0) {\n  const swiper = this;\n  const params = swiper.params;\n\n  const { slides, rtlTranslate: rtl } = swiper;\n\n  if (slides.length === 0) return;\n  if (typeof slides[0].swiperSlideOffset === 'undefined') swiper.updateSlidesOffset();\n\n  let offsetCenter = -translate;\n  if (rtl) offsetCenter = translate;\n\n  // Visible Slides\n  slides.removeClass(params.slideVisibleClass);\n\n  swiper.visibleSlidesIndexes = [];\n  swiper.visibleSlides = [];\n\n  for (let i = 0; i < slides.length; i += 1) {\n    const slide = slides[i];\n    const slideProgress = (\n      (offsetCenter + (params.centeredSlides ? swiper.minTranslate() : 0)) - slide.swiperSlideOffset\n    ) / (slide.swiperSlideSize + params.spaceBetween);\n    if (params.watchSlidesVisibility || (params.centeredSlides && params.autoHeight)) {\n      const slideBefore = -(offsetCenter - slide.swiperSlideOffset);\n      const slideAfter = slideBefore + swiper.slidesSizesGrid[i];\n      const isVisible = (slideBefore >= 0 && slideBefore < swiper.size - 1)\n                || (slideAfter > 1 && slideAfter <= swiper.size)\n                || (slideBefore <= 0 && slideAfter >= swiper.size);\n      if (isVisible) {\n        swiper.visibleSlides.push(slide);\n        swiper.visibleSlidesIndexes.push(i);\n        slides.eq(i).addClass(params.slideVisibleClass);\n      }\n    }\n    slide.progress = rtl ? -slideProgress : slideProgress;\n  }\n  swiper.visibleSlides = $(swiper.visibleSlides);\n}\n\nfunction updateProgress (translate) {\n  const swiper = this;\n  if (typeof translate === 'undefined') {\n    const multiplier = swiper.rtlTranslate ? -1 : 1;\n    // eslint-disable-next-line\n    translate = (swiper && swiper.translate && (swiper.translate * multiplier)) || 0;\n  }\n  const params = swiper.params;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n  let { progress, isBeginning, isEnd } = swiper;\n  const wasBeginning = isBeginning;\n  const wasEnd = isEnd;\n  if (translatesDiff === 0) {\n    progress = 0;\n    isBeginning = true;\n    isEnd = true;\n  } else {\n    progress = (translate - swiper.minTranslate()) / (translatesDiff);\n    isBeginning = progress <= 0;\n    isEnd = progress >= 1;\n  }\n  Utils.extend(swiper, {\n    progress,\n    isBeginning,\n    isEnd,\n  });\n\n  if (params.watchSlidesProgress || params.watchSlidesVisibility || (params.centeredSlides && params.autoHeight)) swiper.updateSlidesProgress(translate);\n\n  if (isBeginning && !wasBeginning) {\n    swiper.emit('reachBeginning toEdge');\n  }\n  if (isEnd && !wasEnd) {\n    swiper.emit('reachEnd toEdge');\n  }\n  if ((wasBeginning && !isBeginning) || (wasEnd && !isEnd)) {\n    swiper.emit('fromEdge');\n  }\n\n  swiper.emit('progress', progress);\n}\n\nfunction updateSlidesClasses () {\n  const swiper = this;\n\n  const {\n    slides, params, $wrapperEl, activeIndex, realIndex,\n  } = swiper;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n\n  slides.removeClass(`${params.slideActiveClass} ${params.slideNextClass} ${params.slidePrevClass} ${params.slideDuplicateActiveClass} ${params.slideDuplicateNextClass} ${params.slideDuplicatePrevClass}`);\n\n  let activeSlide;\n  if (isVirtual) {\n    activeSlide = swiper.$wrapperEl.find(`.${params.slideClass}[data-swiper-slide-index=\"${activeIndex}\"]`);\n  } else {\n    activeSlide = slides.eq(activeIndex);\n  }\n\n  // Active classes\n  activeSlide.addClass(params.slideActiveClass);\n\n  if (params.loop) {\n    // Duplicate to all looped slides\n    if (activeSlide.hasClass(params.slideDuplicateClass)) {\n      $wrapperEl\n        .children(`.${params.slideClass}:not(.${params.slideDuplicateClass})[data-swiper-slide-index=\"${realIndex}\"]`)\n        .addClass(params.slideDuplicateActiveClass);\n    } else {\n      $wrapperEl\n        .children(`.${params.slideClass}.${params.slideDuplicateClass}[data-swiper-slide-index=\"${realIndex}\"]`)\n        .addClass(params.slideDuplicateActiveClass);\n    }\n  }\n  // Next Slide\n  let nextSlide = activeSlide.nextAll(`.${params.slideClass}`).eq(0).addClass(params.slideNextClass);\n  if (params.loop && nextSlide.length === 0) {\n    nextSlide = slides.eq(0);\n    nextSlide.addClass(params.slideNextClass);\n  }\n  // Prev Slide\n  let prevSlide = activeSlide.prevAll(`.${params.slideClass}`).eq(0).addClass(params.slidePrevClass);\n  if (params.loop && prevSlide.length === 0) {\n    prevSlide = slides.eq(-1);\n    prevSlide.addClass(params.slidePrevClass);\n  }\n  if (params.loop) {\n    // Duplicate to all looped slides\n    if (nextSlide.hasClass(params.slideDuplicateClass)) {\n      $wrapperEl\n        .children(`.${params.slideClass}:not(.${params.slideDuplicateClass})[data-swiper-slide-index=\"${nextSlide.attr('data-swiper-slide-index')}\"]`)\n        .addClass(params.slideDuplicateNextClass);\n    } else {\n      $wrapperEl\n        .children(`.${params.slideClass}.${params.slideDuplicateClass}[data-swiper-slide-index=\"${nextSlide.attr('data-swiper-slide-index')}\"]`)\n        .addClass(params.slideDuplicateNextClass);\n    }\n    if (prevSlide.hasClass(params.slideDuplicateClass)) {\n      $wrapperEl\n        .children(`.${params.slideClass}:not(.${params.slideDuplicateClass})[data-swiper-slide-index=\"${prevSlide.attr('data-swiper-slide-index')}\"]`)\n        .addClass(params.slideDuplicatePrevClass);\n    } else {\n      $wrapperEl\n        .children(`.${params.slideClass}.${params.slideDuplicateClass}[data-swiper-slide-index=\"${prevSlide.attr('data-swiper-slide-index')}\"]`)\n        .addClass(params.slideDuplicatePrevClass);\n    }\n  }\n}\n\nfunction updateActiveIndex (newActiveIndex) {\n  const swiper = this;\n  const translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n  const {\n    slidesGrid, snapGrid, params, activeIndex: previousIndex, realIndex: previousRealIndex, snapIndex: previousSnapIndex,\n  } = swiper;\n  let activeIndex = newActiveIndex;\n  let snapIndex;\n  if (typeof activeIndex === 'undefined') {\n    for (let i = 0; i < slidesGrid.length; i += 1) {\n      if (typeof slidesGrid[i + 1] !== 'undefined') {\n        if (translate >= slidesGrid[i] && translate < slidesGrid[i + 1] - ((slidesGrid[i + 1] - slidesGrid[i]) / 2)) {\n          activeIndex = i;\n        } else if (translate >= slidesGrid[i] && translate < slidesGrid[i + 1]) {\n          activeIndex = i + 1;\n        }\n      } else if (translate >= slidesGrid[i]) {\n        activeIndex = i;\n      }\n    }\n    // Normalize slideIndex\n    if (params.normalizeSlideIndex) {\n      if (activeIndex < 0 || typeof activeIndex === 'undefined') activeIndex = 0;\n    }\n  }\n  if (snapGrid.indexOf(translate) >= 0) {\n    snapIndex = snapGrid.indexOf(translate);\n  } else {\n    const skip = Math.min(params.slidesPerGroupSkip, activeIndex);\n    snapIndex = skip + Math.floor((activeIndex - skip) / params.slidesPerGroup);\n  }\n  if (snapIndex >= snapGrid.length) snapIndex = snapGrid.length - 1;\n  if (activeIndex === previousIndex) {\n    if (snapIndex !== previousSnapIndex) {\n      swiper.snapIndex = snapIndex;\n      swiper.emit('snapIndexChange');\n    }\n    return;\n  }\n\n  // Get real index\n  const realIndex = parseInt(swiper.slides.eq(activeIndex).attr('data-swiper-slide-index') || activeIndex, 10);\n\n  Utils.extend(swiper, {\n    snapIndex,\n    realIndex,\n    previousIndex,\n    activeIndex,\n  });\n  swiper.emit('activeIndexChange');\n  swiper.emit('snapIndexChange');\n  if (previousRealIndex !== realIndex) {\n    swiper.emit('realIndexChange');\n  }\n  if (swiper.initialized || swiper.params.runCallbacksOnInit) {\n    swiper.emit('slideChange');\n  }\n}\n\nfunction updateClickedSlide (e) {\n  const swiper = this;\n  const params = swiper.params;\n  const slide = $(e.target).closest(`.${params.slideClass}`)[0];\n  let slideFound = false;\n  if (slide) {\n    for (let i = 0; i < swiper.slides.length; i += 1) {\n      if (swiper.slides[i] === slide) slideFound = true;\n    }\n  }\n\n  if (slide && slideFound) {\n    swiper.clickedSlide = slide;\n    if (swiper.virtual && swiper.params.virtual.enabled) {\n      swiper.clickedIndex = parseInt($(slide).attr('data-swiper-slide-index'), 10);\n    } else {\n      swiper.clickedIndex = $(slide).index();\n    }\n  } else {\n    swiper.clickedSlide = undefined;\n    swiper.clickedIndex = undefined;\n    return;\n  }\n  if (params.slideToClickedSlide && swiper.clickedIndex !== undefined && swiper.clickedIndex !== swiper.activeIndex) {\n    swiper.slideToClickedSlide();\n  }\n}\n\nvar update = {\n  updateSize,\n  updateSlides,\n  updateAutoHeight,\n  updateSlidesOffset,\n  updateSlidesProgress,\n  updateProgress,\n  updateSlidesClasses,\n  updateActiveIndex,\n  updateClickedSlide,\n};\n\nfunction getTranslate (axis = this.isHorizontal() ? 'x' : 'y') {\n  const swiper = this;\n\n  const {\n    params, rtlTranslate: rtl, translate, $wrapperEl,\n  } = swiper;\n\n  if (params.virtualTranslate) {\n    return rtl ? -translate : translate;\n  }\n  if (params.cssMode) {\n    return translate;\n  }\n\n  let currentTranslate = Utils.getTranslate($wrapperEl[0], axis);\n  if (rtl) currentTranslate = -currentTranslate;\n\n  return currentTranslate || 0;\n}\n\nfunction setTranslate (translate, byController) {\n  const swiper = this;\n  const {\n    rtlTranslate: rtl, params, $wrapperEl, wrapperEl, progress,\n  } = swiper;\n  let x = 0;\n  let y = 0;\n  const z = 0;\n\n  if (swiper.isHorizontal()) {\n    x = rtl ? -translate : translate;\n  } else {\n    y = translate;\n  }\n\n  if (params.roundLengths) {\n    x = Math.floor(x);\n    y = Math.floor(y);\n  }\n\n  if (params.cssMode) {\n    wrapperEl[swiper.isHorizontal() ? 'scrollLeft' : 'scrollTop'] = swiper.isHorizontal() ? -x : -y;\n  } else if (!params.virtualTranslate) {\n    $wrapperEl.transform(`translate3d(${x}px, ${y}px, ${z}px)`);\n  }\n  swiper.previousTranslate = swiper.translate;\n  swiper.translate = swiper.isHorizontal() ? x : y;\n\n  // Check if we need to update progress\n  let newProgress;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n  if (translatesDiff === 0) {\n    newProgress = 0;\n  } else {\n    newProgress = (translate - swiper.minTranslate()) / (translatesDiff);\n  }\n  if (newProgress !== progress) {\n    swiper.updateProgress(translate);\n  }\n\n  swiper.emit('setTranslate', swiper.translate, byController);\n}\n\nfunction minTranslate () {\n  return (-this.snapGrid[0]);\n}\n\nfunction maxTranslate () {\n  return (-this.snapGrid[this.snapGrid.length - 1]);\n}\n\nfunction translateTo (translate = 0, speed = this.params.speed, runCallbacks = true, translateBounds = true, internal) {\n  const swiper = this;\n\n  const {\n    params,\n    wrapperEl,\n  } = swiper;\n\n  if (swiper.animating && params.preventInteractionOnTransition) {\n    return false;\n  }\n\n  const minTranslate = swiper.minTranslate();\n  const maxTranslate = swiper.maxTranslate();\n  let newTranslate;\n  if (translateBounds && translate > minTranslate) newTranslate = minTranslate;\n  else if (translateBounds && translate < maxTranslate) newTranslate = maxTranslate;\n  else newTranslate = translate;\n\n  // Update progress\n  swiper.updateProgress(newTranslate);\n\n  if (params.cssMode) {\n    const isH = swiper.isHorizontal();\n    if (speed === 0) {\n      wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = -newTranslate;\n    } else {\n      // eslint-disable-next-line\n      if (wrapperEl.scrollTo) {\n        wrapperEl.scrollTo({\n          [isH ? 'left' : 'top']: -newTranslate,\n          behavior: 'smooth',\n        });\n      } else {\n        wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = -newTranslate;\n      }\n    }\n    return true;\n  }\n\n  if (speed === 0) {\n    swiper.setTransition(0);\n    swiper.setTranslate(newTranslate);\n    if (runCallbacks) {\n      swiper.emit('beforeTransitionStart', speed, internal);\n      swiper.emit('transitionEnd');\n    }\n  } else {\n    swiper.setTransition(speed);\n    swiper.setTranslate(newTranslate);\n    if (runCallbacks) {\n      swiper.emit('beforeTransitionStart', speed, internal);\n      swiper.emit('transitionStart');\n    }\n    if (!swiper.animating) {\n      swiper.animating = true;\n      if (!swiper.onTranslateToWrapperTransitionEnd) {\n        swiper.onTranslateToWrapperTransitionEnd = function transitionEnd(e) {\n          if (!swiper || swiper.destroyed) return;\n          if (e.target !== this) return;\n          swiper.$wrapperEl[0].removeEventListener('transitionend', swiper.onTranslateToWrapperTransitionEnd);\n          swiper.$wrapperEl[0].removeEventListener('webkitTransitionEnd', swiper.onTranslateToWrapperTransitionEnd);\n          swiper.onTranslateToWrapperTransitionEnd = null;\n          delete swiper.onTranslateToWrapperTransitionEnd;\n          if (runCallbacks) {\n            swiper.emit('transitionEnd');\n          }\n        };\n      }\n      swiper.$wrapperEl[0].addEventListener('transitionend', swiper.onTranslateToWrapperTransitionEnd);\n      swiper.$wrapperEl[0].addEventListener('webkitTransitionEnd', swiper.onTranslateToWrapperTransitionEnd);\n    }\n  }\n\n  return true;\n}\n\nvar translate = {\n  getTranslate,\n  setTranslate,\n  minTranslate,\n  maxTranslate,\n  translateTo,\n};\n\nfunction setTransition (duration, byController) {\n  const swiper = this;\n\n  if (!swiper.params.cssMode) {\n    swiper.$wrapperEl.transition(duration);\n  }\n\n  swiper.emit('setTransition', duration, byController);\n}\n\nfunction transitionStart (runCallbacks = true, direction) {\n  const swiper = this;\n  const { activeIndex, params, previousIndex } = swiper;\n  if (params.cssMode) return;\n  if (params.autoHeight) {\n    swiper.updateAutoHeight();\n  }\n\n  let dir = direction;\n  if (!dir) {\n    if (activeIndex > previousIndex) dir = 'next';\n    else if (activeIndex < previousIndex) dir = 'prev';\n    else dir = 'reset';\n  }\n\n  swiper.emit('transitionStart');\n\n  if (runCallbacks && activeIndex !== previousIndex) {\n    if (dir === 'reset') {\n      swiper.emit('slideResetTransitionStart');\n      return;\n    }\n    swiper.emit('slideChangeTransitionStart');\n    if (dir === 'next') {\n      swiper.emit('slideNextTransitionStart');\n    } else {\n      swiper.emit('slidePrevTransitionStart');\n    }\n  }\n}\n\nfunction transitionEnd (runCallbacks = true, direction) {\n  const swiper = this;\n  const { activeIndex, previousIndex, params } = swiper;\n  swiper.animating = false;\n  if (params.cssMode) return;\n  swiper.setTransition(0);\n\n  let dir = direction;\n  if (!dir) {\n    if (activeIndex > previousIndex) dir = 'next';\n    else if (activeIndex < previousIndex) dir = 'prev';\n    else dir = 'reset';\n  }\n\n  swiper.emit('transitionEnd');\n\n  if (runCallbacks && activeIndex !== previousIndex) {\n    if (dir === 'reset') {\n      swiper.emit('slideResetTransitionEnd');\n      return;\n    }\n    swiper.emit('slideChangeTransitionEnd');\n    if (dir === 'next') {\n      swiper.emit('slideNextTransitionEnd');\n    } else {\n      swiper.emit('slidePrevTransitionEnd');\n    }\n  }\n}\n\nvar transition = {\n  setTransition,\n  transitionStart,\n  transitionEnd,\n};\n\nfunction slideTo (index = 0, speed = this.params.speed, runCallbacks = true, internal) {\n  const swiper = this;\n  let slideIndex = index;\n  if (slideIndex < 0) slideIndex = 0;\n\n  const {\n    params, snapGrid, slidesGrid, previousIndex, activeIndex, rtlTranslate: rtl, wrapperEl,\n  } = swiper;\n  if (swiper.animating && params.preventInteractionOnTransition) {\n    return false;\n  }\n\n  const skip = Math.min(swiper.params.slidesPerGroupSkip, slideIndex);\n  let snapIndex = skip + Math.floor((slideIndex - skip) / swiper.params.slidesPerGroup);\n  if (snapIndex >= snapGrid.length) snapIndex = snapGrid.length - 1;\n\n  if ((activeIndex || params.initialSlide || 0) === (previousIndex || 0) && runCallbacks) {\n    swiper.emit('beforeSlideChangeStart');\n  }\n\n  const translate = -snapGrid[snapIndex];\n\n  // Update progress\n  swiper.updateProgress(translate);\n\n  // Normalize slideIndex\n  if (params.normalizeSlideIndex) {\n    for (let i = 0; i < slidesGrid.length; i += 1) {\n      if (-Math.floor(translate * 100) >= Math.floor(slidesGrid[i] * 100)) {\n        slideIndex = i;\n      }\n    }\n  }\n  // Directions locks\n  if (swiper.initialized && slideIndex !== activeIndex) {\n    if (!swiper.allowSlideNext && translate < swiper.translate && translate < swiper.minTranslate()) {\n      return false;\n    }\n    if (!swiper.allowSlidePrev && translate > swiper.translate && translate > swiper.maxTranslate()) {\n      if ((activeIndex || 0) !== slideIndex) return false;\n    }\n  }\n\n  let direction;\n  if (slideIndex > activeIndex) direction = 'next';\n  else if (slideIndex < activeIndex) direction = 'prev';\n  else direction = 'reset';\n\n\n  // Update Index\n  if ((rtl && -translate === swiper.translate) || (!rtl && translate === swiper.translate)) {\n    swiper.updateActiveIndex(slideIndex);\n    // Update Height\n    if (params.autoHeight) {\n      swiper.updateAutoHeight();\n    }\n    swiper.updateSlidesClasses();\n    if (params.effect !== 'slide') {\n      swiper.setTranslate(translate);\n    }\n    if (direction !== 'reset') {\n      swiper.transitionStart(runCallbacks, direction);\n      swiper.transitionEnd(runCallbacks, direction);\n    }\n    return false;\n  }\n  if (params.cssMode) {\n    const isH = swiper.isHorizontal();\n    let t = -translate;\n    if (rtl) {\n      t = wrapperEl.scrollWidth - wrapperEl.offsetWidth - t;\n    }\n    if (speed === 0) {\n      wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = t;\n    } else {\n      // eslint-disable-next-line\n      if (wrapperEl.scrollTo) {\n        wrapperEl.scrollTo({\n          [isH ? 'left' : 'top']: t,\n          behavior: 'smooth',\n        });\n      } else {\n        wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = t;\n      }\n    }\n    return true;\n  }\n\n  if (speed === 0) {\n    swiper.setTransition(0);\n    swiper.setTranslate(translate);\n    swiper.updateActiveIndex(slideIndex);\n    swiper.updateSlidesClasses();\n    swiper.emit('beforeTransitionStart', speed, internal);\n    swiper.transitionStart(runCallbacks, direction);\n    swiper.transitionEnd(runCallbacks, direction);\n  } else {\n    swiper.setTransition(speed);\n    swiper.setTranslate(translate);\n    swiper.updateActiveIndex(slideIndex);\n    swiper.updateSlidesClasses();\n    swiper.emit('beforeTransitionStart', speed, internal);\n    swiper.transitionStart(runCallbacks, direction);\n    if (!swiper.animating) {\n      swiper.animating = true;\n      if (!swiper.onSlideToWrapperTransitionEnd) {\n        swiper.onSlideToWrapperTransitionEnd = function transitionEnd(e) {\n          if (!swiper || swiper.destroyed) return;\n          if (e.target !== this) return;\n          swiper.$wrapperEl[0].removeEventListener('transitionend', swiper.onSlideToWrapperTransitionEnd);\n          swiper.$wrapperEl[0].removeEventListener('webkitTransitionEnd', swiper.onSlideToWrapperTransitionEnd);\n          swiper.onSlideToWrapperTransitionEnd = null;\n          delete swiper.onSlideToWrapperTransitionEnd;\n          swiper.transitionEnd(runCallbacks, direction);\n        };\n      }\n      swiper.$wrapperEl[0].addEventListener('transitionend', swiper.onSlideToWrapperTransitionEnd);\n      swiper.$wrapperEl[0].addEventListener('webkitTransitionEnd', swiper.onSlideToWrapperTransitionEnd);\n    }\n  }\n\n  return true;\n}\n\nfunction slideToLoop (index = 0, speed = this.params.speed, runCallbacks = true, internal) {\n  const swiper = this;\n  let newIndex = index;\n  if (swiper.params.loop) {\n    newIndex += swiper.loopedSlides;\n  }\n\n  return swiper.slideTo(newIndex, speed, runCallbacks, internal);\n}\n\n/* eslint no-unused-vars: \"off\" */\nfunction slideNext (speed = this.params.speed, runCallbacks = true, internal) {\n  const swiper = this;\n  const { params, animating } = swiper;\n  const increment = swiper.activeIndex < params.slidesPerGroupSkip ? 1 : params.slidesPerGroup;\n  if (params.loop) {\n    if (animating) return false;\n    swiper.loopFix();\n    // eslint-disable-next-line\n    swiper._clientLeft = swiper.$wrapperEl[0].clientLeft;\n  }\n  return swiper.slideTo(swiper.activeIndex + increment, speed, runCallbacks, internal);\n}\n\n/* eslint no-unused-vars: \"off\" */\nfunction slidePrev (speed = this.params.speed, runCallbacks = true, internal) {\n  const swiper = this;\n  const {\n    params, animating, snapGrid, slidesGrid, rtlTranslate,\n  } = swiper;\n\n  if (params.loop) {\n    if (animating) return false;\n    swiper.loopFix();\n    // eslint-disable-next-line\n    swiper._clientLeft = swiper.$wrapperEl[0].clientLeft;\n  }\n  const translate = rtlTranslate ? swiper.translate : -swiper.translate;\n  function normalize(val) {\n    if (val < 0) return -Math.floor(Math.abs(val));\n    return Math.floor(val);\n  }\n  const normalizedTranslate = normalize(translate);\n  const normalizedSnapGrid = snapGrid.map((val) => normalize(val));\n  const normalizedSlidesGrid = slidesGrid.map((val) => normalize(val));\n\n  const currentSnap = snapGrid[normalizedSnapGrid.indexOf(normalizedTranslate)];\n  let prevSnap = snapGrid[normalizedSnapGrid.indexOf(normalizedTranslate) - 1];\n  if (typeof prevSnap === 'undefined' && params.cssMode) {\n    snapGrid.forEach((snap) => {\n      if (!prevSnap && normalizedTranslate >= snap) prevSnap = snap;\n    });\n  }\n  let prevIndex;\n  if (typeof prevSnap !== 'undefined') {\n    prevIndex = slidesGrid.indexOf(prevSnap);\n    if (prevIndex < 0) prevIndex = swiper.activeIndex - 1;\n  }\n  return swiper.slideTo(prevIndex, speed, runCallbacks, internal);\n}\n\n/* eslint no-unused-vars: \"off\" */\nfunction slideReset (speed = this.params.speed, runCallbacks = true, internal) {\n  const swiper = this;\n  return swiper.slideTo(swiper.activeIndex, speed, runCallbacks, internal);\n}\n\n/* eslint no-unused-vars: \"off\" */\nfunction slideToClosest (speed = this.params.speed, runCallbacks = true, internal, threshold = 0.5) {\n  const swiper = this;\n  let index = swiper.activeIndex;\n  const skip = Math.min(swiper.params.slidesPerGroupSkip, index);\n  const snapIndex = skip + Math.floor((index - skip) / swiper.params.slidesPerGroup);\n\n  const translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n\n  if (translate >= swiper.snapGrid[snapIndex]) {\n    // The current translate is on or after the current snap index, so the choice\n    // is between the current index and the one after it.\n    const currentSnap = swiper.snapGrid[snapIndex];\n    const nextSnap = swiper.snapGrid[snapIndex + 1];\n    if ((translate - currentSnap) > (nextSnap - currentSnap) * threshold) {\n      index += swiper.params.slidesPerGroup;\n    }\n  } else {\n    // The current translate is before the current snap index, so the choice\n    // is between the current index and the one before it.\n    const prevSnap = swiper.snapGrid[snapIndex - 1];\n    const currentSnap = swiper.snapGrid[snapIndex];\n    if ((translate - prevSnap) <= (currentSnap - prevSnap) * threshold) {\n      index -= swiper.params.slidesPerGroup;\n    }\n  }\n  index = Math.max(index, 0);\n  index = Math.min(index, swiper.slidesGrid.length - 1);\n\n  return swiper.slideTo(index, speed, runCallbacks, internal);\n}\n\nfunction slideToClickedSlide () {\n  const swiper = this;\n  const { params, $wrapperEl } = swiper;\n\n  const slidesPerView = params.slidesPerView === 'auto' ? swiper.slidesPerViewDynamic() : params.slidesPerView;\n  let slideToIndex = swiper.clickedIndex;\n  let realIndex;\n  if (params.loop) {\n    if (swiper.animating) return;\n    realIndex = parseInt($(swiper.clickedSlide).attr('data-swiper-slide-index'), 10);\n    if (params.centeredSlides) {\n      if (\n        (slideToIndex < swiper.loopedSlides - (slidesPerView / 2))\n        || (slideToIndex > (swiper.slides.length - swiper.loopedSlides) + (slidesPerView / 2))\n      ) {\n        swiper.loopFix();\n        slideToIndex = $wrapperEl\n          .children(`.${params.slideClass}[data-swiper-slide-index=\"${realIndex}\"]:not(.${params.slideDuplicateClass})`)\n          .eq(0)\n          .index();\n\n        Utils.nextTick(() => {\n          swiper.slideTo(slideToIndex);\n        });\n      } else {\n        swiper.slideTo(slideToIndex);\n      }\n    } else if (slideToIndex > swiper.slides.length - slidesPerView) {\n      swiper.loopFix();\n      slideToIndex = $wrapperEl\n        .children(`.${params.slideClass}[data-swiper-slide-index=\"${realIndex}\"]:not(.${params.slideDuplicateClass})`)\n        .eq(0)\n        .index();\n\n      Utils.nextTick(() => {\n        swiper.slideTo(slideToIndex);\n      });\n    } else {\n      swiper.slideTo(slideToIndex);\n    }\n  } else {\n    swiper.slideTo(slideToIndex);\n  }\n}\n\nvar slide = {\n  slideTo,\n  slideToLoop,\n  slideNext,\n  slidePrev,\n  slideReset,\n  slideToClosest,\n  slideToClickedSlide,\n};\n\nfunction loopCreate () {\n  const swiper = this;\n  const { params, $wrapperEl } = swiper;\n  // Remove duplicated slides\n  $wrapperEl.children(`.${params.slideClass}.${params.slideDuplicateClass}`).remove();\n\n  let slides = $wrapperEl.children(`.${params.slideClass}`);\n\n  if (params.loopFillGroupWithBlank) {\n    const blankSlidesNum = params.slidesPerGroup - (slides.length % params.slidesPerGroup);\n    if (blankSlidesNum !== params.slidesPerGroup) {\n      for (let i = 0; i < blankSlidesNum; i += 1) {\n        const blankNode = $(document$1.createElement('div')).addClass(`${params.slideClass} ${params.slideBlankClass}`);\n        $wrapperEl.append(blankNode);\n      }\n      slides = $wrapperEl.children(`.${params.slideClass}`);\n    }\n  }\n\n  if (params.slidesPerView === 'auto' && !params.loopedSlides) params.loopedSlides = slides.length;\n\n  swiper.loopedSlides = Math.ceil(parseFloat(params.loopedSlides || params.slidesPerView, 10));\n  swiper.loopedSlides += params.loopAdditionalSlides;\n  if (swiper.loopedSlides > slides.length) {\n    swiper.loopedSlides = slides.length;\n  }\n\n  const prependSlides = [];\n  const appendSlides = [];\n  slides.each((index, el) => {\n    const slide = $(el);\n    if (index < swiper.loopedSlides) appendSlides.push(el);\n    if (index < slides.length && index >= slides.length - swiper.loopedSlides) prependSlides.push(el);\n    slide.attr('data-swiper-slide-index', index);\n  });\n  for (let i = 0; i < appendSlides.length; i += 1) {\n    $wrapperEl.append($(appendSlides[i].cloneNode(true)).addClass(params.slideDuplicateClass));\n  }\n  for (let i = prependSlides.length - 1; i >= 0; i -= 1) {\n    $wrapperEl.prepend($(prependSlides[i].cloneNode(true)).addClass(params.slideDuplicateClass));\n  }\n}\n\nfunction loopFix () {\n  const swiper = this;\n\n  swiper.emit('beforeLoopFix');\n\n  const {\n    activeIndex, slides, loopedSlides, allowSlidePrev, allowSlideNext, snapGrid, rtlTranslate: rtl,\n  } = swiper;\n  let newIndex;\n  swiper.allowSlidePrev = true;\n  swiper.allowSlideNext = true;\n\n  const snapTranslate = -snapGrid[activeIndex];\n  const diff = snapTranslate - swiper.getTranslate();\n\n  // Fix For Negative Oversliding\n  if (activeIndex < loopedSlides) {\n    newIndex = (slides.length - (loopedSlides * 3)) + activeIndex;\n    newIndex += loopedSlides;\n    const slideChanged = swiper.slideTo(newIndex, 0, false, true);\n    if (slideChanged && diff !== 0) {\n      swiper.setTranslate((rtl ? -swiper.translate : swiper.translate) - diff);\n    }\n  } else if (activeIndex >= slides.length - loopedSlides) {\n    // Fix For Positive Oversliding\n    newIndex = -slides.length + activeIndex + loopedSlides;\n    newIndex += loopedSlides;\n    const slideChanged = swiper.slideTo(newIndex, 0, false, true);\n    if (slideChanged && diff !== 0) {\n      swiper.setTranslate((rtl ? -swiper.translate : swiper.translate) - diff);\n    }\n  }\n  swiper.allowSlidePrev = allowSlidePrev;\n  swiper.allowSlideNext = allowSlideNext;\n\n  swiper.emit('loopFix');\n}\n\nfunction loopDestroy () {\n  const swiper = this;\n  const { $wrapperEl, params, slides } = swiper;\n  $wrapperEl.children(`.${params.slideClass}.${params.slideDuplicateClass},.${params.slideClass}.${params.slideBlankClass}`).remove();\n  slides.removeAttr('data-swiper-slide-index');\n}\n\nvar loop = {\n  loopCreate,\n  loopFix,\n  loopDestroy,\n};\n\nfunction setGrabCursor (moving) {\n  const swiper = this;\n  if (Support.touch || !swiper.params.simulateTouch || (swiper.params.watchOverflow && swiper.isLocked) || swiper.params.cssMode) return;\n  const el = swiper.el;\n  el.style.cursor = 'move';\n  el.style.cursor = moving ? '-webkit-grabbing' : '-webkit-grab';\n  el.style.cursor = moving ? '-moz-grabbin' : '-moz-grab';\n  el.style.cursor = moving ? 'grabbing' : 'grab';\n}\n\nfunction unsetGrabCursor () {\n  const swiper = this;\n  if (Support.touch || (swiper.params.watchOverflow && swiper.isLocked) || swiper.params.cssMode) return;\n  swiper.el.style.cursor = '';\n}\n\nvar grabCursor = {\n  setGrabCursor,\n  unsetGrabCursor,\n};\n\nfunction appendSlide (slides) {\n  const swiper = this;\n  const { $wrapperEl, params } = swiper;\n  if (params.loop) {\n    swiper.loopDestroy();\n  }\n  if (typeof slides === 'object' && 'length' in slides) {\n    for (let i = 0; i < slides.length; i += 1) {\n      if (slides[i]) $wrapperEl.append(slides[i]);\n    }\n  } else {\n    $wrapperEl.append(slides);\n  }\n  if (params.loop) {\n    swiper.loopCreate();\n  }\n  if (!(params.observer && Support.observer)) {\n    swiper.update();\n  }\n}\n\nfunction prependSlide (slides) {\n  const swiper = this;\n  const { params, $wrapperEl, activeIndex } = swiper;\n\n  if (params.loop) {\n    swiper.loopDestroy();\n  }\n  let newActiveIndex = activeIndex + 1;\n  if (typeof slides === 'object' && 'length' in slides) {\n    for (let i = 0; i < slides.length; i += 1) {\n      if (slides[i]) $wrapperEl.prepend(slides[i]);\n    }\n    newActiveIndex = activeIndex + slides.length;\n  } else {\n    $wrapperEl.prepend(slides);\n  }\n  if (params.loop) {\n    swiper.loopCreate();\n  }\n  if (!(params.observer && Support.observer)) {\n    swiper.update();\n  }\n  swiper.slideTo(newActiveIndex, 0, false);\n}\n\nfunction addSlide (index, slides) {\n  const swiper = this;\n  const { $wrapperEl, params, activeIndex } = swiper;\n  let activeIndexBuffer = activeIndex;\n  if (params.loop) {\n    activeIndexBuffer -= swiper.loopedSlides;\n    swiper.loopDestroy();\n    swiper.slides = $wrapperEl.children(`.${params.slideClass}`);\n  }\n  const baseLength = swiper.slides.length;\n  if (index <= 0) {\n    swiper.prependSlide(slides);\n    return;\n  }\n  if (index >= baseLength) {\n    swiper.appendSlide(slides);\n    return;\n  }\n  let newActiveIndex = activeIndexBuffer > index ? activeIndexBuffer + 1 : activeIndexBuffer;\n\n  const slidesBuffer = [];\n  for (let i = baseLength - 1; i >= index; i -= 1) {\n    const currentSlide = swiper.slides.eq(i);\n    currentSlide.remove();\n    slidesBuffer.unshift(currentSlide);\n  }\n\n  if (typeof slides === 'object' && 'length' in slides) {\n    for (let i = 0; i < slides.length; i += 1) {\n      if (slides[i]) $wrapperEl.append(slides[i]);\n    }\n    newActiveIndex = activeIndexBuffer > index ? activeIndexBuffer + slides.length : activeIndexBuffer;\n  } else {\n    $wrapperEl.append(slides);\n  }\n\n  for (let i = 0; i < slidesBuffer.length; i += 1) {\n    $wrapperEl.append(slidesBuffer[i]);\n  }\n\n  if (params.loop) {\n    swiper.loopCreate();\n  }\n  if (!(params.observer && Support.observer)) {\n    swiper.update();\n  }\n  if (params.loop) {\n    swiper.slideTo(newActiveIndex + swiper.loopedSlides, 0, false);\n  } else {\n    swiper.slideTo(newActiveIndex, 0, false);\n  }\n}\n\nfunction removeSlide (slidesIndexes) {\n  const swiper = this;\n  const { params, $wrapperEl, activeIndex } = swiper;\n\n  let activeIndexBuffer = activeIndex;\n  if (params.loop) {\n    activeIndexBuffer -= swiper.loopedSlides;\n    swiper.loopDestroy();\n    swiper.slides = $wrapperEl.children(`.${params.slideClass}`);\n  }\n  let newActiveIndex = activeIndexBuffer;\n  let indexToRemove;\n\n  if (typeof slidesIndexes === 'object' && 'length' in slidesIndexes) {\n    for (let i = 0; i < slidesIndexes.length; i += 1) {\n      indexToRemove = slidesIndexes[i];\n      if (swiper.slides[indexToRemove]) swiper.slides.eq(indexToRemove).remove();\n      if (indexToRemove < newActiveIndex) newActiveIndex -= 1;\n    }\n    newActiveIndex = Math.max(newActiveIndex, 0);\n  } else {\n    indexToRemove = slidesIndexes;\n    if (swiper.slides[indexToRemove]) swiper.slides.eq(indexToRemove).remove();\n    if (indexToRemove < newActiveIndex) newActiveIndex -= 1;\n    newActiveIndex = Math.max(newActiveIndex, 0);\n  }\n\n  if (params.loop) {\n    swiper.loopCreate();\n  }\n\n  if (!(params.observer && Support.observer)) {\n    swiper.update();\n  }\n  if (params.loop) {\n    swiper.slideTo(newActiveIndex + swiper.loopedSlides, 0, false);\n  } else {\n    swiper.slideTo(newActiveIndex, 0, false);\n  }\n}\n\nfunction removeAllSlides () {\n  const swiper = this;\n\n  const slidesIndexes = [];\n  for (let i = 0; i < swiper.slides.length; i += 1) {\n    slidesIndexes.push(i);\n  }\n  swiper.removeSlide(slidesIndexes);\n}\n\nvar manipulation = {\n  appendSlide,\n  prependSlide,\n  addSlide,\n  removeSlide,\n  removeAllSlides,\n};\n\nconst Device = (function Device() {\n  const platform = window.navigator.platform;\n  const ua = window.navigator.userAgent;\n\n  const device = {\n    ios: false,\n    android: false,\n    androidChrome: false,\n    desktop: false,\n    iphone: false,\n    ipod: false,\n    ipad: false,\n    edge: false,\n    ie: false,\n    firefox: false,\n    macos: false,\n    windows: false,\n    cordova: !!(window.cordova || window.phonegap),\n    phonegap: !!(window.cordova || window.phonegap),\n    electron: false,\n  };\n\n  const screenWidth = window.screen.width;\n  const screenHeight = window.screen.height;\n\n  const android = ua.match(/(Android);?[\\s\\/]+([\\d.]+)?/); // eslint-disable-line\n  let ipad = ua.match(/(iPad).*OS\\s([\\d_]+)/);\n  const ipod = ua.match(/(iPod)(.*OS\\s([\\d_]+))?/);\n  const iphone = !ipad && ua.match(/(iPhone\\sOS|iOS)\\s([\\d_]+)/);\n  const ie = ua.indexOf('MSIE ') >= 0 || ua.indexOf('Trident/') >= 0;\n  const edge = ua.indexOf('Edge/') >= 0;\n  const firefox = ua.indexOf('Gecko/') >= 0 && ua.indexOf('Firefox/') >= 0;\n  const windows = platform === 'Win32';\n  const electron = ua.toLowerCase().indexOf('electron') >= 0;\n  let macos = platform === 'MacIntel';\n\n  // iPadOs 13 fix\n  if (!ipad\n    && macos\n    && Support.touch\n    && (\n      (screenWidth === 1024 && screenHeight === 1366) // Pro 12.9\n      || (screenWidth === 834 && screenHeight === 1194) // Pro 11\n      || (screenWidth === 834 && screenHeight === 1112) // Pro 10.5\n      || (screenWidth === 768 && screenHeight === 1024) // other\n    )\n  ) {\n    ipad = ua.match(/(Version)\\/([\\d.]+)/);\n    macos = false;\n  }\n\n  device.ie = ie;\n  device.edge = edge;\n  device.firefox = firefox;\n\n  // Android\n  if (android && !windows) {\n    device.os = 'android';\n    device.osVersion = android[2];\n    device.android = true;\n    device.androidChrome = ua.toLowerCase().indexOf('chrome') >= 0;\n  }\n  if (ipad || iphone || ipod) {\n    device.os = 'ios';\n    device.ios = true;\n  }\n  // iOS\n  if (iphone && !ipod) {\n    device.osVersion = iphone[2].replace(/_/g, '.');\n    device.iphone = true;\n  }\n  if (ipad) {\n    device.osVersion = ipad[2].replace(/_/g, '.');\n    device.ipad = true;\n  }\n  if (ipod) {\n    device.osVersion = ipod[3] ? ipod[3].replace(/_/g, '.') : null;\n    device.ipod = true;\n  }\n  // iOS 8+ changed UA\n  if (device.ios && device.osVersion && ua.indexOf('Version/') >= 0) {\n    if (device.osVersion.split('.')[0] === '10') {\n      device.osVersion = ua.toLowerCase().split('version/')[1].split(' ')[0];\n    }\n  }\n\n  // Webview\n  device.webView = !!((iphone || ipad || ipod) && (ua.match(/.*AppleWebKit(?!.*Safari)/i) || window.navigator.standalone))\n    || (window.matchMedia && window.matchMedia('(display-mode: standalone)').matches);\n  device.webview = device.webView;\n  device.standalone = device.webView;\n\n  // Desktop\n  device.desktop = !(device.ios || device.android) || electron;\n  if (device.desktop) {\n    device.electron = electron;\n    device.macos = macos;\n    device.windows = windows;\n    if (device.macos) {\n      device.os = 'macos';\n    }\n    if (device.windows) {\n      device.os = 'windows';\n    }\n  }\n\n  // Pixel Ratio\n  device.pixelRatio = window.devicePixelRatio || 1;\n\n  // Export object\n  return device;\n}());\n\nfunction onTouchStart (event) {\n  const swiper = this;\n  const data = swiper.touchEventsData;\n  const { params, touches } = swiper;\n\n  if (swiper.animating && params.preventInteractionOnTransition) {\n    return;\n  }\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n  const $targetEl = $(e.target);\n\n  if (params.touchEventsTarget === 'wrapper') {\n    if (!$targetEl.closest(swiper.wrapperEl).length) return;\n  }\n  data.isTouchEvent = e.type === 'touchstart';\n  if (!data.isTouchEvent && 'which' in e && e.which === 3) return;\n  if (!data.isTouchEvent && 'button' in e && e.button > 0) return;\n  if (data.isTouched && data.isMoved) return;\n  if (params.noSwiping && $targetEl.closest(params.noSwipingSelector ? params.noSwipingSelector : `.${params.noSwipingClass}`)[0]) {\n    swiper.allowClick = true;\n    return;\n  }\n  if (params.swipeHandler) {\n    if (!$targetEl.closest(params.swipeHandler)[0]) return;\n  }\n\n  touches.currentX = e.type === 'touchstart' ? e.targetTouches[0].pageX : e.pageX;\n  touches.currentY = e.type === 'touchstart' ? e.targetTouches[0].pageY : e.pageY;\n  const startX = touches.currentX;\n  const startY = touches.currentY;\n\n  // Do NOT start if iOS edge swipe is detected. Otherwise iOS app (UIWebView) cannot swipe-to-go-back anymore\n\n  const edgeSwipeDetection = params.edgeSwipeDetection || params.iOSEdgeSwipeDetection;\n  const edgeSwipeThreshold = params.edgeSwipeThreshold || params.iOSEdgeSwipeThreshold;\n  if (\n    edgeSwipeDetection\n    && ((startX <= edgeSwipeThreshold)\n    || (startX >= window.screen.width - edgeSwipeThreshold))\n  ) {\n    return;\n  }\n\n  Utils.extend(data, {\n    isTouched: true,\n    isMoved: false,\n    allowTouchCallbacks: true,\n    isScrolling: undefined,\n    startMoving: undefined,\n  });\n\n  touches.startX = startX;\n  touches.startY = startY;\n  data.touchStartTime = Utils.now();\n  swiper.allowClick = true;\n  swiper.updateSize();\n  swiper.swipeDirection = undefined;\n  if (params.threshold > 0) data.allowThresholdMove = false;\n  if (e.type !== 'touchstart') {\n    let preventDefault = true;\n    if ($targetEl.is(data.formElements)) preventDefault = false;\n    if (\n      document$1.activeElement\n      && $(document$1.activeElement).is(data.formElements)\n      && document$1.activeElement !== $targetEl[0]\n    ) {\n      document$1.activeElement.blur();\n    }\n\n    const shouldPreventDefault = preventDefault && swiper.allowTouchMove && params.touchStartPreventDefault;\n    if (params.touchStartForcePreventDefault || shouldPreventDefault) {\n      e.preventDefault();\n    }\n  }\n  swiper.emit('touchStart', e);\n}\n\nfunction onTouchMove (event) {\n  const swiper = this;\n  const data = swiper.touchEventsData;\n  const { params, touches, rtlTranslate: rtl } = swiper;\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n  if (!data.isTouched) {\n    if (data.startMoving && data.isScrolling) {\n      swiper.emit('touchMoveOpposite', e);\n    }\n    return;\n  }\n  if (data.isTouchEvent && e.type !== 'touchmove') return;\n  const targetTouch = e.type === 'touchmove' && e.targetTouches && (e.targetTouches[0] || e.changedTouches[0]);\n  const pageX = e.type === 'touchmove' ? targetTouch.pageX : e.pageX;\n  const pageY = e.type === 'touchmove' ? targetTouch.pageY : e.pageY;\n  if (e.preventedByNestedSwiper) {\n    touches.startX = pageX;\n    touches.startY = pageY;\n    return;\n  }\n  if (!swiper.allowTouchMove) {\n    // isMoved = true;\n    swiper.allowClick = false;\n    if (data.isTouched) {\n      Utils.extend(touches, {\n        startX: pageX,\n        startY: pageY,\n        currentX: pageX,\n        currentY: pageY,\n      });\n      data.touchStartTime = Utils.now();\n    }\n    return;\n  }\n  if (data.isTouchEvent && params.touchReleaseOnEdges && !params.loop) {\n    if (swiper.isVertical()) {\n      // Vertical\n      if (\n        (pageY < touches.startY && swiper.translate <= swiper.maxTranslate())\n        || (pageY > touches.startY && swiper.translate >= swiper.minTranslate())\n      ) {\n        data.isTouched = false;\n        data.isMoved = false;\n        return;\n      }\n    } else if (\n      (pageX < touches.startX && swiper.translate <= swiper.maxTranslate())\n      || (pageX > touches.startX && swiper.translate >= swiper.minTranslate())\n    ) {\n      return;\n    }\n  }\n  if (data.isTouchEvent && document$1.activeElement) {\n    if (e.target === document$1.activeElement && $(e.target).is(data.formElements)) {\n      data.isMoved = true;\n      swiper.allowClick = false;\n      return;\n    }\n  }\n  if (data.allowTouchCallbacks) {\n    swiper.emit('touchMove', e);\n  }\n  if (e.targetTouches && e.targetTouches.length > 1) return;\n\n  touches.currentX = pageX;\n  touches.currentY = pageY;\n\n  const diffX = touches.currentX - touches.startX;\n  const diffY = touches.currentY - touches.startY;\n  if (swiper.params.threshold && Math.sqrt((diffX ** 2) + (diffY ** 2)) < swiper.params.threshold) return;\n\n  if (typeof data.isScrolling === 'undefined') {\n    let touchAngle;\n    if ((swiper.isHorizontal() && touches.currentY === touches.startY) || (swiper.isVertical() && touches.currentX === touches.startX)) {\n      data.isScrolling = false;\n    } else {\n      // eslint-disable-next-line\n      if ((diffX * diffX) + (diffY * diffY) >= 25) {\n        touchAngle = (Math.atan2(Math.abs(diffY), Math.abs(diffX)) * 180) / Math.PI;\n        data.isScrolling = swiper.isHorizontal() ? touchAngle > params.touchAngle : (90 - touchAngle > params.touchAngle);\n      }\n    }\n  }\n  if (data.isScrolling) {\n    swiper.emit('touchMoveOpposite', e);\n  }\n  if (typeof data.startMoving === 'undefined') {\n    if (touches.currentX !== touches.startX || touches.currentY !== touches.startY) {\n      data.startMoving = true;\n    }\n  }\n  if (data.isScrolling) {\n    data.isTouched = false;\n    return;\n  }\n  if (!data.startMoving) {\n    return;\n  }\n  swiper.allowClick = false;\n  if (!params.cssMode && e.cancelable) {\n    e.preventDefault();\n  }\n  if (params.touchMoveStopPropagation && !params.nested) {\n    e.stopPropagation();\n  }\n\n  if (!data.isMoved) {\n    if (params.loop) {\n      swiper.loopFix();\n    }\n    data.startTranslate = swiper.getTranslate();\n    swiper.setTransition(0);\n    if (swiper.animating) {\n      swiper.$wrapperEl.trigger('webkitTransitionEnd transitionend');\n    }\n    data.allowMomentumBounce = false;\n    // Grab Cursor\n    if (params.grabCursor && (swiper.allowSlideNext === true || swiper.allowSlidePrev === true)) {\n      swiper.setGrabCursor(true);\n    }\n    swiper.emit('sliderFirstMove', e);\n  }\n  swiper.emit('sliderMove', e);\n  data.isMoved = true;\n\n  let diff = swiper.isHorizontal() ? diffX : diffY;\n  touches.diff = diff;\n\n  diff *= params.touchRatio;\n  if (rtl) diff = -diff;\n\n  swiper.swipeDirection = diff > 0 ? 'prev' : 'next';\n  data.currentTranslate = diff + data.startTranslate;\n\n  let disableParentSwiper = true;\n  let resistanceRatio = params.resistanceRatio;\n  if (params.touchReleaseOnEdges) {\n    resistanceRatio = 0;\n  }\n  if ((diff > 0 && data.currentTranslate > swiper.minTranslate())) {\n    disableParentSwiper = false;\n    if (params.resistance) data.currentTranslate = (swiper.minTranslate() - 1) + ((-swiper.minTranslate() + data.startTranslate + diff) ** resistanceRatio);\n  } else if (diff < 0 && data.currentTranslate < swiper.maxTranslate()) {\n    disableParentSwiper = false;\n    if (params.resistance) data.currentTranslate = (swiper.maxTranslate() + 1) - ((swiper.maxTranslate() - data.startTranslate - diff) ** resistanceRatio);\n  }\n\n  if (disableParentSwiper) {\n    e.preventedByNestedSwiper = true;\n  }\n\n  // Directions locks\n  if (!swiper.allowSlideNext && swiper.swipeDirection === 'next' && data.currentTranslate < data.startTranslate) {\n    data.currentTranslate = data.startTranslate;\n  }\n  if (!swiper.allowSlidePrev && swiper.swipeDirection === 'prev' && data.currentTranslate > data.startTranslate) {\n    data.currentTranslate = data.startTranslate;\n  }\n\n\n  // Threshold\n  if (params.threshold > 0) {\n    if (Math.abs(diff) > params.threshold || data.allowThresholdMove) {\n      if (!data.allowThresholdMove) {\n        data.allowThresholdMove = true;\n        touches.startX = touches.currentX;\n        touches.startY = touches.currentY;\n        data.currentTranslate = data.startTranslate;\n        touches.diff = swiper.isHorizontal() ? touches.currentX - touches.startX : touches.currentY - touches.startY;\n        return;\n      }\n    } else {\n      data.currentTranslate = data.startTranslate;\n      return;\n    }\n  }\n\n  if (!params.followFinger || params.cssMode) return;\n\n  // Update active index in free mode\n  if (params.freeMode || params.watchSlidesProgress || params.watchSlidesVisibility) {\n    swiper.updateActiveIndex();\n    swiper.updateSlidesClasses();\n  }\n  if (params.freeMode) {\n    // Velocity\n    if (data.velocities.length === 0) {\n      data.velocities.push({\n        position: touches[swiper.isHorizontal() ? 'startX' : 'startY'],\n        time: data.touchStartTime,\n      });\n    }\n    data.velocities.push({\n      position: touches[swiper.isHorizontal() ? 'currentX' : 'currentY'],\n      time: Utils.now(),\n    });\n  }\n  // Update progress\n  swiper.updateProgress(data.currentTranslate);\n  // Update translate\n  swiper.setTranslate(data.currentTranslate);\n}\n\nfunction onTouchEnd (event) {\n  const swiper = this;\n  const data = swiper.touchEventsData;\n\n  const {\n    params, touches, rtlTranslate: rtl, $wrapperEl, slidesGrid, snapGrid,\n  } = swiper;\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n  if (data.allowTouchCallbacks) {\n    swiper.emit('touchEnd', e);\n  }\n  data.allowTouchCallbacks = false;\n  if (!data.isTouched) {\n    if (data.isMoved && params.grabCursor) {\n      swiper.setGrabCursor(false);\n    }\n    data.isMoved = false;\n    data.startMoving = false;\n    return;\n  }\n  // Return Grab Cursor\n  if (params.grabCursor && data.isMoved && data.isTouched && (swiper.allowSlideNext === true || swiper.allowSlidePrev === true)) {\n    swiper.setGrabCursor(false);\n  }\n\n  // Time diff\n  const touchEndTime = Utils.now();\n  const timeDiff = touchEndTime - data.touchStartTime;\n\n  // Tap, doubleTap, Click\n  if (swiper.allowClick) {\n    swiper.updateClickedSlide(e);\n    swiper.emit('tap click', e);\n    if (timeDiff < 300 && (touchEndTime - data.lastClickTime) < 300) {\n      swiper.emit('doubleTap doubleClick', e);\n    }\n  }\n\n  data.lastClickTime = Utils.now();\n  Utils.nextTick(() => {\n    if (!swiper.destroyed) swiper.allowClick = true;\n  });\n\n  if (!data.isTouched || !data.isMoved || !swiper.swipeDirection || touches.diff === 0 || data.currentTranslate === data.startTranslate) {\n    data.isTouched = false;\n    data.isMoved = false;\n    data.startMoving = false;\n    return;\n  }\n  data.isTouched = false;\n  data.isMoved = false;\n  data.startMoving = false;\n\n  let currentPos;\n  if (params.followFinger) {\n    currentPos = rtl ? swiper.translate : -swiper.translate;\n  } else {\n    currentPos = -data.currentTranslate;\n  }\n\n  if (params.cssMode) {\n    return;\n  }\n\n  if (params.freeMode) {\n    if (currentPos < -swiper.minTranslate()) {\n      swiper.slideTo(swiper.activeIndex);\n      return;\n    }\n    if (currentPos > -swiper.maxTranslate()) {\n      if (swiper.slides.length < snapGrid.length) {\n        swiper.slideTo(snapGrid.length - 1);\n      } else {\n        swiper.slideTo(swiper.slides.length - 1);\n      }\n      return;\n    }\n\n    if (params.freeModeMomentum) {\n      if (data.velocities.length > 1) {\n        const lastMoveEvent = data.velocities.pop();\n        const velocityEvent = data.velocities.pop();\n\n        const distance = lastMoveEvent.position - velocityEvent.position;\n        const time = lastMoveEvent.time - velocityEvent.time;\n        swiper.velocity = distance / time;\n        swiper.velocity /= 2;\n        if (Math.abs(swiper.velocity) < params.freeModeMinimumVelocity) {\n          swiper.velocity = 0;\n        }\n        // this implies that the user stopped moving a finger then released.\n        // There would be no events with distance zero, so the last event is stale.\n        if (time > 150 || (Utils.now() - lastMoveEvent.time) > 300) {\n          swiper.velocity = 0;\n        }\n      } else {\n        swiper.velocity = 0;\n      }\n      swiper.velocity *= params.freeModeMomentumVelocityRatio;\n\n      data.velocities.length = 0;\n      let momentumDuration = 1000 * params.freeModeMomentumRatio;\n      const momentumDistance = swiper.velocity * momentumDuration;\n\n      let newPosition = swiper.translate + momentumDistance;\n      if (rtl) newPosition = -newPosition;\n\n      let doBounce = false;\n      let afterBouncePosition;\n      const bounceAmount = Math.abs(swiper.velocity) * 20 * params.freeModeMomentumBounceRatio;\n      let needsLoopFix;\n      if (newPosition < swiper.maxTranslate()) {\n        if (params.freeModeMomentumBounce) {\n          if (newPosition + swiper.maxTranslate() < -bounceAmount) {\n            newPosition = swiper.maxTranslate() - bounceAmount;\n          }\n          afterBouncePosition = swiper.maxTranslate();\n          doBounce = true;\n          data.allowMomentumBounce = true;\n        } else {\n          newPosition = swiper.maxTranslate();\n        }\n        if (params.loop && params.centeredSlides) needsLoopFix = true;\n      } else if (newPosition > swiper.minTranslate()) {\n        if (params.freeModeMomentumBounce) {\n          if (newPosition - swiper.minTranslate() > bounceAmount) {\n            newPosition = swiper.minTranslate() + bounceAmount;\n          }\n          afterBouncePosition = swiper.minTranslate();\n          doBounce = true;\n          data.allowMomentumBounce = true;\n        } else {\n          newPosition = swiper.minTranslate();\n        }\n        if (params.loop && params.centeredSlides) needsLoopFix = true;\n      } else if (params.freeModeSticky) {\n        let nextSlide;\n        for (let j = 0; j < snapGrid.length; j += 1) {\n          if (snapGrid[j] > -newPosition) {\n            nextSlide = j;\n            break;\n          }\n        }\n\n        if (Math.abs(snapGrid[nextSlide] - newPosition) < Math.abs(snapGrid[nextSlide - 1] - newPosition) || swiper.swipeDirection === 'next') {\n          newPosition = snapGrid[nextSlide];\n        } else {\n          newPosition = snapGrid[nextSlide - 1];\n        }\n        newPosition = -newPosition;\n      }\n      if (needsLoopFix) {\n        swiper.once('transitionEnd', () => {\n          swiper.loopFix();\n        });\n      }\n      // Fix duration\n      if (swiper.velocity !== 0) {\n        if (rtl) {\n          momentumDuration = Math.abs((-newPosition - swiper.translate) / swiper.velocity);\n        } else {\n          momentumDuration = Math.abs((newPosition - swiper.translate) / swiper.velocity);\n        }\n        if (params.freeModeSticky) {\n          // If freeModeSticky is active and the user ends a swipe with a slow-velocity\n          // event, then durations can be 20+ seconds to slide one (or zero!) slides.\n          // It's easy to see this when simulating touch with mouse events. To fix this,\n          // limit single-slide swipes to the default slide duration. This also has the\n          // nice side effect of matching slide speed if the user stopped moving before\n          // lifting finger or mouse vs. moving slowly before lifting the finger/mouse.\n          // For faster swipes, also apply limits (albeit higher ones).\n          const moveDistance = Math.abs((rtl ? -newPosition : newPosition) - swiper.translate);\n          const currentSlideSize = swiper.slidesSizesGrid[swiper.activeIndex];\n          if (moveDistance < currentSlideSize) {\n            momentumDuration = params.speed;\n          } else if (moveDistance < 2 * currentSlideSize) {\n            momentumDuration = params.speed * 1.5;\n          } else {\n            momentumDuration = params.speed * 2.5;\n          }\n        }\n      } else if (params.freeModeSticky) {\n        swiper.slideToClosest();\n        return;\n      }\n\n      if (params.freeModeMomentumBounce && doBounce) {\n        swiper.updateProgress(afterBouncePosition);\n        swiper.setTransition(momentumDuration);\n        swiper.setTranslate(newPosition);\n        swiper.transitionStart(true, swiper.swipeDirection);\n        swiper.animating = true;\n        $wrapperEl.transitionEnd(() => {\n          if (!swiper || swiper.destroyed || !data.allowMomentumBounce) return;\n          swiper.emit('momentumBounce');\n          swiper.setTransition(params.speed);\n          setTimeout(() => {\n            swiper.setTranslate(afterBouncePosition);\n            $wrapperEl.transitionEnd(() => {\n              if (!swiper || swiper.destroyed) return;\n              swiper.transitionEnd();\n            });\n          }, 0);\n        });\n      } else if (swiper.velocity) {\n        swiper.updateProgress(newPosition);\n        swiper.setTransition(momentumDuration);\n        swiper.setTranslate(newPosition);\n        swiper.transitionStart(true, swiper.swipeDirection);\n        if (!swiper.animating) {\n          swiper.animating = true;\n          $wrapperEl.transitionEnd(() => {\n            if (!swiper || swiper.destroyed) return;\n            swiper.transitionEnd();\n          });\n        }\n      } else {\n        swiper.updateProgress(newPosition);\n      }\n\n      swiper.updateActiveIndex();\n      swiper.updateSlidesClasses();\n    } else if (params.freeModeSticky) {\n      swiper.slideToClosest();\n      return;\n    }\n\n    if (!params.freeModeMomentum || timeDiff >= params.longSwipesMs) {\n      swiper.updateProgress();\n      swiper.updateActiveIndex();\n      swiper.updateSlidesClasses();\n    }\n    return;\n  }\n\n  // Find current slide\n  let stopIndex = 0;\n  let groupSize = swiper.slidesSizesGrid[0];\n  for (let i = 0; i < slidesGrid.length; i += (i < params.slidesPerGroupSkip ? 1 : params.slidesPerGroup)) {\n    const increment = (i < params.slidesPerGroupSkip - 1 ? 1 : params.slidesPerGroup);\n    if (typeof slidesGrid[i + increment] !== 'undefined') {\n      if (currentPos >= slidesGrid[i] && currentPos < slidesGrid[i + increment]) {\n        stopIndex = i;\n        groupSize = slidesGrid[i + increment] - slidesGrid[i];\n      }\n    } else if (currentPos >= slidesGrid[i]) {\n      stopIndex = i;\n      groupSize = slidesGrid[slidesGrid.length - 1] - slidesGrid[slidesGrid.length - 2];\n    }\n  }\n\n  // Find current slide size\n  const ratio = (currentPos - slidesGrid[stopIndex]) / groupSize;\n  const increment = (stopIndex < params.slidesPerGroupSkip - 1 ? 1 : params.slidesPerGroup);\n\n  if (timeDiff > params.longSwipesMs) {\n    // Long touches\n    if (!params.longSwipes) {\n      swiper.slideTo(swiper.activeIndex);\n      return;\n    }\n    if (swiper.swipeDirection === 'next') {\n      if (ratio >= params.longSwipesRatio) swiper.slideTo(stopIndex + increment);\n      else swiper.slideTo(stopIndex);\n    }\n    if (swiper.swipeDirection === 'prev') {\n      if (ratio > (1 - params.longSwipesRatio)) swiper.slideTo(stopIndex + increment);\n      else swiper.slideTo(stopIndex);\n    }\n  } else {\n    // Short swipes\n    if (!params.shortSwipes) {\n      swiper.slideTo(swiper.activeIndex);\n      return;\n    }\n    const isNavButtonTarget = swiper.navigation && (e.target === swiper.navigation.nextEl || e.target === swiper.navigation.prevEl);\n    if (!isNavButtonTarget) {\n      if (swiper.swipeDirection === 'next') {\n        swiper.slideTo(stopIndex + increment);\n      }\n      if (swiper.swipeDirection === 'prev') {\n        swiper.slideTo(stopIndex);\n      }\n    } else if (e.target === swiper.navigation.nextEl) {\n      swiper.slideTo(stopIndex + increment);\n    } else {\n      swiper.slideTo(stopIndex);\n    }\n  }\n}\n\nfunction onResize () {\n  const swiper = this;\n\n  const { params, el } = swiper;\n\n  if (el && el.offsetWidth === 0) return;\n\n  // Breakpoints\n  if (params.breakpoints) {\n    swiper.setBreakpoint();\n  }\n\n  // Save locks\n  const { allowSlideNext, allowSlidePrev, snapGrid } = swiper;\n\n  // Disable locks on resize\n  swiper.allowSlideNext = true;\n  swiper.allowSlidePrev = true;\n\n  swiper.updateSize();\n  swiper.updateSlides();\n\n  swiper.updateSlidesClasses();\n  if ((params.slidesPerView === 'auto' || params.slidesPerView > 1) && swiper.isEnd && !swiper.params.centeredSlides) {\n    swiper.slideTo(swiper.slides.length - 1, 0, false, true);\n  } else {\n    swiper.slideTo(swiper.activeIndex, 0, false, true);\n  }\n\n  if (swiper.autoplay && swiper.autoplay.running && swiper.autoplay.paused) {\n    swiper.autoplay.run();\n  }\n  // Return locks after resize\n  swiper.allowSlidePrev = allowSlidePrev;\n  swiper.allowSlideNext = allowSlideNext;\n\n  if (swiper.params.watchOverflow && snapGrid !== swiper.snapGrid) {\n    swiper.checkOverflow();\n  }\n}\n\nfunction onClick (e) {\n  const swiper = this;\n  if (!swiper.allowClick) {\n    if (swiper.params.preventClicks) e.preventDefault();\n    if (swiper.params.preventClicksPropagation && swiper.animating) {\n      e.stopPropagation();\n      e.stopImmediatePropagation();\n    }\n  }\n}\n\nfunction onScroll () {\n  const swiper = this;\n  const { wrapperEl, rtlTranslate } = swiper;\n  swiper.previousTranslate = swiper.translate;\n  if (swiper.isHorizontal()) {\n    if (rtlTranslate) {\n      swiper.translate = ((wrapperEl.scrollWidth - wrapperEl.offsetWidth) - wrapperEl.scrollLeft);\n    } else {\n      swiper.translate = -wrapperEl.scrollLeft;\n    }\n  } else {\n    swiper.translate = -wrapperEl.scrollTop;\n  }\n  // eslint-disable-next-line\n  if (swiper.translate === -0) swiper.translate = 0;\n\n  swiper.updateActiveIndex();\n  swiper.updateSlidesClasses();\n\n  let newProgress;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n  if (translatesDiff === 0) {\n    newProgress = 0;\n  } else {\n    newProgress = (swiper.translate - swiper.minTranslate()) / (translatesDiff);\n  }\n  if (newProgress !== swiper.progress) {\n    swiper.updateProgress(rtlTranslate ? -swiper.translate : swiper.translate);\n  }\n\n  swiper.emit('setTranslate', swiper.translate, false);\n}\n\nlet dummyEventAttached = false;\nfunction dummyEventListener() {}\n\nfunction attachEvents() {\n  const swiper = this;\n  const {\n    params, touchEvents, el, wrapperEl,\n  } = swiper;\n\n  swiper.onTouchStart = onTouchStart.bind(swiper);\n  swiper.onTouchMove = onTouchMove.bind(swiper);\n  swiper.onTouchEnd = onTouchEnd.bind(swiper);\n  if (params.cssMode) {\n    swiper.onScroll = onScroll.bind(swiper);\n  }\n\n  swiper.onClick = onClick.bind(swiper);\n\n  const capture = !!params.nested;\n\n  // Touch Events\n  if (!Support.touch && Support.pointerEvents) {\n    el.addEventListener(touchEvents.start, swiper.onTouchStart, false);\n    document$1.addEventListener(touchEvents.move, swiper.onTouchMove, capture);\n    document$1.addEventListener(touchEvents.end, swiper.onTouchEnd, false);\n  } else {\n    if (Support.touch) {\n      const passiveListener = touchEvents.start === 'touchstart' && Support.passiveListener && params.passiveListeners ? { passive: true, capture: false } : false;\n      el.addEventListener(touchEvents.start, swiper.onTouchStart, passiveListener);\n      el.addEventListener(touchEvents.move, swiper.onTouchMove, Support.passiveListener ? { passive: false, capture } : capture);\n      el.addEventListener(touchEvents.end, swiper.onTouchEnd, passiveListener);\n      if (touchEvents.cancel) {\n        el.addEventListener(touchEvents.cancel, swiper.onTouchEnd, passiveListener);\n      }\n      if (!dummyEventAttached) {\n        document$1.addEventListener('touchstart', dummyEventListener);\n        dummyEventAttached = true;\n      }\n    }\n    if ((params.simulateTouch && !Device.ios && !Device.android) || (params.simulateTouch && !Support.touch && Device.ios)) {\n      el.addEventListener('mousedown', swiper.onTouchStart, false);\n      document$1.addEventListener('mousemove', swiper.onTouchMove, capture);\n      document$1.addEventListener('mouseup', swiper.onTouchEnd, false);\n    }\n  }\n  // Prevent Links Clicks\n  if (params.preventClicks || params.preventClicksPropagation) {\n    el.addEventListener('click', swiper.onClick, true);\n  }\n  if (params.cssMode) {\n    wrapperEl.addEventListener('scroll', swiper.onScroll);\n  }\n\n  // Resize handler\n  if (params.updateOnWindowResize) {\n    swiper.on((Device.ios || Device.android ? 'resize orientationchange observerUpdate' : 'resize observerUpdate'), onResize, true);\n  } else {\n    swiper.on('observerUpdate', onResize, true);\n  }\n}\n\nfunction detachEvents() {\n  const swiper = this;\n\n  const {\n    params, touchEvents, el, wrapperEl,\n  } = swiper;\n\n  const capture = !!params.nested;\n\n  // Touch Events\n  if (!Support.touch && Support.pointerEvents) {\n    el.removeEventListener(touchEvents.start, swiper.onTouchStart, false);\n    document$1.removeEventListener(touchEvents.move, swiper.onTouchMove, capture);\n    document$1.removeEventListener(touchEvents.end, swiper.onTouchEnd, false);\n  } else {\n    if (Support.touch) {\n      const passiveListener = touchEvents.start === 'onTouchStart' && Support.passiveListener && params.passiveListeners ? { passive: true, capture: false } : false;\n      el.removeEventListener(touchEvents.start, swiper.onTouchStart, passiveListener);\n      el.removeEventListener(touchEvents.move, swiper.onTouchMove, capture);\n      el.removeEventListener(touchEvents.end, swiper.onTouchEnd, passiveListener);\n      if (touchEvents.cancel) {\n        el.removeEventListener(touchEvents.cancel, swiper.onTouchEnd, passiveListener);\n      }\n    }\n    if ((params.simulateTouch && !Device.ios && !Device.android) || (params.simulateTouch && !Support.touch && Device.ios)) {\n      el.removeEventListener('mousedown', swiper.onTouchStart, false);\n      document$1.removeEventListener('mousemove', swiper.onTouchMove, capture);\n      document$1.removeEventListener('mouseup', swiper.onTouchEnd, false);\n    }\n  }\n  // Prevent Links Clicks\n  if (params.preventClicks || params.preventClicksPropagation) {\n    el.removeEventListener('click', swiper.onClick, true);\n  }\n\n  if (params.cssMode) {\n    wrapperEl.removeEventListener('scroll', swiper.onScroll);\n  }\n\n  // Resize handler\n  swiper.off((Device.ios || Device.android ? 'resize orientationchange observerUpdate' : 'resize observerUpdate'), onResize);\n}\n\nvar events = {\n  attachEvents,\n  detachEvents,\n};\n\nfunction setBreakpoint () {\n  const swiper = this;\n  const {\n    activeIndex, initialized, loopedSlides = 0, params, $el,\n  } = swiper;\n  const breakpoints = params.breakpoints;\n  if (!breakpoints || (breakpoints && Object.keys(breakpoints).length === 0)) return;\n\n  // Get breakpoint for window width and update parameters\n  const breakpoint = swiper.getBreakpoint(breakpoints);\n\n  if (breakpoint && swiper.currentBreakpoint !== breakpoint) {\n    const breakpointOnlyParams = breakpoint in breakpoints ? breakpoints[breakpoint] : undefined;\n    if (breakpointOnlyParams) {\n      ['slidesPerView', 'spaceBetween', 'slidesPerGroup', 'slidesPerGroupSkip', 'slidesPerColumn'].forEach((param) => {\n        const paramValue = breakpointOnlyParams[param];\n        if (typeof paramValue === 'undefined') return;\n        if (param === 'slidesPerView' && (paramValue === 'AUTO' || paramValue === 'auto')) {\n          breakpointOnlyParams[param] = 'auto';\n        } else if (param === 'slidesPerView') {\n          breakpointOnlyParams[param] = parseFloat(paramValue);\n        } else {\n          breakpointOnlyParams[param] = parseInt(paramValue, 10);\n        }\n      });\n    }\n\n    const breakpointParams = breakpointOnlyParams || swiper.originalParams;\n    const wasMultiRow = params.slidesPerColumn > 1;\n    const isMultiRow = breakpointParams.slidesPerColumn > 1;\n    if (wasMultiRow && !isMultiRow) {\n      $el.removeClass(`${params.containerModifierClass}multirow ${params.containerModifierClass}multirow-column`);\n    } else if (!wasMultiRow && isMultiRow) {\n      $el.addClass(`${params.containerModifierClass}multirow`);\n      if (breakpointParams.slidesPerColumnFill === 'column') {\n        $el.addClass(`${params.containerModifierClass}multirow-column`);\n      }\n    }\n\n    const directionChanged = breakpointParams.direction && breakpointParams.direction !== params.direction;\n    const needsReLoop = params.loop && (breakpointParams.slidesPerView !== params.slidesPerView || directionChanged);\n\n    if (directionChanged && initialized) {\n      swiper.changeDirection();\n    }\n\n    Utils.extend(swiper.params, breakpointParams);\n\n    Utils.extend(swiper, {\n      allowTouchMove: swiper.params.allowTouchMove,\n      allowSlideNext: swiper.params.allowSlideNext,\n      allowSlidePrev: swiper.params.allowSlidePrev,\n    });\n\n    swiper.currentBreakpoint = breakpoint;\n\n    if (needsReLoop && initialized) {\n      swiper.loopDestroy();\n      swiper.loopCreate();\n      swiper.updateSlides();\n      swiper.slideTo((activeIndex - loopedSlides) + swiper.loopedSlides, 0, false);\n    }\n\n    swiper.emit('breakpoint', breakpointParams);\n  }\n}\n\nfunction getBreakpoint (breakpoints) {\n  // Get breakpoint for window width\n  if (!breakpoints) return undefined;\n  let breakpoint = false;\n\n  const points = Object.keys(breakpoints).map((point) => {\n    if (typeof point === 'string' && point.indexOf('@') === 0) {\n      const minRatio = parseFloat(point.substr(1));\n      const value = window.innerHeight * minRatio;\n      return { value, point };\n    }\n    return { value: point, point };\n  });\n\n  points.sort((a, b) => parseInt(a.value, 10) - parseInt(b.value, 10));\n  for (let i = 0; i < points.length; i += 1) {\n    const { point, value } = points[i];\n    if (value <= window.innerWidth) {\n      breakpoint = point;\n    }\n  }\n  return breakpoint || 'max';\n}\n\nvar breakpoints = { setBreakpoint, getBreakpoint };\n\nfunction addClasses () {\n  const swiper = this;\n  const {\n    classNames, params, rtl, $el,\n  } = swiper;\n  const suffixes = [];\n\n  suffixes.push('initialized');\n  suffixes.push(params.direction);\n\n  if (params.freeMode) {\n    suffixes.push('free-mode');\n  }\n  if (params.autoHeight) {\n    suffixes.push('autoheight');\n  }\n  if (rtl) {\n    suffixes.push('rtl');\n  }\n  if (params.slidesPerColumn > 1) {\n    suffixes.push('multirow');\n    if (params.slidesPerColumnFill === 'column') {\n      suffixes.push('multirow-column');\n    }\n  }\n  if (Device.android) {\n    suffixes.push('android');\n  }\n  if (Device.ios) {\n    suffixes.push('ios');\n  }\n\n  if (params.cssMode) {\n    suffixes.push('css-mode');\n  }\n\n  suffixes.forEach((suffix) => {\n    classNames.push(params.containerModifierClass + suffix);\n  });\n\n  $el.addClass(classNames.join(' '));\n}\n\nfunction removeClasses () {\n  const swiper = this;\n  const { $el, classNames } = swiper;\n\n  $el.removeClass(classNames.join(' '));\n}\n\nvar classes = { addClasses, removeClasses };\n\nfunction loadImage (imageEl, src, srcset, sizes, checkForComplete, callback) {\n  let image;\n  function onReady() {\n    if (callback) callback();\n  }\n  const isPicture = $(imageEl).parent('picture')[0];\n\n  if (!isPicture && (!imageEl.complete || !checkForComplete)) {\n    if (src) {\n      image = new window.Image();\n      image.onload = onReady;\n      image.onerror = onReady;\n      if (sizes) {\n        image.sizes = sizes;\n      }\n      if (srcset) {\n        image.srcset = srcset;\n      }\n      if (src) {\n        image.src = src;\n      }\n    } else {\n      onReady();\n    }\n  } else {\n    // image already loaded...\n    onReady();\n  }\n}\n\nfunction preloadImages () {\n  const swiper = this;\n  swiper.imagesToLoad = swiper.$el.find('img');\n  function onReady() {\n    if (typeof swiper === 'undefined' || swiper === null || !swiper || swiper.destroyed) return;\n    if (swiper.imagesLoaded !== undefined) swiper.imagesLoaded += 1;\n    if (swiper.imagesLoaded === swiper.imagesToLoad.length) {\n      if (swiper.params.updateOnImagesReady) swiper.update();\n      swiper.emit('imagesReady');\n    }\n  }\n  for (let i = 0; i < swiper.imagesToLoad.length; i += 1) {\n    const imageEl = swiper.imagesToLoad[i];\n    swiper.loadImage(\n      imageEl,\n      imageEl.currentSrc || imageEl.getAttribute('src'),\n      imageEl.srcset || imageEl.getAttribute('srcset'),\n      imageEl.sizes || imageEl.getAttribute('sizes'),\n      true,\n      onReady\n    );\n  }\n}\n\nvar images = {\n  loadImage,\n  preloadImages,\n};\n\nfunction checkOverflow() {\n  const swiper = this;\n  const params = swiper.params;\n  const wasLocked = swiper.isLocked;\n  const lastSlidePosition = swiper.slides.length > 0 && (params.slidesOffsetBefore + (params.spaceBetween * (swiper.slides.length - 1)) + ((swiper.slides[0]).offsetWidth) * swiper.slides.length);\n\n  if (params.slidesOffsetBefore && params.slidesOffsetAfter && lastSlidePosition) {\n    swiper.isLocked = lastSlidePosition <= swiper.size;\n  } else {\n    swiper.isLocked = swiper.snapGrid.length === 1;\n  }\n\n  swiper.allowSlideNext = !swiper.isLocked;\n  swiper.allowSlidePrev = !swiper.isLocked;\n\n  // events\n  if (wasLocked !== swiper.isLocked) swiper.emit(swiper.isLocked ? 'lock' : 'unlock');\n\n  if (wasLocked && wasLocked !== swiper.isLocked) {\n    swiper.isEnd = false;\n    swiper.navigation.update();\n  }\n}\n\nvar checkOverflow$1 = { checkOverflow };\n\nvar defaults = {\n  init: true,\n  direction: 'horizontal',\n  touchEventsTarget: 'container',\n  initialSlide: 0,\n  speed: 300,\n  cssMode: false,\n  updateOnWindowResize: true,\n  //\n  preventInteractionOnTransition: false,\n\n  // To support iOS's swipe-to-go-back gesture (when being used in-app, with UIWebView).\n  edgeSwipeDetection: false,\n  edgeSwipeThreshold: 20,\n\n  // Free mode\n  freeMode: false,\n  freeModeMomentum: true,\n  freeModeMomentumRatio: 1,\n  freeModeMomentumBounce: true,\n  freeModeMomentumBounceRatio: 1,\n  freeModeMomentumVelocityRatio: 1,\n  freeModeSticky: false,\n  freeModeMinimumVelocity: 0.02,\n\n  // Autoheight\n  autoHeight: false,\n\n  // Set wrapper width\n  setWrapperSize: false,\n\n  // Virtual Translate\n  virtualTranslate: false,\n\n  // Effects\n  effect: 'slide', // 'slide' or 'fade' or 'cube' or 'coverflow' or 'flip'\n\n  // Breakpoints\n  breakpoints: undefined,\n\n  // Slides grid\n  spaceBetween: 0,\n  slidesPerView: 1,\n  slidesPerColumn: 1,\n  slidesPerColumnFill: 'column',\n  slidesPerGroup: 1,\n  slidesPerGroupSkip: 0,\n  centeredSlides: false,\n  centeredSlidesBounds: false,\n  slidesOffsetBefore: 0, // in px\n  slidesOffsetAfter: 0, // in px\n  normalizeSlideIndex: true,\n  centerInsufficientSlides: false,\n\n  // Disable swiper and hide navigation when container not overflow\n  watchOverflow: false,\n\n  // Round length\n  roundLengths: false,\n\n  // Touches\n  touchRatio: 1,\n  touchAngle: 45,\n  simulateTouch: true,\n  shortSwipes: true,\n  longSwipes: true,\n  longSwipesRatio: 0.5,\n  longSwipesMs: 300,\n  followFinger: true,\n  allowTouchMove: true,\n  threshold: 0,\n  touchMoveStopPropagation: false,\n  touchStartPreventDefault: true,\n  touchStartForcePreventDefault: false,\n  touchReleaseOnEdges: false,\n\n  // Unique Navigation Elements\n  uniqueNavElements: true,\n\n  // Resistance\n  resistance: true,\n  resistanceRatio: 0.85,\n\n  // Progress\n  watchSlidesProgress: false,\n  watchSlidesVisibility: false,\n\n  // Cursor\n  grabCursor: false,\n\n  // Clicks\n  preventClicks: true,\n  preventClicksPropagation: true,\n  slideToClickedSlide: false,\n\n  // Images\n  preloadImages: true,\n  updateOnImagesReady: true,\n\n  // loop\n  loop: false,\n  loopAdditionalSlides: 0,\n  loopedSlides: null,\n  loopFillGroupWithBlank: false,\n\n  // Swiping/no swiping\n  allowSlidePrev: true,\n  allowSlideNext: true,\n  swipeHandler: null, // '.swipe-handler',\n  noSwiping: true,\n  noSwipingClass: 'swiper-no-swiping',\n  noSwipingSelector: null,\n\n  // Passive Listeners\n  passiveListeners: true,\n\n  // NS\n  containerModifierClass: 'swiper-container-', // NEW\n  slideClass: 'swiper-slide',\n  slideBlankClass: 'swiper-slide-invisible-blank',\n  slideActiveClass: 'swiper-slide-active',\n  slideDuplicateActiveClass: 'swiper-slide-duplicate-active',\n  slideVisibleClass: 'swiper-slide-visible',\n  slideDuplicateClass: 'swiper-slide-duplicate',\n  slideNextClass: 'swiper-slide-next',\n  slideDuplicateNextClass: 'swiper-slide-duplicate-next',\n  slidePrevClass: 'swiper-slide-prev',\n  slideDuplicatePrevClass: 'swiper-slide-duplicate-prev',\n  wrapperClass: 'swiper-wrapper',\n\n  // Callbacks\n  runCallbacksOnInit: true,\n};\n\n/* eslint no-param-reassign: \"off\" */\n\nconst prototypes = {\n  update,\n  translate,\n  transition,\n  slide,\n  loop,\n  grabCursor,\n  manipulation,\n  events,\n  breakpoints,\n  checkOverflow: checkOverflow$1,\n  classes,\n  images,\n};\n\nconst extendedDefaults = {};\n\nclass Swiper extends SwiperClass {\n  constructor(...args) {\n    let el;\n    let params;\n    if (args.length === 1 && args[0].constructor && args[0].constructor === Object) {\n      params = args[0];\n    } else {\n      [el, params] = args;\n    }\n    if (!params) params = {};\n\n    params = Utils.extend({}, params);\n    if (el && !params.el) params.el = el;\n\n    super(params);\n\n    Object.keys(prototypes).forEach((prototypeGroup) => {\n      Object.keys(prototypes[prototypeGroup]).forEach((protoMethod) => {\n        if (!Swiper.prototype[protoMethod]) {\n          Swiper.prototype[protoMethod] = prototypes[prototypeGroup][protoMethod];\n        }\n      });\n    });\n\n    // Swiper Instance\n    const swiper = this;\n    if (typeof swiper.modules === 'undefined') {\n      swiper.modules = {};\n    }\n    Object.keys(swiper.modules).forEach((moduleName) => {\n      const module = swiper.modules[moduleName];\n      if (module.params) {\n        const moduleParamName = Object.keys(module.params)[0];\n        const moduleParams = module.params[moduleParamName];\n        if (typeof moduleParams !== 'object' || moduleParams === null) return;\n        if (!(moduleParamName in params && 'enabled' in moduleParams)) return;\n        if (params[moduleParamName] === true) {\n          params[moduleParamName] = { enabled: true };\n        }\n        if (\n          typeof params[moduleParamName] === 'object'\n          && !('enabled' in params[moduleParamName])\n        ) {\n          params[moduleParamName].enabled = true;\n        }\n        if (!params[moduleParamName]) params[moduleParamName] = { enabled: false };\n      }\n    });\n\n    // Extend defaults with modules params\n    const swiperParams = Utils.extend({}, defaults);\n    swiper.useModulesParams(swiperParams);\n\n    // Extend defaults with passed params\n    swiper.params = Utils.extend({}, swiperParams, extendedDefaults, params);\n    swiper.originalParams = Utils.extend({}, swiper.params);\n    swiper.passedParams = Utils.extend({}, params);\n\n    // Save Dom lib\n    swiper.$ = $;\n\n    // Find el\n    const $el = $(swiper.params.el);\n    el = $el[0];\n\n    if (!el) {\n      return undefined;\n    }\n\n    if ($el.length > 1) {\n      const swipers = [];\n      $el.each((index, containerEl) => {\n        const newParams = Utils.extend({}, params, { el: containerEl });\n        swipers.push(new Swiper(newParams));\n      });\n      return swipers;\n    }\n\n    el.swiper = swiper;\n    $el.data('swiper', swiper);\n\n    // Find Wrapper\n    let $wrapperEl;\n    if (el && el.shadowRoot && el.shadowRoot.querySelector) {\n      $wrapperEl = $(el.shadowRoot.querySelector(`.${swiper.params.wrapperClass}`));\n      // Children needs to return slot items\n      $wrapperEl.children = (options) => $el.children(options);\n    } else {\n      $wrapperEl = $el.children(`.${swiper.params.wrapperClass}`);\n    }\n    // Extend Swiper\n    Utils.extend(swiper, {\n      $el,\n      el,\n      $wrapperEl,\n      wrapperEl: $wrapperEl[0],\n\n      // Classes\n      classNames: [],\n\n      // Slides\n      slides: $(),\n      slidesGrid: [],\n      snapGrid: [],\n      slidesSizesGrid: [],\n\n      // isDirection\n      isHorizontal() {\n        return swiper.params.direction === 'horizontal';\n      },\n      isVertical() {\n        return swiper.params.direction === 'vertical';\n      },\n      // RTL\n      rtl: (el.dir.toLowerCase() === 'rtl' || $el.css('direction') === 'rtl'),\n      rtlTranslate: swiper.params.direction === 'horizontal' && (el.dir.toLowerCase() === 'rtl' || $el.css('direction') === 'rtl'),\n      wrongRTL: $wrapperEl.css('display') === '-webkit-box',\n\n      // Indexes\n      activeIndex: 0,\n      realIndex: 0,\n\n      //\n      isBeginning: true,\n      isEnd: false,\n\n      // Props\n      translate: 0,\n      previousTranslate: 0,\n      progress: 0,\n      velocity: 0,\n      animating: false,\n\n      // Locks\n      allowSlideNext: swiper.params.allowSlideNext,\n      allowSlidePrev: swiper.params.allowSlidePrev,\n\n      // Touch Events\n      touchEvents: (function touchEvents() {\n        const touch = ['touchstart', 'touchmove', 'touchend', 'touchcancel'];\n        let desktop = ['mousedown', 'mousemove', 'mouseup'];\n        if (Support.pointerEvents) {\n          desktop = ['pointerdown', 'pointermove', 'pointerup'];\n        }\n        swiper.touchEventsTouch = {\n          start: touch[0],\n          move: touch[1],\n          end: touch[2],\n          cancel: touch[3],\n        };\n        swiper.touchEventsDesktop = {\n          start: desktop[0],\n          move: desktop[1],\n          end: desktop[2],\n        };\n        return Support.touch || !swiper.params.simulateTouch ? swiper.touchEventsTouch : swiper.touchEventsDesktop;\n      }()),\n      touchEventsData: {\n        isTouched: undefined,\n        isMoved: undefined,\n        allowTouchCallbacks: undefined,\n        touchStartTime: undefined,\n        isScrolling: undefined,\n        currentTranslate: undefined,\n        startTranslate: undefined,\n        allowThresholdMove: undefined,\n        // Form elements to match\n        formElements: 'input, select, option, textarea, button, video, label',\n        // Last click time\n        lastClickTime: Utils.now(),\n        clickTimeout: undefined,\n        // Velocities\n        velocities: [],\n        allowMomentumBounce: undefined,\n        isTouchEvent: undefined,\n        startMoving: undefined,\n      },\n\n      // Clicks\n      allowClick: true,\n\n      // Touches\n      allowTouchMove: swiper.params.allowTouchMove,\n\n      touches: {\n        startX: 0,\n        startY: 0,\n        currentX: 0,\n        currentY: 0,\n        diff: 0,\n      },\n\n      // Images\n      imagesToLoad: [],\n      imagesLoaded: 0,\n\n    });\n\n    // Install Modules\n    swiper.useModules();\n\n    // Init\n    if (swiper.params.init) {\n      swiper.init();\n    }\n\n    // Return app instance\n    return swiper;\n  }\n\n  slidesPerViewDynamic() {\n    const swiper = this;\n    const {\n      params, slides, slidesGrid, size: swiperSize, activeIndex,\n    } = swiper;\n    let spv = 1;\n    if (params.centeredSlides) {\n      let slideSize = slides[activeIndex].swiperSlideSize;\n      let breakLoop;\n      for (let i = activeIndex + 1; i < slides.length; i += 1) {\n        if (slides[i] && !breakLoop) {\n          slideSize += slides[i].swiperSlideSize;\n          spv += 1;\n          if (slideSize > swiperSize) breakLoop = true;\n        }\n      }\n      for (let i = activeIndex - 1; i >= 0; i -= 1) {\n        if (slides[i] && !breakLoop) {\n          slideSize += slides[i].swiperSlideSize;\n          spv += 1;\n          if (slideSize > swiperSize) breakLoop = true;\n        }\n      }\n    } else {\n      for (let i = activeIndex + 1; i < slides.length; i += 1) {\n        if (slidesGrid[i] - slidesGrid[activeIndex] < swiperSize) {\n          spv += 1;\n        }\n      }\n    }\n    return spv;\n  }\n\n  update() {\n    const swiper = this;\n    if (!swiper || swiper.destroyed) return;\n    const { snapGrid, params } = swiper;\n    // Breakpoints\n    if (params.breakpoints) {\n      swiper.setBreakpoint();\n    }\n    swiper.updateSize();\n    swiper.updateSlides();\n    swiper.updateProgress();\n    swiper.updateSlidesClasses();\n\n    function setTranslate() {\n      const translateValue = swiper.rtlTranslate ? swiper.translate * -1 : swiper.translate;\n      const newTranslate = Math.min(Math.max(translateValue, swiper.maxTranslate()), swiper.minTranslate());\n      swiper.setTranslate(newTranslate);\n      swiper.updateActiveIndex();\n      swiper.updateSlidesClasses();\n    }\n    let translated;\n    if (swiper.params.freeMode) {\n      setTranslate();\n      if (swiper.params.autoHeight) {\n        swiper.updateAutoHeight();\n      }\n    } else {\n      if ((swiper.params.slidesPerView === 'auto' || swiper.params.slidesPerView > 1) && swiper.isEnd && !swiper.params.centeredSlides) {\n        translated = swiper.slideTo(swiper.slides.length - 1, 0, false, true);\n      } else {\n        translated = swiper.slideTo(swiper.activeIndex, 0, false, true);\n      }\n      if (!translated) {\n        setTranslate();\n      }\n    }\n    if (params.watchOverflow && snapGrid !== swiper.snapGrid) {\n      swiper.checkOverflow();\n    }\n    swiper.emit('update');\n  }\n\n  changeDirection(newDirection, needUpdate = true) {\n    const swiper = this;\n    const currentDirection = swiper.params.direction;\n    if (!newDirection) {\n      // eslint-disable-next-line\n      newDirection = currentDirection === 'horizontal' ? 'vertical' : 'horizontal';\n    }\n    if ((newDirection === currentDirection) || (newDirection !== 'horizontal' && newDirection !== 'vertical')) {\n      return swiper;\n    }\n\n    swiper.$el\n      .removeClass(`${swiper.params.containerModifierClass}${currentDirection}`)\n      .addClass(`${swiper.params.containerModifierClass}${newDirection}`);\n\n    swiper.params.direction = newDirection;\n\n    swiper.slides.each((slideIndex, slideEl) => {\n      if (newDirection === 'vertical') {\n        slideEl.style.width = '';\n      } else {\n        slideEl.style.height = '';\n      }\n    });\n\n    swiper.emit('changeDirection');\n    if (needUpdate) swiper.update();\n\n    return swiper;\n  }\n\n  init() {\n    const swiper = this;\n    if (swiper.initialized) return;\n\n    swiper.emit('beforeInit');\n\n    // Set breakpoint\n    if (swiper.params.breakpoints) {\n      swiper.setBreakpoint();\n    }\n\n    // Add Classes\n    swiper.addClasses();\n\n    // Create loop\n    if (swiper.params.loop) {\n      swiper.loopCreate();\n    }\n\n    // Update size\n    swiper.updateSize();\n\n    // Update slides\n    swiper.updateSlides();\n\n    if (swiper.params.watchOverflow) {\n      swiper.checkOverflow();\n    }\n\n    // Set Grab Cursor\n    if (swiper.params.grabCursor) {\n      swiper.setGrabCursor();\n    }\n\n    if (swiper.params.preloadImages) {\n      swiper.preloadImages();\n    }\n\n    // Slide To Initial Slide\n    if (swiper.params.loop) {\n      swiper.slideTo(swiper.params.initialSlide + swiper.loopedSlides, 0, swiper.params.runCallbacksOnInit);\n    } else {\n      swiper.slideTo(swiper.params.initialSlide, 0, swiper.params.runCallbacksOnInit);\n    }\n\n    // Attach events\n    swiper.attachEvents();\n\n    // Init Flag\n    swiper.initialized = true;\n\n    // Emit\n    swiper.emit('init');\n  }\n\n  destroy(deleteInstance = true, cleanStyles = true) {\n    const swiper = this;\n    const {\n      params, $el, $wrapperEl, slides,\n    } = swiper;\n\n    if (typeof swiper.params === 'undefined' || swiper.destroyed) {\n      return null;\n    }\n\n    swiper.emit('beforeDestroy');\n\n    // Init Flag\n    swiper.initialized = false;\n\n    // Detach events\n    swiper.detachEvents();\n\n    // Destroy loop\n    if (params.loop) {\n      swiper.loopDestroy();\n    }\n\n    // Cleanup styles\n    if (cleanStyles) {\n      swiper.removeClasses();\n      $el.removeAttr('style');\n      $wrapperEl.removeAttr('style');\n      if (slides && slides.length) {\n        slides\n          .removeClass([\n            params.slideVisibleClass,\n            params.slideActiveClass,\n            params.slideNextClass,\n            params.slidePrevClass,\n          ].join(' '))\n          .removeAttr('style')\n          .removeAttr('data-swiper-slide-index');\n      }\n    }\n\n    swiper.emit('destroy');\n\n    // Detach emitter events\n    Object.keys(swiper.eventsListeners).forEach((eventName) => {\n      swiper.off(eventName);\n    });\n\n    if (deleteInstance !== false) {\n      swiper.$el[0].swiper = null;\n      swiper.$el.data('swiper', null);\n      Utils.deleteProps(swiper);\n    }\n    swiper.destroyed = true;\n\n    return null;\n  }\n\n  static extendDefaults(newDefaults) {\n    Utils.extend(extendedDefaults, newDefaults);\n  }\n\n  static get extendedDefaults() {\n    return extendedDefaults;\n  }\n\n  static get defaults() {\n    return defaults;\n  }\n\n  static get Class() {\n    return SwiperClass;\n  }\n\n  static get $() {\n    return $;\n  }\n}\n\nvar Device$1 = {\n  name: 'device',\n  proto: {\n    device: Device,\n  },\n  static: {\n    device: Device,\n  },\n};\n\nvar Support$1 = {\n  name: 'support',\n  proto: {\n    support: Support,\n  },\n  static: {\n    support: Support,\n  },\n};\n\nconst Browser = (function Browser() {\n  function isSafari() {\n    const ua = window.navigator.userAgent.toLowerCase();\n    return (ua.indexOf('safari') >= 0 && ua.indexOf('chrome') < 0 && ua.indexOf('android') < 0);\n  }\n  return {\n    isEdge: !!window.navigator.userAgent.match(/Edge/g),\n    isSafari: isSafari(),\n    isUiWebView: /(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(window.navigator.userAgent),\n  };\n}());\n\nvar Browser$1 = {\n  name: 'browser',\n  proto: {\n    browser: Browser,\n  },\n  static: {\n    browser: Browser,\n  },\n};\n\nvar Resize = {\n  name: 'resize',\n  create() {\n    const swiper = this;\n    Utils.extend(swiper, {\n      resize: {\n        resizeHandler() {\n          if (!swiper || swiper.destroyed || !swiper.initialized) return;\n          swiper.emit('beforeResize');\n          swiper.emit('resize');\n        },\n        orientationChangeHandler() {\n          if (!swiper || swiper.destroyed || !swiper.initialized) return;\n          swiper.emit('orientationchange');\n        },\n      },\n    });\n  },\n  on: {\n    init() {\n      const swiper = this;\n      // Emit resize\n      window.addEventListener('resize', swiper.resize.resizeHandler);\n\n      // Emit orientationchange\n      window.addEventListener('orientationchange', swiper.resize.orientationChangeHandler);\n    },\n    destroy() {\n      const swiper = this;\n      window.removeEventListener('resize', swiper.resize.resizeHandler);\n      window.removeEventListener('orientationchange', swiper.resize.orientationChangeHandler);\n    },\n  },\n};\n\nconst Observer = {\n  func: window.MutationObserver || window.WebkitMutationObserver,\n  attach(target, options = {}) {\n    const swiper = this;\n\n    const ObserverFunc = Observer.func;\n    const observer = new ObserverFunc((mutations) => {\n      // The observerUpdate event should only be triggered\n      // once despite the number of mutations.  Additional\n      // triggers are redundant and are very costly\n      if (mutations.length === 1) {\n        swiper.emit('observerUpdate', mutations[0]);\n        return;\n      }\n      const observerUpdate = function observerUpdate() {\n        swiper.emit('observerUpdate', mutations[0]);\n      };\n\n      if (window.requestAnimationFrame) {\n        window.requestAnimationFrame(observerUpdate);\n      } else {\n        window.setTimeout(observerUpdate, 0);\n      }\n    });\n\n    observer.observe(target, {\n      attributes: typeof options.attributes === 'undefined' ? true : options.attributes,\n      childList: typeof options.childList === 'undefined' ? true : options.childList,\n      characterData: typeof options.characterData === 'undefined' ? true : options.characterData,\n    });\n\n    swiper.observer.observers.push(observer);\n  },\n  init() {\n    const swiper = this;\n    if (!Support.observer || !swiper.params.observer) return;\n    if (swiper.params.observeParents) {\n      const containerParents = swiper.$el.parents();\n      for (let i = 0; i < containerParents.length; i += 1) {\n        swiper.observer.attach(containerParents[i]);\n      }\n    }\n    // Observe container\n    swiper.observer.attach(swiper.$el[0], { childList: swiper.params.observeSlideChildren });\n\n    // Observe wrapper\n    swiper.observer.attach(swiper.$wrapperEl[0], { attributes: false });\n  },\n  destroy() {\n    const swiper = this;\n    swiper.observer.observers.forEach((observer) => {\n      observer.disconnect();\n    });\n    swiper.observer.observers = [];\n  },\n};\n\nvar Observer$1 = {\n  name: 'observer',\n  params: {\n    observer: false,\n    observeParents: false,\n    observeSlideChildren: false,\n  },\n  create() {\n    const swiper = this;\n    Utils.extend(swiper, {\n      observer: {\n        init: Observer.init.bind(swiper),\n        attach: Observer.attach.bind(swiper),\n        destroy: Observer.destroy.bind(swiper),\n        observers: [],\n      },\n    });\n  },\n  on: {\n    init() {\n      const swiper = this;\n      swiper.observer.init();\n    },\n    destroy() {\n      const swiper = this;\n      swiper.observer.destroy();\n    },\n  },\n};\n\nconst Virtual = {\n  update(force) {\n    const swiper = this;\n    const { slidesPerView, slidesPerGroup, centeredSlides } = swiper.params;\n    const { addSlidesBefore, addSlidesAfter } = swiper.params.virtual;\n    const {\n      from: previousFrom,\n      to: previousTo,\n      slides,\n      slidesGrid: previousSlidesGrid,\n      renderSlide,\n      offset: previousOffset,\n    } = swiper.virtual;\n    swiper.updateActiveIndex();\n    const activeIndex = swiper.activeIndex || 0;\n\n    let offsetProp;\n    if (swiper.rtlTranslate) offsetProp = 'right';\n    else offsetProp = swiper.isHorizontal() ? 'left' : 'top';\n\n    let slidesAfter;\n    let slidesBefore;\n    if (centeredSlides) {\n      slidesAfter = Math.floor(slidesPerView / 2) + slidesPerGroup + addSlidesBefore;\n      slidesBefore = Math.floor(slidesPerView / 2) + slidesPerGroup + addSlidesAfter;\n    } else {\n      slidesAfter = slidesPerView + (slidesPerGroup - 1) + addSlidesBefore;\n      slidesBefore = slidesPerGroup + addSlidesAfter;\n    }\n    const from = Math.max((activeIndex || 0) - slidesBefore, 0);\n    const to = Math.min((activeIndex || 0) + slidesAfter, slides.length - 1);\n    const offset = (swiper.slidesGrid[from] || 0) - (swiper.slidesGrid[0] || 0);\n\n    Utils.extend(swiper.virtual, {\n      from,\n      to,\n      offset,\n      slidesGrid: swiper.slidesGrid,\n    });\n\n    function onRendered() {\n      swiper.updateSlides();\n      swiper.updateProgress();\n      swiper.updateSlidesClasses();\n      if (swiper.lazy && swiper.params.lazy.enabled) {\n        swiper.lazy.load();\n      }\n    }\n\n    if (previousFrom === from && previousTo === to && !force) {\n      if (swiper.slidesGrid !== previousSlidesGrid && offset !== previousOffset) {\n        swiper.slides.css(offsetProp, `${offset}px`);\n      }\n      swiper.updateProgress();\n      return;\n    }\n    if (swiper.params.virtual.renderExternal) {\n      swiper.params.virtual.renderExternal.call(swiper, {\n        offset,\n        from,\n        to,\n        slides: (function getSlides() {\n          const slidesToRender = [];\n          for (let i = from; i <= to; i += 1) {\n            slidesToRender.push(slides[i]);\n          }\n          return slidesToRender;\n        }()),\n      });\n      onRendered();\n      return;\n    }\n    const prependIndexes = [];\n    const appendIndexes = [];\n    if (force) {\n      swiper.$wrapperEl.find(`.${swiper.params.slideClass}`).remove();\n    } else {\n      for (let i = previousFrom; i <= previousTo; i += 1) {\n        if (i < from || i > to) {\n          swiper.$wrapperEl.find(`.${swiper.params.slideClass}[data-swiper-slide-index=\"${i}\"]`).remove();\n        }\n      }\n    }\n    for (let i = 0; i < slides.length; i += 1) {\n      if (i >= from && i <= to) {\n        if (typeof previousTo === 'undefined' || force) {\n          appendIndexes.push(i);\n        } else {\n          if (i > previousTo) appendIndexes.push(i);\n          if (i < previousFrom) prependIndexes.push(i);\n        }\n      }\n    }\n    appendIndexes.forEach((index) => {\n      swiper.$wrapperEl.append(renderSlide(slides[index], index));\n    });\n    prependIndexes.sort((a, b) => b - a).forEach((index) => {\n      swiper.$wrapperEl.prepend(renderSlide(slides[index], index));\n    });\n    swiper.$wrapperEl.children('.swiper-slide').css(offsetProp, `${offset}px`);\n    onRendered();\n  },\n  renderSlide(slide, index) {\n    const swiper = this;\n    const params = swiper.params.virtual;\n    if (params.cache && swiper.virtual.cache[index]) {\n      return swiper.virtual.cache[index];\n    }\n    const $slideEl = params.renderSlide\n      ? $(params.renderSlide.call(swiper, slide, index))\n      : $(`<div class=\"${swiper.params.slideClass}\" data-swiper-slide-index=\"${index}\">${slide}</div>`);\n    if (!$slideEl.attr('data-swiper-slide-index')) $slideEl.attr('data-swiper-slide-index', index);\n    if (params.cache) swiper.virtual.cache[index] = $slideEl;\n    return $slideEl;\n  },\n  appendSlide(slides) {\n    const swiper = this;\n    if (typeof slides === 'object' && 'length' in slides) {\n      for (let i = 0; i < slides.length; i += 1) {\n        if (slides[i]) swiper.virtual.slides.push(slides[i]);\n      }\n    } else {\n      swiper.virtual.slides.push(slides);\n    }\n    swiper.virtual.update(true);\n  },\n  prependSlide(slides) {\n    const swiper = this;\n    const activeIndex = swiper.activeIndex;\n    let newActiveIndex = activeIndex + 1;\n    let numberOfNewSlides = 1;\n\n    if (Array.isArray(slides)) {\n      for (let i = 0; i < slides.length; i += 1) {\n        if (slides[i]) swiper.virtual.slides.unshift(slides[i]);\n      }\n      newActiveIndex = activeIndex + slides.length;\n      numberOfNewSlides = slides.length;\n    } else {\n      swiper.virtual.slides.unshift(slides);\n    }\n    if (swiper.params.virtual.cache) {\n      const cache = swiper.virtual.cache;\n      const newCache = {};\n      Object.keys(cache).forEach((cachedIndex) => {\n        const $cachedEl = cache[cachedIndex];\n        const cachedElIndex = $cachedEl.attr('data-swiper-slide-index');\n        if (cachedElIndex) {\n          $cachedEl.attr('data-swiper-slide-index', parseInt(cachedElIndex, 10) + 1);\n        }\n        newCache[parseInt(cachedIndex, 10) + numberOfNewSlides] = $cachedEl;\n      });\n      swiper.virtual.cache = newCache;\n    }\n    swiper.virtual.update(true);\n    swiper.slideTo(newActiveIndex, 0);\n  },\n  removeSlide(slidesIndexes) {\n    const swiper = this;\n    if (typeof slidesIndexes === 'undefined' || slidesIndexes === null) return;\n    let activeIndex = swiper.activeIndex;\n    if (Array.isArray(slidesIndexes)) {\n      for (let i = slidesIndexes.length - 1; i >= 0; i -= 1) {\n        swiper.virtual.slides.splice(slidesIndexes[i], 1);\n        if (swiper.params.virtual.cache) {\n          delete swiper.virtual.cache[slidesIndexes[i]];\n        }\n        if (slidesIndexes[i] < activeIndex) activeIndex -= 1;\n        activeIndex = Math.max(activeIndex, 0);\n      }\n    } else {\n      swiper.virtual.slides.splice(slidesIndexes, 1);\n      if (swiper.params.virtual.cache) {\n        delete swiper.virtual.cache[slidesIndexes];\n      }\n      if (slidesIndexes < activeIndex) activeIndex -= 1;\n      activeIndex = Math.max(activeIndex, 0);\n    }\n    swiper.virtual.update(true);\n    swiper.slideTo(activeIndex, 0);\n  },\n  removeAllSlides() {\n    const swiper = this;\n    swiper.virtual.slides = [];\n    if (swiper.params.virtual.cache) {\n      swiper.virtual.cache = {};\n    }\n    swiper.virtual.update(true);\n    swiper.slideTo(0, 0);\n  },\n};\n\nvar Virtual$1 = {\n  name: 'virtual',\n  params: {\n    virtual: {\n      enabled: false,\n      slides: [],\n      cache: true,\n      renderSlide: null,\n      renderExternal: null,\n      addSlidesBefore: 0,\n      addSlidesAfter: 0,\n    },\n  },\n  create() {\n    const swiper = this;\n    Utils.extend(swiper, {\n      virtual: {\n        update: Virtual.update.bind(swiper),\n        appendSlide: Virtual.appendSlide.bind(swiper),\n        prependSlide: Virtual.prependSlide.bind(swiper),\n        removeSlide: Virtual.removeSlide.bind(swiper),\n        removeAllSlides: Virtual.removeAllSlides.bind(swiper),\n        renderSlide: Virtual.renderSlide.bind(swiper),\n        slides: swiper.params.virtual.slides,\n        cache: {},\n      },\n    });\n  },\n  on: {\n    beforeInit() {\n      const swiper = this;\n      if (!swiper.params.virtual.enabled) return;\n      swiper.classNames.push(`${swiper.params.containerModifierClass}virtual`);\n      const overwriteParams = {\n        watchSlidesProgress: true,\n      };\n      Utils.extend(swiper.params, overwriteParams);\n      Utils.extend(swiper.originalParams, overwriteParams);\n\n      if (!swiper.params.initialSlide) {\n        swiper.virtual.update();\n      }\n    },\n    setTranslate() {\n      const swiper = this;\n      if (!swiper.params.virtual.enabled) return;\n      swiper.virtual.update();\n    },\n  },\n};\n\nconst Keyboard = {\n  handle(event) {\n    const swiper = this;\n    const { rtlTranslate: rtl } = swiper;\n    let e = event;\n    if (e.originalEvent) e = e.originalEvent; // jquery fix\n    const kc = e.keyCode || e.charCode;\n    // Directions locks\n    if (!swiper.allowSlideNext && ((swiper.isHorizontal() && kc === 39) || (swiper.isVertical() && kc === 40) || kc === 34)) {\n      return false;\n    }\n    if (!swiper.allowSlidePrev && ((swiper.isHorizontal() && kc === 37) || (swiper.isVertical() && kc === 38) || kc === 33)) {\n      return false;\n    }\n    if (e.shiftKey || e.altKey || e.ctrlKey || e.metaKey) {\n      return undefined;\n    }\n    if (document$1.activeElement && document$1.activeElement.nodeName && (document$1.activeElement.nodeName.toLowerCase() === 'input' || document$1.activeElement.nodeName.toLowerCase() === 'textarea')) {\n      return undefined;\n    }\n    if (swiper.params.keyboard.onlyInViewport && (kc === 33 || kc === 34 || kc === 37 || kc === 39 || kc === 38 || kc === 40)) {\n      let inView = false;\n      // Check that swiper should be inside of visible area of window\n      if (swiper.$el.parents(`.${swiper.params.slideClass}`).length > 0 && swiper.$el.parents(`.${swiper.params.slideActiveClass}`).length === 0) {\n        return undefined;\n      }\n      const windowWidth = window.innerWidth;\n      const windowHeight = window.innerHeight;\n      const swiperOffset = swiper.$el.offset();\n      if (rtl) swiperOffset.left -= swiper.$el[0].scrollLeft;\n      const swiperCoord = [\n        [swiperOffset.left, swiperOffset.top],\n        [swiperOffset.left + swiper.width, swiperOffset.top],\n        [swiperOffset.left, swiperOffset.top + swiper.height],\n        [swiperOffset.left + swiper.width, swiperOffset.top + swiper.height],\n      ];\n      for (let i = 0; i < swiperCoord.length; i += 1) {\n        const point = swiperCoord[i];\n        if (\n          point[0] >= 0 && point[0] <= windowWidth\n          && point[1] >= 0 && point[1] <= windowHeight\n        ) {\n          inView = true;\n        }\n      }\n      if (!inView) return undefined;\n    }\n    if (swiper.isHorizontal()) {\n      if (kc === 33 || kc === 34 || kc === 37 || kc === 39) {\n        if (e.preventDefault) e.preventDefault();\n        else e.returnValue = false;\n      }\n      if (((kc === 34 || kc === 39) && !rtl) || ((kc === 33 || kc === 37) && rtl)) swiper.slideNext();\n      if (((kc === 33 || kc === 37) && !rtl) || ((kc === 34 || kc === 39) && rtl)) swiper.slidePrev();\n    } else {\n      if (kc === 33 || kc === 34 || kc === 38 || kc === 40) {\n        if (e.preventDefault) e.preventDefault();\n        else e.returnValue = false;\n      }\n      if (kc === 34 || kc === 40) swiper.slideNext();\n      if (kc === 33 || kc === 38) swiper.slidePrev();\n    }\n    swiper.emit('keyPress', kc);\n    return undefined;\n  },\n  enable() {\n    const swiper = this;\n    if (swiper.keyboard.enabled) return;\n    $(document$1).on('keydown', swiper.keyboard.handle);\n    swiper.keyboard.enabled = true;\n  },\n  disable() {\n    const swiper = this;\n    if (!swiper.keyboard.enabled) return;\n    $(document$1).off('keydown', swiper.keyboard.handle);\n    swiper.keyboard.enabled = false;\n  },\n};\n\nvar Keyboard$1 = {\n  name: 'keyboard',\n  params: {\n    keyboard: {\n      enabled: false,\n      onlyInViewport: true,\n    },\n  },\n  create() {\n    const swiper = this;\n    Utils.extend(swiper, {\n      keyboard: {\n        enabled: false,\n        enable: Keyboard.enable.bind(swiper),\n        disable: Keyboard.disable.bind(swiper),\n        handle: Keyboard.handle.bind(swiper),\n      },\n    });\n  },\n  on: {\n    init() {\n      const swiper = this;\n      if (swiper.params.keyboard.enabled) {\n        swiper.keyboard.enable();\n      }\n    },\n    destroy() {\n      const swiper = this;\n      if (swiper.keyboard.enabled) {\n        swiper.keyboard.disable();\n      }\n    },\n  },\n};\n\nfunction isEventSupported() {\n  const eventName = 'onwheel';\n  let isSupported = eventName in document$1;\n\n  if (!isSupported) {\n    const element = document$1.createElement('div');\n    element.setAttribute(eventName, 'return;');\n    isSupported = typeof element[eventName] === 'function';\n  }\n\n  if (!isSupported\n    && document$1.implementation\n    && document$1.implementation.hasFeature\n    // always returns true in newer browsers as per the standard.\n    // @see http://dom.spec.whatwg.org/#dom-domimplementation-hasfeature\n    && document$1.implementation.hasFeature('', '') !== true\n  ) {\n    // This is the only way to test support for the `wheel` event in IE9+.\n    isSupported = document$1.implementation.hasFeature('Events.wheel', '3.0');\n  }\n\n  return isSupported;\n}\nconst Mousewheel = {\n  lastScrollTime: Utils.now(),\n  lastEventBeforeSnap: undefined,\n  recentWheelEvents: [],\n  event() {\n    if (window.navigator.userAgent.indexOf('firefox') > -1) return 'DOMMouseScroll';\n    return isEventSupported() ? 'wheel' : 'mousewheel';\n  },\n  normalize(e) {\n    // Reasonable defaults\n    const PIXEL_STEP = 10;\n    const LINE_HEIGHT = 40;\n    const PAGE_HEIGHT = 800;\n\n    let sX = 0;\n    let sY = 0; // spinX, spinY\n    let pX = 0;\n    let pY = 0; // pixelX, pixelY\n\n    // Legacy\n    if ('detail' in e) {\n      sY = e.detail;\n    }\n    if ('wheelDelta' in e) {\n      sY = -e.wheelDelta / 120;\n    }\n    if ('wheelDeltaY' in e) {\n      sY = -e.wheelDeltaY / 120;\n    }\n    if ('wheelDeltaX' in e) {\n      sX = -e.wheelDeltaX / 120;\n    }\n\n    // side scrolling on FF with DOMMouseScroll\n    if ('axis' in e && e.axis === e.HORIZONTAL_AXIS) {\n      sX = sY;\n      sY = 0;\n    }\n\n    pX = sX * PIXEL_STEP;\n    pY = sY * PIXEL_STEP;\n\n    if ('deltaY' in e) {\n      pY = e.deltaY;\n    }\n    if ('deltaX' in e) {\n      pX = e.deltaX;\n    }\n\n    if (e.shiftKey && !pX) { // if user scrolls with shift he wants horizontal scroll\n      pX = pY;\n      pY = 0;\n    }\n\n    if ((pX || pY) && e.deltaMode) {\n      if (e.deltaMode === 1) { // delta in LINE units\n        pX *= LINE_HEIGHT;\n        pY *= LINE_HEIGHT;\n      } else { // delta in PAGE units\n        pX *= PAGE_HEIGHT;\n        pY *= PAGE_HEIGHT;\n      }\n    }\n\n    // Fall-back if spin cannot be determined\n    if (pX && !sX) {\n      sX = (pX < 1) ? -1 : 1;\n    }\n    if (pY && !sY) {\n      sY = (pY < 1) ? -1 : 1;\n    }\n\n    return {\n      spinX: sX,\n      spinY: sY,\n      pixelX: pX,\n      pixelY: pY,\n    };\n  },\n  handleMouseEnter() {\n    const swiper = this;\n    swiper.mouseEntered = true;\n  },\n  handleMouseLeave() {\n    const swiper = this;\n    swiper.mouseEntered = false;\n  },\n  handle(event) {\n    let e = event;\n    const swiper = this;\n    const params = swiper.params.mousewheel;\n\n    if (swiper.params.cssMode) {\n      e.preventDefault();\n    }\n\n    let target = swiper.$el;\n    if (swiper.params.mousewheel.eventsTarged !== 'container') {\n      target = $(swiper.params.mousewheel.eventsTarged);\n    }\n    if (!swiper.mouseEntered && !target[0].contains(e.target) && !params.releaseOnEdges) return true;\n\n    if (e.originalEvent) e = e.originalEvent; // jquery fix\n    let delta = 0;\n    const rtlFactor = swiper.rtlTranslate ? -1 : 1;\n\n    const data = Mousewheel.normalize(e);\n\n    if (params.forceToAxis) {\n      if (swiper.isHorizontal()) {\n        if (Math.abs(data.pixelX) > Math.abs(data.pixelY)) delta = data.pixelX * rtlFactor;\n        else return true;\n      } else if (Math.abs(data.pixelY) > Math.abs(data.pixelX)) delta = data.pixelY;\n      else return true;\n    } else {\n      delta = Math.abs(data.pixelX) > Math.abs(data.pixelY) ? -data.pixelX * rtlFactor : -data.pixelY;\n    }\n\n    if (delta === 0) return true;\n\n    if (params.invert) delta = -delta;\n\n    if (!swiper.params.freeMode) {\n      // Register the new event in a variable which stores the relevant data\n      const newEvent = {\n        time: Utils.now(),\n        delta: Math.abs(delta),\n        direction: Math.sign(delta),\n        raw: event,\n      };\n\n      // Keep the most recent events\n      const recentWheelEvents = swiper.mousewheel.recentWheelEvents;\n      if (recentWheelEvents.length >= 2) {\n        recentWheelEvents.shift(); // only store the last N events\n      }\n      const prevEvent = recentWheelEvents.length ? recentWheelEvents[recentWheelEvents.length - 1] : undefined;\n      recentWheelEvents.push(newEvent);\n\n      // If there is at least one previous recorded event:\n      //   If direction has changed or\n      //   if the scroll is quicker than the previous one:\n      //     Animate the slider.\n      // Else (this is the first time the wheel is moved):\n      //     Animate the slider.\n      if (prevEvent) {\n        if (newEvent.direction !== prevEvent.direction || newEvent.delta > prevEvent.delta || newEvent.time > prevEvent.time + 150) {\n          swiper.mousewheel.animateSlider(newEvent);\n        }\n      } else {\n        swiper.mousewheel.animateSlider(newEvent);\n      }\n\n      // If it's time to release the scroll:\n      //   Return now so you don't hit the preventDefault.\n      if (swiper.mousewheel.releaseScroll(newEvent)) {\n        return true;\n      }\n    } else {\n      // Freemode or scrollContainer:\n\n      // If we recently snapped after a momentum scroll, then ignore wheel events\n      // to give time for the deceleration to finish. Stop ignoring after 500 msecs\n      // or if it's a new scroll (larger delta or inverse sign as last event before\n      // an end-of-momentum snap).\n      const newEvent = { time: Utils.now(), delta: Math.abs(delta), direction: Math.sign(delta) };\n      const { lastEventBeforeSnap } = swiper.mousewheel;\n      const ignoreWheelEvents = lastEventBeforeSnap\n        && newEvent.time < lastEventBeforeSnap.time + 500\n        && newEvent.delta <= lastEventBeforeSnap.delta\n        && newEvent.direction === lastEventBeforeSnap.direction;\n      if (!ignoreWheelEvents) {\n        swiper.mousewheel.lastEventBeforeSnap = undefined;\n\n        if (swiper.params.loop) {\n          swiper.loopFix();\n        }\n        let position = swiper.getTranslate() + (delta * params.sensitivity);\n        const wasBeginning = swiper.isBeginning;\n        const wasEnd = swiper.isEnd;\n\n        if (position >= swiper.minTranslate()) position = swiper.minTranslate();\n        if (position <= swiper.maxTranslate()) position = swiper.maxTranslate();\n\n        swiper.setTransition(0);\n        swiper.setTranslate(position);\n        swiper.updateProgress();\n        swiper.updateActiveIndex();\n        swiper.updateSlidesClasses();\n\n        if ((!wasBeginning && swiper.isBeginning) || (!wasEnd && swiper.isEnd)) {\n          swiper.updateSlidesClasses();\n        }\n\n        if (swiper.params.freeModeSticky) {\n          // When wheel scrolling starts with sticky (aka snap) enabled, then detect\n          // the end of a momentum scroll by storing recent (N=15?) wheel events.\n          // 1. do all N events have decreasing or same (absolute value) delta?\n          // 2. did all N events arrive in the last M (M=500?) msecs?\n          // 3. does the earliest event have an (absolute value) delta that's\n          //    at least P (P=1?) larger than the most recent event's delta?\n          // 4. does the latest event have a delta that's smaller than Q (Q=6?) pixels?\n          // If 1-4 are \"yes\" then we're near the end of a momuntum scroll deceleration.\n          // Snap immediately and ignore remaining wheel events in this scroll.\n          // See comment above for \"remaining wheel events in this scroll\" determination.\n          // If 1-4 aren't satisfied, then wait to snap until 500ms after the last event.\n          clearTimeout(swiper.mousewheel.timeout);\n          swiper.mousewheel.timeout = undefined;\n          const recentWheelEvents = swiper.mousewheel.recentWheelEvents;\n          if (recentWheelEvents.length >= 15) {\n            recentWheelEvents.shift(); // only store the last N events\n          }\n          const prevEvent = recentWheelEvents.length ? recentWheelEvents[recentWheelEvents.length - 1] : undefined;\n          const firstEvent = recentWheelEvents[0];\n          recentWheelEvents.push(newEvent);\n          if (prevEvent && (newEvent.delta > prevEvent.delta || newEvent.direction !== prevEvent.direction)) {\n            // Increasing or reverse-sign delta means the user started scrolling again. Clear the wheel event log.\n            recentWheelEvents.splice(0);\n          } else if (recentWheelEvents.length >= 15\n              && newEvent.time - firstEvent.time < 500\n              && firstEvent.delta - newEvent.delta >= 1\n              && newEvent.delta <= 6\n          ) {\n            // We're at the end of the deceleration of a momentum scroll, so there's no need\n            // to wait for more events. Snap ASAP on the next tick.\n            // Also, because there's some remaining momentum we'll bias the snap in the\n            // direction of the ongoing scroll because it's better UX for the scroll to snap\n            // in the same direction as the scroll instead of reversing to snap.  Therefore,\n            // if it's already scrolled more than 20% in the current direction, keep going.\n            const snapToThreshold = delta > 0 ? 0.8 : 0.2;\n            swiper.mousewheel.lastEventBeforeSnap = newEvent;\n            recentWheelEvents.splice(0);\n            swiper.mousewheel.timeout = Utils.nextTick(() => {\n              swiper.slideToClosest(swiper.params.speed, true, undefined, snapToThreshold);\n            }, 0); // no delay; move on next tick\n          }\n          if (!swiper.mousewheel.timeout) {\n            // if we get here, then we haven't detected the end of a momentum scroll, so\n            // we'll consider a scroll \"complete\" when there haven't been any wheel events\n            // for 500ms.\n            swiper.mousewheel.timeout = Utils.nextTick(() => {\n              const snapToThreshold = 0.5;\n              swiper.mousewheel.lastEventBeforeSnap = newEvent;\n              recentWheelEvents.splice(0);\n              swiper.slideToClosest(swiper.params.speed, true, undefined, snapToThreshold);\n            }, 500);\n          }\n        }\n\n        // Emit event\n        if (!ignoreWheelEvents) swiper.emit('scroll', e);\n\n        // Stop autoplay\n        if (swiper.params.autoplay && swiper.params.autoplayDisableOnInteraction) swiper.autoplay.stop();\n        // Return page scroll on edge positions\n        if (position === swiper.minTranslate() || position === swiper.maxTranslate()) return true;\n      }\n    }\n\n    if (e.preventDefault) e.preventDefault();\n    else e.returnValue = false;\n    return false;\n  },\n  animateSlider(newEvent) {\n    const swiper = this;\n    // If the movement is NOT big enough and\n    // if the last time the user scrolled was too close to the current one (avoid continuously triggering the slider):\n    //   Don't go any further (avoid insignificant scroll movement).\n    if (newEvent.delta >= 6 && Utils.now() - swiper.mousewheel.lastScrollTime < 60) {\n      // Return false as a default\n      return true;\n    }\n    // If user is scrolling towards the end:\n    //   If the slider hasn't hit the latest slide or\n    //   if the slider is a loop and\n    //   if the slider isn't moving right now:\n    //     Go to next slide and\n    //     emit a scroll event.\n    // Else (the user is scrolling towards the beginning) and\n    // if the slider hasn't hit the first slide or\n    // if the slider is a loop and\n    // if the slider isn't moving right now:\n    //   Go to prev slide and\n    //   emit a scroll event.\n    if (newEvent.direction < 0) {\n      if ((!swiper.isEnd || swiper.params.loop) && !swiper.animating) {\n        swiper.slideNext();\n        swiper.emit('scroll', newEvent.raw);\n      }\n    } else if ((!swiper.isBeginning || swiper.params.loop) && !swiper.animating) {\n      swiper.slidePrev();\n      swiper.emit('scroll', newEvent.raw);\n    }\n    // If you got here is because an animation has been triggered so store the current time\n    swiper.mousewheel.lastScrollTime = (new window.Date()).getTime();\n    // Return false as a default\n    return false;\n  },\n  releaseScroll(newEvent) {\n    const swiper = this;\n    const params = swiper.params.mousewheel;\n    if (newEvent.direction < 0) {\n      if (swiper.isEnd && !swiper.params.loop && params.releaseOnEdges) {\n        // Return true to animate scroll on edges\n        return true;\n      }\n    } else if (swiper.isBeginning && !swiper.params.loop && params.releaseOnEdges) {\n      // Return true to animate scroll on edges\n      return true;\n    }\n    return false;\n  },\n  enable() {\n    const swiper = this;\n    const event = Mousewheel.event();\n    if (swiper.params.cssMode) {\n      swiper.wrapperEl.removeEventListener(event, swiper.mousewheel.handle);\n      return true;\n    }\n    if (!event) return false;\n    if (swiper.mousewheel.enabled) return false;\n    let target = swiper.$el;\n    if (swiper.params.mousewheel.eventsTarged !== 'container') {\n      target = $(swiper.params.mousewheel.eventsTarged);\n    }\n    target.on('mouseenter', swiper.mousewheel.handleMouseEnter);\n    target.on('mouseleave', swiper.mousewheel.handleMouseLeave);\n    target.on(event, swiper.mousewheel.handle);\n    swiper.mousewheel.enabled = true;\n    return true;\n  },\n  disable() {\n    const swiper = this;\n    const event = Mousewheel.event();\n    if (swiper.params.cssMode) {\n      swiper.wrapperEl.addEventListener(event, swiper.mousewheel.handle);\n      return true;\n    }\n    if (!event) return false;\n    if (!swiper.mousewheel.enabled) return false;\n    let target = swiper.$el;\n    if (swiper.params.mousewheel.eventsTarged !== 'container') {\n      target = $(swiper.params.mousewheel.eventsTarged);\n    }\n    target.off(event, swiper.mousewheel.handle);\n    swiper.mousewheel.enabled = false;\n    return true;\n  },\n};\n\nvar Mousewheel$1 = {\n  name: 'mousewheel',\n  params: {\n    mousewheel: {\n      enabled: false,\n      releaseOnEdges: false,\n      invert: false,\n      forceToAxis: false,\n      sensitivity: 1,\n      eventsTarged: 'container',\n    },\n  },\n  create() {\n    const swiper = this;\n    Utils.extend(swiper, {\n      mousewheel: {\n        enabled: false,\n        enable: Mousewheel.enable.bind(swiper),\n        disable: Mousewheel.disable.bind(swiper),\n        handle: Mousewheel.handle.bind(swiper),\n        handleMouseEnter: Mousewheel.handleMouseEnter.bind(swiper),\n        handleMouseLeave: Mousewheel.handleMouseLeave.bind(swiper),\n        animateSlider: Mousewheel.animateSlider.bind(swiper),\n        releaseScroll: Mousewheel.releaseScroll.bind(swiper),\n        lastScrollTime: Utils.now(),\n        lastEventBeforeSnap: undefined,\n        recentWheelEvents: [],\n      },\n    });\n  },\n  on: {\n    init() {\n      const swiper = this;\n      if (!swiper.params.mousewheel.enabled && swiper.params.cssMode) {\n        swiper.mousewheel.disable();\n      }\n      if (swiper.params.mousewheel.enabled) swiper.mousewheel.enable();\n    },\n    destroy() {\n      const swiper = this;\n      if (swiper.params.cssMode) {\n        swiper.mousewheel.enable();\n      }\n      if (swiper.mousewheel.enabled) swiper.mousewheel.disable();\n    },\n  },\n};\n\nconst Navigation = {\n  update() {\n    // Update Navigation Buttons\n    const swiper = this;\n    const params = swiper.params.navigation;\n\n    if (swiper.params.loop) return;\n    const { $nextEl, $prevEl } = swiper.navigation;\n\n    if ($prevEl && $prevEl.length > 0) {\n      if (swiper.isBeginning) {\n        $prevEl.addClass(params.disabledClass);\n      } else {\n        $prevEl.removeClass(params.disabledClass);\n      }\n      $prevEl[swiper.params.watchOverflow && swiper.isLocked ? 'addClass' : 'removeClass'](params.lockClass);\n    }\n    if ($nextEl && $nextEl.length > 0) {\n      if (swiper.isEnd) {\n        $nextEl.addClass(params.disabledClass);\n      } else {\n        $nextEl.removeClass(params.disabledClass);\n      }\n      $nextEl[swiper.params.watchOverflow && swiper.isLocked ? 'addClass' : 'removeClass'](params.lockClass);\n    }\n  },\n  onPrevClick(e) {\n    const swiper = this;\n    e.preventDefault();\n    if (swiper.isBeginning && !swiper.params.loop) return;\n    swiper.slidePrev();\n  },\n  onNextClick(e) {\n    const swiper = this;\n    e.preventDefault();\n    if (swiper.isEnd && !swiper.params.loop) return;\n    swiper.slideNext();\n  },\n  init() {\n    const swiper = this;\n    const params = swiper.params.navigation;\n    if (!(params.nextEl || params.prevEl)) return;\n\n    let $nextEl;\n    let $prevEl;\n    if (params.nextEl) {\n      $nextEl = $(params.nextEl);\n      if (\n        swiper.params.uniqueNavElements\n        && typeof params.nextEl === 'string'\n        && $nextEl.length > 1\n        && swiper.$el.find(params.nextEl).length === 1\n      ) {\n        $nextEl = swiper.$el.find(params.nextEl);\n      }\n    }\n    if (params.prevEl) {\n      $prevEl = $(params.prevEl);\n      if (\n        swiper.params.uniqueNavElements\n        && typeof params.prevEl === 'string'\n        && $prevEl.length > 1\n        && swiper.$el.find(params.prevEl).length === 1\n      ) {\n        $prevEl = swiper.$el.find(params.prevEl);\n      }\n    }\n\n    if ($nextEl && $nextEl.length > 0) {\n      $nextEl.on('click', swiper.navigation.onNextClick);\n    }\n    if ($prevEl && $prevEl.length > 0) {\n      $prevEl.on('click', swiper.navigation.onPrevClick);\n    }\n\n    Utils.extend(swiper.navigation, {\n      $nextEl,\n      nextEl: $nextEl && $nextEl[0],\n      $prevEl,\n      prevEl: $prevEl && $prevEl[0],\n    });\n  },\n  destroy() {\n    const swiper = this;\n    const { $nextEl, $prevEl } = swiper.navigation;\n    if ($nextEl && $nextEl.length) {\n      $nextEl.off('click', swiper.navigation.onNextClick);\n      $nextEl.removeClass(swiper.params.navigation.disabledClass);\n    }\n    if ($prevEl && $prevEl.length) {\n      $prevEl.off('click', swiper.navigation.onPrevClick);\n      $prevEl.removeClass(swiper.params.navigation.disabledClass);\n    }\n  },\n};\n\nvar Navigation$1 = {\n  name: 'navigation',\n  params: {\n    navigation: {\n      nextEl: null,\n      prevEl: null,\n\n      hideOnClick: false,\n      disabledClass: 'swiper-button-disabled',\n      hiddenClass: 'swiper-button-hidden',\n      lockClass: 'swiper-button-lock',\n    },\n  },\n  create() {\n    const swiper = this;\n    Utils.extend(swiper, {\n      navigation: {\n        init: Navigation.init.bind(swiper),\n        update: Navigation.update.bind(swiper),\n        destroy: Navigation.destroy.bind(swiper),\n        onNextClick: Navigation.onNextClick.bind(swiper),\n        onPrevClick: Navigation.onPrevClick.bind(swiper),\n      },\n    });\n  },\n  on: {\n    init() {\n      const swiper = this;\n      swiper.navigation.init();\n      swiper.navigation.update();\n    },\n    toEdge() {\n      const swiper = this;\n      swiper.navigation.update();\n    },\n    fromEdge() {\n      const swiper = this;\n      swiper.navigation.update();\n    },\n    destroy() {\n      const swiper = this;\n      swiper.navigation.destroy();\n    },\n    click(e) {\n      const swiper = this;\n      const { $nextEl, $prevEl } = swiper.navigation;\n      if (\n        swiper.params.navigation.hideOnClick\n        && !$(e.target).is($prevEl)\n        && !$(e.target).is($nextEl)\n      ) {\n        let isHidden;\n        if ($nextEl) {\n          isHidden = $nextEl.hasClass(swiper.params.navigation.hiddenClass);\n        } else if ($prevEl) {\n          isHidden = $prevEl.hasClass(swiper.params.navigation.hiddenClass);\n        }\n        if (isHidden === true) {\n          swiper.emit('navigationShow', swiper);\n        } else {\n          swiper.emit('navigationHide', swiper);\n        }\n        if ($nextEl) {\n          $nextEl.toggleClass(swiper.params.navigation.hiddenClass);\n        }\n        if ($prevEl) {\n          $prevEl.toggleClass(swiper.params.navigation.hiddenClass);\n        }\n      }\n    },\n  },\n};\n\nconst Pagination = {\n  update() {\n    // Render || Update Pagination bullets/items\n    const swiper = this;\n    const rtl = swiper.rtl;\n    const params = swiper.params.pagination;\n    if (!params.el || !swiper.pagination.el || !swiper.pagination.$el || swiper.pagination.$el.length === 0) return;\n    const slidesLength = swiper.virtual && swiper.params.virtual.enabled ? swiper.virtual.slides.length : swiper.slides.length;\n    const $el = swiper.pagination.$el;\n    // Current/Total\n    let current;\n    const total = swiper.params.loop ? Math.ceil((slidesLength - (swiper.loopedSlides * 2)) / swiper.params.slidesPerGroup) : swiper.snapGrid.length;\n    if (swiper.params.loop) {\n      current = Math.ceil((swiper.activeIndex - swiper.loopedSlides) / swiper.params.slidesPerGroup);\n      if (current > slidesLength - 1 - (swiper.loopedSlides * 2)) {\n        current -= (slidesLength - (swiper.loopedSlides * 2));\n      }\n      if (current > total - 1) current -= total;\n      if (current < 0 && swiper.params.paginationType !== 'bullets') current = total + current;\n    } else if (typeof swiper.snapIndex !== 'undefined') {\n      current = swiper.snapIndex;\n    } else {\n      current = swiper.activeIndex || 0;\n    }\n    // Types\n    if (params.type === 'bullets' && swiper.pagination.bullets && swiper.pagination.bullets.length > 0) {\n      const bullets = swiper.pagination.bullets;\n      let firstIndex;\n      let lastIndex;\n      let midIndex;\n      if (params.dynamicBullets) {\n        swiper.pagination.bulletSize = bullets.eq(0)[swiper.isHorizontal() ? 'outerWidth' : 'outerHeight'](true);\n        $el.css(swiper.isHorizontal() ? 'width' : 'height', `${swiper.pagination.bulletSize * (params.dynamicMainBullets + 4)}px`);\n        if (params.dynamicMainBullets > 1 && swiper.previousIndex !== undefined) {\n          swiper.pagination.dynamicBulletIndex += (current - swiper.previousIndex);\n          if (swiper.pagination.dynamicBulletIndex > (params.dynamicMainBullets - 1)) {\n            swiper.pagination.dynamicBulletIndex = params.dynamicMainBullets - 1;\n          } else if (swiper.pagination.dynamicBulletIndex < 0) {\n            swiper.pagination.dynamicBulletIndex = 0;\n          }\n        }\n        firstIndex = current - swiper.pagination.dynamicBulletIndex;\n        lastIndex = firstIndex + (Math.min(bullets.length, params.dynamicMainBullets) - 1);\n        midIndex = (lastIndex + firstIndex) / 2;\n      }\n      bullets.removeClass(`${params.bulletActiveClass} ${params.bulletActiveClass}-next ${params.bulletActiveClass}-next-next ${params.bulletActiveClass}-prev ${params.bulletActiveClass}-prev-prev ${params.bulletActiveClass}-main`);\n      if ($el.length > 1) {\n        bullets.each((index, bullet) => {\n          const $bullet = $(bullet);\n          const bulletIndex = $bullet.index();\n          if (bulletIndex === current) {\n            $bullet.addClass(params.bulletActiveClass);\n          }\n          if (params.dynamicBullets) {\n            if (bulletIndex >= firstIndex && bulletIndex <= lastIndex) {\n              $bullet.addClass(`${params.bulletActiveClass}-main`);\n            }\n            if (bulletIndex === firstIndex) {\n              $bullet\n                .prev()\n                .addClass(`${params.bulletActiveClass}-prev`)\n                .prev()\n                .addClass(`${params.bulletActiveClass}-prev-prev`);\n            }\n            if (bulletIndex === lastIndex) {\n              $bullet\n                .next()\n                .addClass(`${params.bulletActiveClass}-next`)\n                .next()\n                .addClass(`${params.bulletActiveClass}-next-next`);\n            }\n          }\n        });\n      } else {\n        const $bullet = bullets.eq(current);\n        const bulletIndex = $bullet.index();\n        $bullet.addClass(params.bulletActiveClass);\n        if (params.dynamicBullets) {\n          const $firstDisplayedBullet = bullets.eq(firstIndex);\n          const $lastDisplayedBullet = bullets.eq(lastIndex);\n          for (let i = firstIndex; i <= lastIndex; i += 1) {\n            bullets.eq(i).addClass(`${params.bulletActiveClass}-main`);\n          }\n          if (swiper.params.loop) {\n            if (bulletIndex >= bullets.length - params.dynamicMainBullets) {\n              for (let i = params.dynamicMainBullets; i >= 0; i -= 1) {\n                bullets.eq(bullets.length - i).addClass(`${params.bulletActiveClass}-main`);\n              }\n              bullets.eq(bullets.length - params.dynamicMainBullets - 1).addClass(`${params.bulletActiveClass}-prev`);\n            } else {\n              $firstDisplayedBullet\n                .prev()\n                .addClass(`${params.bulletActiveClass}-prev`)\n                .prev()\n                .addClass(`${params.bulletActiveClass}-prev-prev`);\n              $lastDisplayedBullet\n                .next()\n                .addClass(`${params.bulletActiveClass}-next`)\n                .next()\n                .addClass(`${params.bulletActiveClass}-next-next`);\n            }\n          } else {\n            $firstDisplayedBullet\n              .prev()\n              .addClass(`${params.bulletActiveClass}-prev`)\n              .prev()\n              .addClass(`${params.bulletActiveClass}-prev-prev`);\n            $lastDisplayedBullet\n              .next()\n              .addClass(`${params.bulletActiveClass}-next`)\n              .next()\n              .addClass(`${params.bulletActiveClass}-next-next`);\n          }\n        }\n      }\n      if (params.dynamicBullets) {\n        const dynamicBulletsLength = Math.min(bullets.length, params.dynamicMainBullets + 4);\n        const bulletsOffset = (((swiper.pagination.bulletSize * dynamicBulletsLength) - (swiper.pagination.bulletSize)) / 2) - (midIndex * swiper.pagination.bulletSize);\n        const offsetProp = rtl ? 'right' : 'left';\n        bullets.css(swiper.isHorizontal() ? offsetProp : 'top', `${bulletsOffset}px`);\n      }\n    }\n    if (params.type === 'fraction') {\n      $el.find(`.${params.currentClass}`).text(params.formatFractionCurrent(current + 1));\n      $el.find(`.${params.totalClass}`).text(params.formatFractionTotal(total));\n    }\n    if (params.type === 'progressbar') {\n      let progressbarDirection;\n      if (params.progressbarOpposite) {\n        progressbarDirection = swiper.isHorizontal() ? 'vertical' : 'horizontal';\n      } else {\n        progressbarDirection = swiper.isHorizontal() ? 'horizontal' : 'vertical';\n      }\n      const scale = (current + 1) / total;\n      let scaleX = 1;\n      let scaleY = 1;\n      if (progressbarDirection === 'horizontal') {\n        scaleX = scale;\n      } else {\n        scaleY = scale;\n      }\n      $el.find(`.${params.progressbarFillClass}`).transform(`translate3d(0,0,0) scaleX(${scaleX}) scaleY(${scaleY})`).transition(swiper.params.speed);\n    }\n    if (params.type === 'custom' && params.renderCustom) {\n      $el.html(params.renderCustom(swiper, current + 1, total));\n      swiper.emit('paginationRender', swiper, $el[0]);\n    } else {\n      swiper.emit('paginationUpdate', swiper, $el[0]);\n    }\n    $el[swiper.params.watchOverflow && swiper.isLocked ? 'addClass' : 'removeClass'](params.lockClass);\n  },\n  render() {\n    // Render Container\n    const swiper = this;\n    const params = swiper.params.pagination;\n    if (!params.el || !swiper.pagination.el || !swiper.pagination.$el || swiper.pagination.$el.length === 0) return;\n    const slidesLength = swiper.virtual && swiper.params.virtual.enabled ? swiper.virtual.slides.length : swiper.slides.length;\n\n    const $el = swiper.pagination.$el;\n    let paginationHTML = '';\n    if (params.type === 'bullets') {\n      const numberOfBullets = swiper.params.loop ? Math.ceil((slidesLength - (swiper.loopedSlides * 2)) / swiper.params.slidesPerGroup) : swiper.snapGrid.length;\n      for (let i = 0; i < numberOfBullets; i += 1) {\n        if (params.renderBullet) {\n          paginationHTML += params.renderBullet.call(swiper, i, params.bulletClass);\n        } else {\n          paginationHTML += `<${params.bulletElement} class=\"${params.bulletClass}\"></${params.bulletElement}>`;\n        }\n      }\n      $el.html(paginationHTML);\n      swiper.pagination.bullets = $el.find(`.${params.bulletClass}`);\n    }\n    if (params.type === 'fraction') {\n      if (params.renderFraction) {\n        paginationHTML = params.renderFraction.call(swiper, params.currentClass, params.totalClass);\n      } else {\n        paginationHTML = `<span class=\"${params.currentClass}\"></span>`\n        + ' / '\n        + `<span class=\"${params.totalClass}\"></span>`;\n      }\n      $el.html(paginationHTML);\n    }\n    if (params.type === 'progressbar') {\n      if (params.renderProgressbar) {\n        paginationHTML = params.renderProgressbar.call(swiper, params.progressbarFillClass);\n      } else {\n        paginationHTML = `<span class=\"${params.progressbarFillClass}\"></span>`;\n      }\n      $el.html(paginationHTML);\n    }\n    if (params.type !== 'custom') {\n      swiper.emit('paginationRender', swiper.pagination.$el[0]);\n    }\n  },\n  init() {\n    const swiper = this;\n    const params = swiper.params.pagination;\n    if (!params.el) return;\n\n    let $el = $(params.el);\n    if ($el.length === 0) return;\n\n    if (\n      swiper.params.uniqueNavElements\n      && typeof params.el === 'string'\n      && $el.length > 1\n      && swiper.$el.find(params.el).length === 1\n    ) {\n      $el = swiper.$el.find(params.el);\n    }\n\n    if (params.type === 'bullets' && params.clickable) {\n      $el.addClass(params.clickableClass);\n    }\n\n    $el.addClass(params.modifierClass + params.type);\n\n    if (params.type === 'bullets' && params.dynamicBullets) {\n      $el.addClass(`${params.modifierClass}${params.type}-dynamic`);\n      swiper.pagination.dynamicBulletIndex = 0;\n      if (params.dynamicMainBullets < 1) {\n        params.dynamicMainBullets = 1;\n      }\n    }\n    if (params.type === 'progressbar' && params.progressbarOpposite) {\n      $el.addClass(params.progressbarOppositeClass);\n    }\n\n    if (params.clickable) {\n      $el.on('click', `.${params.bulletClass}`, function onClick(e) {\n        e.preventDefault();\n        let index = $(this).index() * swiper.params.slidesPerGroup;\n        if (swiper.params.loop) index += swiper.loopedSlides;\n        swiper.slideTo(index);\n      });\n    }\n\n    Utils.extend(swiper.pagination, {\n      $el,\n      el: $el[0],\n    });\n  },\n  destroy() {\n    const swiper = this;\n    const params = swiper.params.pagination;\n    if (!params.el || !swiper.pagination.el || !swiper.pagination.$el || swiper.pagination.$el.length === 0) return;\n    const $el = swiper.pagination.$el;\n\n    $el.removeClass(params.hiddenClass);\n    $el.removeClass(params.modifierClass + params.type);\n    if (swiper.pagination.bullets) swiper.pagination.bullets.removeClass(params.bulletActiveClass);\n    if (params.clickable) {\n      $el.off('click', `.${params.bulletClass}`);\n    }\n  },\n};\n\nvar Pagination$1 = {\n  name: 'pagination',\n  params: {\n    pagination: {\n      el: null,\n      bulletElement: 'span',\n      clickable: false,\n      hideOnClick: false,\n      renderBullet: null,\n      renderProgressbar: null,\n      renderFraction: null,\n      renderCustom: null,\n      progressbarOpposite: false,\n      type: 'bullets', // 'bullets' or 'progressbar' or 'fraction' or 'custom'\n      dynamicBullets: false,\n      dynamicMainBullets: 1,\n      formatFractionCurrent: (number) => number,\n      formatFractionTotal: (number) => number,\n      bulletClass: 'swiper-pagination-bullet',\n      bulletActiveClass: 'swiper-pagination-bullet-active',\n      modifierClass: 'swiper-pagination-', // NEW\n      currentClass: 'swiper-pagination-current',\n      totalClass: 'swiper-pagination-total',\n      hiddenClass: 'swiper-pagination-hidden',\n      progressbarFillClass: 'swiper-pagination-progressbar-fill',\n      progressbarOppositeClass: 'swiper-pagination-progressbar-opposite',\n      clickableClass: 'swiper-pagination-clickable', // NEW\n      lockClass: 'swiper-pagination-lock',\n    },\n  },\n  create() {\n    const swiper = this;\n    Utils.extend(swiper, {\n      pagination: {\n        init: Pagination.init.bind(swiper),\n        render: Pagination.render.bind(swiper),\n        update: Pagination.update.bind(swiper),\n        destroy: Pagination.destroy.bind(swiper),\n        dynamicBulletIndex: 0,\n      },\n    });\n  },\n  on: {\n    init() {\n      const swiper = this;\n      swiper.pagination.init();\n      swiper.pagination.render();\n      swiper.pagination.update();\n    },\n    activeIndexChange() {\n      const swiper = this;\n      if (swiper.params.loop) {\n        swiper.pagination.update();\n      } else if (typeof swiper.snapIndex === 'undefined') {\n        swiper.pagination.update();\n      }\n    },\n    snapIndexChange() {\n      const swiper = this;\n      if (!swiper.params.loop) {\n        swiper.pagination.update();\n      }\n    },\n    slidesLengthChange() {\n      const swiper = this;\n      if (swiper.params.loop) {\n        swiper.pagination.render();\n        swiper.pagination.update();\n      }\n    },\n    snapGridLengthChange() {\n      const swiper = this;\n      if (!swiper.params.loop) {\n        swiper.pagination.render();\n        swiper.pagination.update();\n      }\n    },\n    destroy() {\n      const swiper = this;\n      swiper.pagination.destroy();\n    },\n    click(e) {\n      const swiper = this;\n      if (\n        swiper.params.pagination.el\n        && swiper.params.pagination.hideOnClick\n        && swiper.pagination.$el.length > 0\n        && !$(e.target).hasClass(swiper.params.pagination.bulletClass)\n      ) {\n        const isHidden = swiper.pagination.$el.hasClass(swiper.params.pagination.hiddenClass);\n        if (isHidden === true) {\n          swiper.emit('paginationShow', swiper);\n        } else {\n          swiper.emit('paginationHide', swiper);\n        }\n        swiper.pagination.$el.toggleClass(swiper.params.pagination.hiddenClass);\n      }\n    },\n  },\n};\n\nconst Scrollbar = {\n  setTranslate() {\n    const swiper = this;\n    if (!swiper.params.scrollbar.el || !swiper.scrollbar.el) return;\n    const { scrollbar, rtlTranslate: rtl, progress } = swiper;\n    const {\n      dragSize, trackSize, $dragEl, $el,\n    } = scrollbar;\n    const params = swiper.params.scrollbar;\n\n    let newSize = dragSize;\n    let newPos = (trackSize - dragSize) * progress;\n    if (rtl) {\n      newPos = -newPos;\n      if (newPos > 0) {\n        newSize = dragSize - newPos;\n        newPos = 0;\n      } else if (-newPos + dragSize > trackSize) {\n        newSize = trackSize + newPos;\n      }\n    } else if (newPos < 0) {\n      newSize = dragSize + newPos;\n      newPos = 0;\n    } else if (newPos + dragSize > trackSize) {\n      newSize = trackSize - newPos;\n    }\n    if (swiper.isHorizontal()) {\n      $dragEl.transform(`translate3d(${newPos}px, 0, 0)`);\n      $dragEl[0].style.width = `${newSize}px`;\n    } else {\n      $dragEl.transform(`translate3d(0px, ${newPos}px, 0)`);\n      $dragEl[0].style.height = `${newSize}px`;\n    }\n    if (params.hide) {\n      clearTimeout(swiper.scrollbar.timeout);\n      $el[0].style.opacity = 1;\n      swiper.scrollbar.timeout = setTimeout(() => {\n        $el[0].style.opacity = 0;\n        $el.transition(400);\n      }, 1000);\n    }\n  },\n  setTransition(duration) {\n    const swiper = this;\n    if (!swiper.params.scrollbar.el || !swiper.scrollbar.el) return;\n    swiper.scrollbar.$dragEl.transition(duration);\n  },\n  updateSize() {\n    const swiper = this;\n    if (!swiper.params.scrollbar.el || !swiper.scrollbar.el) return;\n\n    const { scrollbar } = swiper;\n    const { $dragEl, $el } = scrollbar;\n\n    $dragEl[0].style.width = '';\n    $dragEl[0].style.height = '';\n    const trackSize = swiper.isHorizontal() ? $el[0].offsetWidth : $el[0].offsetHeight;\n\n    const divider = swiper.size / swiper.virtualSize;\n    const moveDivider = divider * (trackSize / swiper.size);\n    let dragSize;\n    if (swiper.params.scrollbar.dragSize === 'auto') {\n      dragSize = trackSize * divider;\n    } else {\n      dragSize = parseInt(swiper.params.scrollbar.dragSize, 10);\n    }\n\n    if (swiper.isHorizontal()) {\n      $dragEl[0].style.width = `${dragSize}px`;\n    } else {\n      $dragEl[0].style.height = `${dragSize}px`;\n    }\n\n    if (divider >= 1) {\n      $el[0].style.display = 'none';\n    } else {\n      $el[0].style.display = '';\n    }\n    if (swiper.params.scrollbar.hide) {\n      $el[0].style.opacity = 0;\n    }\n    Utils.extend(scrollbar, {\n      trackSize,\n      divider,\n      moveDivider,\n      dragSize,\n    });\n    scrollbar.$el[swiper.params.watchOverflow && swiper.isLocked ? 'addClass' : 'removeClass'](swiper.params.scrollbar.lockClass);\n  },\n  getPointerPosition(e) {\n    const swiper = this;\n    if (swiper.isHorizontal()) {\n      return ((e.type === 'touchstart' || e.type === 'touchmove') ? e.targetTouches[0].clientX : e.clientX);\n    }\n    return ((e.type === 'touchstart' || e.type === 'touchmove') ? e.targetTouches[0].clientY : e.clientY);\n  },\n  setDragPosition(e) {\n    const swiper = this;\n    const { scrollbar, rtlTranslate: rtl } = swiper;\n    const {\n      $el,\n      dragSize,\n      trackSize,\n      dragStartPos,\n    } = scrollbar;\n\n    let positionRatio;\n    positionRatio = ((scrollbar.getPointerPosition(e)) - $el.offset()[swiper.isHorizontal() ? 'left' : 'top']\n      - (dragStartPos !== null ? dragStartPos : dragSize / 2)) / (trackSize - dragSize);\n    positionRatio = Math.max(Math.min(positionRatio, 1), 0);\n    if (rtl) {\n      positionRatio = 1 - positionRatio;\n    }\n\n    const position = swiper.minTranslate() + ((swiper.maxTranslate() - swiper.minTranslate()) * positionRatio);\n\n    swiper.updateProgress(position);\n    swiper.setTranslate(position);\n    swiper.updateActiveIndex();\n    swiper.updateSlidesClasses();\n  },\n  onDragStart(e) {\n    const swiper = this;\n    const params = swiper.params.scrollbar;\n    const { scrollbar, $wrapperEl } = swiper;\n    const { $el, $dragEl } = scrollbar;\n    swiper.scrollbar.isTouched = true;\n    swiper.scrollbar.dragStartPos = (e.target === $dragEl[0] || e.target === $dragEl)\n      ? scrollbar.getPointerPosition(e) - e.target.getBoundingClientRect()[swiper.isHorizontal() ? 'left' : 'top'] : null;\n    e.preventDefault();\n    e.stopPropagation();\n\n    $wrapperEl.transition(100);\n    $dragEl.transition(100);\n    scrollbar.setDragPosition(e);\n\n    clearTimeout(swiper.scrollbar.dragTimeout);\n\n    $el.transition(0);\n    if (params.hide) {\n      $el.css('opacity', 1);\n    }\n    if (swiper.params.cssMode) {\n      swiper.$wrapperEl.css('scroll-snap-type', 'none');\n    }\n    swiper.emit('scrollbarDragStart', e);\n  },\n  onDragMove(e) {\n    const swiper = this;\n    const { scrollbar, $wrapperEl } = swiper;\n    const { $el, $dragEl } = scrollbar;\n\n    if (!swiper.scrollbar.isTouched) return;\n    if (e.preventDefault) e.preventDefault();\n    else e.returnValue = false;\n    scrollbar.setDragPosition(e);\n    $wrapperEl.transition(0);\n    $el.transition(0);\n    $dragEl.transition(0);\n    swiper.emit('scrollbarDragMove', e);\n  },\n  onDragEnd(e) {\n    const swiper = this;\n\n    const params = swiper.params.scrollbar;\n    const { scrollbar, $wrapperEl } = swiper;\n    const { $el } = scrollbar;\n\n    if (!swiper.scrollbar.isTouched) return;\n    swiper.scrollbar.isTouched = false;\n    if (swiper.params.cssMode) {\n      swiper.$wrapperEl.css('scroll-snap-type', '');\n      $wrapperEl.transition('');\n    }\n    if (params.hide) {\n      clearTimeout(swiper.scrollbar.dragTimeout);\n      swiper.scrollbar.dragTimeout = Utils.nextTick(() => {\n        $el.css('opacity', 0);\n        $el.transition(400);\n      }, 1000);\n    }\n    swiper.emit('scrollbarDragEnd', e);\n    if (params.snapOnRelease) {\n      swiper.slideToClosest();\n    }\n  },\n  enableDraggable() {\n    const swiper = this;\n    if (!swiper.params.scrollbar.el) return;\n    const {\n      scrollbar, touchEventsTouch, touchEventsDesktop, params,\n    } = swiper;\n    const $el = scrollbar.$el;\n    const target = $el[0];\n    const activeListener = Support.passiveListener && params.passiveListeners ? { passive: false, capture: false } : false;\n    const passiveListener = Support.passiveListener && params.passiveListeners ? { passive: true, capture: false } : false;\n    if (!Support.touch) {\n      target.addEventListener(touchEventsDesktop.start, swiper.scrollbar.onDragStart, activeListener);\n      document$1.addEventListener(touchEventsDesktop.move, swiper.scrollbar.onDragMove, activeListener);\n      document$1.addEventListener(touchEventsDesktop.end, swiper.scrollbar.onDragEnd, passiveListener);\n    } else {\n      target.addEventListener(touchEventsTouch.start, swiper.scrollbar.onDragStart, activeListener);\n      target.addEventListener(touchEventsTouch.move, swiper.scrollbar.onDragMove, activeListener);\n      target.addEventListener(touchEventsTouch.end, swiper.scrollbar.onDragEnd, passiveListener);\n    }\n  },\n  disableDraggable() {\n    const swiper = this;\n    if (!swiper.params.scrollbar.el) return;\n    const {\n      scrollbar, touchEventsTouch, touchEventsDesktop, params,\n    } = swiper;\n    const $el = scrollbar.$el;\n    const target = $el[0];\n    const activeListener = Support.passiveListener && params.passiveListeners ? { passive: false, capture: false } : false;\n    const passiveListener = Support.passiveListener && params.passiveListeners ? { passive: true, capture: false } : false;\n    if (!Support.touch) {\n      target.removeEventListener(touchEventsDesktop.start, swiper.scrollbar.onDragStart, activeListener);\n      document$1.removeEventListener(touchEventsDesktop.move, swiper.scrollbar.onDragMove, activeListener);\n      document$1.removeEventListener(touchEventsDesktop.end, swiper.scrollbar.onDragEnd, passiveListener);\n    } else {\n      target.removeEventListener(touchEventsTouch.start, swiper.scrollbar.onDragStart, activeListener);\n      target.removeEventListener(touchEventsTouch.move, swiper.scrollbar.onDragMove, activeListener);\n      target.removeEventListener(touchEventsTouch.end, swiper.scrollbar.onDragEnd, passiveListener);\n    }\n  },\n  init() {\n    const swiper = this;\n    if (!swiper.params.scrollbar.el) return;\n    const { scrollbar, $el: $swiperEl } = swiper;\n    const params = swiper.params.scrollbar;\n\n    let $el = $(params.el);\n    if (swiper.params.uniqueNavElements && typeof params.el === 'string' && $el.length > 1 && $swiperEl.find(params.el).length === 1) {\n      $el = $swiperEl.find(params.el);\n    }\n\n    let $dragEl = $el.find(`.${swiper.params.scrollbar.dragClass}`);\n    if ($dragEl.length === 0) {\n      $dragEl = $(`<div class=\"${swiper.params.scrollbar.dragClass}\"></div>`);\n      $el.append($dragEl);\n    }\n\n    Utils.extend(scrollbar, {\n      $el,\n      el: $el[0],\n      $dragEl,\n      dragEl: $dragEl[0],\n    });\n\n    if (params.draggable) {\n      scrollbar.enableDraggable();\n    }\n  },\n  destroy() {\n    const swiper = this;\n    swiper.scrollbar.disableDraggable();\n  },\n};\n\nvar Scrollbar$1 = {\n  name: 'scrollbar',\n  params: {\n    scrollbar: {\n      el: null,\n      dragSize: 'auto',\n      hide: false,\n      draggable: false,\n      snapOnRelease: true,\n      lockClass: 'swiper-scrollbar-lock',\n      dragClass: 'swiper-scrollbar-drag',\n    },\n  },\n  create() {\n    const swiper = this;\n    Utils.extend(swiper, {\n      scrollbar: {\n        init: Scrollbar.init.bind(swiper),\n        destroy: Scrollbar.destroy.bind(swiper),\n        updateSize: Scrollbar.updateSize.bind(swiper),\n        setTranslate: Scrollbar.setTranslate.bind(swiper),\n        setTransition: Scrollbar.setTransition.bind(swiper),\n        enableDraggable: Scrollbar.enableDraggable.bind(swiper),\n        disableDraggable: Scrollbar.disableDraggable.bind(swiper),\n        setDragPosition: Scrollbar.setDragPosition.bind(swiper),\n        getPointerPosition: Scrollbar.getPointerPosition.bind(swiper),\n        onDragStart: Scrollbar.onDragStart.bind(swiper),\n        onDragMove: Scrollbar.onDragMove.bind(swiper),\n        onDragEnd: Scrollbar.onDragEnd.bind(swiper),\n        isTouched: false,\n        timeout: null,\n        dragTimeout: null,\n      },\n    });\n  },\n  on: {\n    init() {\n      const swiper = this;\n      swiper.scrollbar.init();\n      swiper.scrollbar.updateSize();\n      swiper.scrollbar.setTranslate();\n    },\n    update() {\n      const swiper = this;\n      swiper.scrollbar.updateSize();\n    },\n    resize() {\n      const swiper = this;\n      swiper.scrollbar.updateSize();\n    },\n    observerUpdate() {\n      const swiper = this;\n      swiper.scrollbar.updateSize();\n    },\n    setTranslate() {\n      const swiper = this;\n      swiper.scrollbar.setTranslate();\n    },\n    setTransition(duration) {\n      const swiper = this;\n      swiper.scrollbar.setTransition(duration);\n    },\n    destroy() {\n      const swiper = this;\n      swiper.scrollbar.destroy();\n    },\n  },\n};\n\nconst Parallax = {\n  setTransform(el, progress) {\n    const swiper = this;\n    const { rtl } = swiper;\n\n    const $el = $(el);\n    const rtlFactor = rtl ? -1 : 1;\n\n    const p = $el.attr('data-swiper-parallax') || '0';\n    let x = $el.attr('data-swiper-parallax-x');\n    let y = $el.attr('data-swiper-parallax-y');\n    const scale = $el.attr('data-swiper-parallax-scale');\n    const opacity = $el.attr('data-swiper-parallax-opacity');\n\n    if (x || y) {\n      x = x || '0';\n      y = y || '0';\n    } else if (swiper.isHorizontal()) {\n      x = p;\n      y = '0';\n    } else {\n      y = p;\n      x = '0';\n    }\n\n    if ((x).indexOf('%') >= 0) {\n      x = `${parseInt(x, 10) * progress * rtlFactor}%`;\n    } else {\n      x = `${x * progress * rtlFactor}px`;\n    }\n    if ((y).indexOf('%') >= 0) {\n      y = `${parseInt(y, 10) * progress}%`;\n    } else {\n      y = `${y * progress}px`;\n    }\n\n    if (typeof opacity !== 'undefined' && opacity !== null) {\n      const currentOpacity = opacity - ((opacity - 1) * (1 - Math.abs(progress)));\n      $el[0].style.opacity = currentOpacity;\n    }\n    if (typeof scale === 'undefined' || scale === null) {\n      $el.transform(`translate3d(${x}, ${y}, 0px)`);\n    } else {\n      const currentScale = scale - ((scale - 1) * (1 - Math.abs(progress)));\n      $el.transform(`translate3d(${x}, ${y}, 0px) scale(${currentScale})`);\n    }\n  },\n  setTranslate() {\n    const swiper = this;\n    const {\n      $el, slides, progress, snapGrid,\n    } = swiper;\n    $el.children('[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y], [data-swiper-parallax-opacity], [data-swiper-parallax-scale]')\n      .each((index, el) => {\n        swiper.parallax.setTransform(el, progress);\n      });\n    slides.each((slideIndex, slideEl) => {\n      let slideProgress = slideEl.progress;\n      if (swiper.params.slidesPerGroup > 1 && swiper.params.slidesPerView !== 'auto') {\n        slideProgress += Math.ceil(slideIndex / 2) - (progress * (snapGrid.length - 1));\n      }\n      slideProgress = Math.min(Math.max(slideProgress, -1), 1);\n      $(slideEl).find('[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y], [data-swiper-parallax-opacity], [data-swiper-parallax-scale]')\n        .each((index, el) => {\n          swiper.parallax.setTransform(el, slideProgress);\n        });\n    });\n  },\n  setTransition(duration = this.params.speed) {\n    const swiper = this;\n    const { $el } = swiper;\n    $el.find('[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y], [data-swiper-parallax-opacity], [data-swiper-parallax-scale]')\n      .each((index, parallaxEl) => {\n        const $parallaxEl = $(parallaxEl);\n        let parallaxDuration = parseInt($parallaxEl.attr('data-swiper-parallax-duration'), 10) || duration;\n        if (duration === 0) parallaxDuration = 0;\n        $parallaxEl.transition(parallaxDuration);\n      });\n  },\n};\n\nvar Parallax$1 = {\n  name: 'parallax',\n  params: {\n    parallax: {\n      enabled: false,\n    },\n  },\n  create() {\n    const swiper = this;\n    Utils.extend(swiper, {\n      parallax: {\n        setTransform: Parallax.setTransform.bind(swiper),\n        setTranslate: Parallax.setTranslate.bind(swiper),\n        setTransition: Parallax.setTransition.bind(swiper),\n      },\n    });\n  },\n  on: {\n    beforeInit() {\n      const swiper = this;\n      if (!swiper.params.parallax.enabled) return;\n      swiper.params.watchSlidesProgress = true;\n      swiper.originalParams.watchSlidesProgress = true;\n    },\n    init() {\n      const swiper = this;\n      if (!swiper.params.parallax.enabled) return;\n      swiper.parallax.setTranslate();\n    },\n    setTranslate() {\n      const swiper = this;\n      if (!swiper.params.parallax.enabled) return;\n      swiper.parallax.setTranslate();\n    },\n    setTransition(duration) {\n      const swiper = this;\n      if (!swiper.params.parallax.enabled) return;\n      swiper.parallax.setTransition(duration);\n    },\n  },\n};\n\nconst Zoom = {\n  // Calc Scale From Multi-touches\n  getDistanceBetweenTouches(e) {\n    if (e.targetTouches.length < 2) return 1;\n    const x1 = e.targetTouches[0].pageX;\n    const y1 = e.targetTouches[0].pageY;\n    const x2 = e.targetTouches[1].pageX;\n    const y2 = e.targetTouches[1].pageY;\n    const distance = Math.sqrt(((x2 - x1) ** 2) + ((y2 - y1) ** 2));\n    return distance;\n  },\n  // Events\n  onGestureStart(e) {\n    const swiper = this;\n    const params = swiper.params.zoom;\n    const zoom = swiper.zoom;\n    const { gesture } = zoom;\n    zoom.fakeGestureTouched = false;\n    zoom.fakeGestureMoved = false;\n    if (!Support.gestures) {\n      if (e.type !== 'touchstart' || (e.type === 'touchstart' && e.targetTouches.length < 2)) {\n        return;\n      }\n      zoom.fakeGestureTouched = true;\n      gesture.scaleStart = Zoom.getDistanceBetweenTouches(e);\n    }\n    if (!gesture.$slideEl || !gesture.$slideEl.length) {\n      gesture.$slideEl = $(e.target).closest(`.${swiper.params.slideClass}`);\n      if (gesture.$slideEl.length === 0) gesture.$slideEl = swiper.slides.eq(swiper.activeIndex);\n      gesture.$imageEl = gesture.$slideEl.find('img, svg, canvas, picture, .swiper-zoom-target');\n      gesture.$imageWrapEl = gesture.$imageEl.parent(`.${params.containerClass}`);\n      gesture.maxRatio = gesture.$imageWrapEl.attr('data-swiper-zoom') || params.maxRatio;\n      if (gesture.$imageWrapEl.length === 0) {\n        gesture.$imageEl = undefined;\n        return;\n      }\n    }\n    if (gesture.$imageEl) {\n      gesture.$imageEl.transition(0);\n    }\n    swiper.zoom.isScaling = true;\n  },\n  onGestureChange(e) {\n    const swiper = this;\n    const params = swiper.params.zoom;\n    const zoom = swiper.zoom;\n    const { gesture } = zoom;\n    if (!Support.gestures) {\n      if (e.type !== 'touchmove' || (e.type === 'touchmove' && e.targetTouches.length < 2)) {\n        return;\n      }\n      zoom.fakeGestureMoved = true;\n      gesture.scaleMove = Zoom.getDistanceBetweenTouches(e);\n    }\n    if (!gesture.$imageEl || gesture.$imageEl.length === 0) return;\n    if (Support.gestures) {\n      zoom.scale = e.scale * zoom.currentScale;\n    } else {\n      zoom.scale = (gesture.scaleMove / gesture.scaleStart) * zoom.currentScale;\n    }\n    if (zoom.scale > gesture.maxRatio) {\n      zoom.scale = (gesture.maxRatio - 1) + (((zoom.scale - gesture.maxRatio) + 1) ** 0.5);\n    }\n    if (zoom.scale < params.minRatio) {\n      zoom.scale = (params.minRatio + 1) - (((params.minRatio - zoom.scale) + 1) ** 0.5);\n    }\n    gesture.$imageEl.transform(`translate3d(0,0,0) scale(${zoom.scale})`);\n  },\n  onGestureEnd(e) {\n    const swiper = this;\n    const params = swiper.params.zoom;\n    const zoom = swiper.zoom;\n    const { gesture } = zoom;\n    if (!Support.gestures) {\n      if (!zoom.fakeGestureTouched || !zoom.fakeGestureMoved) {\n        return;\n      }\n      if (e.type !== 'touchend' || (e.type === 'touchend' && e.changedTouches.length < 2 && !Device.android)) {\n        return;\n      }\n      zoom.fakeGestureTouched = false;\n      zoom.fakeGestureMoved = false;\n    }\n    if (!gesture.$imageEl || gesture.$imageEl.length === 0) return;\n    zoom.scale = Math.max(Math.min(zoom.scale, gesture.maxRatio), params.minRatio);\n    gesture.$imageEl.transition(swiper.params.speed).transform(`translate3d(0,0,0) scale(${zoom.scale})`);\n    zoom.currentScale = zoom.scale;\n    zoom.isScaling = false;\n    if (zoom.scale === 1) gesture.$slideEl = undefined;\n  },\n  onTouchStart(e) {\n    const swiper = this;\n    const zoom = swiper.zoom;\n    const { gesture, image } = zoom;\n    if (!gesture.$imageEl || gesture.$imageEl.length === 0) return;\n    if (image.isTouched) return;\n    if (Device.android && e.cancelable) e.preventDefault();\n    image.isTouched = true;\n    image.touchesStart.x = e.type === 'touchstart' ? e.targetTouches[0].pageX : e.pageX;\n    image.touchesStart.y = e.type === 'touchstart' ? e.targetTouches[0].pageY : e.pageY;\n  },\n  onTouchMove(e) {\n    const swiper = this;\n    const zoom = swiper.zoom;\n    const { gesture, image, velocity } = zoom;\n    if (!gesture.$imageEl || gesture.$imageEl.length === 0) return;\n    swiper.allowClick = false;\n    if (!image.isTouched || !gesture.$slideEl) return;\n\n    if (!image.isMoved) {\n      image.width = gesture.$imageEl[0].offsetWidth;\n      image.height = gesture.$imageEl[0].offsetHeight;\n      image.startX = Utils.getTranslate(gesture.$imageWrapEl[0], 'x') || 0;\n      image.startY = Utils.getTranslate(gesture.$imageWrapEl[0], 'y') || 0;\n      gesture.slideWidth = gesture.$slideEl[0].offsetWidth;\n      gesture.slideHeight = gesture.$slideEl[0].offsetHeight;\n      gesture.$imageWrapEl.transition(0);\n      if (swiper.rtl) {\n        image.startX = -image.startX;\n        image.startY = -image.startY;\n      }\n    }\n    // Define if we need image drag\n    const scaledWidth = image.width * zoom.scale;\n    const scaledHeight = image.height * zoom.scale;\n\n    if (scaledWidth < gesture.slideWidth && scaledHeight < gesture.slideHeight) return;\n\n    image.minX = Math.min(((gesture.slideWidth / 2) - (scaledWidth / 2)), 0);\n    image.maxX = -image.minX;\n    image.minY = Math.min(((gesture.slideHeight / 2) - (scaledHeight / 2)), 0);\n    image.maxY = -image.minY;\n\n    image.touchesCurrent.x = e.type === 'touchmove' ? e.targetTouches[0].pageX : e.pageX;\n    image.touchesCurrent.y = e.type === 'touchmove' ? e.targetTouches[0].pageY : e.pageY;\n\n    if (!image.isMoved && !zoom.isScaling) {\n      if (\n        swiper.isHorizontal()\n        && (\n          (Math.floor(image.minX) === Math.floor(image.startX) && image.touchesCurrent.x < image.touchesStart.x)\n          || (Math.floor(image.maxX) === Math.floor(image.startX) && image.touchesCurrent.x > image.touchesStart.x)\n        )\n      ) {\n        image.isTouched = false;\n        return;\n      } if (\n        !swiper.isHorizontal()\n        && (\n          (Math.floor(image.minY) === Math.floor(image.startY) && image.touchesCurrent.y < image.touchesStart.y)\n          || (Math.floor(image.maxY) === Math.floor(image.startY) && image.touchesCurrent.y > image.touchesStart.y)\n        )\n      ) {\n        image.isTouched = false;\n        return;\n      }\n    }\n    if (e.cancelable) {\n      e.preventDefault();\n    }\n    e.stopPropagation();\n\n    image.isMoved = true;\n    image.currentX = (image.touchesCurrent.x - image.touchesStart.x) + image.startX;\n    image.currentY = (image.touchesCurrent.y - image.touchesStart.y) + image.startY;\n\n    if (image.currentX < image.minX) {\n      image.currentX = (image.minX + 1) - (((image.minX - image.currentX) + 1) ** 0.8);\n    }\n    if (image.currentX > image.maxX) {\n      image.currentX = (image.maxX - 1) + (((image.currentX - image.maxX) + 1) ** 0.8);\n    }\n\n    if (image.currentY < image.minY) {\n      image.currentY = (image.minY + 1) - (((image.minY - image.currentY) + 1) ** 0.8);\n    }\n    if (image.currentY > image.maxY) {\n      image.currentY = (image.maxY - 1) + (((image.currentY - image.maxY) + 1) ** 0.8);\n    }\n\n    // Velocity\n    if (!velocity.prevPositionX) velocity.prevPositionX = image.touchesCurrent.x;\n    if (!velocity.prevPositionY) velocity.prevPositionY = image.touchesCurrent.y;\n    if (!velocity.prevTime) velocity.prevTime = Date.now();\n    velocity.x = (image.touchesCurrent.x - velocity.prevPositionX) / (Date.now() - velocity.prevTime) / 2;\n    velocity.y = (image.touchesCurrent.y - velocity.prevPositionY) / (Date.now() - velocity.prevTime) / 2;\n    if (Math.abs(image.touchesCurrent.x - velocity.prevPositionX) < 2) velocity.x = 0;\n    if (Math.abs(image.touchesCurrent.y - velocity.prevPositionY) < 2) velocity.y = 0;\n    velocity.prevPositionX = image.touchesCurrent.x;\n    velocity.prevPositionY = image.touchesCurrent.y;\n    velocity.prevTime = Date.now();\n\n    gesture.$imageWrapEl.transform(`translate3d(${image.currentX}px, ${image.currentY}px,0)`);\n  },\n  onTouchEnd() {\n    const swiper = this;\n    const zoom = swiper.zoom;\n    const { gesture, image, velocity } = zoom;\n    if (!gesture.$imageEl || gesture.$imageEl.length === 0) return;\n    if (!image.isTouched || !image.isMoved) {\n      image.isTouched = false;\n      image.isMoved = false;\n      return;\n    }\n    image.isTouched = false;\n    image.isMoved = false;\n    let momentumDurationX = 300;\n    let momentumDurationY = 300;\n    const momentumDistanceX = velocity.x * momentumDurationX;\n    const newPositionX = image.currentX + momentumDistanceX;\n    const momentumDistanceY = velocity.y * momentumDurationY;\n    const newPositionY = image.currentY + momentumDistanceY;\n\n    // Fix duration\n    if (velocity.x !== 0) momentumDurationX = Math.abs((newPositionX - image.currentX) / velocity.x);\n    if (velocity.y !== 0) momentumDurationY = Math.abs((newPositionY - image.currentY) / velocity.y);\n    const momentumDuration = Math.max(momentumDurationX, momentumDurationY);\n\n    image.currentX = newPositionX;\n    image.currentY = newPositionY;\n\n    // Define if we need image drag\n    const scaledWidth = image.width * zoom.scale;\n    const scaledHeight = image.height * zoom.scale;\n    image.minX = Math.min(((gesture.slideWidth / 2) - (scaledWidth / 2)), 0);\n    image.maxX = -image.minX;\n    image.minY = Math.min(((gesture.slideHeight / 2) - (scaledHeight / 2)), 0);\n    image.maxY = -image.minY;\n    image.currentX = Math.max(Math.min(image.currentX, image.maxX), image.minX);\n    image.currentY = Math.max(Math.min(image.currentY, image.maxY), image.minY);\n\n    gesture.$imageWrapEl.transition(momentumDuration).transform(`translate3d(${image.currentX}px, ${image.currentY}px,0)`);\n  },\n  onTransitionEnd() {\n    const swiper = this;\n    const zoom = swiper.zoom;\n    const { gesture } = zoom;\n    if (gesture.$slideEl && swiper.previousIndex !== swiper.activeIndex) {\n      if (gesture.$imageEl) {\n        gesture.$imageEl.transform('translate3d(0,0,0) scale(1)');\n      }\n      if (gesture.$imageWrapEl) {\n        gesture.$imageWrapEl.transform('translate3d(0,0,0)');\n      }\n\n      zoom.scale = 1;\n      zoom.currentScale = 1;\n\n      gesture.$slideEl = undefined;\n      gesture.$imageEl = undefined;\n      gesture.$imageWrapEl = undefined;\n    }\n  },\n  // Toggle Zoom\n  toggle(e) {\n    const swiper = this;\n    const zoom = swiper.zoom;\n\n    if (zoom.scale && zoom.scale !== 1) {\n      // Zoom Out\n      zoom.out();\n    } else {\n      // Zoom In\n      zoom.in(e);\n    }\n  },\n  in(e) {\n    const swiper = this;\n\n    const zoom = swiper.zoom;\n    const params = swiper.params.zoom;\n    const { gesture, image } = zoom;\n\n    if (!gesture.$slideEl) {\n      if (swiper.params.virtual && swiper.params.virtual.enabled && swiper.virtual) {\n        gesture.$slideEl = swiper.$wrapperEl.children(`.${swiper.params.slideActiveClass}`);\n      } else {\n        gesture.$slideEl = swiper.slides.eq(swiper.activeIndex);\n      }\n      gesture.$imageEl = gesture.$slideEl.find('img, svg, canvas, picture, .swiper-zoom-target');\n      gesture.$imageWrapEl = gesture.$imageEl.parent(`.${params.containerClass}`);\n    }\n    if (!gesture.$imageEl || gesture.$imageEl.length === 0) return;\n\n    gesture.$slideEl.addClass(`${params.zoomedSlideClass}`);\n\n    let touchX;\n    let touchY;\n    let offsetX;\n    let offsetY;\n    let diffX;\n    let diffY;\n    let translateX;\n    let translateY;\n    let imageWidth;\n    let imageHeight;\n    let scaledWidth;\n    let scaledHeight;\n    let translateMinX;\n    let translateMinY;\n    let translateMaxX;\n    let translateMaxY;\n    let slideWidth;\n    let slideHeight;\n\n    if (typeof image.touchesStart.x === 'undefined' && e) {\n      touchX = e.type === 'touchend' ? e.changedTouches[0].pageX : e.pageX;\n      touchY = e.type === 'touchend' ? e.changedTouches[0].pageY : e.pageY;\n    } else {\n      touchX = image.touchesStart.x;\n      touchY = image.touchesStart.y;\n    }\n\n    zoom.scale = gesture.$imageWrapEl.attr('data-swiper-zoom') || params.maxRatio;\n    zoom.currentScale = gesture.$imageWrapEl.attr('data-swiper-zoom') || params.maxRatio;\n    if (e) {\n      slideWidth = gesture.$slideEl[0].offsetWidth;\n      slideHeight = gesture.$slideEl[0].offsetHeight;\n      offsetX = gesture.$slideEl.offset().left;\n      offsetY = gesture.$slideEl.offset().top;\n      diffX = (offsetX + (slideWidth / 2)) - touchX;\n      diffY = (offsetY + (slideHeight / 2)) - touchY;\n\n      imageWidth = gesture.$imageEl[0].offsetWidth;\n      imageHeight = gesture.$imageEl[0].offsetHeight;\n      scaledWidth = imageWidth * zoom.scale;\n      scaledHeight = imageHeight * zoom.scale;\n\n      translateMinX = Math.min(((slideWidth / 2) - (scaledWidth / 2)), 0);\n      translateMinY = Math.min(((slideHeight / 2) - (scaledHeight / 2)), 0);\n      translateMaxX = -translateMinX;\n      translateMaxY = -translateMinY;\n\n      translateX = diffX * zoom.scale;\n      translateY = diffY * zoom.scale;\n\n      if (translateX < translateMinX) {\n        translateX = translateMinX;\n      }\n      if (translateX > translateMaxX) {\n        translateX = translateMaxX;\n      }\n\n      if (translateY < translateMinY) {\n        translateY = translateMinY;\n      }\n      if (translateY > translateMaxY) {\n        translateY = translateMaxY;\n      }\n    } else {\n      translateX = 0;\n      translateY = 0;\n    }\n    gesture.$imageWrapEl.transition(300).transform(`translate3d(${translateX}px, ${translateY}px,0)`);\n    gesture.$imageEl.transition(300).transform(`translate3d(0,0,0) scale(${zoom.scale})`);\n  },\n  out() {\n    const swiper = this;\n\n    const zoom = swiper.zoom;\n    const params = swiper.params.zoom;\n    const { gesture } = zoom;\n\n    if (!gesture.$slideEl) {\n      if (swiper.params.virtual && swiper.params.virtual.enabled && swiper.virtual) {\n        gesture.$slideEl = swiper.$wrapperEl.children(`.${swiper.params.slideActiveClass}`);\n      } else {\n        gesture.$slideEl = swiper.slides.eq(swiper.activeIndex);\n      }\n      gesture.$imageEl = gesture.$slideEl.find('img, svg, canvas, picture, .swiper-zoom-target');\n      gesture.$imageWrapEl = gesture.$imageEl.parent(`.${params.containerClass}`);\n    }\n    if (!gesture.$imageEl || gesture.$imageEl.length === 0) return;\n\n    zoom.scale = 1;\n    zoom.currentScale = 1;\n    gesture.$imageWrapEl.transition(300).transform('translate3d(0,0,0)');\n    gesture.$imageEl.transition(300).transform('translate3d(0,0,0) scale(1)');\n    gesture.$slideEl.removeClass(`${params.zoomedSlideClass}`);\n    gesture.$slideEl = undefined;\n  },\n  // Attach/Detach Events\n  enable() {\n    const swiper = this;\n    const zoom = swiper.zoom;\n    if (zoom.enabled) return;\n    zoom.enabled = true;\n\n    const passiveListener = swiper.touchEvents.start === 'touchstart' && Support.passiveListener && swiper.params.passiveListeners ? { passive: true, capture: false } : false;\n    const activeListenerWithCapture = Support.passiveListener ? { passive: false, capture: true } : true;\n\n    const slideSelector = `.${swiper.params.slideClass}`;\n\n    // Scale image\n    if (Support.gestures) {\n      swiper.$wrapperEl.on('gesturestart', slideSelector, zoom.onGestureStart, passiveListener);\n      swiper.$wrapperEl.on('gesturechange', slideSelector, zoom.onGestureChange, passiveListener);\n      swiper.$wrapperEl.on('gestureend', slideSelector, zoom.onGestureEnd, passiveListener);\n    } else if (swiper.touchEvents.start === 'touchstart') {\n      swiper.$wrapperEl.on(swiper.touchEvents.start, slideSelector, zoom.onGestureStart, passiveListener);\n      swiper.$wrapperEl.on(swiper.touchEvents.move, slideSelector, zoom.onGestureChange, activeListenerWithCapture);\n      swiper.$wrapperEl.on(swiper.touchEvents.end, slideSelector, zoom.onGestureEnd, passiveListener);\n      if (swiper.touchEvents.cancel) {\n        swiper.$wrapperEl.on(swiper.touchEvents.cancel, slideSelector, zoom.onGestureEnd, passiveListener);\n      }\n    }\n\n    // Move image\n    swiper.$wrapperEl.on(swiper.touchEvents.move, `.${swiper.params.zoom.containerClass}`, zoom.onTouchMove, activeListenerWithCapture);\n  },\n  disable() {\n    const swiper = this;\n    const zoom = swiper.zoom;\n    if (!zoom.enabled) return;\n\n    swiper.zoom.enabled = false;\n\n    const passiveListener = swiper.touchEvents.start === 'touchstart' && Support.passiveListener && swiper.params.passiveListeners ? { passive: true, capture: false } : false;\n    const activeListenerWithCapture = Support.passiveListener ? { passive: false, capture: true } : true;\n\n    const slideSelector = `.${swiper.params.slideClass}`;\n\n    // Scale image\n    if (Support.gestures) {\n      swiper.$wrapperEl.off('gesturestart', slideSelector, zoom.onGestureStart, passiveListener);\n      swiper.$wrapperEl.off('gesturechange', slideSelector, zoom.onGestureChange, passiveListener);\n      swiper.$wrapperEl.off('gestureend', slideSelector, zoom.onGestureEnd, passiveListener);\n    } else if (swiper.touchEvents.start === 'touchstart') {\n      swiper.$wrapperEl.off(swiper.touchEvents.start, slideSelector, zoom.onGestureStart, passiveListener);\n      swiper.$wrapperEl.off(swiper.touchEvents.move, slideSelector, zoom.onGestureChange, activeListenerWithCapture);\n      swiper.$wrapperEl.off(swiper.touchEvents.end, slideSelector, zoom.onGestureEnd, passiveListener);\n      if (swiper.touchEvents.cancel) {\n        swiper.$wrapperEl.off(swiper.touchEvents.cancel, slideSelector, zoom.onGestureEnd, passiveListener);\n      }\n    }\n\n    // Move image\n    swiper.$wrapperEl.off(swiper.touchEvents.move, `.${swiper.params.zoom.containerClass}`, zoom.onTouchMove, activeListenerWithCapture);\n  },\n};\n\nvar Zoom$1 = {\n  name: 'zoom',\n  params: {\n    zoom: {\n      enabled: false,\n      maxRatio: 3,\n      minRatio: 1,\n      toggle: true,\n      containerClass: 'swiper-zoom-container',\n      zoomedSlideClass: 'swiper-slide-zoomed',\n    },\n  },\n  create() {\n    const swiper = this;\n    const zoom = {\n      enabled: false,\n      scale: 1,\n      currentScale: 1,\n      isScaling: false,\n      gesture: {\n        $slideEl: undefined,\n        slideWidth: undefined,\n        slideHeight: undefined,\n        $imageEl: undefined,\n        $imageWrapEl: undefined,\n        maxRatio: 3,\n      },\n      image: {\n        isTouched: undefined,\n        isMoved: undefined,\n        currentX: undefined,\n        currentY: undefined,\n        minX: undefined,\n        minY: undefined,\n        maxX: undefined,\n        maxY: undefined,\n        width: undefined,\n        height: undefined,\n        startX: undefined,\n        startY: undefined,\n        touchesStart: {},\n        touchesCurrent: {},\n      },\n      velocity: {\n        x: undefined,\n        y: undefined,\n        prevPositionX: undefined,\n        prevPositionY: undefined,\n        prevTime: undefined,\n      },\n    };\n\n    ('onGestureStart onGestureChange onGestureEnd onTouchStart onTouchMove onTouchEnd onTransitionEnd toggle enable disable in out').split(' ').forEach((methodName) => {\n      zoom[methodName] = Zoom[methodName].bind(swiper);\n    });\n    Utils.extend(swiper, {\n      zoom,\n    });\n\n    let scale = 1;\n    Object.defineProperty(swiper.zoom, 'scale', {\n      get() {\n        return scale;\n      },\n      set(value) {\n        if (scale !== value) {\n          const imageEl = swiper.zoom.gesture.$imageEl ? swiper.zoom.gesture.$imageEl[0] : undefined;\n          const slideEl = swiper.zoom.gesture.$slideEl ? swiper.zoom.gesture.$slideEl[0] : undefined;\n          swiper.emit('zoomChange', value, imageEl, slideEl);\n        }\n        scale = value;\n      },\n    });\n  },\n  on: {\n    init() {\n      const swiper = this;\n      if (swiper.params.zoom.enabled) {\n        swiper.zoom.enable();\n      }\n    },\n    destroy() {\n      const swiper = this;\n      swiper.zoom.disable();\n    },\n    touchStart(e) {\n      const swiper = this;\n      if (!swiper.zoom.enabled) return;\n      swiper.zoom.onTouchStart(e);\n    },\n    touchEnd(e) {\n      const swiper = this;\n      if (!swiper.zoom.enabled) return;\n      swiper.zoom.onTouchEnd(e);\n    },\n    doubleTap(e) {\n      const swiper = this;\n      if (swiper.params.zoom.enabled && swiper.zoom.enabled && swiper.params.zoom.toggle) {\n        swiper.zoom.toggle(e);\n      }\n    },\n    transitionEnd() {\n      const swiper = this;\n      if (swiper.zoom.enabled && swiper.params.zoom.enabled) {\n        swiper.zoom.onTransitionEnd();\n      }\n    },\n    slideChange() {\n      const swiper = this;\n      if (swiper.zoom.enabled && swiper.params.zoom.enabled && swiper.params.cssMode) {\n        swiper.zoom.onTransitionEnd();\n      }\n    },\n  },\n};\n\nconst Lazy = {\n  loadInSlide(index, loadInDuplicate = true) {\n    const swiper = this;\n    const params = swiper.params.lazy;\n    if (typeof index === 'undefined') return;\n    if (swiper.slides.length === 0) return;\n    const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n\n    const $slideEl = isVirtual\n      ? swiper.$wrapperEl.children(`.${swiper.params.slideClass}[data-swiper-slide-index=\"${index}\"]`)\n      : swiper.slides.eq(index);\n\n    let $images = $slideEl.find(`.${params.elementClass}:not(.${params.loadedClass}):not(.${params.loadingClass})`);\n    if ($slideEl.hasClass(params.elementClass) && !$slideEl.hasClass(params.loadedClass) && !$slideEl.hasClass(params.loadingClass)) {\n      $images = $images.add($slideEl[0]);\n    }\n    if ($images.length === 0) return;\n\n    $images.each((imageIndex, imageEl) => {\n      const $imageEl = $(imageEl);\n      $imageEl.addClass(params.loadingClass);\n\n      const background = $imageEl.attr('data-background');\n      const src = $imageEl.attr('data-src');\n      const srcset = $imageEl.attr('data-srcset');\n      const sizes = $imageEl.attr('data-sizes');\n      const $pictureEl = $imageEl.parent('picture');\n\n      swiper.loadImage($imageEl[0], (src || background), srcset, sizes, false, () => {\n        if (typeof swiper === 'undefined' || swiper === null || !swiper || (swiper && !swiper.params) || swiper.destroyed) return;\n        if (background) {\n          $imageEl.css('background-image', `url(\"${background}\")`);\n          $imageEl.removeAttr('data-background');\n        } else {\n          if (srcset) {\n            $imageEl.attr('srcset', srcset);\n            $imageEl.removeAttr('data-srcset');\n          }\n          if (sizes) {\n            $imageEl.attr('sizes', sizes);\n            $imageEl.removeAttr('data-sizes');\n          }\n          if ($pictureEl.length) {\n            $pictureEl.children('source').each((sourceIndex, sourceEl) => {\n              const $source = $(sourceEl);\n\n              if ($source.attr('data-srcset')) {\n                $source.attr('srcset', $source.attr('data-srcset'));\n                $source.removeAttr('data-srcset');\n              }\n            });\n          }\n          if (src) {\n            $imageEl.attr('src', src);\n            $imageEl.removeAttr('data-src');\n          }\n        }\n\n        $imageEl.addClass(params.loadedClass).removeClass(params.loadingClass);\n        $slideEl.find(`.${params.preloaderClass}`).remove();\n        if (swiper.params.loop && loadInDuplicate) {\n          const slideOriginalIndex = $slideEl.attr('data-swiper-slide-index');\n          if ($slideEl.hasClass(swiper.params.slideDuplicateClass)) {\n            const originalSlide = swiper.$wrapperEl.children(`[data-swiper-slide-index=\"${slideOriginalIndex}\"]:not(.${swiper.params.slideDuplicateClass})`);\n            swiper.lazy.loadInSlide(originalSlide.index(), false);\n          } else {\n            const duplicatedSlide = swiper.$wrapperEl.children(`.${swiper.params.slideDuplicateClass}[data-swiper-slide-index=\"${slideOriginalIndex}\"]`);\n            swiper.lazy.loadInSlide(duplicatedSlide.index(), false);\n          }\n        }\n        swiper.emit('lazyImageReady', $slideEl[0], $imageEl[0]);\n        if (swiper.params.autoHeight) {\n          swiper.updateAutoHeight();\n        }\n      });\n\n      swiper.emit('lazyImageLoad', $slideEl[0], $imageEl[0]);\n    });\n  },\n  load() {\n    const swiper = this;\n    const {\n      $wrapperEl, params: swiperParams, slides, activeIndex,\n    } = swiper;\n    const isVirtual = swiper.virtual && swiperParams.virtual.enabled;\n    const params = swiperParams.lazy;\n\n    let slidesPerView = swiperParams.slidesPerView;\n    if (slidesPerView === 'auto') {\n      slidesPerView = 0;\n    }\n\n    function slideExist(index) {\n      if (isVirtual) {\n        if ($wrapperEl.children(`.${swiperParams.slideClass}[data-swiper-slide-index=\"${index}\"]`).length) {\n          return true;\n        }\n      } else if (slides[index]) return true;\n      return false;\n    }\n\n    function slideIndex(slideEl) {\n      if (isVirtual) {\n        return $(slideEl).attr('data-swiper-slide-index');\n      }\n      return $(slideEl).index();\n    }\n\n    if (!swiper.lazy.initialImageLoaded) swiper.lazy.initialImageLoaded = true;\n    if (swiper.params.watchSlidesVisibility) {\n      $wrapperEl.children(`.${swiperParams.slideVisibleClass}`).each((elIndex, slideEl) => {\n        const index = isVirtual ? $(slideEl).attr('data-swiper-slide-index') : $(slideEl).index();\n        swiper.lazy.loadInSlide(index);\n      });\n    } else if (slidesPerView > 1) {\n      for (let i = activeIndex; i < activeIndex + slidesPerView; i += 1) {\n        if (slideExist(i)) swiper.lazy.loadInSlide(i);\n      }\n    } else {\n      swiper.lazy.loadInSlide(activeIndex);\n    }\n    if (params.loadPrevNext) {\n      if (slidesPerView > 1 || (params.loadPrevNextAmount && params.loadPrevNextAmount > 1)) {\n        const amount = params.loadPrevNextAmount;\n        const spv = slidesPerView;\n        const maxIndex = Math.min(activeIndex + spv + Math.max(amount, spv), slides.length);\n        const minIndex = Math.max(activeIndex - Math.max(spv, amount), 0);\n        // Next Slides\n        for (let i = activeIndex + slidesPerView; i < maxIndex; i += 1) {\n          if (slideExist(i)) swiper.lazy.loadInSlide(i);\n        }\n        // Prev Slides\n        for (let i = minIndex; i < activeIndex; i += 1) {\n          if (slideExist(i)) swiper.lazy.loadInSlide(i);\n        }\n      } else {\n        const nextSlide = $wrapperEl.children(`.${swiperParams.slideNextClass}`);\n        if (nextSlide.length > 0) swiper.lazy.loadInSlide(slideIndex(nextSlide));\n\n        const prevSlide = $wrapperEl.children(`.${swiperParams.slidePrevClass}`);\n        if (prevSlide.length > 0) swiper.lazy.loadInSlide(slideIndex(prevSlide));\n      }\n    }\n  },\n};\n\nvar Lazy$1 = {\n  name: 'lazy',\n  params: {\n    lazy: {\n      enabled: false,\n      loadPrevNext: false,\n      loadPrevNextAmount: 1,\n      loadOnTransitionStart: false,\n\n      elementClass: 'swiper-lazy',\n      loadingClass: 'swiper-lazy-loading',\n      loadedClass: 'swiper-lazy-loaded',\n      preloaderClass: 'swiper-lazy-preloader',\n    },\n  },\n  create() {\n    const swiper = this;\n    Utils.extend(swiper, {\n      lazy: {\n        initialImageLoaded: false,\n        load: Lazy.load.bind(swiper),\n        loadInSlide: Lazy.loadInSlide.bind(swiper),\n      },\n    });\n  },\n  on: {\n    beforeInit() {\n      const swiper = this;\n      if (swiper.params.lazy.enabled && swiper.params.preloadImages) {\n        swiper.params.preloadImages = false;\n      }\n    },\n    init() {\n      const swiper = this;\n      if (swiper.params.lazy.enabled && !swiper.params.loop && swiper.params.initialSlide === 0) {\n        swiper.lazy.load();\n      }\n    },\n    scroll() {\n      const swiper = this;\n      if (swiper.params.freeMode && !swiper.params.freeModeSticky) {\n        swiper.lazy.load();\n      }\n    },\n    resize() {\n      const swiper = this;\n      if (swiper.params.lazy.enabled) {\n        swiper.lazy.load();\n      }\n    },\n    scrollbarDragMove() {\n      const swiper = this;\n      if (swiper.params.lazy.enabled) {\n        swiper.lazy.load();\n      }\n    },\n    transitionStart() {\n      const swiper = this;\n      if (swiper.params.lazy.enabled) {\n        if (swiper.params.lazy.loadOnTransitionStart || (!swiper.params.lazy.loadOnTransitionStart && !swiper.lazy.initialImageLoaded)) {\n          swiper.lazy.load();\n        }\n      }\n    },\n    transitionEnd() {\n      const swiper = this;\n      if (swiper.params.lazy.enabled && !swiper.params.lazy.loadOnTransitionStart) {\n        swiper.lazy.load();\n      }\n    },\n    slideChange() {\n      const swiper = this;\n      if (swiper.params.lazy.enabled && swiper.params.cssMode) {\n        swiper.lazy.load();\n      }\n    },\n  },\n};\n\n/* eslint no-bitwise: [\"error\", { \"allow\": [\">>\"] }] */\n\nconst Controller = {\n  LinearSpline: function LinearSpline(x, y) {\n    const binarySearch = (function search() {\n      let maxIndex;\n      let minIndex;\n      let guess;\n      return (array, val) => {\n        minIndex = -1;\n        maxIndex = array.length;\n        while (maxIndex - minIndex > 1) {\n          guess = maxIndex + minIndex >> 1;\n          if (array[guess] <= val) {\n            minIndex = guess;\n          } else {\n            maxIndex = guess;\n          }\n        }\n        return maxIndex;\n      };\n    }());\n    this.x = x;\n    this.y = y;\n    this.lastIndex = x.length - 1;\n    // Given an x value (x2), return the expected y2 value:\n    // (x1,y1) is the known point before given value,\n    // (x3,y3) is the known point after given value.\n    let i1;\n    let i3;\n\n    this.interpolate = function interpolate(x2) {\n      if (!x2) return 0;\n\n      // Get the indexes of x1 and x3 (the array indexes before and after given x2):\n      i3 = binarySearch(this.x, x2);\n      i1 = i3 - 1;\n\n      // We have our indexes i1 & i3, so we can calculate already:\n      // y2 := ((x2−x1) × (y3−y1)) ÷ (x3−x1) + y1\n      return (((x2 - this.x[i1]) * (this.y[i3] - this.y[i1])) / (this.x[i3] - this.x[i1])) + this.y[i1];\n    };\n    return this;\n  },\n  // xxx: for now i will just save one spline function to to\n  getInterpolateFunction(c) {\n    const swiper = this;\n    if (!swiper.controller.spline) {\n      swiper.controller.spline = swiper.params.loop\n        ? new Controller.LinearSpline(swiper.slidesGrid, c.slidesGrid)\n        : new Controller.LinearSpline(swiper.snapGrid, c.snapGrid);\n    }\n  },\n  setTranslate(setTranslate, byController) {\n    const swiper = this;\n    const controlled = swiper.controller.control;\n    let multiplier;\n    let controlledTranslate;\n    function setControlledTranslate(c) {\n      // this will create an Interpolate function based on the snapGrids\n      // x is the Grid of the scrolled scroller and y will be the controlled scroller\n      // it makes sense to create this only once and recall it for the interpolation\n      // the function does a lot of value caching for performance\n      const translate = swiper.rtlTranslate ? -swiper.translate : swiper.translate;\n      if (swiper.params.controller.by === 'slide') {\n        swiper.controller.getInterpolateFunction(c);\n        // i am not sure why the values have to be multiplicated this way, tried to invert the snapGrid\n        // but it did not work out\n        controlledTranslate = -swiper.controller.spline.interpolate(-translate);\n      }\n\n      if (!controlledTranslate || swiper.params.controller.by === 'container') {\n        multiplier = (c.maxTranslate() - c.minTranslate()) / (swiper.maxTranslate() - swiper.minTranslate());\n        controlledTranslate = ((translate - swiper.minTranslate()) * multiplier) + c.minTranslate();\n      }\n\n      if (swiper.params.controller.inverse) {\n        controlledTranslate = c.maxTranslate() - controlledTranslate;\n      }\n      c.updateProgress(controlledTranslate);\n      c.setTranslate(controlledTranslate, swiper);\n      c.updateActiveIndex();\n      c.updateSlidesClasses();\n    }\n    if (Array.isArray(controlled)) {\n      for (let i = 0; i < controlled.length; i += 1) {\n        if (controlled[i] !== byController && controlled[i] instanceof Swiper) {\n          setControlledTranslate(controlled[i]);\n        }\n      }\n    } else if (controlled instanceof Swiper && byController !== controlled) {\n      setControlledTranslate(controlled);\n    }\n  },\n  setTransition(duration, byController) {\n    const swiper = this;\n    const controlled = swiper.controller.control;\n    let i;\n    function setControlledTransition(c) {\n      c.setTransition(duration, swiper);\n      if (duration !== 0) {\n        c.transitionStart();\n        if (c.params.autoHeight) {\n          Utils.nextTick(() => {\n            c.updateAutoHeight();\n          });\n        }\n        c.$wrapperEl.transitionEnd(() => {\n          if (!controlled) return;\n          if (c.params.loop && swiper.params.controller.by === 'slide') {\n            c.loopFix();\n          }\n          c.transitionEnd();\n        });\n      }\n    }\n    if (Array.isArray(controlled)) {\n      for (i = 0; i < controlled.length; i += 1) {\n        if (controlled[i] !== byController && controlled[i] instanceof Swiper) {\n          setControlledTransition(controlled[i]);\n        }\n      }\n    } else if (controlled instanceof Swiper && byController !== controlled) {\n      setControlledTransition(controlled);\n    }\n  },\n};\nvar Controller$1 = {\n  name: 'controller',\n  params: {\n    controller: {\n      control: undefined,\n      inverse: false,\n      by: 'slide', // or 'container'\n    },\n  },\n  create() {\n    const swiper = this;\n    Utils.extend(swiper, {\n      controller: {\n        control: swiper.params.controller.control,\n        getInterpolateFunction: Controller.getInterpolateFunction.bind(swiper),\n        setTranslate: Controller.setTranslate.bind(swiper),\n        setTransition: Controller.setTransition.bind(swiper),\n      },\n    });\n  },\n  on: {\n    update() {\n      const swiper = this;\n      if (!swiper.controller.control) return;\n      if (swiper.controller.spline) {\n        swiper.controller.spline = undefined;\n        delete swiper.controller.spline;\n      }\n    },\n    resize() {\n      const swiper = this;\n      if (!swiper.controller.control) return;\n      if (swiper.controller.spline) {\n        swiper.controller.spline = undefined;\n        delete swiper.controller.spline;\n      }\n    },\n    observerUpdate() {\n      const swiper = this;\n      if (!swiper.controller.control) return;\n      if (swiper.controller.spline) {\n        swiper.controller.spline = undefined;\n        delete swiper.controller.spline;\n      }\n    },\n    setTranslate(translate, byController) {\n      const swiper = this;\n      if (!swiper.controller.control) return;\n      swiper.controller.setTranslate(translate, byController);\n    },\n    setTransition(duration, byController) {\n      const swiper = this;\n      if (!swiper.controller.control) return;\n      swiper.controller.setTransition(duration, byController);\n    },\n  },\n};\n\nconst a11y = {\n  makeElFocusable($el) {\n    $el.attr('tabIndex', '0');\n    return $el;\n  },\n  makeElNotFocusable($el) {\n    $el.attr('tabIndex', '-1');\n    return $el;\n  },\n  addElRole($el, role) {\n    $el.attr('role', role);\n    return $el;\n  },\n  addElLabel($el, label) {\n    $el.attr('aria-label', label);\n    return $el;\n  },\n  disableEl($el) {\n    $el.attr('aria-disabled', true);\n    return $el;\n  },\n  enableEl($el) {\n    $el.attr('aria-disabled', false);\n    return $el;\n  },\n  onEnterKey(e) {\n    const swiper = this;\n    const params = swiper.params.a11y;\n    if (e.keyCode !== 13) return;\n    const $targetEl = $(e.target);\n    if (swiper.navigation && swiper.navigation.$nextEl && $targetEl.is(swiper.navigation.$nextEl)) {\n      if (!(swiper.isEnd && !swiper.params.loop)) {\n        swiper.slideNext();\n      }\n      if (swiper.isEnd) {\n        swiper.a11y.notify(params.lastSlideMessage);\n      } else {\n        swiper.a11y.notify(params.nextSlideMessage);\n      }\n    }\n    if (swiper.navigation && swiper.navigation.$prevEl && $targetEl.is(swiper.navigation.$prevEl)) {\n      if (!(swiper.isBeginning && !swiper.params.loop)) {\n        swiper.slidePrev();\n      }\n      if (swiper.isBeginning) {\n        swiper.a11y.notify(params.firstSlideMessage);\n      } else {\n        swiper.a11y.notify(params.prevSlideMessage);\n      }\n    }\n    if (swiper.pagination && $targetEl.is(`.${swiper.params.pagination.bulletClass}`)) {\n      $targetEl[0].click();\n    }\n  },\n  notify(message) {\n    const swiper = this;\n    const notification = swiper.a11y.liveRegion;\n    if (notification.length === 0) return;\n    notification.html('');\n    notification.html(message);\n  },\n  updateNavigation() {\n    const swiper = this;\n\n    if (swiper.params.loop || !swiper.navigation) return;\n    const { $nextEl, $prevEl } = swiper.navigation;\n\n    if ($prevEl && $prevEl.length > 0) {\n      if (swiper.isBeginning) {\n        swiper.a11y.disableEl($prevEl);\n        swiper.a11y.makeElNotFocusable($prevEl);\n      } else {\n        swiper.a11y.enableEl($prevEl);\n        swiper.a11y.makeElFocusable($prevEl);\n      }\n    }\n    if ($nextEl && $nextEl.length > 0) {\n      if (swiper.isEnd) {\n        swiper.a11y.disableEl($nextEl);\n        swiper.a11y.makeElNotFocusable($nextEl);\n      } else {\n        swiper.a11y.enableEl($nextEl);\n        swiper.a11y.makeElFocusable($nextEl);\n      }\n    }\n  },\n  updatePagination() {\n    const swiper = this;\n    const params = swiper.params.a11y;\n    if (swiper.pagination && swiper.params.pagination.clickable && swiper.pagination.bullets && swiper.pagination.bullets.length) {\n      swiper.pagination.bullets.each((bulletIndex, bulletEl) => {\n        const $bulletEl = $(bulletEl);\n        swiper.a11y.makeElFocusable($bulletEl);\n        swiper.a11y.addElRole($bulletEl, 'button');\n        swiper.a11y.addElLabel($bulletEl, params.paginationBulletMessage.replace(/\\{\\{index\\}\\}/, $bulletEl.index() + 1));\n      });\n    }\n  },\n  init() {\n    const swiper = this;\n\n    swiper.$el.append(swiper.a11y.liveRegion);\n\n    // Navigation\n    const params = swiper.params.a11y;\n    let $nextEl;\n    let $prevEl;\n    if (swiper.navigation && swiper.navigation.$nextEl) {\n      $nextEl = swiper.navigation.$nextEl;\n    }\n    if (swiper.navigation && swiper.navigation.$prevEl) {\n      $prevEl = swiper.navigation.$prevEl;\n    }\n    if ($nextEl) {\n      swiper.a11y.makeElFocusable($nextEl);\n      swiper.a11y.addElRole($nextEl, 'button');\n      swiper.a11y.addElLabel($nextEl, params.nextSlideMessage);\n      $nextEl.on('keydown', swiper.a11y.onEnterKey);\n    }\n    if ($prevEl) {\n      swiper.a11y.makeElFocusable($prevEl);\n      swiper.a11y.addElRole($prevEl, 'button');\n      swiper.a11y.addElLabel($prevEl, params.prevSlideMessage);\n      $prevEl.on('keydown', swiper.a11y.onEnterKey);\n    }\n\n    // Pagination\n    if (swiper.pagination && swiper.params.pagination.clickable && swiper.pagination.bullets && swiper.pagination.bullets.length) {\n      swiper.pagination.$el.on('keydown', `.${swiper.params.pagination.bulletClass}`, swiper.a11y.onEnterKey);\n    }\n  },\n  destroy() {\n    const swiper = this;\n    if (swiper.a11y.liveRegion && swiper.a11y.liveRegion.length > 0) swiper.a11y.liveRegion.remove();\n\n    let $nextEl;\n    let $prevEl;\n    if (swiper.navigation && swiper.navigation.$nextEl) {\n      $nextEl = swiper.navigation.$nextEl;\n    }\n    if (swiper.navigation && swiper.navigation.$prevEl) {\n      $prevEl = swiper.navigation.$prevEl;\n    }\n    if ($nextEl) {\n      $nextEl.off('keydown', swiper.a11y.onEnterKey);\n    }\n    if ($prevEl) {\n      $prevEl.off('keydown', swiper.a11y.onEnterKey);\n    }\n\n    // Pagination\n    if (swiper.pagination && swiper.params.pagination.clickable && swiper.pagination.bullets && swiper.pagination.bullets.length) {\n      swiper.pagination.$el.off('keydown', `.${swiper.params.pagination.bulletClass}`, swiper.a11y.onEnterKey);\n    }\n  },\n};\nvar A11y = {\n  name: 'a11y',\n  params: {\n    a11y: {\n      enabled: true,\n      notificationClass: 'swiper-notification',\n      prevSlideMessage: 'Previous slide',\n      nextSlideMessage: 'Next slide',\n      firstSlideMessage: 'This is the first slide',\n      lastSlideMessage: 'This is the last slide',\n      paginationBulletMessage: 'Go to slide {{index}}',\n    },\n  },\n  create() {\n    const swiper = this;\n    Utils.extend(swiper, {\n      a11y: {\n        liveRegion: $(`<span class=\"${swiper.params.a11y.notificationClass}\" aria-live=\"assertive\" aria-atomic=\"true\"></span>`),\n      },\n    });\n    Object.keys(a11y).forEach((methodName) => {\n      swiper.a11y[methodName] = a11y[methodName].bind(swiper);\n    });\n  },\n  on: {\n    init() {\n      const swiper = this;\n      if (!swiper.params.a11y.enabled) return;\n      swiper.a11y.init();\n      swiper.a11y.updateNavigation();\n    },\n    toEdge() {\n      const swiper = this;\n      if (!swiper.params.a11y.enabled) return;\n      swiper.a11y.updateNavigation();\n    },\n    fromEdge() {\n      const swiper = this;\n      if (!swiper.params.a11y.enabled) return;\n      swiper.a11y.updateNavigation();\n    },\n    paginationUpdate() {\n      const swiper = this;\n      if (!swiper.params.a11y.enabled) return;\n      swiper.a11y.updatePagination();\n    },\n    destroy() {\n      const swiper = this;\n      if (!swiper.params.a11y.enabled) return;\n      swiper.a11y.destroy();\n    },\n  },\n};\n\nconst History = {\n  init() {\n    const swiper = this;\n    if (!swiper.params.history) return;\n    if (!window.history || !window.history.pushState) {\n      swiper.params.history.enabled = false;\n      swiper.params.hashNavigation.enabled = true;\n      return;\n    }\n    const history = swiper.history;\n    history.initialized = true;\n    history.paths = History.getPathValues();\n    if (!history.paths.key && !history.paths.value) return;\n    history.scrollToSlide(0, history.paths.value, swiper.params.runCallbacksOnInit);\n    if (!swiper.params.history.replaceState) {\n      window.addEventListener('popstate', swiper.history.setHistoryPopState);\n    }\n  },\n  destroy() {\n    const swiper = this;\n    if (!swiper.params.history.replaceState) {\n      window.removeEventListener('popstate', swiper.history.setHistoryPopState);\n    }\n  },\n  setHistoryPopState() {\n    const swiper = this;\n    swiper.history.paths = History.getPathValues();\n    swiper.history.scrollToSlide(swiper.params.speed, swiper.history.paths.value, false);\n  },\n  getPathValues() {\n    const pathArray = window.location.pathname.slice(1).split('/').filter((part) => part !== '');\n    const total = pathArray.length;\n    const key = pathArray[total - 2];\n    const value = pathArray[total - 1];\n    return { key, value };\n  },\n  setHistory(key, index) {\n    const swiper = this;\n    if (!swiper.history.initialized || !swiper.params.history.enabled) return;\n    const slide = swiper.slides.eq(index);\n    let value = History.slugify(slide.attr('data-history'));\n    if (!window.location.pathname.includes(key)) {\n      value = `${key}/${value}`;\n    }\n    const currentState = window.history.state;\n    if (currentState && currentState.value === value) {\n      return;\n    }\n    if (swiper.params.history.replaceState) {\n      window.history.replaceState({ value }, null, value);\n    } else {\n      window.history.pushState({ value }, null, value);\n    }\n  },\n  slugify(text) {\n    return text.toString()\n      .replace(/\\s+/g, '-')\n      .replace(/[^\\w-]+/g, '')\n      .replace(/--+/g, '-')\n      .replace(/^-+/, '')\n      .replace(/-+$/, '');\n  },\n  scrollToSlide(speed, value, runCallbacks) {\n    const swiper = this;\n    if (value) {\n      for (let i = 0, length = swiper.slides.length; i < length; i += 1) {\n        const slide = swiper.slides.eq(i);\n        const slideHistory = History.slugify(slide.attr('data-history'));\n        if (slideHistory === value && !slide.hasClass(swiper.params.slideDuplicateClass)) {\n          const index = slide.index();\n          swiper.slideTo(index, speed, runCallbacks);\n        }\n      }\n    } else {\n      swiper.slideTo(0, speed, runCallbacks);\n    }\n  },\n};\n\nvar History$1 = {\n  name: 'history',\n  params: {\n    history: {\n      enabled: false,\n      replaceState: false,\n      key: 'slides',\n    },\n  },\n  create() {\n    const swiper = this;\n    Utils.extend(swiper, {\n      history: {\n        init: History.init.bind(swiper),\n        setHistory: History.setHistory.bind(swiper),\n        setHistoryPopState: History.setHistoryPopState.bind(swiper),\n        scrollToSlide: History.scrollToSlide.bind(swiper),\n        destroy: History.destroy.bind(swiper),\n      },\n    });\n  },\n  on: {\n    init() {\n      const swiper = this;\n      if (swiper.params.history.enabled) {\n        swiper.history.init();\n      }\n    },\n    destroy() {\n      const swiper = this;\n      if (swiper.params.history.enabled) {\n        swiper.history.destroy();\n      }\n    },\n    transitionEnd() {\n      const swiper = this;\n      if (swiper.history.initialized) {\n        swiper.history.setHistory(swiper.params.history.key, swiper.activeIndex);\n      }\n    },\n    slideChange() {\n      const swiper = this;\n      if (swiper.history.initialized && swiper.params.cssMode) {\n        swiper.history.setHistory(swiper.params.history.key, swiper.activeIndex);\n      }\n    },\n  },\n};\n\nconst HashNavigation = {\n  onHashCange() {\n    const swiper = this;\n    swiper.emit('hashChange');\n    const newHash = document$1.location.hash.replace('#', '');\n    const activeSlideHash = swiper.slides.eq(swiper.activeIndex).attr('data-hash');\n    if (newHash !== activeSlideHash) {\n      const newIndex = swiper.$wrapperEl.children(`.${swiper.params.slideClass}[data-hash=\"${newHash}\"]`).index();\n      if (typeof newIndex === 'undefined') return;\n      swiper.slideTo(newIndex);\n    }\n  },\n  setHash() {\n    const swiper = this;\n    if (!swiper.hashNavigation.initialized || !swiper.params.hashNavigation.enabled) return;\n    if (swiper.params.hashNavigation.replaceState && window.history && window.history.replaceState) {\n      window.history.replaceState(null, null, (`#${swiper.slides.eq(swiper.activeIndex).attr('data-hash')}` || ''));\n      swiper.emit('hashSet');\n    } else {\n      const slide = swiper.slides.eq(swiper.activeIndex);\n      const hash = slide.attr('data-hash') || slide.attr('data-history');\n      document$1.location.hash = hash || '';\n      swiper.emit('hashSet');\n    }\n  },\n  init() {\n    const swiper = this;\n    if (!swiper.params.hashNavigation.enabled || (swiper.params.history && swiper.params.history.enabled)) return;\n    swiper.hashNavigation.initialized = true;\n    const hash = document$1.location.hash.replace('#', '');\n    if (hash) {\n      const speed = 0;\n      for (let i = 0, length = swiper.slides.length; i < length; i += 1) {\n        const slide = swiper.slides.eq(i);\n        const slideHash = slide.attr('data-hash') || slide.attr('data-history');\n        if (slideHash === hash && !slide.hasClass(swiper.params.slideDuplicateClass)) {\n          const index = slide.index();\n          swiper.slideTo(index, speed, swiper.params.runCallbacksOnInit, true);\n        }\n      }\n    }\n    if (swiper.params.hashNavigation.watchState) {\n      $(window).on('hashchange', swiper.hashNavigation.onHashCange);\n    }\n  },\n  destroy() {\n    const swiper = this;\n    if (swiper.params.hashNavigation.watchState) {\n      $(window).off('hashchange', swiper.hashNavigation.onHashCange);\n    }\n  },\n};\nvar HashNavigation$1 = {\n  name: 'hash-navigation',\n  params: {\n    hashNavigation: {\n      enabled: false,\n      replaceState: false,\n      watchState: false,\n    },\n  },\n  create() {\n    const swiper = this;\n    Utils.extend(swiper, {\n      hashNavigation: {\n        initialized: false,\n        init: HashNavigation.init.bind(swiper),\n        destroy: HashNavigation.destroy.bind(swiper),\n        setHash: HashNavigation.setHash.bind(swiper),\n        onHashCange: HashNavigation.onHashCange.bind(swiper),\n      },\n    });\n  },\n  on: {\n    init() {\n      const swiper = this;\n      if (swiper.params.hashNavigation.enabled) {\n        swiper.hashNavigation.init();\n      }\n    },\n    destroy() {\n      const swiper = this;\n      if (swiper.params.hashNavigation.enabled) {\n        swiper.hashNavigation.destroy();\n      }\n    },\n    transitionEnd() {\n      const swiper = this;\n      if (swiper.hashNavigation.initialized) {\n        swiper.hashNavigation.setHash();\n      }\n    },\n    slideChange() {\n      const swiper = this;\n      if (swiper.hashNavigation.initialized && swiper.params.cssMode) {\n        swiper.hashNavigation.setHash();\n      }\n    },\n  },\n};\n\n/* eslint no-underscore-dangle: \"off\" */\n\nconst Autoplay = {\n  run() {\n    const swiper = this;\n    const $activeSlideEl = swiper.slides.eq(swiper.activeIndex);\n    let delay = swiper.params.autoplay.delay;\n    if ($activeSlideEl.attr('data-swiper-autoplay')) {\n      delay = $activeSlideEl.attr('data-swiper-autoplay') || swiper.params.autoplay.delay;\n    }\n    clearTimeout(swiper.autoplay.timeout);\n    swiper.autoplay.timeout = Utils.nextTick(() => {\n      if (swiper.params.autoplay.reverseDirection) {\n        if (swiper.params.loop) {\n          swiper.loopFix();\n          swiper.slidePrev(swiper.params.speed, true, true);\n          swiper.emit('autoplay');\n        } else if (!swiper.isBeginning) {\n          swiper.slidePrev(swiper.params.speed, true, true);\n          swiper.emit('autoplay');\n        } else if (!swiper.params.autoplay.stopOnLastSlide) {\n          swiper.slideTo(swiper.slides.length - 1, swiper.params.speed, true, true);\n          swiper.emit('autoplay');\n        } else {\n          swiper.autoplay.stop();\n        }\n      } else if (swiper.params.loop) {\n        swiper.loopFix();\n        swiper.slideNext(swiper.params.speed, true, true);\n        swiper.emit('autoplay');\n      } else if (!swiper.isEnd) {\n        swiper.slideNext(swiper.params.speed, true, true);\n        swiper.emit('autoplay');\n      } else if (!swiper.params.autoplay.stopOnLastSlide) {\n        swiper.slideTo(0, swiper.params.speed, true, true);\n        swiper.emit('autoplay');\n      } else {\n        swiper.autoplay.stop();\n      }\n      if (swiper.params.cssMode && swiper.autoplay.running) swiper.autoplay.run();\n    }, delay);\n  },\n  start() {\n    const swiper = this;\n    if (typeof swiper.autoplay.timeout !== 'undefined') return false;\n    if (swiper.autoplay.running) return false;\n    swiper.autoplay.running = true;\n    swiper.emit('autoplayStart');\n    swiper.autoplay.run();\n    return true;\n  },\n  stop() {\n    const swiper = this;\n    if (!swiper.autoplay.running) return false;\n    if (typeof swiper.autoplay.timeout === 'undefined') return false;\n\n    if (swiper.autoplay.timeout) {\n      clearTimeout(swiper.autoplay.timeout);\n      swiper.autoplay.timeout = undefined;\n    }\n    swiper.autoplay.running = false;\n    swiper.emit('autoplayStop');\n    return true;\n  },\n  pause(speed) {\n    const swiper = this;\n    if (!swiper.autoplay.running) return;\n    if (swiper.autoplay.paused) return;\n    if (swiper.autoplay.timeout) clearTimeout(swiper.autoplay.timeout);\n    swiper.autoplay.paused = true;\n    if (speed === 0 || !swiper.params.autoplay.waitForTransition) {\n      swiper.autoplay.paused = false;\n      swiper.autoplay.run();\n    } else {\n      swiper.$wrapperEl[0].addEventListener('transitionend', swiper.autoplay.onTransitionEnd);\n      swiper.$wrapperEl[0].addEventListener('webkitTransitionEnd', swiper.autoplay.onTransitionEnd);\n    }\n  },\n};\n\nvar Autoplay$1 = {\n  name: 'autoplay',\n  params: {\n    autoplay: {\n      enabled: false,\n      delay: 3000,\n      waitForTransition: true,\n      disableOnInteraction: true,\n      stopOnLastSlide: false,\n      reverseDirection: false,\n    },\n  },\n  create() {\n    const swiper = this;\n    Utils.extend(swiper, {\n      autoplay: {\n        running: false,\n        paused: false,\n        run: Autoplay.run.bind(swiper),\n        start: Autoplay.start.bind(swiper),\n        stop: Autoplay.stop.bind(swiper),\n        pause: Autoplay.pause.bind(swiper),\n        onVisibilityChange() {\n          if (document.visibilityState === 'hidden' && swiper.autoplay.running) {\n            swiper.autoplay.pause();\n          }\n          if (document.visibilityState === 'visible' && swiper.autoplay.paused) {\n            swiper.autoplay.run();\n            swiper.autoplay.paused = false;\n          }\n        },\n        onTransitionEnd(e) {\n          if (!swiper || swiper.destroyed || !swiper.$wrapperEl) return;\n          if (e.target !== this) return;\n          swiper.$wrapperEl[0].removeEventListener('transitionend', swiper.autoplay.onTransitionEnd);\n          swiper.$wrapperEl[0].removeEventListener('webkitTransitionEnd', swiper.autoplay.onTransitionEnd);\n          swiper.autoplay.paused = false;\n          if (!swiper.autoplay.running) {\n            swiper.autoplay.stop();\n          } else {\n            swiper.autoplay.run();\n          }\n        },\n      },\n    });\n  },\n  on: {\n    init() {\n      const swiper = this;\n      if (swiper.params.autoplay.enabled) {\n        swiper.autoplay.start();\n        document.addEventListener('visibilitychange', swiper.autoplay.onVisibilityChange);\n      }\n    },\n    beforeTransitionStart(speed, internal) {\n      const swiper = this;\n      if (swiper.autoplay.running) {\n        if (internal || !swiper.params.autoplay.disableOnInteraction) {\n          swiper.autoplay.pause(speed);\n        } else {\n          swiper.autoplay.stop();\n        }\n      }\n    },\n    sliderFirstMove() {\n      const swiper = this;\n      if (swiper.autoplay.running) {\n        if (swiper.params.autoplay.disableOnInteraction) {\n          swiper.autoplay.stop();\n        } else {\n          swiper.autoplay.pause();\n        }\n      }\n    },\n    touchEnd() {\n      const swiper = this;\n      if (swiper.params.cssMode && swiper.autoplay.paused && !swiper.params.autoplay.disableOnInteraction) {\n        swiper.autoplay.run();\n      }\n    },\n    destroy() {\n      const swiper = this;\n      if (swiper.autoplay.running) {\n        swiper.autoplay.stop();\n      }\n      document.removeEventListener('visibilitychange', swiper.autoplay.onVisibilityChange);\n    },\n  },\n};\n\nconst Fade = {\n  setTranslate() {\n    const swiper = this;\n    const { slides } = swiper;\n    for (let i = 0; i < slides.length; i += 1) {\n      const $slideEl = swiper.slides.eq(i);\n      const offset = $slideEl[0].swiperSlideOffset;\n      let tx = -offset;\n      if (!swiper.params.virtualTranslate) tx -= swiper.translate;\n      let ty = 0;\n      if (!swiper.isHorizontal()) {\n        ty = tx;\n        tx = 0;\n      }\n      const slideOpacity = swiper.params.fadeEffect.crossFade\n        ? Math.max(1 - Math.abs($slideEl[0].progress), 0)\n        : 1 + Math.min(Math.max($slideEl[0].progress, -1), 0);\n      $slideEl\n        .css({\n          opacity: slideOpacity,\n        })\n        .transform(`translate3d(${tx}px, ${ty}px, 0px)`);\n    }\n  },\n  setTransition(duration) {\n    const swiper = this;\n    const { slides, $wrapperEl } = swiper;\n    slides.transition(duration);\n    if (swiper.params.virtualTranslate && duration !== 0) {\n      let eventTriggered = false;\n      slides.transitionEnd(() => {\n        if (eventTriggered) return;\n        if (!swiper || swiper.destroyed) return;\n        eventTriggered = true;\n        swiper.animating = false;\n        const triggerEvents = ['webkitTransitionEnd', 'transitionend'];\n        for (let i = 0; i < triggerEvents.length; i += 1) {\n          $wrapperEl.trigger(triggerEvents[i]);\n        }\n      });\n    }\n  },\n};\n\nvar EffectFade = {\n  name: 'effect-fade',\n  params: {\n    fadeEffect: {\n      crossFade: false,\n    },\n  },\n  create() {\n    const swiper = this;\n    Utils.extend(swiper, {\n      fadeEffect: {\n        setTranslate: Fade.setTranslate.bind(swiper),\n        setTransition: Fade.setTransition.bind(swiper),\n      },\n    });\n  },\n  on: {\n    beforeInit() {\n      const swiper = this;\n      if (swiper.params.effect !== 'fade') return;\n      swiper.classNames.push(`${swiper.params.containerModifierClass}fade`);\n      const overwriteParams = {\n        slidesPerView: 1,\n        slidesPerColumn: 1,\n        slidesPerGroup: 1,\n        watchSlidesProgress: true,\n        spaceBetween: 0,\n        virtualTranslate: true,\n      };\n      Utils.extend(swiper.params, overwriteParams);\n      Utils.extend(swiper.originalParams, overwriteParams);\n    },\n    setTranslate() {\n      const swiper = this;\n      if (swiper.params.effect !== 'fade') return;\n      swiper.fadeEffect.setTranslate();\n    },\n    setTransition(duration) {\n      const swiper = this;\n      if (swiper.params.effect !== 'fade') return;\n      swiper.fadeEffect.setTransition(duration);\n    },\n  },\n};\n\nconst Cube = {\n  setTranslate() {\n    const swiper = this;\n    const {\n      $el, $wrapperEl, slides, width: swiperWidth, height: swiperHeight, rtlTranslate: rtl, size: swiperSize,\n    } = swiper;\n    const params = swiper.params.cubeEffect;\n    const isHorizontal = swiper.isHorizontal();\n    const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n    let wrapperRotate = 0;\n    let $cubeShadowEl;\n    if (params.shadow) {\n      if (isHorizontal) {\n        $cubeShadowEl = $wrapperEl.find('.swiper-cube-shadow');\n        if ($cubeShadowEl.length === 0) {\n          $cubeShadowEl = $('<div class=\"swiper-cube-shadow\"></div>');\n          $wrapperEl.append($cubeShadowEl);\n        }\n        $cubeShadowEl.css({ height: `${swiperWidth}px` });\n      } else {\n        $cubeShadowEl = $el.find('.swiper-cube-shadow');\n        if ($cubeShadowEl.length === 0) {\n          $cubeShadowEl = $('<div class=\"swiper-cube-shadow\"></div>');\n          $el.append($cubeShadowEl);\n        }\n      }\n    }\n    for (let i = 0; i < slides.length; i += 1) {\n      const $slideEl = slides.eq(i);\n      let slideIndex = i;\n      if (isVirtual) {\n        slideIndex = parseInt($slideEl.attr('data-swiper-slide-index'), 10);\n      }\n      let slideAngle = slideIndex * 90;\n      let round = Math.floor(slideAngle / 360);\n      if (rtl) {\n        slideAngle = -slideAngle;\n        round = Math.floor(-slideAngle / 360);\n      }\n      const progress = Math.max(Math.min($slideEl[0].progress, 1), -1);\n      let tx = 0;\n      let ty = 0;\n      let tz = 0;\n      if (slideIndex % 4 === 0) {\n        tx = -round * 4 * swiperSize;\n        tz = 0;\n      } else if ((slideIndex - 1) % 4 === 0) {\n        tx = 0;\n        tz = -round * 4 * swiperSize;\n      } else if ((slideIndex - 2) % 4 === 0) {\n        tx = swiperSize + (round * 4 * swiperSize);\n        tz = swiperSize;\n      } else if ((slideIndex - 3) % 4 === 0) {\n        tx = -swiperSize;\n        tz = (3 * swiperSize) + (swiperSize * 4 * round);\n      }\n      if (rtl) {\n        tx = -tx;\n      }\n\n      if (!isHorizontal) {\n        ty = tx;\n        tx = 0;\n      }\n\n      const transform = `rotateX(${isHorizontal ? 0 : -slideAngle}deg) rotateY(${isHorizontal ? slideAngle : 0}deg) translate3d(${tx}px, ${ty}px, ${tz}px)`;\n      if (progress <= 1 && progress > -1) {\n        wrapperRotate = (slideIndex * 90) + (progress * 90);\n        if (rtl) wrapperRotate = (-slideIndex * 90) - (progress * 90);\n      }\n      $slideEl.transform(transform);\n      if (params.slideShadows) {\n        // Set shadows\n        let shadowBefore = isHorizontal ? $slideEl.find('.swiper-slide-shadow-left') : $slideEl.find('.swiper-slide-shadow-top');\n        let shadowAfter = isHorizontal ? $slideEl.find('.swiper-slide-shadow-right') : $slideEl.find('.swiper-slide-shadow-bottom');\n        if (shadowBefore.length === 0) {\n          shadowBefore = $(`<div class=\"swiper-slide-shadow-${isHorizontal ? 'left' : 'top'}\"></div>`);\n          $slideEl.append(shadowBefore);\n        }\n        if (shadowAfter.length === 0) {\n          shadowAfter = $(`<div class=\"swiper-slide-shadow-${isHorizontal ? 'right' : 'bottom'}\"></div>`);\n          $slideEl.append(shadowAfter);\n        }\n        if (shadowBefore.length) shadowBefore[0].style.opacity = Math.max(-progress, 0);\n        if (shadowAfter.length) shadowAfter[0].style.opacity = Math.max(progress, 0);\n      }\n    }\n    $wrapperEl.css({\n      '-webkit-transform-origin': `50% 50% -${swiperSize / 2}px`,\n      '-moz-transform-origin': `50% 50% -${swiperSize / 2}px`,\n      '-ms-transform-origin': `50% 50% -${swiperSize / 2}px`,\n      'transform-origin': `50% 50% -${swiperSize / 2}px`,\n    });\n\n    if (params.shadow) {\n      if (isHorizontal) {\n        $cubeShadowEl.transform(`translate3d(0px, ${(swiperWidth / 2) + params.shadowOffset}px, ${-swiperWidth / 2}px) rotateX(90deg) rotateZ(0deg) scale(${params.shadowScale})`);\n      } else {\n        const shadowAngle = Math.abs(wrapperRotate) - (Math.floor(Math.abs(wrapperRotate) / 90) * 90);\n        const multiplier = 1.5 - (\n          (Math.sin((shadowAngle * 2 * Math.PI) / 360) / 2)\n          + (Math.cos((shadowAngle * 2 * Math.PI) / 360) / 2)\n        );\n        const scale1 = params.shadowScale;\n        const scale2 = params.shadowScale / multiplier;\n        const offset = params.shadowOffset;\n        $cubeShadowEl.transform(`scale3d(${scale1}, 1, ${scale2}) translate3d(0px, ${(swiperHeight / 2) + offset}px, ${-swiperHeight / 2 / scale2}px) rotateX(-90deg)`);\n      }\n    }\n    const zFactor = (Browser.isSafari || Browser.isUiWebView) ? (-swiperSize / 2) : 0;\n    $wrapperEl\n      .transform(`translate3d(0px,0,${zFactor}px) rotateX(${swiper.isHorizontal() ? 0 : wrapperRotate}deg) rotateY(${swiper.isHorizontal() ? -wrapperRotate : 0}deg)`);\n  },\n  setTransition(duration) {\n    const swiper = this;\n    const { $el, slides } = swiper;\n    slides\n      .transition(duration)\n      .find('.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left')\n      .transition(duration);\n    if (swiper.params.cubeEffect.shadow && !swiper.isHorizontal()) {\n      $el.find('.swiper-cube-shadow').transition(duration);\n    }\n  },\n};\n\nvar EffectCube = {\n  name: 'effect-cube',\n  params: {\n    cubeEffect: {\n      slideShadows: true,\n      shadow: true,\n      shadowOffset: 20,\n      shadowScale: 0.94,\n    },\n  },\n  create() {\n    const swiper = this;\n    Utils.extend(swiper, {\n      cubeEffect: {\n        setTranslate: Cube.setTranslate.bind(swiper),\n        setTransition: Cube.setTransition.bind(swiper),\n      },\n    });\n  },\n  on: {\n    beforeInit() {\n      const swiper = this;\n      if (swiper.params.effect !== 'cube') return;\n      swiper.classNames.push(`${swiper.params.containerModifierClass}cube`);\n      swiper.classNames.push(`${swiper.params.containerModifierClass}3d`);\n      const overwriteParams = {\n        slidesPerView: 1,\n        slidesPerColumn: 1,\n        slidesPerGroup: 1,\n        watchSlidesProgress: true,\n        resistanceRatio: 0,\n        spaceBetween: 0,\n        centeredSlides: false,\n        virtualTranslate: true,\n      };\n      Utils.extend(swiper.params, overwriteParams);\n      Utils.extend(swiper.originalParams, overwriteParams);\n    },\n    setTranslate() {\n      const swiper = this;\n      if (swiper.params.effect !== 'cube') return;\n      swiper.cubeEffect.setTranslate();\n    },\n    setTransition(duration) {\n      const swiper = this;\n      if (swiper.params.effect !== 'cube') return;\n      swiper.cubeEffect.setTransition(duration);\n    },\n  },\n};\n\nconst Flip = {\n  setTranslate() {\n    const swiper = this;\n    const { slides, rtlTranslate: rtl } = swiper;\n    for (let i = 0; i < slides.length; i += 1) {\n      const $slideEl = slides.eq(i);\n      let progress = $slideEl[0].progress;\n      if (swiper.params.flipEffect.limitRotation) {\n        progress = Math.max(Math.min($slideEl[0].progress, 1), -1);\n      }\n      const offset = $slideEl[0].swiperSlideOffset;\n      const rotate = -180 * progress;\n      let rotateY = rotate;\n      let rotateX = 0;\n      let tx = -offset;\n      let ty = 0;\n      if (!swiper.isHorizontal()) {\n        ty = tx;\n        tx = 0;\n        rotateX = -rotateY;\n        rotateY = 0;\n      } else if (rtl) {\n        rotateY = -rotateY;\n      }\n\n      $slideEl[0].style.zIndex = -Math.abs(Math.round(progress)) + slides.length;\n\n      if (swiper.params.flipEffect.slideShadows) {\n        // Set shadows\n        let shadowBefore = swiper.isHorizontal() ? $slideEl.find('.swiper-slide-shadow-left') : $slideEl.find('.swiper-slide-shadow-top');\n        let shadowAfter = swiper.isHorizontal() ? $slideEl.find('.swiper-slide-shadow-right') : $slideEl.find('.swiper-slide-shadow-bottom');\n        if (shadowBefore.length === 0) {\n          shadowBefore = $(`<div class=\"swiper-slide-shadow-${swiper.isHorizontal() ? 'left' : 'top'}\"></div>`);\n          $slideEl.append(shadowBefore);\n        }\n        if (shadowAfter.length === 0) {\n          shadowAfter = $(`<div class=\"swiper-slide-shadow-${swiper.isHorizontal() ? 'right' : 'bottom'}\"></div>`);\n          $slideEl.append(shadowAfter);\n        }\n        if (shadowBefore.length) shadowBefore[0].style.opacity = Math.max(-progress, 0);\n        if (shadowAfter.length) shadowAfter[0].style.opacity = Math.max(progress, 0);\n      }\n      $slideEl\n        .transform(`translate3d(${tx}px, ${ty}px, 0px) rotateX(${rotateX}deg) rotateY(${rotateY}deg)`);\n    }\n  },\n  setTransition(duration) {\n    const swiper = this;\n    const { slides, activeIndex, $wrapperEl } = swiper;\n    slides\n      .transition(duration)\n      .find('.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left')\n      .transition(duration);\n    if (swiper.params.virtualTranslate && duration !== 0) {\n      let eventTriggered = false;\n      // eslint-disable-next-line\n      slides.eq(activeIndex).transitionEnd(function onTransitionEnd() {\n        if (eventTriggered) return;\n        if (!swiper || swiper.destroyed) return;\n        // if (!$(this).hasClass(swiper.params.slideActiveClass)) return;\n        eventTriggered = true;\n        swiper.animating = false;\n        const triggerEvents = ['webkitTransitionEnd', 'transitionend'];\n        for (let i = 0; i < triggerEvents.length; i += 1) {\n          $wrapperEl.trigger(triggerEvents[i]);\n        }\n      });\n    }\n  },\n};\n\nvar EffectFlip = {\n  name: 'effect-flip',\n  params: {\n    flipEffect: {\n      slideShadows: true,\n      limitRotation: true,\n    },\n  },\n  create() {\n    const swiper = this;\n    Utils.extend(swiper, {\n      flipEffect: {\n        setTranslate: Flip.setTranslate.bind(swiper),\n        setTransition: Flip.setTransition.bind(swiper),\n      },\n    });\n  },\n  on: {\n    beforeInit() {\n      const swiper = this;\n      if (swiper.params.effect !== 'flip') return;\n      swiper.classNames.push(`${swiper.params.containerModifierClass}flip`);\n      swiper.classNames.push(`${swiper.params.containerModifierClass}3d`);\n      const overwriteParams = {\n        slidesPerView: 1,\n        slidesPerColumn: 1,\n        slidesPerGroup: 1,\n        watchSlidesProgress: true,\n        spaceBetween: 0,\n        virtualTranslate: true,\n      };\n      Utils.extend(swiper.params, overwriteParams);\n      Utils.extend(swiper.originalParams, overwriteParams);\n    },\n    setTranslate() {\n      const swiper = this;\n      if (swiper.params.effect !== 'flip') return;\n      swiper.flipEffect.setTranslate();\n    },\n    setTransition(duration) {\n      const swiper = this;\n      if (swiper.params.effect !== 'flip') return;\n      swiper.flipEffect.setTransition(duration);\n    },\n  },\n};\n\nconst Coverflow = {\n  setTranslate() {\n    const swiper = this;\n    const {\n      width: swiperWidth, height: swiperHeight, slides, $wrapperEl, slidesSizesGrid,\n    } = swiper;\n    const params = swiper.params.coverflowEffect;\n    const isHorizontal = swiper.isHorizontal();\n    const transform = swiper.translate;\n    const center = isHorizontal ? -transform + (swiperWidth / 2) : -transform + (swiperHeight / 2);\n    const rotate = isHorizontal ? params.rotate : -params.rotate;\n    const translate = params.depth;\n    // Each slide offset from center\n    for (let i = 0, length = slides.length; i < length; i += 1) {\n      const $slideEl = slides.eq(i);\n      const slideSize = slidesSizesGrid[i];\n      const slideOffset = $slideEl[0].swiperSlideOffset;\n      const offsetMultiplier = ((center - slideOffset - (slideSize / 2)) / slideSize) * params.modifier;\n\n      let rotateY = isHorizontal ? rotate * offsetMultiplier : 0;\n      let rotateX = isHorizontal ? 0 : rotate * offsetMultiplier;\n      // var rotateZ = 0\n      let translateZ = -translate * Math.abs(offsetMultiplier);\n\n      let stretch = params.stretch;\n      // Allow percentage to make a relative stretch for responsive sliders\n      if (typeof stretch === 'string' && stretch.indexOf('%') !== -1) {\n        stretch = ((parseFloat(params.stretch) / 100) * slideSize);\n      }\n      let translateY = isHorizontal ? 0 : stretch * (offsetMultiplier);\n      let translateX = isHorizontal ? stretch * (offsetMultiplier) : 0;\n\n      // Fix for ultra small values\n      if (Math.abs(translateX) < 0.001) translateX = 0;\n      if (Math.abs(translateY) < 0.001) translateY = 0;\n      if (Math.abs(translateZ) < 0.001) translateZ = 0;\n      if (Math.abs(rotateY) < 0.001) rotateY = 0;\n      if (Math.abs(rotateX) < 0.001) rotateX = 0;\n\n      const slideTransform = `translate3d(${translateX}px,${translateY}px,${translateZ}px)  rotateX(${rotateX}deg) rotateY(${rotateY}deg)`;\n\n      $slideEl.transform(slideTransform);\n      $slideEl[0].style.zIndex = -Math.abs(Math.round(offsetMultiplier)) + 1;\n      if (params.slideShadows) {\n        // Set shadows\n        let $shadowBeforeEl = isHorizontal ? $slideEl.find('.swiper-slide-shadow-left') : $slideEl.find('.swiper-slide-shadow-top');\n        let $shadowAfterEl = isHorizontal ? $slideEl.find('.swiper-slide-shadow-right') : $slideEl.find('.swiper-slide-shadow-bottom');\n        if ($shadowBeforeEl.length === 0) {\n          $shadowBeforeEl = $(`<div class=\"swiper-slide-shadow-${isHorizontal ? 'left' : 'top'}\"></div>`);\n          $slideEl.append($shadowBeforeEl);\n        }\n        if ($shadowAfterEl.length === 0) {\n          $shadowAfterEl = $(`<div class=\"swiper-slide-shadow-${isHorizontal ? 'right' : 'bottom'}\"></div>`);\n          $slideEl.append($shadowAfterEl);\n        }\n        if ($shadowBeforeEl.length) $shadowBeforeEl[0].style.opacity = offsetMultiplier > 0 ? offsetMultiplier : 0;\n        if ($shadowAfterEl.length) $shadowAfterEl[0].style.opacity = (-offsetMultiplier) > 0 ? -offsetMultiplier : 0;\n      }\n    }\n\n    // Set correct perspective for IE10\n    if (Support.pointerEvents || Support.prefixedPointerEvents) {\n      const ws = $wrapperEl[0].style;\n      ws.perspectiveOrigin = `${center}px 50%`;\n    }\n  },\n  setTransition(duration) {\n    const swiper = this;\n    swiper.slides\n      .transition(duration)\n      .find('.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left')\n      .transition(duration);\n  },\n};\n\nvar EffectCoverflow = {\n  name: 'effect-coverflow',\n  params: {\n    coverflowEffect: {\n      rotate: 50,\n      stretch: 0,\n      depth: 100,\n      modifier: 1,\n      slideShadows: true,\n    },\n  },\n  create() {\n    const swiper = this;\n    Utils.extend(swiper, {\n      coverflowEffect: {\n        setTranslate: Coverflow.setTranslate.bind(swiper),\n        setTransition: Coverflow.setTransition.bind(swiper),\n      },\n    });\n  },\n  on: {\n    beforeInit() {\n      const swiper = this;\n      if (swiper.params.effect !== 'coverflow') return;\n\n      swiper.classNames.push(`${swiper.params.containerModifierClass}coverflow`);\n      swiper.classNames.push(`${swiper.params.containerModifierClass}3d`);\n\n      swiper.params.watchSlidesProgress = true;\n      swiper.originalParams.watchSlidesProgress = true;\n    },\n    setTranslate() {\n      const swiper = this;\n      if (swiper.params.effect !== 'coverflow') return;\n      swiper.coverflowEffect.setTranslate();\n    },\n    setTransition(duration) {\n      const swiper = this;\n      if (swiper.params.effect !== 'coverflow') return;\n      swiper.coverflowEffect.setTransition(duration);\n    },\n  },\n};\n\nconst Thumbs = {\n  init() {\n    const swiper = this;\n    const { thumbs: thumbsParams } = swiper.params;\n    const SwiperClass = swiper.constructor;\n    if (thumbsParams.swiper instanceof SwiperClass) {\n      swiper.thumbs.swiper = thumbsParams.swiper;\n      Utils.extend(swiper.thumbs.swiper.originalParams, {\n        watchSlidesProgress: true,\n        slideToClickedSlide: false,\n      });\n      Utils.extend(swiper.thumbs.swiper.params, {\n        watchSlidesProgress: true,\n        slideToClickedSlide: false,\n      });\n    } else if (Utils.isObject(thumbsParams.swiper)) {\n      swiper.thumbs.swiper = new SwiperClass(Utils.extend({}, thumbsParams.swiper, {\n        watchSlidesVisibility: true,\n        watchSlidesProgress: true,\n        slideToClickedSlide: false,\n      }));\n      swiper.thumbs.swiperCreated = true;\n    }\n    swiper.thumbs.swiper.$el.addClass(swiper.params.thumbs.thumbsContainerClass);\n    swiper.thumbs.swiper.on('tap', swiper.thumbs.onThumbClick);\n  },\n  onThumbClick() {\n    const swiper = this;\n    const thumbsSwiper = swiper.thumbs.swiper;\n    if (!thumbsSwiper) return;\n    const clickedIndex = thumbsSwiper.clickedIndex;\n    const clickedSlide = thumbsSwiper.clickedSlide;\n    if (clickedSlide && $(clickedSlide).hasClass(swiper.params.thumbs.slideThumbActiveClass)) return;\n    if (typeof clickedIndex === 'undefined' || clickedIndex === null) return;\n    let slideToIndex;\n    if (thumbsSwiper.params.loop) {\n      slideToIndex = parseInt($(thumbsSwiper.clickedSlide).attr('data-swiper-slide-index'), 10);\n    } else {\n      slideToIndex = clickedIndex;\n    }\n    if (swiper.params.loop) {\n      let currentIndex = swiper.activeIndex;\n      if (swiper.slides.eq(currentIndex).hasClass(swiper.params.slideDuplicateClass)) {\n        swiper.loopFix();\n        // eslint-disable-next-line\n        swiper._clientLeft = swiper.$wrapperEl[0].clientLeft;\n        currentIndex = swiper.activeIndex;\n      }\n      const prevIndex = swiper.slides.eq(currentIndex).prevAll(`[data-swiper-slide-index=\"${slideToIndex}\"]`).eq(0).index();\n      const nextIndex = swiper.slides.eq(currentIndex).nextAll(`[data-swiper-slide-index=\"${slideToIndex}\"]`).eq(0).index();\n      if (typeof prevIndex === 'undefined') slideToIndex = nextIndex;\n      else if (typeof nextIndex === 'undefined') slideToIndex = prevIndex;\n      else if (nextIndex - currentIndex < currentIndex - prevIndex) slideToIndex = nextIndex;\n      else slideToIndex = prevIndex;\n    }\n    swiper.slideTo(slideToIndex);\n  },\n  update(initial) {\n    const swiper = this;\n    const thumbsSwiper = swiper.thumbs.swiper;\n    if (!thumbsSwiper) return;\n\n    const slidesPerView = thumbsSwiper.params.slidesPerView === 'auto'\n      ? thumbsSwiper.slidesPerViewDynamic()\n      : thumbsSwiper.params.slidesPerView;\n\n    const autoScrollOffset = swiper.params.thumbs.autoScrollOffset;\n    const useOffset = autoScrollOffset && !thumbsSwiper.params.loop;\n    if (swiper.realIndex !== thumbsSwiper.realIndex || useOffset) {\n      let currentThumbsIndex = thumbsSwiper.activeIndex;\n      let newThumbsIndex;\n      let direction;\n      if (thumbsSwiper.params.loop) {\n        if (thumbsSwiper.slides.eq(currentThumbsIndex).hasClass(thumbsSwiper.params.slideDuplicateClass)) {\n          thumbsSwiper.loopFix();\n          // eslint-disable-next-line\n          thumbsSwiper._clientLeft = thumbsSwiper.$wrapperEl[0].clientLeft;\n          currentThumbsIndex = thumbsSwiper.activeIndex;\n        }\n        // Find actual thumbs index to slide to\n        const prevThumbsIndex = thumbsSwiper.slides\n          .eq(currentThumbsIndex)\n          .prevAll(`[data-swiper-slide-index=\"${swiper.realIndex}\"]`).eq(0)\n          .index();\n        const nextThumbsIndex = thumbsSwiper.slides\n          .eq(currentThumbsIndex)\n          .nextAll(`[data-swiper-slide-index=\"${swiper.realIndex}\"]`).eq(0)\n          .index();\n        if (typeof prevThumbsIndex === 'undefined') newThumbsIndex = nextThumbsIndex;\n        else if (typeof nextThumbsIndex === 'undefined') newThumbsIndex = prevThumbsIndex;\n        else if (nextThumbsIndex - currentThumbsIndex === currentThumbsIndex - prevThumbsIndex) newThumbsIndex = currentThumbsIndex;\n        else if (nextThumbsIndex - currentThumbsIndex < currentThumbsIndex - prevThumbsIndex) newThumbsIndex = nextThumbsIndex;\n        else newThumbsIndex = prevThumbsIndex;\n        direction = swiper.activeIndex > swiper.previousIndex ? 'next' : 'prev';\n      } else {\n        newThumbsIndex = swiper.realIndex;\n        direction = newThumbsIndex > swiper.previousIndex ? 'next' : 'prev';\n      }\n      if (useOffset) {\n        newThumbsIndex += direction === 'next' ? autoScrollOffset : -1 * autoScrollOffset;\n      }\n\n      if (thumbsSwiper.visibleSlidesIndexes && thumbsSwiper.visibleSlidesIndexes.indexOf(newThumbsIndex) < 0) {\n        if (thumbsSwiper.params.centeredSlides) {\n          if (newThumbsIndex > currentThumbsIndex) {\n            newThumbsIndex = newThumbsIndex - Math.floor(slidesPerView / 2) + 1;\n          } else {\n            newThumbsIndex = newThumbsIndex + Math.floor(slidesPerView / 2) - 1;\n          }\n        } else if (newThumbsIndex > currentThumbsIndex) {\n          newThumbsIndex = newThumbsIndex - slidesPerView + 1;\n        }\n        thumbsSwiper.slideTo(newThumbsIndex, initial ? 0 : undefined);\n      }\n    }\n\n    // Activate thumbs\n    let thumbsToActivate = 1;\n    const thumbActiveClass = swiper.params.thumbs.slideThumbActiveClass;\n\n    if (swiper.params.slidesPerView > 1 && !swiper.params.centeredSlides) {\n      thumbsToActivate = swiper.params.slidesPerView;\n    }\n\n    if (!swiper.params.thumbs.multipleActiveThumbs) {\n      thumbsToActivate = 1;\n    }\n\n    thumbsToActivate = Math.floor(thumbsToActivate);\n\n    thumbsSwiper.slides.removeClass(thumbActiveClass);\n    if (thumbsSwiper.params.loop || (thumbsSwiper.params.virtual && thumbsSwiper.params.virtual.enabled)) {\n      for (let i = 0; i < thumbsToActivate; i += 1) {\n        thumbsSwiper.$wrapperEl.children(`[data-swiper-slide-index=\"${swiper.realIndex + i}\"]`).addClass(thumbActiveClass);\n      }\n    } else {\n      for (let i = 0; i < thumbsToActivate; i += 1) {\n        thumbsSwiper.slides.eq(swiper.realIndex + i).addClass(thumbActiveClass);\n      }\n    }\n  },\n};\nvar Thumbs$1 = {\n  name: 'thumbs',\n  params: {\n    thumbs: {\n      swiper: null,\n      multipleActiveThumbs: true,\n      autoScrollOffset: 0,\n      slideThumbActiveClass: 'swiper-slide-thumb-active',\n      thumbsContainerClass: 'swiper-container-thumbs',\n    },\n  },\n  create() {\n    const swiper = this;\n    Utils.extend(swiper, {\n      thumbs: {\n        swiper: null,\n        init: Thumbs.init.bind(swiper),\n        update: Thumbs.update.bind(swiper),\n        onThumbClick: Thumbs.onThumbClick.bind(swiper),\n      },\n    });\n  },\n  on: {\n    beforeInit() {\n      const swiper = this;\n      const { thumbs } = swiper.params;\n      if (!thumbs || !thumbs.swiper) return;\n      swiper.thumbs.init();\n      swiper.thumbs.update(true);\n    },\n    slideChange() {\n      const swiper = this;\n      if (!swiper.thumbs.swiper) return;\n      swiper.thumbs.update();\n    },\n    update() {\n      const swiper = this;\n      if (!swiper.thumbs.swiper) return;\n      swiper.thumbs.update();\n    },\n    resize() {\n      const swiper = this;\n      if (!swiper.thumbs.swiper) return;\n      swiper.thumbs.update();\n    },\n    observerUpdate() {\n      const swiper = this;\n      if (!swiper.thumbs.swiper) return;\n      swiper.thumbs.update();\n    },\n    setTransition(duration) {\n      const swiper = this;\n      const thumbsSwiper = swiper.thumbs.swiper;\n      if (!thumbsSwiper) return;\n      thumbsSwiper.setTransition(duration);\n    },\n    beforeDestroy() {\n      const swiper = this;\n      const thumbsSwiper = swiper.thumbs.swiper;\n      if (!thumbsSwiper) return;\n      if (swiper.thumbs.swiperCreated && thumbsSwiper) {\n        thumbsSwiper.destroy();\n      }\n    },\n  },\n};\n\n// Swiper Class\n\nconst components = [\n  Device$1,\n  Support$1,\n  Browser$1,\n  Resize,\n  Observer$1,\n  Virtual$1,\n  Keyboard$1,\n  Mousewheel$1,\n  Navigation$1,\n  Pagination$1,\n  Scrollbar$1,\n  Parallax$1,\n  Zoom$1,\n  Lazy$1,\n  Controller$1,\n  A11y,\n  History$1,\n  HashNavigation$1,\n  Autoplay$1,\n  EffectFade,\n  EffectCube,\n  EffectFlip,\n  EffectCoverflow,\n  Thumbs$1\n];\n\nif (typeof Swiper.use === 'undefined') {\n  Swiper.use = Swiper.Class.use;\n  Swiper.installModule = Swiper.Class.installModule;\n}\n\nSwiper.use(components);\n\nexport default Swiper;\n//# sourceMappingURL=swiper.esm.bundle.js.map\n"], "sourceRoot": ""}