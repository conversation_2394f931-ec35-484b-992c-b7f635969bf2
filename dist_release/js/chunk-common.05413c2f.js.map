{"version": 3, "sources": ["webpack:///./src/components/common/loading/loading.vue?d781", "webpack:///./src/App.vue?d8f4", "webpack:///./src/assets/ss/ssd/find_ss_uid.jpeg", "webpack:///./src/assets/koa/aof/diamondGallery/01.jpg", "webpack:///./src/assets/ss/pc/ss-logo.png", "webpack:///./src/assets/ss/ssd/diamondGallery/ssd-01.jpeg", "webpack:///./src/assets/ss/diamondGallery/01.png", "webpack:///./src/components/pop/CallbackPendingTips.vue?f955", "webpack:///./src/assets/koa/login/login-validate-game-scrrencut.jpeg", "webpack:///./src/components/pop/ChargeConstruction.vue?5a69", "webpack:///./src/assets/dc/login/login-validate-game-scrrencut.jpeg", "webpack:///./src/config/game/rp/stRP.js", "webpack:///./src/components/pop/BackendPopup.vue?888e", "webpack:///./src/store/module/formdata.js", "webpack:///./src/store/module/gameinfo.js", "webpack:///./src/store/module/userinfo.js", "webpack:///./src/store/module/orderPage.js", "webpack:///./src/store/module/riskPolicy.js", "webpack:///./src/store/module/vb.js", "webpack:///./src/store/module/function.js", "webpack:///./src/store/index.js", "webpack:///./src/components/pop/index.vue?7dcb", "webpack:///./src/assets/ss/login/ss_uid.png", "webpack:///./src/utils/logHelper.js", "webpack:///./src/theme lazy ^\\.\\/.*\\.scss$ namespace object", "webpack:///./src/assets/koa/rp/sdk2-diamond-koa.png", "webpack:///./src/assets/dc/login/uid-tips.jpeg", "webpack:///./src/config/game/koa.js", "webpack:///./src/config/game/aof.js", "webpack:///./src/config/game/rom.js", "webpack:///./src/config/game/koaCn.js", "webpack:///./src/config/game/dc.js", "webpack:///./src/config/game/ss/ssv.js", "webpack:///./src/config/game/ss/ssv2.js", "webpack:///./src/config/game/ss/ssd.js", "webpack:///./src/config/game/foundation.js", "webpack:///./src/config/game/ss/ssCP.js", "webpack:///./src/config/game/stCP.js", "webpack:///./src/config/game/mcCP.js", "webpack:///./src/config/game/gogCP.js", "webpack:///./src/config/game/romCP.js", "webpack:///./src/config/game/rp/index.js", "webpack:///./src/config/resConfig.js", "webpack:///./src/utils/resLoaderHelper.js", "webpack:///./src/utils/webReporter.js", "webpack:///./src/utils/flexible_custom.js", "webpack:///./src/utils/prepareBasicInfo.js", "webpack:///./src/App.vue", "webpack:///./src/components/pop/index.vue", "webpack:///./src/components/pop/BackendPopup.vue", "webpack:///src/components/pop/BackendPopup.vue", "webpack:///./src/components/pop/BackendPopup.vue?b7d5", "webpack:///./src/components/pop/BackendPopup.vue?6568", "webpack:///./src/components/pop/ChargeConstruction.vue", "webpack:///src/components/pop/ChargeConstruction.vue", "webpack:///./src/components/pop/ChargeConstruction.vue?321b", "webpack:///./src/components/pop/ChargeConstruction.vue?ce66", "webpack:///./src/components/pop/CallbackPendingTips.vue", "webpack:///src/components/pop/CallbackPendingTips.vue", "webpack:///./src/components/pop/CallbackPendingTips.vue?6e8d", "webpack:///./src/components/pop/CallbackPendingTips.vue?7115", "webpack:///./src/components/pop/ArrearsReminder.vue", "webpack:///src/components/pop/ArrearsReminder.vue", "webpack:///./src/components/pop/ArrearsReminder.vue?6d5a", "webpack:///./src/components/pop/ArrearsReminder.vue?45c6", "webpack:///./src/components/pop/IosForbidden.vue", "webpack:///src/components/pop/IosForbidden.vue", "webpack:///./src/components/pop/IosForbidden.vue?8263", "webpack:///./src/components/pop/IosForbidden.vue?6375", "webpack:///./src/components/pop/CustomDiamond.vue", "webpack:///src/components/pop/CustomDiamond.vue", "webpack:///./src/components/pop/CustomDiamond.vue?b0f5", "webpack:///./src/components/pop/CustomDiamond.vue?8de4", "webpack:///./src/components/pop/AvatarBonusPop.vue", "webpack:///src/components/pop/AvatarBonusPop.vue", "webpack:///./src/components/pop/AvatarBonusPop.vue?8654", "webpack:///./src/components/pop/AvatarBonusPop.vue?2469", "webpack:///./src/components/game/koa/DailyReward.vue", "webpack:///src/components/game/koa/DailyReward.vue", "webpack:///./src/components/game/koa/DailyReward.vue?5dcb", "webpack:///./src/components/game/koa/DailyReward.vue?ccc6", "webpack:///src/components/pop/index.vue", "webpack:///./src/components/pop/index.vue?3122", "webpack:///./src/components/pop/index.vue?1bac", "webpack:///src/App.vue", "webpack:///./src/App.vue?774d", "webpack:///./src/App.vue?3746", "webpack:///./src/router/index.js", "webpack:///./src/components/common/loading/loading.vue", "webpack:///src/components/common/loading/loading.vue", "webpack:///./src/components/common/loading/loading.vue?cf04", "webpack:///./src/components/common/loading/loading.vue?58b7", "webpack:///./src/components/common/loading/loading.js", "webpack:///./src/components/common/toast/toast.vue", "webpack:///src/components/common/toast/toast.vue", "webpack:///./src/components/common/toast/toast.vue?cafb", "webpack:///./src/components/common/toast/toast.vue?e076", "webpack:///./src/components/common/toast/toast.js", "webpack:///./src/components/common/dialog/dialog.vue", "webpack:///src/components/common/dialog/dialog.vue", "webpack:///./src/components/common/dialog/dialog.vue?3deb", "webpack:///./src/components/common/dialog/dialog.vue?4274", "webpack:///./src/components/common/dialog/dialog.js", "webpack:///./src/main.js", "webpack:///./src/assets/dc/rp/sdk2-diamond-dc.png", "webpack:///./src/assets/ss/diamondGallery/03.png", "webpack:///./src/assets/koa/diamondGallery/02.jpg", "webpack:///./src/config/game/rp/ssRP.js", "webpack:///./src/components/pop/IosForbidden.vue?5c54", "webpack:///./src/components/pop/ArrearsReminder.vue?5ed9", "webpack:///./src/assets/ss/diamondGallery/02.png", "webpack:///./src/config/LangConf.js", "webpack:///./src/utils/i18n.js", "webpack:///./src/assets/mo/morp/sdk2-diamond-mo.png", "webpack:///./src/components/pop/CustomDiamond.vue?f74d", "webpack:///./src/config/game/defaultConfig.js", "webpack:///./src/utils/storageUtils.js", "webpack:///./src/components/common/dialog/dialog.vue?8e9d", "webpack:///./src/config/game/rp/ssdRP.js", "webpack:///./src/assets/ss/login/login-validate-game-scrrencut.jpeg", "webpack:///./src/assets/st/strp/sdk2-diamond-st.png", "webpack:///./src/assets/ss/ssv2/login/login-validate-game-scrrencut.png", "webpack:///./src/assets/koa/aof/diamondGallery/02.jpg", "webpack:///./src/assets/ss/ssv2/find_ss_uid.jpeg", "webpack:///./src/components/pop/container.vue?d41f", "webpack:///./src/assets/koa/diamondGallery/03.jpeg", "webpack:///./src/server/index.js", "webpack:///./src/server/http.js", "webpack:///./src/config/game/rp/dcRP.js", "webpack:///./src/assets/koa/login/sample_koa.png", "webpack:///./src/assets/ss/ssd/pc/logo.png", "webpack:///./src/assets/dc/diamondGallery/01.png", "webpack:///./src/components/common/toast/toast.vue?66f6", "webpack:///./src/assets/ss/ssrp/sdk2-diamond-ss.png", "webpack:///./src/components/pop/containerV2.vue", "webpack:///src/components/pop/containerV2.vue", "webpack:///./src/components/pop/containerV2.vue?3f84", "webpack:///./src/components/pop/containerV2.vue?c2ab", "webpack:///./src/assets/ss/ssd/ssdrp/sdk2-diamond-ssd.png", "webpack:///./src/assets/koa/diamondGallery/01.jpg", "webpack:///./src/config/game/rp/moRP.js", "webpack:///./src/config/game/rp/koaRP.js", "webpack:///./src/config/OrderPageConf.js", "webpack:///./src/components/pop/AvatarBonusPop.vue?72de", "webpack:///./src/assets/ss/ssd/login/login-validate-game-scrrencut.jpeg", "webpack:///./src/config/game/rp sync nonrecursive .*RP\\.js$", "webpack:///./src/components/pop/containerV2.vue?83f0", "webpack:///./src/assets/ss/diamondGallery/ssv-01.jpeg", "webpack:///./src/components/game/koa/DailyReward.vue?fceb", "webpack:///./src/components/pop/container.vue", "webpack:///src/components/pop/container.vue", "webpack:///./src/components/pop/container.vue?0b10", "webpack:///./src/components/pop/container.vue?13dd", "webpack:///./src/assets/koa/aof/diamondGallery/03.jpeg", "webpack:///./src/assets/foundation/login/uid-tips.jpeg", "webpack:///./src/assets/foundation/diamondGallery/01.jpeg", "webpack:///./src/utils/utils.js", "webpack:///./langHelper/langJson lazy ^\\.\\/.*\\.json$ namespace object"], "names": ["module", "exports", "localKey", "Object", "create", "baseKey", "pageTitle", "localSwitch", "functionSwitch", "enableAnimation", "config", "gameinfo", "gameProject", "gameCode", "gameId", "whiteChannel", "blackChannel", "greyChannel", "gameName", "appGameDeepLinkIos", "appGameDeepLinkAndroid", "apiParams", "lang<PERSON><PERSON>", "images", "logoPath", "iconDiamond", "require", "ids", "gid", "secret<PERSON>ey", "switch", "namespaced", "state", "chosenChannel", "<PERSON><PERSON><PERSON><PERSON>", "chosen<PERSON><PERSON><PERSON><PERSON>", "isFirstPayUsed", "isInit", "chosen<PERSON><PERSON><PERSON>n<PERSON><PERSON>", "vip", "discountSubChannelId", "diamondBonus", "channelBonus", "level", "isNewUser", "firstPayProducts", "gotDailyReward", "extraCostInfo", "koaTopupEnable", "switchToggleState", "showDoubleExperience", "defaultRebateDynamicInfo", "defaultRebateDynamicInfoAll", "defaultRebateInfo", "defaultRebateInfoByProduct4", "defaultDiscountInfo", "defaultDiscountInfoByProduct4", "isFixedEventOpen", "isFixedRebateWork", "getters", "takeE<PERSON>ct<PERSON><PERSON>aultD<PERSON>unt", "rootState", "defaultDiscount", "userinfo", "is<PERSON>ogin", "FE_INDEX", "takeEffectDefaultRebate", "TWMyCard", "product_id", "includes", "FinalPriceState", "temp", "rawOriginPrice", "rawNowPrice", "taxation", "extra_fee_amount", "offCountTips", "offCountAmount", "finalNowPrice", "finalOriginPrice", "rate", "coin", "level_coin", "feType", "isDefault", "isFixedCoupon", "isFixedRebate", "sdkType", "couponKey", "price", "discount_price", "no_tax_price", "level_currency_price", "type", "i18n", "t", "0", "rateWidthOutPercent", "deduct_price", "currency_symbol", "discount_amount", "unionKey", "channel_id", "sub_channel_id", "currency", "tax_rate", "taxRate", "chosen<PERSON><PERSON>", "extraRate", "priceHelper", "fixPrice", "isDiamondOwn95Off", "diamond", "isDiamondOwnRebate", "getRebateCoin", "getSDKRebateCoin", "mutations", "setChosenChannel", "payload", "resetPrice", "logForClickChannel", "JSON", "stringify", "channel", "channel_name", "resetChannel", "setChosenDiamond", "logForClickDiamond", "setChosenCoupon", "item", "values", "product_discount_range", "<PERSON><PERSON>", "set", "logForClickCoupon", "setFirstPayStatus", "setIsInit", "resetCouponInfo", "initVipInfo", "vip_bonus", "pay_bonus", "is_white", "exp", "daily_reward_receive", "setFirstPayProducts", "payLoad", "length", "for<PERSON>ach", "setFixedCoupon", "setFixedRebate", "setFixedCouponByProduct4", "setFixedRebateByProduct4", "setDailyRewardStatus", "setExtraCostInfo", "extra_fee_rate", "setKoaTopupEnable", "switchToggle", "switchDoubleExperience", "setFixedDynamicRebate", "chosen", "all", "toggleCoupon", "localStorage", "setItem", "Number", "setFixedToggleEvent", "getItem", "timeout", "clearTimeout", "undefined", "setTimeout", "currencySymbol", "toAddSymbolList", "key", "appId", "mainBody", "isSS", "isKOA", "isROMCP", "game", "window", "__GAMENAME", "isCn", "setGameInfo", "$gcbk", "isPuzalaGame", "country", "fpid", "icon", "lang", "name", "openid", "pkg_channel", "server", "lastChannel", "setUserInfo", "uid", "logParams", "__ReportExtraData__", "StorageUtils", "init", "saveLastChannelInfo", "last_channel", "logout", "removeItem", "origin", "pathname", "location", "href", "OrderPageOpenidKey", "current<PERSON><PERSON>", "emit", "forbiddenAccess", "_hasUnion", "showAdyenTips", "showTipsWhenSomeChannel", "finalNames", "push", "hideAdyenChannel", "hideSomeChannel", "list", "banned_status", "expire_time", "value", "builtInCashier", "isDiscountUsed", "setIsDiscountUsed", "savePrefixChannel", "resetBuiltInCashierStatus", "kv", "payermaxDropInObj", "payermax_A34_A34_dropin", "status", "loginValidation", "showMobilePolicy", "showPopPolicy", "boon", "fixedDiscountType", "smallD<PERSON>ondDoubleDiscount", "ckoCheckedByDefault", "showPcDiscountTips", "setFunctionInfo", "updateFunctionInfo", "send_code_enable", "bind_card", "use", "Vuex", "Store", "urlParams", "getUrlParams", "currencyUnit", "city", "zipCode", "isArZone", "isPc", "innerWidth", "isMobile", "agreePrivacyPolicy", "isPCSDK", "__isPCSDK", "IS_CHECKOUT_SDK", "__IS_CHECKOUT_SDK", "IS_CHECKOUT_SDK_V2", "__IS_CHECKOUT_SDK_V2", "setCurrencyUnitByIp", "zipcode", "resetIsXXX", "setPrivacyPolicyStatus", "updateUrlParams", "tc", "parse", "assign", "actions", "modules", "formdata", "orderPage", "riskPolicy", "vb", "gameInfo", "store", "browserMark", "send", "logoInfo", "info", "app_id", "split", "data_version", "event", "event_ts", "ts_pretty", "detail", "gamecode", "gameid", "pay_channel", "tracking_id", "utm_campaign", "browser_id", "opened_by", "getPWADisplayMode", "getPlatform", "action", "s", "makeUpCommonParams", "biPath", "target", "process", "navigator", "sendBeacon", "img", "Image", "src", "qs", "allowDots", "logForPageLoad", "param", "logForClickLogin", "params", "logForLoginSuccess", "gameserver_id", "method", "coupon", "coupon_id", "coupon_description", "logForPayResult", "orderId", "reason", "orderInfo", "order_id", "logForSdk2OpenedSuccess", "o", "randomString", "map", "webpackAsyncContext", "req", "__webpack_require__", "Promise", "resolve", "then", "e", "Error", "code", "id", "keys", "to", "boonAme", "projectId", "loginAction", "getLoginReward", "pwaOpenAction", "getPwaReward", "whats<PERSON><PERSON><PERSON>", "imageUrl", "jumpUrl", "uidTips", "loganFindValidationCode", "customDiamondLimitTips", "loginPlaceHolder", "tokenName", "howToUseDiamond", "loginValidationExpireCount", "whatIsDiamondTitle", "minCustomDiamondNum", "boonPopName", "sortName", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>unt", "p1", "p2", "minimumDiamondId", "useThemeFile", "gameLogCode", "switchRebate", "context", "reduce", "acc", "moduleName", "replace", "default", "sourceFun", "koa", "aof", "rom", "koaCn", "dc", "ssv", "ssv2", "foundation", "ssCP", "stCP", "mc<PERSON>", "gogCP", "romCP", "ssd", "rp", "currentGame", "loadResource", "content", "console", "error", "defaultValue", "key1", "key2", "getContentByKey", "prototype", "$vt", "$imageLoader", "$idLoader", "$gameName", "ReportCustom", "domain", "autoErr", "vueError", "useBeaconFirst", "extra", "appVersion", "flexible", "document", "baseSize", "windowSize", "scale", "documentElement", "style", "fontSize", "commit", "interval", "addEventListener", "reload", "Array", "arr", "isArray", "this", "filter", "x", "render", "_vm", "_c", "_self", "class", "attrs", "on", "$event", "$root", "$emit", "showPage", "isExtraInfoInit", "$route", "fullPath", "_e", "staticRenderFns", "showOuter", "staticClass", "cmpName", "showInner", "tag", "option", "directives", "rawName", "popupShow", "expression", "popupSrc", "jumpPage", "closePopup", "props", "data", "$store", "gameEnv", "popupList", "index", "ymd", "jumpLink", "methods", "changePopup", "cur<PERSON><PERSON><PERSON>", "curPopupSrc", "curJumpLink", "category", "file_details", "$i18n", "locale", "en", "jump_link", "finalUrl", "encodeURIComponent", "open", "created", "broswerLang", "language", "userLanguage", "toLowerCase", "startsWith", "date", "Date", "now", "getTimezoneOffset", "getFullYear", "getMonth", "getDate", "i", "image", "component", "$t", "_l", "fill", "k", "_v", "_s", "indexOf", "prefix", "components", "Container", "computed", "prefixMap", "title", "confirmBtnTxt", "confirmBtnCb", "$router", "scopedSlots", "_u", "fn", "cancel", "go", "proxy", "domProps", "calcTxt", "leaveCount", "mounted", "setInterval", "clearInterval", "num", "errorInput", "composing", "floorInput", "validateCustomInput", "priceState", "nowPrice", "originPrice", "submit", "MAX_VALUE", "MIN_VALUE", "customProductId", "defaultPrice", "cb", "Math", "floor", "mapState", "mapGetters", "calcPrice", "openBox", "expNum", "close", "clickGoVip", "couponName", "couponNameMap", "ContainerV2", "reward", "item_list", "item_name", "item_nums", "vipIntroducePageUrl", "base", "$loading", "show", "getAmeDo", "p0", "res", "$toast", "err", "catch", "finally", "hide", "BackendPopup", "CallbackPendingTips", "ChargeConstruction", "ArrearsReminder", "WhatIsDiamond", "IosForbidden", "BoonPop", "SSVBoonPop", "CustomDiamond", "AvatarBonusPop", "RiskControlPolicy", "DailyReward", "LoginValidation", "PrivacyPolicy", "PrivateConfirmPop", "sdk2Tips", "cacheList", "showLoginValidation", "validationPop", "unshift", "lastPop", "showOtherPop", "$once", "targetArr", "history", "getLocalStorage", "silence", "agree", "setLocalStorage", "$on", "ip", "p3", "response", "popup", "mode", "splice", "newItem", "shift", "PopHome", "loadTheme", "getCurrencyByIp", "_this$$route", "path", "getCommonInfo", "channel_extra_fee_list", "change_coupon_enable", "point_card_product", "__showEmailForm", "billing_address_enable", "getOpenidByFpid", "changeFpidToOpenid", "openid_list", "checkWxOpenid", "isWx", "urlHelper", "getTime", "getWxOpenid", "initLang", "local", "setLocaleMessage", "querySelector", "innerText", "tFlag", "displayMode", "$gtag", "event_label", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "routes", "redirect", "router", "__ROUTERPATH", "loadingNum", "showLoading", "hideLoading", "max", "Loading", "install", "options", "loading", "getVm", "$vm", "cmp", "loadingCmp", "<PERSON><PERSON><PERSON><PERSON>", "extend", "el", "createElement", "body", "append<PERSON><PERSON><PERSON>", "$el", "vm", "log", "message", "msg", "duration", "getVmSingleton", "messageCmp", "obj", "queue", "isPending", "openVm", "next", "bind", "isTipsExit", "some", "m", "showBg", "dismiss", "$nextTick", "$tips", "device", "mobile", "fixToFixed", "productionTip", "<PERSON><PERSON><PERSON><PERSON>", "VueLazyload", "toast", "dialog", "h", "App", "$mount", "EN", "VueI18n", "l", "defaultLang", "allowLang", "fallback<PERSON><PERSON><PERSON>", "messages", "langConf", "langObj", "ko", "ja", "fr", "de", "ru", "discount95Tips", "sdk2_construction_title", "sdk2_construction_content", "entries", "nameSpaceKey", "final<PERSON>ey", "setSessionStorage", "sessionStorage", "getSessionStorage", "get", "post", "fetchUidList", "getUserInfoForToken", "getActivityListForToken", "ameDoByGet", "ameHoldByGet", "ameDoByGetCommon", "sendCode", "checkCode", "toggleCouponSingle", "wxCode", "wx_code", "BasicRequestPath", "productList", "channelList", "placeOrder", "cardPlaceOrder", "orderDetail", "redirectProduct", "lastChosenChannel", "finalPath", "getTokenList", "getRedirectProductList", "getTokenChannelList", "placeOrderToken", "placeOrderCard", "getTokenOrderDetails", "getLastChosenChannel", "isOrderPage", "game_id", "game_project", "source", "localOpenid", "transformUrl", "url", "Game", "service", "axios", "async", "requestController", "needRetry", "requestConfig", "reject", "interceptors", "request", "localCountry", "contact", "localAccountEnv", "isLastRequest", "_t", "OrderPageTokenKey", "OrderPageLangKey", "toUpperCase", "webpackContext", "webpackContextResolve", "size", "<PERSON><PERSON>ead<PERSON>", "hideClose", "customContent", "hideFooter", "confirmClick", "String", "Boolean", "customClass", "search", "slice", "urlPrams", "searchArr", "decodeURIComponent", "tempUrl", "localParams", "sessionParams", "watchDisplayMode", "calcDisplayMode", "isStandalone", "matchMedia", "matches", "referrer", "standalone", "ua", "userAgent", "isIos", "match", "isAndroid", "len", "chars", "maxPos", "pwd", "char<PERSON>t", "random", "navToLogin", "returnUrl", "toFixed", "ceil", "n", "number", "RangeError", "isNaN", "TypeError", "round", "toString", "call", "EPSILON", "pow", "onceADay", "storageKey", "range", "useLocalDate", "tempDate", "popHistory", "showPopup", "numberFormat", "digits", "lookup", "symbol", "rx", "reverse", "find", "dealSmDeviceId", "smDeviceId", "smDeviceIdReady", "SMSdk", "ready", "getDeviceId", "getBrowserInfo", "acceptHeader", "colorDepth", "screen", "screenHeight", "clientHeight", "jetLag", "screenWidth", "clientWidth", "javaEnabled", "javaScriptEnabled", "decryptAES", "ciphertextStr", "deobfuscate", "obfuscatedStr", "fromCharCode", "join", "decrypted", "CryptoJS", "AES", "decrypt", "enc", "Utf8", "iv", "substr", "CBC", "padding", "pad", "Pkcs7", "decryptedStr", "encryptAES", "encryptd", "encrypt", "isWeixin", "jumpToUrl"], "mappings": "kIAAA,W,yECAA,W,uBCAAA,EAAOC,QAAU,IAA0B,mD,gDCA3CD,EAAOC,QAAU,IAA0B,yC,uBCA3CD,EAAOC,QAAU,IAA0B,8C,uBCA3CD,EAAOC,QAAU,IAA0B,8C,uBCA3CD,EAAOC,QAAU,IAA0B,yC,oCCA3C,W,uBCAAD,EAAOC,QAAU,IAA0B,qE,kCCA3C,W,qBCAAD,EAAOC,QAAU,IAA0B,qE,6DCA3C,uBAGA,MAAMC,EAAWC,OAAOC,OAAOC,QAC/BH,EAASI,UAAY,4BACrB,MAAMC,EAAcJ,OAAOC,OAAOI,QAElCD,EAAYE,iBAAkB,EAE9B,MAAMC,EAAS,CACbC,SAAU,CACRC,YAAa,YACbC,SAAU,KACVC,OAAQ,OACRC,aAAc,GACdC,aAAc,GACdC,YAAa,GACbC,SAAU,aACVC,mBAAoB,gDACpBC,uBAAwB,6BAE1BC,UAAW,GACXC,QAASpB,EACTqB,OAAQ,CACNC,SAAU,0GACVC,YAAaC,EAAQ,SAEvBC,IAAK,CACHC,IAAK,eAELC,UAAW,kHAEbC,OAAQvB,GAGKG,gB,oCCnCf,W,mHCIe,GACbqB,YAAY,EACZC,MAAO,CAELC,cAAe,GAGfC,cAAe,GAGfC,aAAc,GACdC,gBAAgB,EAChBC,QAAQ,EACRC,kBAAmB,GAEnBC,IAAK,CACHC,qBAAsB,GACtBC,aAAc,EACdC,aAAc,EACdC,MAAO,EACPN,QAAQ,EACRO,WAAW,GAIbC,iBAAkB,GAGlBC,gBAAgB,EAChBC,cAAe,GACfC,gBAAgB,EAEhBC,mBAAmB,EACnBC,sBAAsB,EAEtBC,yBAA0B,GAC1BC,4BAA6B,GAE7BC,kBAAmB,GACnBC,4BAA6B,GAC7BC,oBAAqB,GACrBC,8BAA+B,GAE/BC,kBAAkB,EAClBC,mBAAmB,GAErBC,QAAS,CAEPC,0BAA2B5B,EAAO2B,EAASE,GACzC,QAAKA,EAAUlD,SAASmD,kBAEhBD,EAAUE,SAASC,YAAchC,EAAMI,gBAAmBJ,EAAMG,aAAa8B,YAEjFJ,EAAUE,SAASC,UAGzBE,wBAAyBlC,EAAO2B,EAASE,GACvC,QAAK7B,EAAMqB,kBAAkBY,WACrBJ,EAAUE,SAASC,YAAchC,EAAMI,gBAAmBJ,EAAMG,aAAa8B,YACjFJ,EAAUE,SAASC,UAEzBG,SAAUnC,GACR,MAAME,EAAgBF,EAAME,cAC5B,OAAOA,EAAckC,YAAclC,EAAckC,WAAWC,SAAS,4BAEvEC,gBAAiBtC,EAAO2B,EAASE,GAE/B,MAAM1B,EAAeH,EAAMG,aACrBD,EAAgBF,EAAME,cACtBqC,EAAO,CAEXC,eAAgB,EAChBC,YAAa,EACbC,SAAU,EACVC,iBAAkB,EAClBC,aAAc,GACdC,eAAgB,GAEhBC,cAAe,EACfC,iBAAkB,EAElBC,KAAM,GACNC,KAAM,EACNC,WAAY,EAEZC,OAAQ,GACRC,WAAW,EACXC,eAAe,EACfC,eAAe,EACfC,QAAS,IAELC,EAAYrD,EAAagD,QAAUnD,EAAMqB,kBAAkB8B,QAAUnD,EAAMuB,oBAAoB4B,QAAUnD,EAAMmB,yBAAyBgC,OAI9I,OAAQK,GACN,IAAK,YACHjB,EAAKY,OAAS,YACdZ,EAAKQ,iBAAmB5C,EAAasD,MACrClB,EAAKO,cAAgB3C,EAAauD,eAClCnB,EAAKE,YAActC,EAAawD,aAChCpB,EAAKC,eAAiBrC,EAAayD,qBACnCrB,EAAKK,aAAe,WAAWzC,EAAa6C,WAC5CT,EAAKgB,QAAUpD,EAAa0D,KAC5B,MAEF,IAAK,mBACHtB,EAAKY,OAAS,mBAEdZ,EAAKO,cAAgB3C,EAAasD,MAClClB,EAAKE,YAAcvC,EAAcyD,aACjCpB,EAAKG,SAAWvC,EAAauC,SAC7BH,EAAKI,iBAAmBxC,EAAawC,iBACrCJ,EAAKS,KAAO7C,EAAa6C,KACzBT,EAAKU,KAAO9C,EAAa8C,KACzBV,EAAKW,WAAa/C,EAAa+C,WAC/BX,EAAKgB,QAAUpD,EAAa0D,KAE5BtB,EAAKK,aAAe,WAAWkB,OAAKC,EAAE,iBAAiB5D,EAAa6C,oCACpE,MAEF,IAAK,kBACHT,EAAKY,OAAS,kBACdZ,EAAKQ,iBAAmB5C,EAAasD,MACrClB,EAAKO,cAAgB3C,EAAauD,eAClCnB,EAAKE,YAActC,EAAawD,aAChCpB,EAAKC,eAAiBrC,EAAayD,qBAEnCrB,EAAKK,aAAekB,OAAKC,EAAE,kBAAmB,CAAEC,EAAG7D,EAAa8D,sBAChE,MAEF,IAAK,cACH1B,EAAKY,OAAS,cACdZ,EAAKQ,iBAAmB7C,EAAcuD,MACtClB,EAAKO,cAAgB3C,EAAasD,MAClClB,EAAKE,YAActC,EAAawD,aAChCpB,EAAKC,eAAiBrC,EAAayD,qBAEnCrB,EAAKK,aAAe,GAAGzC,EAAa+D,gBAAgBhE,EAAciE,sBAClE,MAEF,IAAK,gBACH5B,EAAKY,OAAS,gBACdZ,EAAKO,cAAgB3C,EAAasD,MAClClB,EAAKE,YAActC,EAAawD,aAEhCpB,EAAKK,aAAe,GAAGkB,OAAKC,EAAE,iBAAiB5D,EAAa6C,oCAC5D,MAEF,IAAK,wBAAyB,CAC5B,MAAMzB,EAAsBvB,EAAMuB,oBAElCgB,EAAKY,OAAS,wBACdZ,EAAKa,WAAY,EAEjBb,EAAKO,cAAgBvB,EAAoBmC,eACzCnB,EAAKQ,iBAAmBxB,EAAoBkC,MAC5ClB,EAAKE,YAAclB,EAAoBoC,aACvCpB,EAAKC,eAAiBjB,EAAoBqC,qBAE1CrB,EAAKK,aAAkBrB,EAAoB0C,oBAAvB,QACpB1B,EAAKM,eAAiBtB,EAAoB6C,gBAC1C7B,EAAKG,SAAWnB,EAAoBmB,SACpCH,EAAKI,iBAAmBpB,EAAoBoB,iBAE5CJ,EAAKc,eAAgB,EAErBd,EAAKgB,QAAUhC,EAAoBsC,KACnC,MAEF,IAAK,eAAgB,CACnB,MAAMxC,EAAoBrB,EAAMqB,kBAChCkB,EAAKY,OAAS,eACdZ,EAAKa,WAAY,EAEjBb,EAAKO,cAAgBzB,EAAkBoC,MACvClB,EAAKE,YAAcpB,EAAkBsC,aACrCpB,EAAKG,SAAWrB,EAAkBqB,SAClCH,EAAKI,iBAAmBtB,EAAkBsB,iBAE1CJ,EAAKK,aAAe,GAAGkB,OAAKC,EAAE,iBAAiB1C,EAAkB2B,oCACjET,EAAKe,eAAgB,EACrBf,EAAKgB,QAAUlC,EAAkBwC,KACjC,MAEF,IAAK,uBAAwB,CAC3B,MAAM1C,EAA2BnB,EAAMmB,yBACvCoB,EAAKY,OAAS,uBACdZ,EAAKa,WAAY,EAEjBb,EAAKO,cAAgB3B,EAAyBsC,MAC9ClB,EAAKE,YAActB,EAAyBwC,aAC5CpB,EAAKG,SAAWvB,EAAyBuB,SACzCH,EAAKI,iBAAmBxB,EAAyBwB,iBAEjDJ,EAAKK,aAAe,GAAGkB,OAAKC,EAAE,iBAAiB5C,EAAyB6B,oCACxET,EAAKe,eAAgB,EACrB,MAEF,QAAS,CACPf,EAAKE,YAAcvC,EAAcyD,aACjCpB,EAAKO,cAAgB5C,EAAcuD,MAEnC,MAAMxD,EAAgBD,EAAMC,cACtBoE,EAAWpE,EAAcqE,YAAcrE,EAAcsE,eAAiB,IAAItE,EAAcsE,eAAmB,KACzGX,qBAAsBH,EAAK,SAAEe,EAAUC,SAAUC,EAAO,UAAEC,EAAY,GAAM3E,EAAME,cACpF0E,EAAY5E,EAAMe,cAAcsD,IAAarE,EAAMe,cAAcd,EAAcqE,aAAe,EAEhGM,GAAanB,IACflB,EAAKI,iBAAmBkC,eAAYF,EAAYlB,GAASmB,EAAY,GAAIJ,GACzEjC,EAAKO,cAAgB+B,eAAYF,EAAYlB,GAASmB,EAAY,GAAKF,GAAW,IAAKF,GACvFxE,EAAME,cAAcyC,iBAAmBJ,EAAKI,mBAKlD,OAAOmC,EAASvC,EAAMvC,EAAME,gBAE9B6E,kBAAoB/E,GAAUgF,GAAWhF,EAAMuB,oBAAoByD,EAAQ5C,aAAepC,EAAMwB,8BAA8BwD,EAAQ5C,cAAe,EACrJ6C,mBAAqBjF,GAAUgF,GAAWhF,EAAMqB,kBAAkB2D,EAAQ5C,aAAepC,EAAMsB,4BAA4B0D,EAAQ5C,aAAepC,EAAMoB,4BAA4B4D,EAAQ5C,cAAe,EAC3M8C,cAAelF,GACb,OAAQgF,IACN,MAAMjB,EAAI/D,EAAMqB,kBAAkB2D,EAAQ5C,aAAepC,EAAMsB,4BAA4B0D,EAAQ5C,aAAepC,EAAMoB,4BAA4B4D,EAAQ5C,YAC5J,OAAQ2B,EAAEd,KAAOc,EAAEb,YAAe,IAGtCiC,iBAAkBnF,GAChB,OAAQA,EAAMqB,kBAAkB4B,KAAOjD,EAAMqB,kBAAkB6B,YAAe,IAGlFkC,UAAW,CAETC,iBAAkBrF,EAAOsF,GACvBtF,EAAMC,cAAgBqF,EAEtBC,EAAWvF,GACXN,EAAQ,QAAqB8F,mBAAmBC,KAAKC,UAAU,CAC7DC,QAASL,EAAQM,aACjBtB,WAAYgB,EAAQhB,WACpBC,eAAgBe,EAAQf,mBAG5BsB,aAAc7F,GACZA,EAAMC,cAAgB,GAEtBsF,EAAWvF,IAGb8F,iBAAkB9F,EAAOsF,GACvBtF,EAAME,cAAgBoF,EAEtBC,EAAWvF,GACXN,EAAQ,QAAqBqG,mBAAmBN,KAAKC,UAAUJ,KAGjEU,gBAAiBhG,EAAOsF,GACtB,GAAKA,EAAL,CACAtF,EAAMG,aAAemF,EAErBtF,EAAMM,kBAAoB,GAC1B,IAAK,MAAM2F,KAAQ9H,OAAO+H,OAAOZ,EAAQa,wBAA0B,IACjEC,aAAIC,IAAIrG,EAAMM,kBAAmB2F,EAAK7D,WAAY6D,GAGpDvG,EAAQ,QAAqB4G,kBAAkBhB,KAEjDiB,kBAAmBvG,EAAOsF,GACxBtF,EAAMI,eAAiBkF,GAEzBkB,UAAWxG,EAAOsF,GAChBtF,EAAMK,OAASiF,GAEjBmB,gBAAiBzG,GACfA,EAAMG,aAAe,GACrBH,EAAMM,kBAAoB,GAC1BN,EAAMI,gBAAiB,EACvBJ,EAAMK,QAAS,EACfL,EAAMa,iBAAmB,IAE3B6F,YAAa1G,EAAOsF,GAClBtF,EAAMO,IAAIC,qBAAuB,CAAC,SAAU,QAC5CR,EAAMO,IAAIE,aAAe6E,EAAQqB,UACjC3G,EAAMO,IAAIG,aAAe4E,EAAQsB,UACjC5G,EAAMO,IAAII,MAAQ2E,EAAQ3E,MAC1BX,EAAMO,IAAIF,OAASiF,EAAQuB,SAC3B7G,EAAMO,IAAIK,UAA6B,IAAhB0E,EAAQwB,KAA+B,IAAlBxB,EAAQ3E,MAEpDX,EAAMc,eAAiBwE,EAAQyB,sBAEjCC,oBAAqBhH,EAAOiH,GACrBA,EAAQC,SACZD,EAAQ,GAAGd,wBAA0B,IAAIgB,QAAQlB,IAChDjG,EAAMa,iBAAiBoF,EAAK7D,YAAc,IAAK6D,MAGnDmB,eAAgBpH,EAAOsF,GACrBtF,EAAMuB,oBAAsB+D,EAC5B,IAAK,MAAMW,KAAQ9H,OAAO+H,OAAOZ,EAAQa,wBAA0B,IACjEC,aAAIC,IAAIrG,EAAMuB,oBAAqB0E,EAAK7D,WAAY6D,IAIxDoB,eAAgBrH,EAAOsF,GACrBtF,EAAMqB,kBAAoBiE,EAC1B,IAAK,MAAMW,KAAQ9H,OAAO+H,OAAOZ,EAAQa,wBAA0B,IACjEC,aAAIC,IAAIrG,EAAMqB,kBAAmB4E,EAAK7D,WAAY6D,IAGtDqB,yBAA0BtH,EAAOsF,GAC/B,IAAK,MAAMW,KAAQ9H,OAAO+H,OAAOZ,EAAQa,wBAA0B,IACjEC,aAAIC,IAAIrG,EAAMwB,8BAA+ByE,EAAK7D,WAAY6D,IAGlEsB,yBAA0BvH,EAAOsF,GAC/B,IAAK,MAAMW,KAAQ9H,OAAO+H,OAAOZ,EAAQa,wBAA0B,IACjEC,aAAIC,IAAIrG,EAAMsB,4BAA6B2E,EAAK7D,WAAY6D,IAIhEuB,qBAAsBxH,EAAOsF,GAC3BtF,EAAMc,eAAiBwE,GAEzBmC,iBAAkBzH,EAAOiH,EAAU,IACjCA,EAAQE,QAAQlB,IACd,MAAM5B,EAAW4B,EAAK3B,YAAc2B,EAAK1B,eAAiB,IAAI0B,EAAK1B,eAAmB,IACtFvE,EAAMe,cAAcsD,GAAY4B,EAAKyB,kBAGzCC,kBAAmB3H,EAAOsF,GACxBtF,EAAMgB,eAAiBsE,GAEzBsC,aAAc5H,EAAOsF,GACnBtF,EAAMiB,kBAAoBqE,IAAW,GAEvCuC,uBAAwB7H,EAAOsF,GAC7BtF,EAAMkB,qBAAuBoE,IAAW,GAE1CwC,sBAAuB9H,GAAO,OAAE+H,EAAM,IAAEC,GAAQ,IAC9ChI,EAAMmB,yBAA2B4G,EACjC,IAAK,MAAM9B,KAAS+B,GAAO,GACzB5B,aAAIC,IAAIrG,EAAMoB,4BAA6B6E,EAAK7D,WAAY6D,IAGhEgC,aAAcjI,GACZA,EAAM0B,mBAAqB1B,EAAM0B,kBACjCwG,aAAaC,QAAQ,wBAAyBC,OAAOpI,EAAM0B,qBAE7D2G,oBAAqBrI,EAAOsF,GAC1BtF,EAAMyB,iBAAmB6D,EACrBA,IACFtF,EAAM0B,kBAAsE,MAAlDwG,aAAaI,QAAQ,6BAMvD,IAAIC,EACJ,SAAShD,EAAYvF,GACfuI,IACFC,aAAaD,GACbA,OAAUE,GAGZF,EAAUG,WAAW,KACnB,MAAMzI,EAAgBD,EAAMC,cACtBoE,EAAWpE,EAAcqE,YAAcrE,EAAcsE,eAAiB,IAAItE,EAAcsE,eAAmB,KAEzGX,qBAAsBH,EAAK,SAAEe,EAAUC,SAAUC,EAAO,UAAEC,EAAY,IAD5D3E,EAAMe,cAAcsD,IAAarE,EAAMe,cAAcd,EAAcqE,YACDtE,EAAME,eAK1FsI,aAAaD,GACbA,EAAU,MACT,KAGL,SAASzD,EAAUvC,EAAMrC,GACvB,MAAMyI,EAAiBzI,EAAciE,gBAC/ByE,EAAkB,CAAC,iBAAkB,cAAe,gBAAiB,oBAK3E,OAJAA,EAAgBzB,QAAQ0B,IAClBtG,EAAKsG,KAAMtG,EAAKsG,IAAQ,IAAIF,KAG3BpG,EClYM,OACbxC,YAAY,EACZC,MAAO,CACLpB,YAAa,GACbC,SAAU,GACVK,SAAU,GACV4J,MAAO,GACPhK,OAAQ,GAERgD,iBAAiB,EACjB/C,aAAc,GACdC,aAAc,GACdC,YAAa,GACb8J,SAAU,UAEVC,MAAM,EACNC,OAAO,EACPC,SAAS,EACTC,KAAMC,OAAOC,WACbC,MAAM,GAERlE,UAAW,CACTmE,YAAcvJ,IACZ,MAAMsF,EAAU8D,OAAOI,MAAM,WAAY,IACzCxJ,EAAMpB,YAAc0G,EAAQ1G,YAC5BoB,EAAMnB,SAAWyG,EAAQzG,SACzBmB,EAAMd,SAAWoG,EAAQpG,SACzBc,EAAM8I,MAAQxD,EAAQwD,MACtB9I,EAAMlB,OAASwG,EAAQxG,OAEvBkB,EAAM8B,gBAAkBwD,EAAQxD,gBAChC9B,EAAMjB,aAAeuG,EAAQvG,aAC7BiB,EAAMhB,aAAesG,EAAQtG,aAC7BgB,EAAMf,YAAcqG,EAAQrG,YAE5Be,EAAM+I,SAAWzD,EAAQyD,SACzB/I,EAAMsJ,KAAOhE,EAAQgE,KACrBlD,aAAIC,IAAIrG,EAAO,KAAKA,EAAMnB,UAAY,KAG1C8C,QAAS,CACP8H,aAAczJ,GAA4B,WAAnBA,EAAM+I,W,YCzClB,GACbhJ,YAAY,EACZC,MAAO,CACLgC,SAAS,EACT0H,QAAS,GACTlF,SAAU,GACVmF,KAAM,GACNC,KAAM,GACNC,KAAM,GACNlJ,MAAO,EACPmJ,KAAM,GACNC,OAAQ,GACRC,YAAa,GACbC,OAAQ,EACRC,YAAa,IAEf9E,UAAW,CACT+E,YAAanK,EAAOsF,GAClBtF,EAAMW,MAAQ2E,EAAQ3E,MACtBX,EAAMiK,OAAS3E,EAAQ2E,OACvBjK,EAAMoK,IAAM9E,EAAQ8E,IACpBpK,EAAM+J,OAASzE,EAAQyE,OACvB/J,EAAM4J,KAAOtE,EAAQsE,KACrB5J,EAAM8J,KAAOxE,EAAQwE,KACrB9J,EAAM2J,KAAOrE,EAAQqE,KACrBzB,aAAaC,QAAQ,SAAUnI,EAAM+J,QAErC/J,EAAMgC,SAAU,EAEhB,MAAMqI,EAAY,GACd/E,EAAQ8E,MAAKC,EAAUD,IAAM9E,EAAQ8E,KACrC9E,EAAQyE,SAAQM,EAAUN,OAASzE,EAAQyE,QAC/CX,OAAOkB,qBAAuBlB,OAAOkB,oBAAoBD,GACzDE,OAAaC,KAAKlF,EAAQyE,SAE5BU,oBAAqBzK,EAAOsF,GAC1BtF,EAAMkK,YAAc5E,EAAQoF,cAE9BC,SACEzC,aAAa0C,WAAW,UAExB,MAAM,OAAEC,EAAM,SAAEC,GAAaC,SAC7B3B,OAAO2B,SAASC,KAAO,GAAGH,IAASC,O,YC1C1B,GACb/K,YAAY,EACZC,MAAO,CACL+B,SAAU,CACRC,SAAS,EACT0H,QAAS,GACTlF,SAAU,GACVmF,KAAM,GACNC,KAAM,GACNC,KAAM,GACNlJ,MAAO,EACPmJ,KAAM,GACNC,OAAQ,GACRC,YAAa,GACbC,OAAQ,IAGZ7E,UAAW,CACT+E,YAAanK,EAAOsF,GAClBtF,EAAM+B,SAASpB,MAAQ2E,EAAQ3E,MAC/BX,EAAM+B,SAASkI,OAAS3E,EAAQ2E,OAChCjK,EAAM+B,SAASqI,IAAM9E,EAAQ8E,IAC7BpK,EAAM+B,SAASgI,OAASzE,EAAQyE,OAChC/J,EAAM+B,SAAS6H,KAAOtE,EAAQsE,KAC9B5J,EAAM+B,SAAS+H,KAAOxE,EAAQwE,KAC9B5B,aAAaC,QAAQ8C,OAAoBjL,EAAM+B,SAASgI,QAExD/J,EAAM+B,SAASC,SAAU,EAEzB,MAAMqI,EAAY,GACd/E,EAAQ8E,MAAKC,EAAUD,IAAM9E,EAAQ8E,KACrC9E,EAAQyE,SAAQM,EAAUN,OAASzE,EAAQyE,QAC/CX,OAAOkB,qBAAuBlB,OAAOkB,oBAAoBD,IAE3DM,SACEzC,aAAa0C,WAAWK,QAExB,MAAM,OAAEJ,EAAM,SAAEC,GAAaC,SAC7B3B,OAAO2B,SAASC,KAAO,GAAGH,IAASC,aCxC1B,G,UAAA,CACb/K,YAAY,EACZC,MAAO,CACLkL,WAAY,GAEZ7K,QAAQ,EACR8K,KAAMA,QAERxJ,QAAS,CACPyJ,gBAAiBpL,GACf,QAAKA,EAAMK,QACJL,EAAMkL,WAAWG,UAAU,CAAC,gBAAiB,gBAEtDC,cAAetL,GACb,QAAKA,EAAMK,QACJL,EAAMkL,WAAWG,UAAU,CAAC,eAErCE,wBAAyBvL,EAAO2B,GAC9B,MAAM6J,EAAa,GACnB,OAAKxL,EAAMK,QACPsB,EAAQ2J,eAAeE,EAAWC,KAAK,SACvCzL,EAAMkL,WAAW7I,SAAS,cAAcmJ,EAAWC,KAAK,SACxDzL,EAAMkL,WAAW7I,SAAS,eAAemJ,EAAWC,KAAK,UACzDzL,EAAMkL,WAAW7I,SAAS,eAAemJ,EAAWC,KAAK,UACzDzL,EAAMkL,WAAW7I,SAAS,iBAAiBmJ,EAAWC,KAAK,YAExDD,GAPmBA,GAS5BE,iBAAkB1L,GAChB,QAAKA,EAAMK,QACJL,EAAMkL,WAAWG,UAAU,CAAC,eAAgB,uBAErDM,gBAAiB3L,EAAO2B,GACtB,MAAM6J,EAAa,GACnB,OAAKxL,EAAMK,QACPsB,EAAQ+J,kBAAkBF,EAAWC,KAAK,SAC1CzL,EAAMkL,WAAW7I,SAAS,iBAAiBmJ,EAAWC,KAAK,SAC3DzL,EAAMkL,WAAW7I,SAAS,kBAAkBmJ,EAAWC,KAAK,UAC5DzL,EAAMkL,WAAW7I,SAAS,kBAAkBmJ,EAAWC,KAAK,UAC5DzL,EAAMkL,WAAW7I,SAAS,oBAAoBmJ,EAAWC,KAAK,YAE3DD,GAPmBA,IAU9BpG,UAAW,CACToF,KAAMxK,EAAOsF,EAAU,IAKrB,MAAM,KAAEsG,EAAO,GAAE,KAAET,GAAS7F,EAC5BtF,EAAMK,QAAS,EACfL,EAAMmL,KAAOA,EAEbS,EAAKzE,QAAQlB,IACX,MAAQ4F,cAAehD,EAAKiD,YAAaC,GAAU9F,EACnDjG,EAAMkL,WAAWO,KAAK5C,GAElB,CAAC,gBAAiB,aAAc,cAAe,0BAA0BxG,SAASwG,IACpFsC,EAAK,UAAW,oBAAqB,CAAEtC,MAAKkD,gBC3DvC,GACbhM,YAAY,EACZC,MAAO,CACLgM,gBAAgB,EAChBC,gBAAgB,GAElB7G,UAAW,CACT8G,kBAAmBlM,EAAOsF,GACxBtF,EAAMiM,eAAiB3G,GAEzB6G,kBAAmBnM,EAAOsF,GACpBA,GAAWA,EAAQ4B,SACrBlH,EAAMgM,eAAqC,YAApB1G,EAAQ,GAAGzB,OAGtCuI,0BAA2BpM,EAAOsF,GAChC,IAAKA,IAAYA,EAAQ4B,OAAQ,OAAO,KAExC,MAAMmF,EAAK,GACX/G,EAAQ6B,QAAQlB,IACdoG,EAAG,GAAGpG,EAAK3B,cAAc2B,EAAKL,gBAAgBK,EAAK1B,kBAAkB0B,EAAKpC,QAAUoC,IAGtF,MAAMqG,EAAoBD,EAAGE,wBAC7B,GAAID,EACF,OAAQA,EAAkBE,QACxB,IAAK,QACHxM,EAAMgM,gBAAiB,EACvB,MAEF,IAAK,WACHhM,EAAMgM,gBAAiB,EACvB,MAEF,aClCK,GACbjM,YAAY,EACZC,MAAO,CACLyM,iBAAiB,EACjBC,kBAAkB,EAClBC,eAAe,EACfC,MAAM,EACNC,kBAAmB,GACnBC,4BAA4B,EAC5BC,qBAAqB,EACrBC,oBAAoB,GAEtB5H,UAAW,CACT6H,gBAAkBjN,IAChB,MAAMsF,EAAU8D,OAAOI,MAAM,SAAU,IACvCxJ,EAAMyM,gBAAkBnH,EAAQmH,gBAChCzM,EAAM0M,iBAAmBpH,EAAQoH,iBACjC1M,EAAM2M,cAAgBrH,EAAQqH,cAC9B3M,EAAM4M,KAAOtH,EAAQsH,KACrB5M,EAAM6M,kBAAoBvH,EAAQuH,kBAClC7M,EAAM+M,oBAAsBzH,EAAQyH,oBACpC/M,EAAM8M,2BAA6BxH,EAAQwH,2BAC3C9M,EAAMgN,mBAAqB1H,EAAQ0H,oBAErCE,mBAAmBlN,EAAOsF,GACpB,qBAAsBA,IAAStF,EAAMyM,gBAAkBnH,EAAQ6H,kBAG/D,aAAc7H,GACZ,cAAeA,EAAQ,cAActF,EAAM+M,oBAAsBzH,EAAQ,YAAY8H,cClBjGhH,aAAIiH,IAAIC,QAEO,iBAAIA,OAAKC,MAAM,CAC5BvN,MAAO,CACLwN,UAAWC,iBAEXC,aAAc,GACdhE,QAAS,GACTiE,KAAM,GACN3N,MAAO,GACPwE,SAAU,GACVoJ,QAAS,GACTC,UAAU,EACVC,KAAM1E,OAAO2E,WAAa,IAC1BC,SAAU5E,OAAO2E,WAAa,IAE9BE,oBAAoB,EACpBC,UAAW9E,OAAO+E,UAClBC,kBAAmBhF,OAAOiF,kBAC1BC,qBAAsBlF,OAAOmF,sBAE/BnJ,UAAW,CACToJ,oBAAqBxO,EAAOsF,GAC1BtF,EAAM0N,aAAepI,EAAQnB,gBAC7BnE,EAAM0J,QAAUpE,EAAQoE,QACxB1J,EAAM2N,KAAOrI,EAAQqI,KACrB3N,EAAMA,MAAQsF,EAAQtF,MACtBA,EAAMwE,SAAWc,EAAQd,SACzBxE,EAAM6N,SAAWA,eAASvI,EAAQd,UAClCxE,EAAM4N,QAAUtI,EAAQmJ,SAE1BC,WAAY1O,GACVA,EAAM8N,KAAO1E,OAAO2E,WAAa,IACjC/N,EAAMgO,SAAW5E,OAAO2E,WAAa,KAEvCY,uBAAwB3O,EAAOsF,GAC7BtF,EAAMiO,mBAAqB3I,GAG7BsJ,gBAAiB5O,EAAOsF,GACtB,MAAMuJ,EAAKpJ,KAAKqJ,MAAM9O,EAAMwN,UAAUqB,IAAM,MAC5C1Q,OAAO4Q,OAAOF,EAAIvJ,GAClBtF,EAAMwN,UAAUqB,GAAKpJ,KAAKC,UAAUmJ,KAGxCG,QAAS,GAETC,QAAS,CAAEC,WAAUvQ,WAAUoD,WAAUoN,YAAWC,aAAYC,KAAI7Q,qB,oCC1DtE,W,uBCAAR,EAAOC,QAAU,IAA0B,6C,kCCA3C,udAIA,MAAMqR,EAAWC,aAAMvP,MAAMrB,SACvBoD,EAAWwN,aAAMvP,MAAM+B,SACvByL,EAAY+B,aAAMvP,MAAMwN,UAE9B,IAAIgC,EAActH,aAAaI,QAAQ,gBAAkB,GAMzD,SAASmH,EAAMC,GACb,MAAMC,EAAO,CACXC,OAAWL,aAAMvP,MAAMrB,SAASC,YAAYiR,MAAM,KAAK,GAA/C,eACRC,aAAc,MACdC,MAAO,YACPC,SAAU,cACVrG,KAAM5H,EAAS4H,KAEfsG,UAAW,uBAEbN,EAAKO,OAAS,CACZC,SAAU/G,OAAOI,MAAM,uBAAwB,KAAO8F,EAASzQ,SAC/DuR,QAASd,EAASxQ,OAClBuR,YAAa,MACbC,YAAa9C,EAAU+C,cAAgB,GACvCC,WAAYhB,EACZiB,UAAiC,eAAtBC,OAAqC,OAAOC,OAAgB,MACpEjB,EACHkB,OAAQlB,EAASK,QAEf3G,OAAO+E,WAAa/E,OAAOiF,qBAAmBsB,EAAKO,OAAOG,YAAc7C,EAAUqD,GACzD,OAAzBlB,EAAKO,OAAOC,WACdR,EAAKO,OAAOC,SAAW,KACvBR,EAAKC,OAAS,kBAEa,SAAzBD,EAAKO,OAAOC,WAAqBR,EAAKC,OAAS,kBACnD,MAAM,IAAExF,EAAG,OAAEL,GAAWhI,EACpBqI,IAAQsF,EAAStF,MAAKuF,EAAKO,OAAO9F,IAAMA,GACxCL,IAAQ4F,EAAKO,OAAOnG,OAASA,GAEjC5L,OAAO4Q,OAAOY,EAAMmB,kBAGhBnB,EAAK5F,SAAW4F,EAAKO,OAAOnG,SAAQ4F,EAAKO,OAAOnG,OAAS4F,EAAK5F,QAElE,MAAMgH,EAAU3H,OAAOI,MAAM,mBAAoB,UAC3CwH,EAAS,GAAGC,i8BAAY,wBAA0B3B,EAASzQ,YAAYkS,IAC7E,GAAIG,UAAUC,WACZD,UAAUC,WAAWH,EAAQvL,KAAKC,UAAUiK,QACvC,CACL,MAAMyB,EAAM,IAAIC,MAChBD,EAAIE,IAAM,GAAGN,KAAUO,IAAG7L,UAAUiK,EAAM,CAAE6B,WAAW,OA2CpD,SAASC,IACd,MAAMC,EAAQ,CACZ3B,MAAO,aAETN,EAAKiC,GAIA,SAASC,EAAkBvH,GAChC,MAAMwH,EAAS,CACb7B,MAAO,cACP3F,OAEFqF,EAAKmC,GAIA,SAASC,IACd,MAAMD,EAAS,CACb7B,MAAO,mBACPpP,MAAOoB,EAASpB,MAChBmR,cAAe/P,EAASkI,OACxBH,KAAM/H,EAAS+H,MAEjB2F,EAAKmC,GAIA,SAASpM,EAAoBuM,GAClC,MAAMH,EAAS,CACb7B,MAAO,eACPgC,UAEFtC,EAAKmC,GAIA,SAAS7L,EAAoBgM,GAClC,MAAMH,EAAS,CACb7B,MAAO,gBACPgC,UAEFtC,EAAKmC,GAIA,SAAStL,EAAmB0L,GACjC,MAAMJ,EAAS,CACb7B,MAAO,gBACPkC,UAAWD,EAAOC,UAClBC,mBAAoBF,EAAOnO,MAE7B4L,EAAKmC,GAIA,SAASO,EAAiB3F,EAAQ4F,EAASC,EAAQC,GACxD,MAAMV,EAAS,CACbpF,SACA+F,SAAUH,EACVC,YACGC,GAEL7C,EAAKmC,GAGA,SAASY,EAAyBC,EAAI,IAC3C,MAAMb,EAAS,CACb7B,MAAO,2BACJ0C,GAELhD,EAAKmC,GAhKFpC,IACHA,EAAckD,iBACdxK,aAAaC,QAAQ,cAAeqH,K,uBCXtC,IAAImD,EAAM,CACT,oBAAqB,CACpB,OACA,kBAED,eAAgB,CACf,OACA,kBAED,cAAe,CACd,OACA,kBAED,0BAA2B,CAC1B,OACA,kBAED,eAAgB,CACf,OACA,kBAED,cAAe,CACd,OACA,kBAED,cAAe,CACd,OACA,kBAED,aAAc,CACb,OACA,kBAED,cAAe,CACd,OACA,kBAED,kBAAmB,CAClB,OACA,mBAGF,SAASC,EAAoBC,GAC5B,IAAIC,EAAoBL,EAAEE,EAAKE,GAC9B,OAAOE,QAAQC,UAAUC,MAAK,WAC7B,IAAIC,EAAI,IAAIC,MAAM,uBAAyBN,EAAM,KAEjD,MADAK,EAAEE,KAAO,mBACHF,KAIR,IAAIvT,EAAMgT,EAAIE,GAAMQ,EAAK1T,EAAI,GAC7B,OAAOmT,EAAoBI,EAAEvT,EAAI,IAAIsT,MAAK,WACzC,OAAOH,EAAoB/O,EAAEsP,EAAI,MAGnCT,EAAoBU,KAAO,WAC1B,OAAOnV,OAAOmV,KAAKX,IAEpBC,EAAoBS,GAAK,OACzBrV,EAAOC,QAAU2U,G,qBC5DjB5U,EAAOC,QAAU,IAA0B,uD,qBCA3CD,EAAOC,QAAU,IAA0B,gD,mFCE3C,MAAMC,EAAWC,OAAOC,OAAOC,QACzBE,EAAcJ,OAAOC,OAAOI,QAClCD,EAAYwO,qBAAsB,EAElC,MAAMrO,EAAS,CACbC,SAAU,CACRC,YAAa,aACbC,SAAU,MACVK,SAAU,iBACV4J,MAAO,kBACPhK,OAAQ,OACRgD,iBAAiB,EACjB/C,aAAc,GACdC,aAAc,CAAC,oBACfC,YAAa,CACX,CAAE0G,QAAS,CAAC,SAAU,cAAe4N,GAAItC,+BACzC,CAAEtL,QAAS,CAAC,qBAAsB4N,GAAItC,iCAG1C5R,UAAW,CACTmU,QAAS,CACPC,UAAW,GACXC,YAAa,KACbC,eAAgB,KAChBC,cAAe,KACfC,aAAc,OAGlBvU,QAASpB,EACTqB,OAAQ,CACNuU,aAAc,CACZ,CAAEC,SAAUrU,EAAQ,QAAuCsU,QAAS,IACpE,CAAED,SAAUrU,EAAQ,QAAuCsU,QAAS,IACpE,CAAED,SAAUrU,EAAQ,QAAwCsU,QAAS,KAEvEC,QAASvU,EAAQ,QACjBwU,wBAAyBxU,EAAQ,SAEnCC,IAAK,CACHC,IAAK,eACLkJ,MAAO,uBACPjJ,UAAW,kHAEbC,OAAQvB,GAGKG,QC9Cf,MAAMR,EAAWC,OAAOC,OAAOC,QAC/BH,EAASiW,uBAAyB,2BAClCjW,EAASkW,iBAAmB,uBAC5B,MAAM7V,EAAcJ,OAAOC,OAAOI,QAElCD,EAAYmO,kBAAmB,EAC/BnO,EAAYkO,iBAAkB,EAC9BlO,EAAYwO,qBAAsB,EAElC,MAAMrO,EAAS,CACbC,SAAU,CACRC,YAAa,aACbC,SAAU,MACVK,SAAU,iBACV4J,MAAO,kBACPhK,OAAQ,OACRgD,iBAAiB,EACjB/C,aAAc,GACdC,aAAc,GACdC,YAAa,CACX,CAAE0G,QAAS,CAAC,SAAU,cAAe4N,GAAItC,iCAG7C5R,UAAW,CACTmU,QAAS,CACPC,UAAW,GACXC,YAAa,KACbC,eAAgB,KAChBC,cAAe,KACfC,aAAc,OAGlBvU,QAASpB,EACTqB,OAAQ,CACNuU,aAAc,CACZ,CAAEC,SAAUrU,EAAQ,QAA+CsU,QAAS,IAC5E,CAAED,SAAUrU,EAAQ,QAA+CsU,QAAS,IAC5E,CAAED,SAAUrU,EAAQ,QAAgDsU,QAAS,KAE/EC,QAASvU,EAAQ,QACjBwU,wBAAyBxU,EAAQ,SAEnCC,IAAK,CACHC,IAAK,eACLkJ,MAAO,uBACPjJ,UAAW,kHAEbC,OAAQvB,GAGKG,QClDf,MAAMR,EAAWC,OAAOC,OAAOC,QAC/BH,EAASiW,uBAAyB,2BAClCjW,EAASkW,iBAAmB,uBAC5B,MAAM7V,EAAcJ,OAAOC,OAAOI,QAClCD,EAAYmO,kBAAmB,EAC/BnO,EAAYkO,iBAAkB,EAC9BlO,EAAYwO,qBAAsB,EAElC,MAAMrO,EAAS,CACbC,SAAU,CACRC,YAAa,aACbC,SAAU,MACVK,SAAU,iBACV4J,MAAO,kBACPhK,OAAQ,OACRgD,iBAAiB,EACjB/C,aAAc,GACdC,aAAc,GACdC,YAAa,CACX,CAAE0G,QAAS,CAAC,SAAU,cAAe4N,GAAItC,iCAG7C5R,UAAW,CACTmU,QAAS,CACPC,UAAW,GACXC,YAAa,KACbC,eAAgB,KAChBC,cAAe,KACfC,aAAc,OAGlBvU,QAASpB,EACTqB,OAAQ,CACNuU,aAAc,CACZ,CAAEC,SAAUrU,EAAQ,QAA+CsU,QAAS,IAC5E,CAAED,SAAUrU,EAAQ,QAA+CsU,QAAS,IAC5E,CAAED,SAAUrU,EAAQ,QAAgDsU,QAAS,KAE/EC,QAASvU,EAAQ,QACjBwU,wBAAyBxU,EAAQ,SAEnCC,IAAK,CACHC,IAAK,eACLkJ,MAAO,uBACPjJ,UAAW,kHAEbC,OAAQvB,GAGKG,QCjDf,MAAMR,EAAWC,OAAOC,OAAOC,QAC/BH,EAASI,UAAY,6BACrBJ,EAASiW,uBAAyB,2BAElC,MAAM5V,EAAcJ,OAAOC,OAAOI,QAClCD,EAAYmO,kBAAmB,EAE/B,MAAMhO,EAAS,CACbC,SAAU,CACRC,YAAa,aACbC,SAAU,MACVK,SAAU,iBACV4J,MAAO,kBACPhK,OAAQ,OACRgD,iBAAiB,EACjB/C,aAAc,CAAC,SAAU,cACzBC,aAAc,CAAC,mBAAoB,sBAAuB,eAAgB,qBAC1EsK,MAAM,GAERjK,UAAW,CACTmU,QAAS,CACPC,UAAW,GACXC,YAAa,KACbC,eAAgB,KAChBC,cAAe,KACfC,aAAc,OAGlBvU,QAASpB,EACTqB,OAAQ,CACNuU,aAAc,GAKdG,QAASvU,EAAQ,QACjBwU,wBAAyBxU,EAAQ,SAEnCC,IAAK,CAGHE,UAAW,kHAEbC,OAAQvB,GAGKG,QC9Cf,MAAMR,EAAWC,OAAOC,OAAOC,QAC/BH,EAASmW,UAAY,gBACrBnW,EAASoW,gBAAkB,wBAC3B,MAAM/V,EAAcJ,OAAOC,OAAOI,QAElCD,EAAYqO,MAAO,EACnBrO,EAAYwO,qBAAsB,EAElC,MAAMrO,EAAS,CACbC,SAAU,CACRC,YAAa,cACbC,SAAU,KACVK,SAAU,iBACVJ,OAAQ,SACRgD,iBAAiB,EACjB/C,aAAc,GACdC,aAAc,GACdC,YAAa,IAEfK,QAASpB,EACTqB,OAAQ,CACNuU,aAAc,CACZ,CAAEC,SAAUrU,EAAQ,QAAsCsU,QAAS,KAErEC,QAASvU,EAAQ,QACjBwU,wBAAyBxU,EAAQ,SAEnCC,IAAK,CACHC,IAAK,eACLkJ,MAAO,uBACPjJ,UAAW,mHACX0U,2BAA4B,IAE9BzU,OAAQvB,GAGKG,QCpCf,MAAMR,EAAWC,OAAOC,OAAOC,QAC/BH,EAASmW,UAAY,aACrBnW,EAASkW,iBAAmB,mBAC5BlW,EAASI,UAAY,4BACrBJ,EAASsW,mBAAqB,mBAC9BtW,EAASiW,uBAAyB,0BAElC,MAAM5V,EAAcJ,OAAOC,OAAOI,QAGlCD,EAAYkO,iBAAkB,EAC9BlO,EAAYuO,4BAA6B,EACzCvO,EAAYsO,kBAAoB,eAEhC,MAAMnO,EAAS,CACbC,SAAU,CACRC,YAAa,YACbC,SAAU,MAEVC,OAAQ,QAERE,aAAc,GACdC,YAAa,IAEfI,UAAW,CACTmU,QAAS,CACPC,UAAW,GACXC,YAAa,KACbC,eAAgB,KAChBC,cAAe,KACfC,aAAc,OAGlBvU,QAASpB,EACTqB,OAAQ,CACNuU,aAAc,CACZ,CAAEC,SAAUrU,EAAQ,QAAsCsU,QAAS,IACnE,CAAED,SAAUrU,EAAQ,QAAsCsU,QAAS,IACnE,CAAED,SAAUrU,EAAQ,QAAsCsU,QAAS,KAErEC,QAASvU,EAAQ,QACjBwU,wBAAyBxU,EAAQ,SAEnCC,IAAK,CAGHE,UAAW,iHACX4U,oBAAqB,GACrBC,YAAa,cAEf5U,OAAQvB,GAGKG,QCrDf,MAAMR,EAAWC,OAAOC,OAAOC,QAC/BH,EAASmW,UAAY,aACrBnW,EAASkW,iBAAmB,mBAC5BlW,EAASI,UAAY,8BACrBJ,EAASiW,uBAAyB,0BAElC,MAAM5V,EAAcJ,OAAOC,OAAOI,QAElCD,EAAYkO,iBAAkB,EAC9BlO,EAAYuO,4BAA6B,EACzCvO,EAAYoO,eAAgB,EAC5BpO,EAAYmO,kBAAmB,EAC/BnO,EAAYsO,kBAAoB,eAChCtO,EAAYyO,oBAAqB,EACjCzO,EAAYwO,qBAAsB,EAElC,MAAMrO,EAAS,CACbC,SAAU,CACRC,YAAa,cACbC,SAAU,OACVC,OAAQ,OACRC,aAAc,CAAC,oCACfC,aAAc,GACdC,YAAa,GACb8J,SAAU,SACV4L,SAAU,aAEZtV,UAAW,CACTmU,QAAS,CACPC,UAAW,IACXC,YAAa,KACbC,eAAgB,KAChBC,cAAe,KACfC,aAAc,MAEhBe,qBAAsB,CACpBC,GAAI,IACJC,GAAI,MAEN/D,OAAQ,cAEVzR,QAASpB,EACTqB,OAAQ,CACNuU,aAAc,CACZ,CAAEC,SAAUrU,EAAQ,QAA2CsU,QAAS,KAE1EC,QAASvU,EAAQ,QACjBwU,wBAAyBxU,EAAQ,SAEnCC,IAAK,CACHC,IAAK,eACLkJ,MAAO,uBACPjJ,UAAW,kHACX4U,oBAAqB,GACrBC,YAAa,aACbH,2BAA4B,GAC5BQ,iBAAkB,mBAEpBjV,OAAQvB,GAGKG,QC7Df,MAAMR,EAAWC,OAAOC,OAAOC,QAC/BH,EAASmW,UAAY,iBACrBnW,EAASkW,iBAAmB,uBAC5BlW,EAASI,UAAY,6BACrBJ,EAASoW,gBAAkB,yBAE3B,MAAM/V,EAAcJ,OAAOC,OAAOI,QAElCD,EAAYkO,iBAAkB,EAC9BlO,EAAYoO,eAAgB,EAC5BpO,EAAYmO,kBAAmB,EAC/BnO,EAAYE,iBAAkB,EAC9BF,EAAYyW,cAAe,EAE3B,MAAMtW,EAAS,CACbC,SAAU,CACRC,YAAa,YACbC,SAAU,MACVC,OAAQ,OACRC,aAAc,CAAC,uBAAwB,iBACvCC,aAAc,GACdC,YAAa,GACb0V,SAAU,MAEZtV,UAAW,CACTmU,QAAS,CACPC,UAAW,IACXC,YAAa,KACbC,eAAgB,KAChBC,cAAe,KACfC,aAAc,OAGlBvU,QAASpB,EACTqB,OAAQ,CACNuU,aAAc,CACZ,CAAEC,SAAUrU,EAAQ,QAA+CsU,QAAS,KAE9EC,QAASvU,EAAQ,QACjBwU,wBAAyBxU,EAAQ,SAEnCC,IAAK,CACHC,IAAK,eACLkJ,MAAO,uBACPjJ,UAAW,iHACX6U,YAAa,aACbH,2BAA4B,IAE9BzU,OAAQvB,GAGKG,QCnDf,MAAMR,EAAWC,OAAOC,OAAOC,QAC/BH,EAASmW,UAAY,iBACrBnW,EAASsW,mBAAqB,uBAC9BtW,EAASkW,iBAAmB,uBAC5BlW,EAASoW,gBAAkB,yBAC3BpW,EAASI,UAAY,6BACrB,MAAMC,EAAcJ,OAAOC,OAAOI,QAElCD,EAAYqO,MAAO,EACnBrO,EAAYkO,iBAAkB,EAC9BlO,EAAYyW,cAAe,EAC3BzW,EAAYE,iBAAkB,EAC9BF,EAAYwO,qBAAsB,EAElC,MAAMrO,EAAS,CACbC,SAAU,CACRC,YAAa,oBACbC,SAAU,aAEVC,OAAQ,SACRgD,iBAAiB,EACjB/C,aAAc,GACdC,aAAc,GACdC,YAAa,IAEfK,QAASpB,EACTqB,OAAQ,CACNuU,aAAc,CACZ,CAAEC,SAAUrU,EAAQ,QAA+CsU,QAAS,KAE9EC,QAASvU,EAAQ,SAEnBC,IAAK,CACHC,IAAK,eACLkJ,MAAO,uBACPjJ,UAAW,sHAEbC,OAAQvB,GAGKG,QCxCf,MAAMR,EAAWC,OAAOC,OAAOC,QAC/BH,EAASI,UAAY,4BAErB,MAAMC,EAAcJ,OAAOC,OAAOI,QAGlCD,EAAYyW,cAAe,EAC3BzW,EAAYE,iBAAkB,EAE9B,MAAMC,EAAS,CACbC,SAAU,CACRC,YAAa,YACbC,SAAU,OACVoW,YAAa,KACbnW,OAAQ,QACRE,aAAc,GACdC,YAAa,IAEfI,UAAW,GACXC,QAASpB,EACTqB,OAAQ,GACRI,IAAK,CACHE,UAAW,kHAEbC,OAAQvB,GAGKG,QC3Bf,MAAMR,EAAWC,OAAOC,OAAOC,QAC/BH,EAASI,UAAY,4BAErB,MAAMC,EAAcJ,OAAOC,OAAOI,QAGlCD,EAAYyW,cAAe,EAC3BzW,EAAYE,iBAAkB,EAE9B,MAAMC,EAAS,CACbC,SAAU,CACRC,YAAa,YACbC,SAAU,OACVoW,YAAa,KACbnW,OAAQ,OACRE,aAAc,GACdC,YAAa,IAEfI,UAAW,GACXC,QAASpB,EACTqB,OAAQ,GACRI,IAAK,CACHE,UAAW,kHAEbC,OAAQvB,GAGKG,QC3Bf,MAAMR,EAAWC,OAAOC,OAAOC,QAC/BH,EAASI,UAAY,4BAErB,MAAMC,EAAcJ,OAAOC,OAAOI,QAGlCD,EAAYyW,cAAe,EAC3BzW,EAAYE,iBAAkB,EAG9BF,EAAY2W,cAAe,EAE3B,MAAMxW,EAAS,CACbC,SAAU,CACRC,YAAa,YACbC,SAAU,OACVoW,YAAa,KACbnW,OAAQ,OACRE,aAAc,GACdC,YAAa,IAEfI,UAAW,GACXC,QAASpB,EACTqB,OAAQ,GACRI,IAAK,CACHE,UAAW,kHAEbC,OAAQvB,GAGKG,QC9Bf,MAAMR,GAAWC,OAAOC,OAAOC,QAC/BH,GAASI,UAAY,6BAErB,MAAMC,GAAcJ,OAAOC,OAAOI,QAGlCD,GAAYyW,cAAe,EAC3BzW,GAAYE,iBAAkB,EAG9BF,GAAY2W,cAAe,EAE3B,MAAMxW,GAAS,CACbC,SAAU,CACRC,YAAa,aACbC,SAAU,QACVoW,YAAa,MACbnW,OAAQ,OACRE,aAAc,GACdC,YAAa,IAEfI,UAAW,GACXC,QAASpB,GACTqB,OAAQ,GACRI,IAAK,CACHE,UAAW,mHAEbC,OAAQvB,IAGKG,UC9Bf,MAAMR,GAAWC,OAAOC,OAAOC,QAC/BH,GAASI,UAAY,6BAErB,MAAMC,GAAcJ,OAAOC,OAAOI,QAClCD,GAAYyW,cAAe,EAC3BzW,GAAYE,iBAAkB,EAE9B,MAAMC,GAAS,CACbC,SAAU,CACRC,YAAa,aACbC,SAAU,QACVoW,YAAa,MACbnW,OAAQ,OACRC,aAAc,GACdC,aAAc,GACdC,YAAa,IAEfI,UAAW,GACXC,QAASpB,GACTqB,OAAQ,GACRI,IAAK,CACHC,IAAK,eACLkJ,MAAO,uBACPjJ,UAAW,kHAEbC,OAAQvB,IAGKG,U,8BC9Bf,MAAMyW,GAAUzV,UACVuP,GAAUkG,GAAQ7B,OAAO8B,OAAO,CAACC,EAAKxM,KAC1C,MAAMyM,EAAazM,EAAI0M,QAAQ,mBAAoB,QAEnD,OADAF,EAAIC,GAAcH,GAAQtM,GAAK2M,SAAWL,GAAQtM,GAC3CwM,GACN,IACYpG,UCYf,MAAMwG,GAAY,CAChBC,MACAC,MACAC,MACAC,QACAC,KACAC,MACAC,OACAC,aACAC,OACAC,OACAC,OACAC,SACAC,SACAC,SACGC,IAECC,GAAcrN,OAAOC,WAE3B,SAASqN,KACP,MAAMC,EAAUlB,GAAUgB,IAE1B,OADKE,GAASC,QAAQC,MAASJ,GAAH,cACrB,SAAU5N,EAAKiO,GACpB,GAAIjO,EAAIxG,SAAS,KAAM,CACrB,MAAO0U,EAAMC,GAAQnO,EAAIgH,MAAM,KAC/B,OAAQ8G,EAAQI,IAASJ,EAAQI,GAAMC,IAAUF,EAEnD,OAAOH,EAAQ9N,IAAQiO,GAAgB,IAG5BJ,YC5CftN,OAAOI,MAAQyN,GACf7Q,aAAI8Q,UAAU1N,MAAQyN,GACtB7Q,aAAI8Q,UAAUC,IAAOtO,GAAQ/E,OAAKC,EAAEkT,GAAgB,WAAWpO,IAC/DzC,aAAI8Q,UAAUE,aAAe,CAACvO,EAAKiO,IAAiBG,GAAgB,UAAUpO,EAAOiO,GACrF1Q,aAAI8Q,UAAUG,UAAYjO,OAAOiO,UAAY,CAACxO,EAAKiO,IAAiBG,GAAgB,OAAOpO,EAAOiO,GAClG1Q,aAAI8Q,UAAUI,UAAYlO,OAAOC,W,iBCL/B,IAAIkO,QAAa,CACfC,OAAQ,kCACR1O,MAAOM,OAAOiO,UAAU,SACxBI,SAAS,EACTC,SAAUtR,aACVuR,gBAAgB,EAChBC,MAAO,CACLC,WAAY,W,iBCVlB,SAASC,GAAU1O,EAAQ2O,GACzB,MAAMC,EAAW,KAEXC,EAAa7O,EAAO2E,WAC1B,GAAIkK,EAAa,IAAK,CACpB,MAAMC,EAAQD,EAAa,IAC3BF,EAASI,gBAAgBC,MAAMC,SAAcL,EAAWE,EAAd,UAE1CH,EAASI,gBAAgBC,MAAMC,SADtBJ,EAAa,IACoB,SAEA,OAG5C1I,cAAM+I,OAAO,cAIf,IAAIC,GAFJT,GAAS1O,OAAQ2O,UAGjB3O,OAAOoP,iBAAiB,SAAU,KAChC,MAAMC,EAASA,IAAMX,GAAS1O,OAAQ2O,UAClCQ,IAAU/P,aAAa+P,IAC3BA,GAAW7P,WAAW+P,EAAQ,O,2BCnBhCC,MAAMxB,UAAU7L,UAAY,SAAUsN,EAAM,IAC1C,IAAKD,MAAME,QAAQD,GAAM,MAAMxF,MAAM,+BACrC,MAAO,IAAI0F,MAAMC,OAAOC,GAAKJ,EAAItW,SAAS0W,IAAI7R,QAGhDuG,kBCRA,IAAIuL,GAAS,WAAkB,IAAIC,EAAIJ,KAAKK,EAAGD,EAAIE,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,MAAM,CAACH,EAAI3B,WAAW+B,MAAM,CAAC,GAAK,OAAOC,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAON,EAAIO,MAAMC,MAAM,gBAAgB,CAACP,EAAG,aAAa,CAACG,MAAM,CAAC,QAAU,QAAQ,CAAEJ,EAAIS,UAAYT,EAAIU,gBAAiBT,EAAG,cAAc,CAACrQ,IAAIoQ,EAAIW,OAAOC,WAAWZ,EAAIa,MAAM,GAAGZ,EAAG,YAAY,IAEhVa,GAAkB,GCFlBf,GAAS,WAAkB,IAAIC,EAAIJ,KAAKK,EAAGD,EAAIE,MAAMD,GAAG,OAAOA,EAAG,UAAU,CAACA,EAAG,aAAa,CAACG,MAAM,CAAC,KAAO,OAAO,CAAEJ,EAAIe,UAAWd,EAAG,MAAM,CAACe,YAAY,WAAWhB,EAAIa,OAAOZ,EAAG,aAAa,CAACG,MAAM,CAAC,KAAO,QAAQ,CAAEJ,EAAIiB,SAAWjB,EAAIkB,UAAWjB,EAAGD,EAAIiB,QAAQ,CAACE,IAAI,YAAYf,MAAM,CAAC,OAASJ,EAAIoB,UAAUpB,EAAIa,MAAM,IAAI,IAEnUC,GAAkB,GCFlBf,I,UAAS,WAAkB,IAAIC,EAAIJ,KAAKK,EAAGD,EAAIE,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACA,EAAG,aAAa,CAACG,MAAM,CAAC,KAAO,QAAQ,CAACH,EAAG,MAAM,CAACoB,WAAW,CAAC,CAACxQ,KAAK,OAAOyQ,QAAQ,SAASxO,MAAOkN,EAAIuB,UAAWC,WAAW,cAAcR,YAAY,SAAS,CAACf,EAAG,MAAM,CAACe,YAAY,cAAcZ,MAAM,CAAC,IAAMJ,EAAIyB,UAAUpB,GAAG,CAAC,MAAQL,EAAI0B,YAAYzB,EAAG,SAAS,CAACe,YAAY,cAAcX,GAAG,CAAC,MAAQL,EAAI2B,mBAAmB,KAE/Yb,GAAkB,G,aCWP,IACfjQ,KAAA,eACA+Q,MAAA,CACAR,OAAAlc,QAEA2c,OACA,OACA3R,KAAA,KAAA4R,OAAA/a,MAAArB,SAAAC,YAAAiR,MAAA,QACAmL,QAAA,SACAR,WAAA,EACAS,UAAA,GACAC,MAAA,EACAC,IAAA,GACAtR,KAAA,GACA6Q,SAAA,GACAU,SAAA,KAGAC,QAAA,CACAC,cACA,KAAAd,WAAA,EACA,IAAAe,EAAA,KAAAN,UAAA,KAAAC,OACAM,EAAA,GACAC,EAAA,GAKA,MAAAF,EAAAG,SAAA,CACA,IAAAH,EAAAI,aAAA,KAAAtB,OAAA3Q,SAEA,YADA,KAAAkR,aAEAY,EAAAD,EAAAI,aAAA,KAAAtB,OAAA3Q,cAEA8R,EAAAD,EAAAI,aAAA,KAAAC,MAAAC,QAAA,KAAAhS,OAAA0R,EAAAI,aAAAG,GAEAN,GAIAC,EAAAF,EAAAQ,UACA7T,aAAAC,QAAA,QAAAgB,QAAAoS,EAAAlI,KAAA,KAAA8H,KACAzS,WAAA,KACA,KAAAgS,SAAAc,EACA,KAAAJ,SAAAK,EACA,KAAAjB,WAAA,GACA,MATA,KAAAI,cAWAA,aACA,KAAAM,QACA,KAAAA,OAAA,KAAAD,UAAA/T,QACA,KAAAsT,WAAA,EACA9R,WAAA,SAAA8Q,MAAAC,MAAA,kBAEA,KAAA6B,eAEAX,WACA,IAAAqB,EAAA,GACA,KAAAZ,WAEAY,EADA,KAAAZ,SAAAvL,MAAA,KAAA3I,QAAA,EACA,QAAAkU,mBAAAa,mBAAA/T,aAAAI,QAAA,yBAAAsT,MAAAC,QAAA,KAAAhS,OAEA,QAAAuR,mBAAAa,mBAAA/T,aAAAI,QAAA,yBAAAsT,MAAAC,QAAA,KAAAhS,OAEAT,OAAA8S,KAAAF,EAAA,aAIAG,UACA,MAAAC,EAAAlL,UAAAmL,UAAAnL,UAAAoL,aACA,UAAAF,EAAAG,cAAA,KAAA1S,KAAA,QACAuS,EAAAI,WAAA,WAAA3S,KAAA,QACA,KAAAA,KAAAuS,EAAAvM,MAAA,QAEA,MAAA4M,EAAA,IAAAC,UAAAC,MAAA,QAAAD,MAAAE,oBAAA,KACA,KAAAzB,IAAA,GAAAsB,EAAAI,iBAAAJ,EAAAK,WAAA,KAAAL,EAAAM,YAsBA,KAAA9B,UAAA,KAAAZ,OAAAY,UACA,KAAAA,UAAA/T,OAAA,KAAAoU,cACA,KAAAV,aACA,QAAAoC,EAAA,EAAAA,EAAA,KAAA/B,UAAA/T,OAAA8V,IAAA,CACA,MAAAC,EAAA,IAAA5L,MACA4L,EAAA3L,IACA,QAAA2J,UAAA+B,GAAAtB,SACA,KAAAT,UAAA+B,GAAArB,aAAA,KAAAtB,OAAA3Q,SACA,KAAAuR,UAAA+B,GAAArB,aAAA,KAAAC,MAAAC,QAAA,KAAAhS,SCtHoW,M,yBCQhWqT,GAAY,gBACd,GACA,GACA,IACA,EACA,KACA,WACA,MAIa,GAAAA,G,QCnBXlE,GAAS,WAAkB,IAAIC,EAAIJ,KAAKK,EAAGD,EAAIE,MAAMD,GAAG,OAAOA,EAAG,YAAY,CAACE,MAAM,CAAC,cAAeH,EAAI3B,WAAW+B,MAAM,CAAC,MAAQJ,EAAIkE,GAAG,oBAAoB,GAAK,gBAAgB,CAACjE,EAAG,MAAM,CAACe,YAAY,eAAeb,MAAM,CAACH,EAAI3B,YAAY,CAAC4B,EAAG,MAAM,CAACe,YAAY,YAAY,CAAoB,QAAlBhB,EAAI3B,UAAqB2B,EAAImE,GAAI,IAAI1E,MAAM,GAAG2E,KAAK,KAAK,SAASpX,EAAKqX,GAAG,OAAOpE,EAAG,MAAM,CAACrQ,IAAIyU,EAAErD,YAAY,YAAY,CAAChB,EAAIsE,GAAG,IAAItE,EAAIuE,GAAGvE,EAAIkE,GAAG,0BAAyBG,EAAI,KAAM,KAAKpE,EAAG,MAAMA,EAAG,MAAM,CAACD,EAAImE,GAAInE,EAAIkE,GAAG,yBAAyBG,EAAI,MAAMzN,MAAM,UAAU,SAAS5J,EAAK+W,GAAG,MAAO,CAAQ,IAANM,EAAS,CAAErX,EAAKwX,QAAQ,QAAU,EAAG,CAACxE,EAAIsE,GAAG,IAAItE,EAAIuE,GAAGvX,EAAK4J,MAAM,OAAO,KAAKqJ,EAAG,IAAI,CAACrQ,IAAImU,EAAE3D,MAAM,CAAC,OAAS,UAAU,KAAO,gDAAgD,CAACJ,EAAIsE,GAAG,iDAAiDtE,EAAIsE,GAAGtE,EAAIuE,GAAGvX,EAAK4J,MAAM,OAAO,IAAI,MAAO5J,EAAKwX,QAAQ,QAAU,EAAG,CAACxE,EAAIsE,GAAG,IAAItE,EAAIuE,GAAGvX,EAAK4J,MAAM,OAAO,KAAKqJ,EAAG,IAAI,CAACrQ,IAAImU,EAAE3D,MAAM,CAAC,OAAS,UAAU,KAAO,oCAAoC,CAACJ,EAAIsE,GAAG,qCAAqCtE,EAAIsE,GAAGtE,EAAIuE,GAAGvX,EAAK4J,MAAM,OAAO,IAAI,MAAM,CAACoJ,EAAIsE,GAAGtE,EAAIuE,GAAGvX,KAAQiT,EAAG,KAAK,CAACrQ,IAAI,GAAGmU,KAAKM,OAAY,IAAJA,EAAO,CAACrE,EAAIsE,GAAGtE,EAAIuE,GAAGvX,GAAM,KAAKiT,EAAG,KAAK,CAACrQ,IAAImU,EAAI,QAAQ/D,EAAIsE,GAAG,IAAItE,EAAIuE,GAAGvE,EAAIkE,GAAG,8BAA8B,KAAKjE,EAAG,KAAK,CAACrQ,IAAImU,KAAK,CAAC/D,EAAIsE,GAAGtE,EAAIuE,GAAGvX,GAAM,KAAKiT,EAAG,KAAK,CAACrQ,IAAImU,UAAS,QAA0B,QAAlB/D,EAAI3B,WAAyC,QAAlB2B,EAAI3B,UAAqB2B,EAAImE,GAAI,IAAI1E,MAAM,GAAG2E,KAAK,KAAK,SAASpX,EAAKqX,GAAG,OAAOpE,EAAG,MAAM,CAACrQ,IAAIyU,EAAErD,YAAY,YAAY,CAAChB,EAAIsE,GAAG,IAAItE,EAAIuE,GAAGvE,EAAIkE,GAAG,GAAGlE,EAAI8B,OAAO/a,MAAMrB,SAASwK,0BAA0BmU,EAAI,MAAM,KAAKpE,EAAG,MAAMA,EAAG,MAAM,CAACD,EAAImE,GAAInE,EAAIkE,GAAG,GAAGlE,EAAI8B,OAAO/a,MAAMrB,SAASwK,0BAA0BmU,EAAI,MAAMzN,MAAM,UAAU,SAAS5J,EAAK+W,GAAG,MAAO,CAAQ,IAANM,EAAS,CAAErX,EAAKwX,QAAQ,QAAU,EAAG,CAACxE,EAAIsE,GAAG,IAAItE,EAAIuE,GAAGvX,EAAK4J,MAAM,OAAO,KAAKqJ,EAAG,IAAI,CAACrQ,IAAImU,EAAE3D,MAAM,CAAC,OAAS,UAAU,KAAO,gDAAgD,CAACJ,EAAIsE,GAAG,iDAAiDtE,EAAIsE,GAAGtE,EAAIuE,GAAGvX,EAAK4J,MAAM,OAAO,IAAI,MAAO5J,EAAKwX,QAAQ,QAAU,EAAG,CAACxE,EAAIsE,GAAG,IAAItE,EAAIuE,GAAGvX,EAAK4J,MAAM,OAAO,KAAKqJ,EAAG,IAAI,CAACrQ,IAAImU,EAAE3D,MAAM,CAAC,OAAS,UAAU,KAAO,oCAAoC,CAACJ,EAAIsE,GAAG,qCAAqCtE,EAAIsE,GAAGtE,EAAIuE,GAAGvX,EAAK4J,MAAM,OAAO,IAAI,MAAM,CAACoJ,EAAIsE,GAAGtE,EAAIuE,GAAGvX,KAAQiT,EAAG,KAAK,CAACrQ,IAAI,GAAGmU,KAAKM,OAAY,IAAJA,EAAO,CAACrE,EAAIsE,GAAGtE,EAAIuE,GAAGvX,GAAM,KAAKiT,EAAG,KAAK,CAACrQ,IAAImU,EAAI,QAAQ/D,EAAIsE,GAAG,IAAItE,EAAIuE,GAAGvE,EAAIkE,GAAMlE,EAAI8B,OAAO/a,MAAMrB,SAASwK,KAA7B,2BAA4D,KAAK+P,EAAG,KAAK,CAACrQ,IAAImU,KAAK,CAAC/D,EAAIsE,GAAGtE,EAAIuE,GAAGvX,GAAM,KAAKiT,EAAG,KAAK,CAACrQ,IAAImU,UAAS,QAA0B,OAAlB/D,EAAI3B,UAAoB2B,EAAImE,GAAI,IAAI1E,MAAM,GAAG2E,KAAK,KAAK,SAASpX,EAAKqX,GAAG,OAAOpE,EAAG,MAAM,CAACrQ,IAAIyU,EAAErD,YAAY,YAAY,CAAChB,EAAIsE,GAAG,IAAItE,EAAIuE,GAAGvE,EAAIkE,GAAG,GAAGlE,EAAI8B,OAAO/a,MAAMrB,SAASwK,0BAA0BmU,EAAI,MAAM,KAAKpE,EAAG,MAAMA,EAAG,MAAM,CAACD,EAAImE,GAAInE,EAAIkE,GAAG,GAAGlE,EAAI8B,OAAO/a,MAAMrB,SAASwK,0BAA0BmU,EAAI,MAAMzN,MAAM,UAAU,SAAS5J,EAAK+W,GAAG,MAAO,CAAM,IAAJM,EAAO,CAACrE,EAAIsE,GAAGtE,EAAIuE,GAAGvX,GAAM,KAAKiT,EAAG,KAAK,CAACrQ,IAAImU,EAAI,QAAQ/D,EAAIsE,GAAG,IAAItE,EAAIuE,GAAGvE,EAAIkE,GAAMlE,EAAI8B,OAAO/a,MAAMrB,SAASwK,KAA7B,2BAA4D,KAAK+P,EAAG,KAAK,CAACrQ,IAAImU,KAAK,CAAC/D,EAAIsE,GAAGtE,EAAIuE,GAAGvX,GAAM,KAAKiT,EAAG,KAAK,CAACrQ,IAAImU,UAAS,QAA0B,QAAlB/D,EAAI3B,UAAqB,CAAC2B,EAAImE,GAAI,IAAI1E,MAAM,GAAG2E,KAAK,KAAK,SAASpX,EAAKqX,GAAG,OAAOpE,EAAG,MAAM,CAACrQ,IAAIyU,EAAErD,YAAY,YAAY,CAAChB,EAAIsE,GAAG,IAAItE,EAAIuE,GAAGvE,EAAIkE,GAAG,sBAAqBG,EAAI,KAAM,KAAKpE,EAAG,MAAMA,EAAG,MAAM,CAACD,EAAImE,GAAInE,EAAIkE,GAAG,qBAAqBG,EAAI,MAAMzN,MAAM,UAAU,SAAS5J,EAAK+W,GAAG,MAAO,CAAQ,IAANM,EAAS,CAAErX,EAAKwX,QAAQ,QAAU,EAAG,CAACxE,EAAIsE,GAAG,IAAItE,EAAIuE,GAAGvX,EAAK4J,MAAM,OAAO,KAAKqJ,EAAG,IAAI,CAACrQ,IAAImU,EAAE3D,MAAM,CAAC,OAAS,UAAU,KAAO,6CAA6C,CAACJ,EAAIsE,GAAG,8CAA8CtE,EAAIsE,GAAGtE,EAAIuE,GAAGvX,EAAK4J,MAAM,OAAO,IAAI,MAAO5J,EAAKwX,QAAQ,QAAU,EAAG,CAACxE,EAAIsE,GAAG,IAAItE,EAAIuE,GAAGvX,EAAK4J,MAAM,OAAO,KAAKqJ,EAAG,IAAI,CAACrQ,IAAImU,EAAE3D,MAAM,CAAC,OAAS,UAAU,KAAO,kCAAkC,CAACJ,EAAIsE,GAAG,mCAAmCtE,EAAIsE,GAAGtE,EAAIuE,GAAGvX,EAAK4J,MAAM,OAAO,IAAI,MAAM,CAACoJ,EAAIsE,GAAGtE,EAAIuE,GAAGvX,KAAQiT,EAAG,KAAK,CAACrQ,IAAI,GAAGmU,KAAKM,OAAY,IAAJA,EAAO,CAACrE,EAAIsE,GAAGtE,EAAIuE,GAAGvX,GAAM,KAAKiT,EAAG,KAAK,CAACrQ,IAAImU,EAAI,QAAQ/D,EAAIsE,GAAG,IAAItE,EAAIuE,GAAGvE,EAAIkE,GAAG,0BAA0B,KAAKjE,EAAG,KAAK,CAACrQ,IAAImU,KAAK,CAAC/D,EAAIsE,GAAGtE,EAAIuE,GAAGvX,GAAM,KAAKiT,EAAG,KAAK,CAACrQ,IAAImU,UAAS,QAAO9D,EAAG,MAAM,CAACrQ,IAAI,KAAKoR,YAAY,YAAY,CAAChB,EAAIsE,GAAG,IAAItE,EAAIuE,GAAGvE,EAAIkE,GAAG,yBAAyB,KAAKjE,EAAG,MAAMA,EAAG,MAAM,CAACD,EAAIsE,GAAG,IAAItE,EAAIuE,GAAGvE,EAAIkE,GAAG,0BAA0B,UAA6B,SAAlBlE,EAAI3B,UAAsB2B,EAAImE,GAAI,IAAI1E,MAAM,GAAG2E,KAAK,KAAK,SAASpX,EAAKqX,GAAG,OAAOpE,EAAG,MAAM,CAACrQ,IAAIyU,EAAErD,YAAY,YAAY,CAAChB,EAAIsE,GAAG,IAAItE,EAAIuE,GAAGvE,EAAIkE,GAAG,GAAGlE,EAAIyE,SAASJ,EAAI,MAAM,KAAKpE,EAAG,MAAMA,EAAG,MAAM,CAACD,EAAImE,GAAInE,EAAIkE,GAAG,GAAGlE,EAAIyE,SAASJ,EAAI,MAAMzN,MAAM,UAAU,SAAS5J,EAAK+W,GAAG,MAAO,CAAM,IAAJM,EAAO,CAACrE,EAAIsE,GAAGtE,EAAIuE,GAAGvX,GAAM,KAAKiT,EAAG,KAAK,CAACrQ,IAAImU,EAAI,QAAQ/D,EAAIsE,GAAG,IAAItE,EAAIuE,GAAGvE,EAAIkE,GAAMlE,EAAIyE,OAAP,QAAqB,KAAKxE,EAAG,KAAK,CAACrQ,IAAImU,KAAK,CAAC/D,EAAIsE,GAAGtE,EAAIuE,GAAGvX,GAAM,KAAKiT,EAAG,KAAK,CAACrQ,IAAImU,UAAS,QAA0B,eAAlB/D,EAAI3B,UAA4B2B,EAAImE,GAAI,IAAI1E,MAAM,GAAG2E,KAAK,KAAK,SAASpX,EAAKqX,GAAG,OAAOpE,EAAG,MAAM,CAACrQ,IAAIyU,EAAErD,YAAY,YAAY,CAAChB,EAAIsE,GAAG,IAAItE,EAAIuE,GAAGvE,EAAIkE,GAAG,0BAAyBG,EAAI,KAAM,KAAKpE,EAAG,MAAMA,EAAG,MAAM,CAACD,EAAImE,GAAInE,EAAIkE,GAAG,yBAAyBG,EAAI,MAAMzN,MAAM,UAAU,SAAS5J,EAAK+W,GAAG,MAAO,CAAM,IAAJM,EAAO,CAACrE,EAAIsE,GAAGtE,EAAIuE,GAAGvX,GAAM,KAAKiT,EAAG,KAAK,CAACrQ,IAAImU,EAAI,QAAQ/D,EAAIsE,GAAG,IAAItE,EAAIuE,GAAGvE,EAAIkE,GAAG,8BAA8B,KAAKjE,EAAG,KAAK,CAACrQ,IAAImU,KAAK,CAAC/D,EAAIsE,GAAGtE,EAAIuE,GAAGvX,GAAM,KAAKiT,EAAG,KAAK,CAACrQ,IAAImU,UAAS,QAA0B,QAAlB/D,EAAI3B,UAAqB2B,EAAImE,GAAI,IAAI1E,MAAM,GAAG2E,KAAK,KAAK,SAASpX,EAAKqX,GAAG,OAAOpE,EAAG,MAAM,CAACrQ,IAAIyU,EAAErD,YAAY,YAAY,CAAChB,EAAIsE,GAAG,IAAItE,EAAIuE,GAAGvE,EAAIkE,GAAG,0BAAyBG,EAAI,KAAM,KAAKpE,EAAG,MAAMA,EAAG,MAAM,CAACD,EAAImE,GAAInE,EAAIkE,GAAG,yBAAyBG,EAAI,MAAMzN,MAAM,UAAU,SAAS5J,EAAK+W,GAAG,MAAO,CAAM,IAAJM,EAAO,CAACrE,EAAIsE,GAAGtE,EAAIuE,GAAGvX,GAAM,KAAKiT,EAAG,KAAK,CAACrQ,IAAImU,EAAI,QAAQ/D,EAAIsE,GAAG,IAAItE,EAAIuE,GAAGvE,EAAIkE,GAAG,8BAA8B,KAAKjE,EAAG,KAAK,CAACrQ,IAAImU,KAAK,CAAC/D,EAAIsE,GAAGtE,EAAIuE,GAAGvX,GAAM,KAAKiT,EAAG,KAAK,CAACrQ,IAAImU,UAAS,QAAO/D,EAAImE,GAAI,IAAI1E,MAAM,GAAG2E,KAAK,KAAK,SAASpX,EAAKqX,GAAG,OAAOpE,EAAG,MAAM,CAACrQ,IAAIyU,EAAErD,YAAY,YAAY,CAAChB,EAAIsE,GAAG,IAAItE,EAAIuE,GAAGvE,EAAIkE,GAAG,GAAGlE,EAAI8B,OAAO/a,MAAMrB,SAASwK,0BAA0BmU,EAAI,MAAM,KAAKpE,EAAG,MAAMA,EAAG,MAAM,CAACD,EAAImE,GAAInE,EAAIkE,GAAG,GAAGlE,EAAI8B,OAAO/a,MAAMrB,SAASwK,0BAA0BmU,EAAI,MAAMzN,MAAM,UAAU,SAAS5J,EAAK+W,GAAG,MAAO,CAAM,IAAJM,EAAO,CAACrE,EAAIsE,GAAGtE,EAAIuE,GAAGvX,GAAM,KAAKiT,EAAG,KAAK,CAACrQ,IAAImU,EAAI,QAAQ/D,EAAIsE,GAAG,IAAItE,EAAIuE,GAAGvE,EAAIkE,GAAMlE,EAAI8B,OAAO/a,MAAMrB,SAASwK,KAA7B,2BAA4D,KAAK+P,EAAG,KAAK,CAACrQ,IAAImU,KAAK,CAAC/D,EAAIsE,GAAGtE,EAAIuE,GAAGvX,GAAM,KAAKiT,EAAG,KAAK,CAACrQ,IAAImU,UAAS,SAAQ,KAAK9D,EAAG,MAAM,CAACe,YAAY,YAE7qMF,GAAkB,G,aC+IP,IACfjQ,KAAA,qBACA6T,WAAA,CAAAC,mBACAC,SAAA,CACAH,SACA,MAAAI,EAAA,CACA9H,KAAA,2BAEA,OAAA8H,EAAA,KAAAxG,cCzJ0W,MCQtW,I,UAAY,gBACd,GACA,GACA,IACA,EACA,KACA,WACA,OAIa,M,QCnBX0B,GAAS,WAAkB,IAAIC,EAAIJ,KAAKK,EAAGD,EAAIE,MAAMD,GAAG,OAAOA,EAAG,YAAY,CAACG,MAAM,CAAC,MAAQJ,EAAIkE,GAAG,0BAA0B,OAASlE,EAAIoB,SAAS,CAACnB,EAAG,MAAM,CAACe,YAAY,gBAAgB,CAACf,EAAG,MAAM,CAACe,YAAY,WAAW,CAAChB,EAAIsE,GAAGtE,EAAIuE,GAAGvE,EAAIkE,GAAG,kCAEpPpD,GAAkB,GCQP,IACfjQ,KAAA,sBACA6T,WAAA,CAAAC,mBACA9C,OACA,OACAT,OAAA,CACA0D,MAAA,KAAAZ,GAAA,0BACAa,cAAA,KAAAb,GAAA,gBACAc,iBAAA,KAAAC,QAAA3I,QAAA,SClB2W,MCQvW,I,UAAY,gBACd,GACA,GACA,IACA,EACA,KACA,WACA,OAIa,M,QCnBXyD,GAAS,WAAkB,IAAIC,EAAIJ,KAAKK,EAAGD,EAAIE,MAAMD,GAAG,OAAOA,EAAG,YAAY,CAACe,YAAY,2BAA2Bb,MAAM,CAACH,EAAI2C,MAAMC,QAAQxC,MAAM,CAAC,cAAa,EAAK,GAAK,wBAAwB8E,YAAYlF,EAAImF,GAAG,CAAC,CAACvV,IAAI,YAAYwV,GAAG,WAAW,MAAO,CAACnF,EAAG,MAAM,CAACe,YAAY,wBAAwBX,GAAG,CAAC,MAAQL,EAAIqF,SAAS,CAACrF,EAAIsE,GAAGtE,EAAIuE,GAAGvE,EAAIkE,GAAG,+BAA+BjE,EAAG,MAAM,CAACe,YAAY,oBAAoBX,GAAG,CAAC,MAAQL,EAAIsF,KAAK,CAACtF,EAAIsE,GAAGtE,EAAIuE,GAAGvE,EAAIkE,GAAG,6BAA6BqB,OAAM,MAAS,CAACtF,EAAG,MAAM,CAACe,YAAY,QAAQ,CAAChB,EAAIsE,GAAGtE,EAAIuE,GAAGvE,EAAIkE,GAAG,gCAEljBpD,GAAkB,GCYP,IACfjQ,KAAA,kBACA+Q,MAAA,CACAR,OAAAlc,QAEAwf,WAAA,CAAAC,mBACAvC,QAAA,CACAkD,KACA,KAAA/E,MAAAC,MAAA,YACA,KAAAD,MAAAC,MAAA,6BAEA6E,SACA,KAAA9E,MAAAC,MAAA,YACA,KAAAD,MAAAC,MAAA,+BC3BuW,MCQnW,I,UAAY,gBACd,GACA,GACA,IACA,EACA,KACA,WACA,OAIa,M,QCnBXT,GAAS,WAAkB,IAAIC,EAAIJ,KAAKK,EAAGD,EAAIE,MAAMD,GAAG,OAAOA,EAAG,YAAY,CAACe,YAAY,UAAUZ,MAAM,CAAC,cAAa,IAAO,CAACH,EAAG,MAAM,CAACe,YAAY,eAAe,CAAChB,EAAIsE,GAAGtE,EAAIuE,GAAGvE,EAAIkE,GAAG,kBAAkB,KAAKjE,EAAG,IAAI,CAACuF,SAAS,CAAC,UAAYxF,EAAIuE,GAAGvE,EAAIyF,iBAE5P3E,GAAkB,GCOP,IACfjQ,KAAA,eACA6T,WAAA,CAAAC,mBACA/C,MAAA,CACAR,OAAAlc,QAEA2c,OACA,OACAvC,cAAA9P,EACAkW,WAAA,IAGAd,SAAA,CACAa,UACA,YAAAvB,GAAA,qBAAA5H,QAAA,kBAAAoJ,uBAGAC,UACA,MAAArG,EAAAsG,YAAA,KACA,KAAAF,WAAA,KAAAA,WAAA,EAEA,SAAAA,aACAG,cAAAvG,GACA,KAAAiB,MAAAC,MAAA,YACA/Q,WAAA,KACA,MAAAqB,EAAA,KAAAsQ,OAAAtQ,OACAX,OAAA2B,SAAAC,KAAA,sCAAAiR,mBAAAlS,kBACA,KAEA,OCtCoW,MCQhW,I,UAAY,gBACd,GACA,GACA,IACA,EACA,KACA,WACA,OAIa,M,QCnBXiP,GAAS,WAAkB,IAAIC,EAAIJ,KAAKK,EAAGD,EAAIE,MAAMD,GAAG,OAAOA,EAAG,YAAY,CAACe,YAAY,yBAAyBb,MAAM,CAACH,EAAI2C,MAAMC,OAAQ5C,EAAI3B,YAAY,CAAC4B,EAAG,MAAM,CAACe,YAAY,SAAS,CAAChB,EAAIsE,GAAG,IAAItE,EAAIuE,GAAGvE,EAAIkE,GAAG,yBAAyB,KAAKjE,EAAG,IAAI,CAACe,YAAY,QAAQX,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAON,EAAIO,MAAMC,MAAM,kBAAkBP,EAAG,MAAM,CAACe,YAAY,yBAAyB,CAACf,EAAG,OAAO,CAACe,YAAY,aAAa,CAAChB,EAAIsE,GAAGtE,EAAIuE,GAAGvE,EAAIhW,SAASiW,EAAG,KAAKD,EAAIsE,GAAG,OAAOrE,EAAG,QAAQ,CAACoB,WAAW,CAAC,CAACxQ,KAAK,QAAQyQ,QAAQ,UAAUxO,MAAOkN,EAAI8F,IAAKtE,WAAW,QAAQrB,MAAM,CAAE,cAAeH,EAAI+F,YAAa3F,MAAM,CAAC,KAAO,SAAS,YAAc,IAAIoF,SAAS,CAAC,MAASxF,EAAI8F,KAAMzF,GAAG,CAAC,MAAQ,CAAC,SAASC,GAAWA,EAAOvI,OAAOiO,YAAiBhG,EAAI8F,IAAIxF,EAAOvI,OAAOjF,QAAOkN,EAAIiG,YAAY,KAAOjG,EAAIkG,yBAAyBjG,EAAG,MAAM,CAACe,YAAY,OAAOb,MAAM,CAAE,cAAeH,EAAI+F,aAAc,CAAC/F,EAAIsE,GAAG,IAAItE,EAAIuE,GAAGvE,EAAI9B,IAAI,8BAA8B+B,EAAG,MAAM,CAACe,YAAY,oBAAoB,CAACf,EAAG,MAAM,CAACe,YAAY,QAAQ,CAACf,EAAG,MAAM,CAACe,YAAY,iBAAiB,CAAChB,EAAIsE,GAAGtE,EAAIuE,GAAGvE,EAAIkE,GAAG,eAAe,IAAIlE,EAAIuE,GAAGvE,EAAIhW,KAAOgW,EAAI8F,KAAK,KAAK7F,EAAG,OAAQD,EAAImG,WAAWC,SAAUnG,EAAG,MAAM,CAACE,MAAM,CAAC,YAAY,CAAC,aAAcH,EAAIpL,YAAY,CAACoL,EAAIsE,GAAGtE,EAAIuE,GAAGvE,EAAImG,WAAWC,aAAapG,EAAIa,KAAMb,EAAImG,WAAWE,YAAapG,EAAG,MAAM,CAACE,MAAM,CAAC,eAAe,CAAC,aAAcH,EAAIpL,YAAY,CAACoL,EAAIsE,GAAGtE,EAAIuE,GAAGvE,EAAImG,WAAWE,gBAAgBrG,EAAIa,OAAOZ,EAAG,MAAM,CAACe,YAAY,SAAS,CAACf,EAAG,MAAM,CAACe,YAAY,MAAMX,GAAG,CAAC,MAAQL,EAAIsG,SAAS,CAACtG,EAAIsE,GAAGtE,EAAIuE,GAAGvE,EAAIkE,GAAG,0BAEpgDpD,GAAkB,G,0BCiDtB,MAAAyF,GAAA,IACAC,GAAArW,OAAAI,MAAA,8BACe,QACfM,KAAA,gBACA6T,WAAA,CAAAC,mBACA/C,MAAA,CACAR,OAAAlc,QAEA2c,OACA,OACAiE,IAAAU,GACAhc,MAAA,EACAR,KAAA,EACA0F,eAAA,GACA+W,gBAAA,GACAC,aAAA,IAGAtE,QAAA,CACAkE,SACA,QAAAP,WAAA,YACA,KAAA3E,OAAAuF,IAAA,KAAAvF,OAAAuF,GAAA,KAAAb,KACA,KAAAvF,MAAAC,MAAA,aAEAyF,aACA,KAAAH,IAAAc,KAAAC,MAAA,KAAAf,KACA,KAAAA,IAAAS,KAAA,KAAAT,IAAAS,KAEAL,sBACA,KAAAJ,IAAAS,KAAA,KAAAT,IAAAS,IACA,KAAAT,IAAAU,KAAA,KAAAV,IAAAU,MAGA5B,SAAA,IACAkC,gBAAA,oEACAA,gBAAA,4BACAC,gBAAA,uDACAC,YACA,OAAAxc,GAAAoB,gBAAApB,EAAA,KAAAe,WAEAwa,aACA,YAAAD,IAAAU,IAAA,KAAAV,IAAAS,IAEAJ,aAEA,MAAAA,EAAA,CAAAC,SAAA,EAAAC,YAAA,GAWA,OAVA,KAAAva,kBAAA,CAAA3C,WAAA,KAAAsd,mBACAN,EAAAC,SAAA,KAAAY,UAAA,KAAAxc,MAAA,KAAAsb,IAAA,KACAK,EAAAE,YAAA,KAAAW,UAAA,KAAAxc,MAAA,KAAAsb,MAGAK,EAAAC,SAAA,KAAAY,UAAA,KAAAxc,MAAA,KAAAsb,KAGAK,EAAAC,WAAAD,EAAAC,UAAA,SAAA1W,gBACAyW,EAAAE,cAAAF,EAAAE,aAAA,SAAA3W,gBACAyW,IAGAjD,UACA,cAAAnX,GAAA,KAAAqV,OACA,KAAA5W,MAAAuB,EAAApB,qBACA,KAAAX,KAAA+B,EAAA/B,KACA,KAAA8b,IAAA/Z,EAAAL,UACA,KAAAgE,eAAA3D,EAAAb,gBACA,KAAAub,gBAAA1a,EAAA5C,WACA,KAAAud,aAAA3a,EAAA2a,eCrHqW,MCQjW,I,UAAY,gBACd,GACA,GACA,IACA,EACA,KACA,WACA,OAIa,M,QCnBX3G,GAAS,WAAkB,IAAIC,EAAIJ,KAAKK,EAAGD,EAAIE,MAAMD,GAAG,OAAOA,EAAG,YAAY,CAACe,YAAY,2BAA2Bb,MAAM,CAACH,EAAI2C,MAAMC,QAAQxC,MAAM,CAAC,eAAgB,EAAK,KAAO,6BAA6B,YAAa,EAAK,YAAa,IAAO,CAACH,EAAG,IAAI,CAACI,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAON,EAAIO,MAAMC,MAAM,gBAAgBP,EAAG,MAAM,CAACe,YAAY,4BAA4B,CAACf,EAAG,MAAM,CAACe,YAAY,gBAAgB,CAACf,EAAG,MAAM,CAACe,YAAY,SAAS,CAAChB,EAAIsE,GAAGtE,EAAIuE,GAAGvE,EAAIkE,GAAG,wCAAwCjE,EAAG,MAAM,CAACe,YAAY,UAAUf,EAAG,MAAM,CAACe,YAAY,MAAMX,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAON,EAAIO,MAAMC,MAAM,eAAe,CAACR,EAAIsE,GAAGtE,EAAIuE,GAAGvE,EAAIkE,GAAG,uBAE9oBpD,GAAkB,GCcP,IACfjQ,KAAA,iBACA6T,WAAA,CAAAC,oBClBsW,MCQlW,I,UAAY,gBACd,GACA,GACA,IACA,EACA,KACA,WACA,OAIa,M,QCnBX5E,GAAS,WAAkB,IAAIC,EAAIJ,KAAKK,EAAGD,EAAIE,MAAMD,GAAG,OAAOA,EAAG,eAAe,CAACA,EAAG,MAAM,CAACe,YAAY,wBAAwB,CAAe,QAAbhB,EAAIpV,KAAgB,CAACqV,EAAG,MAAM,CAACe,YAAY,SAAS,CAAChB,EAAIsE,GAAGtE,EAAIuE,GAAGvE,EAAIkE,GAAG,2BAA2BjE,EAAG,MAAM,CAACe,YAAY,qBAAqBf,EAAG,MAAM,CAACe,YAAY,MAAMX,GAAG,CAAC,MAAQL,EAAIiH,UAAU,CAACjH,EAAIsE,GAAGtE,EAAIuE,GAAGvE,EAAIkE,GAAG,oBAAoBjE,EAAG,MAAM,CAACe,YAAY,QAAQ,CAAChB,EAAIsE,GAAGtE,EAAIuE,GAAGvE,EAAIkE,GAAG,+BAA+BlE,EAAIa,KAAmB,QAAbb,EAAIpV,KAAgB,CAACqV,EAAG,MAAM,CAACe,YAAY,SAAS,CAAChB,EAAIsE,GAAGtE,EAAIuE,GAAGvE,EAAIkE,GAAG,2BAA2BjE,EAAG,MAAM,CAACe,YAAY,OAAOwE,SAAS,CAAC,UAAYxF,EAAIuE,GAAGvE,EAAIkE,GAAG,sBAAuB,CAAEnZ,EAAG,SAASiV,EAAIkH,sBAAuBjH,EAAG,MAAM,CAACe,YAAY,qBAAqBf,EAAG,MAAM,CAACe,YAAY,cAAcX,GAAG,CAAC,MAAQL,EAAImH,QAAQ,CAACnH,EAAIsE,GAAGtE,EAAIuE,GAAGvE,EAAIkE,GAAG,kBAAkBjE,EAAG,MAAM,CAACe,YAAY,SAASX,GAAG,CAAC,MAAQL,EAAIoH,aAAa,CAACpH,EAAIsE,GAAGtE,EAAIuE,GAAGvE,EAAIkE,GAAG,sBAAsBlE,EAAIa,KAAmB,WAAbb,EAAIpV,KAAmB,CAACqV,EAAG,MAAM,CAACe,YAAY,SAAS,CAAChB,EAAIsE,GAAGtE,EAAIuE,GAAGvE,EAAIkE,GAAG,2BAA2BjE,EAAG,MAAM,CAACe,YAAY,QAAQ,CAAChB,EAAIsE,GAAGtE,EAAIuE,GAAGvE,EAAIqH,eAAepH,EAAG,MAAM,CAACE,MAAM,CAAC,gBAAkBH,EAAInP,QAAQoP,EAAG,MAAM,CAACe,YAAY,iBAAiBX,GAAG,CAAC,MAAQL,EAAImH,QAAQ,CAACnH,EAAIsE,GAAGtE,EAAIuE,GAAGvE,EAAIkE,GAAG,mBAAmBlE,EAAIa,KAAKZ,EAAG,MAAM,CAACe,YAAY,QAAQX,GAAG,CAAC,MAAQL,EAAImH,UAAU,MAEtzCrG,GAAkB,GC6BtB,MAAAwG,GAAA,4EAEe,QACfzW,KAAA,cACA6T,WAAA,CAAA6C,qBACA3F,MAAA,WACAC,OACA,IAAAjX,EAAA,GACAsc,EAAA,EACArW,EAAA,GACAwW,EAAA,GAEA,QAAAjG,OAAAxW,OAAA,KAAAwW,OAAAxW,SACA,CACA,MAAA4c,EAAA,KAAApG,OAAAoG,OACA3W,EAAA2W,EAAAC,UAAA,GAAAC,UACA7W,EAAAzH,SAAA,UACAwB,EAAA,MACAsc,EAAAM,EAAAC,UAAA,GAAAE,WAEA9W,EAAAzH,SAAA,aACAwB,EAAA,SACAyc,EAAA,KAAAnD,GAAAoD,GAAAzW,KAGA,OACAjG,OACAiG,OAEAqW,SACAG,eAGAzC,SAAA,CACAgD,sBAGA,MAAAC,EAAA7P,i8BAAA,oCAAAqG,WAAA,WAAAsE,MAAAC,OACA,OAAAiF,IAGAzF,QAAA,CACA6E,UACA,SAAAnF,OAAA/a,MAAA+B,SAAAC,QAGA,OAFA,KAAAwX,MAAAC,MAAA,YACA,KAAAD,MAAAC,MAAA,uBACA,KAGA,KAAAsH,SAAAC,OACAC,gBAAA,CAAAC,GAAA,MAAArM,GAAA,GAAAC,GAAA,OACA7B,KAAAkO,IACA,WAAA/N,EAAA,KAAA0H,EAAA,IAAAqG,EACA,IAAA/N,GAAA0H,EAAA5T,QACA,KAAA6T,OAAAzC,OAAA,oCACA,KAAAkB,MAAAC,MAAA,YAEA/Q,WAAA,KACA,KAAA8Q,MAAAC,MAAA,yBAAAgH,OAAA3F,EAAA,MACA,IAEA,KAAAsG,OAAAC,IAAA,KAAAlE,GAAA,kBAGAmE,MAAA,SAAAF,OAAAC,IAAA,KAAAlE,GAAA,iBACAoE,QAAA,SAAAR,SAAAS,SAEApB,QACA,KAAA5G,MAAAC,MAAA,aAEA4G,aACAjX,OAAA8S,KAAA,KAAA2E,oBAAA,cCtGkX,MCQ9W,I,UAAY,gBACd,GACA,GACA,IACA,EACA,KACA,WACA,OAIa,M,qBCKA,IACf/W,KAAA,QACA6T,WAAA,CACA8D,gBACAC,uBACAC,sBACAC,mBACAC,kBAAA,2HACAC,gBACAC,YAAA,2HACAC,eAAA,2HACAC,iBACAC,kBACAC,sBAAA,2HACAC,eACAC,oBAAA,2HACAC,kBAAA,2HACAC,sBAAA,2HACAC,aAAA,6CAEA1H,OACA,OACAd,WAAA,EACAG,WAAA,EACAD,QAAA,GACAG,OAAA,GACAoI,UAAA,GAEAtZ,KAAA,KAAA4R,OAAA/a,MAAArB,SAAAC,YAAAiR,MAAA,QACAmL,QAAA/J,UAAAsL,cACAtB,UAAA,GACAE,IAAA,GACAtR,KAAA,KAGAwR,QAAA,CACAqH,oBAAA9Q,GACA,QAAAsI,QAAA,CACA,MAAAyI,EAAA,CAAA7Y,KAAA,kBAAAuQ,OAAAzI,GACA,yBAAAsI,QACA,KAAAuI,UAAAG,QAAAD,OACA,CACA,MAAAE,EAAA,CAAA/Y,KAAA,KAAAoQ,QAAAG,OAAA,KAAAA,QACA,KAAAoI,UAAAG,QAAAC,GACA,KAAAJ,UAAAG,QAAAD,GACA,KAAAnJ,MAAAC,MAAA,kBAGA,KAAAD,MAAAC,MAAA,4BAAA7H,IAGAkR,eACA,KAAAtJ,MAAAuJ,MAAA,yBACA,MAAArZ,EAAA,KAAAqR,OAAA/a,MAAA0J,QACAsZ,EAAA,2GAAAnT,MAAA,KAEA,IAAAmT,EAAA3gB,SAAAqH,GAAA,YAGA,MAAAuZ,EAAA1Y,QAAA2Y,gBAAA,wBACA,SAAAD,EAAA,YAGA,MAAArR,EAAA,CACAsP,GAAA,MACArM,GAAA,EACAC,GAAA,OACAqO,SAAA,IAEArI,MAAA,MAAAsI,UAAAnC,gBAAArP,GACA,GAAAwR,EAAA,OAAA7Y,QAAA8Y,gBAAA,4BAGA,KAAAtI,OAAAzC,OAAA,6BACA,KAAAkB,MAAAC,MAAA,+BAIA0C,UACA,MAAAC,EAAAlL,UAAAmL,UAAAnL,UAAAoL,aACA,UAAAF,EAAAG,cAAA,KAAA1S,KAAA,QACAuS,EAAAI,WAAA,WAAA3S,KAAA,QACA,KAAAA,KAAAuS,EAAAvM,MAAA,QAEA,KAAA2J,MAAA8J,IAAA,eAAAC,IACA,QAAAxI,OAAA/a,MAAAsO,mBAAA,OACA,MAAAsD,EAAAzT,OAAA4Q,OACA,CACAmS,GAAA,MACArM,GAAA,EACAC,GAAA,OACA0O,GAAA,MACAra,KAAA,KAAAA,MAEA,CAAA6R,QAAA,KAAAA,UAEAyB,EAAA,IAAAC,UAAAC,MAAA,QAAAD,MAAAE,oBAAA,KACAzB,EAAA,GAAAsB,EAAAI,iBAAAJ,EAAAK,WAAA,KAAAL,EAAAM,YACAkE,gBAAArP,GAAAqB,KAAAwQ,IACA,OAAAA,EAAArQ,KAAA,CACA,gBAAAwG,OAAA9P,KAAA,YACA,KAAAmR,UAAAwI,EAAA3I,KACA,QAAAkC,EAAA,EAAAA,EAAA,KAAA/B,UAAA/T,OAAA8V,IAAA,CACA,MAAA0G,EAAA,KAAAzI,UAAA+B,GACA,IAAAtC,EAAA,KACA,MAAAgJ,EAAAC,MAAAzb,aAAAI,QAAA,QAAAa,QAAAua,EAAArQ,QAAA8H,EAAA,CAKA,MAAAuI,EAAAhI,SAAA,CACA,IAAAgI,EAAA/H,aAAA4H,EAAA7Z,SAAA,CACA,KAAAuR,UAAA2I,OAAA5G,EAAA,GACAA,IACA,SACAtC,EAAAgJ,EAAA/H,aAAA4H,EAAA7Z,cAEAgR,EAAAgJ,EAAA/H,aAAA,KAAAC,MAAAC,QAAA,KAAAhS,OAAA6Z,EAAA/H,aAAAG,GAEApB,IACA,KAAAO,UAAA2I,OAAA5G,EAAA,GACAA,UAfA,KAAA/B,UAAA2I,OAAA5G,EAAA,GACAA,IAkBA,KAAA/B,UAAA/T,QACA,KAAAsS,MAAAC,MACA,UACA,eACAtb,OAAA4Q,OAAAwU,EAAA,CAAAtI,UAAA,KAAAA,kBAQA,KAAAzB,MAAA8J,IAAA,WAAAxZ,EAAAuQ,KAGA,GAFA,YAAAvQ,MAAAV,OAAAI,MAAA,8BAEA,KAAA0Q,QAAA,YAAAuI,UAAAhX,KAAA,CAAA3B,OAAAuQ,WAEA,KAAAA,SACA,KAAAH,QAAApQ,EAEA,KAAAkQ,WAAA,EACAtR,WAAA,KACA,KAAAyR,WAAA,GACA,KAEA,KAAAX,MAAA8J,IAAA,WAAAxZ,IACA,KAAAoQ,QAAA,GACA,KAAAC,WAAA,EACA,KAAAE,OAAA,GACA3R,WAAA,KAGA,GAFA,KAAAsR,WAAA,EAEA,KAAAyI,UAAAvb,OAAA,CACA,MAAA2c,EAAA,KAAApB,UAAAqB,QACApb,WAAA,SAAA8Q,MAAAC,MAAA,UAAAoK,EAAA/Z,KAAA+Z,EAAAxJ,QAAA,OAEA,KAGA,KAAAb,MAAA8J,IAAA,sBAAAZ,sBAGA,KAAA3H,OAAA/a,MAAAxB,eAAAmO,eAAA,YAAA2K,YAAA,KAAAwL,iBC/L6V,MCQzV,I,UAAY,gBACd,GACA,GACA,IACA,EACA,KACA,WACA,OAIa,M,qBCHA,IACfnF,WAAA,CAAAoG,YACAjJ,OACA,OACApB,UAAA,EACAC,iBAAA,IAGA0B,QAAA,CACA2I,YAEA,KAAAxa,MAAA,+CAAA8N,kBAEA,KAAAyD,OAAA/a,MAAAsO,oBAAA,qDAEA9D,OAEA,KAAAuQ,OAAA/a,MAAAoO,kBAAA,KAAAsL,SAAA,KAAAC,iBAAA,GACAsK,kBACAhR,KAAAkO,IACA,WAAA/N,EAAA,KAAA0H,GAAAqG,EACA,IAAA+C,EAAA,IAAA9Q,IACA,KAAAsG,UAAA,EACA,KAAAqB,OAAAzC,OAAA,sBAAAwC,GACA,KAAAtB,MAAAC,MAAA,aACA,aAAApX,SAAA,QAAA6hB,EAAA,KAAAtK,cAAA,IAAAsK,OAAA,EAAAA,EAAAC,OAAA,KAAA3K,MAAAC,MAAA,eAAAqB,MAIA,KAAAiG,SAAAC,OACAoD,kBACAnR,KAAAkO,IACA,WAAA/N,EAAA,KAAA0H,GAAAqG,EACA,IAAA/N,IACA,KAAAuG,iBAAA,EACA,KAAAoB,OAAAzC,OAAA,4BAAAwC,EAAAuJ,wBACA,KAAAtJ,OAAAzC,OAAA,wBAAAwC,EAAAwJ,sBACA,KAAAvJ,OAAAzC,OAAA,oCAAAwC,GACA,KAAAtB,MAAAC,MAAA,uBAAAqB,EAAAyJ,oBACAnb,OAAAob,gBAAA1J,EAAA2J,uBACA,KAAA1J,OAAAzC,OAAA,+BAAAwC,EAAAwJ,uBAEA,MAAAlR,GAAA,KAAA2H,OAAAzC,OAAA,qBAEAiJ,QAAA,SAAAR,SAAAS,SAEAkD,kBACA,KAAA3D,SAAAC,OACA2D,gBAAA,CAAAhb,KAAA,KAAAoR,OAAA/a,MAAAwN,UAAAzJ,IACAkP,KAAAkO,IACA,WAAA/N,EAAA,KAAA0H,GAAAqG,EACA,IAAA/N,GAAA0H,EAAA8J,aAAA9J,EAAA8J,YAAA1d,OACAgB,aAAAC,QAAA,SAAA2S,EAAA8J,YAAA,GAAA7a,QAEA6M,QAAAC,MAAA,mBAAAkE,OAAA/a,MAAAwN,UAAAzJ,GAEAqF,OAAA2B,SAAAC,KAAA5B,OAAA2B,SAAAF,SAEA0W,QAAA,SAAAR,SAAAS,SAEAqD,gBACA,IAAAC,QAAA,OACA,MAAAtX,EAAAuX,kBACA,OAAA3b,OAAAlB,aAAAI,QAAA,uBACAkF,EAAA4F,MAAA,mBAAA5F,EAAAxN,OACAoJ,OAAAlB,aAAAC,QAAA,aAAAuU,MAAAsI,UAAAxX,EAAA4F,WACA6R,gBAAAzX,EAAA4F,MAAAH,KAAAkO,IACA,WAAA/N,EAAA,KAAA0H,GAAAqG,EACA,IAAA/N,GAAAhK,OAAAlB,aAAAC,QAAA,eAAA2S,EAAA/Q,gBAIAX,OAAA2B,SAAAC,KAAA,6FAAAiR,mBAAA7S,OAAA2B,SAAAF,sFAGAqa,WACA,MAAAC,EAAA,KAAAvJ,MAAAC,QAAA,GACA,eAAAsJ,UACAlS,KAAAkO,IACAA,GAAArd,OAAAshB,iBAAAD,EAAAhE,GAEA,IAAApD,EAAAhG,SAAAsN,cAAA,SACAtH,EAAAuH,UAAA,KAAAnO,IAAA,iBAIAgF,UACA,MAAAnR,EAAAD,SAAAC,KACAua,EAAAva,EAAA3I,SAAA,QAAA2I,EAAA3I,SAAA,OACA,GAAAkjB,EAAA,YAAAb,kBAEAjT,+BACA,MAAA+T,EAAA9U,QACA,KAAA+U,MAAA1V,MAAA,aACA2V,YAAA,eAAAF,EACA,OAAA7U,QACA6U,IAGA,KAAAzK,OAAA/a,MAAArB,SAAA2K,MAAA,KAAAub,gBACA,KAAAra,OACA,KAAA0a,WAEA,KAAAlB,cCvH6T,MCQzT,I,UAAY,gBACd,GACAhL,GACAe,IACA,EACA,KACA,KACA,OAIa,M,qBCff3T,aAAIiH,IAAIsY,SAER,MAAMC,GAAS,CACb,CACEzB,KAAM,IACNra,KAAM,MACNoT,UAAWA,IAAM,2HAEnB,CACEiH,KAAM,OACNra,KAAM,MACNoT,UAAWA,IAAM,2HAEnB,CACEiH,KAAM,MACNra,KAAM,QACNoT,UAAWA,IAAM,4CAEnB,CACEiH,KAAM,MACNra,KAAM,YACNoT,UAAWA,IAAM,4CAEnB,CACEiH,KAAM,MACNra,KAAM,WACNoT,UAAWA,IAAM,4CAEnB,CACEiH,KAAM,OACNra,KAAM,WACNoT,UAAWA,IAAM,4CAEnB,CACEiH,KAAM,MACNra,KAAM,WACNoT,UAAWA,IAAM,4CAEnB,CACEiH,KAAM,MACNra,KAAM,SACNoT,UAAWA,IAAM,4CAEnB,CACEiH,KAAM,QACNra,KAAM,eACNoT,UAAWA,IAAM,4CAEnB,CACEiH,KAAM,aACNra,KAAM,oBACNoT,UAAWA,IAAM,4CAEnB,CACEiH,KAAM,WACNra,KAAM,kBACNoT,UAAWA,IAAM,4CAOnB,CACEiH,KAAM,eACNra,KAAM,eACNoT,UAAWA,IAAM,4CAEnB,CACEiH,KAAM,oBACNra,KAAM,oBACNoT,UAAWA,IAAM,4CAEnB,CACEiH,KAAM,kBACNra,KAAM,kBACNoT,UAAWA,IAAM,4CAEnB,CACEiH,KAAM,SACNra,KAAM,YACNoT,UAAWA,IAAM,4CAEnB,CACEiH,KAAM,IACN0B,SAAU,MAIdtW,cAAM+I,OAAO,wBACb/I,cAAM+I,OAAO,kCAET/I,cAAMvP,MAAMsO,oBACdsX,GAAOhC,OAAO,EAAG,EAAG,CAClBO,KAAM,IACNra,KAAM,MACNoT,UAAWA,IAAM,8HAGrB,MAAM4I,GAAS,IAAIH,QAAU,CAC3BhC,KAAM,UACN7C,KAAM1X,OAAO2c,cAAgB,IAC7BH,YAGaE,U,aC7GX9M,GAAS,WAAkB,IAAIC,EAAIJ,KAAKK,EAAGD,EAAIE,MAAMD,GAAG,OAAOA,EAAG,aAAa,CAACG,MAAM,CAAC,KAAO,YAAY,CAAEJ,EAAI+M,WAAW,EAAG9M,EAAG,MAAM,CAACE,MAAM,CAAC,kBAAmBH,EAAI3B,UAAW,CAAC,GAAM2B,EAAI3B,UAAUjV,SAAS,SAAS,CAAC6W,EAAG,MAAM,CAACe,YAAY,oBAAoBhB,EAAIa,QAEvQC,GAAkB,GCOP,IACfjQ,KAAA,UACAgR,KAAA,WACA,OACAkL,WAAA,IAGA3K,QAAA,CACA4K,cACA,KAAAD,cAEAE,cACA,KAAAF,WAAA,KAAApP,QAAAC,MAAA,kBACA,KAAAmP,WAAAnG,KAAAsG,IAAA,SAAAH,eCtB8W,MCQ1W,I,UAAY,gBACd,GACA,GACA,IACA,EACA,KACA,WACA,OAIa,M,QCbf,MAAMI,GAAU,CACdC,QAASjgB,EAAKkgB,GACZ,MAAMC,EAAU,GACVC,EAAS,WACb,IAAIC,EAAM,KACV,OAAO,WACL,GAAIA,EAAK,OAAOA,EACX,CAEH,MAAMC,EAAMC,GACNC,EAAcxgB,EAAIygB,OAAOH,GAK/B,OAJAD,EAAM,IAAIG,EAAY,CACpBE,GAAI/O,SAASgP,cAAc,SAE7BhP,SAASiP,KAAKC,YAAYR,EAAIS,KACvBT,IAZE,GAgBTU,EAAKX,IACXD,EAAQvF,KAAO,WACbmG,EAAGlB,eAELM,EAAQ/E,KAAO,WACb2F,EAAGjB,eAEL9f,EAAI8Q,UAAU6J,SAAWwF,EACrBD,GAAS1P,QAAQwQ,IAAId,KAIdF,UCrCXpN,GAAS,WAAkB,IAAIC,EAAIJ,KAAKK,EAAGD,EAAIE,MAAMD,GAAG,OAAOA,EAAG,aAAa,CAACG,MAAM,CAAC,KAAO,YAAY,CAAEJ,EAAIoO,QAASnO,EAAG,UAAU,CAACe,YAAY,eAAe,CAAChB,EAAIsE,GAAGtE,EAAIuE,GAAGvE,EAAIoO,YAAYpO,EAAIa,QAErMC,GAAkB,GCKP,IACfjQ,KAAA,UACAgR,OACA,OACAuM,QAAA,KAGAhM,QAAA,CACAa,KAAAoL,EAAAC,EAAA3H,GACA,KAAAyH,QAAAC,EACA5e,WAAA,KACA,KAAA2e,QAAA,GACA3e,WAAA,KAAAkX,KAAA,MACA2H,MCpB4W,MCQxW,I,UAAY,gBACd,GACA,GACA,IACA,EACA,KACA,WACA,OAIa,M,QChBf,MAAMC,GAAiB,SAAUd,GAC/B,IAAID,EAAM,KACV,OAAO,WACL,GAAIA,EAAK,OAAOA,EACX,CACH,MAAMG,EAAcxgB,aAAIygB,OAAOH,GAK/B,OAJAD,EAAM,IAAIG,EAAY,CACpBE,GAAI/O,SAASgP,cAAc,SAE7BhP,SAASiP,KAAKC,YAAYR,EAAIS,KACvBT,KAKPgB,GAAa,CACjBpB,QAASjgB,EAAKkgB,GACZ,MAAMa,EAAKK,GAAeH,GAAfG,GACLE,EAAM,GACNC,EAAQ,GACd,IAAIC,GAAY,EAEhB,MAAMC,EAASA,CAACP,EAAKC,KACnBK,GAAY,EACZT,EAAGjL,KAAKoL,EAAKC,EAAUG,EAAII,KAAKC,KAAKL,KAEjCM,EAAahX,GAAU2W,EAAMM,KAAKhiB,GAAQA,EAAKqhB,MAAQtW,IAAWmW,EAAGE,UAAYrW,EAEvF,CAAC,MAAO,WAAW7J,QAAQ0B,IACzB6e,EAAI7e,GAAO,SAAUye,EAAKC,EAAW,KACnC,GAAIS,EAAWV,GAAM,OAAO,KACxBK,EAAMzgB,QAAU0gB,EAAWD,EAAMlc,KAAK,CAAE6b,MAAKC,aAC5CM,EAAOP,EAAKC,MAIrBG,EAAII,KAAO,WAET,GADAF,GAAY,EACRD,EAAMzgB,OAAQ,CAChB,MAAMghB,EAAIP,EAAM7D,QAChB+D,EAAOK,EAAEZ,IAAKY,EAAEX,YAIpBnhB,EAAI8Q,UAAUkK,OAASsG,EACvBte,OAAOgY,OAASsG,IAILD,UCpDXzO,GAAS,WAAkB,IAAIC,EAAIJ,KAAKK,EAAGD,EAAIE,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACe,YAAY,gBAAgB,CAACf,EAAG,aAAa,CAACG,MAAM,CAAC,KAAO,OAAO,CAACH,EAAG,MAAM,CAACoB,WAAW,CAAC,CAACxQ,KAAK,OAAOyQ,QAAQ,SAASxO,MAAOkN,EAAIkP,OAAQ1N,WAAW,WAAWR,YAAY,eAAef,EAAG,aAAa,CAACG,MAAM,CAAC,KAAO,UAAU,CAAEJ,EAAIoO,QAASnO,EAAG,MAAM,CAACe,YAAY,6BAA6B,CAACf,EAAG,MAAM,CAACe,YAAY,sBAAsB,CAACf,EAAG,KAAK,CAACD,EAAIsE,GAAGtE,EAAIuE,GAAGvE,EAAInV,KAAKC,EAAE,iBAAiBmV,EAAG,MAAM,CAACe,YAAY,qBAAqBX,GAAG,CAAC,MAAQL,EAAImP,aAAalP,EAAG,MAAM,CAACe,YAAY,wBAAwB,CAAChB,EAAIsE,GAAGtE,EAAIuE,GAAGvE,EAAIoO,YAAYnO,EAAG,MAAM,CAACe,YAAY,uBAAuB,CAACf,EAAG,MAAM,CAACe,YAAY,mBAAmBX,GAAG,CAAC,MAAQL,EAAImP,UAAU,CAACnP,EAAIsE,GAAGtE,EAAIuE,GAAGvE,EAAInV,KAAKC,EAAE,sBAAsBkV,EAAIa,QAAQ,IAEnxBC,GAAkB,GCoBP,IACfjQ,KAAA,UACAgR,OACA,OACAuM,QAAA,GACAc,QAAA,EACAvI,GAAA,GACA9b,cAGAuX,QAAA,CACAa,KAAAoL,EAAAC,EAAA3H,GACA,KAAAuI,QAAA,EACA,KAAAE,UAAA,KACA,KAAAhB,QAAAC,EACA,KAAA1H,QAGAwI,UACA,KAAAf,QAAA,GACA,KAAAc,QAAA,KC1C6W,MCQzW,I,UAAY,gBACd,GACA,GACA,IACA,EACA,KACA,WACA,OAIa,M,QChBf,MAAMX,GAAiB,SAAUd,GAC/B,IAAID,EAAM,KACV,OAAO,WACL,GAAIA,EAAK,OAAOA,EACX,CACH,MAAMG,EAAcxgB,aAAIygB,OAAOH,GAK/B,OAJAD,EAAM,IAAIG,EAAY,CACpBE,GAAI/O,SAASgP,cAAc,SAE7BhP,SAASiP,KAAKC,YAAYR,EAAIS,KACvBT,KAKPgB,GAAa,CACjBpB,QAASjgB,EAAKkgB,GACZ,MAAMa,EAAKK,GAAeH,GAAfG,GACLE,EAAM,GACNC,EAAQ,GACd,IAAIC,GAAY,EAEhB,MAAMC,EAASA,CAACP,EAAKC,KACnBK,GAAY,EACZT,EAAGjL,KAAKoL,EAAKC,EAAUG,EAAII,KAAKC,KAAKL,KAEjCM,EAAahX,GAAU2W,EAAMM,KAAKhiB,GAAQA,EAAKqhB,MAAQtW,IAAWmW,EAAGE,UAAYrW,EAEvF0W,EAAI1G,KAAO,SAAUsG,EAAKC,EAAW,MACnC,GAAIS,EAAWV,GAAM,OAAO,KACxBK,EAAMzgB,QAAU0gB,EAAWD,EAAMlc,KAAK,CAAE6b,MAAKC,aAC5CM,EAAOP,EAAKC,IAGnBG,EAAII,KAAO,WAET,GADAF,GAAY,EACRD,EAAMzgB,OAAQ,CAChB,MAAMghB,EAAIP,EAAM7D,QAChB+D,EAAOK,EAAEZ,IAAKY,EAAEX,YAIpBne,OAAOkf,MAAQliB,EAAI8Q,UAAUoR,MAAQZ,IAI1BD,U,0BChCfre,OAAO4E,SAAWua,QAAOC,SACzBC,kBACAriB,aAAI1H,OAAOgqB,eAAgB,EAC3BtiB,aAAIiH,IAAIsb,QAAS,CACfjqB,OAAQ,CAAE2U,GAAIjK,OAAOiO,UAAU,UAGjCjR,aAAIiH,IAAIub,SACRxiB,aAAIiH,IAAIkZ,IACRngB,aAAIiH,IAAIwb,IACRziB,aAAIiH,IAAIyb,IACRlS,QAAQwQ,IAAI,OAAQ,cAEpB,IAAIhhB,aAAI,CACN0f,UACAvW,oBACAzL,YACAkV,OAAS+P,GAAMA,EAAEC,MAChBC,OAAO,S,uBCnCVjrB,EAAOC,QAAU,IAA0B,sD,uBCA3CD,EAAOC,QAAU,IAA0B,yC,gDCA3CD,EAAOC,QAAU,IAA0B,yC,oCCA3C,uBAGA,MAAMC,EAAWC,OAAOC,OAAOC,QAC/BH,EAASI,UAAY,4BACrB,MAAMC,EAAcJ,OAAOC,OAAOI,QAElCD,EAAYE,iBAAkB,EAE9B,MAAMC,EAAS,CACbC,SAAU,CACRC,YAAa,YACbC,SAAU,OACVC,OAAQ,QACRC,aAAc,GACdC,aAAc,GACdC,YAAa,GACbC,SAAU,qBACVC,mBAAoB,iDACpBC,uBAAwB,yBAE1BC,UAAW,GACXC,QAASpB,EACTqB,OAAQ,CACNC,SAAUE,EAAQ,QAClBD,YAAaC,EAAQ,SAEvBC,IAAK,CACHC,IAAK,eACLkJ,MAAO,uBACPjJ,UAAW,kHAEbC,OAAQvB,GAGKG,gB,sFCnCf,W,2DCAA,W,uBCAAV,EAAOC,QAAU,IAA0B,yC,8GCE5B,GACbS,OAAQ,CAGNod,GAAIoN,I,YCDR9iB,aAAIiH,IAAI8b,QAER,MAAMtf,EAAO0F,aAAMvP,MAAMwN,UAAU4b,GAAKlY,UAAUmL,UAAYnL,UAAUoL,aACxE,IAAI+M,EAEkEA,EAA3C,UAAvBxf,EAAK0S,eAAoD,UAAvB1S,EAAK0S,cAAyC,QAC3E1S,EAAK2S,WAAW,MAAqB,QAC3B3S,EAAKgG,MAAM,KAAK,GAEnC,MAAMyZ,EAAY,CAAC,QAAS,KAAM,QAAS,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,MACtIA,EAAUjnB,SAASgnB,KAAcA,EAAc,MAEpD,MAAMvlB,EAAO,IAAIqlB,OAAQ,CACvBtN,OAAQwN,EACRE,eAAgB,KAChBC,SAAUC,EAAS/qB,SAGNoF,SAER,MAAM4lB,EAAU,CACrB5N,GAAI,UAGJ6N,GAAI,MAMJC,GAAI,MAGJC,GAAI,WACJC,GAAI,UAEJC,GAAI,Y,qBCzCN/rB,EAAOC,QAAU,04E,kCCAjB,W,sFCAA,oEACA,MAAMI,EAAU,CAEdiW,gBAAiB,qBACjBE,mBAAoB,6BAEpBJ,iBAAkB,6BAElB9V,UAAW,mCACX0rB,eAAgB,yBAChB3V,UAAW,aACXF,uBAAwB,iCACxB8V,wBAAyB,0BACzBC,0BAA2B,6BAE7B,IAAK,MAAOrhB,EAAKkD,KAAU5N,OAAOgsB,QAAQ9rB,GACxCA,EAAQwK,GAAOkD,EAAMwJ,QAAQ,YAAanM,OAAOC,YAGnD,MAAM7K,EAAiB,CACrBiO,iBAAiB,EACjBC,kBAAkB,EAClBC,eAAe,EACfC,MAAM,EACNC,kBAAmB,GACnBC,4BAA4B,EAC5BC,qBAAqB,EACrBC,oBAAoB,EACpBgI,cAAc,EACdvW,iBAAiB,I,kCC7BnB,kCAAO,MAAM8L,EAAgB,WAC3B,IAAI6f,EACJ,MAAM7f,EAAe,GAEf8f,EAAWxhB,GAAOuhB,EAAe,GAAGA,KAAgBvhB,IAAQA,EAuBlE,OArBA0B,EAAaC,KAAO,SAAU3B,GAC5BuhB,EAAevhB,GAGjB0B,EAAa8Y,gBAAkB,SAAUxa,EAAKkD,GAC5C6K,QAAQwQ,IAAIiD,EAASxhB,GAAMkD,GAC3B7D,aAAaC,QAAQkiB,EAASxhB,GAAMkD,IAGtCxB,EAAa+f,kBAAoB,SAAUzhB,EAAKkD,GAC9Cwe,eAAepiB,QAAQkiB,EAASxhB,GAAMkD,IAGxCxB,EAAa2Y,gBAAkB,SAAUra,GACvC,OAAOX,aAAaI,QAAQ+hB,EAASxhB,KAGvC0B,EAAaigB,kBAAoB,SAAU3hB,GACzC,OAAO0hB,eAAejiB,QAAQ+hB,EAASxhB,KAGlC0B,EA3BoB,I,oCCA7B,W,sFCAA,uBAGA,MAAMrM,EAAWC,OAAOC,OAAOC,QAC/BH,EAASI,UAAY,6BACrBJ,EAASmW,UAAY,iBACrB,MAAM9V,EAAcJ,OAAOC,OAAOI,QAElCD,EAAYE,iBAAkB,EAE9B,MAAMC,EAAS,CACbC,SAAU,CACRC,YAAa,YACbC,SAAU,MACVC,OAAQ,OACRC,aAAc,GACdC,aAAc,GACdC,YAAa,GACbC,SAAU,gBACVC,mBAAoB,gDACpBC,uBAAwB,4BAE1BC,UAAW,GACXC,QAASpB,EACTqB,OAAQ,CACNC,SAAUE,EAAQ,QAClBD,YAAaC,EAAQ,SAEvBC,IAAK,CACHC,IAAK,eACLkJ,MAAO,uBACPjJ,UAAW,kHAEbC,OAAQvB,GAGKG,gB,uBCpCfV,EAAOC,QAAU,IAA0B,qE,qBCA3CD,EAAOC,QAAU,8oF,qBCAjBD,EAAOC,QAAU,IAA0B,oE,qBCA3CD,EAAOC,QAAU,IAA0B,yC,qBCA3CD,EAAOC,QAAU,IAA0B,mD,kCCA3C,W,qBCAAD,EAAOC,QAAU,IAA0B,0C,kCCA3C,0tBAEO,MAAMgjB,EAAWrP,GAAU6Y,eAAI,UAAW7Y,GAEpCqS,EAAkBrS,GAAU6Y,eAAI,uBAAwB7Y,GACxD+S,EAAqB/S,GAAU8Y,eAAK,uBAAwB9Y,GAC5D+Y,EAAe/Y,GAAU8Y,eAAK,oBAAqB9Y,GAGnDgZ,EAAsBhZ,GAAU8Y,eAAK,mBAAoB9Y,GACzDiZ,EAA0BjZ,GAAU8Y,eAAK,kBAAmB9Y,GAE5DkZ,EAAalZ,GAAU6Y,eAAI,UAAW7Y,GACtCmZ,EAAenZ,GAAU6Y,eAAI,YAAa7Y,GAC1CoZ,EAAmBpZ,GAAU6Y,eAAI,gBAAiB7Y,GAElDwS,EAAgBxS,GAAU8Y,eAAK,qBAAsB9Y,GACrDqZ,EAAWrZ,GAAU8Y,eAAK,2BAA4B9Y,GACtDsZ,EAAYtZ,GAAU8Y,eAAK,4BAA6B9Y,GAExDuZ,EAAqBvZ,GAAU8Y,eAAK,qCAAsC9Y,GAE1EqT,EAAcmG,GAAUX,eAAI,UAAW,CAAEvJ,GAAI,MAAOrM,GAAI,GAAIC,GAAI,KAAM0O,GAAI,MAAO6H,QAASD,IAEjGE,EAAmB,CACvBC,YAAa,kBACbC,YAAa,kBACbC,WAAY,qBACZC,eAAgB,+BAChBC,YAAa,eACbC,gBAAiB,0BACjBC,kBAAmB,wBAEfC,EAAY3tB,OAAOC,OAAOktB,GAEhC,OAAQliB,OAAOC,YACb,IAAK,QACHiiB,EAAiBC,YAAc,qBAC/BD,EAAiBE,YAAc,qBAC/B,MAEF,IAAK,MAAO,IAAK,OACfF,EAAiBC,YAAc,6BAC/BD,EAAiBE,YAAc,wBAC/BF,EAAiBG,WAAa,gCAC9BH,EAAiBI,eAAiB,sCAClCJ,EAAiBK,YAAc,mBAC/BL,EAAiBO,kBAAoB,2BACrC,MAEF,IAAK,aAAc,IAAK,OAAQ,IAAK,OAAQ,IAAK,OAAQ,IAAK,OAAQ,IAAK,QAAS,IAAK,QAAS,IAAK,OAAQ,IAAK,QAAS,IAAK,OAAQ,IAAK,QAC9IP,EAAiBM,gBAAkB,iBAIhC,MAAMG,EAAena,GAAU8Y,eAAKoB,EAAUP,YAAa3Z,GACrDoa,EAAyBpa,GAAU8Y,eAAKoB,EAAUF,gBAAiBha,GACnEqa,EAAsBra,GAAU8Y,eAAKoB,EAAUN,YAAa5Z,GAC5Dsa,EAAkBta,GAAU8Y,eAAKoB,EAAUL,WAAY7Z,GACvDua,EAAiBva,GAAU8Y,eAAKoB,EAAUJ,eAAgB9Z,GAC1Dwa,EAAuBxa,GAAU8Y,eAAKoB,EAAUH,YAAa/Z,GAC7Dya,EAAuBza,GAAU6Y,eAAIqB,EAAUD,kBAAmBja,I,kCC7D/E,yLAIA,SAASd,EAAoBc,GAC3B,MAAM0a,EAAcvhB,SAASD,SAASzI,SAAS,UACzCrC,EAAQuP,aAAMvP,MACd+B,EAAWuqB,EAActsB,EAAMmP,UAAUpN,SAAW/B,EAAM+B,SAC1DpD,EAAWqB,EAAMrB,SAavB,GAXKiT,IAAQA,EAAS,IACtBA,EAAO2a,SAAW5tB,EAASG,OAC3B8S,EAAO4a,aAAe7tB,EAASC,YAC/BgT,EAAO6a,OAAS,MAEZzsB,EAAM0J,UAASkI,EAAOlI,QAAU1J,EAAM0J,SACtC1J,EAAMwE,WAAUoN,EAAOpN,SAAWxE,EAAMwE,WAEvCoN,EAAO7H,QAAUhI,EAASgI,SAAQ6H,EAAO7H,OAAShI,EAASgI,SAC3D6H,EAAOxH,KAAOrI,EAASqI,MAAKwH,EAAOxH,IAAMrI,EAASqI,MAElDkiB,EAAa,CAChB,MAAMI,EAAcxkB,aAAaI,QAAQ,WAAa,IACjDsJ,EAAO7H,QAAU2iB,IACpB9a,EAAO7H,OAAS2iB,GAKpB,OADI1sB,EAAMkO,SAAWlO,EAAMoO,mBAAiBwD,EAAO6a,OAASld,aAAMvP,MAAMwN,UAAUqD,GAC3Ee,EAET,SAAS+a,EAAcC,EAAM,IAC3B,MAAMtjB,EAAOiG,aAAMvP,MAAMrB,SAAS2K,KAC5BujB,EAAOvjB,EAAUiG,aAAMvP,MAAMrB,SAASE,SAAxB,KAAuC0Q,aAAMvP,MAAMrB,SAASE,SAEhF,OAAI+tB,EAAIpQ,WAAW,cAAsBoQ,EAAIrX,QAAQ,aAActE,gCAC/D2b,EAAIpQ,WAAW,QAAgBoQ,EAAIrX,QAAQ,OAAStE,i8BAAY,sBAAwB4b,IAAS5b,gCACjG2b,EAAIpQ,WAAW,YAAoBoQ,EAAIrX,QAAQ,WAAatE,i8BAAY,0BAA4B4b,IAAS5b,uDAC7G2b,EAAIpQ,WAAW,QAAgBoQ,EAAIrX,QAAQ,OAAQtE,i8BAAY,sBAAwB4b,IACvFD,EAAIpQ,WAAW,UAAkBoQ,EAAIrX,QAAQ,SAAUtE,i8BAAY,wBAA0B4b,IAC1FD,EAGT,MAAME,EAAUC,IAAM3uB,OAAO,CAAEmK,QAAS,MA2DxCykB,eAAeC,EAAmBlb,EAAQ6a,EAAKtnB,GAC7C,MAAM4nB,EAAYN,EAAIpQ,WAAW,YAAcoQ,EAAIpQ,WAAW,aAExD2Q,EAAgB,CAAEpb,SAAQ6a,OAIhC,MAHe,QAAX7a,IAAkBob,EAAcvb,OAAStM,GAC9B,SAAXyM,IAAmBob,EAAcrS,KAAOxV,GAExC4nB,GACFC,EAAcP,IAASA,EAAH,SACb,IAAI7Z,QAAQ,CAACC,EAASoa,KAC3BxW,QAAQwQ,IAAI,SAAS+F,EAAcP,KACnCE,EAAQK,GACLla,KAAKkO,GAAOnO,EAAQmO,IACpBG,MAAM,IAAM,IAAIvO,QAAQ,CAACC,EAASoa,IAAW1kB,WAAW0kB,EAAQ,OAEhE9L,MAAM,KACL1K,QAAQwQ,IAAI,SAASwF,GACdE,EAAQK,KAEhBla,KAAKkO,GAAOnO,EAAQmO,IAEpBG,MAAM,KACL1K,QAAQwQ,IAAI,SAASwF,GAEdE,EAAQK,KAEhBla,KAAKkO,GAAOnO,EAAQmO,IACpBG,MAAMD,GAAO+L,EAAO/L,OAIpByL,EAAQK,GAzFjBL,EAAQO,aAAaC,QAAQjgB,IAC3B3O,IACwB,SAAlBA,EAAOqT,OAAmBrT,EAAOoc,KAAOhK,EAAmBpS,EAAOoc,MACjEpc,EAAOkT,OAASd,EAAmBpS,EAAOkT,QAI/ClT,EAAOkuB,IAAMD,EAAajuB,EAAOkuB,KAGjC,MAAMW,EAAehD,eAAejiB,QAAQ,iBAAmBiH,aAAMvP,MAAMwN,UAAU+f,aACrF,GAAIA,EAAc,CAChB,MAAMC,EAAU9uB,EAAOkuB,IAAIvqB,SAAS,KAAO,IAAM,IACjD3D,EAAOkuB,KAAO,GAAGY,YAAkBD,IAGrC,MAAME,EAAkBlD,eAAejiB,QAAQ,oBAAsBiH,aAAMvP,MAAMwN,UAAUigB,gBAC3F,GAAIA,EAAiB,CACnB,MAAM/P,EAAShf,EAAOkuB,IAAIvqB,SAAS,KAAO,IAAM,IAChD3D,EAAOkuB,KAAQlP,EAAS,iBAAiB+P,EAG3C,GAAI/uB,EAAOkuB,IAAIvqB,SAAS,uBAAwB,CAC9C,MAAM,IAAE+H,EAAG,OAAEL,GAAWrL,EAAOoc,KAC3B/Q,GAAUK,UAAY1L,EAAOoc,KAAK/Q,OAGxC,OAAOrL,GAETmY,IACEzN,OAAOgY,OAAOC,IAAIvd,OAAKC,EAAE,gBAClBgP,QAAQqa,OAAOvW,KAG1BiW,EAAQO,aAAa5J,SAASpW,IAC5BoW,GAcSA,EAAS3I,KAElBjE,IACE,MAAM6W,IAAkB7W,EAAMnY,OAAOkuB,KAAO,IAAIvqB,SAAS,SAEzD,OADIqrB,GAAetkB,OAAOgY,OAAOC,IAAIvd,OAAKC,EAAE,gBACrCgP,QAAQqa,OAAOvW,KAsC1B,MAAM4T,EAAMuC,MAAOJ,EAAKhb,IAAWqb,EAAkB,MAAOL,EAAKhb,GAC3D8Y,EAAOsC,MAAOJ,EAAK9R,IAASmS,EAAkB,OAAQL,EAAK9R,I,yDCzIjE,uBAGA,MAAM5c,EAAWC,OAAOC,OAAOC,QAC/BH,EAASmW,UAAY,gBACrBnW,EAASI,UAAY,4BAErB,MAAMC,EAAcJ,OAAOC,OAAOI,QAElCD,EAAYE,iBAAkB,EAE9B,MAAMC,EAAS,CACbC,SAAU,CACRC,YAAa,cACbC,SAAU,KACVC,OAAQ,SACRC,aAAc,GACdC,aAAc,GACdC,YAAa,GACbC,SAAU,kBACVC,mBAAoB,uDACpBC,uBAAwB,4BAE1BC,UAAW,GACXC,QAASpB,EACTqB,OAAQ,CACNC,SAAU,4HACVC,YAAaC,EAAQ,SAEvBC,IAAK,CACHC,IAAK,eACLkJ,MAAO,uBACPjJ,UAAW,oHAEbC,OAAQvB,GAGKG,gB,qBCrCfV,EAAOC,QAAU,IAA0B,iD,qBCA3CD,EAAOC,QAAU,IAA0B,2C,qBCA3CD,EAAOC,QAAU,IAA0B,yC,yDCA3C,W,mBCAAD,EAAOC,QAAU,04E,yDCAjB,IAAI+a,EAAS,WAAkB,IAAIC,EAAIJ,KAAKK,EAAGD,EAAIE,MAAMD,GAAG,OAAOA,EAAG,UAAU,CAACe,YAAY,iBAAiB,CAAChB,EAAI0U,GAAG,YAAY,IAE9H5T,EAAkB,GCKP,GACfjQ,KAAA,eCRmW,I,wBCQ/VoT,EAAY,eACd,EACAlE,EACAe,GACA,EACA,KACA,WACA,MAIa,OAAAmD,E,4BCnBflf,EAAOC,QAAU,0iF,qBCAjBD,EAAOC,QAAU,IAA0B,yC,grrDCA3C,uBAGA,MAAMC,EAAWC,OAAOC,OAAOC,QAC/BH,EAASI,UAAY,4BACrBJ,EAASmW,UAAY,gBACrBnW,EAASgsB,0BAA4B,+BACrC,MAAM3rB,EAAcJ,OAAOC,OAAOI,QAElCD,EAAYE,iBAAkB,EAE9B,MAAMC,EAAS,CACbC,SAAU,CACRC,YAAa,YACbC,SAAU,KACVC,OAAQ,QACRC,aAAc,GACdC,aAAc,GACdC,YAAa,GACbC,SAAU,kBACVC,mBAAoB,gDACpBC,uBAAwB,+BAE1BC,UAAW,GACXC,QAASpB,EACTqB,OAAQ,CACNC,SAAU,qJACVC,YAAaC,EAAQ,SAEvBC,IAAK,CACHC,IAAK,eACLkJ,MAAO,uBACPjJ,UAAW,kHAEbC,OAAQvB,GAGKG,gB,kCCrCf,uBAGA,MAAMR,EAAWC,OAAOC,OAAOC,QAC/BH,EAASI,UAAY,6BACrB,MAAMC,EAAcJ,OAAOC,OAAOI,QAElCD,EAAYE,iBAAkB,EAE9B,MAAMC,EAAS,CACbC,SAAU,CACRC,YAAa,aACbC,SAAU,MACVC,OAAQ,OACRC,aAAc,GACdC,aAAc,GACdC,YAAa,GACbC,SAAU,iBACVC,mBAAoB,iDACpBC,uBAAwB,gCAE1BC,UAAW,GACXC,QAASpB,EACTqB,OAAQ,CACNC,SAAU,gIACVC,YAAaC,EAAQ,SAEvBC,IAAK,CACHC,IAAK,eACLkJ,MAAO,uBACPjJ,UAAW,kHAEbC,OAAQvB,GAGKG,gB,kCCnCf,sGAAO,MAAMuM,EAAqB,qBACrB2iB,EAAoB,oBACpBC,EAAmB,mBACC,iBAAiBC,e,kCCHlD,W,qBCAA9vB,EAAOC,QAAU,IAA0B,qE,4CCA3C,IAAI0U,EAAM,CACT,YAAa,OACb,aAAc,OACd,YAAa,OACb,YAAa,OACb,aAAc,OACd,YAAa,QAId,SAASob,EAAelb,GACvB,IAAIQ,EAAK2a,EAAsBnb,GAC/B,OAAOC,EAAoBO,GAE5B,SAAS2a,EAAsBnb,GAC9B,IAAIC,EAAoBL,EAAEE,EAAKE,GAAM,CACpC,IAAIK,EAAI,IAAIC,MAAM,uBAAyBN,EAAM,KAEjD,MADAK,EAAEE,KAAO,mBACHF,EAEP,OAAOP,EAAIE,GAEZkb,EAAeza,KAAO,WACrB,OAAOnV,OAAOmV,KAAKX,IAEpBob,EAAe/a,QAAUgb,EACzBhwB,EAAOC,QAAU8vB,EACjBA,EAAe1a,GAAK,Q,kCC3BpB,W,qBCAArV,EAAOC,QAAU,IAA0B,8C,yDCA3C,W,kCCAA,IAAI+a,EAAS,WAAkB,IAAIC,EAAIJ,KAAKK,EAAGD,EAAIE,MAAMD,GAAG,OAAOA,EAAG,UAAU,CAACe,YAAY,iCAAiCb,MAAM,CAACH,EAAIgV,KAAMhV,EAAI3B,YAAY,CAAG2B,EAAIiV,WAAqMjV,EAAIa,KAA7LZ,EAAG,KAAK,CAACe,YAAY,SAAS,CAAChB,EAAIsE,GAAGtE,EAAIuE,GAAGvE,EAAIoB,OAAO0D,OAAS9E,EAAI8E,OAAO,KAAO9E,EAAIkV,UAAuFlV,EAAIa,KAAhFZ,EAAG,IAAI,CAACI,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAON,EAAIO,MAAMC,MAAM,kBAAsCR,EAAImV,cAAuEnV,EAAI0U,GAAG,WAA/DzU,EAAG,MAAM,CAACe,YAAY,WAAW,CAAChB,EAAI0U,GAAG,YAAY,GAAuB1U,EAAIoV,WAAmPpV,EAAIa,KAA3OZ,EAAG,MAAM,CAACe,YAAY,kBAAkB,CAAChB,EAAI0U,GAAG,aAAY,WAAW,MAAO,CAACzU,EAAG,MAAM,CAACe,YAAY,wBAAwBX,GAAG,CAAC,MAAQL,EAAIqV,eAAe,CAACrV,EAAIsE,GAAGtE,EAAIuE,GAAGvE,EAAIoB,OAAO2D,eAAiB/E,EAAIkE,GAAG,uBAAsB,IAAa,IAEltBpD,EAAkB,GCcP,GACfjQ,KAAA,YACA+Q,MAAA,CACAkD,MAAAwQ,OACAlU,OAAA,CACAxW,KAAA1F,OACAqX,aAAA,KAEA2Y,UAAAK,QACAN,WAAAM,QACAH,WAAAG,QACAJ,cAAAI,QACAP,KAAAM,OACAE,YAAAF,QAEAlT,QAAA,CACAiT,eACA,KAAAjU,OAAA4D,cAAA,KAAA5D,OAAA4D,eACA,KAAAzE,MAAAC,MAAA,eClCiW,I,wBCQ7VyD,EAAY,eACd,EACAlE,EACAe,GACA,EACA,KACA,WACA,MAIa,OAAAmD,E,8BCnBflf,EAAOC,QAAU,IAA0B,0C,qBCA3CD,EAAOC,QAAU,IAA0B,gD,qBCA3CD,EAAOC,QAAU,IAA0B,0C,itBCCpC,MAAM8mB,EAAa2J,IACxBA,EAASA,GAAUtlB,OAAO2B,SAAS2jB,OAAOC,MAAM,GAChD,MAAMC,EAAW,GACXC,EAAYH,EAAO7e,MAAM,KAC/B,IAAK,MAAM9D,KAAS8iB,EAAU3oB,SAAU,CACtC,MAAMgV,EAAQnP,EAAM0R,QAAQ,KACtBH,EAAIvR,EAAM4iB,MAAM,EAAGzT,GACzB0T,EAAStR,GAAKwR,mBAAmB/iB,EAAM4iB,MAAMzT,EAAQ,IAEvD,OAAO0T,GAGInhB,EAAgB,WAC3B,IAAImhB,EAAW,GACf,OAAO,WAEL,GAAI7jB,SAASC,KAAK3I,SAAS,UAAW,MAAO,GAC7C,GAAiC,OAA7BoD,KAAKC,UAAUkpB,GAAoB,CACrC,MAAMG,EAAUhK,IACViK,EAAcvpB,KAAKqJ,MAAM5G,aAAaI,QAAQ,cAAgB,MAC9D2mB,EAAgBxpB,KAAKqJ,MAAMyb,eAAejiB,QAAQ,cAAgB,MAGvC,UAA7B0mB,EAAYze,cAA6BnH,OAAOC,YAAcD,OAAOC,WAAWmT,WAAW,QAAQuS,EAAQxe,aAAe,SAEpG,MAAtBxF,SAASD,UAAqB1B,OAAO2c,cAAgB3c,OAAO2c,aAAavJ,WAAWzR,SAASD,UAE/F5C,aAAaC,QAAQ,YAAa1C,KAAKC,UAAUvH,OAAO4Q,OAAO,GAAIigB,EAAaD,KAEhFxE,eAAepiB,QAAQ,YAAa1C,KAAKC,UAAUvH,OAAO4Q,OAAO,GAAIkgB,EAAeF,KAGtFH,EAAWzwB,OAAO4Q,OAAO,GAAIigB,EAAaC,EAAeF,GACrD,WAAYA,GAAS7mB,aAAaC,QAAQ,SAAU4mB,EAAQhlB,QAC1C,SAAlBglB,EAAQhf,OAAkBmf,IAEhC,OAAON,GAxBkB,GA4BhB/gB,EAAWhF,GAAO,CAAC,OAAOxG,SAASwG,GAEnCsmB,EAAkBA,KAC7B,MAAMC,EAAehmB,OAAOimB,WAAW,8BAA8BC,QACrE,OAAIvX,SAASwX,SAAS/S,WAAW,kBACxB,MACEtL,UAAUse,YAAcJ,EAC1B,aAEF,WAEI1e,EAAoBye,IACjC,IAAID,EAAmB,WACrB,MAAM3W,EAAWsG,YAAY,KACD,eAAtBsQ,MACFvY,QAAQwQ,IAAI,uBACZtI,cAAcvG,GAEVnP,OAAOmQ,QAAUnQ,OAAOmQ,OAAOE,OAAOrQ,OAAOmQ,OAAOE,MAAM,uBAE/D,MAGE,MAAM9I,EAAc,MACzB,MAAM8e,EAAKve,UAAUwe,UACfC,EAAQF,EAAGG,MAAM,iCACjBC,EAAYJ,EAAGhS,QAAQ,YAAc,GAAKgS,EAAGhS,QAAQ,QAAU,EACrE,OAAIkS,EAAc,MACdE,EAAkB,UACf,MANkB,GASpB,SAASnd,EAAcod,GAC5BA,EAAMA,GAAO,GACb,MAAMC,EAAQ,qDACRC,EAASD,EAAM7oB,OACrB,IAAI+oB,EAAM,GACV,IAAK,IAAIjT,EAAI,EAAGA,EAAI8S,EAAK9S,IACvBiT,GAAYF,EAAMG,OAAOrQ,KAAKC,MAAMD,KAAKsQ,SAAWH,IAEtD,OAAOC,GAAM,IAAIvT,MAAOsI,UAGnB,MAAMoL,EAAaA,CAAChH,EAAI,GAAItqB,EAAS,MAC1C,MAAM+K,GAAQuf,GAAK,IAAI7T,QAAQ,IAAK,KAC9B8a,EAAYtlB,SAASF,OAASE,SAASD,SAE7C1B,OAAO2B,SAASC,KAAO,wDAA+CiR,mBAAmBoU,gCAAwCxmB,SAAY/K,KAGlI+F,EAAc,SAAUpB,EAAOe,GAC1Cf,EAAQ2E,OAAO3E,EAAM6sB,QAAQ,IAC7B,MAAM3X,EAAM,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,OAC5L,OAAIA,EAAItW,SAASmC,GAAkBqb,KAAK0Q,KAAK9sB,IAChCA,EAAM6sB,QAAQ,IAGhB7H,EAAa,WACxB,MAAM5d,EAASzC,OAAO8O,UAAUoZ,QAEhCloB,OAAO8O,UAAUoZ,QAAU,SAAUE,GACnC,MAAMC,EAAS5X,KACf,GAAI2X,EAAI,IAAMA,EAAI,EAAG,MAAM,IAAIE,WAAW,sDAC1C,GAAItoB,OAAOuoB,MAAMF,GAAS,MAAM,IAAIG,UAAUH,EAAS,gCAEvD,YAAUhoB,IAAN+nB,GAAyB,IAANA,EAAgB3Q,KAAKgR,MAAMJ,GAAQK,WAEnDjmB,EAAOkmB,KACZlR,KAAKgR,OAAOhY,KAAOzQ,OAAO4oB,SAAWnR,KAAKoR,IAAI,GAAIT,IAAM3Q,KAAKoR,IAAI,GAAIT,GACrEA,KAKOU,EAAW,SAAUC,EAAYC,EAAQ,GAAIC,GAAe,GACvE,MAAM5U,EAAO,IAAIC,KAAKA,KAAKC,OAAS0U,EAAe,EAAqC,IAAjC,IAAI3U,MAAOE,oBAA2B,MACvF0U,EAAW,GAAG7U,EAAKI,iBAAiBJ,EAAKK,WAAa,KAAKL,EAAKM,YAChEwU,EAAa9rB,KAAKqJ,MAAM5G,aAAaI,QAAQ6oB,IAAe,MAC5DK,GAAaD,EAAWlvB,SAASivB,MAAeF,EAAMlqB,QAAUkqB,EAAM/uB,SAASivB,IAKrF,OAJIE,IACFD,EAAW9lB,KAAK6lB,GAChBppB,aAAaC,QAAQgpB,EAAY1rB,KAAKC,UAAU6rB,KAE3CC,GAGIC,EAAgB1S,IAC3B,MAAM2S,EAAS,EACTC,EAAS,CACb,CAAE5lB,MAAO,EAAG6lB,OAAQ,IACpB,CAAE7lB,MAAO,IAAK6lB,OAAQ,KACtB,CAAE7lB,MAAO,IAAK6lB,OAAQ,KACtB,CAAE7lB,MAAO,IAAK6lB,OAAQ,KACtB,CAAE7lB,MAAO,KAAM6lB,OAAQ,KACvB,CAAE7lB,MAAO,KAAM6lB,OAAQ,KACvB,CAAE7lB,MAAO,KAAM6lB,OAAQ,MAEnBC,EAAK,2BACX,IAAI5rB,EAAO0rB,EAAOhD,QAAQmD,UAAUC,MAAK,SAAU9rB,GACjD,OAAO8Y,GAAO9Y,EAAK8F,SAErB,OAAO9F,GAAQ8Y,EAAM9Y,EAAK8F,OAAOukB,QAAQoB,GAAQnc,QAAQsc,EAAI,MAAQ5rB,EAAK2rB,OAAS,KA0B9E,SAASI,EAAgBpS,GAC9B,IAAIqS,EAAa,GACbC,GAAkB,EAClB9oB,OAAO+oB,OAAS/oB,OAAO+oB,MAAMC,MAC/BhpB,OAAO+oB,MAAMC,OAAM,WACjBH,EAAa7oB,OAAO+oB,MAAME,YAAcjpB,OAAO+oB,MAAME,cAAgBJ,EAEhEC,IACHA,GAAkB,EAClBtS,GAAMA,EAAGqS,QAIbrb,QAAQC,MAAM,uBACd+I,GAAMA,EAAGqS,IAIN,SAASK,IACd,MAAO,CACLra,WAAY,KACZsa,aAAc,2FACdC,WAAYC,OAAOD,WACnBE,aAAc3a,SAASI,gBAAgBwa,cAAgB5a,SAASiP,KAAK2L,aACrEC,QAAQ,IAAIlW,MAAOE,oBACnB8S,UAAWxe,UAAUwe,UACrBmD,YAAa9a,SAASI,gBAAgB2a,aAAe/a,SAASiP,KAAK8L,YACnEC,aAAa,EACbC,mBAAmB,GAIhB,SAASC,EAAYC,EAAerzB,GACzC,MAAM+sB,EAAM/sB,EACZ,SAASszB,EAAaC,GACpB,OAAOA,EAAcvjB,MAAM,KAAK8C,IAAIS,GAAQmb,OAAO8E,aAAajgB,EAAO,IAAIkgB,KAAK,IAElF,IAAIC,EAAYC,IAASC,IAAIC,QAC3BR,EACAM,IAASG,IAAIC,KAAK9kB,MAAMqkB,EAAYvG,IACpC,CACEiH,GAAIL,IAASG,IAAIC,KAAK9kB,MAAMqkB,EAAYvG,GAAKkH,OAAO,EAAG,KACvDnQ,KAAM6P,IAAS7P,KAAKoQ,IACpBC,QAASR,IAASS,IAAIC,QAGtBC,EAAeZ,EAAUzC,SAAS0C,IAASG,IAAIC,MAEnD,OAAOO,EAEF,SAASC,EAAYlB,GAC1B,MAAMtG,EAAM,iHACZ,SAASuG,EAAaC,GACpB,OAAOA,EAAcvjB,MAAM,KAAK8C,IAAIS,GAAQmb,OAAO8E,aAAajgB,EAAO,IAAIkgB,KAAK,IAGlF,MAAMe,EAAWb,IAASC,IAAIa,QAC5BpB,EACAM,IAASG,IAAIC,KAAK9kB,MAAMqkB,EAAYvG,IACpC,CACEiH,GAAIL,IAASG,IAAIC,KAAK9kB,MAAMqkB,EAAYvG,GAAKkH,OAAO,EAAG,KACvDnQ,KAAM6P,IAAS7P,KAAKoQ,IACpBC,QAASR,IAASS,IAAIC,QAI1B,OAAOG,EAASvD,WAGX,MAAMhM,EAAO,MAClB,IAAI2K,EAAKve,UAAUwe,UAAUnT,cACzBgY,GAA6C,IAAlC9E,EAAGhS,QAAQ,kBAC1B,OAAO8W,GAHW,GAMbvH,eAAewH,EAAWxgB,EAAU,IAEzC,MAAMzE,SAAe,6CAAmBiG,QAExC,GAAIjG,EAAMvP,MAAM+B,SAASgI,OAAQ,CAC/B,MAAMyjB,EAAUxZ,EAAQyJ,QAAQ,MAAQ,EAAI,IAAM,IAClDzJ,GAAW,GAAGwZ,WAAiBvR,mBAAmB1M,EAAMvP,MAAM+B,SAASgI,UAEzEX,OAAO8S,KAAKlI,EAAS,Y,qBC3PvB,IAAIrB,EAAM,CACT,YAAa,CACZ,OACA,cAED,YAAa,CACZ,OACA,cAED,YAAa,CACZ,QAED,YAAa,CACZ,OACA,cAED,YAAa,CACZ,OACA,cAED,YAAa,CACZ,OACA,cAED,YAAa,CACZ,OACA,cAED,YAAa,CACZ,OACA,cAED,YAAa,CACZ,OACA,cAED,YAAa,CACZ,OACA,cAED,YAAa,CACZ,OACA,eAED,YAAa,CACZ,OACA,eAED,YAAa,CACZ,OACA,eAED,YAAa,CACZ,OACA,eAED,YAAa,CACZ,OACA,eAED,YAAa,CACZ,OACA,eAED,YAAa,CACZ,OACA,eAED,YAAa,CACZ,OACA,eAED,eAAgB,CACf,OACA,eAED,eAAgB,CACf,OACA,gBAGF,SAASC,EAAoBC,GAC5B,IAAIC,EAAoBL,EAAEE,EAAKE,GAC9B,OAAOE,QAAQC,UAAUC,MAAK,WAC7B,IAAIC,EAAI,IAAIC,MAAM,uBAAyBN,EAAM,KAEjD,MADAK,EAAEE,KAAO,mBACHF,KAIR,IAAIvT,EAAMgT,EAAIE,GAAMQ,EAAK1T,EAAI,GAC7B,OAAOoT,QAAQ/K,IAAIrI,EAAIgvB,MAAM,GAAGhc,IAAIG,EAAoBI,IAAID,MAAK,WAChE,OAAOH,EAAoB/O,EAAEsP,EAAI,MAGnCT,EAAoBU,KAAO,WAC1B,OAAOnV,OAAOmV,KAAKX,IAEpBC,EAAoBS,GAAK,OACzBrV,EAAOC,QAAU2U", "file": "js/chunk-common.05413c2f.js", "sourcesContent": ["export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./loading.vue?vue&type=style&index=0&id=431badf6&prod&lang=scss&scoped=true\"", "export * from \"-!../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../node_modules/cache-loader/dist/cjs.js??ref--1-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./App.vue?vue&type=style&index=0&id=91be50f6&prod&lang=scss\"", "module.exports = __webpack_public_path__ + \"static/**********/img/find_ss_uid.fb889732.jpeg\";", "module.exports = __webpack_public_path__ + \"static/**********/img/01.e10e50c5.jpg\";", "module.exports = __webpack_public_path__ + \"static/**********/img/ss-logo.5d23881b.png\";", "module.exports = __webpack_public_path__ + \"static/**********/img/ssd-01.12517efb.jpeg\";", "module.exports = __webpack_public_path__ + \"static/**********/img/01.9e1400ad.png\";", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./CallbackPendingTips.vue?vue&type=style&index=0&id=34bf5da9&prod&scoped=true&lang=scss\"", "module.exports = __webpack_public_path__ + \"static/**********/img/login-validate-game-scrrencut.2ccb2fa1.jpeg\";", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ChargeConstruction.vue?vue&type=style&index=0&id=25c29973&prod&scoped=true&lang=scss\"", "module.exports = __webpack_public_path__ + \"static/**********/img/login-validate-game-scrrencut.4b523a15.jpeg\";", "/* RP表示直购礼包 */\nimport { baseKey, functionSwitch } from '@/config/game/defaultConfig'\n\nconst localKey = Object.create(baseKey)\nlocalKey.pageTitle = 'st-official-website-title'\nconst localSwitch = Object.create(functionSwitch)\n\nlocalSwitch.enableAnimation = true\n\nconst config = {\n  gameinfo: {\n    gameProject: 'st_global',\n    gameCode: 'ST', // 控制api\n    gameId: '2202',\n    whiteChannel: [],\n    blackChannel: [],\n    greyChannel: [],\n    gameName: 'Storm Shot',\n    appGameDeepLinkIos: 'https://st-universal-link.kingsgroupgames.com',\n    appGameDeepLinkAndroid: 'com.sivona.stormshot.e://'\n  },\n  apiParams: {},\n  langKey: localKey,\n  images: {\n    logoPath: 'https://kg-web-cdn.akamaized.net/prod/web-pay-unique_gog/dist_st_online/static/img/st-logo.41d54e47.png',\n    iconDiamond: require('@/assets/st/strp/sdk2-diamond-st.png')\n  },\n  ids: {\n    gid: 'G-K8VLKTWD8Q',\n    // appId: 'DAk5Rea1745289938511',\n    secretKey: '116 117 96 104 109 112 99 98 109 79 79 112 80 91 111 108 36 112 51 114 99 114 68 102 82 67 50 106 89 102 74 83'\n  },\n  switch: localSwitch\n}\n\nexport default config\n", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./BackendPopup.vue?vue&type=style&index=0&id=cc5e4232&prod&scoped=true&lang=scss\"", "import Vue from 'vue'\nimport { priceHelper } from '@/utils/utils'\nimport i18n from '@/utils/i18n'\n\nexport default {\n  namespaced: true,\n  state: {\n    /* 渠道 */\n    chosenChannel: {},\n\n    /* ss钻石 */\n    chosenDiamond: {},\n\n    /* 优惠券 */\n    chosenCoupon: {},\n    isFirstPayUsed: true,\n    isInit: false,\n    chosenCouponOther: {},\n\n    vip: {\n      discountSubChannelId: [],\n      diamondBonus: 0,\n      channelBonus: 0,\n      level: 0,\n      isInit: false,\n      isNewUser: false\n    },\n\n    /* 首充折扣档位 */\n    firstPayProducts: {},\n\n    // 每日奖励状态\n    gotDailyReward: false,\n    extraCostInfo: {},\n    koaTopupEnable: false,\n\n    switchToggleState: false, // 优惠券转化是否开启\n    showDoubleExperience: false,\n\n    defaultRebateDynamicInfo: {}, // 固定动态返钻，每个档位价格不一样\n    defaultRebateDynamicInfoAll: {},\n\n    defaultRebateInfo: {}, // 固定返钻\n    defaultRebateInfoByProduct4: {}, // ss等不是每个档位都有固定折扣，但是第四个有，通过第四个档位来判断该用户是否有固定折扣\n    defaultDiscountInfo: {}, // 固定折扣\n    defaultDiscountInfoByProduct4: {},\n\n    isFixedEventOpen: false, // mc、gog 切换活动是否开启\n    isFixedRebateWork: false // mc、gog通过开关切成固定返钻\n  },\n  getters: {\n    // 开启长期XX折\n    takeEffectDefaultDiscount (state, getters, rootState) {\n      if (!rootState.gameinfo.defaultDiscount) return false\n      // case 1： 登录情况下，没有首冲、没有选中优惠券\n      return (rootState.userinfo.isLogin && !!(state.isFirstPayUsed && !state.chosenCoupon.FE_INDEX)) ||\n        // case 2: 未登录\n        (!rootState.userinfo.isLogin)\n    },\n    // 固定返钻\n    takeEffectDefaultRebate (state, getters, rootState) {\n      if (!state.defaultRebateInfo.FE_INDEX) return false\n      return (rootState.userinfo.isLogin && !!(state.isFirstPayUsed && !state.chosenCoupon.FE_INDEX)) ||\n        (!rootState.userinfo.isLogin)\n    },\n    TWMyCard (state) {\n      const chosenDiamond = state.chosenDiamond\n      return chosenDiamond.product_id && chosenDiamond.product_id.includes('com.kingsgroup.ss.tw.mc')\n    },\n    FinalPriceState (state, getters, rootState) {\n      // const chosenDiamond = state.chosenDiamond\n      const chosenCoupon = state.chosenCoupon\n      const chosenDiamond = state.chosenDiamond\n      const temp = {\n        /* 优惠券 */\n        rawOriginPrice: 0,\n        rawNowPrice: 0, // 展示原价\n        taxation: 0,\n        extra_fee_amount: 0,\n        offCountTips: '', // 比如多少折\n        offCountAmount: '', // 比如实际少了2块\n        /* 上面价格 + (加税+额外费用) */\n        finalNowPrice: 0,\n        finalOriginPrice: 0,\n\n        rate: '', // 返钻比例\n        coin: 0,  // 实际发放钻石数量\n        level_coin: 0,  // 档位钻石数量\n        // 其他辅助\n        feType: '', // 优惠券类型,\n        isDefault: false, // 95或者返钻\n        isFixedCoupon: false, // 固定折扣\n        isFixedRebate: false, // 固定返钻\n        sdkType: '' // 首充优惠与固定优惠 act_type\n      }\n      const couponKey = chosenCoupon.feType || state.defaultRebateInfo.feType || state.defaultDiscountInfo.feType || state.defaultRebateDynamicInfo.feType\n\n\n      // 首冲、优惠券\n      switch (couponKey) {\n        case 'first_pay': {\n          temp.feType = 'first_pay'\n          temp.finalOriginPrice = chosenCoupon.price\n          temp.finalNowPrice = chosenCoupon.discount_price\n          temp.rawNowPrice = chosenCoupon.no_tax_price\n          temp.rawOriginPrice = chosenCoupon.level_currency_price\n          temp.offCountTips = `1st Pay ${chosenCoupon.rate} OFF`\n          temp.sdkType = chosenCoupon.type\n          break\n        }\n        case 'first_pay_rebate': {\n          temp.feType = 'first_pay_rebate'\n\n          temp.finalNowPrice = chosenCoupon.price\n          temp.rawNowPrice = chosenDiamond.no_tax_price\n          temp.taxation = chosenCoupon.taxation\n          temp.extra_fee_amount = chosenCoupon.extra_fee_amount\n          temp.rate = chosenCoupon.rate\n          temp.coin = chosenCoupon.coin\n          temp.level_coin = chosenCoupon.level_coin\n          temp.sdkType = chosenCoupon.type\n\n          temp.offCountTips = `1st Pay ${i18n.t('bonus_tips')} ${chosenCoupon.rate} <i class=\"diamond-icon\"></i>`\n          break\n        }\n        case 'discount_coupon': {\n          temp.feType = 'discount_coupon'\n          temp.finalOriginPrice = chosenCoupon.price\n          temp.finalNowPrice = chosenCoupon.discount_price\n          temp.rawNowPrice = chosenCoupon.no_tax_price\n          temp.rawOriginPrice = chosenCoupon.level_currency_price\n\n          temp.offCountTips = i18n.t('coupon_discount', { 0: chosenCoupon.rateWidthOutPercent })\n          break\n        }\n        case 'cash_coupon': {\n          temp.feType = 'cash_coupon'\n          temp.finalOriginPrice = chosenDiamond.price\n          temp.finalNowPrice = chosenCoupon.price\n          temp.rawNowPrice = chosenCoupon.no_tax_price\n          temp.rawOriginPrice = chosenCoupon.level_currency_price\n\n          temp.offCountTips = `${chosenCoupon.deduct_price} ${chosenDiamond.currency_symbol} OFF`\n          break\n        }\n        case 'rebate_coupon': {\n          temp.feType = 'rebate_coupon'\n          temp.finalNowPrice = chosenCoupon.price\n          temp.rawNowPrice = chosenCoupon.no_tax_price\n\n          temp.offCountTips = `${i18n.t('bonus_tips')} ${chosenCoupon.rate} <i class=\"diamond-icon\"></i>`\n          break\n        }\n        case 'fixed_discount_coupon': {\n          const defaultDiscountInfo = state.defaultDiscountInfo\n          // 有些没有95折\n          temp.feType = 'fixed_discount_coupon'\n          temp.isDefault = true\n\n          temp.finalNowPrice = defaultDiscountInfo.discount_price\n          temp.finalOriginPrice = defaultDiscountInfo.price\n          temp.rawNowPrice = defaultDiscountInfo.no_tax_price\n          temp.rawOriginPrice = defaultDiscountInfo.level_currency_price\n\n          temp.offCountTips = `${defaultDiscountInfo.rateWidthOutPercent}% OFF`\n          temp.offCountAmount = defaultDiscountInfo.discount_amount\n          temp.taxation = defaultDiscountInfo.taxation\n          temp.extra_fee_amount = defaultDiscountInfo.extra_fee_amount\n\n          temp.isFixedCoupon = true\n\n          temp.sdkType = defaultDiscountInfo.type\n          break\n        }\n        case 'fixed_rebate': {\n          const defaultRebateInfo = state.defaultRebateInfo\n          temp.feType = 'fixed_rebate'\n          temp.isDefault = true\n\n          temp.finalNowPrice = defaultRebateInfo.price\n          temp.rawNowPrice = defaultRebateInfo.no_tax_price\n          temp.taxation = defaultRebateInfo.taxation\n          temp.extra_fee_amount = defaultRebateInfo.extra_fee_amount\n\n          temp.offCountTips = `${i18n.t('bonus_tips')} ${defaultRebateInfo.rate} <i class=\"diamond-icon\"></i>`\n          temp.isFixedRebate = true\n          temp.sdkType = defaultRebateInfo.type\n          break\n        }\n        case 'fixed_dynamic_rebate': {\n          const defaultRebateDynamicInfo = state.defaultRebateDynamicInfo\n          temp.feType = 'fixed_dynamic_rebate'\n          temp.isDefault = true\n\n          temp.finalNowPrice = defaultRebateDynamicInfo.price\n          temp.rawNowPrice = defaultRebateDynamicInfo.no_tax_price\n          temp.taxation = defaultRebateDynamicInfo.taxation\n          temp.extra_fee_amount = defaultRebateDynamicInfo.extra_fee_amount\n\n          temp.offCountTips = `${i18n.t('bonus_tips')} ${defaultRebateDynamicInfo.rate} <i class=\"diamond-icon\"></i>`\n          temp.isFixedRebate = true\n          break\n        }\n        default: {\n          temp.rawNowPrice = chosenDiamond.no_tax_price\n          temp.finalNowPrice = chosenDiamond.price\n\n          const chosenChannel = state.chosenChannel\n          const unionKey = chosenChannel.channel_id + (chosenChannel.sub_channel_id ? `_${chosenChannel.sub_channel_id}` : '')\n          const { level_currency_price: price, currency, tax_rate: taxRate, chosenNum = 1 } = state.chosenDiamond\n          const extraRate = state.extraCostInfo[unionKey] || state.extraCostInfo[chosenChannel.channel_id] || 1\n\n          if (extraRate && price) {\n            temp.extra_fee_amount = priceHelper(chosenNum * price * (extraRate - 1), currency)\n            temp.finalNowPrice = priceHelper(chosenNum * price * (extraRate - 1 + (taxRate || 1)), currency)\n            state.chosenDiamond.extra_fee_amount = temp.extra_fee_amount // 不改之前的逻辑，因为之前是否显示额外费用用的此参数\n          }\n        }\n      }\n\n      return fixPrice(temp, state.chosenDiamond)\n    },\n    isDiamondOwn95Off: (state) => diamond => state.defaultDiscountInfo[diamond.product_id] || state.defaultDiscountInfoByProduct4[diamond.product_id] || false,\n    isDiamondOwnRebate: (state) => diamond => state.defaultRebateInfo[diamond.product_id] || state.defaultRebateInfoByProduct4[diamond.product_id] || state.defaultRebateDynamicInfoAll[diamond.product_id] || false,\n    getRebateCoin (state) {\n      return (diamond) => {\n        const t = state.defaultRebateInfo[diamond.product_id] || state.defaultRebateInfoByProduct4[diamond.product_id] || state.defaultRebateDynamicInfoAll[diamond.product_id]\n        return (t.coin - t.level_coin) || 0\n      }\n    },\n    getSDKRebateCoin (state) {\n      return (state.defaultRebateInfo.coin - state.defaultRebateInfo.level_coin) || 0\n    }\n  },\n  mutations: {\n    /* 渠道 */\n    setChosenChannel (state, payload) {\n      state.chosenChannel = payload\n\n      resetPrice(state)\n      require('@/utils/logHelper').logForClickChannel(JSON.stringify({\n        channel: payload.channel_name,\n        channel_id: payload.channel_id,\n        sub_channel_id: payload.sub_channel_id\n      }))\n    },\n    resetChannel (state) {\n      state.chosenChannel = {}\n\n      resetPrice(state)\n    },\n    /* ss钻石 */\n    setChosenDiamond (state, payload) {\n      state.chosenDiamond = payload\n\n      resetPrice(state)\n      require('@/utils/logHelper').logForClickDiamond(JSON.stringify(payload))\n    },\n    /* 优惠券 */\n    setChosenCoupon (state, payload) {\n      if (!payload) return\n      state.chosenCoupon = payload\n\n      state.chosenCouponOther = {}\n      for (const item of Object.values(payload.product_discount_range || [])) {\n        Vue.set(state.chosenCouponOther, item.product_id, item)\n      }\n\n      require('@/utils/logHelper').logForClickCoupon(payload)\n    },\n    setFirstPayStatus (state, payload) {\n      state.isFirstPayUsed = payload\n    },\n    setIsInit (state, payload) {\n      state.isInit = payload\n    },\n    resetCouponInfo (state) {\n      state.chosenCoupon = {}\n      state.chosenCouponOther = {}\n      state.isFirstPayUsed = true\n      state.isInit = false\n      state.firstPayProducts = {};\n    },\n    initVipInfo (state, payload) {\n      state.vip.discountSubChannelId = ['scheme', '1380']\n      state.vip.diamondBonus = payload.vip_bonus\n      state.vip.channelBonus = payload.pay_bonus\n      state.vip.level = payload.level\n      state.vip.isInit = payload.is_white\n      state.vip.isNewUser = (payload.exp === 0 && payload.level === 1)\n\n      state.gotDailyReward = payload.daily_reward_receive\n    },\n    setFirstPayProducts (state, payLoad) {\n      if (!payLoad.length) return\n      (payLoad[0].product_discount_range || []).forEach(item => {\n        state.firstPayProducts[item.product_id] = { ...item }\n      })\n    },\n    setFixedCoupon (state, payload) {\n      state.defaultDiscountInfo = payload\n      for (const item of Object.values(payload.product_discount_range || [])) {\n        Vue.set(state.defaultDiscountInfo, item.product_id, item)\n      }\n    },\n    // 固定返钻\n    setFixedRebate (state, payload) {\n      state.defaultRebateInfo = payload\n      for (const item of Object.values(payload.product_discount_range || [])) {\n        Vue.set(state.defaultRebateInfo, item.product_id, item)\n      }\n    },\n    setFixedCouponByProduct4 (state, payload) {\n      for (const item of Object.values(payload.product_discount_range || [])) {\n        Vue.set(state.defaultDiscountInfoByProduct4, item.product_id, item)\n      }\n    },\n    setFixedRebateByProduct4 (state, payload) {\n      for (const item of Object.values(payload.product_discount_range || [])) {\n        Vue.set(state.defaultRebateInfoByProduct4, item.product_id, item)\n      }\n    },\n\n    setDailyRewardStatus (state, payload) {\n      state.gotDailyReward = payload\n    },\n    setExtraCostInfo (state, payLoad = []) {\n      payLoad.forEach(item => {\n        const unionKey = item.channel_id + (item.sub_channel_id ? `_${item.sub_channel_id}` : '')\n        state.extraCostInfo[unionKey] = item.extra_fee_rate\n      })\n    },\n    setKoaTopupEnable (state, payload) {\n      state.koaTopupEnable = payload\n    },\n    switchToggle (state, payload) {\n      state.switchToggleState = payload || false\n    },\n    switchDoubleExperience (state, payload) {\n      state.showDoubleExperience = payload || false\n    },\n    setFixedDynamicRebate (state, { chosen, all } = {}) {\n      state.defaultRebateDynamicInfo = chosen\n      for (const item of (all || [])) {\n        Vue.set(state.defaultRebateDynamicInfoAll, item.product_id, item)\n      }\n    },\n    toggleCoupon (state) {\n      state.isFixedRebateWork = !state.isFixedRebateWork\n      localStorage.setItem('fixedRebateWorkStatus', Number(state.isFixedRebateWork))\n    },\n    setFixedToggleEvent (state, payload) {\n      state.isFixedEventOpen = payload\n      if (payload) {\n        state.isFixedRebateWork = localStorage.getItem('fixedRebateWorkStatus') === '1'\n      }\n    }\n  }\n}\n\nlet timeout\nfunction resetPrice (state) {\n  if (timeout) {\n    clearTimeout(timeout)\n    timeout = undefined\n  }\n\n  timeout = setTimeout(() => {\n    const chosenChannel = state.chosenChannel\n    const unionKey = chosenChannel.channel_id + (chosenChannel.sub_channel_id ? `_${chosenChannel.sub_channel_id}` : '')\n    const extraRate = state.extraCostInfo[unionKey] || state.extraCostInfo[chosenChannel.channel_id] || 1\n    const { level_currency_price: price, currency, tax_rate: taxRate, chosenNum = 1 } = state.chosenDiamond\n\n    // Vue.set(state.chosenDiamond, 'extra_fee_amount', priceHelper(chosenNum * price * (extraRate - 1), currency))\n    // Vue.set(state.chosenDiamond, 'nowPriceWithTaxAndExtra', priceHelper(chosenNum * price * (extraRate - 1 + (taxRate || 1)), currency))\n\n    clearTimeout(timeout)\n    timeout = null\n  }, 500)\n}\n\nfunction fixPrice (temp, chosenDiamond) {\n  const currencySymbol = chosenDiamond.currency_symbol\n  const toAddSymbolList = ['rawOriginPrice', 'rawNowPrice', 'finalNowPrice', 'finalOriginPrice']\n  toAddSymbolList.forEach(key => {\n    if (temp[key]) temp[key] += ` ${currencySymbol}`\n  })\n\n  return temp\n}\n", "import Vue from 'vue'\n\nexport default {\n  namespaced: true,\n  state: {\n    gameProject: '',\n    gameCode: '',\n    gameName: '',\n    appId: '',\n    gameId: '',\n\n    defaultDiscount: true,\n    whiteChannel: [],\n    blackChannel: [],\n    greyChannel: [],\n    mainBody: 'funplus', // 游戏主体\n\n    isSS: false,\n    isKOA: false,\n    isROMCP: false,\n    game: window.__GAMENAME,\n    isCn: false\n  },\n  mutations: {\n    setGameInfo: (state) => {\n      const payload = window.$gcbk('gameinfo', {})\n      state.gameProject = payload.gameProject\n      state.gameCode = payload.gameCode\n      state.gameName = payload.gameName\n      state.appId = payload.appId\n      state.gameId = payload.gameId\n\n      state.defaultDiscount = payload.defaultDiscount\n      state.whiteChannel = payload.whiteChannel\n      state.blackChannel = payload.blackChannel\n      state.greyChannel = payload.greyChannel\n\n      state.mainBody = payload.mainBody\n      state.isCn = payload.isCn\n      Vue.set(state, `is${state.gameCode}`, true)\n    }\n  },\n  getters: {\n    isPuzalaGame: state => state.mainBody === 'puzala',\n  }\n}\n", "import { StorageUtils } from '@/utils/storageUtils'\n\nexport default {\n  namespaced: true,\n  state: {\n    isLogin: false,\n    country: '',\n    currency: '',\n    fpid: '',\n    icon: '',\n    lang: '',\n    level: 0,\n    name: '',\n    openid: '',\n    pkg_channel: '',\n    server: 0,\n    lastChannel: {} // 上次选中的渠道\n  },\n  mutations: {\n    setUserInfo (state, payload) {\n      state.level = payload.level\n      state.server = payload.server\n      state.uid = payload.uid\n      state.openid = payload.openid\n      state.icon = payload.icon\n      state.name = payload.name\n      state.fpid = payload.fpid\n      localStorage.setItem('openid', state.openid)\n\n      state.isLogin = true\n\n      const logParams = {}\n      if (payload.uid) logParams.uid = payload.uid\n      if (payload.openid) logParams.openid = payload.openid\n      window.__ReportExtraData__ && window.__ReportExtraData__(logParams)\n      StorageUtils.init(payload.openid)\n    },\n    saveLastChannelInfo (state, payload) {\n      state.lastChannel = payload.last_channel\n    },\n    logout () {\n      localStorage.removeItem('openid')\n\n      const { origin, pathname } = location\n      window.location.href = `${origin}${pathname}`\n    }\n  }\n}\n", "import { OrderPageOpenidKey } from '@/config/OrderPageConf'\n\nexport default {\n  namespaced: true,\n  state: {\n    userinfo: {\n      isLogin: false,\n      country: '',\n      currency: '',\n      fpid: '',\n      icon: '',\n      lang: '',\n      level: 0,\n      name: '',\n      openid: '',\n      pkg_channel: '',\n      server: 0\n    }\n  },\n  mutations: {\n    setUserInfo (state, payload) {\n      state.userinfo.level = payload.level\n      state.userinfo.server = payload.server\n      state.userinfo.uid = payload.uid\n      state.userinfo.openid = payload.openid\n      state.userinfo.icon = payload.icon\n      state.userinfo.name = payload.name\n      localStorage.setItem(OrderPageOpenidKey, state.userinfo.openid)\n\n      state.userinfo.isLogin = true\n\n      const logParams = {}\n      if (payload.uid) logParams.uid = payload.uid\n      if (payload.openid) logParams.openid = payload.openid\n      window.__ReportExtraData__ && window.__ReportExtraData__(logParams)\n    },\n    logout () {\n      localStorage.removeItem(OrderPageOpenidKey)\n\n      const { origin, pathname } = location\n      window.location.href = `${origin}${pathname}/order`\n    }\n  }\n}\n", "export default {\n  namespaced: true,\n  state: {\n    currentKey: [],\n    // currentValue: 0,\n    isInit: false,\n    emit: () => {}\n  },\n  getters: {\n    forbiddenAccess (state) {\n      if (!state.isInit) return false\n      return state.currentKey._hasUnion(['always_banned', 'banned_uid'])\n    },\n    showAdyenTips (state) {\n      if (!state.isInit) return false\n      return state.currentKey._hasUnion(['use_adyen'])\n    },\n    showTipsWhenSomeChannel (state, getters) {\n      const finalNames = []\n      if (!state.isInit) return finalNames\n      if (getters.showAdyenTips) finalNames.push('adyen')\n      if (state.currentKey.includes('use_wxpay')) finalNames.push('wxpay')\n      if (state.currentKey.includes('use_alipay')) finalNames.push('alipay')\n      if (state.currentKey.includes('use_paypal')) finalNames.push('paypal')\n      if (state.currentKey.includes('use_pingpong')) finalNames.push('pingpong')\n\n      return finalNames\n    },\n    hideAdyenChannel (state) {\n      if (!state.isInit) return false\n      return state.currentKey._hasUnion(['banned_adyen', 'banned_adyen_card'])\n    },\n    hideSomeChannel (state, getters) {\n      const finalNames = []\n      if (!state.isInit) return finalNames\n      if (getters.hideAdyenChannel) finalNames.push('adyen')\n      if (state.currentKey.includes('banned_wxpay')) finalNames.push('wxpay')\n      if (state.currentKey.includes('banned_alipay')) finalNames.push('alipay')\n      if (state.currentKey.includes('banned_paypal')) finalNames.push('paypal')\n      if (state.currentKey.includes('banned_pingpong')) finalNames.push('pingpong')\n\n      return finalNames\n    }\n  },\n  mutations: {\n    init (state, payload = {}) {\n      // payload.list = [{\n      //   banned_status: 'always_banned',\n      //   expire_time: 0\n      // }]\n      const { list = [], emit } = payload\n      state.isInit = true\n      state.emit = emit\n\n      list.forEach(item => {\n        const { banned_status: key, expire_time: value } = item\n        state.currentKey.push(key)\n\n        if (['always_banned', 'banned_uid', 'access_warn', 'access_warn_black_room'].includes(key)) {\n          emit('showPop', 'RiskControlPolicy', { key, value })\n        }\n      })\n    }\n  }\n}\n", "export default {\n  namespaced: true,\n  state: {\n    builtInCashier: false, // 内嵌收银台，前置卡组件忽略\n    isDiscountUsed: false // 小档位是否使用\n  },\n  mutations: {\n    setIsDiscountUsed (state, payload) {\n      state.isDiscountUsed = payload\n    },\n    savePrefixChannel (state, payload) {\n      if (payload && payload.length) {\n        state.builtInCashier = payload[0].type === 'drop_in' // 在此填写其他打开灰度的逻辑\n      }\n    },\n    resetBuiltInCashierStatus (state, payload) {\n      if (!payload || !payload.length) return null\n\n      const kv = {}\n      payload.forEach(item => {\n        kv[`${item.channel_id}_${item.channel_name}_${item.sub_channel_id}_${item.type}`] = item\n      })\n\n      const payermaxDropInObj = kv.payermax_A34_A34_dropin\n      if (payermaxDropInObj) {\n        switch (payermaxDropInObj.status) {\n          case 'close': {\n            state.builtInCashier = false\n            break\n          }\n          case 'open_all': {\n            state.builtInCashier = true\n            break\n          }\n          default: {\n            // 默认open_ab，不管\n          }\n        }\n      }\n    }\n  }\n}\n", "export default {\n  namespaced: true,\n  state: {\n    loginValidation: false,\n    showMobilePolicy: false, // 移动端在下面的退款协议\n    showPopPolicy: false, // 弹窗的协议\n    boon: false,\n    fixedDiscountType: '',\n    smallDiamondDoubleDiscount: false, // 100钻小档位双倍\n    ckoCheckedByDefault: true, // 默认勾选cko记住卡号\n    showPcDiscountTips: false // pc端左侧faq上面显示折扣信息\n  },\n  mutations: {\n    setFunctionInfo: (state) => {\n      const payload = window.$gcbk('switch', {})\n      state.loginValidation = payload.loginValidation\n      state.showMobilePolicy = payload.showMobilePolicy\n      state.showPopPolicy = payload.showPopPolicy\n      state.boon = payload.boon\n      state.fixedDiscountType = payload.fixedDiscountType\n      state.ckoCheckedByDefault = payload.ckoCheckedByDefault\n      state.smallDiamondDoubleDiscount = payload.smallDiamondDoubleDiscount\n      state.showPcDiscountTips = payload.showPcDiscountTips\n    },\n    updateFunctionInfo(state, payload){\n      if ('send_code_enable' in payload) state.loginValidation = payload.send_code_enable\n\n      // 切换功能\n      if ('switches' in payload) {\n        if ('bind_card' in payload['switches'])  state.ckoCheckedByDefault = payload['switches'].bind_card\n      }\n    }\n  }\n}\n", "import Vue from 'vue'\nimport Vuex from 'vuex'\nimport formdata from '@/store/module/formdata'\nimport gameinfo from '@/store/module/gameinfo'\nimport userinfo from '@/store/module/userinfo'\nimport orderPage from '@/store/module/orderPage'\nimport riskPolicy from '@/store/module/riskPolicy'\nimport vb from '@/store/module/vb'\nimport functionSwitch from '@/store/module/function'\nimport { getUrlParams, isArZone } from '@/utils/utils'\n\nVue.use(Vuex)\n\nexport default new Vuex.Store({\n  state: {\n    urlParams: getUrlParams(),\n\n    currencyUnit: '', // 货币符号\n    country: '',\n    city: '',\n    state: '',\n    currency: '',\n    zipCode: '',\n    isArZone: false, // 是否需要货币 金额和符号\n    isPc: window.innerWidth > 940,\n    isMobile: window.innerWidth < 940,\n\n    agreePrivacyPolicy: true,\n    isPCSDK: !!window.__isPCSDK,\n    IS_CHECKOUT_SDK: !!window.__IS_CHECKOUT_SDK,\n    IS_CHECKOUT_SDK_V2: !!window.__IS_CHECKOUT_SDK_V2\n  },\n  mutations: {\n    setCurrencyUnitByIp (state, payload) {\n      state.currencyUnit = payload.currency_symbol\n      state.country = payload.country\n      state.city = payload.city\n      state.state = payload.state\n      state.currency = payload.currency\n      state.isArZone = isArZone(payload.currency)\n      state.zipCode = payload.zipcode\n    },\n    resetIsXXX (state) {\n      state.isPc = window.innerWidth > 940\n      state.isMobile = window.innerWidth < 940\n    },\n    setPrivacyPolicyStatus (state, payload) {\n      state.agreePrivacyPolicy = payload\n    },\n\n    updateUrlParams (state, payload) {\n      const tc = JSON.parse(state.urlParams.tc || '{}')\n      Object.assign(tc, payload)\n      state.urlParams.tc = JSON.stringify(tc)\n    }\n  },\n  actions: {\n  },\n  modules: { formdata, gameinfo, userinfo, orderPage, riskPolicy, vb, functionSwitch }\n})\n", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=2a798d4a&prod&scoped=true&lang=scss\"", "module.exports = __webpack_public_path__ + \"static/**********/img/ss_uid.9e057120.png\";", "import qs from 'qs'\nimport { makeUpCommonParams } from '@/server/http'\nimport store from '@/store'\nimport { getPlatform, getPWADisplayMode, randomString } from '@/utils/utils'\nconst gameInfo = store.state.gameinfo\nconst userinfo = store.state.userinfo\nconst urlParams = store.state.urlParams\n\nlet browserMark = localStorage.getItem('browserMark') || ''\nif (!browserMark) {\n  browserMark = randomString()\n  localStorage.setItem('browserMark', browserMark)\n}\n\nfunction send (logoInfo) {\n  const info = {\n    app_id: `${store.state.gameinfo.gameProject.split('_')[0]}.global.prod`,\n    data_version: '3.0',\n    event: 'third_pay',\n    event_ts: 1670812301475,\n    fpid: userinfo.fpid,\n    // ip: '*************',\n    ts_pretty: '2022/12/12 02:31:41'\n  }\n  info.detail = {\n    gamecode: window.$gcbk('gameinfo.gameLogCode', '') || gameInfo.gameCode,\n    gameid: +gameInfo.gameId,\n    pay_channel: 'web',\n    tracking_id: urlParams.utm_campaign || '',\n    browser_id: browserMark,\n    opened_by: getPWADisplayMode === 'standalone' ? `pwa_${getPlatform}` : '',\n    ...logoInfo,\n    action: logoInfo.event\n  }\n  if (window.__isPCSDK || window.__IS_CHECKOUT_SDK) info.detail.pay_channel = urlParams.s\n  if (info.detail.gamecode === 'DC') {\n    info.detail.gamecode = 'dc'\n    info.app_id = 'dc.global.prod'\n  }\n  if (info.detail.gamecode === 'SSV2') info.app_id = 'ss.global.prod'\n  const { uid, openid } = userinfo\n  if (uid && !logoInfo.uid) info.detail.uid = uid\n  if (openid) info.detail.openid = openid\n\n  Object.assign(info, makeUpCommonParams())\n\n  // detail 补充 openid\n  if (info.openid && !info.detail.openid) info.detail.openid = info.openid\n\n  const biPath =  window.$gcbk('apiParams.biPath', '/bilog')\n  const target = `${process.env['VUE_APP_PREFIX_TOKEN_' + gameInfo.gameCode]}${biPath}`\n  if (navigator.sendBeacon) {\n    navigator.sendBeacon(target, JSON.stringify(info))\n  } else {\n    const img = new Image()\n    img.src = `${target}?${qs.stringify(info, { allowDots: true })}`\n  }\n}\n\n// export function logForPageLoad () {\n//   const params = {\n//     position: 'choose_pay',\n//     action: 'load_done'\n//   }\n//   send(params)\n// }\n//\n// export function logForBtnClick (method) {\n//   const params = {\n//     position: 'choose_pay',\n//     action: 'click_channel',\n//     method\n//   }\n//   send(params)\n// }\n//\n// export function logForConfirmPurchase (method) {\n//   const params = {\n//     position: 'choose_pay',\n//     action: 'click_method',\n//     method: method // '选择支付方式时加入该参数（采用选取的支付方式英文代称）'\n//   }\n//   send(params)\n// }\n//\n// export function logForPayResult (status, orderId, reason, method) {\n//   const params = {\n//     position: 'pay_end',\n//     action: 'judgment',\n//     status, // 'success / failed'\n//     order_id: orderId, // '订单id，订单唯一识别码'\n//     reason, // '余额不足等代指支付失败原因的英文代称 / ',\n//     method // '支付方式（采用选取的支付方式英文代称）'\n//   }\n//   send(params)\n// }\n\n// 收银台页面加载\nexport function logForPageLoad () {\n  const param = {\n    event: 'load_done'\n  }\n  send(param)\n}\n\n// 点击登录\nexport function logForClickLogin (uid) {\n  const params = {\n    event: 'click_login',\n    uid\n  }\n  send(params)\n}\n\n// 登录成功\nexport function logForLoginSuccess () {\n  const params = {\n    event: 'login_successful',\n    level: userinfo.level,\n    gameserver_id: userinfo.server,\n    name: userinfo.name\n  }\n  send(params)\n}\n\n// 渠道点击\nexport function logForClickChannel (method) {\n  const params = {\n    event: 'click_method',\n    method\n  }\n  send(params)\n}\n\n// 钻石点击\nexport function logForClickDiamond (method) {\n  const params = {\n    event: 'click_product',\n    method\n  }\n  send(params)\n}\n\n// 选择优惠券\nexport function logForClickCoupon (coupon) {\n  const params = {\n    event: 'select_coupon',\n    coupon_id: coupon.coupon_id,\n    coupon_description: coupon.type\n  }\n  send(params)\n}\n\n// 请求结果\nexport function logForPayResult (status, orderId, reason, orderInfo) {\n  const params = {\n    status, // 'success / failed'\n    order_id: orderId, // '订单id，订单唯一识别码'\n    reason, // '余额不足等代指支付失败原因的英文代称 / ',\n    ...orderInfo\n  }\n  send(params)\n}\n\nexport function logForSdk2OpenedSuccess (o = {}) {\n  const params = {\n    event: 'open_fpstore_checkout',\n    ...o\n  }\n  send(params)\n}\n", "var map = {\n\t\"./foundation.scss\": [\n\t\t\"8e24\",\n\t\t\"chunk-74c44dda\"\n\t],\n\t\"./gogCP.scss\": [\n\t\t\"fe86\",\n\t\t\"chunk-774c9dcc\"\n\t],\n\t\"./mcCP.scss\": [\n\t\t\"daef\",\n\t\t\"chunk-772f4c0b\"\n\t],\n\t\"./pc-cash-purchase.scss\": [\n\t\t\"e616\",\n\t\t\"chunk-77290e93\"\n\t],\n\t\"./romCP.scss\": [\n\t\t\"1479\",\n\t\t\"chunk-744b7684\"\n\t],\n\t\"./sdk2.scss\": [\n\t\t\"2a74\",\n\t\t\"chunk-746e021d\"\n\t],\n\t\"./ssCP.scss\": [\n\t\t\"8d4c\",\n\t\t\"chunk-74c3e6ae\"\n\t],\n\t\"./ssd.scss\": [\n\t\t\"2fa5\",\n\t\t\"chunk-7470e5c1\"\n\t],\n\t\"./stCP.scss\": [\n\t\t\"6ee1\",\n\t\t\"chunk-74a8ddee\"\n\t],\n\t\"./standard.scss\": [\n\t\t\"c887\",\n\t\t\"chunk-770de2b5\"\n\t]\n};\nfunction webpackAsyncContext(req) {\n\tif(!__webpack_require__.o(map, req)) {\n\t\treturn Promise.resolve().then(function() {\n\t\t\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\t\t\te.code = 'MODULE_NOT_FOUND';\n\t\t\tthrow e;\n\t\t});\n\t}\n\n\tvar ids = map[req], id = ids[0];\n\treturn __webpack_require__.e(ids[1]).then(function() {\n\t\treturn __webpack_require__.t(id, 7);\n\t});\n}\nwebpackAsyncContext.keys = function webpackAsyncContextKeys() {\n\treturn Object.keys(map);\n};\nwebpackAsyncContext.id = \"50ca\";\nmodule.exports = webpackAsyncContext;", "module.exports = __webpack_public_path__ + \"static/**********/img/sdk2-diamond-koa.c7b081d0.png\";", "module.exports = __webpack_public_path__ + \"static/**********/img/uid-tips.bbf9d6d4.jpeg\";", "import { baseKey, functionSwitch } from '@/config/game/defaultConfig'\n\nconst localKey = Object.create(baseKey)\nconst localSwitch = Object.create(functionSwitch)\nlocalSwitch.ckoCheckedByDefault = true\n\nconst config = {\n  gameinfo: {\n    gameProject: 'koa_global',\n    gameCode: 'KOA',\n    gameName: 'King of Avalon',\n    appId: 'koa.global.prod',\n    gameId: '2031',\n    defaultDiscount: false,\n    whiteChannel: [],\n    blackChannel: ['android_bilibili'],\n    greyChannel: [\n      { channel: ['ios_cn', 'android_cn'], to: process.env.VUE_APP_CN_ADDRESS_KOA },\n      { channel: ['rom_global_google'], to: process.env.VUE_APP_ROM_ADDRESS_KOA }\n    ]\n  },\n  apiParams: {\n    boonAme: {\n      projectId: 15,\n      loginAction: 1097,\n      getLoginReward: 1098,\n      pwaOpenAction: 1099,\n      getPwaReward: 1100\n    }\n  },\n  langKey: localKey,\n  images: {\n    whatsDiamond: [\n      { imageUrl: require('@/assets/koa/diamondGallery/01.jpg'), jumpUrl: '' },\n      { imageUrl: require('@/assets/koa/diamondGallery/02.jpg'), jumpUrl: '' },\n      { imageUrl: require('@/assets/koa/diamondGallery/03.jpeg'), jumpUrl: '' }\n    ],\n    uidTips: require('@/assets/koa/login/sample_koa.png'),\n    loganFindValidationCode: require('@/assets/koa/login/login-validate-game-scrrencut.jpeg')\n  },\n  ids: {\n    gid: 'G-LB15MXTLZK',\n    appId: 'e3Xnhny1706589599129',\n    secretKey: '108 112 98 96 104 109 112 99 98 109 79 112 80 91 111 108 36 112 51 114 99 114 68 102 82 67 50 106 89 102 74 83'\n  },\n  switch: localSwitch\n}\n\nexport default config\n", "import { baseKey, functionSwitch } from '@/config/game/defaultConfig'\n\nconst localKey = Object.create(baseKey)\nlocalKey.customDiamondLimitTips = 'custom-diamond-limit-koa'\nlocalKey.loginPlaceHolder = 'please-input-uid_koa'\nconst localSwitch = Object.create(functionSwitch)\n\nlocalSwitch.showMobilePolicy = false\nlocalSwitch.loginValidation = false\nlocalSwitch.ckoCheckedByDefault = true\n\nconst config = {\n  gameinfo: {\n    gameProject: 'koa_global',\n    gameCode: 'KOA',\n    gameName: 'King of Avalon',\n    appId: 'koa.global.prod',\n    gameId: '2031',\n    defaultDiscount: false,\n    whiteChannel: [],\n    blackChannel: [],\n    greyChannel: [\n      { channel: ['ios_cn', 'android_cn'], to: process.env.VUE_APP_CN_ADDRESS_KOA }\n    ]\n  },\n  apiParams: {\n    boonAme: {\n      projectId: 15,\n      loginAction: 1097,\n      getLoginReward: 1098,\n      pwaOpenAction: 1099,\n      getPwaReward: 1100\n    }\n  },\n  langKey: localKey,\n  images: {\n    whatsDiamond: [\n      { imageUrl: require('../../assets/koa/aof/diamondGallery/01.jpg'), jumpUrl: '' },\n      { imageUrl: require('../../assets/koa/aof/diamondGallery/02.jpg'), jumpUrl: '' },\n      { imageUrl: require('../../assets/koa/aof/diamondGallery/03.jpeg'), jumpUrl: '' },\n    ],\n    uidTips: require('@/assets/koa/login/sample_koa.png'),\n    loganFindValidationCode: require('@/assets/koa/login/login-validate-game-scrrencut.jpeg')\n  },\n  ids: {\n    gid: 'G-LT6WCT52Q5',\n    appId: 'RbWazjW1744102094299',\n    secretKey: '108 112 98 96 104 109 112 99 98 109 79 112 80 91 111 108 36 112 51 114 99 114 68 102 82 67 50 106 89 102 74 83'\n  },\n  switch: localSwitch\n}\n\nexport default config\n", "import { baseKey, functionSwitch } from '@/config/game/defaultConfig'\n\nconst localKey = Object.create(baseKey)\nlocalKey.customDiamondLimitTips = 'custom-diamond-limit-koa'\nlocalKey.loginPlaceHolder = 'please-input-uid_koa'\nconst localSwitch = Object.create(functionSwitch)\nlocalSwitch.showMobilePolicy = false\nlocalSwitch.loginValidation = false\nlocalSwitch.ckoCheckedByDefault = true\n\nconst config = {\n  gameinfo: {\n    gameProject: 'koa_global',\n    gameCode: 'KOA',\n    gameName: 'King of Avalon',\n    appId: 'koa.global.prod',\n    gameId: '2031',\n    defaultDiscount: false,\n    whiteChannel: [],\n    blackChannel: [],\n    greyChannel: [\n      { channel: ['ios_cn', 'android_cn'], to: process.env.VUE_APP_CN_ADDRESS_KOA }\n    ]\n  },\n  apiParams: {\n    boonAme: {\n      projectId: 15,\n      loginAction: 1097,\n      getLoginReward: 1098,\n      pwaOpenAction: 1099,\n      getPwaReward: 1100\n    }\n  },\n  langKey: localKey,\n  images: {\n    whatsDiamond: [\n      { imageUrl: require('../../assets/koa/aof/diamondGallery/01.jpg'), jumpUrl: '' },\n      { imageUrl: require('../../assets/koa/aof/diamondGallery/02.jpg'), jumpUrl: '' },\n      { imageUrl: require('../../assets/koa/aof/diamondGallery/03.jpeg'), jumpUrl: '' }\n    ],\n    uidTips: require('@/assets/koa/login/sample_koa.png'),\n    loganFindValidationCode: require('@/assets/koa/login/login-validate-game-scrrencut.jpeg')\n  },\n  ids: {\n    gid: 'G-LT6WCT52Q5',\n    appId: 'RbWazjW1744102094299',\n    secretKey: '108 112 98 96 104 109 112 99 98 109 79 112 80 91 111 108 36 112 51 114 99 114 68 102 82 67 50 106 89 102 74 83'\n  },\n  switch: localSwitch\n}\n\nexport default config\n", "import { baseKey, functionSwitch } from '@/config/game/defaultConfig'\n\nconst localKey = Object.create(baseKey)\nlocalKey.pageTitle = 'koa-official-website-title'\nlocalKey.customDiamondLimitTips = 'custom-diamond-limit-koa'\n\nconst localSwitch = Object.create(functionSwitch)\nlocalSwitch.showMobilePolicy = false\n\nconst config = {\n  gameinfo: {\n    gameProject: 'koa_global',\n    gameCode: 'KOA',\n    gameName: 'King of Avalon',\n    appId: 'koa.global.prod',\n    gameId: '2031',\n    defaultDiscount: false,\n    whiteChannel: ['ios_cn', 'android_cn'],\n    blackChannel: ['android_bilibili', 'android_cn_newbirth', 'android_cn37', 'android_cn37union'],\n    isCn: true // api请求用\n  },\n  apiParams: {\n    boonAme: {\n      projectId: 15,\n      loginAction: 1097,\n      getLoginReward: 1098,\n      pwaOpenAction: 1099,\n      getPwaReward: 1100\n    }\n  },\n  langKey: localKey,\n  images: {\n    whatsDiamond: [\n      // { imageUrl: require('@/assets/koa/diamondGallery/01.jpg'), jumpUrl: '' },\n      // { imageUrl: require('@/assets/koa/diamondGallery/02.jpg'), jumpUrl: '' },\n      // { imageUrl: require('@/assets/koa/diamondGallery/03.jpeg'), jumpUrl: '' }\n    ],\n    uidTips: require('@/assets/koa/login/sample_koa.png'),\n    loganFindValidationCode: require('@/assets/koa/login/login-validate-game-scrrencut.jpeg')\n  },\n  ids: {\n    // gid: 'G-LB15MXTLZK',\n    // appId: 'e3Xnhny1706589599129',\n    secretKey: '108 112 98 96 104 109 112 99 98 109 79 112 80 91 111 108 36 112 51 114 99 114 68 102 82 67 50 106 89 102 74 83'\n  },\n  switch: localSwitch\n}\n\nexport default config\n", "import { baseKey, functionSwitch } from '@/config/game/defaultConfig'\n\nconst localKey = Object.create(baseKey)\nlocalKey.tokenName = 'token_name_dc'\nlocalKey.howToUseDiamond = 'how-to-use-diamond_dc'\nconst localSwitch = Object.create(functionSwitch)\n\nlocalSwitch.boon = false\nlocalSwitch.ckoCheckedByDefault = true\n\nconst config = {\n  gameinfo: {\n    gameProject: 'dcdl_global',\n    gameCode: 'DC',\n    gameName: 'King of Avalon',\n    gameId: '888888',\n    defaultDiscount: false,\n    whiteChannel: [],\n    blackChannel: [],\n    greyChannel: []\n  },\n  langKey: localKey,\n  images: {\n    whatsDiamond: [\n      { imageUrl: require('@/assets/dc/diamondGallery/01.png'), jumpUrl: '' }\n    ],\n    uidTips: require('@/assets/dc/login/uid-tips.jpeg'),\n    loganFindValidationCode: require('@/assets/dc/login/login-validate-game-scrrencut.jpeg')\n  },\n  ids: {\n    gid: 'G-3FME4BZMSF',\n    appId: '7HjTjCy1739172217783',\n    secretKey: '101 100 101 109 96 104 109 112 99 98 109 112 80 91 111 108 36 112 51 114 99 114 68 102 82 67 50 106 89 102 74 83',\n    loginValidationExpireCount: 15\n  },\n  switch: localSwitch\n}\n\nexport default config\n", "import { baseKey, functionSwitch } from '@/config/game/defaultConfig'\n\nconst localKey = Object.create(baseKey)\nlocalKey.tokenName = 'token_name'\nlocalKey.loginPlaceHolder = 'please-input-uid'\nlocalKey.pageTitle = 'ss-official-website-title'\nlocalKey.whatIsDiamondTitle = 'top-diamond-tips'\nlocalKey.customDiamondLimitTips = 'custom-diamond-limit-ss'\n\nconst localSwitch = Object.create(functionSwitch)\n\n// localSwitch.boon = false\nlocalSwitch.loginValidation = true\nlocalSwitch.smallDiamondDoubleDiscount = true\nlocalSwitch.fixedDiscountType = 'fixed_rebate'\n\nconst config = {\n  gameinfo: {\n    gameProject: 'ss_global',\n    gameCode: 'SSV', // 控制api\n    // gameName: 'King of Avalon',\n    gameId: '30001',\n    // whiteChannel: ['ss_funplus_pc_vshoot'],\n    blackChannel: [],\n    greyChannel: []\n  },\n  apiParams: {\n    boonAme: {\n      projectId: 21,\n      loginAction: 1114,\n      getLoginReward: 1115,\n      pwaOpenAction: 1116,\n      getPwaReward: 1117\n    }\n  },\n  langKey: localKey,\n  images: {\n    whatsDiamond: [\n      { imageUrl: require('@/assets/ss/diamondGallery/01.png'), jumpUrl: '' },\n      { imageUrl: require('@/assets/ss/diamondGallery/02.png'), jumpUrl: '' },\n      { imageUrl: require('@/assets/ss/diamondGallery/03.png'), jumpUrl: '' }\n    ],\n    uidTips: require('@/assets/ss/login/ss_uid.png'),\n    loganFindValidationCode: require('@/assets/ss/login/login-validate-game-scrrencut.jpeg')\n  },\n  ids: {\n    // gid: 'G-LB15MXTLZK',\n    // appId: 'e3Xnhny1706589599129',\n    secretKey: '116 116 96 104 109 112 99 98 109 79 79 112 80 91 111 108 36 112 51 114 99 114 68 102 82 67 50 106 89 102 74 83',\n    minCustomDiamondNum: 10,\n    boonPopName: 'SSVBoonPop'\n  },\n  switch: localSwitch\n}\n\nexport default config\n", "import { baseKey, functionSwitch } from '@/config/game/defaultConfig'\n\nconst localKey = Object.create(baseKey)\nlocalKey.tokenName = 'token_name'\nlocalKey.loginPlaceHolder = 'please-input-uid'\nlocalKey.pageTitle = 'ssv2-official-website-title'\nlocalKey.customDiamondLimitTips = 'custom-diamond-limit-ss'\n\nconst localSwitch = Object.create(functionSwitch)\n\nlocalSwitch.loginValidation = true\nlocalSwitch.smallDiamondDoubleDiscount = true // 开启小档位\nlocalSwitch.showPopPolicy = true\nlocalSwitch.showMobilePolicy = false\nlocalSwitch.fixedDiscountType = 'fixed_rebate'\nlocalSwitch.showPcDiscountTips = true\nlocalSwitch.ckoCheckedByDefault = true\n\nconst config = {\n  gameinfo: {\n    gameProject: 'ssv2_global',\n    gameCode: 'SSV2', // 控制api\n    gameId: '8519',\n    whiteChannel: ['ss_googleplay_material_parkour21'],\n    blackChannel: [],\n    greyChannel: [],\n    mainBody: 'puzala', // 所属主体 funplus/puzala 默认是funplus\n    sortName: 'Front War'\n  },\n  apiParams: {\n    boonAme: {\n      projectId: 158,\n      loginAction: 2646,\n      getLoginReward: 2647,\n      pwaOpenAction: 2648,\n      getPwaReward: 2649\n    },\n    smallDiamondDiscount: {\n      p1: 158,\n      p2: 2654,\n    },\n    biPath: '/sdk/bilog'\n  },\n  langKey: localKey,\n  images: {\n    whatsDiamond: [\n      { imageUrl: require('@/assets/ss/diamondGallery/ssv-01.jpeg'), jumpUrl: '' }\n    ],\n    uidTips: require('@/assets/ss/ssv2/find_ss_uid.jpeg'),\n    loganFindValidationCode: require('@/assets/ss/ssv2/login/login-validate-game-scrrencut.png')\n  },\n  ids: {\n    gid: 'G-RWBD2X9CPK',\n    appId: 'DAk5Rea1745289938511',\n    secretKey: '116 116 119 51 96 104 109 112 99 98 109 112 80 91 111 108 36 112 51 114 99 114 68 102 82 67 50 106 89 102 74 83', //ssv2\n    minCustomDiamondNum: 10,\n    boonPopName: 'SSVBoonPop',\n    loginValidationExpireCount: 15,\n    minimumDiamondId: 'com.pc.miamond0' // 如果smallDiamondDoubleDiscount生效，最小购买钻石没有固定返钻\n  },\n  switch: localSwitch\n}\n\nexport default config\n", "import { baseKey, functionSwitch } from '@/config/game/defaultConfig'\n\nconst localKey = Object.create(baseKey)\nlocalKey.tokenName = 'token_name_ssd'\nlocalKey.loginPlaceHolder = 'please-input-uid_ssd'\nlocalKey.pageTitle = 'ssd-official-website-title'\nlocalKey.howToUseDiamond = 'how-to-use-diamond-ssd'\n\nconst localSwitch = Object.create(functionSwitch)\n\nlocalSwitch.loginValidation = true\nlocalSwitch.showPopPolicy = true\nlocalSwitch.showMobilePolicy = true\nlocalSwitch.enableAnimation = true\nlocalSwitch.useThemeFile = true\n\nconst config = {\n  gameinfo: {\n    gameProject: 'ts_global',\n    gameCode: 'SSD', // 控制api\n    gameId: '8612',\n    whiteChannel: ['ts_googleplay_global', 'ts_ios_global'],\n    blackChannel: [],\n    greyChannel: [],\n    sortName: 'TS'\n  },\n  apiParams: {\n    boonAme: {\n      projectId: 168,\n      loginAction: 2743,\n      getLoginReward: 2744,\n      pwaOpenAction: 2745,\n      getPwaReward: 2746\n    }\n  },\n  langKey: localKey,\n  images: {\n    whatsDiamond: [\n      { imageUrl: require('@/assets/ss/ssd/diamondGallery/ssd-01.jpeg'), jumpUrl: '' }\n    ],\n    uidTips: require('@/assets/ss/ssd/find_ss_uid.jpeg'),\n    loganFindValidationCode: require('@/assets/ss/ssd/login/login-validate-game-scrrencut.jpeg')\n  },\n  ids: {\n    gid: 'G-RWBD2X9CPK',\n    appId: 'DAk5Rea1745289938511',\n    secretKey: '117 116 96 104 109 112 99 98 109 79 79 112 80 91 111 108 36 112 51 114 99 114 68 102 82 67 50 106 89 102 74 83',\n    boonPopName: 'SSVBoonPop',\n    loginValidationExpireCount: 15\n  },\n  switch: localSwitch\n}\n\nexport default config\n", "import { baseKey, functionSwitch } from '@/config/game/defaultConfig'\n\nconst localKey = Object.create(baseKey)\nlocalKey.tokenName = 'token_name_fnd'\nlocalKey.whatIsDiamondTitle = 'fnd-top-diamond-tips'\nlocalKey.loginPlaceHolder = 'please-input-uid_fnd'\nlocalKey.howToUseDiamond = 'how-to-use-diamond_fnd'\nlocalKey.pageTitle = 'fnd-official-website-title'\nconst localSwitch = Object.create(functionSwitch)\n\nlocalSwitch.boon = false\nlocalSwitch.loginValidation = false\nlocalSwitch.useThemeFile = true\nlocalSwitch.enableAnimation = true\nlocalSwitch.ckoCheckedByDefault = true\n\nconst config = {\n  gameinfo: {\n    gameProject: 'foundation_global',\n    gameCode: 'FOUNDATION',\n    // gameName: 'King of Avalon',\n    gameId: '999999',\n    defaultDiscount: false,\n    whiteChannel: [],\n    blackChannel: [],\n    greyChannel: []\n  },\n  langKey: localKey,\n  images: {\n    whatsDiamond: [\n      { imageUrl: require('@/assets/foundation/diamondGallery/01.jpeg'), jumpUrl: '' }\n    ],\n    uidTips: require('@/assets/foundation/login/uid-tips.jpeg')\n  },\n  ids: {\n    gid: 'G-3FME4BZMSF',\n    appId: '7HjTjCy1739172217783',\n    secretKey: '103 112 118 111 101 98 117 106 112 111 96 104 109 112 99 98 109 112 51 114 99 114 68 102 82 67 50 106 89 102 74 83'\n  },\n  switch: localSwitch\n}\n\nexport default config\n", "import { baseKey, functionSwitch } from '@/config/game/defaultConfig'\n\nconst localKey = Object.create(baseKey)\nlocalKey.pageTitle = 'ss-official-website-title'\n\nconst localSwitch = Object.create(functionSwitch)\n\n// localSwitch.fixedDiscountType = 'fixed_rebate'\nlocalSwitch.useThemeFile = true\nlocalSwitch.enableAnimation = true\n\nconst config = {\n  gameinfo: {\n    gameProject: 'ss_global',\n    gameCode: 'SSCP',\n    gameLogCode: 'ss',\n    gameId: '30001',\n    blackChannel: [],\n    greyChannel: []\n  },\n  apiParams: {},\n  langKey: localKey,\n  images: {},\n  ids: {\n    secretKey: '116 116 96 104 109 112 99 98 109 79 79 112 80 91 111 108 36 112 51 114 99 114 68 102 82 67 50 106 89 102 74 83',\n  },\n  switch: localSwitch\n}\n\nexport default config\n", "import { baseKey, functionSwitch } from '@/config/game/defaultConfig'\n\nconst localKey = Object.create(baseKey)\nlocalKey.pageTitle = 'st-official-website-title'\n\nconst localSwitch = Object.create(functionSwitch)\n\n// localSwitch.fixedDiscountType = 'fixed_rebate'\nlocalSwitch.useThemeFile = true\nlocalSwitch.enableAnimation = true\n\nconst config = {\n  gameinfo: {\n    gameProject: 'st_global',\n    gameCode: 'STCP',\n    gameLogCode: 'st',\n    gameId: '2202',\n    blackChannel: [],\n    greyChannel: []\n  },\n  apiParams: {},\n  langKey: localKey,\n  images: {},\n  ids: {\n    secretKey: '116 117 96 104 109 112 99 98 109 79 79 112 80 91 111 108 36 112 51 114 99 114 68 102 82 67 50 106 89 102 74 83',\n  },\n  switch: localSwitch\n}\n\nexport default config", "import { baseKey, functionSwitch } from '@/config/game/defaultConfig'\n\nconst localKey = Object.create(baseKey)\nlocalKey.pageTitle = 'mc-official-website-title'\n\nconst localSwitch = Object.create(functionSwitch)\n\n// localSwitch.fixedDiscountType = 'fixed_discount'\nlocalSwitch.useThemeFile = true\nlocalSwitch.enableAnimation = true\n\n// 开启切换返钻\nlocalSwitch.switchRebate = false\n\nconst config = {\n  gameinfo: {\n    gameProject: 'mc_global',\n    gameCode: 'MCCP',\n    gameLogCode: 'mc',\n    gameId: '2200',\n    blackChannel: [],\n    greyChannel: []\n  },\n  apiParams: {},\n  langKey: localKey,\n  images: {},\n  ids: {\n    secretKey: '110 100 96 104 109 112 99 98 109 79 79 112 80 91 111 108 36 112 51 114 99 114 68 102 82 67 50 106 89 102 74 83',\n  },\n  switch: localSwitch\n}\n\nexport default config", "import { baseKey, functionSwitch } from '@/config/game/defaultConfig'\n\nconst localKey = Object.create(baseKey)\nlocalKey.pageTitle = 'gog-official-website-title'\n\nconst localSwitch = Object.create(functionSwitch)\n\n// localSwitch.fixedDiscountType = 'fixed_discount'\nlocalSwitch.useThemeFile = true\nlocalSwitch.enableAnimation = true\n\n// 开启切换返钻\nlocalSwitch.switchRebate = false\n\nconst config = {\n  gameinfo: {\n    gameProject: 'gog_global',\n    gameCode: 'GOGCP',\n    gameLogCode: 'gog',\n    gameId: '2064',\n    blackChannel: [],\n    greyChannel: []\n  },\n  apiParams: {},\n  langKey: localKey,\n  images: {},\n  ids: {\n    secretKey: '104 112 104 96 104 109 112 99 98 109 79 112 80 91 111 108 36 112 51 114 99 114 68 102 82 67 50 106 89 102 74 83',\n  },\n  switch: localSwitch\n}\n\nexport default config", "import { baseKey, functionSwitch } from '@/config/game/defaultConfig'\n\nconst localKey = Object.create(baseKey)\nlocalKey.pageTitle = 'rom-official-website-title'\n\nconst localSwitch = Object.create(functionSwitch)\nlocalSwitch.useThemeFile = true\nlocalSwitch.enableAnimation = false\n\nconst config = {\n  gameinfo: {\n    gameProject: 'koa_global',\n    gameCode: 'ROMCP',\n    gameLogCode: 'koa',\n    gameId: '2031',\n    whiteChannel: [],\n    blackChannel: [],\n    greyChannel: []\n  },\n  apiParams: {},\n  langKey: localKey,\n  images: {},\n  ids: {\n    gid: 'G-LT6WCT52Q5',\n    appId: 'RbWazjW1744102094299',\n    secretKey: '108 112 98 96 104 109 112 99 98 109 79 112 80 91 111 108 36 112 51 114 99 114 68 102 82 67 50 106 89 102 74 83'\n  },\n  switch: localSwitch\n}\n\nexport default config\n", "const context = require.context('.', false, /.*RP\\.js$/)\nconst modules = context.keys().reduce((acc, key) => {\n  const moduleName = key.replace(/^\\.\\/(.*)RP\\.js$/, '$1RP')\n  acc[moduleName] = context(key).default || context(key)\n  return acc\n}, {})\nexport default modules\n", "import koa from './game/koa'\nimport aof from './game/aof'\nimport rom from './game/rom'\nimport koaCn from './game/koaCn'\nimport dc from './game/dc'\nimport ssv from './game/ss/ssv'\nimport ssv2 from './game/ss/ssv2'\nimport ssd from './game/ss/ssd'\nimport foundation from './game/foundation'\n\nimport ssCP from './game/ss/ssCP'\nimport stCP from './game/stCP'\nimport mcCP from './game/mcCP'\nimport gogCP from './game/gogCP'\nimport romCP from './game/romCP'\n\nimport rp from './game/rp/index'\n\nconst sourceFun = {\n  koa,\n  aof,\n  rom,\n  koaCn,\n  dc,\n  ssv,\n  ssv2,\n  foundation,\n  ssCP,\n  stCP,\n  mcCP,\n  gogCP,\n  romCP,\n  ssd,\n  ...rp\n}\nconst currentGame = window.__GAMENAME\n\nfunction loadResource () {\n  const content = sourceFun[currentGame]\n  if (!content) console.error(`${currentGame} 资源配置文件未注册`)\n  return function (key, defaultValue) {\n    if (key.includes('.')) {\n      const [key1, key2] = key.split('.')\n      return (content[key1] && content[key1][key2]) || defaultValue\n    }\n    return content[key] || defaultValue || {}\n  }\n}\nexport default loadResource()\n", "import Vue from 'vue'\nimport i18n from '@/utils/i18n'\nimport getContentByKey from '@/config/resConfig'\n\nwindow.$gcbk = getContentByKey\nVue.prototype.$gcbk = getContentByKey\nVue.prototype.$vt = (key) => i18n.t(getContentByKey(`langKey.${key}`)) // 变量 $vt\nVue.prototype.$imageLoader = (key, defaultValue) => getContentByKey(`images.${key}`, defaultValue)\nVue.prototype.$idLoader = window.$idLoader = (key, defaultValue) => getContentByKey(`ids.${key}`, defaultValue)\nVue.prototype.$gameName = window.__GAMENAME\n", "import ReportCustom from '@ufe/reporter'\nimport Vue from 'vue'\nif (process.env.NODE_ENV !== 'development') {\n  // eslint-disable-next-line no-new\n  new ReportCustom({\n    domain: 'https://web-monitor.funplus.com',\n    appId: window.$idLoader('appId'),\n    autoErr: true,\n    vueError: Vue,\n    useBeaconFirst: true,\n    extra: {\n      appVersion: '0.0.2'\n    }\n  })\n}\n", "import store from '@/store'\nfunction flexible (window, document) {\n  const baseSize = 37.7\n\n  const windowSize = window.innerWidth\n  if (windowSize < 576) {\n    const scale = windowSize / 750\n    document.documentElement.style.fontSize = `${baseSize * scale}px`\n  } else if (windowSize > 940) {\n    document.documentElement.style.fontSize = '37.5px'\n  } else {\n    document.documentElement.style.fontSize = '25px'\n  }\n\n  store.commit('resetIsXXX')\n}\nflexible(window, document)\n\nlet interval\nwindow.addEventListener('resize', () => {\n  const reload = () => flexible(window, document)\n  if (interval) clearTimeout(interval)\n  interval = setTimeout(reload, 100)\n})\n", "import { getUrlParams } from '@/utils/utils'\n\n// eslint-disable-next-line no-extend-native\nArray.prototype._hasUnion = function (arr = []) {\n  if (!Array.isArray(arr)) throw Error('params `arr` must be Array!')\n  return [...this].filter(x => arr.includes(x)).length\n}\n\ngetUrlParams()\n", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{class:[_vm.$gameName],attrs:{\"id\":\"app\"},on:{\"click\":function($event){return _vm.$root.$emit('BodyClick')}}},[_c('keep-alive',{attrs:{\"include\":\"Pay\"}},[(_vm.showPage && _vm.isExtraInfoInit)?_c('router-view',{key:_vm.$route.fullPath}):_vm._e()],1),_c('PopHome')],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('section',[_c('transition',{attrs:{\"name\":\"bg\"}},[(_vm.showOuter)?_c('div',{staticClass:\"pop-bg\"}):_vm._e()]),_c('transition',{attrs:{\"name\":\"cmp\"}},[(_vm.cmpName && _vm.showInner)?_c(_vm.cmpName,{tag:\"component\",attrs:{\"option\":_vm.option}}):_vm._e()],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[_c('transition',{attrs:{\"name\":\"pop\"}},[_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.popupShow),expression:\"popupShow\"}],staticClass:\"popup\"},[_c('img',{staticClass:\"popup-image\",attrs:{\"src\":_vm.popupSrc},on:{\"click\":_vm.jumpPage}}),_c('button',{staticClass:\"popup-close\",on:{\"click\":_vm.closePopup}})])])],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <div>\n    <transition name=\"pop\">\n      <div class=\"popup\" v-show=\"popupShow\">\n        <img class=\"popup-image\" :src=\"popupSrc\" @click=\"jumpPage\" />\n        <button class=\"popup-close\" @click=\"closePopup\"></button>\n      </div>\n    </transition>\n  </div>\n</template>\n\n<script>\nimport { getAmeDo } from \"@/server\";\nexport default {\n  name: \"BackendPopup\",\n  props: {\n    option: Object,\n  },\n  data(){\n    return {\n      game: this.$store.state.gameinfo.gameProject.split('_')[0],\n      gameEnv: \"master\",\n      popupShow: false,\n      popupList: [],\n      index: 0,\n      ymd: \"\",\n      lang: \"\",\n      popupSrc: \"\",\n      jumpLink: \"\",\n    };\n  },\n  methods: {\n    changePopup() {\n      this.popupShow = false;\n      let curPopup = this.popupList[this.index];\n      let curPopupSrc = \"\";\n      let curJumpLink = \"\";\n    //   if (curPopup.mode == 1 && localStorage.getItem(`${this.game}_${curPopup.id}`) === this.ymd) {\n    //     this.closePopup();\n    //     return;\n    //   }\n      if (curPopup.category == 1) {\n        if (!curPopup.file_details[this.option.country]) {\n          this.closePopup();\n          return;\n        } else curPopupSrc = curPopup.file_details[this.option.country];\n      } else {\n        curPopupSrc = curPopup.file_details[this.$i18n.locale || this.lang] || curPopup.file_details.en;\n      }\n      if (!curPopupSrc) {\n        this.closePopup();\n        return;\n      }\n      curJumpLink = curPopup.jump_link;\n      localStorage.setItem(`${this.game}_${curPopup.id}`, this.ymd);\n      setTimeout(() => {\n        this.popupSrc = curPopupSrc;\n        this.jumpLink = curJumpLink;\n        this.popupShow = true;\n      }, 350);\n    },\n    closePopup() {\n      this.index++;\n      if (this.index >= this.popupList.length) {\n        this.popupShow = false\n        setTimeout(() => this.$root.$emit(\"closePop\"), 350)\n      }\n      else this.changePopup();\n    },\n    jumpPage() {\n      let finalUrl = \"\";\n      if (this.jumpLink) {\n        if (this.jumpLink.split(\"?\").length >= 2) {\n          finalUrl = `${this.jumpLink}&openid=${encodeURIComponent(localStorage.getItem(\"openid\") || \"\")}&l=${this.$i18n.locale || this.lang}`;\n        } else {\n          finalUrl = `${this.jumpLink}?openid=${encodeURIComponent(localStorage.getItem(\"openid\") || \"\")}&l=${this.$i18n.locale || this.lang}`;\n        }\n        window.open(finalUrl, \"_blank\");\n      }\n    },\n  },\n  created() {\n    const broswerLang = navigator.language || navigator.userLanguage;\n    if (broswerLang.toLowerCase() === \"zh-tw\") this.lang = \"zh_tw\";\n    else if (broswerLang.startsWith(\"zh\")) this.lang = \"zh_cn\";\n    else this.lang = broswerLang.split(\"-\")[0];\n\n    const date = new Date(Date.now() + new Date().getTimezoneOffset() * 60 * 1000);\n    this.ymd = `${date.getFullYear()}/${date.getMonth() + 1}/${date.getDate()}`;\n\n    // const params = Object.assign({\n    //     p0: 'web',\n    //     p1: 9,\n    //     p2: '1653',\n    //     p3: 'api',\n    //     game: this.game\n    // }, process.env.VUE_APP_PROD_ENV === 'ONLINE' ? {} : { gameEnv: this.gameEnv })\n    // getAmeDo(params).then((response) => {\n    //     if (response.code === 0) {\n    //         this.popupList = response.data\n    //         if (this.popupList.length) this.changePopup()\n    //         else this.closePopup()\n    //         for (let i = 0; i < this.popupList.length; i++) {\n    //             const image = new Image()\n    //             image.src = this.popupList[i].category == 1\n    //                 ? this.popupList[i].file_details[this.option.country]\n    //                 : this.popupList[i].file_details[this.$i18n.locale || this.lang]\n    //         }\n    //     }\n    // })\n    this.popupList = this.option.popupList;\n    if (this.popupList.length) this.changePopup();\n    else this.closePopup();\n    for (let i = 0; i < this.popupList.length; i++) {\n      const image = new Image();\n      image.src =\n        this.popupList[i].category == 1\n          ? this.popupList[i].file_details[this.option.country]\n          : this.popupList[i].file_details[this.$i18n.locale || this.lang];\n    }\n  },\n};\n</script>\n\n<style scoped lang=\"scss\">\n@use \"~@/utils/utils.scss\" as utils;\n\n.popup {\n  z-index: 100001;\n  position: fixed;\n  left: 50%;\n  top: 50%;\n  transform: translate(-50%, -50%);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.popup-image {\n  width: 550px;\n  border-radius: 5px;\n  cursor: pointer;\n  transition: all 0.5s;\n\n  @include utils.setPcContent {\n    width: 500PX;\n  }\n}\n\n.popup-close {\n  width: 54px;\n  height: 54px;\n  margin-top: 30px;\n  background: none;\n  background-image: url(\"~@/assets/common/pop/close-popup.png\");\n  background-size: cover;\n  outline: none;\n  border: 0;\n  cursor: pointer;\n\n  @include utils.setPcContent {\n    width: 45px;\n    height: 45px;\n  }\n\n  &:active {\n    transform: scale(0.95);\n    -webkit-filter: brightness(0.85);\n    filter: brightness(0.85);\n  }\n}\n\n.pop-enter-active,\n.pop-leave-active {\n  transition: all 0.3s;\n}\n\n.pop-enter {\n  opacity: 0;\n}\n\n.pop-leave-to {\n  opacity: 0;\n}\n</style>\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./BackendPopup.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./BackendPopup.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./BackendPopup.vue?vue&type=template&id=cc5e4232&scoped=true\"\nimport script from \"./BackendPopup.vue?vue&type=script&lang=js\"\nexport * from \"./BackendPopup.vue?vue&type=script&lang=js\"\nimport style0 from \"./BackendPopup.vue?vue&type=style&index=0&id=cc5e4232&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"cc5e4232\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('container',{class:['faq-wrapper', _vm.$gameName],attrs:{\"title\":_vm.$t('construction_faq'),\"id\":\"faq-wrapper\"}},[_c('div',{staticClass:\"construction\",class:[_vm.$gameName]},[_c('div',{staticClass:\"faq-wrap\"},[(_vm.$gameName === 'koa')?_vm._l((new Array(7).fill('')),function(item,k){return _c('div',{key:k,staticClass:\"faq-list\"},[_vm._v(\" \"+_vm._s(_vm.$t(`koa_construction_faq_q${k + 1}`))+\" \"),_c('br'),_c('div',[_vm._l((_vm.$t(`koa_construction_faq_q${k + 1}a`).split('<br/>')),function(item,i){return [(k === 2)?[(item.indexOf('{0}') > -1)?[_vm._v(\" \"+_vm._s(item.split('{0}')[0])),_c('a',{key:i,attrs:{\"target\":\"__blank\",\"href\":\"https://koa.kingsgroupgames.com/pc/activity\"}},[_vm._v(\"https://koa.kingsgroupgames.com/pc/activity\")]),_vm._v(_vm._s(item.split('{0}')[1])+\" \")]:(item.indexOf('{1}') > -1)?[_vm._v(\" \"+_vm._s(item.split('{1}')[0])),_c('a',{key:i,attrs:{\"target\":\"__blank\",\"href\":\"https://koa.kingsgroupgames.com\"}},[_vm._v(\"https://koa.kingsgroupgames.com\")]),_vm._v(_vm._s(item.split('{1}')[1])+\" \")]:[_vm._v(_vm._s(item))],_c('br',{key:`${i}_${k}`})]:(k===4)?[_vm._v(_vm._s(item)+\" \"),_c('br',{key:i + '5a1'}),_vm._v(\" \"+_vm._s(_vm.$t('koa_construction_faq_q5a1'))+\" \"),_c('br',{key:i})]:[_vm._v(_vm._s(item)+\" \"),_c('br',{key:i})]]})],2)])}):(_vm.$gameName === 'aof' || _vm.$gameName === 'rom')?_vm._l((new Array(7).fill('')),function(item,k){return _c('div',{key:k,staticClass:\"faq-list\"},[_vm._v(\" \"+_vm._s(_vm.$t(`${_vm.$store.state.gameinfo.game}_construction_faq_q${k + 1}`))+\" \"),_c('br'),_c('div',[_vm._l((_vm.$t(`${_vm.$store.state.gameinfo.game}_construction_faq_q${k + 1}a`).split('<br/>')),function(item,i){return [(k === 2)?[(item.indexOf('{0}') > -1)?[_vm._v(\" \"+_vm._s(item.split('{0}')[0])),_c('a',{key:i,attrs:{\"target\":\"__blank\",\"href\":\"https://koa.kingsgroupgames.com/pc/activity\"}},[_vm._v(\"https://koa.kingsgroupgames.com/pc/activity\")]),_vm._v(_vm._s(item.split('{0}')[1])+\" \")]:(item.indexOf('{1}') > -1)?[_vm._v(\" \"+_vm._s(item.split('{1}')[0])),_c('a',{key:i,attrs:{\"target\":\"__blank\",\"href\":\"https://koa.kingsgroupgames.com\"}},[_vm._v(\"https://koa.kingsgroupgames.com\")]),_vm._v(_vm._s(item.split('{1}')[1])+\" \")]:[_vm._v(_vm._s(item))],_c('br',{key:`${i}_${k}`})]:(k===4)?[_vm._v(_vm._s(item)+\" \"),_c('br',{key:i + '5a1'}),_vm._v(\" \"+_vm._s(_vm.$t(`${_vm.$store.state.gameinfo.game}_construction_faq_q5a1`))+\" \"),_c('br',{key:i})]:[_vm._v(_vm._s(item)+\" \"),_c('br',{key:i})]]})],2)])}):(_vm.$gameName === 'dc')?_vm._l((new Array(6).fill('')),function(item,k){return _c('div',{key:k,staticClass:\"faq-list\"},[_vm._v(\" \"+_vm._s(_vm.$t(`${_vm.$store.state.gameinfo.game}_construction_faq_q${k + 1}`))+\" \"),_c('br'),_c('div',[_vm._l((_vm.$t(`${_vm.$store.state.gameinfo.game}_construction_faq_q${k + 1}a`).split('<br/>')),function(item,i){return [(k===2)?[_vm._v(_vm._s(item)+\" \"),_c('br',{key:i + '2a1'}),_vm._v(\" \"+_vm._s(_vm.$t(`${_vm.$store.state.gameinfo.game}_construction_faq_q3a1`))+\" \"),_c('br',{key:i})]:[_vm._v(_vm._s(item)+\" \"),_c('br',{key:i})]]})],2)])}):(_vm.$gameName === 'ssv')?[_vm._l((new Array(9).fill('')),function(item,k){return _c('div',{key:k,staticClass:\"faq-list\"},[_vm._v(\" \"+_vm._s(_vm.$t(`construction_faq_q${k + 1}`))+\" \"),_c('br'),_c('div',[_vm._l((_vm.$t(`construction_faq_q${k + 1}a`).split('<br/>')),function(item,i){return [(k === 3)?[(item.indexOf('{0}') > -1)?[_vm._v(\" \"+_vm._s(item.split('{0}')[0])),_c('a',{key:i,attrs:{\"target\":\"__blank\",\"href\":\"https://stateofsurvival.game/pc/activity\"}},[_vm._v(\"https://stateofsurvival.game/pc/activity\")]),_vm._v(_vm._s(item.split('{0}')[1])+\" \")]:(item.indexOf('{1}') > -1)?[_vm._v(\" \"+_vm._s(item.split('{1}')[0])),_c('a',{key:i,attrs:{\"target\":\"__blank\",\"href\":\"https://stateofsurvival.game/\"}},[_vm._v(\"https://stateofsurvival.game/\")]),_vm._v(_vm._s(item.split('{1}')[1])+\" \")]:[_vm._v(_vm._s(item))],_c('br',{key:`${i}_${k}`})]:(k===4)?[_vm._v(_vm._s(item)+\" \"),_c('br',{key:i + '5a1'}),_vm._v(\" \"+_vm._s(_vm.$t(`construction_faq_q5a1`))+\" \"),_c('br',{key:i})]:[_vm._v(_vm._s(item)+\" \"),_c('br',{key:i})]]})],2)])}),_c('div',{key:\"10\",staticClass:\"faq-list\"},[_vm._v(\" \"+_vm._s(_vm.$t('construction_faq_q10'))+\" \"),_c('br'),_c('div',[_vm._v(\" \"+_vm._s(_vm.$t('construction_faq_q10a'))+\" \")])])]:(_vm.$gameName === 'ssv2')?_vm._l((new Array(6).fill('')),function(item,k){return _c('div',{key:k,staticClass:\"faq-list\"},[_vm._v(\" \"+_vm._s(_vm.$t(`${_vm.prefix}${k + 1}`))+\" \"),_c('br'),_c('div',[_vm._l((_vm.$t(`${_vm.prefix}${k + 1}a`).split('<br/>')),function(item,i){return [(k===2)?[_vm._v(_vm._s(item)+\" \"),_c('br',{key:i + '5a1'}),_vm._v(\" \"+_vm._s(_vm.$t(`${_vm.prefix}3a1`))+\" \"),_c('br',{key:i})]:[_vm._v(_vm._s(item)+\" \"),_c('br',{key:i})]]})],2)])}):(_vm.$gameName === 'foundation')?_vm._l((new Array(6).fill('')),function(item,k){return _c('div',{key:k,staticClass:\"faq-list\"},[_vm._v(\" \"+_vm._s(_vm.$t(`fnd_construction_faq_q${k + 1}`))+\" \"),_c('br'),_c('div',[_vm._l((_vm.$t(`fnd_construction_faq_q${k + 1}a`).split('<br/>')),function(item,i){return [(k===3)?[_vm._v(_vm._s(item)+\" \"),_c('br',{key:i + '3a1'}),_vm._v(\" \"+_vm._s(_vm.$t(`fnd_construction_faq_q3a1`))+\" \"),_c('br',{key:i})]:[_vm._v(_vm._s(item)+\" \"),_c('br',{key:i})]]})],2)])}):(_vm.$gameName === 'ssd')?_vm._l((new Array(6).fill('')),function(item,k){return _c('div',{key:k,staticClass:\"faq-list\"},[_vm._v(\" \"+_vm._s(_vm.$t(`ssd_construction_faq_q${k + 1}`))+\" \"),_c('br'),_c('div',[_vm._l((_vm.$t(`ssd_construction_faq_q${k + 1}a`).split('<br/>')),function(item,i){return [(k===2)?[_vm._v(_vm._s(item)+\" \"),_c('br',{key:i + '3a1'}),_vm._v(\" \"+_vm._s(_vm.$t(`ssd_construction_faq_q3a1`))+\" \"),_c('br',{key:i})]:[_vm._v(_vm._s(item)+\" \"),_c('br',{key:i})]]})],2)])}):_vm._l((new Array(6).fill('')),function(item,k){return _c('div',{key:k,staticClass:\"faq-list\"},[_vm._v(\" \"+_vm._s(_vm.$t(`${_vm.$store.state.gameinfo.game}_construction_faq_q${k + 1}`))+\" \"),_c('br'),_c('div',[_vm._l((_vm.$t(`${_vm.$store.state.gameinfo.game}_construction_faq_q${k + 1}a`).split('<br/>')),function(item,i){return [(k===2)?[_vm._v(_vm._s(item)+\" \"),_c('br',{key:i + '2a1'}),_vm._v(\" \"+_vm._s(_vm.$t(`${_vm.$store.state.gameinfo.game}_construction_faq_q3a1`))+\" \"),_c('br',{key:i})]:[_vm._v(_vm._s(item)+\" \"),_c('br',{key:i})]]})],2)])})],2)]),_c('div',{staticClass:\"mask\"})])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <container :title=\"$t('construction_faq')\" :class=\"['faq-wrapper', $gameName]\" id=\"faq-wrapper\">\n    <div class=\"construction\" :class=\"[$gameName]\">\n      <div class=\"faq-wrap\">\n        <template v-if=\"$gameName === 'koa'\">\n          <div class=\"faq-list\" v-for=\"(item, k) in new Array(7).fill('')\" :key=\"k\">\n            {{ $t(`koa_construction_faq_q${k + 1}`) }}\n            <br>\n            <div>\n              <template v-for=\"(item, i) in $t(`koa_construction_faq_q${k + 1}a`).split('<br/>')\">\n                <template v-if=\"k === 2\">\n                  <template v-if=\"item.indexOf('{0}') > -1\">\n                    {{ item.split('{0}')[0] }}<a target=\"__blank\" href=\"https://koa.kingsgroupgames.com/pc/activity\" :key=\"i\">https://koa.kingsgroupgames.com/pc/activity</a>{{ item.split('{0}')[1] }}\n                  </template>\n                  <template v-else-if=\"item.indexOf('{1}') > -1\">\n                    {{ item.split('{1}')[0] }}<a target=\"__blank\" href=\"https://koa.kingsgroupgames.com\" :key=\"i\">https://koa.kingsgroupgames.com</a>{{ item.split('{1}')[1] }}\n                  </template>\n                  <template v-else>{{ item }}</template>\n                  <br :key=\"`${i}_${k}`\">\n                </template>\n                <template v-else-if=\"k===4\">{{ item }} <br :key=\"i + '5a1'\"> {{$t('koa_construction_faq_q5a1')}} <br :key=\"i\"></template>\n                <template v-else>{{ item }} <br :key=\"i\"></template>\n              </template>\n            </div>\n          </div>\n        </template>\n        <template v-else-if=\"$gameName === 'aof' || $gameName === 'rom'\">\n          <div class=\"faq-list\" v-for=\"(item, k) in new Array(7).fill('')\" :key=\"k\">\n            {{ $t(`${$store.state.gameinfo.game}_construction_faq_q${k + 1}`) }}\n            <br>\n            <div>\n              <template v-for=\"(item, i) in $t(`${$store.state.gameinfo.game}_construction_faq_q${k + 1}a`).split('<br/>')\">\n                <template v-if=\"k === 2\">\n                  <template v-if=\"item.indexOf('{0}') > -1\">\n                    {{ item.split('{0}')[0] }}<a target=\"__blank\" href=\"https://koa.kingsgroupgames.com/pc/activity\" :key=\"i\">https://koa.kingsgroupgames.com/pc/activity</a>{{ item.split('{0}')[1] }}\n                  </template>\n                  <template v-else-if=\"item.indexOf('{1}') > -1\">\n                    {{ item.split('{1}')[0] }}<a target=\"__blank\" href=\"https://koa.kingsgroupgames.com\" :key=\"i\">https://koa.kingsgroupgames.com</a>{{ item.split('{1}')[1] }}\n                  </template>\n                  <template v-else>{{ item }}</template>\n                  <br :key=\"`${i}_${k}`\">\n                </template>\n                <template v-else-if=\"k===4\">{{ item }} <br :key=\"i + '5a1'\"> {{$t(`${$store.state.gameinfo.game}_construction_faq_q5a1`)}} <br :key=\"i\"></template>\n                <template v-else>{{ item }} <br :key=\"i\"></template>\n              </template>\n            </div>\n          </div>\n        </template>\n        <template v-else-if=\"$gameName === 'dc'\">\n          <div class=\"faq-list\" v-for=\"(item, k) in new Array(6).fill('')\" :key=\"k\">\n            {{ $t(`${$store.state.gameinfo.game}_construction_faq_q${k + 1}`) }}\n            <br>\n            <div>\n              <template v-for=\"(item, i) in $t(`${$store.state.gameinfo.game}_construction_faq_q${k + 1}a`).split('<br/>')\">\n                <template v-if=\"k===2\">{{ item }} <br :key=\"i + '2a1'\"> {{$t(`${$store.state.gameinfo.game}_construction_faq_q3a1`)}} <br :key=\"i\"></template>\n                <template v-else>{{ item }} <br :key=\"i\"></template>\n              </template>\n            </div>\n          </div>\n        </template>\n        <template v-else-if=\"$gameName === 'ssv'\">\n          <div class=\"faq-list\" v-for=\"(item, k) in new Array(9).fill('')\" :key=\"k\">\n            {{ $t(`construction_faq_q${k + 1}`) }}\n            <br>\n            <div>\n              <template v-for=\"(item, i) in $t(`construction_faq_q${k + 1}a`).split('<br/>')\">\n                <template v-if=\"k === 3\">\n                  <template v-if=\"item.indexOf('{0}') > -1\">\n                    {{ item.split('{0}')[0] }}<a target=\"__blank\" href=\"https://stateofsurvival.game/pc/activity\" :key=\"i\">https://stateofsurvival.game/pc/activity</a>{{ item.split('{0}')[1] }}\n                  </template>\n                  <template v-else-if=\"item.indexOf('{1}') > -1\">\n                    {{ item.split('{1}')[0] }}<a target=\"__blank\" href=\"https://stateofsurvival.game/\" :key=\"i\">https://stateofsurvival.game/</a>{{ item.split('{1}')[1] }}\n                  </template>\n                  <template v-else>{{ item }}</template>\n                  <br :key=\"`${i}_${k}`\">\n                </template>\n                <template v-else-if=\"k===4\">{{ item }} <br :key=\"i + '5a1'\"> {{$t(`construction_faq_q5a1`)}} <br :key=\"i\"></template>\n                <template v-else>{{ item }} <br :key=\"i\"></template>\n              </template>\n            </div>\n          </div>\n          <div class=\"faq-list\" key=\"10\">\n            {{ $t('construction_faq_q10') }}\n            <br>\n            <div>\n              {{ $t('construction_faq_q10a') }}\n            </div>\n          </div>\n        </template>\n        <template v-else-if=\"$gameName === 'ssv2'\">\n          <div class=\"faq-list\" v-for=\"(item, k) in new Array(6).fill('')\" :key=\"k\">\n            {{ $t(`${prefix}${k + 1}`) }}\n            <br>\n            <div>\n              <template v-for=\"(item, i) in $t(`${prefix}${k + 1}a`).split('<br/>')\">\n                <template v-if=\"k===2\">{{ item }} <br :key=\"i + '5a1'\"> {{$t(`${prefix}3a1`)}} <br :key=\"i\"></template>\n                <template v-else>{{ item }} <br :key=\"i\"></template>\n              </template>\n            </div>\n          </div>\n        </template>\n        <template v-else-if=\"$gameName === 'foundation'\">\n          <div class=\"faq-list\" v-for=\"(item, k) in new Array(6).fill('')\" :key=\"k\">\n            {{ $t(`fnd_construction_faq_q${k + 1}`) }}\n            <br>\n            <div>\n              <template v-for=\"(item, i) in $t(`fnd_construction_faq_q${k + 1}a`).split('<br/>')\">\n                <template v-if=\"k===3\">{{ item }} <br :key=\"i + '3a1'\"> {{$t(`fnd_construction_faq_q3a1`)}} <br :key=\"i\"></template>\n                <template v-else>{{ item }} <br :key=\"i\"></template>\n              </template>\n            </div>\n          </div>\n        </template>\n        <template v-else-if=\"$gameName === 'ssd'\">\n          <div class=\"faq-list\" v-for=\"(item, k) in new Array(6).fill('')\" :key=\"k\">\n            {{ $t(`ssd_construction_faq_q${k + 1}`) }}\n            <br>\n            <div>\n              <template v-for=\"(item, i) in $t(`ssd_construction_faq_q${k + 1}a`).split('<br/>')\">\n                <template v-if=\"k===2\">{{ item }} <br :key=\"i + '3a1'\"> {{$t(`ssd_construction_faq_q3a1`)}} <br :key=\"i\"></template>\n                <template v-else>{{ item }} <br :key=\"i\"></template>\n              </template>\n            </div>\n          </div>\n        </template>\n        <template v-else>\n          <div class=\"faq-list\" v-for=\"(item, k) in new Array(6).fill('')\" :key=\"k\">\n            {{ $t(`${$store.state.gameinfo.game}_construction_faq_q${k + 1}`) }}\n            <br>\n            <div>\n              <template v-for=\"(item, i) in $t(`${$store.state.gameinfo.game}_construction_faq_q${k + 1}a`).split('<br/>')\">\n                <template v-if=\"k===2\">{{ item }} <br :key=\"i + '2a1'\"> {{$t(`${$store.state.gameinfo.game}_construction_faq_q3a1`)}} <br :key=\"i\"></template>\n                <template v-else>{{ item }} <br :key=\"i\"></template>\n              </template>\n            </div>\n          </div>\n        </template>\n      </div>\n    </div>\n    <div class=\"mask\"></div>\n  </container>\n</template>\n\n<script>\nimport Container from '@/components/pop/container'\nexport default {\n  name: 'ChargeConstruction',\n  components: { Container },\n  computed: {\n    prefix(){\n      const prefixMap = {\n        ssv2: 'ssv2_construction_faq_q',\n      }\n      return prefixMap[this.$gameName]\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n@use \"~@/utils/utils.scss\" as utils;\n.construction {\n  text-align: left;\n  height: 60vh;\n  overflow: auto;\n  p:nth-of-type(n+2){\n    @include utils.setPcContent{\n      margin-top: 10PX;\n    }\n    @include utils.setMobileContent{\n      margin-top: 15px;\n    }\n  }\n  @include utils.setPropByBp(\n    $m: (padding-bottom: 60px),\n    $p: (padding-bottom: 40PX),\n  );\n}\n.mask {\n  @include utils.setPropByBp(\n    $m: (height: 60px),\n    $p: (height: 40PX),\n  );\n  position: absolute;\n  bottom: -2px;\n  left: 0;\n  right: 0;\n  background: linear-gradient(to bottom, rgba(56, 56, 56, 0), rgba(56, 56, 56, 1));\n}\n.faq-wrap {\n  .faq-title {\n    text-align: center;\n    color: #ffffff;\n  }\n  .faq-list {\n    margin-top: 10PX;\n    a {\n      color: #919090;\n      text-decoration: underline;\n      &:active, &:focus, &:visited {\n        color: #919090;\n      }\n    }\n  }\n}\n\n.faq-wrapper.dc{\n  .mask {\n    background: linear-gradient(to bottom, rgba(195,203,225, 0), rgba(195,203,225, 1));\n  }\n  .faq-wrap {\n    .faq-list{\n      color: #525280;\n    }\n  }\n}\n.faq-wrapper.ssv{\n\n}\n</style>\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ChargeConstruction.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ChargeConstruction.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./ChargeConstruction.vue?vue&type=template&id=25c29973&scoped=true\"\nimport script from \"./ChargeConstruction.vue?vue&type=script&lang=js\"\nexport * from \"./ChargeConstruction.vue?vue&type=script&lang=js\"\nimport style0 from \"./ChargeConstruction.vue?vue&type=style&index=0&id=25c29973&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"25c29973\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('container',{attrs:{\"title\":_vm.$t('pending_page_pop_title'),\"option\":_vm.option}},[_c('div',{staticClass:\"err-tips-pop\"},[_c('div',{staticClass:\"pop-msg\"},[_vm._v(_vm._s(_vm.$t('pending_page_pop_tips')))])])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <container :title=\"$t('pending_page_pop_title')\" :option=\"option\">\n    <div class=\"err-tips-pop\">\n      <div class=\"pop-msg\">{{ $t('pending_page_pop_tips') }}</div>\n    </div>\n  </container>\n</template>\n\n<script>\nimport Container from '@/components/pop/container'\nexport default {\n  name: 'CallbackPendingTips',\n  components: { Container },\n  data () {\n    return {\n      option: {\n        title: this.$t('pending_page_pop_title'),\n        confirmBtnTxt: this.$t('cb_back_home'),\n        confirmBtnCb: () => this.$router.replace('/')\n      }\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n@use \"~@/utils/utils.scss\" as utils;\n\n.err-tips-pop{\n  text-align: center;\n  .pop-msg{\n    font-size: 26px;\n    line-height: 30px;\n    font-weight: 500;\n    font-family: PingFangSC-Regular, PingFang SC;\n\n    @include utils.setPropByBp(\n      $m:( font-size: 26px, font-weight: 400, color: #8C8C8C, line-height: 37px,),\n      $p:( font-size: 20px, font-weight: 400, color: #8C8C8C, line-height: 28px,),\n    )\n  }\n}\n</style>\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./CallbackPendingTips.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./CallbackPendingTips.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./CallbackPendingTips.vue?vue&type=template&id=34bf5da9&scoped=true\"\nimport script from \"./CallbackPendingTips.vue?vue&type=script&lang=js\"\nexport * from \"./CallbackPendingTips.vue?vue&type=script&lang=js\"\nimport style0 from \"./CallbackPendingTips.vue?vue&type=style&index=0&id=34bf5da9&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"34bf5da9\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('container',{staticClass:\"arrears-reminder-wrapper\",class:[_vm.$i18n.locale],attrs:{\"hide-close\":true,\"id\":\"pop-arrears-reminder\"},scopedSlots:_vm._u([{key:\"footerBtn\",fn:function(){return [_c('div',{staticClass:\"custom-btn btn-cancel\",on:{\"click\":_vm.cancel}},[_vm._v(_vm._s(_vm.$t('arrears-reminder-cancel')))]),_c('div',{staticClass:\"custom-btn btn-ok\",on:{\"click\":_vm.go}},[_vm._v(_vm._s(_vm.$t('arrears-reminder-ok')))])]},proxy:true}])},[_c('div',{staticClass:\"desc\"},[_vm._v(_vm._s(_vm.$t('arrears-reminder-desc')))])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <container :hide-close=\"true\" class=\"arrears-reminder-wrapper\" :class=\"[$i18n.locale]\" id=\"pop-arrears-reminder\">\n    <div class=\"desc\">{{ $t('arrears-reminder-desc') }}</div>\n<!--<div class=\"debt\" v-html=\"$t('arrears-reminder-debt', {0: `<span>${option.debt}</span>`, 1: `<i></i>`})\"></div>-->\n\n    <template #footerBtn>\n      <div class=\"custom-btn btn-cancel\" @click=\"cancel\">{{ $t('arrears-reminder-cancel') }}</div>\n      <div class=\"custom-btn btn-ok\" @click=\"go\">{{ $t('arrears-reminder-ok') }}</div>\n    </template>\n  </container>\n</template>\n\n<script>\nimport Container from '@/components/pop/container'\nexport default {\n  name: 'ArrearsReminder',\n  props: {\n    option: Object\n  },\n  components: { Container },\n  methods: {\n    go () {\n      this.$root.$emit('closePop')\n      this.$root.$emit('arrearsReminderResult', true)\n    },\n    cancel () {\n      this.$root.$emit('closePop')\n      this.$root.$emit('arrearsReminderResult', false)\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n@use \"~@/utils/utils.scss\" as utils;\n\n.arrears-reminder-wrapper {\n  box-shadow: 0px 2px 12px 0px rgba(0,0,0,0.25);\n  border-radius: 20px;\n  border: 2px solid #000000;\n  background-color: white;\n  padding-top: 12px!important;\n\n  .desc{\n    font-size: 20px;\n    font-family: PingFangSC-Regular, PingFang SC;\n    font-weight: 400;\n    line-height: 28px;\n    color: #2C2C2C;\n    text-align: left;\n  }\n\n  .debt{\n    margin-top: 15px;\n    background: #F6F6F6;\n    border-radius: 6px;\n    font-size: 18px;\n    font-family: PingFangSC-Medium, PingFang SC;\n    font-weight: 500;\n    color: #A2A2A2;\n    line-height: 28px;\n    text-align: left;\n    padding: 12px 15px;\n    overflow: hidden;\n\n    ::v-deep{\n      span{\n        color: #FE6917;\n        margin: 0 4px;\n      }\n      i{\n        @include utils.bgCenter('common/diamond/diamond-icon.png', 20px, 20px);\n        display: inline-block;\n      }\n    }\n  }\n\n  ::v-deep{\n    .footer-wrapper{\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin-top: 30px;\n\n      .custom-btn{\n        min-width: 200px;\n        border-radius: 10px;\n        font-size: 20px;\n        padding: 11px 0;\n        line-height: 28px;\n        cursor: pointer;\n\n        &.btn-ok{\n          margin-left: 32px;\n          background: #FE6917;\n          color: #FFFFFF;\n        }\n\n        &.btn-cancel{\n          color: #7C7C7C;\n          border: 1px solid #A8A6A8;\n        }\n      }\n    }\n  }\n\n  @include utils.setPcContent{\n    padding-top: 2PX!important;\n\n    .desc{\n      font-size: 18PX;\n      line-height: 25PX;\n    }\n\n    .debt{\n      margin-top: 15PX;\n      padding: 12PX 15PX;\n      font-size: 18PX;\n      line-height: 25PX;\n\n      ::v-deep{\n        span{\n          margin: 0 4PX;\n        }\n        i{\n          @include utils.bgCenter('common/diamond/diamond-icon.png', 16PX, 16PX);\n          margin-left: 1PX;\n        }\n      }\n    }\n\n    ::v-deep{\n      .footer-wrapper{\n        margin-top: 30PX;\n\n        .custom-btn{\n          min-width: 200PX;\n          border-radius: 10PX;\n          font-size: 20PX;\n          padding: 11PX 0;\n          line-height: 28PX;\n\n          &.btn-ok{\n            margin-left: 32PX;\n          }\n\n          &.btn-cancel{\n            color: #7C7C7C;\n            border: 1PX solid #A8A6A8;\n          }\n        }\n      }\n    }\n  }\n\n  &.ar{\n    .desc, .debt{\n      text-align: right;\n      direction: rtl;\n    }\n  }\n}\n</style>\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ArrearsReminder.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ArrearsReminder.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./ArrearsReminder.vue?vue&type=template&id=2f1ed20c&scoped=true\"\nimport script from \"./ArrearsReminder.vue?vue&type=script&lang=js\"\nexport * from \"./ArrearsReminder.vue?vue&type=script&lang=js\"\nimport style0 from \"./ArrearsReminder.vue?vue&type=style&index=0&id=2f1ed20c&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2f1ed20c\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('container',{staticClass:\"ios-pop\",attrs:{\"hide-close\":true}},[_c('div',{staticClass:\"description\"},[_vm._v(_vm._s(_vm.$t('pkg_not_allow'))+\" \"),_c('p',{domProps:{\"innerHTML\":_vm._s(_vm.calcTxt)}})])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <container :hide-close=\"true\" class=\"ios-pop\">\n    <div class=\"description\">{{ $t('pkg_not_allow') }} <p v-html=\"calcTxt\"></p> </div>\n  </container>\n</template>\n\n<script>\nimport Container from '@/components/pop/container'\n\nexport default {\n  name: 'IosForbidden',\n  components: { Container },\n  props: {\n    option: Object\n  },\n  data () {\n    return {\n      interval: undefined,\n      leaveCount: 3\n    }\n  },\n  computed: {\n    calcTxt () {\n      return this.$t('channel_not_allow').replace('3', `<span>${this.leaveCount}</span>`)\n    }\n  },\n  mounted () {\n    const interval = setInterval(() => {\n      this.leaveCount = this.leaveCount - 1\n\n      if (this.leaveCount === 0) {\n        clearInterval(interval)\n        this.$root.$emit('closePop')\n        setTimeout(() => {\n          const openid = this.option.openid\n          window.location.href = `${process.env.VUE_APP_OlD_STORE_URL_KOA}?openid=${encodeURIComponent(openid)}&from=paykoa`\n        }, 0)\n      }\n    }, 1000)\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n.ios-pop {\n  background-color: white;\n  border-radius: 0;\n\n  .description {\n    font-size: 28px;\n    font-family: PingFangSC-Medium, PingFang SC;\n    font-weight: 500;\n    color: #303030;\n    line-height: 40px;\n\n    ::v-deep{\n      span{\n        color: #FF5E0F;\n      }\n    }\n  }\n}\n\n::v-deep {\n  .content{\n    margin-top: 0!important;\n  }\n  .footer-wrapper {\n    display: none;\n  }\n}\n</style>\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./IosForbidden.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./IosForbidden.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./IosForbidden.vue?vue&type=template&id=6892a751&scoped=true\"\nimport script from \"./IosForbidden.vue?vue&type=script&lang=js\"\nexport * from \"./IosForbidden.vue?vue&type=script&lang=js\"\nimport style0 from \"./IosForbidden.vue?vue&type=style&index=0&id=6892a751&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"6892a751\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('container',{staticClass:\"custom-diamond-wrapper\",class:[_vm.$i18n.locale, _vm.$gameName]},[_c('div',{staticClass:\"title\"},[_vm._v(\" \"+_vm._s(_vm.$t('custom-diamond-label'))+\" \"),_c('i',{staticClass:\"close\",on:{\"click\":function($event){return _vm.$root.$emit('closePop')}}})]),_c('div',{staticClass:\"diamond-input-wrapper\"},[_c('span',{staticClass:\"basic-num\"},[_vm._v(_vm._s(_vm.coin))]),_c('i'),_vm._v(\" x \"),_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.num),expression:\"num\"}],class:{ 'error-input': _vm.errorInput },attrs:{\"type\":\"number\",\"placeholder\":\"\"},domProps:{\"value\":(_vm.num)},on:{\"input\":[function($event){if($event.target.composing)return;_vm.num=$event.target.value},_vm.floorInput],\"blur\":_vm.validateCustomInput}})]),_c('div',{staticClass:\"tips\",class:{ 'error-input': _vm.errorInput }},[_vm._v(\"*\"+_vm._s(_vm.$vt('customDiamondLimitTips')))]),_c('div',{staticClass:\"checkout-wrapper\"},[_c('div',{staticClass:\"left\"},[_c('div',{staticClass:\"total-diamond\"},[_vm._v(_vm._s(_vm.$t('totalPrice'))+\"：\"+_vm._s(_vm.coin * _vm.num)+\" \"),_c('i')]),(_vm.priceState.nowPrice)?_c('div',{class:['now-price',{'is-ar-zone': _vm.isArZone}]},[_vm._v(_vm._s(_vm.priceState.nowPrice))]):_vm._e(),(_vm.priceState.originPrice)?_c('div',{class:['origin-price',{'is-ar-zone': _vm.isArZone}]},[_vm._v(_vm._s(_vm.priceState.originPrice))]):_vm._e()]),_c('div',{staticClass:\"right\"},[_c('div',{staticClass:\"btn\",on:{\"click\":_vm.submit}},[_vm._v(_vm._s(_vm.$t('confirm-btn')))])])])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <container class=\"custom-diamond-wrapper\" :class=\"[$i18n.locale, $gameName]\">\n    <div class=\"title\">\n      {{ $t('custom-diamond-label') }}\n      <i class=\"close\" @click=\"$root.$emit('closePop')\"></i>\n    </div>\n    <div class=\"diamond-input-wrapper\">\n      <span class=\"basic-num\">{{ coin }}</span>\n      <i></i>\n      x\n      <input type=\"number\" placeholder=\"\" v-model=\"num\" :class=\"{ 'error-input': errorInput }\" @input=\"floorInput\" @blur=\"validateCustomInput\">\n    </div>\n    <div class=\"tips\" :class=\"{ 'error-input': errorInput }\">*{{ $vt('customDiamondLimitTips') }}</div>\n\n    <div class=\"checkout-wrapper\">\n      <div class=\"left\">\n        <div class=\"total-diamond\">{{ $t('totalPrice') }}：{{ coin * num }} <i></i></div>\n<!--        <template v-if=\"customProductId === chosenDiamond.product_id && chosenCoupon.productId === customProductId\">-->\n<!--          <div :class=\"['now-price',{'is-ar-zone': isArZone}]\">-->\n<!--            <span class=\"symbol\">{{ currencySymbol }}</span>-->\n<!--            <template v-if=\"chosenCoupon.feType==='first_pay'\">{{ chosenCoupon.discount_price }}</template>-->\n<!--            <template v-if=\"chosenCoupon.feType==='discount_coupon'\">{{ chosenCoupon.discount_price }}</template>-->\n<!--            <template v-if=\"chosenCoupon.feType==='cash_coupon'\">{{ chosenCoupon.price }}</template>-->\n<!--          </div>-->\n<!--          <div :class=\"['origin-price',{'is-ar-zone': isArZone}]\">{{ `${currencySymbol} ${defaultPrice}` }}</div>-->\n<!--        </template>-->\n<!--        <template v-else-if=\"chosenCouponOther[customProductId]\">-->\n<!--          <div :class=\"['now-price',{'is-ar-zone': isArZone}]\">-->\n<!--            <span class=\"symbol\">{{ currencySymbol }}</span>-->\n<!--            {{ chosenCouponOther[customProductId].price }}-->\n<!--          </div>-->\n<!--          <div :class=\"['origin-price',{'is-ar-zone': isArZone}]\"> {{ `${currencySymbol} ${chosenCouponOther[customProductId].original_price}` }}</div>-->\n<!--        </template>-->\n<!--        <template v-else>-->\n<!--          <div class=\"now-price\">{{ currencySymbol }} {{ calcPrice(price * num) }}</div>-->\n<!--        </template>-->\n          <div v-if=\"priceState.nowPrice\" :class=\"['now-price',{'is-ar-zone': isArZone}]\">{{ priceState.nowPrice }}</div>\n          <div v-if=\"priceState.originPrice\" :class=\"['origin-price',{'is-ar-zone': isArZone}]\">{{ priceState.originPrice }}</div>\n      </div>\n      <div class=\"right\">\n        <div class=\"btn\" @click=\"submit\">{{ $t('confirm-btn') }}</div>\n      </div>\n    </div>\n  </container>\n</template>\n\n<script>\nimport Container from '@/components/pop/containerV2'\nimport { mapGetters, mapState } from 'vuex'\nimport { priceHelper } from '@/utils/utils'\n\nconst MAX_VALUE = 100\nconst MIN_VALUE = window.$gcbk('ids.minCustomDiamondNum', 11)\nexport default {\n  name: 'CustomDiamond',\n  components: { Container },\n  props: {\n    option: Object\n  },\n  data () {\n    return {\n      num: MIN_VALUE,\n      price: 0,\n      coin: 0,\n      currencySymbol: '',\n      customProductId: '',\n      defaultPrice: 0\n    }\n  },\n  methods: {\n    submit () {\n      if (this.errorInput) return null\n      if (this.option.cb) this.option.cb(this.num)\n      this.$root.$emit('closePop')\n    },\n    floorInput () {\n      this.num = Math.floor(this.num)\n      if (this.num > MAX_VALUE) this.num = MAX_VALUE\n    },\n    validateCustomInput () {\n      if (this.num > MAX_VALUE) this.num = MAX_VALUE\n      if (this.num < MIN_VALUE) this.num = MIN_VALUE\n    }\n  },\n  computed: {\n    ...mapState('formdata', ['chosenDiamond', 'chosenCoupon', 'chosenCouponOther']),\n    ...mapState(['currency', 'isArZone']),\n    ...mapGetters('formdata', ['isDiamondOwn95Off', 'isDiamondOwnRebate']),\n    calcPrice () {\n      return price => priceHelper(price, this.currency)\n    },\n    errorInput () {\n      return this.num < MIN_VALUE || this.num > MAX_VALUE\n    },\n    priceState () {\n      /* 现在只兼容了：固定返钻和95折，如需增加优惠券需要在开发 */\n      const priceState = { nowPrice: 0, originPrice: 0 }\n      if (this.isDiamondOwn95Off({ product_id: this.customProductId })) {\n        priceState.nowPrice = this.calcPrice(this.price * this.num * 0.95)\n        priceState.originPrice = this.calcPrice(this.price * this.num)\n      } else {\n        // 固定返钻和默认情况\n        priceState.nowPrice = this.calcPrice(this.price * this.num)\n      }\n\n      if (priceState.nowPrice) priceState.nowPrice += ` ${this.currencySymbol}`\n      if (priceState.originPrice) priceState.originPrice += ` ${this.currencySymbol}`\n      return priceState\n    }\n  },\n  created () {\n    const { diamond } = this.option\n    this.price = diamond.level_currency_price\n    this.coin = diamond.coin\n    this.num = diamond.chosenNum\n    this.currencySymbol = diamond.currency_symbol\n    this.customProductId = diamond.product_id\n    this.defaultPrice = diamond.defaultPrice\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n@use \"~@/utils/utils.scss\" as utils;\n\n$red: #FF5E0F;\n.error-input{\n  color: $red!important;\n}\n\n.custom-diamond-wrapper {\n  background-color: #4C4C4C;\n  color: white;\n  border-radius: 18px;\n  padding-top: 20px;\n  overflow: hidden;\n\n  .title{\n    height: 56px;\n    font-size: 40px;\n    font-family: PingFangSC-Semibold, PingFang SC;\n    font-weight: 600;\n    color: #FFFFFF;\n    line-height: 56px;\n    position: relative;\n\n    .close{\n      @include utils.bgCenter('koa/pop/pop-close.png',34px,33px);\n      display: inline-block;\n      position: absolute;\n      cursor: pointer;\n      right: 30px;\n      top: 50%;\n      transform: translateY(-50%);\n    }\n  }\n\n  .diamond-input-wrapper{\n    display: inline-block;\n    align-items: center;\n    justify-content: center;\n    margin-top: 30px;\n\n    // x *\n    font-size: 28px;\n    font-family: PingFangSC-Semibold, PingFang SC;\n    font-weight: 600;\n\n    .basic-num{\n      font-size: 30px;\n      font-family: PingFangSC-Semibold, PingFang SC;\n      font-weight: 600;\n      color: #FFFFFF;\n      line-height: 42px;\n    }\n\n    i {\n      margin: 0 10px 0 2px;\n      cursor: pointer;\n      @include utils.bgCenter( 'koa/diamond/diamond.png', calc(23px * 1.1), calc(19px * 1.1));\n      flex-shrink: 0;\n      position: relative;\n      display: inline-block;\n    }\n\n    input{\n      border: 1px solid #979797;\n      background: transparent;\n      border-radius: 8px;\n      height: 60px;\n      margin-left: 10px;\n      appearance: none;\n      -webkit-appearance:none;\n      color: white;\n      text-indent: 15px;\n      font-size: 30px;\n      width: 260px;\n      font-weight: 600;\n\n      &:active, &:focus{\n        appearance: none;\n        -webkit-appearance:none;\n        outline: none;\n      }\n\n      &.error-input{\n        border-color: $red;\n      }\n    }\n    input::-webkit-outer-spin-button,\n    input::-webkit-inner-spin-button {\n      -webkit-appearance: none !important;\n      margin: 0;\n    }\n    input[type=number]{-moz-appearance:textfield;}\n  }\n\n  .tips{\n    font-size: 18px;\n    font-family: PingFangSC-Semibold, PingFang SC;\n    font-weight: 600;\n    color: #FFFFFF;\n    line-height: 22px;\n    margin-top: 10px;\n  }\n\n  .checkout-wrapper{\n    background: #383838;\n    margin-top: 30px;\n    justify-content: space-between;\n    padding: 18px 30px 18px 30px;\n    display: flex;\n    align-items: center;\n\n    .left{\n      text-align: left;\n      .total-diamond{\n        font-size: 26px;\n        font-family: PingFangSC-Regular, PingFang SC;\n        color: #FFFFFF;\n        line-height: 1;\n        font-weight: 600;\n\n        i {\n          @include utils.bgCenter( 'koa/diamond/diamond.png', calc(23px * 1.1), calc(19px * 1.1));\n          flex-shrink: 0;\n          display: inline-block;\n        }\n      }\n      .now-price{\n        font-size: 42px;\n        font-family: PingFangSC-Semibold, PingFang SC;\n        font-weight: 600;\n        color: #FDDB70;\n        line-height: 1;\n        margin-top: 10px;\n\n        .symbol{\n          font-size: 26px;\n          line-height: 1;\n        }\n      }\n      .origin-price{\n        font-size: 22px;\n        font-family: PingFangSC-Regular, PingFang SC;\n        font-weight: 400;\n        color: #FDDB70 ;\n        line-height: 1;\n        text-decoration: line-through;\n        margin-top: 10px;\n      }\n    }\n    .right{\n      .btn{\n        line-height: 70px;\n        background: #FDDB70;\n        border-radius: 35px;\n        padding: 0 50px;\n        font-size: 30px;\n        color: #422E00;\n        font-weight: bold;\n      }\n    }\n  }\n}\n@include utils.setPcContent{\n  .custom-diamond-wrapper{\n    border-radius: 18PX;\n    padding-top: 20PX;\n\n    .title{\n      font-size: 24PX;\n      line-height: 33PX;\n      height: 33px;\n\n      .close{\n        @include utils.bgCenter('koa/pop/pop-close.png',20PX,20PX);\n        right: 20PX;\n      }\n    }\n\n    .diamond-input-wrapper{\n      margin-top: 20PX;\n      // x *\n      font-size: 20PX;\n\n      .basic-num{\n        font-size: 20PX;\n        line-height: 28PX;\n      }\n\n      i {\n        margin: 0 10PX 0 2PX;\n        @include utils.bgCenter( 'koa/diamond/diamond.png', calc(23px * .82), calc(19px * .82));\n      }\n\n      input{\n        border: 2PX solid #FFFFFF;\n        border-radius: 8PX;\n        height: 50PX;\n        margin-left: 10PX;\n        text-indent: 15PX;\n        font-size: 20PX;\n        width: 200PX;\n      }\n    }\n\n    .tips{\n      font-size: 14PX;\n      line-height: 1;\n      margin-top: 10PX;\n    }\n\n    .checkout-wrapper{\n      margin-top: 16PX;\n      padding: 8PX 20PX 10PX;\n\n      .left{\n        .total-diamond{\n          font-size: 20PX;\n          line-height: 32PX;\n\n          i {\n            @include utils.bgCenter( 'koa/diamond/diamond.png', calc(23px * .75), calc(19px * .75));\n          }\n        }\n        .now-price{\n          font-size: 26PX;\n          line-height: 32PX;\n          margin-top: 2px;\n\n          .symbol{\n            font-size: 18PX;\n          }\n        }\n        .origin-price{\n          font-size: 18PX;\n          margin-top: 6px;\n        }\n      }\n      .right{\n        .btn{\n          line-height: 50PX;\n          border-radius: 25PX;\n          padding: 0 56PX;\n          font-size: 22PX;\n          cursor: pointer;\n          font-weight: bold;\n        }\n      }\n    }\n  }\n}\n\n/* ssv */\n.custom-diamond-wrapper.ssv{\n  .diamond-input-wrapper{\n    i {\n      @include utils.bgCenterForSSV('diamond/diamond.png', 24px, 24px);\n    }\n  }\n  .checkout-wrapper{\n    .left{\n      .total-diamond{\n        i {\n          @include utils.bgCenterForSSV('diamond/diamond.png', 24px, 24px);\n        }\n      }\n      .now-price{\n        color: #FF5E0F;\n      }\n      .origin-price{\n        color: #C74200;\n      }\n    }\n    .right{\n      .btn{\n        background: #FF5E0F;\n        color: white;\n      }\n    }\n  }\n}\n\n.custom-diamond-wrapper.ssv2{\n  .diamond-input-wrapper{\n    i {\n      @include utils.bgCenterForSS('diamond/diamond.png', 24px, 24px);\n    }\n  }\n  .checkout-wrapper{\n    .left{\n      .total-diamond{\n        i {\n          @include utils.bgCenterForSS('diamond/diamond.png', 24px, 24px);\n        }\n      }\n      .now-price{\n        color: #FF5E0F;\n      }\n      .origin-price{\n        color: #C74200;\n      }\n    }\n    .right{\n      .btn{\n        background: #FF5E0F;\n        color: white;\n      }\n    }\n  }\n}\n</style>\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./CustomDiamond.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./CustomDiamond.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./CustomDiamond.vue?vue&type=template&id=6883bc84&scoped=true\"\nimport script from \"./CustomDiamond.vue?vue&type=script&lang=js\"\nexport * from \"./CustomDiamond.vue?vue&type=script&lang=js\"\nimport style0 from \"./CustomDiamond.vue?vue&type=style&index=0&id=6883bc84&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"6883bc84\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('container',{staticClass:\"arrears-reminder-wrapper\",class:[_vm.$i18n.locale],attrs:{\"customContent\":true,\"size\":\"avatar-bonus-pop-container\",\"hideFooter\":true,\"hideHeader\":true}},[_c('i',{on:{\"click\":function($event){return _vm.$root.$emit('closePop')}}}),_c('div',{staticClass:\"avatar-bonus-pop-content\"},[_c('div',{staticClass:\"content-body\"},[_c('div',{staticClass:\"title\"},[_vm._v(_vm._s(_vm.$t('gog-activity-banner-avatar-title')))]),_c('div',{staticClass:\"frame\"}),_c('div',{staticClass:\"btn\",on:{\"click\":function($event){return _vm.$root.$emit('closePop')}}},[_vm._v(_vm._s(_vm.$t('shop_now')))])])])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <container :customContent=true size=\"avatar-bonus-pop-container\" :hideFooter=\"true\" :hideHeader=\"true\" class=\"arrears-reminder-wrapper\" :class=\"[$i18n.locale]\">\n    <i @click=\"$root.$emit('closePop')\"></i>\n    <div class=\"avatar-bonus-pop-content\">\n      <div class=\"content-body\">\n        <div class=\"title\">{{ $t('gog-activity-banner-avatar-title') }}</div>\n        <div class=\"frame\"></div>\n        <div class=\"btn\" @click=\"$root.$emit('closePop')\">{{ $t('shop_now') }}</div>\n      </div>\n    </div>\n  </container>\n</template>\n\n<script>\nimport Container from '@/components/pop/container'\n\nexport default {\n  name: 'AvatarBonusPop',\n  components: { Container }\n}\n</script>\n\n<style scoped lang=\"scss\">\n@use \"~@/utils/utils.scss\" as utils;\n.avatar-bonus-pop-container {\n  background-color: transparent !important;\n  border: 0px !important;\n  @include utils.setPropByBp(\n    $m: (padding: 0px 0 0px,width: 700px, height: 385px),\n    $p: (padding: 0px 0 0px,width: 771px),\n  );\n\n  i {\n    @include utils.bgCenter('koa/pop/pop-close-bg.png',36px,36px);\n    display: inline-block;\n    position: absolute;\n    z-index: 100001;\n    cursor: pointer;\n\n    @include utils.setPropByBp(\n      $m: (top:0px, right: 0px, width:36px, height: 36px),\n      $p: (top:0px, right: 0px, width:36px, height: 36px),\n    );\n  }\n .avatar-bonus-pop-content {\n    @include utils.bgCenter('koa/pop/avatar-bonus-bg.png',100%, 100%);\n    position: relative;\n    padding-right: 25px;\n    display: flex;\n    justify-content: flex-end;\n    .content-body {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      justify-content: flex-start;\n      width: 409px;\n    }\n    .title {\n      margin: 0;\n      color: #FFF7CB;\n      padding-top: 89px;\n      width: 100%;\n      height: 126px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      text-align: center;\n      font-size: 26px;\n      font-family: SourceHanSansCN-Bold, SourceHanSansCN;\n      font-weight: bold;\n      color: #FFF429;\n      line-height: 1.2;\n      @include utils.setPropByBp(\n        $m: (padding-top: 80px, font-size: 24px, line-height: 34px, height: 114px)\n      );\n    }\n    .frame {\n      @include utils.bgCenter('koa/pop/avatar-bonus-frame.png', 91px, 88px);\n      margin-top: 54px;\n      @include utils.setPropByBp(\n        $m: (padding-top: 48px, width: 83px, height: 81px)\n      );\n    }\n\n    .btn {\n      @include utils.bgCenter('koa/pop/avatar-bonus-btn.png', 251px, 54px);\n      cursor: pointer;\n      margin-top: 70px;\n      font-size: 24px;\n      color: #ffffff;\n      text-shadow: 0px 1px 1px rgba(255,255,255,0.77);\n      line-height: 54px;\n      @include utils.setPropByBp(\n        $m: (margin-top: 62px, width: 228px, height: 50px, font-size: 22px, line-height: 50px)\n      );\n    }\n\n  }\n\n  @include utils.setPcContent{\n    .avatar-bonus-pop-content {\n      @include utils.bgCenter('koa/pop/avatar-bonus-bg.png', 771px, 424px);\n      padding-right: 30px;\n      .content-body {\n        width: 437px;\n      }\n    }\n  }\n}\n\n</style>\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./AvatarBonusPop.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./AvatarBonusPop.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./AvatarBonusPop.vue?vue&type=template&id=5fc05c74&scoped=true\"\nimport script from \"./AvatarBonusPop.vue?vue&type=script&lang=js\"\nexport * from \"./AvatarBonusPop.vue?vue&type=script&lang=js\"\nimport style0 from \"./AvatarBonusPop.vue?vue&type=style&index=0&id=5fc05c74&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"5fc05c74\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('container-v2',[_c('div',{staticClass:\"daily-reward-wrapper\"},[(_vm.type === 'box')?[_c('div',{staticClass:\"title\"},[_vm._v(_vm._s(_vm.$t('title-daily-rewards')))]),_c('div',{staticClass:\"daily-reward-box\"}),_c('div',{staticClass:\"btn\",on:{\"click\":_vm.openBox}},[_vm._v(_vm._s(_vm.$t('btn-open-now')))]),_c('div',{staticClass:\"tips\"},[_vm._v(_vm._s(_vm.$t('subtitle-daily-rewards')))])]:_vm._e(),(_vm.type === 'exp')?[_c('div',{staticClass:\"title\"},[_vm._v(_vm._s(_vm.$t('btn-congratulations')))]),_c('div',{staticClass:\"name\",domProps:{\"innerHTML\":_vm._s(_vm.$t('text-obtain-vip-exp', { 0: `<span>${_vm.expNum}</span>` }))}}),_c('div',{staticClass:\"daily-reward-exp\"}),_c('div',{staticClass:\"btn btn-exp\",on:{\"click\":_vm.close}},[_vm._v(_vm._s(_vm.$t('btn-accept')))]),_c('div',{staticClass:\"go-vip\",on:{\"click\":_vm.clickGoVip}},[_vm._v(_vm._s(_vm.$t('btn-check-vip')))])]:_vm._e(),(_vm.type === 'coupon')?[_c('div',{staticClass:\"title\"},[_vm._v(_vm._s(_vm.$t('btn-congratulations')))]),_c('div',{staticClass:\"name\"},[_vm._v(_vm._s(_vm.couponName))]),_c('div',{class:['daily-reward-' + _vm.name]}),_c('div',{staticClass:\"btn btn-coupon\",on:{\"click\":_vm.close}},[_vm._v(_vm._s(_vm.$t('btn-accept')))])]:_vm._e(),_c('div',{staticClass:\"close\",on:{\"click\":_vm.close}})],2)])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <container-v2>\n    <div class=\"daily-reward-wrapper\">\n      <template v-if=\"type === 'box'\">\n        <div class=\"title\">{{ $t('title-daily-rewards') }}</div>\n        <div class=\"daily-reward-box\"></div>\n        <div class=\"btn\" @click=\"openBox\">{{ $t('btn-open-now') }}</div>\n        <div class=\"tips\">{{ $t('subtitle-daily-rewards') }}</div>\n      </template>\n      <template v-if=\"type === 'exp'\">\n        <div class=\"title\">{{ $t('btn-congratulations') }}</div>\n        <div class=\"name\" v-html=\"$t('text-obtain-vip-exp', { 0: `<span>${expNum}</span>` })\"></div>\n        <div class=\"daily-reward-exp\"></div>\n        <div class=\"btn btn-exp\" @click=\"close\">{{ $t('btn-accept') }}</div>\n        <div class=\"go-vip\" @click=\"clickGoVip\">{{ $t('btn-check-vip') }}</div>\n      </template>\n      <template v-if=\"type === 'coupon'\">\n        <div class=\"title\">{{ $t('btn-congratulations') }}</div>\n        <div class=\"name\">{{ couponName }}</div>\n        <div :class=\"['daily-reward-' + name]\"></div>\n        <div class=\"btn btn-coupon\" @click=\"close\">{{ $t('btn-accept') }}</div>\n      </template>\n\n      <div class=\"close\" @click=\"close\"></div>\n    </div>\n  </container-v2>\n</template>\n\n<script>\nimport ContainerV2 from '@/components/pop/containerV2.vue'\nimport { getAmeDo } from '@/server'\nconst couponNameMap = { '9_day_vip_72': 'text-10-off-coupons', '95_day_vip_72': 'text-5-off-coupons' }\n\nexport default {\n  name: 'DailyReward',\n  components: { ContainerV2 },\n  props: ['option'],\n  data () {\n    let type = ''\n    let expNum = 0\n    let name = ''\n    let couponName = ''\n\n    if (this.option.type) type = this.option.type\n    else {\n      const reward = this.option.reward\n      name = reward.item_list[0].item_name\n      if (name.includes('_exp')) {\n        type = 'exp'\n        expNum = reward.item_list[0].item_nums\n      }\n      if (name.includes('day_vip')) {\n        type = 'coupon'\n        couponName = this.$t(couponNameMap[name])\n      }\n    }\n    return {\n      type,\n      name,\n\n      expNum,\n      couponName\n    }\n  },\n  computed: {\n    vipIntroducePageUrl () {\n      // let base = process.env.VUE_APP_VipIntroducePageUrl + '?l=' + this.$i18n.locale\n      // if (this.userinfo.openid) base += `&openid=${encodeURIComponent(this.userinfo.openid)}`\n      const base = process.env[`VUE_APP_VipIntroducePageUrl_${this.$gameName}`] + '?l=' + this.$i18n.locale\n      return base\n    }\n  },\n  methods: {\n    openBox () {\n      if (!this.$store.state.userinfo.isLogin) {\n        this.$root.$emit('closePop')\n        this.$root.$emit('ClickPayButNotLogin')\n        return null\n      }\n\n      this.$loading.show()\n      getAmeDo({ p0: 'web', p1: 11, p2: 1422 })\n        .then(res => {\n          const { code, data = [] } = res\n          if (code === 0 && data.length) {\n            this.$store.commit('formdata/setDailyRewardStatus', true)\n            this.$root.$emit('closePop')\n\n            setTimeout(() => {\n              this.$root.$emit('showPop', 'DailyReward', { reward: data[0] })\n            }, 0)\n          } else {\n            this.$toast.err(this.$t('network_err'))\n          }\n        })\n        .catch(() => this.$toast.err(this.$t('network_err')))\n        .finally(() => this.$loading.hide())\n    },\n    close () {\n      this.$root.$emit('closePop')\n    },\n    clickGoVip () {\n      window.open(this.vipIntroducePageUrl, '_target')\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n@use \"~@/utils/utils\" as utils;\n.daily-reward-wrapper{\n    background-color: rgba(44, 55, 87);\n    border-radius: 20px;\n    padding-top: 60px;\n    padding-bottom: 30px;\n    position: relative;\n\n    .title{\n      font-size: 40px;\n      font-family: SourceHanSansCN-Heavy, SourceHanSansCN;\n      font-weight: 800;\n      color: #FFFFFF;\n      line-height: 58px;\n      letter-spacing: 2px;\n    }\n\n    .daily-reward-box{\n      @include utils.bgCenter('koa/dailyReward/daily-reward-box.png', 440px, 440px);\n      margin: 22px auto 0;\n    }\n\n    .btn{\n      width: 320px;\n      height: 70px;\n      background: #EED585;\n      border-radius: 45px;\n\n      font-size: 30px;\n      font-family: PingFangSC-Semibold, PingFang SC;\n      font-weight: 600;\n      color: #402600;\n\n      display: flex;\n      align-items: center;\n      justify-content: center;\n\n      margin: -20px auto 0;\n    }\n\n    .tips{\n      font-size: 28px;\n      font-family: SourceHanSansCN-Regular, SourceHanSansCN;\n      font-weight: 400;\n      color: #A4A4A4;\n      line-height: 40px;\n      margin-top: 20px;\n    }\n\n    .name{\n      font-size: 30px;\n      font-family: SourceHanSansCN-Medium, SourceHanSansCN;\n      font-weight: 500;\n      color: #EED585;\n      line-height: 44px;\n      margin-top: 6px;\n\n      ::v-deep{\n        span{\n          font-size: 40px;\n          color: #FF2516;\n          margin: 0 5px;\n        }\n      }\n    }\n\n    .daily-reward-exp{\n      @include utils.bgCenter('koa/dailyReward/daily-reward-exp.png', 445px, 400px);\n      margin: 22px auto 0;\n    }\n\n    .btn-exp{\n      margin: -60px auto 0;\n    }\n\n    .go-vip{\n      font-size: 20px;\n      font-family: SourceHanSansCN-Regular, SourceHanSansCN;\n      font-weight: 400;\n      color: #969696;\n      line-height: 29px;\n      margin-top: 20px;\n      text-decoration: underline;\n    }\n\n    .daily-reward-95_day_vip_72{\n      @include utils.bgCenter('koa/dailyReward/daily-reward-95_day_vip_72.png', 403px, 221px);\n      margin: 78px auto 0;\n    }\n\n    .daily-reward-9_day_vip_72{\n      @include utils.bgCenter('koa/dailyReward/daily-reward-9_day_vip_72.png', 403px, 221px);\n      margin: 78px auto 0;\n    }\n\n    .btn-coupon{\n      margin-top: 79px;\n    }\n\n    .close{\n      @include utils.bgCenter('koa/dailyReward/daily-reward-close.png', 54px, 54px);\n      position: absolute;\n\n      bottom: calc(-30px - 54px);\n      left: 50%;\n      transform: translateX(-50%);\n      cursor: pointer;\n    }\n  }\n\n@include utils.setPcContent{\n  .daily-reward-wrapper{\n    padding-top: 40px;\n    padding-bottom: 35px;\n\n    .title{\n      font-size: 30px;\n      line-height: 44px;\n      letter-spacing: 1px;\n    }\n\n    .daily-reward-box{\n      @include utils.bgCenter('koa/dailyReward/daily-reward-box.png', 362px, 387px);\n      margin: -59px auto 0;\n    }\n\n    .btn{\n      width: 260px;\n      height: 50px;\n      border-radius: 25px;\n      font-size: 20px;\n      margin: -44px auto 0;\n      cursor: pointer;\n    }\n\n    .tips{\n      font-size: 18px;\n      line-height: 26px;\n      margin-top: 9px;\n    }\n\n    .name{\n      font-size: 20px;\n      line-height: 44px;\n      margin-top: 0;\n\n      ::v-deep{\n        span{\n          font-size: 30px;\n          color: #FF2516;\n        }\n      }\n    }\n\n    .daily-reward-exp{\n      @include utils.bgCenter('koa/dailyReward/daily-reward-exp.png', 329px, 295px);\n      margin: 10px auto 0;\n    }\n\n    .btn-exp{\n      margin: -50px auto 0;\n    }\n\n    .go-vip{\n      font-size: 18px;\n      line-height: 26px;\n      margin-top: 10px;\n      cursor: pointer;\n    }\n\n    .daily-reward-95_day_vip_72{\n      @include utils.bgCenter('koa/dailyReward/daily-reward-95_day_vip_72.png', 311px, 172px);\n      margin: 39px auto 0;\n    }\n\n    .daily-reward-9_day_vip_72{\n      @include utils.bgCenter('koa/dailyReward/daily-reward-9_day_vip_72.png', 311px, 172px);\n      margin: 39px auto 0;\n    }\n\n    .btn-coupon{\n      margin-top: 65px;\n    }\n\n    .close{\n      @include utils.bgCenter('koa/dailyReward/daily-reward-close.png', 42px, 42px);\n      bottom: calc(-20px - 42px);\n    }\n  }\n}\n</style>\n", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./DailyReward.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./DailyReward.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./DailyReward.vue?vue&type=template&id=c2ab6286&scoped=true\"\nimport script from \"./DailyReward.vue?vue&type=script&lang=js\"\nexport * from \"./DailyReward.vue?vue&type=script&lang=js\"\nimport style0 from \"./DailyReward.vue?vue&type=style&index=0&id=c2ab6286&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"c2ab6286\",\n  null\n  \n)\n\nexport default component.exports", "<template>\n  <section>\n    <transition name=\"bg\">\n      <div v-if=\"showOuter\" class=\"pop-bg\"></div>\n    </transition>\n    <transition name=\"cmp\">\n      <component v-if=\"cmpName && showInner\" :is=\"cmpName\" :option=\"option\"></component>\n    </transition>\n  </section>\n</template>\n\n<script>\nimport BackendPopup from '@/components/pop/BackendPopup.vue'\nimport ChargeConstruction from '@/components/pop/ChargeConstruction'\nimport CallbackPendingTips from '@/components/pop/CallbackPendingTips'\nimport ArrearsReminder from '@/components/pop/ArrearsReminder'\nimport IosForbidden from '@/components/pop/IosForbidden'\nimport CustomDiamond from '@/components/pop/CustomDiamond'\nimport AvatarBonusPop from '@/components/pop/AvatarBonusPop'\nimport DailyReward from '@/components/game/koa/DailyReward.vue'\nimport { StorageUtils } from '@/utils/storageUtils'\n\nimport { getAmeDo } from '@/server'\n\nexport default {\n  name: 'index',\n  components: {\n    BackendPopup,\n    CallbackPendingTips,\n    ChargeConstruction,\n    ArrearsReminder,\n    WhatIsDiamond: () => import(/* webpackChunkName: 'chunk-functions' */ '@/components/pop/WhatIsDiamond'),\n    IosForbidden,\n    BoonPop: () => import(/* webpackChunkName: 'chunk-functions' */ '@/components/game/koa/BoonPop.vue'),\n    SSVBoonPop: () => import(/* webpackChunkName: 'chunk-functions' */ '@/components/game/ssv/BoonPop.vue'),\n    CustomDiamond,\n    AvatarBonusPop,\n    RiskControlPolicy: () => import(/* webpackChunkName: 'chunk-functions' */ '@/components/pop/RiskControlPolicy'),\n    DailyReward,\n    LoginValidation: () => import(/* webpackChunkName: 'chunk-functions' */ '@/components/pop/LoginValidation'),\n    PrivacyPolicy: () => import(/* webpackChunkName: 'chunk-functions' */ '@/components/pop/PrivacyPolicy.vue'),\n    PrivateConfirmPop: () => import(/* webpackChunkName: 'chunk-functions' */'@/components/pop/privateConfirmPop'),\n    sdk2Tips: () => import(/* webpackChunkName: 'chunk-sdk2' */ '@/components/pop/sdk2Tips')\n  },\n  data () {\n    return {\n      showOuter: false,\n      showInner: false,\n      cmpName: '',\n      option: {},\n      cacheList: [],\n\n      game: this.$store.state.gameinfo.gameProject.split('_')[0],\n      gameEnv: process.env.VUE_APP_PROD_ENV.toLowerCase(),\n      popupList: [],\n      ymd: '',\n      lang: ''\n    }\n  },\n  methods: {\n    showLoginValidation (params) {\n      if (this.cmpName) {\n        const validationPop = { name: 'LoginValidation', option: params }\n        if (this.cmpName === 'BackendPopup') {\n          this.cacheList.unshift(validationPop)\n        } else {\n          const lastPop = { name: this.cmpName, option: this.option }\n          this.cacheList.unshift(lastPop)\n          this.cacheList.unshift(validationPop)\n          this.$root.$emit('closePop')\n        }\n      } else {\n        this.$root.$emit('showPop', 'LoginValidation', params)\n      }\n    },\n    showOtherPop () {\n      this.$root.$once('loginSuccess', async () => {\n        const country = this.$store.state.country\n        const targetArr = 'NL,BE,LU,DK,IE,GR,SE,FI,AT,CY,EE,LV,LT,PL,CS,SK,SI,HU,MT,RO,BG,HR,DE,FR,IT,ES,PT,CA,GB,AU,NZ,US,KR,TW,JP'.split(',')\n        // 其他国家直接返回\n        if (!targetArr.includes(country)) return null\n\n        // 查看本地信息，已经点过就不弹了\n        const history = StorageUtils.getLocalStorage('confirmPrivacyPolicy')\n        if (history === '1') return null\n\n        // 查看服务端信息，已经点过就不弹了\n        const params = {\n          p0: 'web',\n          p1: 7,\n          p2: '1066',\n          silence: true\n        }\n        const { data: { agree } } = await getAmeDo(params)\n        if (agree) return StorageUtils.setLocalStorage('confirmPrivacyPolicy', '1')\n\n        // 如果未同意，弹窗\n        this.$store.commit('setPrivacyPolicyStatus', false)\n        this.$root.$emit('showPop', 'PrivacyPolicy')\n      })\n    }\n  },\n  created () {\n    const broswerLang = navigator.language || navigator.userLanguage\n    if (broswerLang.toLowerCase() === 'zh-tw') this.lang = 'zh_tw'\n    else if (broswerLang.startsWith('zh')) this.lang = 'zh_cn'\n    else this.lang = broswerLang.split('-')[0]\n\n    this.$root.$on('backendPopup', (ip) => {\n      if (this.$store.state.IS_CHECKOUT_SDK_V2) return\n      const params = Object.assign(\n        {\n          p0: 'web',\n          p1: 9,\n          p2: '1653',\n          p3: 'api',\n          game: this.game\n        },\n        process.env.VUE_APP_PROD_ENV === 'ONLINE' ? {} : { gameEnv: this.gameEnv }\n      )\n      const date = new Date(Date.now() + new Date().getTimezoneOffset() * 60 * 1000)\n      const ymd = `${date.getFullYear()}/${date.getMonth() + 1}/${date.getDate()}`\n      getAmeDo(params).then((response) => {\n        if (response.code === 0) {\n          if (this.$route.name !== 'Pay') return null\n          this.popupList = response.data\n          for (let i = 0; i < this.popupList.length; i++) {\n            const popup = this.popupList[i]\n            let popupSrc = null\n            if (popup.mode == 1 && localStorage.getItem(`${this.game}_${popup.id}`) === ymd) {\n              this.popupList.splice(i, 1)\n              i--\n              continue\n            }\n            if (popup.category == 1) {\n              if (!popup.file_details[ip.country]) {\n                this.popupList.splice(i, 1)\n                i--\n                continue\n              } else popupSrc = popup.file_details[ip.country]\n            } else {\n              popupSrc = popup.file_details[this.$i18n.locale || this.lang] || popup.file_details.en\n            }\n            if (!popupSrc) {\n              this.popupList.splice(i, 1)\n              i--\n              continue\n            }\n          }\n          if (this.popupList.length) {\n            this.$root.$emit(\n              'showPop',\n              'BackendPopup',\n              Object.assign(ip, { popupList: this.popupList })\n            )\n          }\n        }\n      })\n\n      // this.$root.$emit(\"showPop\", \"BackendPopup\", ip);\n    })\n    this.$root.$on('showPop', (name, option) => {\n      if (name === 'BoonPop') name = window.$gcbk('ids.boonPopName', 'BoonPop')\n      // 如果有多个,添加到缓存\n      if (this.cmpName) return this.cacheList.push({ name, option })\n\n      this.option = option\n      this.cmpName = name\n\n      this.showOuter = true\n      setTimeout(() => {\n        this.showInner = true\n      }, 0)\n    })\n    this.$root.$on('closePop', (name) => {\n      this.cmpName = ''\n      this.showInner = false\n      this.option = {}\n      setTimeout(() => {\n        this.showOuter = false\n\n        if (this.cacheList.length) {\n          const newItem = this.cacheList.shift()\n          setTimeout(() => this.$root.$emit('showPop', newItem.name, newItem.option), 300)\n        }\n      }, 0)\n    })\n\n    this.$root.$on('saveValidation', this.showLoginValidation)\n\n    // 隐私协议\n    if (this.$store.state.functionSwitch.showPopPolicy || this.$gameName === 'dc') this.showOtherPop()\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n.bg-enter, .bg-leave-to {\n  opacity: 0;\n}\n.bg-enter-active, .bg-leave-active{\n  transition: opacity .3s;\n}\n.pop-bg {\n  height: 100%;\n  width: 100%;\n  position: fixed;\n  left: 0;\n  top: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 100001;\n}\n\n.cmp-enter {\n  opacity: 0;\n  transform: translate(-50%, calc(-50% - 50PX)) !important;\n}\n.cmp-leave-to{\n  opacity: 0;\n  transform: translate(-50%, -50%) scale(0.9) !important;\n}\n.cmp-enter-active, .cmp-leave-active{\n  transition: all .3s ease;\n}\n</style>\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=2a798d4a&scoped=true\"\nimport script from \"./index.vue?vue&type=script&lang=js\"\nexport * from \"./index.vue?vue&type=script&lang=js\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=2a798d4a&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2a798d4a\",\n  null\n  \n)\n\nexport default component.exports", "<template>\n  <div id=\"app\" @click=\"$root.$emit('BodyClick')\" :class=\"[$gameName]\">\n    <keep-alive include=\"Pay\">\n      <router-view v-if=\"showPage && isExtraInfoInit\" :key=\"$route.fullPath\"></router-view>\n    </keep-alive>\n    <PopHome></PopHome>\n  </div>\n</template>\n\n<script>\nimport PopHome from '@/components/pop'\nimport { getCurrencyByIp, changeFpidToOpenid, getCommonInfo, getWxOpenid } from '@/server'\nimport { getPWADisplayMode, getPlatform, isWx, urlHelper } from '@/utils/utils'\nimport { logForPageLoad } from '@/utils/logHelper'\nimport i18n from '@/utils/i18n'\n\nexport default {\n  components: { PopHome },\n  data () {\n    return {\n      showPage: false,\n      isExtraInfoInit: false\n    }\n  },\n  methods: {\n    loadTheme () {\n      // eslint-disable-next-line no-unused-expressions\n      if (this.$gcbk('switch.useThemeFile', false)) import((`@/theme/${this.$gameName}.scss`))\n      // eslint-disable-next-line no-unused-expressions\n      if (this.$store.state.IS_CHECKOUT_SDK_V2) import('@/theme/sdk2.scss')\n    },\n    init () {\n      // 减少收银台等待时间（领导希望更快展示）（优化2）。\n      if (this.$store.state.IS_CHECKOUT_SDK) this.showPage = this.isExtraInfoInit = true\n      getCurrencyByIp()\n        .then(res => {\n          const { code, data } = res\n          if (code === 0) {\n            this.showPage = true\n            this.$store.commit('setCurrencyUnitByIp', data)\n            this.$root.$emit('IPInitEnd')\n            if (['/pay', '/'].includes(this.$route?.path)) this.$root.$emit('backendPopup', data)\n          }\n        })\n\n      this.$loading.show()\n      getCommonInfo()\n        .then(res => {\n          const { code, data } = res\n          if (code === 0) {\n            this.isExtraInfoInit = true\n            this.$store.commit('formdata/setExtraCostInfo', data.channel_extra_fee_list)\n            this.$store.commit('formdata/switchToggle', data.change_coupon_enable)\n            this.$store.commit('functionSwitch/updateFunctionInfo', data)\n            this.$root.$emit('updateSpecialDiamond', data.point_card_product)\n            window.__showEmailForm = data.billing_address_enable // 控制在adyen等渠道页，是否显示email搜集表单。\n            this.$store.commit('formdata/setFixedToggleEvent', data.change_coupon_enable) // gog、mc 是否开启切换返钻\n          }\n          if (code === 1000) this.$store.commit('userinfo/logout') // 错误openid\n        })\n        .finally(() => this.$loading.hide())\n    },\n    getOpenidByFpid () {\n      this.$loading.show()\n      changeFpidToOpenid({ fpid: this.$store.state.urlParams.t })\n        .then(res => {\n          const { code, data } = res\n          if (code === 0 && data.openid_list && data.openid_list.length) {\n            localStorage.setItem('openid', data.openid_list[0].openid)\n          } else {\n            console.error(`openid兑换失败：${this.$store.state.urlParams.t}`)\n          }\n          window.location.href = window.location.origin\n        })\n        .finally(() => this.$loading.hide())\n    },\n    checkWxOpenid () {\n      if (!isWx) return\n      const urlParams = urlHelper()\n      if (window.localStorage.getItem('fp_wx_openid')) return undefined\n      else if (urlParams.code && urlParams.state === 'fp_wx_get_code') {\n        window.localStorage.setItem(`code_${new Date().getTime()}`, urlParams.code)\n        getWxOpenid(urlParams.code).then(res => {\n          const { code, data } = res\n          code === 0 && window.localStorage.setItem('fp_wx_openid', data.openid)\n        })\n        return undefined\n      } else {\n        window.location.href = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx9d99d0a7d7b719b8&redirect_uri=${encodeURIComponent(window.location.origin)}&response_type=code&scope=snsapi_base&state=fp_wx_get_code#wechat_redirect`\n      }\n    },\n    initLang () {\n      const local = this.$i18n.locale || ''\n      import(/* webpackChunkName: 'asyncTxt\\/' */ `../langHelper/langJson/${local}.json`)\n        .then(res => {\n          res && i18n.setLocaleMessage(local, res)\n\n          var title = document.querySelector('title')\n          title.innerText = this.$vt('pageTitle')\n        })\n    }\n  },\n  created () {\n    const href = location.href\n    const tFlag = href.includes('&t=') || href.includes('?t=')\n    if (tFlag) return this.getOpenidByFpid()\n\n    logForPageLoad()\n    const displayMode = getPWADisplayMode\n    this.$gtag.event('opened_by', {\n      event_label: displayMode === 'standalone'\n        ? `pwa_${getPlatform}`\n        : displayMode\n    })\n\n    if (this.$store.state.gameinfo.isCn) this.checkWxOpenid()\n    this.init()\n    this.initLang()\n\n    this.loadTheme()\n  }\n}\n</script>\n\n<style lang=\"scss\">\n@import \"~@/utils/reset.scss\";\n#app {\n  font-family: Avenir, Helvetica, Arial, sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  text-align: center;\n  overflow-y: scroll;\n  height: 100%;\n  background-size: cover;\n  background-position: center center;\n}\n\n#app.koa{\n  color: #2c3e50;\n  background-image: url(https://kg-web-cdn.akamaized.net/prod/versionAdmin/source/web-pay-hub/dev/page-bg-koa.png);\n  background-color: rgb(28, 38, 64);\n}\n\n#app.aof, #app.rom, #app.koaCn{\n  @extend .koa;\n}\n\n#app.dc{\n  background-image: url(https://kg-web-cdn.akamaized.net/prod/versionAdmin/source/web-pay-hub/dev/page-bg-dc.png);\n  background-color: rgb(23, 20, 25);\n}\n#app.ssv{\n  background-image: url(https://kg-web-cdn.akamaized.net/prod/versionAdmin/source/web-pay-hub/page-bg-ssv.png);\n  background-color: rgb(28, 28, 28);\n}\n\n#app.ssv2{\n  background-image: url(https://kg-web-cdn.akamaized.net/prod/versionAdmin/source/web-pay-hub/page-bg-ssv.png);\n  background-color: rgb(37, 23, 13);\n}\n</style>\n", "import mod from \"-!../node_modules/cache-loader/dist/cjs.js??ref--13-0!../node_modules/thread-loader/dist/cjs.js!../node_modules/babel-loader/lib/index.js!../node_modules/cache-loader/dist/cjs.js??ref--1-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./App.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../node_modules/cache-loader/dist/cjs.js??ref--13-0!../node_modules/thread-loader/dist/cjs.js!../node_modules/babel-loader/lib/index.js!../node_modules/cache-loader/dist/cjs.js??ref--1-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./App.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./App.vue?vue&type=template&id=91be50f6\"\nimport script from \"./App.vue?vue&type=script&lang=js\"\nexport * from \"./App.vue?vue&type=script&lang=js\"\nimport style0 from \"./App.vue?vue&type=style&index=0&id=91be50f6&prod&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "import Vue from 'vue'\nimport VueRouter from 'vue-router'\nimport store from '@/store'\n\nVue.use(VueRouter)\n\nconst routes = [\n  {\n    path: '/',\n    name: 'Pay',\n    component: () => import(/* webpackChunkName: \"pagePay\" */ '../views/Pay.vue')\n  },\n  {\n    path: '/pay',\n    name: 'Pay',\n    component: () => import(/* webpackChunkName: \"pagePay\" */ '../views/Pay.vue')\n  },\n  {\n    path: '/ad',\n    name: 'Adyen',\n    component: () => import(/* webpackChunkName: \"pageAdyen\" */ '../views/paymethod/Adyen')\n  },\n  {\n    path: '/aw',\n    name: 'Airwallex',\n    component: () => import(/* webpackChunkName: \"pageSmall\" */ '../views/paymethod/airwallex')\n  },\n  {\n    path: '/pp',\n    name: 'pingpong',\n    component: () => import(/* webpackChunkName: \"pageSmall\" */ '../views/paymethod/pingpong')\n  },\n  {\n    path: '/cko',\n    name: 'checkout',\n    component: () => import(/* webpackChunkName: \"pageSmall\" */ '../views/paymethod/checkout')\n  },\n  {\n    path: '/pm',\n    name: 'payermax',\n    component: () => import(/* webpackChunkName: \"pageSmall\" */ '../views/paymethod/payermax')\n  },\n  {\n    path: '/sp',\n    name: 'stripe',\n    component: () => import(/* webpackChunkName: \"pageSmall\" */ '../views/paymethod/stripe.vue')\n  },\n  {\n    path: '/fail',\n    name: 'CallbackFail',\n    component: () => import(/* webpackChunkName: \"pageSmall\" */ '../views/PaymentCallback.vue')\n  },\n  {\n    path: '/completed',\n    name: 'CallbackCompleted',\n    component: () => import(/* webpackChunkName: \"pageSmall\" */ '../views/PaymentCallback.vue')\n  },\n  {\n    path: '/pending',\n    name: 'CallbackPending',\n    component: () => import(/* webpackChunkName: \"pageSmall\" */ '../views/PaymentCallback.vue')\n  },\n  // {\n  //   path: '/boon',\n  //   name: 'Boon',\n  //   component: () => import(/* webpackChunkName: \"Boon\" */ '../views/Boon.vue')\n  // },\n  {\n    path: '/common/fail',\n    name: 'CallbackFail',\n    component: () => import(/* webpackChunkName: \"pageSmall\" */ '../views/PaymentCallbackCommon.vue')\n  },\n  {\n    path: '/common/completed',\n    name: 'CallbackCompleted',\n    component: () => import(/* webpackChunkName: \"pageSmall\" */ '../views/PaymentCallbackCommon.vue')\n  },\n  {\n    path: '/common/pending',\n    name: 'CallbackPending',\n    component: () => import(/* webpackChunkName: \"pageSmall\" */ '../views/PaymentCallbackCommon.vue')\n  },\n  {\n    path: '/order',\n    name: 'OrderPage',\n    component: () => import(/* webpackChunkName: \"pageSmall\" */ '../views/OrderPage.vue')\n  },\n  {\n    path: '*',\n    redirect: '/'\n  }\n]\n\nstore.commit('gameinfo/setGameInfo')\nstore.commit('functionSwitch/setFunctionInfo')\n\nif (store.state.IS_CHECKOUT_SDK_V2) {\n  routes.splice(0, 2, {\n    path: '/',\n    name: 'Pay',\n    component: () => import(/* webpackChunkName: \"pageSdkV2\" */ '../views/PaySdk2.vue')\n  })\n}\nconst router = new VueRouter({\n  mode: 'history',\n  base: window.__ROUTERPATH || '/',\n  routes\n})\n\nexport default router\n", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('transition',{attrs:{\"name\":\"loading\"}},[(_vm.loadingNum>0)?_c('div',{class:['loading-wrapper', _vm.$gameName, {'rp': _vm.$gameName.includes('RP')}]},[_c('div',{staticClass:\"lds-dual-ring\"})]):_vm._e()])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <transition name=\"loading\">\n    <div v-if=\"loadingNum>0\" :class=\"['loading-wrapper', $gameName, {'rp': $gameName.includes('RP')}]\">\n      <div class=\"lds-dual-ring\"></div>\n    </div>\n  </transition>\n</template>\n\n<script>\nexport default {\n  name: 'Loading',\n  data: function () {\n    return {\n      loadingNum: 0\n    }\n  },\n  methods: {\n    showLoading () {\n      this.loadingNum++\n    },\n    hideLoading () {\n      if (this.loadingNum - 1 < 0) console.error('mistake match！')\n      this.loadingNum = Math.max(0, --this.loadingNum)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@use \"~@/utils/utils.scss\" as utils;\n\n.loading-enter,.loading-leave-to{\n  opacity: 0;\n}\n\n.loading-enter-active,.loading-leave-active{\n  transition: all .3s;\n}\n\n@keyframes lds-dual-ring {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n\n.loading-wrapper{\n  height: 100vh;\n  width: 100vw;\n  background-color: rgba(0,0,0,0);\n  position: fixed;\n  top: 0;\n  left: 0;\n  bottom: 0;\n  right: 0;\n  z-index: 100000;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  // opacity: 0;\n\n  .lds-dual-ring {\n    display: inline-block;\n\n    &:after{\n      content: \" \";\n      display: block;\n      border-radius: 50%;\n      animation: lds-dual-ring 1.2s linear infinite;\n    }\n  }\n\n  @include utils.setPcContent{\n    .lds-dual-ring{\n      width: 60PX;\n      height: 60PX;\n\n      &:after{\n        width: 36PX;\n        height: 36PX;\n        margin: 5PX;\n        border: 4.3PX solid #fff;\n        border-color: #DDB358 transparent #DDB358 transparent;\n      }\n    }\n  }\n  @include utils.setMobileContent{\n    .lds-dual-ring{\n      width: 80px;\n      height: 80px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n\n      &:after{\n        width: 60px;\n        height: 60px;\n        border: 8px solid #fff;\n        border-color: #DDB358 transparent #DDB358 transparent;\n      }\n    }\n  }\n}\n\n.loading-wrapper.ssv2{\n  @include utils.setPcContent{\n    .lds-dual-ring{\n      width: 40PX;\n      height: 40PX;\n\n      &:after{\n        width: 33PX;\n        height: 33PX;\n        border: 3PX solid #fff;\n        border-color: #FF5A00 transparent #FF5A00 transparent;\n      }\n    }\n  }\n  @include utils.setMobileContent{\n    .lds-dual-ring{\n      width: 60px;\n      height: 60px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n\n      &:after{\n        width: 50px;\n        height: 50px;\n        border: 5px solid #fff;\n        border-color: #FF5A00 transparent #FF5A00 transparent;\n      }\n    }\n  }\n}\n\n.loading-wrapper.foundation,\n.loading-wrapper.ssd,\n.loading-wrapper.rp{\n  @extend .ssv2;\n}\n\n.loading-wrapper.ssCP, .loading-wrapper.stCP, .loading-wrapper.mcCP, .loading-wrapper.gogCP, .loading-wrapper.romCP {\n  @include utils.setPcContent{\n    .lds-dual-ring {\n      width: 60px;\n      height: 60px;\n\n      &:after {\n        margin: 0;\n        border: none;\n        animation-duration: 1.5s;\n        @include utils.bgCenter(\"common/icon/loading.png\", 45px, 45px);\n      }\n    }\n  }\n\n  @include utils.setMobileContent {\n    .lds-dual-ring {\n      width: 100px;\n      height: 100px;\n\n      &:after {\n        margin: 0;\n        border: none;\n        animation-duration: 1.5s;\n        @include utils.bgCenter(\"common/icon/loading.png\", 60px, 60px);\n      }\n    }\n  }\n}\n</style>\n", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./loading.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./loading.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./loading.vue?vue&type=template&id=431badf6&scoped=true\"\nimport script from \"./loading.vue?vue&type=script&lang=js\"\nexport * from \"./loading.vue?vue&type=script&lang=js\"\nimport style0 from \"./loading.vue?vue&type=style&index=0&id=431badf6&prod&lang=scss&scoped=true\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"431badf6\",\n  null\n  \n)\n\nexport default component.exports", "import loadingCmp from './loading.vue'\n\n// import loadingComponentMobile from '@/components/mobile/common/loading'\n// import loadingComponentPc from '@/components/pc/common/loading'\n// import { isMobile } from \"@/utils/utils\";\n\nconst Loading = {\n  install (Vue, options) {\n    const loading = {}\n    const getVm = (function () {\n      let $vm = null\n      return function () {\n        if ($vm) return $vm\n        else {\n          // let cmp = isMobile ? loadingComponentMobile : loadingComponentPc\n          const cmp = loadingCmp\n          const Constructor = Vue.extend(cmp)\n          $vm = new Constructor({\n            el: document.createElement('div')\n          })\n          document.body.appendChild($vm.$el)\n          return $vm\n        }\n      }\n    })()\n    const vm = getVm()\n    loading.show = function () {\n      vm.showLoading()\n    }\n    loading.hide = function () {\n      vm.hideLoading()\n    }\n    Vue.prototype.$loading = loading\n    if (options) console.log(options)\n  }\n}\n\nexport default Loading\n", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('transition',{attrs:{\"name\":\"message\"}},[(_vm.message)?_c('section',{staticClass:\"msg-wrapper\"},[_vm._v(_vm._s(_vm.message))]):_vm._e()])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n<transition name=\"message\">\n  <section v-if=\"message\" class=\"msg-wrapper\">{{ message }}</section>\n</transition>\n</template>\n\n<script>\nexport default {\n  name: 'message',\n  data () {\n    return {\n      message: ''\n    }\n  },\n  methods: {\n    open (msg, duration, cb) {\n      this.message = msg\n      setTimeout(() => {\n        this.message = ''\n        setTimeout(() => { cb() }, 300)\n      }, duration)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@use \"~@/utils/utils.scss\" as utils;\n\n.message-enter-active,.message-leave-active{\n  transition: all .36s ease;\n}\n\n.message-enter{\n  opacity: 0;\n  transform: translate(-50%,calc(-50% - 50px)) !important;\n}\n\n.message-leave-to{\n  opacity: 0;\n  transform: translate(-50%,calc(-50% - 50px)) !important;\n}\n\n  .msg-wrapper{\n    position: fixed;\n    left: 50%;\n    top: 50%;\n    transform: translate(-50%,-50%);\n    z-index: 10000000;\n    color: #f9f9f9;\n    background-color: rgba(0,0,0,.75);\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    text-align: center;\n\n    @include utils.setPropByBp(\n      $p: (max-width: 700px, min-height: 40px,padding: 14px 20px,font-size: 16px,border-radius: 12px),\n      $m: (max-width: calc(100% - 100px), min-width: 220px, line-height: 36px,min-height: 75px, padding: 12px 30px,font-size: 24px,border-radius: 16px)\n    );\n  }\n</style>\n", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./toast.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./toast.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./toast.vue?vue&type=template&id=c502de0a&scoped=true\"\nimport script from \"./toast.vue?vue&type=script&lang=js\"\nexport * from \"./toast.vue?vue&type=script&lang=js\"\nimport style0 from \"./toast.vue?vue&type=style&index=0&id=c502de0a&prod&lang=scss&scoped=true\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"c502de0a\",\n  null\n  \n)\n\nexport default component.exports", "import message from './toast.vue'\nimport Vue from 'vue'\n\nconst getVmSingleton = function (cmp) {\n  let $vm = null\n  return function () {\n    if ($vm) return $vm\n    else {\n      const Constructor = Vue.extend(cmp)\n      $vm = new Constructor({\n        el: document.createElement('div')\n      })\n      document.body.appendChild($vm.$el)\n      return $vm\n    }\n  }\n}\n\nconst messageCmp = {\n  install (Vue, options) {\n    const vm = getVmSingleton(message)()\n    const obj = {}\n    const queue = []\n    let isPending = false\n\n    const openVm = (msg, duration) => {\n      isPending = true\n      vm.open(msg, duration, obj.next.bind(obj))\n    }\n    const isTipsExit = target => queue.some(item => item.msg === target) || vm.message === target;\n\n    ['err', 'success'].forEach(key => {\n      obj[key] = function (msg, duration = 2000) {\n        if (isTipsExit(msg)) return null\n        if (queue.length || isPending) queue.push({ msg, duration })\n        else openVm(msg, duration)\n      }\n    })\n\n    obj.next = function () {\n      isPending = false\n      if (queue.length) {\n        const m = queue.shift()\n        openVm(m.msg, m.duration)\n      }\n    }\n\n    Vue.prototype.$toast = obj\n    window.$toast = obj\n  }\n}\n\nexport default messageCmp\n", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"tips-wrapper\"},[_c('transition',{attrs:{\"name\":\"bg\"}},[_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.showBg),expression:\"showBg\"}],staticClass:\"modal-bg\"})]),_c('transition',{attrs:{\"name\":\"modal\"}},[(_vm.message)?_c('div',{staticClass:\"modal-click-before-choose\"},[_c('div',{staticClass:\"custom-modal-title\"},[_c('h3',[_vm._v(_vm._s(_vm.i18n.t('text_tips')))]),_c('div',{staticClass:\"custom-modal-close\",on:{\"click\":_vm.dismiss}})]),_c('div',{staticClass:\"custom-modal-content\"},[_vm._v(_vm._s(_vm.message))]),_c('div',{staticClass:\"custom-modal-footer\"},[_c('div',{staticClass:\"custom-modal-btn\",on:{\"click\":_vm.dismiss}},[_vm._v(_vm._s(_vm.i18n.t('modalBtnOk')))])])]):_vm._e()])],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <div class=\"tips-wrapper\">\n    <transition name=\"bg\">\n      <div v-show=\"showBg\" class=\"modal-bg\"></div>\n    </transition>\n    <transition name=\"modal\">\n      <div v-if=\"message\" class=\"modal-click-before-choose\">\n        <div class=\"custom-modal-title\">\n          <h3>{{ i18n.t('text_tips') }}</h3>\n          <div class=\"custom-modal-close\" @click=\"dismiss\"></div>\n        </div>\n        <div class=\"custom-modal-content\">{{ message }}</div>\n        <div class=\"custom-modal-footer\">\n          <div class=\"custom-modal-btn\" @click=\"dismiss\">{{ i18n.t('modalBtnOk') }}</div>\n        </div>\n      </div>\n    </transition>\n  </div>\n</template>\n\n<script>\nimport i18n from '@/utils/i18n'\nexport default {\n  name: 'message',\n  data () {\n    return {\n      message: '',\n      showBg: false,\n      cb: '',\n      i18n\n    }\n  },\n  methods: {\n    open (msg, duration, cb) {\n      this.showBg = true\n      this.$nextTick(() => {\n        this.message = msg\n        this.cb = cb\n      })\n    },\n    dismiss () {\n      this.message = ''\n      this.showBg = false\n      // setTimeout(() => { this.cb() }, 300)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@use \"~@/utils/utils.scss\" as utils;\n\n.modal-enter-active,.modal-leave-active{\n  transition: all .3s\n}\n.modal-enter{\n  transform: translate(-50%, -25%) !important;\n  opacity: 0;\n}\n.modal-leave-to{\n  opacity: 0;\n}\n\n.bg-enter-active, .bg-leave-active{\n  transition: opacity .3s;\n}\n.bg-enter, .bg-leave-to{\n  opacity: 0;\n}\n\n.tips-wrapper{\n  position: fixed;\n  z-index: 100;\n}\n\n.modal-bg{\n  background-color: rgba(0,0,0,0.6);\n  height: 100%;\n  width: 100%;\n  position: fixed;\n  left: 0;\n  top: 0;\n  z-index: 100;\n}\n.modal-click-before-choose{\n  position: fixed;\n  left: 50%;\n  top: 50%;\n  transform: translate(-50%,-50%);\n  background-color: #393434;\n  background-image: url(\"~@/assets/common/pop/dialog-bg.png\");\n  background-size: 100% auto;\n  background-repeat: no-repeat;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  z-index: 101;\n  min-height: 200px;\n  width: 670px;\n  padding-bottom: 45px;\n\n  .custom-modal-title{\n    width: 100%;\n    text-align: center;\n\n    h3{\n      height: 80px;\n      line-height: 80px;\n      font-size: 38px;\n      font-family: PingFangSC-Semibold, PingFang SC;\n      font-weight: 600;\n      color: #D6CBB1;\n    }\n\n    .custom-modal-close{\n      right: 4px;\n      top: 4px;\n      position: absolute;\n      z-index: 1;\n      cursor: pointer;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      //background-image: url(\"~@/assets/image/shopping-modal-close.png\");\n      background-size: 20px 20px;\n      background-position: center center;\n      background-repeat: no-repeat;\n    }\n  }\n\n  .custom-modal-content{\n    font-size: 28px;\n    font-family: PingFangSC-Regular, PingFang SC;\n    font-weight: 400;\n    color: #8C8C8C;\n    line-height: 40px;\n    padding: 65px 52px;\n    text-align: center;\n    width: 100%;\n  }\n\n  .custom-modal-footer{\n    display: flex;\n    align-items: center;\n    justify-content: center;\n\n    .custom-modal-btn{\n      background-color: #FF5A00;\n      cursor: pointer;\n      font-size: 31px;\n      font-family: PingFangSC-Regular, PingFang SC;\n      font-weight: 400;\n      color: #FFFFFF;\n      line-height: 86px;\n      padding: 0 68px;\n      border-radius: 8px;\n    }\n  }\n\n  @include utils.setPcContent{\n    background-image: url(\"~@/assets/common/pop/dialog-bg_pc.png\");\n    width: 550PX;\n    min-height: 100PX;\n    padding-bottom: 6PX;\n\n    .custom-modal-title{\n      h3{\n        height: 62PX;\n        line-height: 50PX;\n        font-size: 22PX;\n      }\n    }\n\n    .custom-modal-content{\n      font-size: 18PX;\n      line-height: 25PX;\n      padding: 30PX 77PX;\n    }\n\n    .custom-modal-footer{\n      .custom-modal-btn{\n        font-size: 24PX;\n        line-height: 53PX;\n        height: 53PX;\n        padding: 0 36PX;\n        border-radius: 8PX;\n      }\n    }\n  }\n}\n</style>\n", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./dialog.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./dialog.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./dialog.vue?vue&type=template&id=c106f7fc&scoped=true\"\nimport script from \"./dialog.vue?vue&type=script&lang=js\"\nexport * from \"./dialog.vue?vue&type=script&lang=js\"\nimport style0 from \"./dialog.vue?vue&type=style&index=0&id=c106f7fc&prod&lang=scss&scoped=true\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"c106f7fc\",\n  null\n  \n)\n\nexport default component.exports", "import message from './dialog.vue'\nimport Vue from 'vue'\n\nconst getVmSingleton = function (cmp) {\n  let $vm = null\n  return function () {\n    if ($vm) return $vm\n    else {\n      const Constructor = Vue.extend(cmp)\n      $vm = new Constructor({\n        el: document.createElement('div')\n      })\n      document.body.appendChild($vm.$el)\n      return $vm\n    }\n  }\n}\n\nconst messageCmp = {\n  install (Vue, options) {\n    const vm = getVmSingleton(message)()\n    const obj = {}\n    const queue = []\n    let isPending = false\n\n    const openVm = (msg, duration) => {\n      isPending = true\n      vm.open(msg, duration, obj.next.bind(obj))\n    }\n    const isTipsExit = target => queue.some(item => item.msg === target) || vm.message === target\n\n    obj.show = function (msg, duration = 15000) {\n      if (isTipsExit(msg)) return null\n      if (queue.length || isPending) queue.push({ msg, duration })\n      else openVm(msg, duration)\n    }\n\n    obj.next = function () {\n      isPending = false\n      if (queue.length) {\n        const m = queue.shift()\n        openVm(m.msg, m.duration)\n      }\n    }\n\n    window.$tips = Vue.prototype.$tips = obj\n  }\n}\n\nexport default messageCmp\n", "import Vue from 'vue'\nimport '@/utils/resLoaderHelper'\nimport './utils/webReporter'\nimport './utils/flexible_custom'\nimport './utils/prepareBasicInfo'\nimport App from './App.vue'\nimport router from './router'\nimport store from './store'\nimport i18n from '@/utils/i18n'\nimport VueLazyload from 'vue-lazyload'\nimport loading from '@/components/common/loading/loading'\nimport toast from '@/components/common/toast/toast'\nimport dialog from '@/components/common/dialog/dialog'\nimport VueGtag from 'vue-gtag'\nimport { fixToFixed } from '@/utils/utils'\nimport device from 'current-device'\n\nwindow.isMobile = device.mobile()\nfixToFixed()\nVue.config.productionTip = false\nVue.use(VueGtag, {\n  config: { id: window.$idLoader('gid') }\n})\n\nVue.use(VueLazyload)\nVue.use(loading)\nVue.use(toast)\nVue.use(dialog)\nconsole.log('test', 202507151040)\n\nnew Vue({\n  router,\n  store,\n  i18n,\n  render: (h) => h(App)\n}).$mount('#app')\n", "module.exports = __webpack_public_path__ + \"static/**********/img/sdk2-diamond-dc.d3d9429e.png\";", "module.exports = __webpack_public_path__ + \"static/**********/img/03.8677051a.png\";", "module.exports = __webpack_public_path__ + \"static/**********/img/02.0569826e.jpg\";", "/* RP表示直购礼包 */\nimport { baseKey, functionSwitch } from '@/config/game/defaultConfig'\n\nconst localKey = Object.create(baseKey)\nlocalKey.pageTitle = 'ss-official-website-title'\nconst localSwitch = Object.create(functionSwitch)\n\nlocalSwitch.enableAnimation = true\n\nconst config = {\n  gameinfo: {\n    gameProject: 'ss_global',\n    gameCode: 'SSRP', // 控制api\n    gameId: '30001',\n    whiteChannel: [],\n    blackChannel: [],\n    greyChannel: [],\n    gameName: 'State of Survivals',\n    appGameDeepLinkIos: 'https://sos-universal-link.kingsgroupgames.com',\n    appGameDeepLinkAndroid: 'com.kingsgroup.sos://'\n  },\n  apiParams: {},\n  langKey: localKey,\n  images: {\n    logoPath: require('@/assets/ss/pc/ss-logo.png'),\n    iconDiamond: require('@/assets/ss/ssrp/sdk2-diamond-ss.png')\n  },\n  ids: {\n    gid: 'G-RWBD2X9CPK',\n    appId: 'DAk5Rea1745289938511',\n    secretKey: '116 116 96 104 109 112 99 98 109 79 79 112 80 91 111 108 36 112 51 114 99 114 68 102 82 67 50 106 89 102 74 83'\n  },\n  switch: localSwitch\n}\n\nexport default config\n", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./IosForbidden.vue?vue&type=style&index=0&id=6892a751&prod&scoped=true&lang=scss\"", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ArrearsReminder.vue?vue&type=style&index=0&id=2f1ed20c&prod&scoped=true&lang=scss\"", "module.exports = __webpack_public_path__ + \"static/**********/img/02.e53d9316.png\";", "import EN from '../../langHelper/langJson/en.json'\n\nexport default {\n  config: {\n    // zh_cn: ZHCN,\n    // zh_tw: ZHTW,\n    en: EN\n  }\n}\n", "import Vue from 'vue'\nimport VueI18n from 'vue-i18n'\nimport langConf from '../config/LangConf'\nimport store from '@/store'\n\nVue.use(VueI18n)\n\nconst lang = store.state.urlParams.l || navigator.language || navigator.userLanguage\nlet defaultLang\n\nif (lang.toLowerCase() === 'zh-tw' || lang.toLowerCase() === 'zh_tw') defaultLang = 'zh_tw'\nelse if (lang.startsWith('zh')) defaultLang = 'zh_cn'\nelse defaultLang = lang.split('-')[0]\n\nconst allowLang = ['zh_cn', 'en', 'zh_tw', 'ar', 'de', 'es', 'fr', 'id', 'it', 'ja', 'ko', 'nl', 'pl', 'pt', 'ru', 'sv', 'th', 'tr', 'vi', 'my']\nif (!allowLang.includes(defaultLang)) defaultLang = 'en'\n\nconst i18n = new VueI18n({\n  locale: defaultLang, // 语言标识\n  fallbackLocale: 'en',\n  messages: langConf.config\n})\n\nexport default i18n\n\nexport const langObj = {\n  en: 'English',\n  // zh_cn: '中文',\n  // zh_tw: '繁體中文',\n  ko: '한국어',\n  // sv: 'xxx',\n  // pl:'xxx',\n  // nl:'xxx',\n  // id: 'Bahasa Indonesia',\n  // es: 'Español',\n  ja: '日本語',\n  // tr:'xxx',\n  // vi:'xxx',\n  fr: 'Français',\n  de: 'Deutsch',\n  // it: 'Italiano',\n  ru: 'Русский'\n  // pt: 'Português',\n  // th: 'xxx',\n  // ar: 'xxx',\n  // my: 'xxx',\n}\n", "module.exports = \"data:image/png;base64,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\"", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./CustomDiamond.vue?vue&type=style&index=0&id=6883bc84&prod&scoped=true&lang=scss\"", "/* 1、多语言Key */\nconst baseKey = {\n  /* 钻石轮播弹窗 */\n  howToUseDiamond: 'how-to-use-diamond', // 标题\n  whatIsDiamondTitle: '$gameName-top-diamond-tips', // 描述\n  /* 登录页 */\n  loginPlaceHolder: 'please-input-uid_$gameName',\n  /* 主页  */\n  pageTitle: '$gameName-official-website-title',\n  discount95Tips: 'discount95-change-tips',\n  tokenName: 'token_name',\n  customDiamondLimitTips: 'custom-diamond-limit-$gameName',\n  sdk2_construction_title: 'sdk2_construction_title',\n  sdk2_construction_content: 'sdk2_construction_content'\n}\nfor (const [key, value] of Object.entries(baseKey)) {\n  baseKey[key] = value.replace('$gameName', window.__GAMENAME)\n}\n\nconst functionSwitch = {\n  loginValidation: true,\n  showMobilePolicy: true,\n  showPopPolicy: false,\n  boon: true,\n  fixedDiscountType: '',\n  smallDiamondDoubleDiscount: false,\n  ckoCheckedByDefault: true, // 默认勾选cko记住卡号\n  showPcDiscountTips: false,\n  useThemeFile: false,\n  enableAnimation: false\n}\nexport {\n  baseKey,\n  functionSwitch\n}\n", "export const StorageUtils = (function () {\n  let nameSpaceKey\n  const StorageUtils = {}\n\n  const finalKey = key => nameSpaceKey ? `${nameSpaceKey}_${key}` : key\n\n  StorageUtils.init = function (key) {\n    nameSpaceKey = key\n  }\n\n  StorageUtils.setLocalStorage = function (key, value) {\n    console.log(finalKey(key), value)\n    localStorage.setItem(finalKey(key), value)\n  }\n\n  StorageUtils.setSessionStorage = function (key, value) {\n    sessionStorage.setItem(finalKey(key), value)\n  }\n\n  StorageUtils.getLocalStorage = function (key) {\n    return localStorage.getItem(finalKey(key))\n  }\n\n  StorageUtils.getSessionStorage = function (key) {\n    return sessionStorage.getItem(finalKey(key))\n  }\n\n  return StorageUtils\n})()\n", "export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./dialog.vue?vue&type=style&index=0&id=c106f7fc&prod&lang=scss&scoped=true\"", "/* RP表示直购礼包 */\nimport { baseKey, functionSwitch } from '@/config/game/defaultConfig'\n\nconst localKey = Object.create(baseKey)\nlocalKey.pageTitle = 'ssd-official-website-title'\nlocalKey.tokenName = 'token_name_ssd'\nconst localSwitch = Object.create(functionSwitch)\n\nlocalSwitch.enableAnimation = true\n\nconst config = {\n  gameinfo: {\n    gameProject: 'ts_global',\n    gameCode: 'SSD', // 控制api\n    gameId: '8612',\n    whiteChannel: [],\n    blackChannel: [],\n    greyChannel: [],\n    gameName: 'Tiles Survive',\n    appGameDeepLinkIos: 'https://ts-universal-link.kingsgroupgames.com',\n    appGameDeepLinkAndroid: 'com.funplus.ts.global://'\n  },\n  apiParams: {},\n  langKey: localKey,\n  images: {\n    logoPath: require('@/assets/ss/ssd/pc/logo.png'),\n    iconDiamond: require('@/assets/ss/ssd/ssdrp/sdk2-diamond-ssd.png')\n  },\n  ids: {\n    gid: 'G-RWBD2X9CPK',\n    appId: 'DAk5Rea1745289938511',\n    secretKey: '117 116 96 104 109 112 99 98 109 79 79 112 80 91 111 108 36 112 51 114 99 114 68 102 82 67 50 106 89 102 74 83'\n  },\n  switch: localSwitch\n}\n\nexport default config\n", "module.exports = __webpack_public_path__ + \"static/**********/img/login-validate-game-scrrencut.d00e70f9.jpeg\";", "module.exports = \"data:image/png;base64,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\"", "module.exports = __webpack_public_path__ + \"static/**********/img/login-validate-game-scrrencut.0bcac712.png\";", "module.exports = __webpack_public_path__ + \"static/**********/img/02.0569826e.jpg\";", "module.exports = __webpack_public_path__ + \"static/**********/img/find_ss_uid.271922f5.jpeg\";", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./container.vue?vue&type=style&index=0&id=324a5e70&prod&scoped=true&lang=scss\"", "module.exports = __webpack_public_path__ + \"static/**********/img/03.11adf5fe.jpeg\";", "import { post, get } from '@/server/http'\n\nexport const getAmeDo = params => get('/ame/do', params)\nexport const getSSProductList = params => post('/api/sdk/coin_products', params)\nexport const getCurrencyByIp = params => get('/token/getIpCurrency', params)\nexport const changeFpidToOpenid = params => post('/account/fpid2openid', params)\nexport const fetchUidList = params => post('/account/uid_list', params)\n\n/* 代币 */\nexport const getUserInfoForToken = params => post('/account/store/u', params)\nexport const getActivityListForToken = params => post('/token/act/init', params)\n\nexport const ameDoByGet = params => get('/ame/do', params)\nexport const ameHoldByGet = params => get('/ame/hold', params)\nexport const ameDoByGetCommon = params => get('/ameCommon/do', params)\n\nexport const getCommonInfo = params => post('/token/common/info', params)\nexport const sendCode = params => post('/account/store/send_code', params)\nexport const checkCode = params => post('/account/store/check_code', params)\nexport const toggleCouponType = params => post('/token/act/coupon/change/type', params)\nexport const toggleCouponSingle = params => post('/token/act/coupon/change/only_type', params)\n\nexport const getWxOpenid = wxCode => get('/ame/do', { p0: 'web', p1: 20, p2: 1113, p3: 'api', wx_code: wxCode })\n\nconst BasicRequestPath = {\n  productList: '/token/products',\n  channelList: '/token/channels',\n  placeOrder: '/token/place_order',\n  cardPlaceOrder: 'token/point_card_place_order',\n  orderDetail: '/token/order',\n  redirectProduct: '/token/pay_sdk/products',\n  lastChosenChannel: '/token/user_pay_info'\n}\nconst finalPath = Object.create(BasicRequestPath)\n\nswitch (window.__GAMENAME) {\n  case 'koaCn': {\n    BasicRequestPath.productList = '/token/cn/products'\n    BasicRequestPath.channelList = '/token/cn/channels'\n    break\n  }\n  case 'ssv': case 'ssv2': {\n    BasicRequestPath.productList = '/token/store/coin_products'\n    BasicRequestPath.channelList = '/token/store/channels'\n    BasicRequestPath.placeOrder = '/token/store/coin_place_order'\n    BasicRequestPath.cardPlaceOrder = '/token/store/point_card_place_order'\n    BasicRequestPath.orderDetail = '/token/sdk/order'\n    BasicRequestPath.lastChosenChannel = '/token/sdk/user_pay_info'\n    break\n  }\n  case 'foundation': case 'ssRP': case 'ssCP': case 'stCP': case 'mcCP': case 'gogCP': case 'romCP': case 'stRP': case 'ssdRP': case 'moRP': case 'koaRP': {\n    BasicRequestPath.redirectProduct = '/token/product'\n  }\n}\n\nexport const getTokenList = params => post(finalPath.productList, params)\nexport const getRedirectProductList = params => post(finalPath.redirectProduct, params)\nexport const getTokenChannelList = params => post(finalPath.channelList, params)\nexport const placeOrderToken = params => post(finalPath.placeOrder, params)\nexport const placeOrderCard = params => post(finalPath.cardPlaceOrder, params)\nexport const getTokenOrderDetails = params => post(finalPath.orderDetail, params)\nexport const getLastChosenChannel = params => get(finalPath.lastChosenChannel, params)\n", "import axios from 'axios'\nimport store from '@/store'\nimport i18n from '@/utils/i18n'\n\nfunction makeUpCommonParams (params) {\n  const isOrderPage = location.pathname.includes('/order')\n  const state = store.state\n  const userinfo = isOrderPage ? state.orderPage.userinfo : state.userinfo\n  const gameinfo = state.gameinfo\n\n  if (!params) params = {}\n  params.game_id = +gameinfo.gameId\n  params.game_project = gameinfo.gameProject\n  params.source = 'web'\n\n  if (state.country) params.country = state.country\n  if (state.currency) params.currency = state.currency\n\n  if (!params.openid && userinfo.openid) params.openid = userinfo.openid\n  if (!params.uid && userinfo.uid) params.uid = userinfo.uid\n\n  if (!isOrderPage) {\n    const localOpenid = localStorage.getItem('openid') || ''\n    if (!params.openid && localOpenid) {\n      params.openid = localOpenid\n    }\n  }\n\n  if (state.isPCSDK || state.IS_CHECKOUT_SDK) params.source = store.state.urlParams.s\n  return params\n}\nfunction transformUrl (url = '') {\n  const isCn = store.state.gameinfo.isCn\n  const Game = isCn ? `${store.state.gameinfo.gameCode}CN` : store.state.gameinfo.gameCode\n\n  if (url.startsWith('/ameCommon')) return url.replace('/ameCommon', process.env.VUE_APP_PREFIX_AME)\n  if (url.startsWith('/ame')) return url.replace('/ame', (process.env['VUE_APP_PREFIX_AME_' + Game] || process.env.VUE_APP_PREFIX_AME))\n  if (url.startsWith('/account')) return url.replace('/account', (process.env['VUE_APP_PREFIX_ACCOUNT_' + Game] || process.env.VUE_APP_PREFIX_ACCOUNT))\n  if (url.startsWith('/api')) return url.replace('/api', process.env['VUE_APP_PREFIX_API_' + Game])\n  if (url.startsWith('/token')) return url.replace('/token', process.env['VUE_APP_PREFIX_TOKEN_' + Game])\n  return url\n}\n\nconst service = axios.create({ timeout: 10000 })\nservice.interceptors.request.use(\n  config => {\n    if (config.method === 'post') config.data = makeUpCommonParams(config.data)\n    else config.params = makeUpCommonParams(config.params)\n\n    // if (process.env.NODE_ENV !== 'development') {\n    // 本地开发走转发\n    config.url = transformUrl(config.url)\n    // }\n\n    const localCountry = sessionStorage.getItem('localCountry') || store.state.urlParams.localCountry\n    if (localCountry) {\n      const contact = config.url.includes('?') ? '&' : '?'\n      config.url += `${contact}country=${localCountry}`\n    }\n\n    const localAccountEnv = sessionStorage.getItem('localAccountEnv') || store.state.urlParams.localAccountEnv\n    if (localAccountEnv) {\n      const prefix = config.url.includes('?') ? '&' : '?'\n      config.url += (prefix + `gameServerEnv=${localAccountEnv}`)\n    }\n\n    if (config.url.includes('/account/store/user')) {\n      const { uid, openid } = config.data\n      if (openid && uid) delete config.data.openid\n    }\n\n    return config\n  },\n  error => {\n    window.$toast.err(i18n.t('network_err'))\n    return Promise.reject(error)\n  }\n)\nservice.interceptors.response.use(\n  response => {\n    //     try {\n    //       if (response.status !== 200 || ![0, 2].includes(response.data.code)) {\n    //         console.error(\n    //           `\n    // ${response.config.url}\n    // ${response.config.data}\n    // ${JSON.stringify(response.data)}\n    //           `\n    //         )\n    //       }\n    //     } catch (e) {\n    //       console.error(e.message)\n    //     }\n    return response.data\n  },\n  error => {\n    const isLastRequest = !(error.config.url || '').includes('try=1')\n    if (isLastRequest) window.$toast.err(i18n.t('network_err'))\n    return Promise.reject(error)\n  }\n)\n\nasync function requestController (method, url, payload) {\n  const needRetry = url.startsWith('/token/') || url.startsWith('/account/')\n\n  const requestConfig = { method, url }\n  if (method === 'get') requestConfig.params = payload\n  if (method === 'post') requestConfig.data = payload\n\n  if (needRetry) {\n    requestConfig.url = `${url}?try=1`\n    return new Promise((resolve, reject) => {\n      console.log(`第1次请求:${requestConfig.url}`)\n      service(requestConfig)\n        .then(res => resolve(res))\n        .catch(() => new Promise((resolve, reject) => setTimeout(reject, 3000)))\n        // 第二次请求\n        .catch(() => {\n          console.log(`第2次请求:${url}`)\n          return service(requestConfig)\n        })\n        .then(res => resolve(res))\n        // 第三次请求\n        .catch(() => {\n          console.log(`第3次请求:${url}`)\n          // requestConfig.url = url.replace('/api', '/apiBack')\n          return service(requestConfig)\n        })\n        .then(res => resolve(res))\n        .catch(err => reject(err))\n    })\n  }\n\n  return service(requestConfig)\n}\n\nconst get = async (url, params) => requestController('get', url, params)\nconst post = async (url, data) => requestController('post', url, data)\n\nexport { get, post, makeUpCommonParams, service }\n", "/* RP表示直购礼包 */\nimport { baseKey, functionSwitch } from '@/config/game/defaultConfig'\n\nconst localKey = Object.create(baseKey)\nlocalKey.tokenName = 'token_name_dc'\nlocalKey.pageTitle = 'dc-official-website-title'\n\nconst localSwitch = Object.create(functionSwitch)\n\nlocalSwitch.enableAnimation = true\n\nconst config = {\n  gameinfo: {\n    gameProject: 'dcdl_global',\n    gameCode: 'DC',\n    gameId: '888888',\n    whiteChannel: [],\n    blackChannel: [],\n    greyChannel: [],\n    gameName: 'DC: Dark Legion',\n    appGameDeepLinkIos: 'https://dc-global-universal-link.kingsgroupgames.com',\n    appGameDeepLinkAndroid: 'com.funplus.ts.global://'\n  },\n  apiParams: {},\n  langKey: localKey,\n  images: {\n    logoPath: 'https://kg-web-cdn.akamaized.net/online/user-platform-web/web-pay-hub/dist_online/static/1743436800/img/logo.d522842e.png',\n    iconDiamond: require('@/assets/dc/rp/sdk2-diamond-dc.png')\n  },\n  ids: {\n    gid: 'G-3FME4BZMSF',\n    appId: '7HjTjCy1739172217783',\n    secretKey: '101 100 101 109 96 104 109 112 99 98 109 112 80 91 111 108 36 112 51 114 99 114 68 102 82 67 50 106 89 102 74 83'\n  },\n  switch: localSwitch\n}\n\nexport default config\n", "module.exports = __webpack_public_path__ + \"static/**********/img/sample_koa.376544b6.png\";", "module.exports = __webpack_public_path__ + \"static/**********/img/logo.a739303b.png\";", "module.exports = __webpack_public_path__ + \"static/**********/img/01.eee859f6.png\";", "export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./toast.vue?vue&type=style&index=0&id=c502de0a&prod&lang=scss&scoped=true\"", "module.exports = \"data:image/png;base64,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\"", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('section',{staticClass:\"pop-container\"},[_vm._t(\"default\")],2)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <section class=\"pop-container\">\n    <slot></slot>\n  </section>\n</template>\n\n<script>\nexport default {\n  name: 'containerV2'\n}\n</script>\n\n<style scoped lang=\"scss\">\n@use \"~@/utils/utils.scss\" as utils;\n\n.pop-container{\n  position: fixed;\n  left: 50%;\n  top: 50%;\n  transform: translate(-50%, -50%);\n  z-index: 100001;\n  display: inline-block;\n  @include utils.setPropByBp(\n    $m: (width: calc(100vw - 48px), max-width: 750px),\n    $p: (width: 600px),\n  );\n}\n</style>\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./containerV2.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./containerV2.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./containerV2.vue?vue&type=template&id=1671dd6e&scoped=true\"\nimport script from \"./containerV2.vue?vue&type=script&lang=js\"\nexport * from \"./containerV2.vue?vue&type=script&lang=js\"\nimport style0 from \"./containerV2.vue?vue&type=style&index=0&id=1671dd6e&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1671dd6e\",\n  null\n  \n)\n\nexport default component.exports", "module.exports = \"data:image/png;base64,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\"", "module.exports = __webpack_public_path__ + \"static/**********/img/01.e10e50c5.jpg\";", "/* RP表示直购礼包 */\nimport { baseKey, functionSwitch } from '@/config/game/defaultConfig'\n\nconst localKey = Object.create(baseKey)\nlocalKey.pageTitle = 'mo-official-website-title'\nlocalKey.tokenName = 'token_name_mo'\nlocalKey.sdk2_construction_content = 'sdk2_construction_content_mo'\nconst localSwitch = Object.create(functionSwitch)\n\nlocalSwitch.enableAnimation = true\n\nconst config = {\n  gameinfo: {\n    gameProject: 'mo_global',\n    gameCode: 'MO', // 控制api\n    gameId: '70001',\n    whiteChannel: [],\n    blackChannel: [],\n    greyChannel: [],\n    gameName: 'Sea of Conquest',\n    appGameDeepLinkIos: 'https://mo-universal-link.kingsgroupgames.com',\n    appGameDeepLinkAndroid: 'com.seaofconquest.global://'\n  },\n  apiParams: {},\n  langKey: localKey,\n  images: {\n    logoPath: 'https://kg-web-cdn.akamaized.net/antiaAutoOnline/user-platform-web/web-pay-unique/dist_online/static/1745563516/img/pc-main-left-logo.004959d4.png',\n    iconDiamond: require('@/assets/mo/morp/sdk2-diamond-mo.png')\n  },\n  ids: {\n    gid: 'G-XXHDPBZFJG',\n    appId: 'C4GsWnM1712815808618',\n    secretKey: '110 112 96 104 109 112 99 98 109 79 79 112 80 91 111 108 36 112 51 114 99 114 68 102 82 67 50 106 89 102 74 83'\n  },\n  switch: localSwitch\n}\n\nexport default config\n", "/* RP表示直购礼包 */\nimport { baseKey, functionSwitch } from '@/config/game/defaultConfig'\n\nconst localKey = Object.create(baseKey)\nlocalKey.pageTitle = 'koa-official-website-title'\nconst localSwitch = Object.create(functionSwitch)\n\nlocalSwitch.enableAnimation = true\n\nconst config = {\n  gameinfo: {\n    gameProject: 'koa_global',\n    gameCode: 'KOA', // 控制api\n    gameId: '2031',\n    whiteChannel: [],\n    blackChannel: [],\n    greyChannel: [],\n    gameName: 'King of Avalon',\n    appGameDeepLinkIos: 'https://koa-universal-link.kingsgroupgames.com',\n    appGameDeepLinkAndroid: 'com.diandian.kingofavalon://'\n  },\n  apiParams: {},\n  langKey: localKey,\n  images: {\n    logoPath: 'https://kg-web-cdn.akamaized.net/online/user-platform-web/web-pay-hub/dist_online/static/1749024030/img/koa-logo.825114b1.png',\n    iconDiamond: require('@/assets/koa/rp/sdk2-diamond-koa.png')\n  },\n  ids: {\n    gid: 'G-LB15MXTLZK',\n    appId: 'e3Xnhny1706589599129',\n    secretKey: '108 112 98 96 104 109 112 99 98 109 79 112 80 91 111 108 36 112 51 114 99 114 68 102 82 67 50 106 89 102 74 83'\n  },\n  switch: localSwitch\n}\n\nexport default config\n", "export const OrderPageOpenidKey = 'OrderPageOpenidKey'\nexport const OrderPageTokenKey = 'OrderPageTokenKey'\nexport const OrderPageLangKey = 'OrderPageLangKey'\nexport const OrderUrlParamsKey = 'OrderUrlParams'.toUpperCase()\n", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./AvatarBonusPop.vue?vue&type=style&index=0&id=5fc05c74&prod&scoped=true&lang=scss\"", "module.exports = __webpack_public_path__ + \"static/**********/img/login-validate-game-scrrencut.d51fabbc.jpeg\";", "var map = {\n\t\"./dcRP.js\": \"b8d2\",\n\t\"./koaRP.js\": \"dcc0\",\n\t\"./moRP.js\": \"d708\",\n\t\"./ssRP.js\": \"642c\",\n\t\"./ssdRP.js\": \"9e62\",\n\t\"./stRP.js\": \"3d74\"\n};\n\n\nfunction webpackContext(req) {\n\tvar id = webpackContextResolve(req);\n\treturn __webpack_require__(id);\n}\nfunction webpackContextResolve(req) {\n\tif(!__webpack_require__.o(map, req)) {\n\t\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\t\te.code = 'MODULE_NOT_FOUND';\n\t\tthrow e;\n\t}\n\treturn map[req];\n}\nwebpackContext.keys = function webpackContextKeys() {\n\treturn Object.keys(map);\n};\nwebpackContext.resolve = webpackContextResolve;\nmodule.exports = webpackContext;\nwebpackContext.id = \"e3e5\";", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./containerV2.vue?vue&type=style&index=0&id=1671dd6e&prod&scoped=true&lang=scss\"", "module.exports = __webpack_public_path__ + \"static/**********/img/ssv-01.1c5b48ec.jpeg\";", "export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./DailyReward.vue?vue&type=style&index=0&id=c2ab6286&prod&scoped=true&lang=scss\"", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('section',{staticClass:\"pop-container pop-container-v1\",class:[_vm.size, _vm.$gameName]},[(!_vm.hideHeader)?_c('h3',{staticClass:\"title\"},[_vm._v(_vm._s(_vm.option.title || _vm.title)+\" \"),(!_vm.hideClose)?_c('i',{on:{\"click\":function($event){return _vm.$root.$emit('closePop')}}}):_vm._e()]):_vm._e(),(!_vm.customContent)?_c('div',{staticClass:\"content\"},[_vm._t(\"default\")],2):_vm._t(\"default\"),(!_vm.hideFooter)?_c('div',{staticClass:\"footer-wrapper\"},[_vm._t(\"footerBtn\",function(){return [_c('div',{staticClass:\"btn-confirm click-btn\",on:{\"click\":_vm.confirmClick}},[_vm._v(_vm._s(_vm.option.confirmBtnTxt || _vm.$t('modalBtnOk')))])]})],2):_vm._e()],2)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <section class=\"pop-container pop-container-v1\" :class=\"[size, $gameName]\">\n    <h3 v-if=\"!hideHeader\" class=\"title\">{{ option.title || title }} <i v-if=\"!hideClose\" @click=\"$root.$emit('closePop')\"></i></h3>\n    <div v-if=\"!customContent\" class=\"content\">\n      <slot></slot>\n    </div>\n    <slot v-else></slot>\n    <div v-if=\"!hideFooter\" class=\"footer-wrapper\">\n      <slot name=\"footerBtn\">\n        <div class=\"btn-confirm click-btn\" @click=\"confirmClick\">{{ option.confirmBtnTxt || $t('modalBtnOk') }}</div>\n      </slot>\n    </div>\n  </section>\n</template>\n\n<script>\nexport default {\n  name: 'container',\n  props: {\n    title: String,\n    option: {\n      type: Object,\n      default: () => ({})\n    },\n    hideClose: <PERSON>olean,\n    hideHeader: <PERSON><PERSON><PERSON>,\n    hideFooter: <PERSON><PERSON><PERSON>,\n    customContent: Boolean,\n    size: String,\n    customClass: String\n  },\n  methods: {\n    confirmClick () {\n      if (this.option.confirmBtnCb) this.option.confirmBtnCb()\n      this.$root.$emit('closePop')\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n@use \"~@/utils/utils.scss\" as utils;\n\n.pop-container{\n  background-color: #383838;\n  position: fixed;\n  left: 50%;\n  top: 50%;\n  transform: translate(-50%, -50%);\n  z-index: 100001;\n  display: inline-block;\n  padding: 30px 0 42px;\n  width: calc(100% - 48px);\n  max-width: 750px;\n\n  .title{\n    font-family: PingFangSC-Semibold, PingFang SC;\n    font-weight: 600;\n    color: #FFFFFF;\n    line-height: 40px;\n    letter-spacing: 1px;\n    position: relative;\n    font-size: 28px;\n\n    i{\n      @include utils.bgCenter('koa/pop/pop-close.png', 36px, 36px);\n      display: inline-block;\n      position: absolute;\n      cursor: pointer;\n      top:50%;\n      transform: translateY(-50%);\n      right: 30px;\n    }\n  }\n\n  .content{\n    box-sizing: border-box;\n    overflow-y: auto;\n    font-family: PingFangSC-Regular, PingFang SC;\n    font-weight: 400;\n    color: #919090;\n    position: relative;\n    margin-top: 30px;\n    min-height: 100px;\n    max-height: 60vh;\n    padding: 0 60px;\n    font-size: 24px;\n    line-height: 33px;\n  }\n\n  .btn-confirm {\n    font-family: PingFangSC-Semibold, PingFang SC;\n    font-weight: 600;\n    cursor: pointer;\n    line-height: 72px;\n    width: 220px;\n    height: 72px;\n    margin: 28px auto 0;\n    background: linear-gradient(180deg, #F6E190 0%, #CBA455 100%);\n    @include utils.flexCenter;\n    font-size: 28px;\n  }\n}\n@include utils.setPcContent{\n  .pop-container{\n    padding: 27px 0 30px;\n    width: 654px;\n\n    .title{\n      font-size: 22px;\n      line-height: 29px;\n      max-width: 90%;\n      margin: 0 auto;\n\n      i{\n        right: 0;\n        @include utils.bgCenter('koa/pop/pop-close.png', 25px, 25px);\n      }\n    }\n\n    .content{\n      margin-top: 25px;\n      min-height: 80px;\n      max-height: 60vh;\n      padding: 0 39px;\n      font-size: 16px;\n      line-height: 21px\n    }\n\n    .btn-confirm{\n      font-size: 24px;\n      margin: 20px auto 0;\n\n      width: 220px;\n      height: 60px;\n    }\n  }\n}\n\n.pop-container.dc{\n  background: #C3CBE1;\n  .title{\n    @extend .dc-stroke;\n    line-height: 1.2;\n\n    i{\n      @include utils.bgCenterForDC('coupon/pop-close.png', 36px, 36px);\n    }\n  }\n  .btn-confirm {\n    color: #F4FBFF;\n    font-size: 28px;\n    white-space: nowrap;\n    padding: 0;\n    width: 220px;\n    height: 72px;\n    @extend .dc-btn-decoration;\n    @include utils.flexCenter;\n    @extend .dc-stroke;\n  }\n\n  @include utils.setPcContent{\n    .title{\n      i{\n        @include utils.bgCenterForDC('coupon/pop-close.png', 30px, 30px);\n      }\n    }\n\n    .btn-confirm {\n      color: #F4FBFF;\n      font-size: 24px;\n      white-space: nowrap;\n      padding: 0;\n      width: 220px;\n      height: 60px;\n    }\n  }\n}\n.pop-container.ssv{\n  .btn-confirm {\n    background: #FF5E0F;\n    font-size: 30px;\n    line-height: 72px;\n    padding: 0 79px;\n    height: 72px;\n    border-radius: 8px;\n    margin: 28px auto 0;\n    color: #FFFFFF;\n  }\n\n  @include utils.setPcContent{\n    .btn-confirm {\n      font-size: 24px;\n      line-height: 53px;\n      padding: 0 59px;\n      height: 53px;\n      border-radius: 8px;\n      margin: 20px auto 0\n    }\n  }\n}\n.pop-container.ssv2{\n  .btn-confirm{\n    color: #FFFFFF;\n    background: #FF5E0F;\n    border-radius: 8px\n  }\n}\n</style>\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./container.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./container.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./container.vue?vue&type=template&id=324a5e70&scoped=true\"\nimport script from \"./container.vue?vue&type=script&lang=js\"\nexport * from \"./container.vue?vue&type=script&lang=js\"\nimport style0 from \"./container.vue?vue&type=style&index=0&id=324a5e70&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"324a5e70\",\n  null\n  \n)\n\nexport default component.exports", "module.exports = __webpack_public_path__ + \"static/**********/img/03.11adf5fe.jpeg\";", "module.exports = __webpack_public_path__ + \"static/**********/img/uid-tips.67811b01.jpeg\";", "module.exports = __webpack_public_path__ + \"static/**********/img/01.e6d348f6.jpeg\";", "import CryptoJS from 'crypto-js'\nexport const urlHelper = (search) => {\n  search = search || window.location.search.slice(1)\n  const urlPrams = {}\n  const searchArr = search.split('&')\n  for (const value of searchArr.values()) {\n    const index = value.indexOf('=')\n    const k = value.slice(0, index)\n    urlPrams[k] = decodeURIComponent(value.slice(index + 1))\n  }\n  return urlPrams\n}\n\nexport const getUrlParams = (function () {\n  let urlPrams = {}\n  return function () {\n    // order页逻辑和主站隔离\n    if (location.href.includes('/order')) return {}\n    if (JSON.stringify(urlPrams) === '{}') {\n      const tempUrl = urlHelper()\n      const localParams = JSON.parse(localStorage.getItem('urlParams') || '{}')\n      const sessionParams = JSON.parse(sessionStorage.getItem('urlParams') || '{}')\n\n      /* ruapp优先级最高 */\n      if (localParams.utm_campaign === 'ruapp' && (window.__GAMENAME && window.__GAMENAME.startsWith('ss'))) tempUrl.utm_campaign = 'ruapp' // ss 特有\n\n      if (location.pathname === '/' || (window.__ROUTERPATH && window.__ROUTERPATH.startsWith(location.pathname))) {\n        /* 打开/sdk/pay 视为第一次进入页面 */\n        localStorage.setItem('urlParams', JSON.stringify(Object.assign({}, localParams, tempUrl)))\n      } else {\n        sessionStorage.setItem('urlParams', JSON.stringify(Object.assign({}, sessionParams, tempUrl)))\n      }\n\n      urlPrams = Object.assign({}, localParams, sessionParams, tempUrl)\n      if ('openid' in tempUrl) localStorage.setItem('openid', tempUrl.openid)\n      if (tempUrl.event === 'boon') watchDisplayMode()\n    }\n    return urlPrams\n  }\n}())\n\nexport const isArZone = key => ['VND'].includes(key)\n\nexport const calcDisplayMode = () => {\n  const isStandalone = window.matchMedia('(display-mode: standalone)').matches\n  if (document.referrer.startsWith('android-app://')) {\n    return 'twa'\n  } else if (navigator.standalone || isStandalone) {\n    return 'standalone'\n  }\n  return 'browser'\n}\nexport const getPWADisplayMode = calcDisplayMode()\nvar watchDisplayMode = function () {\n  const interval = setInterval(() => {\n    if (calcDisplayMode() === 'standalone') {\n      console.log('install successful!')\n      clearInterval(interval)\n      // window.location.reload()\n      if (window.$event && window.$event.$emit) window.$event.$emit('installSuccessful')\n    }\n  }, 3000)\n}\n\nexport const getPlatform = (() => {\n  const ua = navigator.userAgent\n  const isIos = ua.match(/\\(i[^;]+;( U;)? CPU.+Mac OS X/)\n  const isAndroid = ua.indexOf('Android') > -1 || ua.indexOf('Adr') > -1\n  if (isIos) return 'ios'\n  if (isAndroid) return 'android'\n  return 'pc'\n})()\n\nexport function randomString (len) {\n  len = len || 18\n  const chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz123456789'\n  const maxPos = chars.length\n  let pwd = ''\n  for (let i = 0; i < len; i++) {\n    pwd = pwd + chars.charAt(Math.floor(Math.random() * maxPos))\n  }\n  return pwd + new Date().getTime()\n}\n\nexport const navToLogin = (l = '', gameId = '') => {\n  const lang = (l || '').replace('_', '-')\n  const returnUrl = location.origin + location.pathname\n\n  window.location.href = `${process.env.VUE_APP_LOGIN_PAGE_URL}?refer=${encodeURIComponent(returnUrl)}&source_id=1&ng=token&lang=${lang}&gid=${gameId}`\n}\n\nexport const priceHelper = function (price, currency) {\n  price = Number(price.toFixed(4))\n  const arr = ['BDT', 'CLP', 'COP', 'CRC', 'DZD', 'HUF', 'IDR', 'INR', 'IQD', 'JPY', 'KES', 'KRW', 'KZT', 'LBP', 'LKR', 'MMK', 'NGN', 'PHP', 'PKR', 'PYG', 'RSD', 'RUB', 'THB', 'TWD', 'TZS', 'VND']\n  if (arr.includes(currency)) return Math.ceil(price)\n  else return +price.toFixed(2)\n}\n\nexport const fixToFixed = function () {\n  const origin = Number.prototype.toFixed\n  // eslint-disable-next-line no-extend-native\n  Number.prototype.toFixed = function (n) {\n    const number = this\n    if (n > 20 || n < 0) throw new RangeError('toFixed() digits argument must be between 0 and 20')\n    if (Number.isNaN(number)) throw new TypeError(number + '.toFixed() is not a function')\n    // 如果忽略该参数，则默认为 0，进行四舍五入，不包括小数部分\n    if (n === undefined || n === 0) return Math.round(number).toString()\n\n    return origin.call(\n      Math.round((this + Number.EPSILON) * Math.pow(10, n)) / Math.pow(10, n),\n      n\n    )\n  }\n}\n\nexport const onceADay = function (storageKey, range = [], useLocalDate = false) {\n  const date = new Date(Date.now() + (useLocalDate ? 0 : new Date().getTimezoneOffset() * 60 * 1000))\n  const tempDate = `${date.getFullYear()}/${date.getMonth() + 1}/${date.getDate()}`\n  const popHistory = JSON.parse(localStorage.getItem(storageKey) || '[]')\n  const showPopup = !popHistory.includes(tempDate) && (!range.length || range.includes(tempDate))\n  if (showPopup) {\n    popHistory.push(tempDate)\n    localStorage.setItem(storageKey, JSON.stringify(popHistory))\n  }\n  return showPopup\n}\n\nexport const numberFormat = (num) => {\n  const digits = 2\n  const lookup = [\n    { value: 1, symbol: '' },\n    { value: 1e3, symbol: 'K' },\n    { value: 1e6, symbol: 'M' },\n    { value: 1e9, symbol: 'G' },\n    { value: 1e12, symbol: 'T' },\n    { value: 1e15, symbol: 'P' },\n    { value: 1e18, symbol: 'E' }\n  ]\n  const rx = /\\.0+$|(\\.[0-9]*[1-9])0+$/\n  var item = lookup.slice().reverse().find(function (item) {\n    return num >= item.value\n  })\n  return item ? (num / item.value).toFixed(digits).replace(rx, '$1') + item.symbol : '0'\n}\n\nexport const isActivityTime = (value) => {\n  const { start, end } = value\n  const now = new Date().getTime()\n  if (start && end) {\n    const utcDate1 = new Date(Date.UTC(start.year, start.month - 1, start.day, 0, 0, 0)).getTime()\n    const utcDate2 = new Date(Date.UTC(end.year, end.month - 1, end.day, 0, 0, 0)).getTime()\n    if (now > utcDate1 && now < utcDate2) {\n      return true\n    }\n    return false\n  } else if (start && !end) {\n    const utcDate1 = new Date(Date.UTC(start.year, start.month - 1, start.day, 0, 0, 0)).getTime()\n    if (now > utcDate1) return true\n    return false\n  } else {\n    const utcDate2 = new Date(Date.UTC(end.year, end.month - 1, end.day, 0, 0, 0)).getTime()\n    if (now < utcDate2) {\n      return true\n    }\n    return false\n  }\n}\n\nexport function dealSmDeviceId (cb) {\n  let smDeviceId = ''\n  let smDeviceIdReady = false\n  if (window.SMSdk && window.SMSdk.ready) {\n    window.SMSdk.ready(function () {\n      smDeviceId = window.SMSdk.getDeviceId ? window.SMSdk.getDeviceId() : smDeviceId\n\n      if (!smDeviceIdReady) {\n        smDeviceIdReady = true\n        cb && cb(smDeviceId)\n      }\n    })\n  } else {\n    console.error('数美 sdk is not ready')\n    cb && cb(smDeviceId)\n  }\n}\n\nexport function getBrowserInfo () {\n  return {\n    windowSize: '05',\n    acceptHeader: 'text/html, application/xhtml+xml, application/xml;q=0.9, image/webp, image/apng, *;q=0.8',\n    colorDepth: screen.colorDepth,\n    screenHeight: document.documentElement.clientHeight || document.body.clientHeight,\n    jetLag: new Date().getTimezoneOffset(),\n    userAgent: navigator.userAgent,\n    screenWidth: document.documentElement.clientWidth || document.body.clientWidth,\n    javaEnabled: true,\n    javaScriptEnabled: true\n  }\n}\n\nexport function decryptAES (ciphertextStr, secretKey) {\n  const url = secretKey\n  function deobfuscate (obfuscatedStr) {\n    return obfuscatedStr.split(' ').map(code => String.fromCharCode(code - 1)).join('')\n  }\n  var decrypted = CryptoJS.AES.decrypt(\n    ciphertextStr,\n    CryptoJS.enc.Utf8.parse(deobfuscate(url)),\n    {\n      iv: CryptoJS.enc.Utf8.parse(deobfuscate(url).substr(0, 16)),\n      mode: CryptoJS.mode.CBC,\n      padding: CryptoJS.pad.Pkcs7\n    }\n  )\n  var decryptedStr = decrypted.toString(CryptoJS.enc.Utf8)\n\n  return decryptedStr\n}\nexport function encryptAES (ciphertextStr) {\n  const url = '110 106 111 106 38 37 112 49 112 79 79 112 80 91 111 108 36 112 51 114 99 114 68 102 82 67 50 106 89 102 74 83'\n  function deobfuscate (obfuscatedStr) {\n    return obfuscatedStr.split(' ').map(code => String.fromCharCode(code - 1)).join('')\n  }\n\n  const encryptd = CryptoJS.AES.encrypt(\n    ciphertextStr,\n    CryptoJS.enc.Utf8.parse(deobfuscate(url)),\n    {\n      iv: CryptoJS.enc.Utf8.parse(deobfuscate(url).substr(0, 16)),\n      mode: CryptoJS.mode.CBC,\n      padding: CryptoJS.pad.Pkcs7\n    }\n  )\n\n  return encryptd.toString()\n}\n\nexport const isWx = (() => {\n  var ua = navigator.userAgent.toLowerCase()\n  var isWeixin = ua.indexOf('micromessenger') !== -1\n  return isWeixin\n})()\n\nexport async function jumpToUrl (jumpUrl = ''){\n  // const urlParams = window._getInitUrlParams()\n  const store = (await import('@/store')).default\n\n  if (store.state.userinfo.openid) {\n    const contact = jumpUrl.indexOf('?') >= 0 ? '&' : '?'\n    jumpUrl += `${contact}openid=${encodeURIComponent(store.state.userinfo.openid)}`\n  }\n  window.open(jumpUrl, '_blank')\n}\n", "var map = {\n\t\"./ar.json\": [\n\t\t\"a91a\",\n\t\t\"asyncTxt/0\"\n\t],\n\t\"./de.json\": [\n\t\t\"559d\",\n\t\t\"asyncTxt/1\"\n\t],\n\t\"./en.json\": [\n\t\t\"d5a8\"\n\t],\n\t\"./es.json\": [\n\t\t\"b01c\",\n\t\t\"asyncTxt/3\"\n\t],\n\t\"./fr.json\": [\n\t\t\"f22d\",\n\t\t\"asyncTxt/4\"\n\t],\n\t\"./id.json\": [\n\t\t\"4da5\",\n\t\t\"asyncTxt/5\"\n\t],\n\t\"./it.json\": [\n\t\t\"5af6\",\n\t\t\"asyncTxt/6\"\n\t],\n\t\"./ja.json\": [\n\t\t\"95fb\",\n\t\t\"asyncTxt/7\"\n\t],\n\t\"./ko.json\": [\n\t\t\"9dad\",\n\t\t\"asyncTxt/8\"\n\t],\n\t\"./my.json\": [\n\t\t\"817a\",\n\t\t\"asyncTxt/9\"\n\t],\n\t\"./nl.json\": [\n\t\t\"87f6\",\n\t\t\"asyncTxt/10\"\n\t],\n\t\"./pl.json\": [\n\t\t\"1310\",\n\t\t\"asyncTxt/11\"\n\t],\n\t\"./pt.json\": [\n\t\t\"1dbe\",\n\t\t\"asyncTxt/12\"\n\t],\n\t\"./ru.json\": [\n\t\t\"fa07\",\n\t\t\"asyncTxt/13\"\n\t],\n\t\"./sv.json\": [\n\t\t\"9e21\",\n\t\t\"asyncTxt/14\"\n\t],\n\t\"./th.json\": [\n\t\t\"20a8\",\n\t\t\"asyncTxt/15\"\n\t],\n\t\"./tr.json\": [\n\t\t\"cecc\",\n\t\t\"asyncTxt/16\"\n\t],\n\t\"./vi.json\": [\n\t\t\"8ced\",\n\t\t\"asyncTxt/17\"\n\t],\n\t\"./zh_cn.json\": [\n\t\t\"f139\",\n\t\t\"asyncTxt/18\"\n\t],\n\t\"./zh_tw.json\": [\n\t\t\"ebcb\",\n\t\t\"asyncTxt/19\"\n\t]\n};\nfunction webpackAsyncContext(req) {\n\tif(!__webpack_require__.o(map, req)) {\n\t\treturn Promise.resolve().then(function() {\n\t\t\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\t\t\te.code = 'MODULE_NOT_FOUND';\n\t\t\tthrow e;\n\t\t});\n\t}\n\n\tvar ids = map[req], id = ids[0];\n\treturn Promise.all(ids.slice(1).map(__webpack_require__.e)).then(function() {\n\t\treturn __webpack_require__.t(id, 3);\n\t});\n}\nwebpackAsyncContext.keys = function webpackAsyncContextKeys() {\n\treturn Object.keys(map);\n};\nwebpackAsyncContext.id = \"fdb9\";\nmodule.exports = webpackAsyncContext;"], "sourceRoot": ""}