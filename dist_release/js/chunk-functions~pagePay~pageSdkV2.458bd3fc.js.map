{"version": 3, "sources": ["webpack:///./node_modules/ua-parser-js/src/ua-parser.js", "webpack:///(webpack)/buildin/amd-options.js", "webpack:///./node_modules/core-js/modules/es.iterator.filter.js"], "names": ["window", "undefined", "LIBVERSION", "EMPTY", "UNKNOWN", "FUNC_TYPE", "UNDEF_TYPE", "OBJ_TYPE", "STR_TYPE", "MAJOR", "MODEL", "NAME", "TYPE", "VENDOR", "VERSION", "ARCHITECTURE", "CONSOLE", "MOBILE", "TABLET", "SMARTTV", "WEARABLE", "EMBEDDED", "UA_MAX_LENGTH", "AMAZON", "APPLE", "ASUS", "BLACKBERRY", "BROWSER", "CHROME", "EDGE", "FIREFOX", "GOOGLE", "HUAWEI", "LG", "MICROSOFT", "MOTOROLA", "OPERA", "SAMSUNG", "SHARP", "SONY", "XIAOMI", "ZEBRA", "FACEBOOK", "CHROMIUM_OS", "MAC_OS", "SUFFIX_BROWSER", "extend", "regexes", "extensions", "mergedRegexes", "i", "length", "concat", "enumerize", "arr", "enums", "toUpperCase", "has", "str1", "str2", "lowerize", "indexOf", "str", "toLowerCase", "majorize", "version", "replace", "split", "trim", "len", "substring", "rgxMapper", "ua", "arrays", "j", "k", "p", "q", "matches", "match", "regex", "props", "exec", "this", "call", "test", "strMapper", "map", "hasOwnProperty", "oldSafariMap", "windowsVersionMap", "browser", "cpu", "device", "engine", "os", "<PERSON><PERSON><PERSON><PERSON>", "getResult", "_navigator", "navigator", "_ua", "userAgent", "_uach", "userAgentData", "_rgxmap", "_isSelfNav", "<PERSON><PERSON><PERSON><PERSON>", "_browser", "brave", "isBrave", "getCPU", "_cpu", "getDevice", "_device", "mobile", "standalone", "maxTouchPoints", "getEngine", "_engine", "getOS", "_os", "platform", "getUA", "setUA", "CPU", "DEVICE", "ENGINE", "OS", "module", "exports", "$", "j<PERSON><PERSON><PERSON>", "Zepto", "parser", "get", "set", "result", "prop", "__webpack_amd_options__", "aCallable", "anObject", "getIteratorDirect", "createIteratorProxy", "callWithSafeIterationClosing", "IS_PURE", "iteratorClose", "iteratorHelperThrowsOnInvalidIterator", "iteratorHelperWithoutClosingOnEarlyError", "FILTER_WITHOUT_THROWING_ON_INVALID_ITERATOR", "filterWithoutClosingOnEarlyError", "TypeError", "FORCED", "IteratorProxy", "done", "value", "iterator", "predicate", "next", "counter", "target", "proto", "real", "forced", "filter", "error"], "mappings": "wHAAA,OAUA,SAAWA,EAAQC,GAEf,aAOA,IAAIC,EAAc,SACdC,EAAc,GACdC,EAAc,IACdC,EAAc,WACdC,EAAc,YACdC,EAAc,SACdC,EAAc,SACdC,EAAc,QACdC,EAAc,QACdC,EAAc,OACdC,EAAc,OACdC,EAAc,SACdC,EAAc,UACdC,EAAc,eACdC,EAAc,UACdC,EAAc,SACdC,EAAc,SACdC,EAAc,UACdC,EAAc,WACdC,EAAc,WACdC,EAAgB,IAEhBC,EAAU,SACVC,EAAU,QACVC,EAAU,OACVC,EAAa,aACbC,EAAU,UACVC,EAAU,SACVC,EAAU,OACVC,EAAU,UACVC,EAAU,SACVC,EAAU,SACVC,EAAU,KACVC,EAAY,YACZC,EAAY,WACZC,EAAU,QACVC,EAAU,UACVC,EAAU,QACVC,EAAU,OACVC,EAAU,SACVC,EAAU,QACVC,EAAc,WACdC,EAAc,cACdC,EAAU,SACVC,EAAiB,WAMjBC,EAAS,SAAUC,EAASC,GACxB,IAAIC,EAAgB,GACpB,IAAK,IAAIC,KAAKH,EACNC,EAAWE,IAAMF,EAAWE,GAAGC,OAAS,IAAM,EAC9CF,EAAcC,GAAKF,EAAWE,GAAGE,OAAOL,EAAQG,IAEhDD,EAAcC,GAAKH,EAAQG,GAGnC,OAAOD,GAEXI,EAAY,SAAUC,GAElB,IADA,IAAIC,EAAQ,GACHL,EAAE,EAAGA,EAAEI,EAAIH,OAAQD,IACxBK,EAAMD,EAAIJ,GAAGM,eAAiBF,EAAIJ,GAEtC,OAAOK,GAEXE,EAAM,SAAUC,EAAMC,GAClB,cAAcD,IAASlD,IAAuD,IAA5CoD,EAASD,GAAME,QAAQD,EAASF,KAEtEE,EAAW,SAAUE,GACjB,OAAOA,EAAIC,eAEfC,GAAW,SAAUC,GACjB,cAAa,IAAczD,EAAWyD,EAAQC,QAAQ,WAAY/D,GAAOgE,MAAM,KAAK,GAAKlE,GAE7FmE,GAAO,SAAUN,EAAKO,GAClB,UAAU,IAAU7D,EAEhB,OADAsD,EAAMA,EAAII,QAAQ,SAAU/D,UACf,IAAUG,EAAawD,EAAMA,EAAIQ,UAAU,EAAGhD,IAQnEiD,GAAY,SAAUC,EAAIC,GAEtB,IAAWC,EAAGC,EAAGC,EAAGC,EAAGC,EAASC,EAA5B7B,EAAI,EAGR,MAAOA,EAAIuB,EAAOtB,SAAW2B,EAAS,CAElC,IAAIE,EAAQP,EAAOvB,GACf+B,EAAQR,EAAOvB,EAAI,GACvBwB,EAAIC,EAAI,EAGR,MAAOD,EAAIM,EAAM7B,SAAW2B,EAAS,CAEjC,IAAKE,EAAMN,GAAM,MAGjB,GAFAI,EAAUE,EAAMN,KAAKQ,KAAKV,GAEpBM,EACF,IAAKF,EAAI,EAAGA,EAAIK,EAAM9B,OAAQyB,IAC1BG,EAAQD,IAAUH,GAClBE,EAAII,EAAML,UAECC,IAAMtE,GAAYsE,EAAE1B,OAAS,EACnB,IAAb0B,EAAE1B,cACS0B,EAAE,IAAMxE,EAEf8E,KAAKN,EAAE,IAAMA,EAAE,GAAGO,KAAKD,KAAMJ,GAG7BI,KAAKN,EAAE,IAAMA,EAAE,GAEC,IAAbA,EAAE1B,cAEE0B,EAAE,KAAOxE,GAAewE,EAAE,GAAGK,MAAQL,EAAE,GAAGQ,KAKjDF,KAAKN,EAAE,IAAME,EAAQA,EAAMb,QAAQW,EAAE,GAAIA,EAAE,IAAM5E,EAHjDkF,KAAKN,EAAE,IAAME,EAAQF,EAAE,GAAGO,KAAKD,KAAMJ,EAAOF,EAAE,IAAM5E,EAKpC,IAAb4E,EAAE1B,SACLgC,KAAKN,EAAE,IAAME,EAAQF,EAAE,GAAGO,KAAKD,KAAMJ,EAAMb,QAAQW,EAAE,GAAIA,EAAE,KAAO5E,GAG1EkF,KAAKN,GAAKE,GAAgB9E,EAK1CiD,GAAK,IAIboC,GAAY,SAAUxB,EAAKyB,GAEvB,IAAK,IAAIrC,KAAKqC,EAEV,UAAWA,EAAIrC,KAAO3C,GAAYgF,EAAIrC,GAAGC,OAAS,GAC9C,IAAK,IAAIuB,EAAI,EAAGA,EAAIa,EAAIrC,GAAGC,OAAQuB,IAC/B,GAAIjB,EAAI8B,EAAIrC,GAAGwB,GAAIZ,GACf,OAAQZ,IAAM9C,EAAWH,EAAYiD,OAG1C,GAAIO,EAAI8B,EAAIrC,GAAIY,GACnB,OAAQZ,IAAM9C,EAAWH,EAAYiD,EAG7C,OAAOqC,EAAIC,eAAe,KAAOD,EAAI,KAAOzB,GAQhD2B,GAAe,CACX,MAAU,KACV,IAAU,KACV,IAAU,KACV,MAAU,OACV,QAAU,OACV,QAAU,OACV,QAAU,OACV,IAAU,KAEdC,GAAoB,CAChB,GAAc,OACd,UAAc,SACd,SAAc,QACd,IAAc,SACd,GAAc,CAAC,SAAU,UACzB,MAAc,SACd,EAAc,SACd,EAAc,SACd,IAAc,SACd,GAAc,CAAC,SAAU,WACzB,GAAc,OAOlB3C,GAAU,CAEV4C,QAAU,CAAC,CAEP,gCACG,CAAC7E,EAAS,CAACH,EAAM,WAAY,CAChC,+BACG,CAACG,EAAS,CAACH,EAAM,SAAU,CAG9B,4BACA,mDACA,2CACG,CAACA,EAAMG,GAAU,CACpB,yBACG,CAACA,EAAS,CAACH,EAAMyB,EAAM,UAAW,CACrC,4BACG,CAACtB,EAAS,CAACH,EAAMyB,EAAM,QAAS,CACnC,qBACG,CAACtB,EAAS,CAACH,EAAMyB,IAAS,CAG7B,0DACG,CAACtB,EAAS,CAACH,EAAM,UAAW,CAC/B,+CACG,CAACG,EAAS,CAACH,EAAM,YAAa,CACjC,uBACA,uEAGA,4DACA,2BAGA,+NAEA,sCACA,uBACG,CAACA,EAAMG,GAAU,CACpB,6BACG,CAACA,EAAS,CAACH,EAAM,UAAW,CAC/B,qBACG,CAACG,EAAS,CAACH,EAAM,eAAgB,CACpC,qDACG,CAACG,EAAS,CAACH,EAAM,KAAKgB,IAAW,CACpC,+BACA,+BACA,8BACG,CAACb,EAAS,CAACH,EAAM,WAAY,CAChC,yBACG,CAACG,EAAS,CAACH,EAAM,cAAe,CACnC,+CACG,CAACG,EAAS,CAACH,EAAM,OAAQ,CAC5B,oCACG,CAACG,EAAS,CAACH,EAAM,WAAY,CAChC,yBACG,CAACG,EAAS,CAACH,EAAM,gBAAgBgB,IAAW,CAC/C,2BACG,CAAC,CAAChB,EAAM,OAAQ,aAAagB,GAAUb,GAAU,CACpD,uBACG,CAACA,EAAS,CAACH,EAAMmB,EAAQ,WAAY,CACxC,qBACG,CAAChB,EAAS,CAACH,EAAMyB,EAAM,WAAY,CACtC,0BACG,CAACtB,EAAS,CAACH,EAAM,YAAa,CACjC,sBACG,CAACG,EAAS,CAACH,EAAM,YAAa,CACjC,qBACG,CAACG,EAAS,CAACH,EAAMyB,EAAM,WAAY,CACtC,2BACG,CAACtB,EAAS,CAACH,EAAM,OAASkC,IAAkB,CAC/C,sBACG,CAAC/B,EAAS,CAACH,EAAMmB,IAAW,CAC/B,+BACG,CAAChB,EAAS,CAACH,EAAM,QAAS,CAC7B,sBACG,CAAC,CAACA,EAAM,OAAQ,aAAcG,GAAU,CAC3C,yDACG,CAAC,CAACH,EAAM,OAAQ,KAAOkC,GAAiB/B,GAAU,CACrD,8BACG,CAACA,EAAS,CAACH,EAAM0B,EAAU,cAAe,CAC7C,0BACG,CAACvB,EAAS,CAACH,EAAM,mBAAoB,CACxC,4BACG,CAAC,CAACA,EAAM,gBAAiBG,GAAU,CACtC,gCACA,iDACA,sEACG,CAACH,EAAMG,GAAU,CACpB,sBACA,sBACG,CAACH,GAAO,CACX,kCACA,oCACG,CAACG,EAASH,GAAO,CAGpB,+DACG,CAAC,CAACA,EAAM+B,GAAW5B,GAAU,CAChC,uBACA,uCACA,kCACA,4BACA,4BACA,6BACA,qCACA,iDACG,CAACH,EAAMG,GAAU,CACpB,gCACG,CAACA,EAAS,CAACH,EAAM,QAAS,CAC7B,8CACG,CAACG,EAAS,CAACH,EAAM,WAAY,CAEhC,oCACG,CAACG,EAAS,CAACH,EAAMiB,EAAO,cAAe,CAE1C,+BACG,CAAC,CAACjB,EAAMiB,EAAO,YAAad,GAAU,CAEzC,2DACG,CAACA,EAAS,CAACH,EAAM,WAAWgB,IAAW,CAE1C,+DACG,CAAChB,EAAMG,GAAU,CAEpB,gDACG,CAACA,EAAS,CAACH,EAAM,kBAAmB,CACvC,sDACG,CAACG,EAASH,GAAO,CACpB,gDACG,CAACA,EAAM,CAACG,EAASwE,GAAWG,KAAgB,CAE/C,8BACG,CAAC9E,EAAMG,GAAU,CAGpB,wCACG,CAAC,CAACH,EAAM,YAAaG,GAAU,CAClC,kCACG,CAACH,EAAMG,GAAU,CACpB,uCACG,CAACA,EAAS,CAACH,EAAMmB,EAAQ,aAAc,CAC1C,6BACA,cACA,8FAEA,+FAEA,wBACA,2CAGA,+GAEA,wBACG,CAACnB,EAAM,CAACG,EAAS,KAAM,MAAO,CAEjC,wBACG,CAACH,EAAM,CAACG,EAAS,eAAgB,MAGxC8E,IAAM,CAAC,CAEH,iDACG,CAAC,CAAC7E,EAAc,UAAW,CAE9B,gBACG,CAAC,CAACA,EAAc6C,IAAY,CAE/B,0BACG,CAAC,CAAC7C,EAAc,SAAU,CAE7B,oCACG,CAAC,CAACA,EAAc,UAAW,CAE9B,mCACG,CAAC,CAACA,EAAc,UAAW,CAG9B,8BACG,CAAC,CAACA,EAAc,QAAS,CAE5B,0CACG,CAAC,CAACA,EAAc,OAAQZ,EAAOyD,IAAY,CAE9C,kBACG,CAAC,CAAC7C,EAAc,UAAW,CAE9B,2HAEG,CAAC,CAACA,EAAc6C,KAGvBiC,OAAS,CAAC,CAON,mFACG,CAACnF,EAAO,CAACG,EAAQwB,GAAU,CAACzB,EAAMM,IAAU,CAC/C,iEACA,kCACA,iBACG,CAACR,EAAO,CAACG,EAAQwB,GAAU,CAACzB,EAAMK,IAAU,CAG/C,4CACG,CAACP,EAAO,CAACG,EAAQW,GAAQ,CAACZ,EAAMK,IAAU,CAC7C,6BACA,oCACA,kCACG,CAACP,EAAO,CAACG,EAAQW,GAAQ,CAACZ,EAAMM,IAAU,CAC7C,iBACG,CAACR,EAAO,CAACG,EAAQW,IAAS,CAG7B,iCACG,CAACd,EAAO,CAACG,EAAQyB,GAAQ,CAAC1B,EAAMK,IAAU,CAG7C,4BACG,CAACP,EAAO,CAACG,EAAQ,SAAU,CAACD,EAAMK,IAAU,CAG/C,+DACG,CAACP,EAAO,CAACG,EAAQmB,GAAS,CAACpB,EAAMM,IAAU,CAC9C,4BACA,sEACG,CAACR,EAAO,CAACG,EAAQmB,GAAS,CAACpB,EAAMK,IAAU,CAG9C,kDACA,yBACA,uCACA,iDACA,4DACA,6GACG,CAAC,CAACP,EAAO,KAAM,KAAM,CAACG,EAAQ2B,GAAS,CAAC5B,EAAMK,IAAU,CAC3D,+CACA,8CACE,CAAC,CAACP,EAAO,KAAM,KAAM,CAACG,EAAQ2B,GAAS,CAAC5B,EAAMM,IAAU,CAG1D,sBACA,mEACG,CAACR,EAAO,CAACG,EAAQ,QAAS,CAACD,EAAMK,IAAU,CAC9C,wBACG,CAACP,EAAO,CAACG,EAAQ,QAAS,CAACD,EAAMM,IAAU,CAG9C,yBACA,oCACG,CAACR,EAAO,CAACG,EAAQ,QAAS,CAACD,EAAMK,IAAU,CAG9C,mCACG,CAACP,EAAO,CAACG,EAAQ,UAAW,CAACD,EAAMK,IAAU,CAGhD,iFACA,4BACA,sDACG,CAACP,EAAO,CAACG,EAAQsB,GAAW,CAACvB,EAAMK,IAAU,CAChD,qCACG,CAACP,EAAO,CAACG,EAAQsB,GAAW,CAACvB,EAAMM,IAAU,CAGhD,iEACG,CAACR,EAAO,CAACG,EAAQoB,GAAK,CAACrB,EAAMM,IAAU,CAC1C,sDACA,oDACA,wBACG,CAACR,EAAO,CAACG,EAAQoB,GAAK,CAACrB,EAAMK,IAAU,CAG1C,oBACA,qEACG,CAACP,EAAO,CAACG,EAAQ,UAAW,CAACD,EAAMM,IAAU,CAGhD,qCACA,0BACG,CAAC,CAACR,EAAO,KAAM,KAAM,CAACG,EAAQ,SAAU,CAACD,EAAMK,IAAU,CAG5D,gBACG,CAACP,EAAO,CAACG,EAAQkB,GAAS,CAACnB,EAAMM,IAAU,CAC9C,6CACG,CAACR,EAAO,CAACG,EAAQkB,GAAS,CAACnB,EAAMK,IAAU,CAG9C,2GACG,CAACP,EAAO,CAACG,EAAQ0B,GAAO,CAAC3B,EAAMK,IAAU,CAC5C,oBACA,iCACG,CAAC,CAACP,EAAO,iBAAkB,CAACG,EAAQ0B,GAAO,CAAC3B,EAAMM,IAAU,CAG/D,sCACA,0CACG,CAACR,EAAO,CAACG,EAAQ,WAAY,CAACD,EAAMK,IAAU,CAGjD,eACA,yCACA,gCACG,CAACP,EAAO,CAACG,EAAQU,GAAS,CAACX,EAAMM,IAAU,CAC9C,iDACG,CAAC,CAACR,EAAO,QAAS,iBAAkB,CAACG,EAAQU,GAAS,CAACX,EAAMK,IAAU,CAG1E,gCACG,CAACP,EAAOG,EAAQ,CAACD,EAAMM,IAAU,CACpC,gCACA,kBACG,CAACR,EAAO,CAACG,EAAQa,GAAa,CAACd,EAAMK,IAAU,CAGlD,qFACG,CAACP,EAAO,CAACG,EAAQY,GAAO,CAACb,EAAMM,IAAU,CAC5C,iDACG,CAACR,EAAO,CAACG,EAAQY,GAAO,CAACb,EAAMK,IAAU,CAG5C,cACG,CAACP,EAAO,CAACG,EAAQ,OAAQ,CAACD,EAAMM,IAAU,CAC7C,2CAGA,oCACA,iFACG,CAACL,EAAQ,CAACH,EAAO,KAAM,KAAM,CAACE,EAAMK,IAAU,CAGjD,gHACG,CAACP,EAAO,CAACG,EAAQ,OAAQ,CAACD,EAAMM,IAAU,CAG7C,mBACG,CAAC,CAACL,EAAQ+C,GAAWlD,EAAO,CAACE,EAAM0E,GAAW,CAAE,OAAW,CAAC,UAAW,SAAU,IAAM,YAAc,CAGxG,uCACG,CAAC5E,EAAO,CAACG,EAAQ,QAAS,CAACD,EAAMM,IAAU,CAG9C,8BACA,qBACG,CAACR,EAAO,CAACG,EAAQ,SAAU,CAACD,EAAMK,IAAU,CAG/C,kDACG,CAACP,EAAO,CAACG,EAAQ,WAAY,CAACD,EAAMK,IAAU,CAGjD,8BACA,oCACG,CAACP,EAAO,CAACG,EAAQ,aAAc,CAACD,EAAMK,IAAU,CAGnD,gBACA,+CACG,CAACP,EAAO,CAACG,EAAQ,OAAQ,CAACD,EAAMK,IAAU,CAG7C,0CACG,CAACP,EAAO,CAACG,EAAQ,aAAc,CAACD,EAAMK,IAAU,CAGnD,qCACG,CAACP,EAAO,CAACG,EAAQ,WAAY,CAACD,EAAMK,IAAU,CAGjD,+HAEA,uCACA,mBACA,iBACA,8BACA,0BACA,WACA,yBACG,CAACJ,EAAQH,EAAO,CAACE,EAAMK,IAAU,CAEpC,mBACA,2BACA,wBACA,uCACA,uBACA,4BACA,iCACA,kCACA,8BACA,gCACA,mCACG,CAACJ,EAAQH,EAAO,CAACE,EAAMM,IAAU,CAEpC,kBACG,CAACR,EAAO,CAACG,EAAQqB,GAAY,CAACtB,EAAMM,IAAU,CACjD,qCACG,CAACR,EAAO,CAACG,EAAQ,aAAc,CAACD,EAAMK,IAAU,CACnD,aACG,CAACP,EAAO,CAACG,EAAQ,QAAS,CAACD,EAAMK,IAAU,CAC9C,gBACG,CAACP,EAAO,CAACG,EAAQ,WAAY,CAACD,EAAMK,IAAU,CACjD,iBACG,CAACP,EAAO,CAACG,EAAQ,OAAQ,CAACD,EAAMM,IAAU,CAC7C,0BACG,CAACR,EAAO,CAACG,EAAQ,QAAS,CAACD,EAAMM,IAAU,CAC9C,wBACG,CAACR,EAAO,CAACG,EAAQ,WAAY,CAACD,EAAMM,IAAU,CACjD,+CACG,CAACR,EAAO,CAACG,EAAQ,kBAAmB,CAACD,EAAMM,IAAU,CACxD,qBACG,CAACR,EAAO,CAACG,EAAQ,YAAa,CAACD,EAAMM,IAAU,CAClD,cACG,CAACR,EAAO,CAACG,EAAQ,OAAQ,CAACD,EAAMM,IAAU,CAC7C,mBACG,CAACR,EAAO,CAACG,EAAQ,OAAQ,CAACD,EAAMK,IAAU,CAC7C,wBACG,CAACP,EAAO,CAACG,EAAQ,SAAU,CAACD,EAAMK,IAAU,CAC/C,mBACG,CAACP,EAAO,CAACG,EAAQ,SAAU,CAACD,EAAMM,IAAU,CAC/C,wBACG,CAACR,EAAO,CAACG,EAAQ,QAAS,CAACD,EAAMM,IAAU,CAC9C,mBACA,sCACG,CAAC,CAACL,EAAQ,gBAAiBH,EAAO,CAACE,EAAMM,IAAU,CACtD,sBACG,CAACR,EAAO,CAACG,EAAQ,YAAa,CAACD,EAAMM,IAAU,CAClD,8BACG,CAACR,EAAO,CAACG,EAAQ,YAAa,CAACD,EAAMM,IAAU,CAClD,oDACG,CAAC,CAACL,EAAQ,SAAUH,EAAO,CAACE,EAAMK,IAAU,CAC/C,2BACG,CAAC,CAACJ,EAAQ,SAAUH,EAAO,CAACE,EAAMK,IAAU,CAC/C,cACG,CAACP,EAAO,CAACG,EAAQ,aAAc,CAACD,EAAMK,IAAU,CACnD,uCACG,CAACP,EAAO,CAACG,EAAQ,WAAY,CAACD,EAAMM,IAAU,CACjD,wBACG,CAACR,EAAO,CAACG,EAAQ,aAAc,CAACD,EAAMM,IAAU,CACnD,kBACG,CAACR,EAAO,CAACG,EAAQ,SAAU,CAACD,EAAMM,IAAU,CAC/C,qBACG,CAACR,EAAO,CAACG,EAAQ,UAAW,CAACD,EAAMM,IAAU,CAChD,mBACG,CAACL,EAAQH,EAAO,CAACE,EAAMK,IAAU,CACpC,sBACG,CAAC,CAACP,EAAO,MAAO,KAAM,CAACG,EAAQqB,GAAY,CAACtB,EAAMK,IAAU,CAC/D,yDACG,CAACP,EAAO,CAACG,EAAQ4B,GAAQ,CAAC7B,EAAMM,IAAU,CAC7C,yCACG,CAACR,EAAO,CAACG,EAAQ4B,GAAQ,CAAC7B,EAAMK,IAAU,CAM7C,wBACG,CAACJ,EAAQ,CAACD,EAAMO,IAAW,CAC9B,uBACG,CAAC,CAACT,EAAO,IAAK,WAAY,CAACG,EAAQwB,GAAU,CAACzB,EAAMO,IAAW,CAClE,8DACG,CAAC,CAACN,EAAQoB,GAAK,CAACrB,EAAMO,IAAW,CACpC,gBACG,CAACN,EAAQ,CAACH,EAAOc,EAAM,OAAQ,CAACZ,EAAMO,IAAW,CACpD,UACG,CAAC,CAACT,EAAOkB,EAAO,QAAS,CAACf,EAAQkB,GAAS,CAACnB,EAAMO,IAAW,CAChE,6BACG,CAACT,EAAO,CAACG,EAAQU,GAAS,CAACX,EAAMO,IAAW,CAC/C,uBACA,uBACG,CAACT,EAAO,CAACG,EAAQyB,GAAQ,CAAC1B,EAAMO,IAAU,CAC7C,4BACG,CAACT,EAAO,CAACG,EAAQ0B,GAAO,CAAC3B,EAAMO,IAAW,CAC7C,qBACG,CAACT,EAAO,CAACG,EAAQ2B,GAAS,CAAC5B,EAAMO,IAAW,CAC/C,6BACG,CAACN,EAAQH,EAAO,CAACE,EAAMO,IAAW,CACrC,0CACA,6DACG,CAAC,CAACN,EAAQuD,IAAO,CAAC1D,EAAO0D,IAAO,CAACxD,EAAMO,IAAW,CACrD,mDACG,CAAC,CAACP,EAAMO,IAAW,CAMtB,UACA,8BACG,CAACN,EAAQH,EAAO,CAACE,EAAMI,IAAW,CACrC,0BACG,CAACN,EAAO,CAACG,EAAQ,UAAW,CAACD,EAAMI,IAAW,CACjD,mCACG,CAACN,EAAO,CAACG,EAAQ0B,GAAO,CAAC3B,EAAMI,IAAW,CAC7C,sCACG,CAACN,EAAO,CAACG,EAAQqB,GAAY,CAACtB,EAAMI,IAAW,CAMlD,mCACG,CAACN,EAAO,CAACG,EAAQwB,GAAU,CAACzB,EAAMQ,IAAY,CACjD,kBACG,CAACP,EAAQH,EAAO,CAACE,EAAMQ,IAAY,CACtC,wCACG,CAACV,EAAO,CAACG,EAAQW,GAAQ,CAACZ,EAAMQ,IAAY,CAC/C,wBACG,CAACV,EAAO,CAACG,EAAQkB,GAAS,CAACnB,EAAMQ,IAAY,CAChD,6BACG,CAACV,EAAO,CAACG,EAAQ4B,GAAQ,CAAC7B,EAAMQ,IAAY,CAM/C,wBACG,CAACV,EAAO,CAACG,EAAQkB,GAAS,CAACnB,EAAMQ,IAAY,CAChD,kCACG,CAACP,EAAQH,EAAO,CAACE,EAAMQ,IAAY,CACtC,yBACG,CAACV,EAAO,CAACG,EAAQ6B,GAAW,CAAC9B,EAAMQ,IAAY,CAMlD,wCACG,CAACP,EAAQ,CAACD,EAAMS,IAAY,CAC/B,cACG,CAACX,EAAO,CAACG,EAAQU,GAAS,CAACX,EAAMS,IAAY,CAMhD,kEACG,CAACX,EAAO,CAACE,EAAMK,IAAU,CAC5B,+DACG,CAACP,EAAO,CAACE,EAAMM,IAAU,CAC5B,gDACG,CAAC,CAACN,EAAMM,IAAU,CACrB,kEACG,CAAC,CAACN,EAAMK,IAAU,CACrB,kCACG,CAACP,EAAO,CAACG,EAAQ,aAGxBiF,OAAS,CAAC,CAEN,8BACG,CAAChF,EAAS,CAACH,EAAMkB,EAAK,SAAU,CAEnC,wBACG,CAAClB,EAAMG,GAAU,CAEpB,6CACG,CAACA,EAAS,CAACH,EAAM,UAAW,CAE/B,uBACA,4EACA,0BACA,yCACA,8BACA,eACG,CAACA,EAAMG,GAAU,CAEpB,iCACG,CAACA,EAASH,IAGjBoF,GAAK,CAAC,CAGF,mCACG,CAACpF,EAAMG,GAAU,CACpB,yDACG,CAACH,EAAM,CAACG,EAASwE,GAAWI,KAAqB,CACpD,0BACA,2CACA,wCACG,CAAC,CAAC5E,EAASwE,GAAWI,IAAoB,CAAC/E,EAAM,YAAa,CAGjE,sDACA,4CACA,wBACG,CAAC,CAACG,EAAS,KAAM,KAAM,CAACH,EAAM,QAAS,CAC1C,0BACA,yCACG,CAAC,CAACA,EAAMiC,GAAS,CAAC9B,EAAS,KAAM,MAAO,CAG3C,kDACG,CAACA,EAASH,GAAO,CACpB,2FACA,8BACA,+BACA,kBACG,CAACA,EAAMG,GAAU,CACpB,cACG,CAACA,EAAS,CAACH,EAAMe,IAAc,CAClC,6DACG,CAACZ,EAAS,CAACH,EAAM,YAAa,CACjC,mFACG,CAACG,EAAS,CAACH,EAAMmB,EAAQ,QAAS,CACrC,kBACA,wCACG,CAAChB,EAAS,CAACH,EAAM,UAAW,CAC/B,wCACG,CAACG,EAAS,CAACH,EAAM,YAAa,CAGjC,qBACG,CAACG,EAAS,CAACH,EAAMiB,EAAO,SAAU,CACrC,oCACG,CAAC,CAACjB,EAAMgC,GAAc7B,GAAS,CAGlC,qBACA,iBACA,2BAGA,mDACA,2BAGA,wCACA,yBACA,4BACA,8SAEA,2BACA,oBACA,6EACA,kBACG,CAACH,EAAMG,GAAU,CACpB,yBACG,CAAC,CAACH,EAAM,WAAYG,GAAU,CACjC,sCACA,kCACA,mEACA,sBACG,CAACH,EAAMG,KAQdkF,GAAW,SAAUxB,EAAIxB,GAOzB,UALWwB,IAAOjE,IACdyC,EAAawB,EACbA,EAAKvE,KAGHkF,gBAAgBa,IAClB,OAAO,IAAIA,GAASxB,EAAIxB,GAAYiD,YAGxC,IAAIC,SAAqBlG,IAAWM,GAAcN,EAAOmG,UAAanG,EAAOmG,UAAYlG,EACrFmG,EAAM5B,IAAQ0B,GAAcA,EAAWG,UAAaH,EAAWG,UAAYlG,GAC3EmG,EAASJ,GAAcA,EAAWK,cAAiBL,EAAWK,cAAgBtG,EAC9EuG,EAAUxD,EAAaF,EAAOC,GAASC,GAAcD,GACrD0D,EAAaP,GAAcA,EAAWG,WAAaD,EAyEvD,OAvEAjB,KAAKuB,WAAa,WACd,IAAIC,EAAW,GASf,OARAA,EAAShG,GAAQV,EACjB0G,EAAS7F,GAAWb,EACpBsE,GAAUa,KAAKuB,EAAUP,EAAKI,EAAQb,SACtCgB,EAASlG,GAASuD,GAAS2C,EAAS7F,IAEhC2F,GAAcP,GAAcA,EAAWU,cAAgBV,EAAWU,MAAMC,SAAWxG,IACnFsG,EAAShG,GAAQ,SAEdgG,GAEXxB,KAAK2B,OAAS,WACV,IAAIC,EAAO,GAGX,OAFAA,EAAKhG,GAAgBd,EACrBsE,GAAUa,KAAK2B,EAAMX,EAAKI,EAAQZ,KAC3BmB,GAEX5B,KAAK6B,UAAY,WACb,IAAIC,EAAU,GAad,OAZAA,EAAQpG,GAAUZ,EAClBgH,EAAQvG,GAAST,EACjBgH,EAAQrG,GAAQX,EAChBsE,GAAUa,KAAK6B,EAASb,EAAKI,EAAQX,QACjCY,IAAeQ,EAAQrG,IAAS0F,GAASA,EAAMY,SAC/CD,EAAQrG,GAAQK,GAGhBwF,GAAgC,aAAlBQ,EAAQvG,IAAyBwF,UAAqBA,EAAWiB,aAAe7G,GAAc4F,EAAWkB,gBAAkBlB,EAAWkB,eAAiB,IACrKH,EAAQvG,GAAS,OACjBuG,EAAQrG,GAAQM,GAEb+F,GAEX9B,KAAKkC,UAAY,WACb,IAAIC,EAAU,GAId,OAHAA,EAAQ3G,GAAQV,EAChBqH,EAAQxG,GAAWb,EACnBsE,GAAUa,KAAKkC,EAASlB,EAAKI,EAAQV,QAC9BwB,GAEXnC,KAAKoC,MAAQ,WACT,IAAIC,EAAM,GASV,OARAA,EAAI7G,GAAQV,EACZuH,EAAI1G,GAAWb,EACfsE,GAAUa,KAAKoC,EAAKpB,EAAKI,EAAQT,IAC7BU,IAAee,EAAI7G,IAAS2F,GAASA,EAAMmB,UAA8B,WAAlBnB,EAAMmB,WAC7DD,EAAI7G,GAAQ2F,EAAMmB,SACGvD,QAAQ,aAAcvB,GACtBuB,QAAQ,SAAUtB,IAEpC4E,GAEXrC,KAAKc,UAAY,WACb,MAAO,CACHzB,GAAUW,KAAKuC,QACf/B,QAAUR,KAAKuB,aACfZ,OAAUX,KAAKkC,YACftB,GAAUZ,KAAKoC,QACf1B,OAAUV,KAAK6B,YACfpB,IAAUT,KAAK2B,WAGvB3B,KAAKuC,MAAQ,WACT,OAAOtB,GAEXjB,KAAKwC,MAAQ,SAAUnD,GAEnB,OADA4B,SAAc5B,IAAOhE,GAAYgE,EAAGrB,OAAS7B,EAAiB8C,GAAKI,EAAIlD,GAAiBkD,EACjFW,MAEXA,KAAKwC,MAAMvB,GACJjB,MAGXa,GAASlF,QAAUZ,EACnB8F,GAASrE,QAAW0B,EAAU,CAAC1C,EAAMG,EAASL,IAC9CuF,GAAS4B,IAAMvE,EAAU,CAACtC,IAC1BiF,GAAS6B,OAASxE,EAAU,CAAC3C,EAAOG,EAAQD,EAAMI,EAASC,EAAQE,EAASD,EAAQE,EAAUC,IAC9F2E,GAAS8B,OAAS9B,GAAS+B,GAAK1E,EAAU,CAAC1C,EAAMG,WAOvC,IAAcR,UAET0H,IAAW1H,GAAc0H,EAAOC,UACvCA,EAAUD,EAAOC,QAAUjC,IAE/BiC,EAAQjC,SAAWA,IAGf,aAAmB3F,GAAa,WAChC,aACI,OAAO2F,IACV,2CACahG,IAAWM,IAEzBN,EAAOgG,SAAWA,IAS1B,IAAIkC,UAAWlI,IAAWM,IAAeN,EAAOmI,QAAUnI,EAAOoI,OACjE,GAAIF,KAAMA,GAAE1D,GAAI,CACZ,IAAI6D,GAAS,IAAIrC,GACjBkC,GAAE1D,GAAK6D,GAAOpC,YACdiC,GAAE1D,GAAG8D,IAAM,WACP,OAAOD,GAAOX,SAElBQ,GAAE1D,GAAG+D,IAAM,SAAU/D,GACjB6D,GAAOV,MAAMnD,GACb,IAAIgE,EAASH,GAAOpC,YACpB,IAAK,IAAIwC,KAAQD,EACbN,GAAE1D,GAAGiE,GAAQD,EAAOC,MAj+BpC,CAs+BqB,kBAAXzI,OAAsBA,OAASmF,O,sBCh/BzC,YACA6C,EAAOC,QAAUS,I,mDCAjB,IAAIR,EAAI,EAAQ,QACZ9C,EAAO,EAAQ,QACfuD,EAAY,EAAQ,QACpBC,EAAW,EAAQ,QACnBC,EAAoB,EAAQ,QAC5BC,EAAsB,EAAQ,QAC9BC,EAA+B,EAAQ,QACvCC,EAAU,EAAQ,QAClBC,EAAgB,EAAQ,QACxBC,EAAwC,EAAQ,QAChDC,EAA2C,EAAQ,QAEnDC,GAA+CJ,IAAYE,EAAsC,UAAU,eAC3GG,GAAoCL,IAAYI,GAC/CD,EAAyC,SAAUG,WAEpDC,EAASP,GAAWI,GAA+CC,EAEnEG,EAAgBV,GAAoB,WACtC,IAGIN,EAAQiB,EAAMC,EAHdC,EAAWxE,KAAKwE,SAChBC,EAAYzE,KAAKyE,UACjBC,EAAO1E,KAAK0E,KAEhB,MAAO,EAAM,CAGX,GAFArB,EAASI,EAASxD,EAAKyE,EAAMF,IAC7BF,EAAOtE,KAAKsE,OAASjB,EAAOiB,KACxBA,EAAM,OAEV,GADAC,EAAQlB,EAAOkB,MACXX,EAA6BY,EAAUC,EAAW,CAACF,EAAOvE,KAAK2E,YAAY,GAAO,OAAOJ,MAMjGxB,EAAE,CAAE6B,OAAQ,WAAYC,OAAO,EAAMC,MAAM,EAAMC,OAAQX,GAAU,CACjEY,OAAQ,SAAgBP,GACtBhB,EAASzD,MACT,IACEwD,EAAUiB,GACV,MAAOQ,GACPnB,EAAc9D,KAAM,QAASiF,GAG/B,OAAIf,EAAyCjE,EAAKiE,EAAkClE,KAAMyE,GAEnF,IAAIJ,EAAcX,EAAkB1D,MAAO,CAChDyE,UAAWA", "file": "js/chunk-functions~pagePay~pageSdkV2.458bd3fc.js", "sourcesContent": ["/////////////////////////////////////////////////////////////////////////////////\n/* UAParser.js v1.0.40\n   Copyright © 2012-2024 F<PERSON><PERSON> <<EMAIL>>\n   MIT License *//*\n   Detect Browser, Engine, OS, CPU, and Device type/model from User-Agent data.\n   Supports browser & node.js environment. \n   Demo   : https://faisalman.github.io/ua-parser-js\n   Source : https://github.com/faisalman/ua-parser-js */\n/////////////////////////////////////////////////////////////////////////////////\n\n(function (window, undefined) {\n\n    'use strict';\n\n    //////////////\n    // Constants\n    /////////////\n\n\n    var LIBVERSION  = '1.0.40',\n        EMPTY       = '',\n        UNKNOWN     = '?',\n        FUNC_TYPE   = 'function',\n        UNDEF_TYPE  = 'undefined',\n        OBJ_TYPE    = 'object',\n        STR_TYPE    = 'string',\n        MAJOR       = 'major',\n        MODEL       = 'model',\n        NAME        = 'name',\n        TYPE        = 'type',\n        VENDOR      = 'vendor',\n        VERSION     = 'version',\n        ARCHITECTURE= 'architecture',\n        CONSOLE     = 'console',\n        MOBILE      = 'mobile',\n        TABLET      = 'tablet',\n        SMARTTV     = 'smarttv',\n        WEARABLE    = 'wearable',\n        EMBEDDED    = 'embedded',\n        UA_MAX_LENGTH = 500;\n\n    var AMAZON  = 'Amazon',\n        APPLE   = 'Apple',\n        ASUS    = 'ASUS',\n        BLACKBERRY = 'BlackBerry',\n        BROWSER = 'Browser',\n        CHROME  = 'Chrome',\n        EDGE    = 'Edge',\n        FIREFOX = 'Firefox',\n        GOOGLE  = 'Google',\n        HUAWEI  = 'Huawei',\n        LG      = 'LG',\n        MICROSOFT = 'Microsoft',\n        MOTOROLA  = 'Motorola',\n        OPERA   = 'Opera',\n        SAMSUNG = 'Samsung',\n        SHARP   = 'Sharp',\n        SONY    = 'Sony',\n        XIAOMI  = 'Xiaomi',\n        ZEBRA   = 'Zebra',\n        FACEBOOK    = 'Facebook',\n        CHROMIUM_OS = 'Chromium OS',\n        MAC_OS  = 'Mac OS',\n        SUFFIX_BROWSER = ' Browser';\n\n    ///////////\n    // Helper\n    //////////\n\n    var extend = function (regexes, extensions) {\n            var mergedRegexes = {};\n            for (var i in regexes) {\n                if (extensions[i] && extensions[i].length % 2 === 0) {\n                    mergedRegexes[i] = extensions[i].concat(regexes[i]);\n                } else {\n                    mergedRegexes[i] = regexes[i];\n                }\n            }\n            return mergedRegexes;\n        },\n        enumerize = function (arr) {\n            var enums = {};\n            for (var i=0; i<arr.length; i++) {\n                enums[arr[i].toUpperCase()] = arr[i];\n            }\n            return enums;\n        },\n        has = function (str1, str2) {\n            return typeof str1 === STR_TYPE ? lowerize(str2).indexOf(lowerize(str1)) !== -1 : false;\n        },\n        lowerize = function (str) {\n            return str.toLowerCase();\n        },\n        majorize = function (version) {\n            return typeof(version) === STR_TYPE ? version.replace(/[^\\d\\.]/g, EMPTY).split('.')[0] : undefined;\n        },\n        trim = function (str, len) {\n            if (typeof(str) === STR_TYPE) {\n                str = str.replace(/^\\s\\s*/, EMPTY);\n                return typeof(len) === UNDEF_TYPE ? str : str.substring(0, UA_MAX_LENGTH);\n            }\n    };\n\n    ///////////////\n    // Map helper\n    //////////////\n\n    var rgxMapper = function (ua, arrays) {\n\n            var i = 0, j, k, p, q, matches, match;\n\n            // loop through all regexes maps\n            while (i < arrays.length && !matches) {\n\n                var regex = arrays[i],       // even sequence (0,2,4,..)\n                    props = arrays[i + 1];   // odd sequence (1,3,5,..)\n                j = k = 0;\n\n                // try matching uastring with regexes\n                while (j < regex.length && !matches) {\n\n                    if (!regex[j]) { break; }\n                    matches = regex[j++].exec(ua);\n\n                    if (!!matches) {\n                        for (p = 0; p < props.length; p++) {\n                            match = matches[++k];\n                            q = props[p];\n                            // check if given property is actually array\n                            if (typeof q === OBJ_TYPE && q.length > 0) {\n                                if (q.length === 2) {\n                                    if (typeof q[1] == FUNC_TYPE) {\n                                        // assign modified match\n                                        this[q[0]] = q[1].call(this, match);\n                                    } else {\n                                        // assign given value, ignore regex match\n                                        this[q[0]] = q[1];\n                                    }\n                                } else if (q.length === 3) {\n                                    // check whether function or regex\n                                    if (typeof q[1] === FUNC_TYPE && !(q[1].exec && q[1].test)) {\n                                        // call function (usually string mapper)\n                                        this[q[0]] = match ? q[1].call(this, match, q[2]) : undefined;\n                                    } else {\n                                        // sanitize match using given regex\n                                        this[q[0]] = match ? match.replace(q[1], q[2]) : undefined;\n                                    }\n                                } else if (q.length === 4) {\n                                        this[q[0]] = match ? q[3].call(this, match.replace(q[1], q[2])) : undefined;\n                                }\n                            } else {\n                                this[q] = match ? match : undefined;\n                            }\n                        }\n                    }\n                }\n                i += 2;\n            }\n        },\n\n        strMapper = function (str, map) {\n\n            for (var i in map) {\n                // check if current value is array\n                if (typeof map[i] === OBJ_TYPE && map[i].length > 0) {\n                    for (var j = 0; j < map[i].length; j++) {\n                        if (has(map[i][j], str)) {\n                            return (i === UNKNOWN) ? undefined : i;\n                        }\n                    }\n                } else if (has(map[i], str)) {\n                    return (i === UNKNOWN) ? undefined : i;\n                }\n            }\n            return map.hasOwnProperty('*') ? map['*'] : str;\n    };\n\n    ///////////////\n    // String map\n    //////////////\n\n    // Safari < 3.0\n    var oldSafariMap = {\n            '1.0'   : '/8',\n            '1.2'   : '/1',\n            '1.3'   : '/3',\n            '2.0'   : '/412',\n            '2.0.2' : '/416',\n            '2.0.3' : '/417',\n            '2.0.4' : '/419',\n            '?'     : '/'\n        },\n        windowsVersionMap = {\n            'ME'        : '4.90',\n            'NT 3.11'   : 'NT3.51',\n            'NT 4.0'    : 'NT4.0',\n            '2000'      : 'NT 5.0',\n            'XP'        : ['NT 5.1', 'NT 5.2'],\n            'Vista'     : 'NT 6.0',\n            '7'         : 'NT 6.1',\n            '8'         : 'NT 6.2',\n            '8.1'       : 'NT 6.3',\n            '10'        : ['NT 6.4', 'NT 10.0'],\n            'RT'        : 'ARM'\n    };\n\n    //////////////\n    // Regex map\n    /////////////\n\n    var regexes = {\n\n        browser : [[\n\n            /\\b(?:crmo|crios)\\/([\\w\\.]+)/i                                      // Chrome for Android/iOS\n            ], [VERSION, [NAME, 'Chrome']], [\n            /edg(?:e|ios|a)?\\/([\\w\\.]+)/i                                       // Microsoft Edge\n            ], [VERSION, [NAME, 'Edge']], [\n\n            // Presto based\n            /(opera mini)\\/([-\\w\\.]+)/i,                                        // Opera Mini\n            /(opera [mobiletab]{3,6})\\b.+version\\/([-\\w\\.]+)/i,                 // Opera Mobi/Tablet\n            /(opera)(?:.+version\\/|[\\/ ]+)([\\w\\.]+)/i                           // Opera\n            ], [NAME, VERSION], [\n            /opios[\\/ ]+([\\w\\.]+)/i                                             // Opera mini on iphone >= 8.0\n            ], [VERSION, [NAME, OPERA+' Mini']], [\n            /\\bop(?:rg)?x\\/([\\w\\.]+)/i                                          // Opera GX\n            ], [VERSION, [NAME, OPERA+' GX']], [\n            /\\bopr\\/([\\w\\.]+)/i                                                 // Opera Webkit\n            ], [VERSION, [NAME, OPERA]], [\n\n            // Mixed\n            /\\bb[ai]*d(?:uhd|[ub]*[aekoprswx]{5,6})[\\/ ]?([\\w\\.]+)/i            // Baidu\n            ], [VERSION, [NAME, 'Baidu']], [\n            /\\b(?:mxbrowser|mxios|myie2)\\/?([-\\w\\.]*)\\b/i                       // Maxthon\n            ], [VERSION, [NAME, 'Maxthon']], [\n            /(kindle)\\/([\\w\\.]+)/i,                                             // Kindle\n            /(lunascape|maxthon|netfront|jasmine|blazer|sleipnir)[\\/ ]?([\\w\\.]*)/i,      \n                                                                                // Lunascape/Maxthon/Netfront/Jasmine/Blazer/Sleipnir\n            // Trident based\n            /(avant|iemobile|slim(?:browser|boat|jet))[\\/ ]?([\\d\\.]*)/i,        // Avant/IEMobile/SlimBrowser/SlimBoat/Slimjet\n            /(?:ms|\\()(ie) ([\\w\\.]+)/i,                                         // Internet Explorer\n\n            // Blink/Webkit/KHTML based                                         // Flock/RockMelt/Midori/Epiphany/Silk/Skyfire/Bolt/Iron/Iridium/PhantomJS/Bowser/QupZilla/Falkon\n            /(flock|rockmelt|midori|epiphany|silk|skyfire|ovibrowser|bolt|iron|vivaldi|iridium|phantomjs|bowser|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|duckduckgo|klar|helio|(?=comodo_)?dragon)\\/([-\\w\\.]+)/i,\n                                                                                // Rekonq/Puffin/Brave/Whale/QQBrowserLite/QQ//Vivaldi/DuckDuckGo/Klar/Helio/Dragon\n            /(heytap|ovi|115)browser\\/([\\d\\.]+)/i,                              // HeyTap/Ovi/115\n            /(weibo)__([\\d\\.]+)/i                                               // Weibo\n            ], [NAME, VERSION], [\n            /quark(?:pc)?\\/([-\\w\\.]+)/i                                         // Quark\n            ], [VERSION, [NAME, 'Quark']], [\n            /\\bddg\\/([\\w\\.]+)/i                                                 // DuckDuckGo\n            ], [VERSION, [NAME, 'DuckDuckGo']], [\n            /(?:\\buc? ?browser|(?:juc.+)ucweb)[\\/ ]?([\\w\\.]+)/i                 // UCBrowser\n            ], [VERSION, [NAME, 'UC'+BROWSER]], [\n            /microm.+\\bqbcore\\/([\\w\\.]+)/i,                                     // WeChat Desktop for Windows Built-in Browser\n            /\\bqbcore\\/([\\w\\.]+).+microm/i,\n            /micromessenger\\/([\\w\\.]+)/i                                        // WeChat\n            ], [VERSION, [NAME, 'WeChat']], [\n            /konqueror\\/([\\w\\.]+)/i                                             // Konqueror\n            ], [VERSION, [NAME, 'Konqueror']], [\n            /trident.+rv[: ]([\\w\\.]{1,9})\\b.+like gecko/i                       // IE11\n            ], [VERSION, [NAME, 'IE']], [\n            /ya(?:search)?browser\\/([\\w\\.]+)/i                                  // Yandex\n            ], [VERSION, [NAME, 'Yandex']], [\n            /slbrowser\\/([\\w\\.]+)/i                                             // Smart Lenovo Browser\n            ], [VERSION, [NAME, 'Smart Lenovo '+BROWSER]], [\n            /(avast|avg)\\/([\\w\\.]+)/i                                           // Avast/AVG Secure Browser\n            ], [[NAME, /(.+)/, '$1 Secure '+BROWSER], VERSION], [\n            /\\bfocus\\/([\\w\\.]+)/i                                               // Firefox Focus\n            ], [VERSION, [NAME, FIREFOX+' Focus']], [\n            /\\bopt\\/([\\w\\.]+)/i                                                 // Opera Touch\n            ], [VERSION, [NAME, OPERA+' Touch']], [\n            /coc_coc\\w+\\/([\\w\\.]+)/i                                            // Coc Coc Browser\n            ], [VERSION, [NAME, 'Coc Coc']], [\n            /dolfin\\/([\\w\\.]+)/i                                                // Dolphin\n            ], [VERSION, [NAME, 'Dolphin']], [\n            /coast\\/([\\w\\.]+)/i                                                 // Opera Coast\n            ], [VERSION, [NAME, OPERA+' Coast']], [\n            /miuibrowser\\/([\\w\\.]+)/i                                           // MIUI Browser\n            ], [VERSION, [NAME, 'MIUI' + SUFFIX_BROWSER]], [\n            /fxios\\/([\\w\\.-]+)/i                                                // Firefox for iOS\n            ], [VERSION, [NAME, FIREFOX]], [\n            /\\bqihoobrowser\\/?([\\w\\.]*)/i                                       // 360\n            ], [VERSION, [NAME, '360']], [\n            /\\b(qq)\\/([\\w\\.]+)/i                                                // QQ\n            ], [[NAME, /(.+)/, '$1Browser'], VERSION], [\n            /(oculus|sailfish|huawei|vivo|pico)browser\\/([\\w\\.]+)/i\n            ], [[NAME, /(.+)/, '$1' + SUFFIX_BROWSER], VERSION], [              // Oculus/Sailfish/HuaweiBrowser/VivoBrowser/PicoBrowser\n            /samsungbrowser\\/([\\w\\.]+)/i                                        // Samsung Internet\n            ], [VERSION, [NAME, SAMSUNG + ' Internet']], [\n            /metasr[\\/ ]?([\\d\\.]+)/i                                            // Sogou Explorer\n            ], [VERSION, [NAME, 'Sogou Explorer']], [\n            /(sogou)mo\\w+\\/([\\d\\.]+)/i                                          // Sogou Mobile\n            ], [[NAME, 'Sogou Mobile'], VERSION], [\n            /(electron)\\/([\\w\\.]+) safari/i,                                    // Electron-based App\n            /(tesla)(?: qtcarbrowser|\\/(20\\d\\d\\.[-\\w\\.]+))/i,                   // Tesla\n            /m?(qqbrowser|2345(?=browser|chrome|explorer))\\w*[\\/ ]?v?([\\w\\.]+)/i   // QQ/2345\n            ], [NAME, VERSION], [\n            /(lbbrowser|rekonq)/i,                                              // LieBao Browser/Rekonq\n            /\\[(linkedin)app\\]/i                                                // LinkedIn App for iOS & Android\n            ], [NAME], [\n            /ome\\/([\\w\\.]+) \\w* ?(iron) saf/i,                                  // Iron\n            /ome\\/([\\w\\.]+).+qihu (360)[es]e/i                                  // 360\n            ], [VERSION, NAME], [\n\n            // WebView\n            /((?:fban\\/fbios|fb_iab\\/fb4a)(?!.+fbav)|;fbav\\/([\\w\\.]+);)/i       // Facebook App for iOS & Android\n            ], [[NAME, FACEBOOK], VERSION], [\n            /(Klarna)\\/([\\w\\.]+)/i,                                             // Klarna Shopping Browser for iOS & Android\n            /(kakao(?:talk|story))[\\/ ]([\\w\\.]+)/i,                             // Kakao App\n            /(naver)\\(.*?(\\d+\\.[\\w\\.]+).*\\)/i,                                  // Naver InApp\n            /safari (line)\\/([\\w\\.]+)/i,                                        // Line App for iOS\n            /\\b(line)\\/([\\w\\.]+)\\/iab/i,                                        // Line App for Android\n            /(alipay)client\\/([\\w\\.]+)/i,                                       // Alipay\n            /(twitter)(?:and| f.+e\\/([\\w\\.]+))/i,                               // Twitter\n            /(chromium|instagram|snapchat)[\\/ ]([-\\w\\.]+)/i                     // Chromium/Instagram/Snapchat\n            ], [NAME, VERSION], [\n            /\\bgsa\\/([\\w\\.]+) .*safari\\//i                                      // Google Search Appliance on iOS\n            ], [VERSION, [NAME, 'GSA']], [\n            /musical_ly(?:.+app_?version\\/|_)([\\w\\.]+)/i                        // TikTok\n            ], [VERSION, [NAME, 'TikTok']], [\n\n            /headlesschrome(?:\\/([\\w\\.]+)| )/i                                  // Chrome Headless\n            ], [VERSION, [NAME, CHROME+' Headless']], [\n\n            / wv\\).+(chrome)\\/([\\w\\.]+)/i                                       // Chrome WebView\n            ], [[NAME, CHROME+' WebView'], VERSION], [\n\n            /droid.+ version\\/([\\w\\.]+)\\b.+(?:mobile safari|safari)/i           // Android Browser\n            ], [VERSION, [NAME, 'Android '+BROWSER]], [\n\n            /(chrome|omniweb|arora|[tizenoka]{5} ?browser)\\/v?([\\w\\.]+)/i       // Chrome/OmniWeb/Arora/Tizen/Nokia\n            ], [NAME, VERSION], [\n\n            /version\\/([\\w\\.\\,]+) .*mobile\\/\\w+ (safari)/i                      // Mobile Safari\n            ], [VERSION, [NAME, 'Mobile Safari']], [\n            /version\\/([\\w(\\.|\\,)]+) .*(mobile ?safari|safari)/i                // Safari & Safari Mobile\n            ], [VERSION, NAME], [\n            /webkit.+?(mobile ?safari|safari)(\\/[\\w\\.]+)/i                      // Safari < 3.0\n            ], [NAME, [VERSION, strMapper, oldSafariMap]], [\n\n            /(webkit|khtml)\\/([\\w\\.]+)/i\n            ], [NAME, VERSION], [\n\n            // Gecko based\n            /(navigator|netscape\\d?)\\/([-\\w\\.]+)/i                              // Netscape\n            ], [[NAME, 'Netscape'], VERSION], [\n            /(wolvic|librewolf)\\/([\\w\\.]+)/i                                    // Wolvic/LibreWolf\n            ], [NAME, VERSION], [\n            /mobile vr; rv:([\\w\\.]+)\\).+firefox/i                               // Firefox Reality\n            ], [VERSION, [NAME, FIREFOX+' Reality']], [\n            /ekiohf.+(flow)\\/([\\w\\.]+)/i,                                       // Flow\n            /(swiftfox)/i,                                                      // Swiftfox\n            /(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror)[\\/ ]?([\\w\\.\\+]+)/i,\n                                                                                // IceDragon/Iceweasel/Camino/Chimera/Fennec/Maemo/Minimo/Conkeror\n            /(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\\/([-\\w\\.]+)$/i,\n                                                                                // Firefox/SeaMonkey/K-Meleon/IceCat/IceApe/Firebird/Phoenix\n            /(firefox)\\/([\\w\\.]+)/i,                                            // Other Firefox-based\n            /(mozilla)\\/([\\w\\.]+) .+rv\\:.+gecko\\/\\d+/i,                         // Mozilla\n\n            // Other\n            /(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|obigo|mosaic|(?:go|ice|up)[\\. ]?browser)[-\\/ ]?v?([\\w\\.]+)/i,\n                                                                                // Polaris/Lynx/Dillo/iCab/Doris/Amaya/w3m/NetSurf/Obigo/Mosaic/Go/ICE/UP.Browser\n            /(links) \\(([\\w\\.]+)/i                                              // Links\n            ], [NAME, [VERSION, /_/g, '.']], [\n            \n            /(cobalt)\\/([\\w\\.]+)/i                                              // Cobalt\n            ], [NAME, [VERSION, /master.|lts./, \"\"]]\n        ],\n\n        cpu : [[\n\n            /(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\\)]/i                     // AMD64 (x64)\n            ], [[ARCHITECTURE, 'amd64']], [\n\n            /(ia32(?=;))/i                                                      // IA32 (quicktime)\n            ], [[ARCHITECTURE, lowerize]], [\n\n            /((?:i[346]|x)86)[;\\)]/i                                            // IA32 (x86)\n            ], [[ARCHITECTURE, 'ia32']], [\n\n            /\\b(aarch64|arm(v?8e?l?|_?64))\\b/i                                 // ARM64\n            ], [[ARCHITECTURE, 'arm64']], [\n\n            /\\b(arm(?:v[67])?ht?n?[fl]p?)\\b/i                                   // ARMHF\n            ], [[ARCHITECTURE, 'armhf']], [\n\n            // PocketPC mistakenly identified as PowerPC\n            /windows (ce|mobile); ppc;/i\n            ], [[ARCHITECTURE, 'arm']], [\n\n            /((?:ppc|powerpc)(?:64)?)(?: mac|;|\\))/i                            // PowerPC\n            ], [[ARCHITECTURE, /ower/, EMPTY, lowerize]], [\n\n            /(sun4\\w)[;\\)]/i                                                    // SPARC\n            ], [[ARCHITECTURE, 'sparc']], [\n\n            /((?:avr32|ia64(?=;))|68k(?=\\))|\\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\\b|pa-risc)/i\n                                                                                // IA64, 68K, ARM/64, AVR/32, IRIX/64, MIPS/64, SPARC/64, PA-RISC\n            ], [[ARCHITECTURE, lowerize]]\n        ],\n\n        device : [[\n\n            //////////////////////////\n            // MOBILES & TABLETS\n            /////////////////////////\n\n            // Samsung\n            /\\b(sch-i[89]0\\d|shw-m380s|sm-[ptx]\\w{2,4}|gt-[pn]\\d{2,4}|sgh-t8[56]9|nexus 10)/i\n            ], [MODEL, [VENDOR, SAMSUNG], [TYPE, TABLET]], [\n            /\\b((?:s[cgp]h|gt|sm)-(?![lr])\\w+|sc[g-]?[\\d]+a?|galaxy nexus)/i,\n            /samsung[- ]((?!sm-[lr])[-\\w]+)/i,\n            /sec-(sgh\\w+)/i\n            ], [MODEL, [VENDOR, SAMSUNG], [TYPE, MOBILE]], [\n\n            // Apple\n            /(?:\\/|\\()(ip(?:hone|od)[\\w, ]*)(?:\\/|;)/i                          // iPod/iPhone\n            ], [MODEL, [VENDOR, APPLE], [TYPE, MOBILE]], [\n            /\\((ipad);[-\\w\\),; ]+apple/i,                                       // iPad\n            /applecoremedia\\/[\\w\\.]+ \\((ipad)/i,\n            /\\b(ipad)\\d\\d?,\\d\\d?[;\\]].+ios/i\n            ], [MODEL, [VENDOR, APPLE], [TYPE, TABLET]], [\n            /(macintosh);/i\n            ], [MODEL, [VENDOR, APPLE]], [\n\n            // Sharp\n            /\\b(sh-?[altvz]?\\d\\d[a-ekm]?)/i\n            ], [MODEL, [VENDOR, SHARP], [TYPE, MOBILE]], [\n\n            // Honor\n            /(?:honor)([-\\w ]+)[;\\)]/i\n            ], [MODEL, [VENDOR, 'Honor'], [TYPE, MOBILE]], [\n\n            // Huawei\n            /\\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\\d{2})\\b(?!.+d\\/s)/i\n            ], [MODEL, [VENDOR, HUAWEI], [TYPE, TABLET]], [\n            /(?:huawei)([-\\w ]+)[;\\)]/i,\n            /\\b(nexus 6p|\\w{2,4}e?-[atu]?[ln][\\dx][012359c][adn]?)\\b(?!.+d\\/s)/i\n            ], [MODEL, [VENDOR, HUAWEI], [TYPE, MOBILE]], [\n\n            // Xiaomi\n            /\\b(poco[\\w ]+|m2\\d{3}j\\d\\d[a-z]{2})(?: bui|\\))/i,                  // Xiaomi POCO\n            /\\b; (\\w+) build\\/hm\\1/i,                                           // Xiaomi Hongmi 'numeric' models\n            /\\b(hm[-_ ]?note?[_ ]?(?:\\d\\w)?) bui/i,                             // Xiaomi Hongmi\n            /\\b(redmi[\\-_ ]?(?:note|k)?[\\w_ ]+)(?: bui|\\))/i,                   // Xiaomi Redmi\n            /oid[^\\)]+; (m?[12][0-389][01]\\w{3,6}[c-y])( bui|; wv|\\))/i,        // Xiaomi Redmi 'numeric' models\n            /\\b(mi[-_ ]?(?:a\\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\\d?\\w?)[_ ]?(?:plus|se|lite|pro)?)(?: bui|\\))/i // Xiaomi Mi\n            ], [[MODEL, /_/g, ' '], [VENDOR, XIAOMI], [TYPE, MOBILE]], [\n            /oid[^\\)]+; (2\\d{4}(283|rpbf)[cgl])( bui|\\))/i,                     // Redmi Pad\n            /\\b(mi[-_ ]?(?:pad)(?:[\\w_ ]+))(?: bui|\\))/i                        // Mi Pad tablets\n            ],[[MODEL, /_/g, ' '], [VENDOR, XIAOMI], [TYPE, TABLET]], [\n\n            // OPPO\n            /; (\\w+) bui.+ oppo/i,\n            /\\b(cph[12]\\d{3}|p(?:af|c[al]|d\\w|e[ar])[mt]\\d0|x9007|a101op)\\b/i\n            ], [MODEL, [VENDOR, 'OPPO'], [TYPE, MOBILE]], [\n            /\\b(opd2\\d{3}a?) bui/i\n            ], [MODEL, [VENDOR, 'OPPO'], [TYPE, TABLET]], [\n\n            // Vivo\n            /vivo (\\w+)(?: bui|\\))/i,\n            /\\b(v[12]\\d{3}\\w?[at])(?: bui|;)/i\n            ], [MODEL, [VENDOR, 'Vivo'], [TYPE, MOBILE]], [\n\n            // Realme\n            /\\b(rmx[1-3]\\d{3})(?: bui|;|\\))/i\n            ], [MODEL, [VENDOR, 'Realme'], [TYPE, MOBILE]], [\n\n            // Motorola\n            /\\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\\b[\\w ]+build\\//i,\n            /\\bmot(?:orola)?[- ](\\w*)/i,\n            /((?:moto[\\w\\(\\) ]+|xt\\d{3,4}|nexus 6)(?= bui|\\)))/i\n            ], [MODEL, [VENDOR, MOTOROLA], [TYPE, MOBILE]], [\n            /\\b(mz60\\d|xoom[2 ]{0,2}) build\\//i\n            ], [MODEL, [VENDOR, MOTOROLA], [TYPE, TABLET]], [\n\n            // LG\n            /((?=lg)?[vl]k\\-?\\d{3}) bui| 3\\.[-\\w; ]{10}lg?-([06cv9]{3,4})/i\n            ], [MODEL, [VENDOR, LG], [TYPE, TABLET]], [\n            /(lm(?:-?f100[nv]?|-[\\w\\.]+)(?= bui|\\))|nexus [45])/i,\n            /\\blg[-e;\\/ ]+((?!browser|netcast|android tv)\\w+)/i,\n            /\\blg-?([\\d\\w]+) bui/i\n            ], [MODEL, [VENDOR, LG], [TYPE, MOBILE]], [\n\n            // Lenovo\n            /(ideatab[-\\w ]+)/i,\n            /lenovo ?(s[56]000[-\\w]+|tab(?:[\\w ]+)|yt[-\\d\\w]{6}|tb[-\\d\\w]{6})/i\n            ], [MODEL, [VENDOR, 'Lenovo'], [TYPE, TABLET]], [\n\n            // Nokia\n            /(?:maemo|nokia).*(n900|lumia \\d+)/i,\n            /nokia[-_ ]?([-\\w\\.]*)/i\n            ], [[MODEL, /_/g, ' '], [VENDOR, 'Nokia'], [TYPE, MOBILE]], [\n\n            // Google\n            /(pixel c)\\b/i                                                      // Google Pixel C\n            ], [MODEL, [VENDOR, GOOGLE], [TYPE, TABLET]], [\n            /droid.+; (pixel[\\daxl ]{0,6})(?: bui|\\))/i                         // Google Pixel\n            ], [MODEL, [VENDOR, GOOGLE], [TYPE, MOBILE]], [\n\n            // Sony\n            /droid.+; (a?\\d[0-2]{2}so|[c-g]\\d{4}|so[-gl]\\w+|xq-a\\w[4-7][12])(?= bui|\\).+chrome\\/(?![1-6]{0,1}\\d\\.))/i\n            ], [MODEL, [VENDOR, SONY], [TYPE, MOBILE]], [\n            /sony tablet [ps]/i,\n            /\\b(?:sony)?sgp\\w+(?: bui|\\))/i\n            ], [[MODEL, 'Xperia Tablet'], [VENDOR, SONY], [TYPE, TABLET]], [\n\n            // OnePlus\n            / (kb2005|in20[12]5|be20[12][59])\\b/i,\n            /(?:one)?(?:plus)? (a\\d0\\d\\d)(?: b|\\))/i\n            ], [MODEL, [VENDOR, 'OnePlus'], [TYPE, MOBILE]], [\n\n            // Amazon\n            /(alexa)webm/i,\n            /(kf[a-z]{2}wi|aeo(?!bc)\\w\\w)( bui|\\))/i,                           // Kindle Fire without Silk / Echo Show\n            /(kf[a-z]+)( bui|\\)).+silk\\//i                                      // Kindle Fire HD\n            ], [MODEL, [VENDOR, AMAZON], [TYPE, TABLET]], [\n            /((?:sd|kf)[0349hijorstuw]+)( bui|\\)).+silk\\//i                     // Fire Phone\n            ], [[MODEL, /(.+)/g, 'Fire Phone $1'], [VENDOR, AMAZON], [TYPE, MOBILE]], [\n\n            // BlackBerry\n            /(playbook);[-\\w\\),; ]+(rim)/i                                      // BlackBerry PlayBook\n            ], [MODEL, VENDOR, [TYPE, TABLET]], [\n            /\\b((?:bb[a-f]|st[hv])100-\\d)/i,\n            /\\(bb10; (\\w+)/i                                                    // BlackBerry 10\n            ], [MODEL, [VENDOR, BLACKBERRY], [TYPE, MOBILE]], [\n\n            // Asus\n            /(?:\\b|asus_)(transfo[prime ]{4,10} \\w+|eeepc|slider \\w+|nexus 7|padfone|p00[cj])/i\n            ], [MODEL, [VENDOR, ASUS], [TYPE, TABLET]], [\n            / (z[bes]6[027][012][km][ls]|zenfone \\d\\w?)\\b/i\n            ], [MODEL, [VENDOR, ASUS], [TYPE, MOBILE]], [\n\n            // HTC\n            /(nexus 9)/i                                                        // HTC Nexus 9\n            ], [MODEL, [VENDOR, 'HTC'], [TYPE, TABLET]], [\n            /(htc)[-;_ ]{1,2}([\\w ]+(?=\\)| bui)|\\w+)/i,                         // HTC\n\n            // ZTE\n            /(zte)[- ]([\\w ]+?)(?: bui|\\/|\\))/i,\n            /(alcatel|geeksphone|nexian|panasonic(?!(?:;|\\.))|sony(?!-bra))[-_ ]?([-\\w]*)/i         // Alcatel/GeeksPhone/Nexian/Panasonic/Sony\n            ], [VENDOR, [MODEL, /_/g, ' '], [TYPE, MOBILE]], [\n\n            // TCL\n            /droid [\\w\\.]+; ((?:8[14]9[16]|9(?:0(?:48|60|8[01])|1(?:3[27]|66)|2(?:6[69]|9[56])|466))[gqswx])\\w*(\\)| bui)/i\n            ], [MODEL, [VENDOR, 'TCL'], [TYPE, TABLET]], [\n\n            // itel\n            /(itel) ((\\w+))/i\n            ], [[VENDOR, lowerize], MODEL, [TYPE, strMapper, { 'tablet' : ['p10001l', 'w7001'], '*' : 'mobile' }]], [\n\n            // Acer\n            /droid.+; ([ab][1-7]-?[0178a]\\d\\d?)/i\n            ], [MODEL, [VENDOR, 'Acer'], [TYPE, TABLET]], [\n\n            // Meizu\n            /droid.+; (m[1-5] note) bui/i,\n            /\\bmz-([-\\w]{2,})/i\n            ], [MODEL, [VENDOR, 'Meizu'], [TYPE, MOBILE]], [\n                \n            // Ulefone\n            /; ((?:power )?armor(?:[\\w ]{0,8}))(?: bui|\\))/i\n            ], [MODEL, [VENDOR, 'Ulefone'], [TYPE, MOBILE]], [\n\n            // Energizer\n            /; (energy ?\\w+)(?: bui|\\))/i,\n            /; energizer ([\\w ]+)(?: bui|\\))/i\n            ], [MODEL, [VENDOR, 'Energizer'], [TYPE, MOBILE]], [\n\n            // Cat\n            /; cat (b35);/i,\n            /; (b15q?|s22 flip|s48c|s62 pro)(?: bui|\\))/i\n            ], [MODEL, [VENDOR, 'Cat'], [TYPE, MOBILE]], [\n\n            // Smartfren\n            /((?:new )?andromax[\\w- ]+)(?: bui|\\))/i\n            ], [MODEL, [VENDOR, 'Smartfren'], [TYPE, MOBILE]], [\n\n            // Nothing\n            /droid.+; (a(?:015|06[35]|142p?))/i\n            ], [MODEL, [VENDOR, 'Nothing'], [TYPE, MOBILE]], [\n\n            // MIXED\n            /(blackberry|benq|palm(?=\\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron|infinix|tecno|micromax|advan)[-_ ]?([-\\w]*)/i,\n                                                                                // BlackBerry/BenQ/Palm/Sony-Ericsson/Acer/Asus/Dell/Meizu/Motorola/Polytron/Infinix/Tecno/Micromax/Advan\n            /; (imo) ((?!tab)[\\w ]+?)(?: bui|\\))/i,                             // IMO\n            /(hp) ([\\w ]+\\w)/i,                                                 // HP iPAQ\n            /(asus)-?(\\w+)/i,                                                   // Asus\n            /(microsoft); (lumia[\\w ]+)/i,                                      // Microsoft Lumia\n            /(lenovo)[-_ ]?([-\\w]+)/i,                                          // Lenovo\n            /(jolla)/i,                                                         // Jolla\n            /(oppo) ?([\\w ]+) bui/i                                             // OPPO\n            ], [VENDOR, MODEL, [TYPE, MOBILE]], [\n\n            /(imo) (tab \\w+)/i,                                                 // IMO\n            /(kobo)\\s(ereader|touch)/i,                                         // Kobo\n            /(archos) (gamepad2?)/i,                                            // Archos\n            /(hp).+(touchpad(?!.+tablet)|tablet)/i,                             // HP TouchPad\n            /(kindle)\\/([\\w\\.]+)/i,                                             // Kindle\n            /(nook)[\\w ]+build\\/(\\w+)/i,                                        // Nook\n            /(dell) (strea[kpr\\d ]*[\\dko])/i,                                   // Dell Streak\n            /(le[- ]+pan)[- ]+(\\w{1,9}) bui/i,                                  // Le Pan Tablets\n            /(trinity)[- ]*(t\\d{3}) bui/i,                                      // Trinity Tablets\n            /(gigaset)[- ]+(q\\w{1,9}) bui/i,                                    // Gigaset Tablets\n            /(vodafone) ([\\w ]+)(?:\\)| bui)/i                                   // Vodafone\n            ], [VENDOR, MODEL, [TYPE, TABLET]], [\n\n            /(surface duo)/i                                                    // Surface Duo\n            ], [MODEL, [VENDOR, MICROSOFT], [TYPE, TABLET]], [\n            /droid [\\d\\.]+; (fp\\du?)(?: b|\\))/i                                 // Fairphone\n            ], [MODEL, [VENDOR, 'Fairphone'], [TYPE, MOBILE]], [\n            /(u304aa)/i                                                         // AT&T\n            ], [MODEL, [VENDOR, 'AT&T'], [TYPE, MOBILE]], [\n            /\\bsie-(\\w*)/i                                                      // Siemens\n            ], [MODEL, [VENDOR, 'Siemens'], [TYPE, MOBILE]], [\n            /\\b(rct\\w+) b/i                                                     // RCA Tablets\n            ], [MODEL, [VENDOR, 'RCA'], [TYPE, TABLET]], [\n            /\\b(venue[\\d ]{2,7}) b/i                                            // Dell Venue Tablets\n            ], [MODEL, [VENDOR, 'Dell'], [TYPE, TABLET]], [\n            /\\b(q(?:mv|ta)\\w+) b/i                                              // Verizon Tablet\n            ], [MODEL, [VENDOR, 'Verizon'], [TYPE, TABLET]], [\n            /\\b(?:barnes[& ]+noble |bn[rt])([\\w\\+ ]*) b/i                       // Barnes & Noble Tablet\n            ], [MODEL, [VENDOR, 'Barnes & Noble'], [TYPE, TABLET]], [\n            /\\b(tm\\d{3}\\w+) b/i\n            ], [MODEL, [VENDOR, 'NuVision'], [TYPE, TABLET]], [\n            /\\b(k88) b/i                                                        // ZTE K Series Tablet\n            ], [MODEL, [VENDOR, 'ZTE'], [TYPE, TABLET]], [\n            /\\b(nx\\d{3}j) b/i                                                   // ZTE Nubia\n            ], [MODEL, [VENDOR, 'ZTE'], [TYPE, MOBILE]], [\n            /\\b(gen\\d{3}) b.+49h/i                                              // Swiss GEN Mobile\n            ], [MODEL, [VENDOR, 'Swiss'], [TYPE, MOBILE]], [\n            /\\b(zur\\d{3}) b/i                                                   // Swiss ZUR Tablet\n            ], [MODEL, [VENDOR, 'Swiss'], [TYPE, TABLET]], [\n            /\\b((zeki)?tb.*\\b) b/i                                              // Zeki Tablets\n            ], [MODEL, [VENDOR, 'Zeki'], [TYPE, TABLET]], [\n            /\\b([yr]\\d{2}) b/i,\n            /\\b(dragon[- ]+touch |dt)(\\w{5}) b/i                                // Dragon Touch Tablet\n            ], [[VENDOR, 'Dragon Touch'], MODEL, [TYPE, TABLET]], [\n            /\\b(ns-?\\w{0,9}) b/i                                                // Insignia Tablets\n            ], [MODEL, [VENDOR, 'Insignia'], [TYPE, TABLET]], [\n            /\\b((nxa|next)-?\\w{0,9}) b/i                                        // NextBook Tablets\n            ], [MODEL, [VENDOR, 'NextBook'], [TYPE, TABLET]], [\n            /\\b(xtreme\\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i                  // Voice Xtreme Phones\n            ], [[VENDOR, 'Voice'], MODEL, [TYPE, MOBILE]], [\n            /\\b(lvtel\\-)?(v1[12]) b/i                                           // LvTel Phones\n            ], [[VENDOR, 'LvTel'], MODEL, [TYPE, MOBILE]], [\n            /\\b(ph-1) /i                                                        // Essential PH-1\n            ], [MODEL, [VENDOR, 'Essential'], [TYPE, MOBILE]], [\n            /\\b(v(100md|700na|7011|917g).*\\b) b/i                               // Envizen Tablets\n            ], [MODEL, [VENDOR, 'Envizen'], [TYPE, TABLET]], [\n            /\\b(trio[-\\w\\. ]+) b/i                                              // MachSpeed Tablets\n            ], [MODEL, [VENDOR, 'MachSpeed'], [TYPE, TABLET]], [\n            /\\btu_(1491) b/i                                                    // Rotor Tablets\n            ], [MODEL, [VENDOR, 'Rotor'], [TYPE, TABLET]], [\n            /(shield[\\w ]+) b/i                                                 // Nvidia Shield Tablets\n            ], [MODEL, [VENDOR, 'Nvidia'], [TYPE, TABLET]], [\n            /(sprint) (\\w+)/i                                                   // Sprint Phones\n            ], [VENDOR, MODEL, [TYPE, MOBILE]], [\n            /(kin\\.[onetw]{3})/i                                                // Microsoft Kin\n            ], [[MODEL, /\\./g, ' '], [VENDOR, MICROSOFT], [TYPE, MOBILE]], [\n            /droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\\)/i             // Zebra\n            ], [MODEL, [VENDOR, ZEBRA], [TYPE, TABLET]], [\n            /droid.+; (ec30|ps20|tc[2-8]\\d[kx])\\)/i\n            ], [MODEL, [VENDOR, ZEBRA], [TYPE, MOBILE]], [\n\n            ///////////////////\n            // SMARTTVS\n            ///////////////////\n\n            /smart-tv.+(samsung)/i                                              // Samsung\n            ], [VENDOR, [TYPE, SMARTTV]], [\n            /hbbtv.+maple;(\\d+)/i\n            ], [[MODEL, /^/, 'SmartTV'], [VENDOR, SAMSUNG], [TYPE, SMARTTV]], [\n            /(nux; netcast.+smarttv|lg (netcast\\.tv-201\\d|android tv))/i        // LG SmartTV\n            ], [[VENDOR, LG], [TYPE, SMARTTV]], [\n            /(apple) ?tv/i                                                      // Apple TV\n            ], [VENDOR, [MODEL, APPLE+' TV'], [TYPE, SMARTTV]], [\n            /crkey/i                                                            // Google Chromecast\n            ], [[MODEL, CHROME+'cast'], [VENDOR, GOOGLE], [TYPE, SMARTTV]], [\n            /droid.+aft(\\w+)( bui|\\))/i                                         // Fire TV\n            ], [MODEL, [VENDOR, AMAZON], [TYPE, SMARTTV]], [\n            /\\(dtv[\\);].+(aquos)/i,\n            /(aquos-tv[\\w ]+)\\)/i                                               // Sharp\n            ], [MODEL, [VENDOR, SHARP], [TYPE, SMARTTV]],[\n            /(bravia[\\w ]+)( bui|\\))/i                                              // Sony\n            ], [MODEL, [VENDOR, SONY], [TYPE, SMARTTV]], [\n            /(mitv-\\w{5}) bui/i                                                 // Xiaomi\n            ], [MODEL, [VENDOR, XIAOMI], [TYPE, SMARTTV]], [\n            /Hbbtv.*(technisat) (.*);/i                                         // TechniSAT\n            ], [VENDOR, MODEL, [TYPE, SMARTTV]], [\n            /\\b(roku)[\\dx]*[\\)\\/]((?:dvp-)?[\\d\\.]*)/i,                          // Roku\n            /hbbtv\\/\\d+\\.\\d+\\.\\d+ +\\([\\w\\+ ]*; *([\\w\\d][^;]*);([^;]*)/i         // HbbTV devices\n            ], [[VENDOR, trim], [MODEL, trim], [TYPE, SMARTTV]], [\n            /\\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\\b/i                   // SmartTV from Unidentified Vendors\n            ], [[TYPE, SMARTTV]], [\n\n            ///////////////////\n            // CONSOLES\n            ///////////////////\n\n            /(ouya)/i,                                                          // Ouya\n            /(nintendo) ([wids3utch]+)/i                                        // Nintendo\n            ], [VENDOR, MODEL, [TYPE, CONSOLE]], [\n            /droid.+; (shield) bui/i                                            // Nvidia\n            ], [MODEL, [VENDOR, 'Nvidia'], [TYPE, CONSOLE]], [\n            /(playstation [345portablevi]+)/i                                   // Playstation\n            ], [MODEL, [VENDOR, SONY], [TYPE, CONSOLE]], [\n            /\\b(xbox(?: one)?(?!; xbox))[\\); ]/i                                // Microsoft Xbox\n            ], [MODEL, [VENDOR, MICROSOFT], [TYPE, CONSOLE]], [\n\n            ///////////////////\n            // WEARABLES\n            ///////////////////\n\n            /\\b(sm-[lr]\\d\\d[05][fnuw]?s?)\\b/i                                   // Samsung Galaxy Watch\n            ], [MODEL, [VENDOR, SAMSUNG], [TYPE, WEARABLE]], [\n            /((pebble))app/i                                                    // Pebble\n            ], [VENDOR, MODEL, [TYPE, WEARABLE]], [\n            /(watch)(?: ?os[,\\/]|\\d,\\d\\/)[\\d\\.]+/i                              // Apple Watch\n            ], [MODEL, [VENDOR, APPLE], [TYPE, WEARABLE]], [\n            /droid.+; (glass) \\d/i                                              // Google Glass\n            ], [MODEL, [VENDOR, GOOGLE], [TYPE, WEARABLE]], [\n            /droid.+; (wt63?0{2,3})\\)/i\n            ], [MODEL, [VENDOR, ZEBRA], [TYPE, WEARABLE]], [\n\n            ///////////////////\n            // XR\n            ///////////////////\n\n            /droid.+; (glass) \\d/i                                              // Google Glass\n            ], [MODEL, [VENDOR, GOOGLE], [TYPE, WEARABLE]], [\n            /(pico) (4|neo3(?: link|pro)?)/i                                    // Pico\n            ], [VENDOR, MODEL, [TYPE, WEARABLE]], [\n            /; (quest( \\d| pro)?)/i                                             // Oculus Quest\n            ], [MODEL, [VENDOR, FACEBOOK], [TYPE, WEARABLE]], [\n\n            ///////////////////\n            // EMBEDDED\n            ///////////////////\n\n            /(tesla)(?: qtcarbrowser|\\/[-\\w\\.]+)/i                              // Tesla\n            ], [VENDOR, [TYPE, EMBEDDED]], [\n            /(aeobc)\\b/i                                                        // Echo Dot\n            ], [MODEL, [VENDOR, AMAZON], [TYPE, EMBEDDED]], [\n\n            ////////////////////\n            // MIXED (GENERIC)\n            ///////////////////\n\n            /droid .+?; ([^;]+?)(?: bui|; wv\\)|\\) applew).+? mobile safari/i    // Android Phones from Unidentified Vendors\n            ], [MODEL, [TYPE, MOBILE]], [\n            /droid .+?; ([^;]+?)(?: bui|\\) applew).+?(?! mobile) safari/i       // Android Tablets from Unidentified Vendors\n            ], [MODEL, [TYPE, TABLET]], [\n            /\\b((tablet|tab)[;\\/]|focus\\/\\d(?!.+mobile))/i                      // Unidentifiable Tablet\n            ], [[TYPE, TABLET]], [\n            /(phone|mobile(?:[;\\/]| [ \\w\\/\\.]*safari)|pda(?=.+windows ce))/i    // Unidentifiable Mobile\n            ], [[TYPE, MOBILE]], [\n            /(android[-\\w\\. ]{0,9});.+buil/i                                    // Generic Android Device\n            ], [MODEL, [VENDOR, 'Generic']]\n        ],\n\n        engine : [[\n\n            /windows.+ edge\\/([\\w\\.]+)/i                                       // EdgeHTML\n            ], [VERSION, [NAME, EDGE+'HTML']], [\n\n            /(arkweb)\\/([\\w\\.]+)/i                                              // ArkWeb\n            ], [NAME, VERSION], [\n\n            /webkit\\/537\\.36.+chrome\\/(?!27)([\\w\\.]+)/i                         // Blink\n            ], [VERSION, [NAME, 'Blink']], [\n\n            /(presto)\\/([\\w\\.]+)/i,                                             // Presto\n            /(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna|servo)\\/([\\w\\.]+)/i, // WebKit/Trident/NetFront/NetSurf/Amaya/Lynx/w3m/Goanna/Servo\n            /ekioh(flow)\\/([\\w\\.]+)/i,                                          // Flow\n            /(khtml|tasman|links)[\\/ ]\\(?([\\w\\.]+)/i,                           // KHTML/Tasman/Links\n            /(icab)[\\/ ]([23]\\.[\\d\\.]+)/i,                                      // iCab\n            /\\b(libweb)/i\n            ], [NAME, VERSION], [\n\n            /rv\\:([\\w\\.]{1,9})\\b.+(gecko)/i                                     // Gecko\n            ], [VERSION, NAME]\n        ],\n\n        os : [[\n\n            // Windows\n            /microsoft (windows) (vista|xp)/i                                   // Windows (iTunes)\n            ], [NAME, VERSION], [\n            /(windows (?:phone(?: os)?|mobile))[\\/ ]?([\\d\\.\\w ]*)/i             // Windows Phone\n            ], [NAME, [VERSION, strMapper, windowsVersionMap]], [\n            /windows nt 6\\.2; (arm)/i,                                        // Windows RT\n            /windows[\\/ ]?([ntce\\d\\. ]+\\w)(?!.+xbox)/i,\n            /(?:win(?=3|9|n)|win 9x )([nt\\d\\.]+)/i\n            ], [[VERSION, strMapper, windowsVersionMap], [NAME, 'Windows']], [\n\n            // iOS/macOS\n            /ip[honead]{2,4}\\b(?:.*os ([\\w]+) like mac|; opera)/i,              // iOS\n            /(?:ios;fbsv\\/|iphone.+ios[\\/ ])([\\d\\.]+)/i,\n            /cfnetwork\\/.+darwin/i\n            ], [[VERSION, /_/g, '.'], [NAME, 'iOS']], [\n            /(mac os x) ?([\\w\\. ]*)/i,\n            /(macintosh|mac_powerpc\\b)(?!.+haiku)/i                             // Mac OS\n            ], [[NAME, MAC_OS], [VERSION, /_/g, '.']], [\n\n            // Mobile OSes\n            /droid ([\\w\\.]+)\\b.+(android[- ]x86|harmonyos)/i                    // Android-x86/HarmonyOS\n            ], [VERSION, NAME], [                                               // Android/WebOS/QNX/Bada/RIM/Maemo/MeeGo/Sailfish OS/OpenHarmony\n            /(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish|openharmony)[-\\/ ]?([\\w\\.]*)/i,\n            /(blackberry)\\w*\\/([\\w\\.]*)/i,                                      // Blackberry\n            /(tizen|kaios)[\\/ ]([\\w\\.]+)/i,                                     // Tizen/KaiOS\n            /\\((series40);/i                                                    // Series 40\n            ], [NAME, VERSION], [\n            /\\(bb(10);/i                                                        // BlackBerry 10\n            ], [VERSION, [NAME, BLACKBERRY]], [\n            /(?:symbian ?os|symbos|s60(?=;)|series60)[-\\/ ]?([\\w\\.]*)/i         // Symbian\n            ], [VERSION, [NAME, 'Symbian']], [\n            /mozilla\\/[\\d\\.]+ \\((?:mobile|tablet|tv|mobile; [\\w ]+); rv:.+ gecko\\/([\\w\\.]+)/i // Firefox OS\n            ], [VERSION, [NAME, FIREFOX+' OS']], [\n            /web0s;.+rt(tv)/i,\n            /\\b(?:hp)?wos(?:browser)?\\/([\\w\\.]+)/i                              // WebOS\n            ], [VERSION, [NAME, 'webOS']], [\n            /watch(?: ?os[,\\/]|\\d,\\d\\/)([\\d\\.]+)/i                              // watchOS\n            ], [VERSION, [NAME, 'watchOS']], [\n\n            // Google Chromecast\n            /crkey\\/([\\d\\.]+)/i                                                 // Google Chromecast\n            ], [VERSION, [NAME, CHROME+'cast']], [\n            /(cros) [\\w]+(?:\\)| ([\\w\\.]+)\\b)/i                                  // Chromium OS\n            ], [[NAME, CHROMIUM_OS], VERSION],[\n\n            // Smart TVs\n            /panasonic;(viera)/i,                                               // Panasonic Viera\n            /(netrange)mmh/i,                                                   // Netrange\n            /(nettv)\\/(\\d+\\.[\\w\\.]+)/i,                                         // NetTV\n\n            // Console\n            /(nintendo|playstation) ([wids345portablevuch]+)/i,                 // Nintendo/Playstation\n            /(xbox); +xbox ([^\\);]+)/i,                                         // Microsoft Xbox (360, One, X, S, Series X, Series S)\n\n            // Other\n            /\\b(joli|palm)\\b ?(?:os)?\\/?([\\w\\.]*)/i,                            // Joli/Palm\n            /(mint)[\\/\\(\\) ]?(\\w*)/i,                                           // Mint\n            /(mageia|vectorlinux)[; ]/i,                                        // Mageia/VectorLinux\n            /([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\\/ ]?(?!chrom|package)([-\\w\\.]*)/i,\n                                                                                // Ubuntu/Debian/SUSE/Gentoo/Arch/Slackware/Fedora/Mandriva/CentOS/PCLinuxOS/RedHat/Zenwalk/Linpus/Raspbian/Plan9/Minix/RISCOS/Contiki/Deepin/Manjaro/elementary/Sabayon/Linspire\n            /(hurd|linux) ?([\\w\\.]*)/i,                                         // Hurd/Linux\n            /(gnu) ?([\\w\\.]*)/i,                                                // GNU\n            /\\b([-frentopcghs]{0,5}bsd|dragonfly)[\\/ ]?(?!amd|[ix346]{1,2}86)([\\w\\.]*)/i, // FreeBSD/NetBSD/OpenBSD/PC-BSD/GhostBSD/DragonFly\n            /(haiku) (\\w+)/i                                                    // Haiku\n            ], [NAME, VERSION], [\n            /(sunos) ?([\\w\\.\\d]*)/i                                             // Solaris\n            ], [[NAME, 'Solaris'], VERSION], [\n            /((?:open)?solaris)[-\\/ ]?([\\w\\.]*)/i,                              // Solaris\n            /(aix) ((\\d)(?=\\.|\\)| )[\\w\\.])*/i,                                  // AIX\n            /\\b(beos|os\\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i, // BeOS/OS2/AmigaOS/MorphOS/OpenVMS/Fuchsia/HP-UX/SerenityOS\n            /(unix) ?([\\w\\.]*)/i                                                // UNIX\n            ], [NAME, VERSION]\n        ]\n    };\n\n    /////////////////\n    // Constructor\n    ////////////////\n\n    var UAParser = function (ua, extensions) {\n\n        if (typeof ua === OBJ_TYPE) {\n            extensions = ua;\n            ua = undefined;\n        }\n\n        if (!(this instanceof UAParser)) {\n            return new UAParser(ua, extensions).getResult();\n        }\n\n        var _navigator = (typeof window !== UNDEF_TYPE && window.navigator) ? window.navigator : undefined;\n        var _ua = ua || ((_navigator && _navigator.userAgent) ? _navigator.userAgent : EMPTY);\n        var _uach = (_navigator && _navigator.userAgentData) ? _navigator.userAgentData : undefined;\n        var _rgxmap = extensions ? extend(regexes, extensions) : regexes;\n        var _isSelfNav = _navigator && _navigator.userAgent == _ua;\n\n        this.getBrowser = function () {\n            var _browser = {};\n            _browser[NAME] = undefined;\n            _browser[VERSION] = undefined;\n            rgxMapper.call(_browser, _ua, _rgxmap.browser);\n            _browser[MAJOR] = majorize(_browser[VERSION]);\n            // Brave-specific detection\n            if (_isSelfNav && _navigator && _navigator.brave && typeof _navigator.brave.isBrave == FUNC_TYPE) {\n                _browser[NAME] = 'Brave';\n            }\n            return _browser;\n        };\n        this.getCPU = function () {\n            var _cpu = {};\n            _cpu[ARCHITECTURE] = undefined;\n            rgxMapper.call(_cpu, _ua, _rgxmap.cpu);\n            return _cpu;\n        };\n        this.getDevice = function () {\n            var _device = {};\n            _device[VENDOR] = undefined;\n            _device[MODEL] = undefined;\n            _device[TYPE] = undefined;\n            rgxMapper.call(_device, _ua, _rgxmap.device);\n            if (_isSelfNav && !_device[TYPE] && _uach && _uach.mobile) {\n                _device[TYPE] = MOBILE;\n            }\n            // iPadOS-specific detection: identified as Mac, but has some iOS-only properties\n            if (_isSelfNav && _device[MODEL] == 'Macintosh' && _navigator && typeof _navigator.standalone !== UNDEF_TYPE && _navigator.maxTouchPoints && _navigator.maxTouchPoints > 2) {\n                _device[MODEL] = 'iPad';\n                _device[TYPE] = TABLET;\n            }\n            return _device;\n        };\n        this.getEngine = function () {\n            var _engine = {};\n            _engine[NAME] = undefined;\n            _engine[VERSION] = undefined;\n            rgxMapper.call(_engine, _ua, _rgxmap.engine);\n            return _engine;\n        };\n        this.getOS = function () {\n            var _os = {};\n            _os[NAME] = undefined;\n            _os[VERSION] = undefined;\n            rgxMapper.call(_os, _ua, _rgxmap.os);\n            if (_isSelfNav && !_os[NAME] && _uach && _uach.platform && _uach.platform != 'Unknown') {\n                _os[NAME] = _uach.platform  \n                                    .replace(/chrome os/i, CHROMIUM_OS)\n                                    .replace(/macos/i, MAC_OS);           // backward compatibility\n            }\n            return _os;\n        };\n        this.getResult = function () {\n            return {\n                ua      : this.getUA(),\n                browser : this.getBrowser(),\n                engine  : this.getEngine(),\n                os      : this.getOS(),\n                device  : this.getDevice(),\n                cpu     : this.getCPU()\n            };\n        };\n        this.getUA = function () {\n            return _ua;\n        };\n        this.setUA = function (ua) {\n            _ua = (typeof ua === STR_TYPE && ua.length > UA_MAX_LENGTH) ? trim(ua, UA_MAX_LENGTH) : ua;\n            return this;\n        };\n        this.setUA(_ua);\n        return this;\n    };\n\n    UAParser.VERSION = LIBVERSION;\n    UAParser.BROWSER =  enumerize([NAME, VERSION, MAJOR]);\n    UAParser.CPU = enumerize([ARCHITECTURE]);\n    UAParser.DEVICE = enumerize([MODEL, VENDOR, TYPE, CONSOLE, MOBILE, SMARTTV, TABLET, WEARABLE, EMBEDDED]);\n    UAParser.ENGINE = UAParser.OS = enumerize([NAME, VERSION]);\n\n    ///////////\n    // Export\n    //////////\n\n    // check js environment\n    if (typeof(exports) !== UNDEF_TYPE) {\n        // nodejs env\n        if (typeof module !== UNDEF_TYPE && module.exports) {\n            exports = module.exports = UAParser;\n        }\n        exports.UAParser = UAParser;\n    } else {\n        // requirejs env (optional)\n        if (typeof(define) === FUNC_TYPE && define.amd) {\n            define(function () {\n                return UAParser;\n            });\n        } else if (typeof window !== UNDEF_TYPE) {\n            // browser env\n            window.UAParser = UAParser;\n        }\n    }\n\n    // jQuery/Zepto specific (optional)\n    // Note:\n    //   In AMD env the global scope should be kept clean, but jQuery is an exception.\n    //   jQuery always exports to global scope, unless jQuery.noConflict(true) is used,\n    //   and we should catch that.\n    var $ = typeof window !== UNDEF_TYPE && (window.jQuery || window.Zepto);\n    if ($ && !$.ua) {\n        var parser = new UAParser();\n        $.ua = parser.getResult();\n        $.ua.get = function () {\n            return parser.getUA();\n        };\n        $.ua.set = function (ua) {\n            parser.setUA(ua);\n            var result = parser.getResult();\n            for (var prop in result) {\n                $.ua[prop] = result[prop];\n            }\n        };\n    }\n\n})(typeof window === 'object' ? window : this);\n", "/* globals __webpack_amd_options__ */\nmodule.exports = __webpack_amd_options__;\n", "'use strict';\nvar $ = require('../internals/export');\nvar call = require('../internals/function-call');\nvar aCallable = require('../internals/a-callable');\nvar anObject = require('../internals/an-object');\nvar getIteratorDirect = require('../internals/get-iterator-direct');\nvar createIteratorProxy = require('../internals/iterator-create-proxy');\nvar callWithSafeIterationClosing = require('../internals/call-with-safe-iteration-closing');\nvar IS_PURE = require('../internals/is-pure');\nvar iteratorClose = require('../internals/iterator-close');\nvar iteratorHelperThrowsOnInvalidIterator = require('../internals/iterator-helper-throws-on-invalid-iterator');\nvar iteratorHelperWithoutClosingOnEarlyError = require('../internals/iterator-helper-without-closing-on-early-error');\n\nvar FILTER_WITHOUT_THROWING_ON_INVALID_ITERATOR = !IS_PURE && !iteratorHelperThrowsOnInvalidIterator('filter', function () { /* empty */ });\nvar filterWithoutClosingOnEarlyError = !IS_PURE && !FILTER_WITHOUT_THROWING_ON_INVALID_ITERATOR\n  && iteratorHelperWithoutClosingOnEarlyError('filter', TypeError);\n\nvar FORCED = IS_PURE || FILTER_WITHOUT_THROWING_ON_INVALID_ITERATOR || filterWithoutClosingOnEarlyError;\n\nvar IteratorProxy = createIteratorProxy(function () {\n  var iterator = this.iterator;\n  var predicate = this.predicate;\n  var next = this.next;\n  var result, done, value;\n  while (true) {\n    result = anObject(call(next, iterator));\n    done = this.done = !!result.done;\n    if (done) return;\n    value = result.value;\n    if (callWithSafeIterationClosing(iterator, predicate, [value, this.counter++], true)) return value;\n  }\n});\n\n// `Iterator.prototype.filter` method\n// https://tc39.es/ecma262/#sec-iterator.prototype.filter\n$({ target: 'Iterator', proto: true, real: true, forced: FORCED }, {\n  filter: function filter(predicate) {\n    anObject(this);\n    try {\n      aCallable(predicate);\n    } catch (error) {\n      iteratorClose(this, 'throw', error);\n    }\n\n    if (filterWithoutClosingOnEarlyError) return call(filterWithoutClosingOnEarlyError, this, predicate);\n\n    return new IteratorProxy(getIteratorDirect(this), {\n      predicate: predicate\n    });\n  }\n});\n"], "sourceRoot": ""}