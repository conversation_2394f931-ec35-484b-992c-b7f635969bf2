{"timestamp": "2025-07-15T09:43:25.226Z", "summary": {"passed": 17, "failed": 1, "warnings": 2, "passRate": 85}, "details": [{"type": "pass", "message": "Vue配置包含Gzip压缩", "details": ""}, {"type": "pass", "message": "Vue配置包含代码分割", "details": ""}, {"type": "warn", "message": "Vue配置可能缺少支付SDK分离", "details": ""}, {"type": "pass", "message": "Axios已升级到安全版本", "details": ""}, {"type": "pass", "message": "Vue已升级到2.7版本", "details": ""}, {"type": "pass", "message": "已安装Gzip压缩插件", "details": ""}, {"type": "pass", "message": "已安装图片优化插件", "details": ""}, {"type": "warn", "message": "无法运行安全审计", "details": "Command failed: npm audit --audit-level=high --json"}, {"type": "info", "message": "node_modules 大小: 310M", "details": ""}, {"type": "pass", "message": "src/utils/performance-monitor.js 存在", "details": ""}, {"type": "pass", "message": "src/utils/api-cache.js 存在", "details": ""}, {"type": "pass", "message": "src/server/http.optimized.js 存在", "details": ""}, {"type": "pass", "message": "config/service-worker.optimized.js 存在", "details": ""}, {"type": "pass", "message": "scripts/performance-test.js 存在", "details": ""}, {"type": "pass", "message": "性能监控已集成到main.js", "details": ""}, {"type": "pass", "message": "生产环境已禁用source map", "details": ""}, {"type": "pass", "message": "已配置性能预算", "details": ""}, {"type": "pass", "message": "已配置缓存组", "details": ""}, {"type": "pass", "message": "构建测试成功", "details": ""}, {"type": "fail", "message": "JavaScript总大小: 2.02MB (过大，建议: <0.5MB)", "details": ""}, {"type": "info", "message": "CSS总大小: 1197.54KB", "details": ""}, {"type": "pass", "message": "发现 47 个Gzip压缩文件", "details": ""}]}